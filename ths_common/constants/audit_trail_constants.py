from ths_common.constants.base_enum import BaseEnum
from ths_common.constants.domain_event_constants import DomainEvent


class AuditType(BaseEnum):
    BOOKING_CREATED = "Booking Created", (
        DomainEvent.BOOKING_CREATED,
        DomainEvent.BILL_CREATED,
        DomainEvent.OVERBOOKING_CREATED,
    )
    BOOKING_RECREATED = "Booking Re-Created", (
        DomainEvent.BOOKING_RECREATED,
        DomainEvent.ROOM_FREED,
        DomainEvent.BILL_RECREATED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.ADDON_REMOVED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.OVERBOOKING_CREATED,
    )
    BOOKING_CONFIRMED = "Booking Confirmed", (DomainEvent.BOOKING_CONFIRMED,)
    BOOKING_CANCELLED = "Booking Cancelled", (
        DomainEvent.BOOKING_CANCELLED,
        DomainEvent.ROOM_FREED,
        DomainEvent.ALLOWANCE_PASSED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.CHARGE_INVOICED,
        DomainEvent.CHARGE_POSTED,
        DomainEvent.DNR_MODIFIED,
        DomainEvent.INVOICE_GENERATED,
        DomainEvent.PAYMENT_CANCELLED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.PREVIEW_INVOICE_GENERATED,
    )
    BOOKING_MARKED_NOSHOW = "Booking Marked NoShow", (
        DomainEvent.BOOKING_MARKED_NOSHOW,
        DomainEvent.ROOM_FREED,
        DomainEvent.ALLOWANCE_PASSED,
        DomainEvent.PAYMENT_CANCELLED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.CHARGE_INVOICED,
        DomainEvent.CHARGE_POSTED,
        DomainEvent.INVOICE_GENERATED,
        DomainEvent.PREVIEW_INVOICE_GENERATED,
    )
    PARTIAL_BOOKING_CANCELLED = "Partial Booking Cancelled", (
        DomainEvent.ROOM_STAY_CANCELLED,
        DomainEvent.ROOM_FREED,
        DomainEvent.GUEST_STAY_REMOVED,
        DomainEvent.ROOM_STAY_OCCUPANCY_CHANGED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.CHARGE_CANCELLED,
        DomainEvent.CHARGE_POSTED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.PAYMENT_POSTED,
        DomainEvent.PAYMENT_CANCELLED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.ROOM_STAY_OCCUPANCY_CHANGED,
    )
    PARTIAL_BOOKING_MARKED_NOSHOW = "Partial Booking Marked NoShow", (
        DomainEvent.ROOM_STAY_MARKED_NOSHOW,
        DomainEvent.GUEST_STAY_MARKED_NOSHOW,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.CHARGE_CANCELLED,
        DomainEvent.ROOM_FREED,
        DomainEvent.PAYMENT_CANCELLED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.PAYMENT_POSTED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.CHARGE_POSTED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
    )

    BOOKING_DETAILS_MODIFIED = "Booking Details Modified", (
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
    )
    TA_COMMISSION_MODIFIED = "TA Commission Details Modified", (
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.TA_COMMISSION_UPDATED,
    )
    ROOM_STAY_ADDED = "Room Stay Added", (
        DomainEvent.ROOM_STAY_ADDED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
    )
    ROOM_STAY_DATES_CHANGED = "Room Stay Dates Changed", (
        DomainEvent.ROOM_STAY_DATES_CHANGED,
        DomainEvent.ROOM_CHANGED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.ALLOWANCE_PASSED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.PAYMENT_CANCELLED,
        DomainEvent.CHARGE_CANCELLED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.ADDON_REMOVED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
    )
    ROOM_STAY_ROOM_TYPE_CHANGED = "Room Stay Room Type Changed", (
        DomainEvent.ROOM_TYPE_CHANGED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.ROOM_CHANGED,
        DomainEvent.ALLOWANCE_PASSED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.CHARGE_CANCELLED,
    )
    DISALLOW_CHARGE_ADDITION_CHANGED = "Disallow Charge Addition Changed", (
        DomainEvent.DISALLOW_CHARGE_ADDITION_CHANGED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
    )
    GUEST_STAY_ADDED = "Guest Stay Added", (
        DomainEvent.GUEST_STAY_ADDED,
        DomainEvent.ROOM_STAY_OCCUPANCY_CHANGED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.CHARGE_CANCELLED,
        DomainEvent.PAYMENT_CANCELLED,
        DomainEvent.ALLOWANCE_PASSED,
    )
    GUEST_DETAILS_MODIFIED = "Guest Details Modified", (
        DomainEvent.GUEST_DETAILS_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
    )
    GUEST_ASSIGNED_TO_GUEST_STAY = "Guest Assigned To Guest Stay", (
        DomainEvent.GUEST_ASSIGNED_TO_GUEST_STAY,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
    )
    GUEST_MARKED_AS_BOOKER = "Guest Marked As Booker", (DomainEvent.GUEST_STAY_UPDATED,)
    SPOT_CREDIT_PROVIDED = "Spot Credit provided", (DomainEvent.SPOT_CREDIT_PROVIDED,)
    INVOICE_REISSUED = "Invoice Reissued", (DomainEvent.INVOICE_REISSUED,)
    INVOICE_REISSUED_FOR_NON_FINANCIAL_CHANGES = (
        "Invoice Reissued For Non Financial Changes",
        (DomainEvent.INVOICE_REISSUED_FOR_NON_FINANCIAL_CHANGES,),
    )
    INVOICE_GENERATED_VIA_INVOICE_ACCOUNT = "Invoice Generated Via Invoice Account"
    GUEST_UN_MARKED_AS_BOOKER = "Guest Un Marked As Booker", (
        DomainEvent.GUEST_STAY_UPDATED,
        DomainEvent.CHARGE_SPLIT_MODIFIED,
        DomainEvent.BILLED_ENTITY_UPDATED,
    )
    BOOKING_OWNER_DETAILS_MODIFIED = "Booking Owner Details Modified", (
        DomainEvent.BOOKING_OWNER_DETAILS_MODIFIED,
        DomainEvent.GUEST_DETAILS_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
    )

    CHECKIN_PERFORMED = "Check-In Performed", (
        DomainEvent.CHECKIN_PERFORMED,
        DomainEvent.GUEST_DETAILS_MODIFIED,
        DomainEvent.ROOM_CHANGED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
    )
    CHECKOUT_PERFORMED = "Check-Out Performed", (
        DomainEvent.CHECKOUT_PERFORMED,
        DomainEvent.INVOICE_GENERATED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.CHARGE_POSTED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.PAYMENT_POSTED,
        DomainEvent.ADDON_REMOVED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.CHARGE_CANCELLED,
        DomainEvent.CHARGE_ADDED,
    )
    PREVIEW_INVOICE_GENERATED = "Preview Invoice Generated", (
        DomainEvent.PREVIEW_INVOICE_GENERATED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.CHARGE_POSTED,
        DomainEvent.CHARGE_INVOICED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.PAYMENT_POSTED,
    )
    BILL_TO_CHANGED = "Bill-To Changed", (DomainEvent.BILL_TO_CHANGED,)
    INVOICE_GENERATED = "Invoice Generated", (DomainEvent.INVOICE_GENERATED,)
    CHARGE_ADDED = "Charge Added", (
        DomainEvent.CHARGE_ADDED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.CHARGE_POSTED,
        DomainEvent.PAYMENT_POSTED,
        DomainEvent.PAYMENT_MODIFIED,
    )
    CHARGE_MODIFIED = "Charge Modified", (
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.CHARGE_CANCELLED,
        DomainEvent.CHARGE_SPLIT_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.PAYMENT_CANCELLED,
        DomainEvent.BILL_AMOUNT_CHANGED,
    )
    CHARGE_CANCELLED = "Charge Cancelled", (
        DomainEvent.CHARGE_CANCELLED,
        DomainEvent.ALLOWANCE_PASSED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.PAYMENT_CANCELLED,
        DomainEvent.PAYMENT_MODIFIED,
    )
    PAYMENT_ADDED = "Payment Added", (DomainEvent.PAYMENT_ADDED,)
    PAYMENT_CANCELLED = "Payment Cancelled", (DomainEvent.PAYMENT_CANCELLED,)
    REFUND_ADDED = "Refund Added", (DomainEvent.REFUND_ADDED,)
    PAYMENT_POSTED = "Payment Posted", (DomainEvent.PAYMENT_POSTED,)
    PAYMENT_MODIFIED = "Payment Modified", (DomainEvent.PAYMENT_MODIFIED,)
    PAYMENT_REDISTRIBUTED = "Payment Redistributed", (
        DomainEvent.PAYMENT_REDISTRIBUTED,
    )
    INVOICE_REGENERATED = "Invoice Regenerated", (DomainEvent.INVOICE_REGENERATED,)
    CASH_COUNTER_REFUND_ADDED = "Cash Counter Refund Added", (
        DomainEvent.CASH_COUNTER_REFUND_ADDED,
    )
    CASH_COUNTER_PAYMENT_ADDED = "Cash Counter Payment Added", (
        DomainEvent.CASH_COUNTER_PAYMENT_ADDED,
    )

    BOOKING_CANCELLATION_REVERSED = "Booking Cancellation Reversed", (
        DomainEvent.BOOKING_CANCELLATION_REVERSED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.ALLOWANCE_PASSED,
    )
    BOOKING_NOSHOW_REVERSED = "Booking Noshow Reversed", (
        DomainEvent.BOOKING_NO_SHOW_REVERSED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.ALLOWANCE_PASSED,
        DomainEvent.BILL_AMOUNT_CHANGED,
    )
    BOOKING_PARTIAL_NOSHOW_REVERSED = "Booking Partial Noshow Reversed", (
        DomainEvent.BOOKING_PARTIAL_NO_SHOW_REVERSED,
        DomainEvent.ALLOWANCE_PASSED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.CHARGE_CANCELLED,
        DomainEvent.GUEST_STAY_NOSHOW_REVERSED,
        DomainEvent.PAYMENT_CANCELLED,
        DomainEvent.ROOM_STAY_NOSHOW_REVERSED,
    )
    CHECKOUT_REVERSED = "Checkout Reversed", (
        DomainEvent.CHECKOUT_REVERSED,
        DomainEvent.INVOICE_CANCELLED,
        DomainEvent.ALLOWANCE_PASSED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.CHARGE_POSTED,
        DomainEvent.INVOICE_CANCELLED,
        DomainEvent.OVERBOOKING_CREATED,
        DomainEvent.PAYMENT_CANCELLED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.PAYMENT_POSTED,
        DomainEvent.ROOM_CHANGED,
    )
    CHECKIN_REVERSED = "Checkin Reversed", (
        DomainEvent.CHECKIN_REVERSED,
        DomainEvent.ROOM_FREED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.CHARGE_DELETED,
        DomainEvent.CHARGE_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
    )
    ADDON_CREATED = "Add-on Created", (
        DomainEvent.ADDON_CREATED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.CHARGE_MODIFIED,
    )
    ADDON_MODIFIED = "Add-on Modified", (
        DomainEvent.ADDON_MODIFIED,
        DomainEvent.BILL_AMOUNT_CHANGED,
        DomainEvent.CHARGE_ADDED,
        DomainEvent.CHARGE_CANCELLED,
        DomainEvent.CHARGE_DELETED,
        DomainEvent.CHARGE_MODIFIED,
    )
    ADDON_REMOVED = "Add-on Removed", (
        DomainEvent.ADDON_REMOVED,
        DomainEvent.CHARGE_CANCELLED,
        DomainEvent.CHARGE_DELETED,
        DomainEvent.CHARGE_MODIFIED,
    )
    ATTACHMENT_ADDED = "Attachment Added", (
        DomainEvent.ATTACHMENT_ADDED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
    )
    ATTACHMENT_MODIFIED = "Attachment Modified", (
        DomainEvent.ATTACHMENT_MODIFIED,
        DomainEvent.WEB_CHECKIN_ID_VERIFICATION,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
        DomainEvent.WEB_CHECKIN_VERIFICATION_STATUS_CHANGED,
    )
    ATTACHMENT_DELETED = "Attachment Deleted", (DomainEvent.ATTACHMENT_DELETED,)
    WEB_CHECKIN_COMPLETED_BY_GUESTS = "Web CheckIn Completed By Guests", (
        DomainEvent.WEB_CHECKIN_COMPLETED_BY_GUESTS,
    )
    WEB_CHECKIN_MODIFIED = "Web CheckIn Modified", (
        DomainEvent.WEB_CHECKIN_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
    )
    ALLOWANCE_PASSED = "Allowance Passed", (
        DomainEvent.ALLOWANCE_PASSED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
    )
    ALLOWANCE_CANCELLED = "Allowance Cancelled", (DomainEvent.ALLOWANCE_CANCELLED,)
    CHARGE_POSTED = "Charge Posted", (
        DomainEvent.CHARGE_POSTED,
        DomainEvent.CHARGE_MODIFIED,
    )
    CHARGE_TRANSFERRED = "Charge Transferred", (
        DomainEvent.CHARGE_TRANSFERRED,
        DomainEvent.ALLOWANCE_PASSED,
    )
    REFUND_CANCELLED = "Refund Cancelled", (
        DomainEvent.REFUND_CANCELLED,
        DomainEvent.BOOKING_DETAILS_MODIFIED,
        DomainEvent.PAYMENT_MODIFIED,
    )
    ALLOWANCE_POSTED = "Allowance Posted", (DomainEvent.ALLOWANCE_POSTED,)

    CREDIT_SHELL_REDEEMED = "Credit Shell Redeemed"

    RATE_PLAN_CHANGED = "Rate Plan Changed", (DomainEvent.RATE_PLAN_CHANGED,)

    FUNDING_REQUEST_CREATED = 'Funding Request Created'
    FUNDING_REQUEST_MODIFIED = 'Funding Request Modified'

    BTT_REQUEST_CREATED = 'New BTT request'
    BTT_REQUEST_MODIFIED = 'BTT amount modified'

    @property
    def allowed_domain_event_types(self):
        return self.values[1]


class DNRAuditType(BaseEnum):
    DNR_CREATED = "DNR Created"
    DNR_MODIFIED = "DNR Modified"
    DNR_RESOLVED = "DNR Resolved"
    DNR_REMOVED = "DNR Removed"
