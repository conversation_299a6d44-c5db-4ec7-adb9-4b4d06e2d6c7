# coding=utf-8
"""
hotel Constants
"""

from ths_common.constants.base_enum import BaseEnum


class ManagedBy(BaseEnum):
    """
    ManagedBy enum
    """

    HX = 'hx'
    CRS = 'crs'
    MIGRATION_IN_PROGRESS = 'migration_in_progress'
    MOCK_MIGRATION_IN_PROGRESS = 'mock_migration_in_progress'


class BrandCodes(object):
    INDEPENDENT = 'independent'
    TRYST = 'tryst'
    TREND = 'trend'
    TRIP = 'trip'
    HOTELSUPERHERO = 'hotelsuperhero'
