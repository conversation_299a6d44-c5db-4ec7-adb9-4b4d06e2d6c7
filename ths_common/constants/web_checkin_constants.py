from ths_common.constants.base_enum import BaseEnum


class WebCheckinStatus(BaseEnum):
    COMPLETE = 'complete'
    PENDING = 'pending'
    REJECTED = 'rejected'
    VERIFIED = 'verified'


WEB_CHECKIN_LINK_HASH_NAME = "web-checkin-link"


class WebCheckinRejectionReason(BaseEnum):
    @property
    def reason(self):
        return self.values[0]

    @property
    def description(self):
        return self.values[1]

    BOOKING_MODIFICATIONS = (
        "Booking Modifications",
        "of modification(s) made in the booking",
    )


class EmailTemplates(BaseEnum):
    @property
    def identifier(self):
        return self.values[0]

    @property
    def subject(self):
        return self.values[1]

    WEB_CHECKIN_REMINDER = (
        "web_checkin_reminder",
        "Remote Check-In now open for your upcoming booking {booking_id}",
    )
    WEB_CHECKIN_REJECTION = (
        "web_checkin_rejection",
        "Remote Check-In request for {booking_id} could not be processed",
    )
    CRITICAL_TASKS_REMINDER = (
        "critical_task_reminder",
        "Please clear the pending Night Audit Tasks for {hotel_name}",
    )


class MessageTemplates(BaseEnum):
    WEB_CHECKIN_COMMUNICATION = "web_checkin_communication"
    WEB_CHECKIN_FUTURE_BOOKING = "web_checkin_future_booking"
    WEB_CHECKIN_REMINDER = "web_checkin_reminder"
    WEB_CHECKIN_REJECTION = "web_checkin_rejection"
