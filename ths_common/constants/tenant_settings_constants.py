from ths_common.constants.base_enum import BaseEnum


class TenantSettingName(BaseEnum):
    DISABLE_PAYMENT_LINK = "disable_payment_link"
    DISABLED_JOBS = "crs.disabled_jobs"
    E_REG_CARD = "e_reg_card"
    CASHIERING_ENABLED = "cashiering_enabled"
    URL_FOR_WEB_CHECKIN = "url_for_web_checkin"
    WEB_CHECKIN_ENABLED = "web_checkin_enabled"
    E_INVOICING = 'e_invoicing'
    E_INVOICING_FAILURE_HANDLING = 'e_invoicing_failure_handling'
    WEB_CHECKIN_SMS_ENABLED = "web_checkin_sms_enabled"
    WEB_CHECKIN_REJECTION_REASONS_WITH_MSG = 'web_checkin_rejection_reasons_with_msg'
    RATE_MANAGER_ENABLED = "rate_manager_enabled"
    TREEBO_TENANT_ID = "treebo"
    TEN100_TENANT_ID = "ten100"
    PIS_TENANT_ID = "tntpis"
    HOTEL_USES_POSTTAX_PRICE = "rate_config.hotel_uses_posttax_price"
    DISABLE_AUTO_CANCELLATION_NOSHOW_CHARGES = (
        'crs.disable_auto_cancellation_noshow_charges'
    )
    INVOICE_SUMMARY_ENABLED = "crs.invoice_summary_enabled"
    SHOW_ALLOWANCE_AS_SEPARATE_LINE_ITEM = "show_allowance_as_separate_line_item"
    IS_REPORT_ENABLED = "reports.is_report_enabled"
    SEND_INVOICE_TO_BOOKER_EMAIL_ON_CHECKOUT = (
        "send_invoice_to_booker_email_on_checkout"
    )
    PAYMENT_CONFIG = "payment_config"
    RECORD_PAYMENT_MAX_VALUE = "record_payment_max_value"
    ISSUE_REFUND_MAX_CONDITION = "issue_refund_max_condition"
    CREDIT_NOTE_NUM_MAX_LENGTH = "credit_note_num_max_length"
    INVOICE_NUMBER_GENERATION_STRATEGY = "invoice_number_generation_strategy"
    CREDIT_NOTE_NUMBER_GENERATION_STRATEGY = "credit_number_generation_strategy"
    INVOICE_NUM_MAX_LENGTH = "invoice_num_max_length"
    BOOKER_IDS_DISABLED_FOR_B2B_AlERTS = "booker_ids_disabled_for_b2b_alerts"
    REFUND_RULE = "refund_rule"
    IS_AUTO_REFUND_ENABLED = "is_auto_refund_enabled"
    AR_DEBTOR_CONFIG = "ar_module.debtor_config"
    USE_CANCELLATION_POLICY = "use_cancellation_policy"
    EMAIL_IDS_FOR_FAILURE_COMMUNICATION = "email_ids_for_failure_communication"
    DUMMY_HOTELS = "dummy_hotels"
    AUTO_APPROVED_PAYOUT_LINK_AMOUNT = "auto_approved_payout_link_amount"
    SPOT_CREDIT_ELIGIBLE_ACCOUNT_CATEGORY = "spot_credit_eligible_account_category"
    IS_PG_REFUND_ENABLED_FROM_PMS = "is_pg_refund_enabled_from_pms"
    PAYMENT_DATA_PUSH_CONFIG = "nav_payment_data_push_config"
    BO_PAYMENT_MODE_CONFIG_BY_INVOICE_DATE = (
        "back_office_payment_mode_config_by_invoice_date"
    )
    BO_CHAIN_MANAGER_COMPANY_CODE = "back_office_chain_manager_company_code"
    KNOWLARITY_IVR_BASE_SETTINGS = "knowlarity_ivr_base_settings"
    PAYMENT_REMINDER_IVR_SETTINGS = "acq.payment_reminder_ivr_settings"
    ACQ_DISABLED_CHANNELS = "acq.disabled_channels"
    ACQ_DISABLED_APPLICATION_SOURCES = "acq.disabled_application_sources"
    ACQ_VALID_BOOKING_GUARANTEE = "acq.booking_guarantee"
    ACQ_CANCELLATION_SLOTS = "acq.cancellation_slots"
    REVMAN_ENABLED_HOTELS = "revman_enabled_hotels"
    IS_BO_ENABLED = "is_back_office_enabled"
    IS_GUARANTEE_ENABLED = "is_guarantee_enabled"
    ACQ_CONFIGS = "acq_configs"
    IS_ACQ_ENABLED_FOR_SU = "is_acq_enabled_for_su"
    ROOMS_COUNT_FOR_BULK_RATE_PLAN_APPLICABILITY = (
        "rooms_count_for_bulk_rate_plan_applicability"
    )
    RESTRICT_SPECIAL_CHARACTER_IN_CN_NUM = "restrict_special_character_in_cn_num"
    CHAIN_MANAGER_PARENT_COMPANY_CODE = "chain_manager_parent_company_code"
    BOOKING_FUNDING_ENABLED = "booking_funding_enabled"
    MAXIMUM_AMOUNT_ALLOWED_FOR_MANUAL_FUNDING = (
        'maximum_amount_allowed_for_manual_funding'
    )


class ERegCardLevel(BaseEnum):
    BOOKING = 'booking'
    ROOM = 'room'
    GUEST = 'guest'


class ERegCardConfig(BaseEnum):
    ENABLED = 'enabled'
    REQUIRED = 'required'
    LEVEL = 'level'


class EInvoicingConfig(BaseEnum):
    ENABLED = 'enabled'
    REPORTING_EMAIL = 'reporting_email'
    OWNER_IDS = 'owner_ids'
    AUTH_TOKEN = 'auth_token'
    URL = 'url'


class ReportName(BaseEnum):
    MANAGER_FLASH_REPORT = 'manager_flash_report'
    TRAIL_BALANCE_REPORT = 'trail_balance_report'
