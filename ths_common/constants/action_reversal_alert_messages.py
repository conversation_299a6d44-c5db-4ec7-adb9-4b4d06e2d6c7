from ths_common.constants.base_enum import BaseEnum


class ActionReversalAlertMessage(BaseEnum):
    INVOICE_CANCELLED = "{no_of_invoices} invoice(s) cancelled."
    OVERBOOKING_CREATED = "{no_of_overbookings} overbooking(s) created."
    ROOMS_FREED = "Room(s): {freed_rooms} freed after checkin reversal."
    ROOM_CHANGED = "Room Changed from {old_room} to {new_room}."
    EXPENSE_TYPE_CHARGES_DELETED = (
        "Charges deleted with charge ids: {deleted_charge_ids}."
    )

    def message(self, *args, **kwargs):
        return self.value.format(*args, **kwargs)
