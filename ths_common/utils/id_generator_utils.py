import datetime
import random
import string
import uuid


def random_id_generator(prefix=None):
    """

    Returns: a random generated prefixed number

    """
    created_at = datetime.datetime.utcnow()
    part1 = created_at.strftime("%d%m%y")
    part2 = created_at.strftime("%H%M")

    number = random.randint(0, 999999999)
    part4 = int(number % 10000)
    number = (number - part4) / 10000
    part3 = int(number % 10000)
    part3 += created_at.month * 10
    if prefix:
        parts = [
            prefix,
            part1,
            part2,
            str(part3).rjust(3, '0'),
            str(part4).rjust(4, '0'),
        ]
    else:
        parts = [part1, part2, str(part3).rjust(3, '0'), str(part4).rjust(4, '0')]
    return '-'.join(parts)


def random_uuid_generator(prefix=None):
    """

    Returns: a random time based uuid

    """
    time_bases_uuid = uuid.uuid1().hex
    if prefix:
        parts = [prefix, time_bases_uuid]
    else:
        parts = [time_bases_uuid]

    return '-'.join(parts)


def generate_short_random_id(length=8, prefix=None):
    alphabet = string.ascii_lowercase + string.digits
    random_suffix = ''.join(random.choices(alphabet, k=length))
    if prefix:
        return '{}{}'.format(prefix, random_suffix).upper()
    else:
        return random_suffix


def generate_short_numerical_random_id(length=8, prefix='', separator=None):
    """
    Returns a random id
    params: All optional
            length - Total length of the ID
            leading_zero_length - If leading zeros are required, number of characters as zero should be provided
            prefix - Provide desired prefix for example 'BIL' to denote Billing
            separator = Provide desired separator for example '-'
    """
    separator = separator if separator else ''
    return prefix + separator + str(rand_x_digit_num(x=length - len(prefix)))


def rand_x_digit_num(x=10, leading_zeroes=False):
    """Return an X digit number, leading_zeroes returns a string, otherwise int"""
    if not leading_zeroes:
        # wrap with str() for uniform results
        return random.randint(10 ** (x - 1), 10**x - 1)
    else:
        return '{0:0{x}d}'.format(random.randint(0, 10**x - 1), x=x)
