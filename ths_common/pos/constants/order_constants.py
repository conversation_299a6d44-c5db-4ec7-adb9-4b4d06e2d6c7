from ths_common.constants.base_enum import BaseEnum


class PosOrderStatus(BaseEnum):
    CREATED = "created", "Created"
    SCHEDULED = "scheduled", "Scheduled"
    SETTLED = "settled", "Settled"
    CANCELLED = "cancelled", "Cancelled"
    PREPARING = "preparing", "Preparing"
    READY = "ready", "Ready"
    DELIVERED = "delivered", "Delivered"
    VOIDED = "voided", "Voided"


class PosOrderType(BaseEnum):
    RESTAURANT = "restaurant", "Restaurant"
    ROOM_SERVICE = "room_service", "Room Service"
    DELIVERY = "delivery", "Delivery"
    TABLE_ORDER = "table_order", "Table Order"
    TAKE_AWAY = "take_away", "Take Away"


class PosOrderSettlementMethod(BaseEnum):
    TRANSFER_TO_ROOM = "transfer_to_room"
    SETTLE_AT_POS = "settle_at_pos"
    TRANSFER_TO_COMPANY = "transfer_to_company"


class PosOrderPriorities(BaseEnum):
    ON_TRACK = "on_track", "ON_TRACK"
    DELAYED = "delayed", "DELAYED"
    EXTREMELY_DELAYED = "extremely_delayed", "EXTREMELY_DELAYED"


class PosOrderItemStatus(BaseEnum):
    RECEIVED = "received", "Received"
    PREPARING = "preparing", "Preparing"
    READY = "ready", "Ready"
    DELIVERED = "delivered", "Delivered"


class PosSourceOfCustomer(BaseEnum):
    IN_HOUSE = 'inhouse', "In House"
    WALK_IN_SOCIAL = 'walk_in_social', "Walk in Social"
    WALK_IN_CORPORATE = 'walk_in_corporate', "Walk in Corporate"
    WALK_IN_SOCIAL_GROUP = 'walk_in_social_group', "Walk in Social Group"
    WALK_IN_CORPORATE_GROUP = 'walk_in_corporate_group', "Walk in Corporate Group"


class SellerType(BaseEnum):
    RESTAURANT = "restaurant", "Restaurant"
    OTHER = "other", "Other"


class PosReservationStatus(BaseEnum):
    CREATED = "created", "Created"
    GUEST_ARRIVED = "guest_arrived", "Guest Arrived"
    GUEST_SEATED = "guest_seated", "Guest Seated"
    CANCELLED = "cancelled", "CANCELLED"


class SellerTableStatus(BaseEnum):
    AVAILABLE = "available", "Available"
    OCCUPIED = "occupied", "Occupied"
    DIRTY = "dirty", "Dirty"
    UNAVAILABLE = "unavailable", "Unavailable"


class PosBillStatus(BaseEnum):
    SETTLED = "settled", "Settled"
    UNSETTLED = "unsettled", "Unsettled"


class PosSplitBillSplitType(BaseEnum):
    """
    All newly created bills from bill_split can have the following possible values except None
    Primary bill can have all possible values. If a primary bill is split, the respective split_type would be set.
    If an item is added/updated, the item will be added to the primary bill and split_type would be set to None.
    """

    EQUAL = "equal", "Equal"
    AMOUNT = "amount", "Amount"
    PERCENTAGE = "percentage", "Percentage"
    ITEM = "item", "Item"
    FOOD_AND_ALCOHOL = "food_and_alcohol", "Food and Alcohol"
    PRIMARY = "primary", "Primary"


class OrderItemComplimentaryType(BaseEnum):
    """
    Every propery will have these three complimentary types by default.
    Additional complimentary types can be added from catalogue admin.
    """

    SERVICE_RECOVERY = "service_recovery", "Service Recovery"
    STAFF_ENTITLEMENT = "staff_entitlement", "Staff Entitlement"
    ENTERTAINING_GUESTS = "entertaining_guests", "Entertaining Guests"


class OrderRemarkAction(BaseEnum):
    ORDER_ITEM_REMOVED = "order_item_removed", "Item Removed From Order"
    ORDER_ITEM_MARKED_COMPLIMENTARY = (
        "order_item_marked_complimentary",
        "Item Marked Complimentary",
    )
    ORDER_TYPE_CHANGED = "pos_order_type_changed", "Order Type Changed"
    ORDER_MARKED_VOID = "pos_order_marked_void", "Order Marked Void"
    ORDER_MARKED_COMPLIMENTARY = (
        "pos_order_marked_complimentary",
        "Order Marked Complimentary",
    )
