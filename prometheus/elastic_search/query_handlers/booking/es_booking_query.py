from datetime import datetime
from typing import List

from prometheus.elastic_search.query.query_objects import And<PERSON><PERSON><PERSON>, EqualsQuery, InQuery


class ChannelSubChannelFilterDto:
    def __init__(self, channel: str, sub_channels: List[str] = None):
        self.channel = channel
        self.sub_channels = sub_channels

    def build_es_query(self):
        channel_queries = [EqualsQuery("channel_code", self.channel)]
        if self.sub_channels:
            channel_queries.append(InQuery("subchannel_code", self.sub_channels))
        return AndQuery(*channel_queries)


class ESBookingSearchQuery:
    def __init__(
        self,
        hotel_id=None,
        status: List = None,
        channel_code: List = None,
        query=None,
        bill_id: List = None,
        booking_id: List = None,
        partial_reference_number=None,
        checkin_lte=None,
        checkin_gte=None,
        checkout_lte=None,
        checkout_gte=None,
        customer_reference_id=None,
        reference_numbers: List = None,
        guest_email=None,
        guest_phone=None,
        email_or_phone=None,
        application_codes: List[str] = None,
        net_balance_gte=None,
        channels_not_in=None,
        application_codes_not_in=None,
        channel_sub_channel_exclusions: List[ChannelSubChannelFilterDto] = None,
        sort_by=None,
        limit=20,
        offset=0,
    ):
        self.hotel_id = hotel_id
        self.status = status
        self.channel_code = channel_code
        self.application_codes = application_codes
        self.bill_id = bill_id
        self.booking_id = booking_id
        self.partial_reference_number = partial_reference_number
        self.customer_reference_id = customer_reference_id
        self.checkin_lte = (
            checkin_lte.isoformat()
            if isinstance(checkin_lte, datetime)
            else checkin_lte
        )
        self.checkin_gte = (
            checkin_gte.isoformat()
            if isinstance(checkin_gte, datetime)
            else checkin_gte
        )
        self.checkout_lte = (
            checkout_lte.isoformat()
            if isinstance(checkout_lte, datetime)
            else checkout_lte
        )
        self.checkout_gte = (
            checkout_gte.isoformat()
            if isinstance(checkout_gte, datetime)
            else checkout_gte
        )
        self.reference_numbers = reference_numbers
        self.guest_email = guest_email
        self.guest_phone = guest_phone
        self.email_or_phone = email_or_phone
        self.net_balance_gte = net_balance_gte
        self.channels_not_in = channels_not_in
        self.application_codes_not_in = application_codes_not_in
        self.channel_sub_channel_exclusions = channel_sub_channel_exclusions
        self.query = query

        self.sort_by = sort_by
        self.limit = limit
        self.offset = offset

    @staticmethod
    def build_from_booking_search_request(search_request):
        return ESBookingSearchQuery(
            hotel_id=search_request.hotel_id,
            status=search_request.status,
            channel_code=search_request.channel_code,
            application_codes=search_request.application_codes,
            bill_id=search_request.bill_id,
            booking_id=search_request.booking_id,
            partial_reference_number=search_request.partial_reference_number,
            customer_reference_id=search_request.customer_reference_id,
            checkin_lte=search_request.checkin_lte,
            checkin_gte=search_request.checkin_gte,
            checkout_lte=search_request.checkout_lte,
            checkout_gte=search_request.checkout_gte,
            reference_numbers=search_request.reference_numbers,
            guest_email=search_request.guest_email,
            guest_phone=search_request.guest_phone,
            email_or_phone=search_request.email_or_phone,
            query=search_request.query,
            sort_by=search_request.sort_by,
            limit=search_request.limit,
            offset=search_request.offset,
        )
