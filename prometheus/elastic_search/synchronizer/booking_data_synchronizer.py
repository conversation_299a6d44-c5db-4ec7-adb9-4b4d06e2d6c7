import json
import logging
import math
from collections import namedtuple
from datetime import datetime
from typing import Dict, List

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.common.decorators import timed
from prometheus.common.helpers.circuit_breaker import CircuitBreaker
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.dtos.elastic_search_dtos import (
    ElasticSearchBookingDetailsDto,
)
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.elastic_search import indexes
from prometheus.elastic_search.query.query_builder import ElasticSearchQueryBuilder
from prometheus.elastic_search.query.query_objects import InQuery
from prometheus.elastic_search.schema.bookings import ESBookingDataSchema
from prometheus.infrastructure.external_clients.elastic_search_client import (
    ElasticSearchServiceClient,
    ESBatchSizeExceededException,
)
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)

BATCH_SIZE = 1000
FREQUENCY = 5
MAX_NUMBER_OF_DAYS_FOR_SINGLE_SYNC = 365


@register_instance(
    dependencies=[
        ElasticSearchServiceClient,
        BookingRepository,
        BillRepository,
    ]
)
class BookingDataSynchronizer:
    DataSet = namedtuple('DataSet', ['booking', 'bill'])

    def __init__(
        self,
        elastic_search_service_client: ElasticSearchServiceClient,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
    ):
        self.elastic_search_service_client = elastic_search_service_client
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository

    @timed
    def handle(self, booking_ids=None, from_date_to_sync=None):
        logger.info(
            f"Processing booking data sync from: {from_date_to_sync}/booking ids : {booking_ids}"
        )
        booking_ids_to_handle = self._get_booking_ids_to_handle(
            booking_ids, from_date_to_sync
        )
        if not booking_ids_to_handle:
            logger.info("No records found to sync")
            return
        deleted_bookings = self.booking_repository.fetch_deleted_bookings(
            booking_ids_to_handle
        )
        if deleted_bookings:
            self._delete_from_elastic_search(deleted_bookings)
        booking_ids_to_handle = list(set(booking_ids_to_handle) - set(deleted_bookings))
        if booking_ids_to_handle:
            self._handle_new_bookings_and_modifications(booking_ids_to_handle)

    def _delete_from_elastic_search(self, deleted_booking_ids):
        query = (
            ElasticSearchQueryBuilder()
            .filter(InQuery('_id', deleted_booking_ids))
            .build()
        )
        self.elastic_search_service_client.delete_by_query(
            indexes.get_booking_index_name(),
            query,
        )

    def _get_booking_ids_to_handle(self, booking_ids, from_date_to_sync):
        if booking_ids:
            return booking_ids
        from_date_to_sync = self._normalize_from_date(from_date_to_sync)
        to_date_to_sync = None
        if (
            dateutils.current_datetime() - from_date_to_sync
        ).days > MAX_NUMBER_OF_DAYS_FOR_SINGLE_SYNC:
            to_date_to_sync = dateutils.add(
                from_date_to_sync, days=MAX_NUMBER_OF_DAYS_FOR_SINGLE_SYNC
            )
        return self.booking_repository.fetch_all_booking_ids_by_modified_at(
            from_date_to_sync, modified_till=to_date_to_sync
        )

    @staticmethod
    def _normalize_from_date(from_date_to_sync):
        if from_date_to_sync is None:
            return dateutils.subtract(
                dateutils.current_datetime(), minutes=FREQUENCY + 1
            )
        return (
            datetime.fromisoformat(from_date_to_sync)
            if isinstance(from_date_to_sync, str)
            else from_date_to_sync
        )

    def _handle_new_bookings_and_modifications(self, modified_booking_ids):
        total_batches = math.ceil(len(modified_booking_ids) / BATCH_SIZE)
        circuit_breaker = CircuitBreaker(threshold=0 if total_batches == 1 else 3)
        for batch_index, booking_ids in enumerate(
            chunks(modified_booking_ids, BATCH_SIZE)
        ):
            logger.info(
                f"Processing batch number {batch_index +1} out of {total_batches}"
            )
            booking_to_sync: List[
                ElasticSearchBookingDetailsDto
            ] = self.booking_repository.load_booking_details_for_es_data_sync(
                booking_ids
            )
            self._enrich_bill_data_from_db(booking_to_sync)
            try:
                self._sync_data(booking_to_sync)
                circuit_breaker.success()
            except Exception as e:
                booking_ids = [dto.booking_id for dto in booking_to_sync]
                logger.exception(e)
                circuit_breaker.error(e)

    def _enrich_bill_data_from_db(self, booking_to_sync):
        bill_ids = [dto.bill_id for dto in booking_to_sync]
        booking_balances: Dict[str, Money] = {
            aggregate.bill_id: aggregate.net_payable
            for aggregate in self.bill_repository.load_all_with_yield_per(
                bill_ids,
                skip_billed_entities=True,
            )
        }
        for booking_data in booking_to_sync:
            net_payable = booking_balances.get(booking_data.bill_id)
            booking_data.enrich_net_balance(net_payable)

    def _sync_data(self, bookings: List[ElasticSearchBookingDetailsDto]):
        data = "\n".join([self._build_data_to_ingest(booking) for booking in bookings])
        try:
            self.elastic_search_service_client.add_documents_in_bulk(
                indexes.get_booking_index_name(), f"{data}\n"
            )
        except ESBatchSizeExceededException:
            for batch in chunks(bookings, int(len(bookings) / 2)):
                self._sync_data(batch)

    @staticmethod
    def _build_data_to_ingest(booking: ElasticSearchBookingDetailsDto):
        booking_id = booking.booking_id
        booking_data = ESBookingDataSchema().dump(booking).data
        return (
            f'{json.dumps({"index": {"_id": booking_id}})}\n{json.dumps(booking_data)}'
        )

    def index_crs_data_items(self, booking_data_list=None, bill_data_list=None):
        assert (
            booking_data_list or bill_data_list
        ), "At least one of booking_data_list or bill_data_list should be provided"

        data_set: List[
            BookingDataSynchronizer.DataSet
        ] = self._correlate_booking_and_bill_data(bill_data_list, booking_data_list)
        self._index_crs_data(data_set)

    @staticmethod
    def _correlate_booking_and_bill_data(bill_data_list, booking_data_list):
        bill_data_dict = {bill["bill_id"]: bill for bill in (bill_data_list or [])}
        data_set = []
        for booking_data in booking_data_list or []:
            bill_id = booking_data["bill_id"]
            bill_data = (
                bill_data_dict.pop(booking_data["bill_id"])
                if bill_id in bill_data_dict
                else None
            )
            data_set.append(
                BookingDataSynchronizer.DataSet(booking=booking_data, bill=bill_data)
            )
        for bill_data in bill_data_dict.values():
            data_set.append(
                BookingDataSynchronizer.DataSet(booking=None, bill=bill_data)
            )
        return data_set

    def _index_crs_data(self, data_set: List[DataSet]):
        circuit_breaker = CircuitBreaker(threshold=3)
        bill_ids, booking_ids, bill_data_change = set(), set(), dict()
        for data in data_set:
            if data.booking:
                booking_ids.add(data.booking["booking_id"])
            elif data.bill:
                bill_ids.add(data.bill["bill_id"])
            if data.bill:
                bill_data_change[data.bill["bill_id"]] = data.bill

        booking_to_sync = []
        if booking_ids:
            booking_to_sync.extend(
                self.booking_repository.load_booking_details_for_es_data_sync(
                    booking_ids=list(booking_ids)
                )
            )
        if bill_ids:
            booking_to_sync.extend(
                self.booking_repository.load_booking_details_for_es_data_sync(
                    bill_ids=list(bill_ids)
                )
            )
        if not booking_to_sync:
            return
        self._enrich_bill_data_from_billing_change(bill_data_change, booking_to_sync)

        try:
            self._bulk_upsert_data_to_es(booking_to_sync)
            circuit_breaker.success()
        except Exception as e:
            logger.exception("Failed to sync data to elastic search for booking %s")
            circuit_breaker.error(e)

    @staticmethod
    def _enrich_bill_data_from_billing_change(bill_data_change, booking_to_sync):
        for data in booking_to_sync:
            if data.bill_id in bill_data_change:
                net_payable = bill_data_change[data.bill_id].get("net_payable")
                if net_payable:
                    data.enrich_net_balance(Money(net_payable))

    def _bulk_upsert_data_to_es(self, booking_to_sync):
        bulk_payload = self._build_bulk_upsert_payload(booking_to_sync)
        try:
            self.elastic_search_service_client.add_documents_in_bulk(
                indexes.get_booking_index_name(),
                bulk_payload,
            )
        except ESBatchSizeExceededException:
            for batch in chunks(booking_to_sync, int(len(booking_to_sync) / 2)):
                self._bulk_upsert_data_to_es(batch)

    @staticmethod
    def _build_bulk_upsert_payload(
        bookings: List[ElasticSearchBookingDetailsDto],
    ) -> str:
        bulk_payload = []
        for booking in bookings:
            booking_id = booking.booking_id
            booking_data = ESBookingDataSchema().dump(booking).data
            action_metadata = json.dumps({"update": {"_id": booking_id}})
            upsert_data = json.dumps({"doc": booking_data, "upsert": booking_data})
            bulk_payload.append(f"{action_metadata}\n{upsert_data}")
        return "\n".join(bulk_payload) + "\n"
