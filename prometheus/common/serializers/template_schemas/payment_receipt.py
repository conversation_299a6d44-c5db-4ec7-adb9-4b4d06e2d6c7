from marshmallow import Schema, fields
from treebo_commons.money.money_field import <PERSON><PERSON>ield

from shared_kernel.serializers.value_objects import (
    AddressSchema,
    GSTDetailsSchema,
    PhoneSchema,
)


class PaymentReceiptReceiverSchema(Schema):
    first_name = fields.String()
    last_name = fields.String()
    address = fields.Nested(AddressSchema, allow_none=True)
    phone = fields.String()
    email = fields.String()


class VendorSchema(Schema):
    logo = fields.String()
    name = fields.String()
    address = fields.Nested(AddressSchema, allow_none=True)
    gst_details = fields.Nested(GSTDetailsSchema, allow_none=True)
    phone = fields.Nested(PhoneSchema, allow_none=True)
    email = fields.String()
    legal_signature_url = fields.String()


class PaymentReceiptSchemaForTemplate(Schema):
    payment_receipt_receiver_info = fields.Nested(
        PaymentReceiptReceiverSchema, allow_none=True
    )
    affected_room_nos = fields.List(fields.String())
    amount = MoneyField()
    payment_receipt_date = fields.Date()
    date_of_payment = fields.Date()
    payment_receipt_number = fields.String()
    payment_type = fields.String()
    payment_ref_id = fields.String()
    payment_mode = fields.String()
    comment = fields.String()
    payment_time = fields.Time()


class PaymentReceiptTemplateSchema(Schema):
    payment_receipt = fields.Nested(PaymentReceiptSchemaForTemplate, required=True)
    vendor_meta = fields.Nested(VendorSchema, required=True)
    vendor_context = fields.Dict()
