from marshmallow import Schema, fields
from treebo_commons.money.money_field import MoneyField

from prometheus.common.serializers.request.cashiering import CurrencySellerDetailSchema
from prometheus.common.serializers.request.value_objects import VendorDetailsSchema
from shared_kernel.serializers.validators import validate_empty_string
from shared_kernel.serializers.value_objects import (
    TaxDetailSchemaWithStringifiedDecimal,
)


class VendorMetaDetailsSchema(Schema):
    vendor_logo = fields.String()
    vendor_pan_number = fields.String()
    vendor_tan_number = fields.String()
    vendor_tin_number = fields.String()


class CurrencyBuyerDetailSchema(Schema):
    vendor_meta = fields.Nested(VendorMetaDetailsSchema)
    vendor_details = fields.Nested(VendorDetailsSchema, required=True)
    forex_license_no = fields.String()


class EncashmentCertificateSchema(Schema):
    currency_buyer_detail = fields.Nested(CurrencyBuyerDetailSchema)
    currency_seller_detail = fields.Nested(CurrencySellerDetailSchema, required=True)
    amount_in_foreign_currency = MoneyField(required=True)
    foreign_currency_payment_mode = fields.String()
    amount_in_base_currency = MoneyField(required=True)
    taxable_amount = MoneyField()
    tax_amount = MoneyField()
    tax_details = fields.Nested(TaxDetailSchemaWithStringifiedDecimal, many=True)
    round_off = MoneyField(required=True)
    total_payable_in_base_currency = MoneyField(required=True)
    transaction_date = fields.Date(
        required=True,
        description="A date string in ISO-8601 format.",
        error_messages={
            'null': 'Date of transaction may not be null.',
            'validator_failed': 'Invalid value for transaction date.',
        },
    )
    exchange_rate = fields.String(
        required=True,
        description="Rate of exchange in the format: 'x <currency> = y "
        "<currency>'. E.g.: '1 USD = 70 INR'",
    )
    remarks = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        description='Optional comment field',
    )
    transaction_id = fields.String(required=True)
    certificate_number = fields.String(required=True)
    sac_code = fields.String()
