from marshmallow import Schema, fields

from prometheus.core.api_docs import swag_schema


@swag_schema
class InventorySetupSchema(Schema):
    from_date = fields.Date(
        required=True,
        error_messages={
            'null': 'From date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    to_date = fields.Date(
        required=True,
        error_messages={
            'null': 'To date may not be null.',
            'required': 'Please provide to date.',
            'validator_failed': "'{input}' is not a valid value for To date.",
        },
    )
    push_all = fields.<PERSON><PERSON>an(required=False, missing=False)


@swag_schema
class InitMigrationSchema(Schema):
    dry_run = fields.Boolean(
        required=True,
        error_messages={
            'null': 'Dry run may not be null.',
            'required': 'Please provide value for Dry run',
            'validator_failed': "'{input}' is not a valid value for Dry run",
        },
    )
