from marshmallow import Schema, ValidationError, fields, validates_schema
from marshmallow.decorators import post_load
from marshmallow.validate import OneOf

from prometheus.core.api_docs import swag_schema
from shared_kernel.serializers.validators import validate_empty_string
from ths_common.constants.inventory_constants import HouseKeepingStatus


@swag_schema
class CreateHouseKeeperSchema(Schema):
    name = fields.String(
        validate=validate_empty_string,
        required=True,
        description='Name for new housekeeper',
    )


@swag_schema
class UpdateHouseKeeperSchema(Schema):
    name = fields.String(
        validate=validate_empty_string,
        required=False,
        description='Name for new housekeeper',
    )


@swag_schema
class CreateOrUpdateHouseKeepersSchema(Schema):
    housekeeper_id = fields.Integer(
        description="Identifier of housekeeper, unique under a given hotel. Required if housekeeper's name is to be "
        "updated"
    )
    name = fields.String(
        validate=validate_empty_string, description="New or updated housekeeper name"
    )
    delete = fields.Boolean(
        description="Set this value to True, if housekeeper needs to be deleted"
    )

    @validates_schema(skip_on_field_errors=True)
    def validate_data(self, data):
        """
        :param data:
        :return:
        """
        if 'delete' in data and data.get('delete'):
            if not data.get('housekeeper_id'):
                raise ValidationError(
                    "housekeeper_id is required field, for deleting",
                    field_names=['delete'],
                )

        else:
            if not data.get('name'):
                raise ValidationError(
                    "name field is required for either updating or creating new housekeeper",
                    field_names=['name'],
                )


@swag_schema
class UpdateHouseKeepingRecordSchema(Schema):
    housekeeping_status = fields.String(
        required=False,
        validate=[
            OneOf(
                HouseKeepingStatus.all(),
                error="'{input}' is not a valid choice for Booking Status",
            )
        ],
    )
    housekeeper_id = fields.Integer(required=False)
    remarks = fields.String(required=False, allow_none=True)

    @post_load
    def convert_string_to_enum(self, data):
        if data.get('housekeeping_status'):
            data['housekeeping_status'] = HouseKeepingStatus(
                data['housekeeping_status']
            )


@swag_schema
class UpdateHouseKeepingOccupancySchema(Schema):
    count = fields.Integer(required=True)
    remarks = fields.String(required=False, allow_none=True)
