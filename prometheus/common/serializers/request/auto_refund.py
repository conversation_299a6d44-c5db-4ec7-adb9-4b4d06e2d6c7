from marshmallow import Schema, fields

from prometheus.core.api_docs import swag_schema


@swag_schema
class AutoRefundContactDetailsSchema(Schema):
    email = fields.String(max_length=500, allow_null=True, allow_blank=True)
    contact = fields.String(max_length=100, allow_null=True, allow_blank=True)
    name = fields.String(max_length=500, allow_null=True, allow_blank=True)


@swag_schema
class AutoRefundRequestSchema(Schema):
    amount = fields.Decimal(required=True)
    currency = fields.String()
    payment_ids = fields.List(
        fields.String(), required=False, default=[], allow_none=True
    )
    gateway = fields.String(required=False, allow_null=True)
    hotel_id = fields.String(max_length=100, required=False, allow_null=True)
    contact_info = fields.Nested(
        AutoRefundContactDetailsSchema, allow_null=True, required=False
    )
    use_payout_link = fields.Boolean(default=True)
    service_id = fields.String(required=False, allow_null=True, allow_blank=True)


@swag_schema
class PayoutLinkCancellationRequestSchema(Schema):
    pg_payout_id = fields.String(max_length=100, required=True)
    gateway = fields.String(required=False, allow_null=True)
    hotel_id = fields.String(max_length=100, required=False, allow_null=True)


@swag_schema
class AutoRefundPayoutLinkRequestSchema(Schema):
    amount = fields.Decimal(required=True)
    currency = fields.String()
    service_id = fields.String(required=True)
    contact_info = fields.Nested(AutoRefundContactDetailsSchema, required=True)


@swag_schema
class AutoRefundViaRazorPayOrAirPayRequestSchema(Schema):
    amount = fields.Decimal(required=True)
    currency = fields.String()
    service_id = fields.String(required=True)
    payment_id = fields.String()
    gateway = fields.String()


@swag_schema
class AutoRefundViaTreeboWalletRequestSchema(Schema):
    amount = fields.Decimal(required=True)
    gateway = fields.String()
    notes = fields.List(fields.String())
    payment_id = fields.String()


@swag_schema
class AutoRefundViaTreeboCorporateRewardsRequestSchema(Schema):
    amount = fields.Decimal(required=True)
    booking_id = fields.String()
