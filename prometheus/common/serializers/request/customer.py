from marshmallow import Schema, fields
from marshmallow.decorators import post_load
from marshmallow.validate import OneOf

from prometheus.common.serializers.request.value_objects import (
    AddressSchema,
    EmploymentDetailsSchema,
    GuestMetadataSchema,
    IDProofSchema,
    PhoneSchema,
    TravelDetailsSchema,
)
from prometheus.core.api_docs import swag_schema
from shared_kernel.serializers.validators import validate_empty_string
from shared_kernel.serializers.value_objects import GSTDetailsSchema
from ths_common.constants.booking_constants import (
    ERegCardStatus,
    Genders,
    ProfileTypes,
    Salutation,
)
from ths_common.utils.common_utils import sanitize_phone_number
from ths_common.value_objects import (
    CustomerVisaDetails,
    GuestPreference,
    LoyaltyProgramDetails,
    PassportDetails,
    PhoneNumber,
    TravelBaseDetails,
    VIPDetails,
)


class PreferencesSchema(Schema):
    name = fields.String(required=True)
    values = fields.List(fields.String(), required=True)


class GuestPreferencesSchema(Schema):
    room_preferences = fields.Nested(PreferencesSchema, allow_none=True, many=True)
    fnb_preferences = fields.Nested(PreferencesSchema, allow_none=True, many=True)
    housekeeping_preferences = fields.Nested(
        PreferencesSchema, allow_none=True, many=True
    )
    transfers_preferences = fields.Nested(PreferencesSchema, allow_none=True, many=True)
    spa_preferences = fields.Nested(PreferencesSchema, allow_none=True, many=True)
    newspaper_preferences = fields.Nested(PreferencesSchema, allow_none=True, many=True)
    others_preferences = fields.Nested(PreferencesSchema, allow_none=True, many=True)

    @post_load
    def post_load(self, obj):
        return GuestPreference.from_json(obj)


class VIPDetailsSchema(Schema):
    status = fields.String(allow_none=True)
    details = fields.String(allow_none=True)

    @post_load
    def post_load(self, obj):
        return VIPDetails.from_json(obj)


class LoyaltyProgramDetailsSchema(Schema):
    program_name = fields.String(allow_none=True)
    membership_number = fields.String(allow_none=True)
    membership_level = fields.String(allow_none=True)
    current_points_balance = fields.String(allow_none=True)
    external_url = fields.String(allow_none=True)
    program_start_date = fields.Date(allow_none=True)
    program_end_date = fields.Date(allow_none=True)
    program_id = fields.String(allow_none=True)

    @post_load
    def post_load(self, data):
        return LoyaltyProgramDetails(
            program_name=data.get('program_name'),
            program_id=data.get('program_id'),
            membership_number=data.get('membership_number'),
            membership_level=data.get('membership_level'),
            current_points_balance=data.get('current_points_balance'),
            external_url=data.get('external_url'),
            program_start_date=data.get('program_start_date'),
            program_end_date=data.get('program_end_date'),
        )


class PassportDetailsSchema(Schema):
    name_on_passport = fields.String(allow_none=True)
    passport_number = fields.String(allow_none=True)
    issued_by_country = fields.String(allow_none=True)
    issued_at_place = fields.String(allow_none=True)
    issue_date = fields.String(allow_none=True)
    expiry_date = fields.String(allow_none=True)
    attachment_url = fields.String(allow_none=True)

    @post_load
    def post_load(self, obj):
        return PassportDetails.from_json(obj)


class VisaDetailsSchema(Schema):
    visa_type = fields.String(allow_none=True)
    visa_number = fields.String(allow_none=True)
    issued_by_country = fields.String(allow_none=True)
    issued_at_place = fields.String(allow_none=True)
    issue_date = fields.String(allow_none=True)
    expiry_date = fields.String(allow_none=True)
    is_employed_in_issuing_country = fields.Boolean(allow_none=True)
    attachment_url = fields.String(allow_none=True)

    @post_load
    def post_load(self, obj):
        return CustomerVisaDetails.from_json(obj)


class TravelBaseDetailsSchema(Schema):
    datetime = fields.DateTime(allow_none=True)
    mode = fields.String(allow_none=True)
    flight_or_train_number = fields.String(allow_none=True)
    flight_or_train_datetime = fields.DateTime(allow_none=True)
    transfer = fields.String(allow_none=True)

    @post_load
    def post_load(self, obj):
        return TravelBaseDetails.from_json(obj)


@swag_schema
class NewCustomerSchema(Schema):
    """
    Add guest detail Schema
    """

    salutation = fields.String(
        allow_none=True,
        validate=OneOf(
            Salutation.all(), error="'{input}' is not a valid choice for Salutation"
        ),
    )
    first_name = fields.String(required=True, validate=validate_empty_string)
    last_name = fields.String(allow_none=True, validate=validate_empty_string)
    phone = fields.Nested(PhoneSchema, allow_none=True)
    email = fields.String(allow_none=True)
    gender = fields.String(
        allow_none=True,
        validate=OneOf(
            Genders.all(), error="'{input}' is not a valid choice for Gender"
        ),
    )
    age = fields.Integer(allow_none=True)
    address = fields.Nested(AddressSchema, allow_none=True)
    nationality = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        description='Customer\'s nationality',
    )
    id_proof = fields.Nested(IDProofSchema, allow_none=True)
    image_url = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        description='To be filled by the FDM',
    )
    reference_id = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        attribute='external_ref_id',
        description='Place holder for unique identifier provided by calling service',
    )
    profile_type = fields.String(
        allow_none=True,
        validate=OneOf(
            ProfileTypes.all(),
            error="'{input}' is not a valid choice for Profile " "Type",
        ),
        missing=ProfileTypes.INDIVIDUAL.value,
    )
    gst_details = fields.Nested(
        GSTDetailsSchema, allow_none=True, description='GST information for the Invoice'
    )
    user_profile_id = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        attribute='user_profile_id',
        description='Place holder for unique profile identifier provided by calling service',
    )

    date_of_birth = fields.Date(allow_none=True)
    is_primary = fields.Boolean(default=False)
    is_booker = fields.Boolean()
    travel_details = fields.Nested(TravelDetailsSchema, allow_none=True)
    employment_details = fields.Nested(EmploymentDetailsSchema, allow_none=True)
    eregcard_status = fields.String(
        allow_none=True,
        validate=[
            OneOf(
                ERegCardStatus.all(),
                error="'{input}' is not a valid choice for e reg card Status",
            )
        ],
    )

    attachment_id = fields.String(
        allow_none=True, description='Booking attachment ID of guest id_proof'
    )
    verifier_signature = fields.String(allow_none=True)
    is_vip = fields.Boolean(default=False, allow_none=True)
    guest_preferences = fields.Nested(GuestPreferencesSchema, allow_none=True)
    guest_metadata = fields.Nested(GuestMetadataSchema, allow_none=True)

    vip_details = fields.Nested(VIPDetailsSchema, allow_none=True)
    loyalty_program_details = fields.Nested(
        LoyaltyProgramDetailsSchema, allow_none=True
    )
    passport_details = fields.Nested(PassportDetailsSchema, allow_none=True)
    visa_details = fields.Nested(VisaDetailsSchema, allow_none=True)
    arrival_details = fields.Nested(TravelBaseDetailsSchema, allow_none=True)
    departure_details = fields.Nested(TravelBaseDetailsSchema, allow_none=True)

    @post_load
    def convert_string_to_enum(self, data):
        if data.get('gender'):
            data['gender'] = Genders(data['gender'])
        if data.get('profile_type'):
            data['profile_type'] = ProfileTypes(data['profile_type'])

        phone = data.get("phone")
        if phone:
            country_code, phone_number = sanitize_phone_number(phone.number)
            if not phone_number:
                data["phone"] = None
            else:
                data["phone"] = PhoneNumber(
                    phone_number,
                    phone.country_code if phone.country_code else country_code,
                )
        if data.get('salutation'):
            data['salutation'] = Salutation(data['salutation'])


@swag_schema
class NewBookingOwnerSchema(NewCustomerSchema):
    phone = fields.Nested(PhoneSchema, required=False, allow_none=True)


@swag_schema
class UpdateCustomerSchema(Schema):
    salutation = fields.String(
        allow_none=True,
        validate=OneOf(
            Salutation.all(), error="'{input}' is not a valid choice for Salutation"
        ),
    )
    first_name = fields.String(validate=validate_empty_string)
    last_name = fields.String(allow_none=True, validate=validate_empty_string)
    phone = fields.Nested(PhoneSchema, allow_none=True)
    email = fields.String(allow_none=True)
    gender = fields.String(
        validate=OneOf(
            Genders.all(), error="'{input}' is not a valid choice for Gender"
        ),
        allow_none=True,
    )
    age = fields.Integer(allow_none=True)
    address = fields.Nested(AddressSchema, allow_none=True)
    nationality = fields.String(
        description='Customer\'s nationality',
        allow_none=True,
        validate=validate_empty_string,
    )
    id_proof = fields.Nested(IDProofSchema, allow_none=True)
    image_url = fields.Str(
        description='To be filled by the FDM',
        allow_none=True,
        validate=validate_empty_string,
    )
    reference_id = fields.Str(
        description='Place holder for unique identifier provided by calling service',
        allow_none=True,
        validate=validate_empty_string,
    )
    gst_details = fields.Nested(
        GSTDetailsSchema, description='GST information for the Invoice', allow_none=True
    )
    profile_type = fields.String(
        validate=OneOf(
            ProfileTypes.all(),
            error="'{input}' is not a valid choice for Profile " "Type",
        ),
        required=False,
    )
    user_profile_id = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        attribute='user_profile_id',
        description='Place holder for unique profile identifier provided by calling service',
    )

    date_of_birth = fields.Date(allow_none=True)
    is_primary = fields.Boolean(default=False)
    is_booker = fields.Boolean()
    travel_details = fields.Nested(TravelDetailsSchema, allow_none=True)
    employment_details = fields.Nested(EmploymentDetailsSchema, allow_none=True)
    eregcard_status = fields.String(
        allow_none=True,
        validate=[
            OneOf(
                ERegCardStatus.all(),
                error="'{input}' is not a valid choice for e reg card Status",
            )
        ],
    )
    verifier_signature = fields.String(allow_none=True)
    is_vip = fields.Boolean(allow_none=True)
    guest_preferences = fields.Nested(GuestPreferencesSchema, allow_none=True)
    guest_metadata = fields.Nested(GuestMetadataSchema, allow_none=True)

    vip_details = fields.Nested(VIPDetailsSchema, allow_none=True)
    loyalty_program_details = fields.Nested(
        LoyaltyProgramDetailsSchema, allow_none=True
    )
    passport_details = fields.Nested(PassportDetailsSchema, allow_none=True)
    visa_details = fields.Nested(VisaDetailsSchema, allow_none=True)
    arrival_details = fields.Nested(TravelBaseDetailsSchema, allow_none=True)
    departure_details = fields.Nested(TravelBaseDetailsSchema, allow_none=True)
    async_eregcard_generation = fields.Boolean(missing=False)

    @post_load
    def convert_string_to_enum(self, data):
        if data.get('gender'):
            data['gender'] = Genders(data['gender'])

        if data.get('profile_type'):
            data['profile_type'] = ProfileTypes(data['profile_type'])

        phone = data.get("phone")
        if phone:
            country_code, phone_number = sanitize_phone_number(phone.number)
            if not phone_number:
                data["phone"] = None
            else:
                data["phone"] = PhoneNumber(
                    phone_number,
                    phone.country_code if phone.country_code else country_code,
                )
        if data.get('salutation'):
            data['salutation'] = Salutation(data['salutation'])


@swag_schema
class BatchCustomerUpdateSchema(UpdateCustomerSchema):
    customer_id = fields.String(
        required=True, description='Customer id to update customer details'
    )
