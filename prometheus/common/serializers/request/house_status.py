from marshmallow import Schema, ValidationError, fields, validates_schema

from prometheus.core.api_docs import swag_schema


@swag_schema
class HouseStatusFilter(Schema):
    business_date = fields.Date(
        description="Business date for which house statistics needs to be fetched. Defaults "
        "to current business date"
    )


@swag_schema
class GuestSearchFilter(Schema):
    room_no = fields.String(required=False, allow_none=True)
    name = fields.String(required=False, allow_none=True)

    @validates_schema
    def validate_one_present(self, data, **kwargs):
        if not data.get('room_no') and not data.get('name'):
            raise ValidationError('Either room_no or name must be provided.')
