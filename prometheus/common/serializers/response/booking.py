from typing import Optional

from marshmallow import Schema, fields, post_dump, post_load
from marshmallow.validate import OneOf
from pydantic import ConfigDict, Field
from treebo_commons.money.money_field import MoneyField

from prometheus.common.serializers import RatePlanCommissionSchema
from prometheus.common.serializers.base_schema import ThsBaseSchema
from prometheus.common.serializers.request.booking import (
    BookingActionPayloadSchema,
    NewBookingActionSchema,
)
from prometheus.common.serializers.request.value_objects import (
    BilledEntityAccountSchema,
    BookingSourceSchema,
    DiscountDetailsSchema,
    GuaranteeInformationSchema,
    PoliciesSchema,
    RestrictionsSchema,
    SegmentSchema,
)
from prometheus.common.serializers.response.billing import (
    BilledEntityAccountSummarySchema,
    BookingInvoicePaymentInfoSchema,
)
from prometheus.common.serializers.response.customer import (
    CustomerIntegrationEventSchema,
    CustomerSchema,
    SearchBookingCustomerSchema,
)
from prometheus.common.serializers.response.invoice import (
    HotelBookingInvoiceTemplateSchema,
    InvoiceSchema,
)
from prometheus.common.serializers.response.package import PackageSchema
from prometheus.common.serializers.response.room_stay import (
    RoomStayConfigResponseSchema,
    RoomStayIntegrationEventsSchema,
    RoomStaySchema,
)
from prometheus.common.serializers.response.shared import ApplicationSourceTraceSchema
from prometheus.common.serializers.response.value_objects import (
    AccountDetailsSchema,
    CompanyDetailsSchema,
    TravelAgentDetailsSchema,
)
from prometheus.core.api_docs import swag_schema
from schema_instances import register_schema
from shared_kernel.api_helpers.apispec_pydantic_plugin.models import ApiBaseModel
from ths_common.constants.booking_constants import (
    ActionStatus,
    AgeGroup,
    BookingActions,
    BookingStatus,
    ReversalAllowed,
)
from ths_common.value_objects import NotAssigned


class PendingBookedChargeSchema(Schema):
    charge_id = fields.Integer()
    billed_entity_account = fields.Nested(BilledEntityAccountSchema)
    posttax_amount = MoneyField()
    pretax_amount = MoneyField()
    tax_amount = MoneyField()
    item_name = fields.String()
    sku_category_id = fields.String()
    room_no = fields.String()
    room_type = fields.String()
    charge_to = fields.List(fields.String())
    guest_id = fields.String()


class PendingBookedAllowanceSchema(PendingBookedChargeSchema):
    allowance_id = fields.Integer()
    charge_split_id = fields.Integer()


@swag_schema
class BookingInvoicePreviewsSchema(Schema):
    """
    Response for Invoice preview request
    """

    invoice_preview_non_credit_amount = MoneyField(
        required=True, description='Total preview amount of ' 'non-credit type '
    )
    invoice_preview_credit_amount = MoneyField(
        required=True, description='Total preview amount of credit type'
    )
    invoice_group_id = fields.String()
    invoice_previews = fields.Nested(InvoiceSchema, many=True)
    pending_booked_charges_for_today = fields.Nested(
        PendingBookedChargeSchema, many=True
    )
    pending_booked_allowances_for_today = fields.Nested(
        PendingBookedAllowanceSchema, many=True
    )
    accounts_with_only_payments = fields.Nested(
        BilledEntityAccountSummarySchema, many=True
    )


@swag_schema
class BookingInvoiceGeneratedSchema(BookingInvoicePreviewsSchema):
    """
    Response for Invoice generated request
    """

    invoice_preview_non_credit_amount = MoneyField(
        required=True,
        description='Total amount of ' 'non-credit type after generating ' 'invoices',
    )
    invoice_preview_credit_amount = MoneyField(
        required=True,
        description='Total amount of credit type after ' 'generating invoices',
    )


@swag_schema
class BookingGuestStayResponseSchema(Schema):
    """
    Minimalistic Guest stay schema to be responded by with booking
    """

    created_at = fields.LocalDateTime()
    modified_at = fields.LocalDateTime()
    guest_stay_id = fields.Integer(
        required=True,
        description='Unique identifier created at the time of creating the Occupancy',
    )
    status = fields.String(
        validate=OneOf(BookingStatus.all()), description='The status of the Guest'
    )
    guest_id = fields.String(
        required=False, description='The Guest allocated to the Occupancy'
    )
    age_group = fields.String(validate=OneOf(AgeGroup.all()))

    @post_load
    def convert_string_to_enum(self, data):
        if data.get('status'):
            data['status'] = BookingStatus(data['status'])
        if data.get('age_group'):
            data['age_group'] = AgeGroup(data['age_group'])


@swag_schema
class BookingRoomStayResponseSchema(Schema):
    """
    Minimalistic Room stay schema to be responded by with booking
    """

    created_at = fields.LocalDateTime()
    modified_at = fields.LocalDateTime()
    room_stay_id = fields.Integer(
        required=True,
        description='Unique Identifier generated at the time of creating the Room',
    )
    room_type_id = fields.String(
        required=True, description='Room Type ID as configured in CS'
    )
    room_id = fields.Integer(description='Room ID as configured in CS (if assigned)')
    status = fields.String(
        validate=OneOf(BookingStatus.all()), description='The status of the Room'
    )
    guest_stays = fields.Nested(
        BookingGuestStayResponseSchema,
        many=True,
        description='Information about the Guests staying in the room',
    )
    checkin_date = fields.LocalDateTime()
    checkout_date = fields.LocalDateTime()

    @post_load
    def convert_string_to_enum(self, data):
        if data.get('status'):
            data['status'] = BookingStatus(data['status'])


@swag_schema
class BasicBookingResponseSchema(Schema):
    booking_id = fields.String(
        required=True,
        description='Unique identifier generated at the time of '
        'creating a new booking',
    )
    hotel_id = fields.String(
        required=True, description='Unique identifier from Catalog Service'
    )
    checkin_date = fields.LocalDateTime(required=True)
    checkout_date = fields.LocalDateTime(required=True)
    status = fields.String(
        validate=OneOf(BookingStatus.all()),
        required=True,
        description='The status of the booking',
    )
    source = fields.Nested(
        BookingSourceSchema,
        required=True,
        description='Booking source details, for ex. OTA/MMT',
    )
    version = fields.Integer(
        description='Version number of the Booking. Should be provided at the time of editing. '
        'This is used to resolve conflicts, in case the Booking is changed by '
        'multiple clients'
    )


@swag_schema
class ShallowBookingResponse(BasicBookingResponseSchema):
    created_at = fields.LocalDateTime()
    modified_at = fields.LocalDateTime()
    actual_checkin_date = fields.LocalDateTime()
    actual_checkout_date = fields.LocalDateTime()
    comments = fields.String(description='Any user/client comments about the Booking')
    bill_id = fields.String(
        description='Unique identifier for the bill. It holds all the information about the '
        'Charges incurred and Payments made'
    )
    hold_till = fields.LocalDateTime(required=False)
    # Extra field for OTA reference_number
    reference_number = fields.String(
        description='The external Booking reference number'
    )
    # Metadata
    extra_information = fields.Dict(
        description='Any additional information may be passed in this'
    )
    room_stays = fields.Nested(
        BookingRoomStayResponseSchema,
        many=True,
        description='Details about the Room Stay',
    )
    cancellation_reason = fields.String(description="Reason for Booking Cancellation")
    cancellation_datetime = fields.LocalDateTime(
        description="Field represent time when the booking was either cancelled or marked noshow"
    )
    seller_model = fields.String(description="The seller model of the booking")
    booking_level_btc_invoice = fields.Boolean(
        description="Whether single BTC invoice will be generated for entire booking at the end, or room wise "
        "invoices are allowed"
    )
    group_name = fields.String()
    company_details = fields.Nested(CompanyDetailsSchema)
    travel_agent_details = fields.Nested(TravelAgentDetailsSchema)
    account_details = fields.Nested(
        AccountDetailsSchema, required=False, allow_none=True
    )
    default_billed_entity_category = fields.String()
    default_payment_instruction = fields.String()
    default_billed_entity_category_for_extras = fields.String()
    default_payment_instruction_for_extras = fields.String()
    segments = fields.Nested(
        SegmentSchema,
        required=False,
        allow_none=True,
        many=True,
        description='Details of segmentations',
    )
    guarantee_information = fields.Nested(
        GuaranteeInformationSchema,
        required=False,
        allow_none=True,
        description='Booking guarantee information',
    )
    discount_details = fields.Nested(
        DiscountDetailsSchema,
        required=False,
        allow_none=True,
        many=True,
        description='Booking discount details',
    )

    @post_dump
    def customise_serialized_data(self, data):
        """
        Sets the nested fields attributes from related aggregates, that are set in the context of this schema object
        :param data:
        :return:
        """
        extra_data = self.context.get('extra_data', dict())
        if not extra_data:
            return data

        if 'booking_aggregate' in extra_data:
            room_stays = extra_data['booking_aggregate'].room_stays
            data['room_stays'] = (
                BookingRoomStayResponseSchema(many=True).dump(room_stays).data
            )
        return data


@swag_schema
class BookingPaymentResponseSchema(Schema):
    """
    Booking schema with payment to be responded by with booking
    """

    booking = fields.Nested(ShallowBookingResponse)
    payment = fields.Nested(BookingInvoicePaymentInfoSchema)

    @post_load
    def convert_string_to_enum(self, data):
        if data.get('booking'):
            data['status'] = BookingStatus(data['booking']['status'])


@swag_schema
class BookingRoomConfigResponseSchema(BasicBookingResponseSchema):
    room_stays = fields.Nested(
        RoomStayConfigResponseSchema,
        many=True,
        description="Basic config for room stay",
    )

    @post_dump
    def customise_serialized_data(self, data):
        """
        Sets the nested fields attributes from related aggregates, that are set in the context of this schema object
        :param data:
        :return:
        """
        extra_data = self.context.get('extra_data', dict())
        if not extra_data:
            return data

        if 'booking_aggregate' in extra_data:
            room_stays = extra_data['booking_aggregate'].room_stays
            data['room_stays'] = (
                RoomStayConfigResponseSchema(many=True).dump(room_stays).data
            )
        return data


@swag_schema
class BookingResponseSchema(ShallowBookingResponse):
    """
    Booking Response Schema
    """

    room_stays = fields.Nested(
        RoomStaySchema, many=True, description='Details about the Room Stay'
    )
    booking_owner_id = fields.String(
        attribute='owner_id',
        required=False,
        description='The customer id for the booking owner',
    )
    customers = fields.Nested(
        CustomerSchema, many=True, description='Information about the Guests'
    )
    allowed_actions = fields.List(
        fields.String(validate=OneOf(BookingActions.all())), required=False
    )
    stay_start = fields.Date(
        description="Start date of stay to be shown on UI, derived from checkin date or actual checkin date, "
        "considering switch over time of hotel"
    )
    stay_end = fields.Date(
        description="End date of stay to be shown on UI, derived from checkout date or actual checkout date, "
        "considering free late checkout time of hotel"
    )

    @post_dump
    def customise_serialized_data(self, data):
        """
        Sets the nested fields attributes from related aggregates, that are set in the context of this schema object
        :param data:
        :return:
        """
        extra_data = self.context.get('extra_data', dict())
        if not extra_data:
            return data

        if 'booking_aggregate' in extra_data:
            booking_aggregate = extra_data['booking_aggregate']
            single_hotel_search = extra_data.get('single_hotel_search', NotAssigned)
            room_stays = booking_aggregate.room_stays
            if single_hotel_search == NotAssigned or single_hotel_search:
                data['room_stays'] = RoomStaySchema(many=True).dump(room_stays).data
            else:
                data['room_stays'] = (
                    RoomStaySchema(
                        many=True,
                        exclude=(
                            'stay_start',
                            'stay_end',
                            'allowed_actions',
                            'guest_stays.stay_start',
                            'guest_stays.stay_end',
                            'guest_stays.allowed_actions',
                        ),
                    )
                    .dump(room_stays)
                    .data
                )

            data['customers'] = (
                CustomerSchema(many=True).dump(booking_aggregate.customers).data
            )

        return data


@swag_schema
class BookingRatePlanResponseSchema(Schema):
    rate_plan_id = fields.String()
    rate_plan_reference_id = fields.String()
    name = fields.String()
    rate_plan_code = fields.String()
    is_flexi = fields.Boolean()
    package = fields.Nested(PackageSchema)
    restrictions = fields.Nested(RestrictionsSchema, allow_none=True)
    policies = fields.Nested(PoliciesSchema, allow_none=True)
    print_rate = fields.Boolean()
    suppress_rate = fields.Boolean()
    commission_details = fields.Nested(RatePlanCommissionSchema)


@swag_schema
class BookingResponseSchemaForSearch(BookingResponseSchema):
    """
    Booking Response Schema
    """

    customers = fields.Nested(
        SearchBookingCustomerSchema,
        many=True,
        description='Information about the Guests',
    )

    @post_dump
    def customise_serialized_data(self, data):
        """
        Sets the nested fields attributes from related aggregates, that are set in the context of this schema object
        :param data:
        :return:
        """
        extra_data = self.context.get('extra_data', dict())
        if not extra_data:
            return data

        if 'booking_aggregate' in extra_data:
            booking_aggregate = extra_data['booking_aggregate']
            single_hotel_search = extra_data.get('single_hotel_search', NotAssigned)
            room_stays = booking_aggregate.room_stays
            if single_hotel_search == NotAssigned or single_hotel_search:
                data['room_stays'] = RoomStaySchema(many=True).dump(room_stays).data
            else:
                data['room_stays'] = (
                    RoomStaySchema(
                        many=True,
                        exclude=(
                            'stay_start',
                            'stay_end',
                            'allowed_actions',
                            'guest_stays.stay_start',
                            'guest_stays.stay_end',
                            'guest_stays.allowed_actions',
                        ),
                    )
                    .dump(room_stays)
                    .data
                )

            data['customers'] = (
                SearchBookingCustomerSchema(many=True)
                .dump(booking_aggregate.customers)
                .data
            )
            data["booking_rate_plan_details"] = (
                BookingRatePlanResponseSchema(
                    many=True,
                    only=(
                        'name',
                        'rate_plan_id',
                        'suppress_rate',
                    ),
                )
                .dump(booking_aggregate.rate_plans)
                .data
            )

        return data


@swag_schema
class BookingIntegrationEventSchema(ShallowBookingResponse):
    room_stays = fields.Nested(
        RoomStayIntegrationEventsSchema,
        many=True,
        description='Details about the Room Stay',
    )
    booking_owner_id = fields.String(
        attribute='owner_id',
        required=False,
        description='The customer id for the booking owner',
    )
    customers = fields.Nested(
        CustomerIntegrationEventSchema,
        many=True,
        description='Information about the Guests',
    )
    deleted = fields.Boolean()

    @post_dump
    def customise_serialized_data(self, data):
        data = super(BookingIntegrationEventSchema, self).customise_serialized_data(
            data
        )
        extra_data = self.context.get('extra_data', dict())
        if not extra_data:
            return data

        if 'booking_aggregate' in extra_data:
            booking_aggregate = extra_data['booking_aggregate']
            room_stays = booking_aggregate.get_all_room_stays()
            data['room_stays'] = (
                RoomStayIntegrationEventsSchema(many=True).dump(room_stays).data
            )
            data['customers'] = (
                CustomerIntegrationEventSchema(many=True)
                .dump(booking_aggregate.get_all_customers())
                .data
            )
        return data


@swag_schema
class OverflowResponseSchema(Schema):
    booking_id = fields.String(
        required=True, description="Unique identifier of the booking"
    )
    room_type_id = fields.String(
        required=True, description='Room Type ID as configured in CS'
    )
    room_stay_id = fields.Integer(
        required=True,
        description='Unique Identifier generated at the time of creating the Room',
    )


@swag_schema
class OverflowEventSchema(Schema):
    hotel_id = fields.String(
        required=True, description='Unique identifier from Catalog Service'
    )
    booking_id = fields.String(
        required=True, description="Unique identifier of the booking"
    )
    room_type_id = fields.String(
        required=True, description='Room Type ID as configured in CS'
    )
    room_stay_id = fields.Integer(
        required=True,
        description='Unique Identifier generated at the time of creating the Room',
    )
    start_date = fields.Date()
    end_date = fields.Date()
    deleted = fields.Boolean()


@swag_schema
class BookingSearchResponseSchema(Schema):
    """
    Booking search response schema
    """

    bookings = fields.Nested(
        BookingResponseSchemaForSearch,
        many=True,
        required=True,
        description='Booking Search Results',
    )
    limit = fields.Integer(required=True, description='Number of results')
    offset = fields.Integer(
        required=True, description='Offset from which the results are fetched'
    )
    total = fields.Integer(
        required=False, description='Total number of bookings for the query'
    )

    @post_dump
    def customise_serialized_data(self, data):
        extra_data = self.context.get('extra_data', dict())
        if not extra_data:
            return data

        data['bookings'] = [booking.data for booking in extra_data]
        return data


@swag_schema
class GetBookingRoomConfigsResponseSchema(Schema):
    """
    Booking search response schema
    """

    bookings = fields.Nested(
        BookingRoomConfigResponseSchema,
        many=True,
        required=True,
        description='Booking Search Results',
    )
    limit = fields.Integer(required=True, description='Number of results')
    offset = fields.Integer(
        required=True, description='Offset from which the results are fetched'
    )
    total = fields.Integer(
        required=True, description='Total number of bookings for the query'
    )

    @post_dump
    def customise_serialized_data(self, data):
        extra_data = self.context.get('extra_data', dict())
        if not extra_data:
            return data

        data['bookings'] = [booking.data for booking in extra_data]
        return data


@swag_schema
class ActionReversalAlertSchema(Schema):
    message = fields.String(
        required=True, description="The alert message shown to the user"
    )
    payload = fields.Dict(descrption="Extra information regarding the reversal")


@swag_schema
class BookingActionReverseSideEffectsSchema(Schema):
    alerts = fields.Nested(
        ActionReversalAlertSchema, description="List of alerts to the user", many=True
    )


@swag_schema
class BookingActionSchema(NewBookingActionSchema):
    action_id = fields.String(required=True)
    created_by = fields.String(required=False)
    status = fields.String(validate=OneOf(ActionStatus.all()), required=True)
    payload = fields.Nested(BookingActionPayloadSchema, attribute="real_payload")
    reversal_side_effects = fields.Nested(BookingActionReverseSideEffectsSchema)
    reversal = fields.String(validate=OneOf(ReversalAllowed.all()), required=True)

    @post_load
    def convert_string_to_enum(self, data):
        data['status'] = ActionStatus(data['status'])


@swag_schema
class ExpenseItemSchema(ApiBaseModel):
    model_config = ConfigDict(from_attributes=True)

    expense_item_id: Optional[str] = Field(
        None,
        description="Id of the expense item. This should be used in expense and addons",
    )
    name: Optional[str] = Field(None, description="Name of the expense item")
    description: Optional[str] = Field(
        None, description="Description about the expense item"
    )
    short_name: Optional[str] = Field(
        None, description="Short name of the expense item"
    )
    sku_category_id: Optional[str] = Field(
        None, description="SKU category id defined by tax service and catalog service"
    )
    linked: Optional[bool] = Field(None)
    addon_code: Optional[str] = Field(None)
    sku_id: Optional[str] = Field(None)


@swag_schema
class BookingInvoiceTemplateResponse(Schema):
    template = fields.Nested(HotelBookingInvoiceTemplateSchema)
    invoice_url = fields.String()
    invoice_signed_url = fields.String()


@register_schema(many=True)
@swag_schema
class BookingAuditTrailSchema(ThsBaseSchema):
    created_at = fields.LocalDateTime(attribute="timestamp")
    user = fields.String()
    user_type = fields.String()
    application = fields.String()
    application_trace = fields.Nested(ApplicationSourceTraceSchema, allow_none=True)
    audit_type = fields.String()
    action_id = fields.String(
        description="The action id of the action if the audit entry if of a "
        "booking action else None"
    )
    audit_payload = fields.Dict()


@swag_schema
class ProformaInvoiceTemplateResponseSchema(Schema):
    """
    Response for Proforma Invoice Template Request
    """

    invoice_url_map = fields.Dict()
    invoice_templates = fields.Nested(HotelBookingInvoiceTemplateSchema, many=True)


@swag_schema
class RoomStayCancellationChargeResponseSchema(Schema):
    cancellation_charge = MoneyField(
        required=True, description="cancellation charge of the room stay"
    )
    room_stay_id = fields.Integer()


@swag_schema
class BookingCancellationChargeResponseSchema(Schema):
    cancellation_charge = MoneyField(
        required=True, description="Total cancellation charge of the booking"
    )
    room_stay_cancellation_charges = fields.Nested(
        RoomStayCancellationChargeResponseSchema, many=True
    )


@swag_schema
class BookingRatePlanShortResponseSchema(Schema):
    name = fields.String()
    rate_plan_code = fields.String(attribute="short_code")
    package = fields.Nested(PackageSchema, attribute="package_details")


class BookingDefaultBillingInstructionResponseSchema(Schema):
    booking_id = fields.String()
    default_billed_entity_category = fields.String()
    default_payment_instruction = fields.String()
    default_billed_entity_category_for_extras = fields.String()
    default_payment_instruction_for_extras = fields.String()


@swag_schema
class CancellationAndRefundAmountDetailsResponseSchema(Schema):
    cancellation_charge = MoneyField(
        required=True, description="Total cancellation charge of the policy"
    )
    refund_amount = MoneyField(
        required=True, description="Total refund amount under the policy"
    )


@swag_schema
class CancellationPolicyDetailsSchema(Schema):
    policy = fields.String()
    cancellation_and_refund_amount_details = fields.Nested(
        CancellationAndRefundAmountDetailsResponseSchema
    )


@swag_schema
class BookingCancellationChargeV2ResponseSchema(Schema):
    cancellation_policy_details = fields.Nested(
        CancellationPolicyDetailsSchema, many=True
    )


@swag_schema
class RefundDetailsForCancellationPolicyResponseSchema(Schema):
    refund_amount = MoneyField(allow_none=True)
    payment_mode = fields.String(allow_none=True)
