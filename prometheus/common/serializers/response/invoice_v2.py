from marshmallow import Schema, fields, pre_dump
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import MoneyField

from prometheus.common.serializers.request.value_objects import (
    BilledEntityAccountSchema,
)
from prometheus.common.serializers.response.billing import NewBillSummarySchema
from prometheus.core.api_docs import swag_schema
from shared_kernel.serializers.validators import validate_empty_string
from ths_common.constants.billing_constants import (
    InvoiceStatus,
    IssuedByType,
    IssuedToType,
)


@swag_schema
class ShallowInvoiceBillToInfoSchema(Schema):
    """
    Invoice Bill to schema
    """

    customer_id = fields.String()
    name = fields.String(required=True)
    gstin_num = fields.String(allow_none=True, validate=validate_empty_string)


@swag_schema
class ShallowInvoiceSchema(Schema):
    """
    Invoice Schema
    """

    invoice_id = fields.String(required=True)
    bill_id = fields.String(required=True)
    invoice_number = fields.String(
        required=True,
        description='Invoice number in series compliant with the GST Council rules',
    )
    pretax_amount = MoneyField(
        required=True, description='Total amount exclusive of tax'
    )
    tax_amount = MoneyField(required=True, description='Total applicable tax')
    posttax_amount = MoneyField(
        required=True, description='Total amount inclusive of tax'
    )
    invoice_date = fields.Date(required=True, description='Date of invoice generation')
    status = fields.String(validate=OneOf(InvoiceStatus.all()))
    bill_to = fields.Nested(ShallowInvoiceBillToInfoSchema, required=True)
    bill_to_type = fields.String(
        required=True, description='bill to type in the invoice'
    )
    allowed_charge_types = fields.List(
        fields.String, description='allowed charge types in the invoice'
    )
    version = fields.Integer(
        required=True,
        description='Version number of the Invoice. Should be provided at the time of editing. '
        'This is used to resolve conflicts, in case the Invoice is changed by '
        'multiple clients',
    )
    invoice_url = fields.Url(required=False)
    signed_url = fields.Url(required=False)
    issued_to_type = fields.String(validate=OneOf(IssuedToType.all()))
    issued_by_type = fields.String(validate=OneOf(IssuedByType.all()))
    hotel_invoice_id = fields.String(
        description="Invoice id for invoice issued by hotel to treebo in reseller"
    )
    hotel_invoice_number = fields.String(
        required=False,
        description='Invoice number of corresponding buy side invoice',
        dump_only=True,
        allow_none=True,
    )
    is_einvoice = fields.Boolean(dump_only=True)
    billed_entity_account = fields.Nested(BilledEntityAccountSchema, required=True)
    is_downloaded = fields.Boolean()
    is_reissue_allowed = fields.Boolean()

    @pre_dump
    def load_data(self, invoice_aggregate):
        data = invoice_aggregate.invoice
        data.hotel_invoice_number = invoice_aggregate.hotel_invoice_number
        return data


@swag_schema
class InvoiceChargeSchemaV2(Schema):
    """
    Schema for Invoice charges. Used for only testing purposes
    """

    invoice_charge_id = fields.Integer(required=True)
    charge_id = fields.Integer(required=True)
    charge_split_ids = fields.List(fields.String, required=True)
    applicable_date = fields.LocalDateTime(
        required=True,
        description="Date of consumption of the charge. "
        "A datetime string in ISO-8601 format. Timezone information is mandatory.",
    )
    pretax_amount = MoneyField(required=True, description='The pretax amount')
    tax_amount = MoneyField(required=True, description='The tax amount')
    posttax_amount = MoneyField(
        required=True, description='The charge amount inclusive of tax'
    )
    credit_note_generated_amount = MoneyField(
        description="The amount for which credit note has already been generated"
    )


@swag_schema
class InvoiceSchemaWithLineItems(ShallowInvoiceSchema):
    """
    The Invoice schema for raw data
    """

    summary = fields.Nested(NewBillSummarySchema)
    invoice_charges = fields.Nested(
        InvoiceChargeSchemaV2,
        many=True,
        allow_none=True,
        description='This key will be sent if show_raw is true in the request params',
    )

    @pre_dump
    def load_data(self, invoice_aggregate):
        data = super(InvoiceSchemaWithLineItems, self).load_data(invoice_aggregate)
        data.invoice_charges = invoice_aggregate.invoice_charges
        data.hotel_invoice_number = invoice_aggregate.hotel_invoice_number
        if hasattr(invoice_aggregate, 'summary'):
            data.summary = invoice_aggregate.summary
        return data
