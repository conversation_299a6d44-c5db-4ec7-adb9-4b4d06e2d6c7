from marshmallow import fields, pre_dump

from prometheus.common.serializers import AddonSchema, AddonSchemaV2
from prometheus.core.api_docs import swag_schema


@swag_schema
class AddonResponseSchema(AddonSchema):
    booking_id = fields.String(
        required=True, description='Unique identifier of the booking'
    )
    addon_id = fields.String(
        required=True, description='Unique identifier of the addon'
    )
    expense_ids = fields.List(
        fields.String,
        required=False,
        description='Ids of expenses created using this addon',
    )
    version = fields.Integer(description="Addon Version")

    @pre_dump
    def prepare_data(self, addon_aggregate):
        # TODO find a way to refactor this without adding attributes dynamically
        # to an object
        data = addon_aggregate.addon
        data.expense_ids = addon_aggregate.expenses
        return data


@swag_schema
class AddonResponseSchemaV2(AddonSchemaV2, AddonResponseSchema):
    expense_ids = fields.List(
        fields.String,
        required=False,
        description='Ids of expenses created using this addon',
    )
    linked = fields.Boolean(
        required=True,
        description='True in case the amount for this needs to be included into stay charge and '
        'no expense needs to be created for this type of addon',
    )

    class Meta:
        exclude = ('charge_checkin', 'charge_checkout', 'charge_other_days')
