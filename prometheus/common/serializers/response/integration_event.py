from marshmallow import Schema, fields
from marshmallow.validate import OneO<PERSON>

from prometheus.common.serializers.response import BookingIntegrationEventSchema
from prometheus.common.serializers.response.billing import BillSchema, CreditNoteSchema
from prometheus.common.serializers.response.inventory import (
    DNRResponseSchema,
    RoomTypeInventoryIntegrationEventSchema,
)
from prometheus.common.serializers.response.invoice import InvoiceSchema
from prometheus.core.api_docs import swag_schema

BOOKING_ENTITY_NAME = 'booking'
INVOICE_ENTITY_NAME = 'invoice'
BILL_ENTITY_NAME = 'bill'
DNR_ENTITY_NAME = 'dnr'
ROOM_INVENTORY_ENTITY_NAME = 'room_inventory'
ROOM_TYPE_INVENTORY_ENTITY_NAME = 'room_type_inventory'
CREDIT_NOTE_ENTITY_NAME = "credit_note"
OVERFLOW_ENTITY_NAME = "room_stay_overflow"

ENTITY_NAMES = (
    BOOKING_ENTITY_NAME,
    INVOICE_ENTITY_NAME,
    BILL_ENTITY_NAME,
    DNR_ENTITY_NAME,
    ROOM_TYPE_INVENTORY_ENTITY_NAME,
    CREDIT_NOTE_ENTITY_NAME,
)


@swag_schema
class BookingEntityIntegrationEventSchema(Schema):
    entity_name = fields.String(validate=OneOf(ENTITY_NAMES))
    payload = fields.Nested(BookingIntegrationEventSchema)


@swag_schema
class BillingEntityIntegrationEventSchema(Schema):
    entity_name = fields.String(validate=OneOf(ENTITY_NAMES))
    payload = fields.Nested(BillSchema)


@swag_schema
class InvoiceEntityIntegrationEventSchema(Schema):
    entity_name = fields.String(validate=OneOf(ENTITY_NAMES))
    payload = fields.Nested(InvoiceSchema)


@swag_schema
class DNREntityIntegrationEventSchema(Schema):
    entity_name = fields.String(validate=OneOf(ENTITY_NAMES))
    payload = fields.Nested(DNRResponseSchema)


@swag_schema
class RoomTypeInventoryEntityIntegrationEventSchema(Schema):
    entity_name = fields.String(validate=OneOf(ENTITY_NAMES))
    payload = fields.Nested(RoomTypeInventoryIntegrationEventSchema)


@swag_schema
class GenericIntegrationEventEntitySchema(Schema):
    entity_name = fields.String(validate=OneOf(ENTITY_NAMES))
    payload = fields.Dict()


@swag_schema
class CreditNoteEntityIntegrationEventSchema(Schema):
    entity_name = fields.String(validate=OneOf(ENTITY_NAMES))
    payload = fields.Nested(CreditNoteSchema)


@swag_schema
class IntegrationEventSchema(Schema):
    message_id = fields.String()
    generated_at = fields.LocalDateTime()
    events = fields.Nested(GenericIntegrationEventEntitySchema, many=True)
    booking_entity_events = fields.Nested(BookingEntityIntegrationEventSchema)
    bill_entity_events = fields.Nested(BillingEntityIntegrationEventSchema)
    invoice_entity_events = fields.Nested(InvoiceEntityIntegrationEventSchema)
    dnr_entity_events = fields.Nested(DNREntityIntegrationEventSchema)
    room_type_inventory_entity_events = fields.Nested(
        RoomTypeInventoryEntityIntegrationEventSchema
    )
    credit_note_events = fields.Nested(CreditNoteEntityIntegrationEventSchema)
