from marshmallow import Schema, fields
from treebo_commons.money.money_field import MoneyField

from prometheus.core.api_docs import swag_schema


@swag_schema
class _GuestsSchema(Schema):
    adults = fields.Integer(required=True)
    children = fields.Integer(required=True)


@swag_schema
class _DepartureReportDetailsSchema(Schema):
    amount_paid = MoneyField()
    booking_amount = MoneyField(required=True)
    booking_channel_type = fields.String(required=True)
    booking_id = fields.String(required=True)
    booking_status = fields.String(required=True)
    checkin_date = fields.Date(required=True)
    checkout_date = fields.Date(required=True)
    guest_name = fields.String(required=False)
    pre_tax_amount = MoneyField(required=True)
    room_number = fields.String(required=False)
    room_type = fields.String(required=False)
    tax_amount = fields.String(required=True)
    remaining_balance_due = MoneyField()
    number_of_guests = fields.Nested(_GuestsSchema)


@swag_schema
class DepartureReportsDetailsResponseSchema(Schema):
    reports = fields.Nested(_DepartureReportDetailsSchema, many=True)


@swag_schema
class _DepartureReportDateWiseSummarySchema(Schema):
    booking_count = fields.Integer(required=True)
    channel_name = fields.String(required=False)
    date = fields.Date(required=True)
    number_of_guests = fields.Nested(_GuestsSchema)
    number_of_rooms = fields.Integer(required=True)
    pre_tax_amount = MoneyField()
    tax_amount = MoneyField()
    total_booking_amount = MoneyField()


@swag_schema
class _DepartureReportSummarySchema(Schema):
    channel_name = fields.String(required=False)
    number_of_bookings = fields.Integer(required=True)
    number_of_guests = fields.Nested(_GuestsSchema)
    pre_tax_amount = MoneyField()
    tax_amount = MoneyField()
    total_booking_amount = MoneyField()


@swag_schema
class DepartureReportSummaryResponseSchema(Schema):
    datewise_summary = fields.Nested(_DepartureReportDateWiseSummarySchema, many=True)
    summary = fields.Nested(_DepartureReportSummarySchema, many=True)


@swag_schema
class _ArrivalReportDetailsSchema(Schema):
    amount_paid = MoneyField()
    booking_amount = MoneyField(required=True)
    booking_channel_type = fields.String(required=True)
    booking_id = fields.String(required=True)
    booking_status = fields.String(required=True)
    checkin_date = fields.Date(required=True)
    checkout_date = fields.Date(required=True)
    guest_name = fields.String(required=False)
    pre_tax_amount = MoneyField(required=True)
    room_number = fields.String(required=False)
    room_type = fields.String(required=False)
    tax_amount = fields.String(required=True)
    remaining_balance_due = MoneyField()
    number_of_guests = fields.Nested(_GuestsSchema)


@swag_schema
class ArrivalReportsDetailsResponseSchema(Schema):
    reports = fields.Nested(_DepartureReportDetailsSchema, many=True)


@swag_schema
class _ArrivalReportSummarySchema(Schema):
    booking_count = fields.Integer(required=True)
    channel_name = fields.String(required=False)
    date = fields.Date(required=True)
    number_of_guests = fields.Nested(_GuestsSchema)
    number_of_rooms = fields.Integer(required=True)
    pre_tax_amount = MoneyField()
    tax_amount = MoneyField()
    total_booking_amount = MoneyField()


@swag_schema
class _ArrivalReportDateWiseSummarySchema(Schema):
    channel_name = fields.String(required=False)
    number_of_bookings = fields.Integer(required=True)
    number_of_guests = fields.Nested(_GuestsSchema)
    pre_tax_amount = MoneyField()
    tax_amount = MoneyField()
    total_booking_amount = MoneyField()


@swag_schema
class ArrivalReportSummaryResponseSchema(Schema):
    datewise_summary = fields.Nested(_DepartureReportDateWiseSummarySchema, many=True)
    summary = fields.Nested(_DepartureReportSummarySchema, many=True)


@swag_schema
class _GuestInHouseReportDetailsSchema(Schema):
    amount_paid = MoneyField()
    booking_amount = MoneyField(required=True)
    booking_channel_type = fields.String(required=True)
    booking_id = fields.String(required=True)
    booking_status = fields.String(required=True)
    checkin_date = fields.Date(required=True)
    checkout_date = fields.Date(required=True)
    guest_name = fields.String(required=False)
    pre_tax_amount = MoneyField(required=True)
    room_number = fields.String(required=False)
    room_type = fields.String(required=False)
    tax_amount = fields.String(required=True)
    remaining_balance_due = MoneyField()
    number_of_guests = fields.Nested(_GuestsSchema)


@swag_schema
class GuestInHouseReportsDetailsResponseSchema(Schema):
    reports = fields.Nested(_DepartureReportDetailsSchema, many=True)


@swag_schema
class _GuestInHouseReportSummarySchema(Schema):
    booking_count = fields.Integer(required=True)
    channel_name = fields.String(required=False)
    date = fields.Date(required=True)
    number_of_guests = fields.Nested(_GuestsSchema)
    number_of_rooms = fields.Integer(required=True)
    pre_tax_amount = MoneyField()
    tax_amount = MoneyField()
    total_booking_amount = MoneyField()


@swag_schema
class _GuestInHouseReportDateWiseSummarySchema(Schema):
    channel_name = fields.String(required=False)
    number_of_bookings = fields.Integer(required=True)
    number_of_guests = fields.Nested(_GuestsSchema)
    pre_tax_amount = MoneyField()
    tax_amount = MoneyField()
    total_booking_amount = MoneyField()


@swag_schema
class GuestInHouseReportSummaryResponseSchema(Schema):
    datewise_summary = fields.Nested(_DepartureReportDateWiseSummarySchema, many=True)
    summary = fields.Nested(_DepartureReportSummarySchema, many=True)


@swag_schema
class _CashierReportDetailsSchema(Schema):
    amount = MoneyField()
    booking_id = fields.String()
    currency = fields.String(required=True)
    guest_name = fields.String(required=True)
    payment_date = fields.Date(required=True)
    payment_mode = fields.String(required=True)
    payment_type = fields.String(required=True)
    sub_payment_mode = fields.String(required=True)


@swag_schema
class CashierReportsDetailsResponseSchema(Schema):
    reports = fields.Nested(_CashierReportDetailsSchema, many=True)


@swag_schema
class _CashierReportModeSummarySchema(Schema):
    amount_inflow = MoneyField()
    amount_outflow = MoneyField()
    balance = MoneyField()
    mode_name = fields.String(required=False)


@swag_schema
class _CashierReportDateWiseSummarySchema(Schema):
    amount_collected = MoneyField()
    date = fields.Date(required=True)
    mode_name = fields.String(required=False)


@swag_schema
class _CashierReportCurrencyWiseSummarySchema(Schema):
    amount_inflow = MoneyField()
    amount_outflow = MoneyField()
    balance = MoneyField()
    currency_name = fields.String()


@swag_schema
class CurrencyReportSummaryResponseSchema(Schema):
    currency_wise_summary = fields.Nested(
        _CashierReportCurrencyWiseSummarySchema, many=True
    )
    datewise_summary = fields.Nested(_CashierReportDateWiseSummarySchema, many=True)
    mode_wise_summary = fields.Nested(_CashierReportModeSummarySchema, many=True)


@swag_schema
class _MisReportSummarySchema(Schema):
    arr = MoneyField()
    average_dnr_per_day = fields.Decimal()
    average_revenue_per_day = MoneyField()
    occupancy = fields.String()
    revpar = MoneyField()
    total_dnrs = fields.Integer()
    total_revenue = MoneyField()
    total_room_nights = fields.Integer()


@swag_schema
class MisReportsSummaryResponseSchema(Schema):
    summary = fields.Nested(_MisReportSummarySchema)


@swag_schema
class _CashierReportBaseCurrencyWiseSummarySchema(Schema):
    inflow_amount_in_base_currency = MoneyField()
    outflow_amount_in_base_currency = MoneyField()
    balance_in_base_currency = MoneyField()
    currency_name = fields.String()


@swag_schema
class _CashierReportPaymentCurrencyWiseSummarySchema(Schema):
    inflow_amount_in_payment_currency = MoneyField()
    outflow_amount_in_payment_currency = MoneyField()
    balance_in_payment_currency = MoneyField()
    currency_name = fields.String()


@swag_schema
class _CashierReportTransactionTypeResponseSchema(Schema):
    transaction_type = fields.String()
    inflow_amount_in_base_currency = MoneyField()
    outflow_amount_in_base_currency = MoneyField()
    balance_in_base_currency = MoneyField()
    currency_name = fields.String()


# todo keep the name cashiering here as already similar schema defined for old report use case
@swag_schema
class _CashieringReportPaymentModeSummarySchema(Schema):
    inflow_amount_in_base_currency = MoneyField()
    outflow_amount_in_base_currency = MoneyField()
    balance_in_base_currency = MoneyField()
    payment_mode = fields.String(required=False)
    currency_name = fields.String()


@swag_schema
class _SummaryByDatesGroupedOnCurrencyResponseSchema(Schema):
    payment_date = fields.Date()
    inflow_amount_in_payment_currency = MoneyField()
    outflow_amount_in_payment_currency = MoneyField()
    balance_in_payment_currency = MoneyField()
    currency_name = fields.String()


@swag_schema
class _SummaryByDateGroupedOnPaymentModeResponseSchema(
    _CashierReportBaseCurrencyWiseSummarySchema
):
    payment_date = fields.Date()
    payment_mode = fields.String()


@swag_schema
class _SummaryByRegisterGroupedOnPaymentModeResponseSchema(Schema):
    cash_register_name = fields.String()
    payment_mode = fields.String()
    inflow_amount_in_base_currency = MoneyField()
    outflow_amount_in_base_currency = MoneyField()
    balance_in_base_currency = MoneyField()
    currency_name = fields.String()


@swag_schema
class _SummaryByRegisterGroupedOnCurrencyResponseSchema(Schema):
    cash_register_name = fields.String()
    inflow_amount_in_payment_currency = MoneyField()
    outflow_amount_in_payment_currency = MoneyField()
    balance_in_payment_currency = MoneyField()
    currency_name = fields.String()


@swag_schema
class OverallCashierReportSummaryResponseSchema(Schema):
    transaction_type_wise_summary = fields.Nested(
        _CashierReportTransactionTypeResponseSchema, many=True
    )
    currency_wise_summary = fields.Nested(
        _CashierReportPaymentCurrencyWiseSummarySchema, many=True
    )
    cash_register_wise_summary = fields.Nested(
        _SummaryByRegisterGroupedOnCurrencyResponseSchema, many=True
    )
    payment_date_wise_summary = fields.Nested(
        _SummaryByDateGroupedOnPaymentModeResponseSchema, many=True
    )


@swag_schema
class CashCashierReportSummary(Schema):
    currency_wise_summary = fields.Nested(
        _CashierReportPaymentCurrencyWiseSummarySchema, many=True
    )
    cash_register_wise_summary = fields.Nested(
        _SummaryByRegisterGroupedOnCurrencyResponseSchema, many=True
    )
    payment_date_wise_summary = fields.Nested(
        _SummaryByDatesGroupedOnCurrencyResponseSchema, many=True
    )


@swag_schema
class NonCashCashierReportSummary(Schema):
    payment_mode_wise_summary = fields.Nested(
        _CashieringReportPaymentModeSummarySchema, many=True
    )
    cash_register_wise_summary = fields.Nested(
        _SummaryByRegisterGroupedOnPaymentModeResponseSchema, many=True
    )
    payment_date_wise_summary = fields.Nested(
        _SummaryByDateGroupedOnPaymentModeResponseSchema, many=True
    )


@swag_schema
class _SummayBySessionResposeSchema(
    _CashierReportPaymentCurrencyWiseSummarySchema,
    _CashierReportBaseCurrencyWiseSummarySchema,
):
    cash_register_name = fields.String()
    session_number = fields.Integer()
    start_datetime = fields.DateTime()
    end_datetime = fields.DateTime()
    payment_mode = fields.String()
    opening_balance_in_base_currency = MoneyField()
    closing_balance_in_base_currency = MoneyField()


@swag_schema
class SessionCashierReportSummary(Schema):
    session_wise_summary = fields.Nested(_SummayBySessionResposeSchema, many=True)


@swag_schema
class _SummaryByUserSessionGroupedOnCurrencyResponseSchema(
    _CashierReportPaymentCurrencyWiseSummarySchema
):
    user = fields.String()
    cash_register_name = fields.String()
    session_number = fields.Integer()
    start_datetime = fields.DateTime()
    end_datetime = fields.DateTime()


@swag_schema
class _SummaryByUserSessionGroupedOnPaymentModeResponseSchema(
    _CashierReportBaseCurrencyWiseSummarySchema
):
    user = fields.String()
    cash_register_name = fields.String()
    session_number = fields.Integer()
    start_datetime = fields.DateTime()
    end_datetime = fields.DateTime()
    payment_mode = fields.String()


@swag_schema
class UserCashierReportSummary(Schema):
    currency_wise_summary = fields.Nested(
        _SummaryByUserSessionGroupedOnCurrencyResponseSchema, many=True
    )
    payment_mode_wise_summary = fields.Nested(
        _SummaryByUserSessionGroupedOnPaymentModeResponseSchema, many=True
    )


@swag_schema
class CashierReportDetailsResponseSchema(Schema):
    added_by = fields.String()
    amount_in_base_currency = MoneyField()
    balance_in_base_currency = MoneyField()
    cash_register_name = fields.String()
    session_number = fields.Integer()
    booking_id = fields.String()
    payment_type = fields.String()  # inflow or outflow
    payment_mode = fields.String()
    payment_datetime = fields.DateTime()
    payment_currency = fields.String()
    base_currency = fields.String()
    amount_in_payment_currency = MoneyField()
    balance_in_payment_currency = MoneyField()


@swag_schema
class CashierReportPaymentUserResponseSchema(Schema):
    user = fields.String()


@swag_schema
class LedgerSchema(Schema):
    opening_balance = MoneyField()
    closing_balance = MoneyField()
    charges = MoneyField()
    payments = MoneyField()


@swag_schema
class GuestLedgerSchema(LedgerSchema):
    deposit_transferred_at_checkin = MoneyField()
    charges_transferred_at_checkout = MoneyField()


@swag_schema
class DepositLedgerSchema(GuestLedgerSchema):
    pass


@swag_schema
class ARLedgerSchema(LedgerSchema):
    charges_transferred_at_checkout = MoneyField()


class RevenueComponentsSchema(Schema):
    sku_category_id = fields.String()
    pretax_amount = MoneyField()


class POSRevenueComponentsSchema(Schema):
    seller_id = fields.String()
    seller_name = fields.String()
    revenue_components = fields.Nested(RevenueComponentsSchema, many=True)


class RevenueSchema(Schema):
    total_revenue = MoneyField()
    front_desk_revenue_components = fields.Nested(RevenueComponentsSchema, many=True)
    pos_revenue_components = fields.Nested(POSRevenueComponentsSchema, many=True)


class NonRevenueComponentsSchema(Schema):
    tax_code = fields.String()
    amount = MoneyField()


class POSNonRevenueComponentsSchema(Schema):
    seller_id = fields.String()
    seller_name = fields.String()
    non_revenue_components = fields.Nested(NonRevenueComponentsSchema, many=True)


class NonRevenueSchema(Schema):
    total_non_revenue = MoneyField()
    front_desk_non_revenue_components = fields.Nested(
        NonRevenueComponentsSchema, many=True
    )
    pos_non_revenue_components = fields.Nested(POSNonRevenueComponentsSchema, many=True)


class PaymentComponentsSchema(Schema):
    payment_mode = fields.String()
    amount = MoneyField()


class PaymentReportSchema(Schema):
    total_payments = MoneyField()
    payment_components = fields.Nested(PaymentComponentsSchema, many=True)


class _TrailBalanceReportResponseSchema(Schema):
    balance_brought_forward = MoneyField()
    revenue = fields.Nested(RevenueSchema)
    non_revenue = fields.Nested(NonRevenueSchema)
    payments = fields.Nested(PaymentReportSchema)
    total_transaction_today = MoneyField()
    grand_total = MoneyField()
    ar_ledger_activity = MoneyField()
    deposit_ledger_activity = MoneyField()
    balance_carried_forward = MoneyField()
    guest_ledger = fields.Nested(GuestLedgerSchema)
    ar_ledger = fields.Nested(ARLedgerSchema)
    deposit_ledger = fields.Nested(DepositLedgerSchema)
    hotel_balance = MoneyField()


@swag_schema
class TrailBalanceReportSummaryResponseSchema(Schema):
    prev_day = fields.Nested(_TrailBalanceReportResponseSchema)
    mtd = fields.Nested(_TrailBalanceReportResponseSchema)
    ytd = fields.Nested(_TrailBalanceReportResponseSchema)
    presigned_url = fields.String()


@swag_schema
class FlashReportRoomAndReservationFieldSchema(Schema):
    total_rooms_in_hotel = fields.Integer()
    dnr_rooms = fields.Integer()
    total_rooms_in_hotel_excluding_dnr_rooms = fields.Integer()
    occupied_rooms = fields.Integer()
    multiple_occupancy = fields.Integer()
    available_rooms = fields.Integer()
    available_rooms_excluding_dnr_rooms = fields.Integer()
    complimentary_rooms = fields.Integer()
    house_use_rooms = fields.Integer()
    day_use_rooms = fields.Integer()
    occupied_rooms_excluding_comp_and_house_use = fields.Integer()
    occupied_rooms_excluding_house_use = fields.Integer()
    occupied_rooms_excluding_comp_rooms = fields.Integer()
    percentage_of_occupied_rooms = fields.String()
    percentage_of_occupied_rooms_excluding_comp_and_house_rooms = fields.String()
    percentage_of_occupied_rooms_excluding_comp = fields.String()
    percentage_of_occupied_rooms_excluding_house = fields.String()
    percentage_of_occupied_rooms_excluding_comp_and_dnr = fields.String()
    percentage_of_occupied_rooms_excluding_house_and_dnr = fields.String()
    percentage_of_occupied_rooms_excluding_dnrs = fields.String()
    in_house_adults = fields.Integer()
    in_house_children = fields.Integer()
    total_in_house_persons = fields.Integer()
    in_house_non_corporate_persons = fields.Integer()
    in_house_corporate_persons = fields.Integer()
    in_house_corporate_rooms = fields.Integer()
    in_house_travel_agent_rooms = fields.Integer()
    arrival_rooms = fields.Integer()
    arrival_persons = fields.Integer()
    walk_in_rooms = fields.Integer()
    walk_in_persons = fields.Integer()
    extended_stay_rooms = fields.Integer()
    extended_stay_persons = fields.Integer()
    departure_rooms = fields.Integer()
    departure_persons = fields.Integer()
    early_departure_rooms = fields.Integer()
    early_departure_persons = fields.Integer()
    no_show_rooms = fields.Integer()
    no_show_persons = fields.Integer()
    cancelled_reservation_for_today = fields.Integer()
    late_reservation_cancellations_for_today = fields.Integer()
    reservation_made_today = fields.Integer()
    reservation_cancellation_made_today = fields.Integer()
    room_nights_reserved_today = fields.Integer()
    clean_rooms = fields.Integer()
    dirty_rooms = fields.Integer()
    oos_rooms = fields.Integer()
    double_as_singles = fields.Integer()
    max_occupancy = fields.Integer()
    percentage_of_occupied_beds = fields.String()
    percentage_of_multiple_occupancy = fields.String()


@swag_schema
class FlashReportFinanceFieldSchema(Schema):
    pretax_adr = MoneyField()
    posttax_adr = MoneyField()
    pretax_adr_minus_comp = MoneyField()
    posttax_adr_minus_comp = MoneyField()
    pretax_adr_minus_house = MoneyField()
    posttax_adr_minus_house = MoneyField()
    pretax_adr_minus_house_and_comp = MoneyField()
    posttax_adr_minus_house_and_comp = MoneyField()
    pretax_average_person_rate = MoneyField()
    posttax_average_person_rate = MoneyField()
    pretax_room_revenue = MoneyField()
    posttax_room_revenue = MoneyField()
    pretax_fnb_revenue = MoneyField()
    posttax_fnb_revenue = MoneyField()
    pretax_other_revenue = MoneyField()
    posttax_other_revenue = MoneyField()
    pretax_total_revenue = MoneyField()
    posttax_total_revenue = MoneyField()
    pretax_member_revenue = MoneyField()
    posttax_member_revenue = MoneyField()
    pretax_member_room_revenue = MoneyField()
    posttax_member_room_revenue = MoneyField()
    pretax_total_revenue_per_person = MoneyField()
    posttax_total_revenue_per_person = MoneyField()
    posttax_rev_par = MoneyField()
    pretax_rev_par = MoneyField()
    payment = MoneyField()
    refund = MoneyField()
    credit_offered = MoneyField()


@swag_schema
class FlashReportForecastFieldSchema(Schema):
    tomorrow_arrival_persons = fields.Integer()
    tomorrow_arrival_rooms = fields.Integer()
    tomorrow_departure_rooms = fields.Integer()
    tomorrow_departure_persons = fields.Integer()
    percentage_of_room_occupied_for_tomorrow = fields.String()
    percentage_of_occupied_rooms_for_next_seven_days = fields.String()
    percentage_of_projected_occupied_rooms_for_current_month = fields.String()
    percentage_of_projected_occupied_rooms_for_current_year = fields.String()


@swag_schema
class FlashReportPOSFieldSchema(Schema):
    seller_id = fields.String()
    seller_name = fields.String()
    pretax_revenue = MoneyField()
    posttax_revenue = MoneyField()


@swag_schema
class FlashManagerReportResponseSchema(
    FlashReportRoomAndReservationFieldSchema,
    FlashReportFinanceFieldSchema,
    FlashReportForecastFieldSchema,
):
    adr = MoneyField()
    adr_minus_comp = MoneyField()
    adr_minus_house = MoneyField()
    adr_minus_house_and_comp = MoneyField()
    average_person_rate = MoneyField()
    room_revenue = MoneyField()
    fnb_revenue = MoneyField()
    other_revenue = MoneyField()
    total_revenue = MoneyField()
    member_revenue = MoneyField()
    member_room_revenue = MoneyField()
    total_revenue_per_person = MoneyField()
