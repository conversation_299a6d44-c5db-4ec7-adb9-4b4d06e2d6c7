from marshmallow import Schema, fields

from prometheus.common.serializers.request.billing import PayoutLikSchema
from prometheus.core.api_docs import swag_schema


@swag_schema
class AutoRefundResponseSchema(Schema):
    refund_order_id = fields.String()
    payout_link = fields.Nested(PayoutLikSchema, allow_null=True, required=False)


@swag_schema
class PayoutLinkSchema(Schema):
    pg_payout_id = fields.String()
    contact_id = fields.String()
    status = fields.String()
    short_url = fields.String()
    expire_by = fields.Integer()


@swag_schema
class AutoRefundViaPayoutLinkResponseSchema(Schema):
    currency = fields.String()
    gateway = fields.String()
    amount = fields.Decimal()
    payout_link = fields.Nested(PayoutLinkSchema)


@swag_schema
class AutoRefundViaRazorPayOrAirPayResponseSchema(Schema):
    receipt = fields.String()
    currency = fields.String()
    gateway = fields.String()
    pg_order_id = fields.String()
    refund_order_id = fields.String()
    amount = fields.Decimal()
    refund_meta = fields.String()
    notes = fields.List(fields.String())


@swag_schema
class AutoRefundViaTreeboWalletResponseSchema(Schema):
    receipt = fields.String()
    currency = fields.String()
    gateway = fields.String()
    amount = fields.Decimal()
    notes = fields.List(fields.String())
    pg_order_id = fields.String(allow_none=True)
    refund_order_id = fields.String()
    refund_meta = fields.Dict(allow_none=True)


@swag_schema
class AutoRefundViaTreeboCorporateRewardsResponseSchema(Schema):
    order_id = fields.String()
    booking_id = fields.String(allow_none=True)
    payment_id = fields.String()
    amount = fields.Decimal()
    customer_id = fields.String()
    currency = fields.String()
    gateway = fields.String()
    notes = fields.String()
    status = fields.String()
    entity_type = fields.String()
    entity_id = fields.String()
    extra_id = fields.String()
    payment_source = fields.String(allow_none=True)
    payment_link = fields.String(allow_none=True)
