from marshmallow import Schema, fields

from prometheus.domain.billing.domain_events.schema.billing import (
    BillAmountUpdatedEventSchema,
    BillCreatedEventSchema,
    ChargeAddedEventSchema,
    ChargeCancelledEventSchema,
    ChargeModifiedEventSchema,
    InvoiceBillToUpdateEventSchema,
    InvoiceGeneratedEventSchema,
    InvoiceRegeneratedEventSchema,
    PaymentEventSchema,
    PreviewInvoiceGeneratedEventSchema,
)
from prometheus.domain.booking.domain_events.schema.booking import (
    BookingCreatedEventSchema,
    BookingDetailsUpdatedEventSchema,
    BookingReCreatedEventSchema,
    CustomerUpdatedEventSchema,
    GuestCheckedOutEventSchema,
    GuestStayAddedEventSchema,
    GuestStayCancelledEventSchema,
    NoShowEventSchema,
    RoomStayAddedEventSchema,
    RoomStayCancelledEventSchema,
    RoomStayDatesChangedEventSchema,
    RoomStayOccupancyChangedEventSchema,
    RoomTypeChangedEventSchema,
)

__all__ = ['DomainEventsSchema']


class DomainEventsSchema(Schema):
    booking_created = fields.Nested(BookingCreatedEventSchema)
    booking_details_updated = fields.Nested(BookingDetailsUpdatedEventSchema)
    booking_recreated = fields.Nested(BookingReCreatedEventSchema)
    customer_updated = fields.Nested(CustomerUpdatedEventSchema)
    checkin_performed = fields.Nested(GuestCheckedOutEventSchema)
    checkout_performed = fields.Nested(GuestCheckedOutEventSchema)
    guest_stay_added = fields.Nested(GuestStayAddedEventSchema)
    guest_stay_cancelled = fields.Nested(GuestStayCancelledEventSchema)
    guest_stay_marked_noshow = fields.Nested(NoShowEventSchema)
    room_stay_added = fields.Nested(RoomStayAddedEventSchema)
    room_stay_cancelled = fields.Nested(RoomStayCancelledEventSchema)
    room_stay_dates_changed = fields.Nested(RoomStayDatesChangedEventSchema)
    room_stay_marked_noshow = fields.Nested(NoShowEventSchema, many=True)
    room_stay_occupancy_changed = fields.Nested(RoomStayOccupancyChangedEventSchema)
    room_stay_room_type_changed = fields.Nested(RoomTypeChangedEventSchema)
    bill_amount_updated = fields.Nested(BillAmountUpdatedEventSchema)
    bill_created = fields.Nested(BillCreatedEventSchema)
    bill_recreated = fields.Nested(BillCreatedEventSchema)
    charge_added = fields.Nested(ChargeAddedEventSchema)
    charge_cancelled = fields.Nested(ChargeCancelledEventSchema)
    charge_modified = fields.Nested(ChargeModifiedEventSchema)
    invoice_bill_to_updated = fields.Nested(InvoiceBillToUpdateEventSchema)
    invoice_generated = fields.Nested(InvoiceGeneratedEventSchema)
    invoice_regenerated = fields.Nested(InvoiceRegeneratedEventSchema)
    payment_added = fields.Nested(PaymentEventSchema, many=True)
    preview_invoice_generated = fields.Nested(PreviewInvoiceGeneratedEventSchema)
    refund_added = fields.Nested(PaymentEventSchema, many=True)
