from marshmallow import fields, post_load
from marshmallow.validate import OneOf

from prometheus.common.serializers import BaseExpenseSchema, ExpenseServiceContextSchema
from prometheus.core.api_docs import swag_schema
from ths_common.constants.booking_constants import ExpenseStatus


@swag_schema
class ExpenseSchema(BaseExpenseSchema):
    """
    Expense response schema
    """

    expense_id = fields.Integer(required=True)
    charge_id = fields.Integer(required=True)
    status = fields.String(
        validate=OneOf(ExpenseStatus.all()),
        required=True,
        description='Status of the expense',
    )
    via_addon = fields.Boolean()
    sku_id = fields.String()
    service_context = fields.Nested(
        ExpenseServiceContextSchema,
        allow_none=True,
        required=False,
        description="Details of the addon service",
    )

    @post_load
    def convert_string_to_enum(self, data):
        data['status'] = ExpenseStatus(data['status'])
