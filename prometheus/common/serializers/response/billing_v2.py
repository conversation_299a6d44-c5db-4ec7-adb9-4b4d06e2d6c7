from marshmallow import Schema, fields
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import MoneyField

from prometheus.common.serializers import PayoutLikSchema
from prometheus.common.serializers.base_schema import ThsBaseSchema
from prometheus.common.serializers.request.validators import UserDefinedEnumValidator
from prometheus.common.serializers.request.value_objects import (
    BilledEntityAccountSchema,
    ChargeItemSchema,
    NameSchema,
)
from prometheus.common.serializers.response.billing import NewBillSummarySchema
from prometheus.common.serializers.response.invoice_v2 import (
    ShallowInvoiceBillToInfoSchema,
)
from prometheus.core.api_docs import swag_schema
from prometheus.infrastructure.external_clients.catalog_service.user_defined_enums import (
    UserDefinedEnums,
)
from schema_instances import register_schema
from shared_kernel.serializers.validators import validate_empty_string
from ths_common.constants.billing_constants import (
    BilledEntityStatus,
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    CreditNoteStatus,
    IssuedByType,
    IssuedToType,
    PaymentStatus,
    PaymentTypes,
)


@swag_schema
class PaymentSplitResponseSchema(Schema):
    payment_split_id = fields.String(required=True)
    billed_entity_account = fields.Nested(BilledEntityAccountSchema, required=True)
    amount = MoneyField(required=True)
    payment_mode = fields.String(required=True)


@swag_schema
class ShallowPaymentResponseSchema(Schema):
    """
    New Payment Schema
    """

    payment_id = fields.String(required=True)
    payment_splits = fields.Nested(PaymentSplitResponseSchema, required=True, many=True)
    amount = MoneyField(required=True)
    payment_mode = fields.String(
        validate=UserDefinedEnumValidator(UserDefinedEnums.PAYMENT_MODE), required=True
    )
    payment_mode_sub_type = fields.String(
        allow_none=True,
        validate=UserDefinedEnumValidator(UserDefinedEnums.PAYMENT_MODE_SUB_TYPES),
    )
    payment_type = fields.String(
        validate=OneOf(
            PaymentTypes.all(), error="'{input}' is not a valid choice for Payment Type"
        ),
        required=True,
    )
    status = fields.String(
        validate=OneOf(
            PaymentStatus.all(),
            error="'{input}' is not a valid choice for Payment Status",
        ),
        required=True,
    )
    paid_by = fields.String(
        validate=UserDefinedEnumValidator(UserDefinedEnums.PAID_BY), required=True
    )
    paid_to = fields.String(
        validate=UserDefinedEnumValidator(UserDefinedEnums.PAID_TO), required=True
    )
    payment_ref_id = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        description='Payment reference ID as given by the payment gateway.',
    )
    comment = fields.String(allow_none=True, description='Optional comment field')
    payer = fields.String(
        allow_none=True, description='Stores guest/company making payment for booking'
    )
    confirmed = fields.Boolean(missing=True, default=True)
    date_of_payment = fields.LocalDateTime(
        required=True, description="A datetime string in ISO-8601 format, with timezone"
    )
    payment_receipt_url = fields.Url(required=False, dump_only=True)
    payor_billed_entity_id = fields.Integer(
        description='This field signifies which Billed Entity has made the payment.'
    )
    payout_details = fields.Nested(PayoutLikSchema, required=False, allow_none=True)


@swag_schema
class ShallowAllowanceResponseSchema(Schema):
    allowance_id = fields.Integer()
    tax_amount = MoneyField()
    pretax_amount = MoneyField(allow_none=True, description='The pretax amount')
    posttax_amount = MoneyField()
    is_active = fields.Boolean()
    invoice_id = fields.String(required=False)
    credit_note_id = fields.String(required=False)
    billed_entity_account = fields.Nested(BilledEntityAccountSchema)
    status = fields.String(
        validate=OneOf(ChargeStatus.all()),
        required=True,
        description='Status of allowance',
    )
    posting_date = fields.Date()


@swag_schema
class ShallowChargeSplitSchema(Schema):
    charge_split_id = fields.Integer(required=True)
    pre_tax = MoneyField(required=True)
    tax = MoneyField(required=True)
    post_tax = MoneyField(required=True)
    invoice_id = fields.String(required=False)
    credit_note_id = fields.String(required=False)
    percentage = fields.Decimal(required=True)
    billed_entity_account = fields.Nested(BilledEntityAccountSchema)
    charge_type = fields.String(
        validate=OneOf(ChargeTypes.all()),
        allow_none=True,
        description='credit - Guest can check-out without paying. The expenses will be billed '
        'to the company non-credit - Guest has to clear the dues at the time of '
        'check-out',
    )
    bill_to_type = fields.String(
        validate=OneOf(ChargeBillToTypes.all()),
        allow_none=True,
        description='Whom should the expenses be billed to',
    )
    payment_id = fields.Integer()
    allowances = fields.Nested(ShallowAllowanceResponseSchema, many=True)


@swag_schema
class ShallowChargeSchema(Schema):
    charge_id = fields.Integer(required=True)
    applicable_date = fields.LocalDateTime(
        required=True,
        description="Date of consumption of the charge. "
        "A datetime string in ISO-8601 format. Timezone information is mandatory.",
    )
    posting_date = fields.Date()
    item = fields.Nested(ChargeItemSchema, description='Details about the item')
    pretax_amount = MoneyField(required=True, description='The pretax amount')
    tax_amount = MoneyField(required=True, description='The tax amount')
    posttax_amount = MoneyField(
        required=True, description='The charge amount inclusive of tax'
    )
    status = fields.String(
        validate=OneOf(ChargeStatus.all()),
        required=True,
        description='Status of the charge',
    )
    comment = fields.String(description='Any User/Client comment regarding the Charge')
    charge_splits = fields.Nested(
        ShallowChargeSplitSchema,
        many=True,
        description='Details of the split, in case of multiple invoices',
    )
    charge_to = fields.List(fields.String, required=True)
    inclusion_charge_ids = fields.List(fields.Integer)
    linked_addon_charge_ids = fields.List(fields.Integer)
    split_allowed = fields.Boolean()
    is_inclusion_charge = fields.Boolean()


@swag_schema
class BilledEntityAccountResponseSchema(Schema):
    account_number = fields.Integer(required=True)
    folio_number = fields.Integer(required=True)
    locked = fields.Boolean(required=True)
    invoiced = fields.Boolean(required=True)
    net_balance = MoneyField()
    account_type = fields.String(
        validate=OneOf(ChargeTypes.all()), allow_none=True, required=True
    )
    is_allowance_account = fields.Boolean(required=True)


@swag_schema
class ShallowBilledEntitySchema(Schema):
    billed_entity_id = fields.Integer(required=True)
    name = fields.Nested(NameSchema, required=True)
    accounts = fields.Nested(
        BilledEntityAccountResponseSchema, many=True, required=True
    )
    category = fields.String(required=True)
    status = fields.String(validate=OneOf(BilledEntityStatus.all()))


@register_schema()
@swag_schema
class ShallowBillResponseSchemaV2(ThsBaseSchema):
    app_id = fields.String(dump_only=True)
    bill_id = fields.String(dump_only=True)
    parent_reference_number = fields.String(required=True)
    vendor_id = fields.String()
    version = fields.Integer(dump_only=True)
    summary = fields.Nested(NewBillSummarySchema, dump_only=True)
    payments = fields.Nested(
        ShallowPaymentResponseSchema, many=True, description='List of payments'
    )
    charges = fields.Nested(
        ShallowChargeSchema, many=True, description='List of charges'
    )
    status = fields.String()
    billed_entities = fields.Nested(ShallowBilledEntitySchema, many=True)


@swag_schema
class ShallowCreditNoteSchema(Schema):
    bill_id = fields.String()
    credit_note_id = fields.String(required=True)
    invoice_ids = fields.List(fields.String)
    credit_note_number = fields.String(
        required=True,
        description='credit note number in series compliant with the GST Council rules',
    )
    credit_note_url = fields.Url(required=False)
    credit_note_date = fields.Date(
        required=True, description='Date of credit note generation'
    )
    pretax_amount = MoneyField(required=True, description='The pretax amount')
    tax_amount = MoneyField(required=True, description='The tax amount')
    posttax_amount = MoneyField(
        required=True, description='The charge amount inclusive of tax'
    )
    status = fields.String(
        validate=OneOf(CreditNoteStatus.all()),
        required=True,
        description='Status of the credit note',
    )
    issued_to = fields.Nested(ShallowInvoiceBillToInfoSchema, required=True)
    issued_to_type = fields.String(validate=OneOf(IssuedToType.all()))
    issued_by_type = fields.String(validate=OneOf(IssuedByType.all()))
    hotel_credit_note_id = fields.String(
        required=False,
        description="credit note id for credit note issued by hotel, "
        "to treebo, "
        "in case this invoice is generated for hotel on "
        "reseller",
    )
    hotel_credit_note_number = fields.String(
        required=False,
        description='credit note number of corresponding buy side invoice',
        dump_only=True,
        allow_none=True,
    )
    version = fields.Integer()
    signed_url = fields.Url(required=False)
    billed_entity_account = fields.Nested(BilledEntityAccountSchema, required=True)
    comment = fields.String(
        description='Any User/Client comment regarding the credit note'
    )


@swag_schema
class BillSummaryResponseSchemaV2(Schema):
    app_id = fields.String(required=True)
    bill_id = fields.String(dump_only=True)
    bill_date = fields.LocalDateTime(
        required=True,
        description="A datetime string in ISO-8601 format. Timezone information is mandatory.",
    )
    parent_reference_number = fields.String(required=True)
    vendor_id = fields.String()
    version = fields.Integer(required=True)
    summary = fields.Nested(NewBillSummarySchema)
