from marshmallow import Schema, fields, post_load
from marshmallow.validate import OneOf

from prometheus.core.api_docs import swag_schema
from ths_common.constants.hotel_constants import ManagedBy


@swag_schema
class HotelConfigResponseSchema(Schema):
    hotel_id = fields.String()
    migration_start_date = fields.LocalDateTime()
    migration_end_date = fields.LocalDateTime()
    live_date = fields.LocalDateTime()
    managed_by = fields.String(validate=OneOf(ManagedBy.all()))

    @post_load
    def convert_string_to_enum(self, data):
        if data.get('managed_by'):
            data['managed_by'] = ManagedBy(data['managed_by'])
