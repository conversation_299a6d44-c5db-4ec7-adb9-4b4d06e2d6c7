from marshmallow import Schema, fields
from treebo_commons.money.money_field import MoneyField

from prometheus.core.api_docs import swag_schema


@swag_schema
class HouseStatusResponseSchema(Schema):
    room_id = fields.Integer()
    housekeeping_status = fields.String()
    reservation_statuses = fields.List(fields.String)


@swag_schema
class _RoomsAndGuestsCountSchema(Schema):
    rooms = fields.Integer()
    guests = fields.Integer()
    vip_guests = fields.Integer()


@swag_schema
class HouseStatisticsResponseSchema(Schema):
    business_date = fields.Date()
    available_rooms = fields.Integer()
    occupied_rooms = fields.Integer()
    dnr_rooms = fields.Integer()
    early_departures = fields.Integer()
    day_use_rooms = fields.Integer()
    day_of_arrival_cancellations = fields.Integer()
    total_rooms_and_guests_under_group_booking = fields.Nested(
        _RoomsAndGuestsCountSchema
    )
    total_rooms_and_guests_under_individual_booking = fields.Nested(
        _RoomsAndGuestsCountSchema
    )
    total_rooms = fields.Integer()
    total_eod_projected_dnr_rooms = fields.Integer()
    eod_projected_rooms_available_for_sale = fields.Integer()
    occupancy = fields.Decimal(as_string=True)
    total_room_revenue = MoneyField()
    arr = MoneyField()


@swag_schema
class HouseStatusChargeTypeResponseSchema(Schema):
    complimentary = fields.Integer()
    house_use = fields.Integer()
    others = fields.Integer()


@swag_schema
class HouseStatusArrivalResponseSchema(Schema):
    due_in_room_assigned = fields.Nested(HouseStatusChargeTypeResponseSchema)
    due_in_room_unassigned = fields.Nested(HouseStatusChargeTypeResponseSchema)
    arrived = fields.Nested(HouseStatusChargeTypeResponseSchema)


@swag_schema
class HouseStatusDeparturesResponseSchema(Schema):
    due_out = fields.Nested(HouseStatusChargeTypeResponseSchema)
    departed = fields.Nested(HouseStatusChargeTypeResponseSchema)


@swag_schema
class HouseStatusArrivalDepartureResponseSchema(Schema):
    arrivals = fields.Nested(HouseStatusArrivalResponseSchema)
    departures = fields.Nested(HouseStatusDeparturesResponseSchema)


@swag_schema
class GuestDetailsSchema(Schema):
    guest_id = fields.Str(required=True)
    first_name = fields.Str(required=True)
    last_name = fields.Str(allow_none=True)
    profile_id = fields.Str(allow_none=True)
    salutation = fields.Str(allow_none=True)
    is_vip = fields.Bool(allow_none=True)


@swag_schema
class HotelBookingDetailsResponseSchema(Schema):
    booking_id = fields.Str(required=True)
    comments = fields.Str(allow_none=True)
    checkin_date = fields.Str(required=True)
    checkout_date = fields.Str(required=True)
    hotel_id = fields.Str(required=True)
    room_no = fields.Str(allow_none=True)
    room_type = fields.Str(required=True)
    room_stay_id = fields.Str(required=True)
    disallow_charge_addition = fields.Bool(allow_none=True, default=False)
    rate_plan_code = fields.Str(allow_none=True)
    package_name = fields.Str(allow_none=True)
    adult = fields.Integer(allow_none=True)
    child = fields.Integer(allow_none=True)
    group_name = fields.Str(allow_none=True)
    origin = fields.Str(allow_none=True)
    guest_details = fields.Nested(GuestDetailsSchema, required=True)
