from marshmallow import Schema, fields

from prometheus.core.api_docs import swag_schema


@swag_schema
class CriticalTaskEntity(Schema):
    type = fields.String(description="Type of critical task", required=True)
    entity_name = fields.String(description="Entity name of critical tasks")

    entity_ids = fields.List(fields.String, description="Entity ids of critical tasks")
    expiry_time = fields.LocalDateTime(description="End time of critical tasks")
    remaining_time = fields.Integer(
        description="Remaining time to resolve critical tasks in seconds"
    )
    count = fields.Integer(description="Count of critical tasks")


@swag_schema
class CriticalTasksResponseSchema(Schema):
    hotel_id = fields.String(
        description="Search critical tasks by Hotel ID provided by Catalog Service.",
        required=True,
    )
    critical_tasks = fields.Nested(CriticalTaskEntity, required=True, many=True)
