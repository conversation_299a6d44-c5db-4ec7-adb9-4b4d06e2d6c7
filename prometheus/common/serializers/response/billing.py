from marshmallow import (
    <PERSON>hem<PERSON>,
    ValidationError,
    fields,
    post_dump,
    pre_dump,
    validates_schema,
)
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import MoneyField
from treebo_commons.utils import dateutils

from prometheus.common.serializers import (
    ChargeItemSchema,
    ItemCodeSchema,
    NewPaymentSchema,
    VendorDetailsSchema,
)
from prometheus.common.serializers.base_schema import ThsBaseSchema
from prometheus.common.serializers.request.billing import (
    AllowanceSchema,
    NewPaymentSplitSchema,
    PayoutLikSchema,
)
from prometheus.common.serializers.request.value_objects import (
    BilledEntityAccountSchema,
)
from prometheus.common.serializers.response.value_objects import (
    BookingBillParentInfoSchema,
    ChargeComponentSchema,
    CumulativeTaxBreakupSchema,
    InvoiceBillToInfoSchema,
    InvoiceIssuedByInfoSchema,
    NameSchema,
)
from prometheus.core.api_docs import swag_schema
from schema_instances import register_schema
from shared_kernel.serializers.validators import validate_empty_string
from shared_kernel.serializers.value_objects import TaxDetailSchema
from ths_common.constants.billing_constants import (
    BilledEntityStatus,
    ChargeBillToTypes,
    ChargeSplitType,
    ChargeStatus,
    ChargeTypes,
    CreditNoteStatus,
    IssuedByType,
    IssuedToType,
)


@swag_schema
class PaymentSplitSchema(NewPaymentSplitSchema):
    payment_split_id = fields.String(required=True)
    payment_mode = fields.String(required=True)
    payment_type = fields.String(required=True)


@swag_schema
class PaymentSchema(NewPaymentSchema):
    """
    Payment response schema
    """

    payment_id = fields.String(required=True)
    payment_splits = fields.Nested(PaymentSplitSchema, required=True, many=True)
    date_of_payment = fields.LocalDateTime(
        required=True, description="A datetime string in ISO-8601 format, with timezone"
    )
    payment_receipt_url = fields.Url(required=False, dump_only=True)
    payout_details = fields.Nested(PayoutLikSchema, required=False, allow_none=True)


@swag_schema
class AllowanceResponseSchema(AllowanceSchema):
    allowance_id = fields.Integer()
    tax_amount = MoneyField()
    posttax_amount = MoneyField()
    is_active = fields.Boolean()
    invoice_id = fields.String(required=False)
    credit_note_id = fields.String(required=False)
    billed_entity_account = fields.Nested(BilledEntityAccountSchema)
    status = fields.String(
        validate=OneOf(ChargeStatus.all()),
        required=True,
        description='Status of allowance',
    )
    posting_date = fields.Date()


@swag_schema
class ChargeSplitSchema(Schema):
    """
    Charge split schema
    """

    charge_split_id = fields.Integer(required=True)
    charge_to = fields.String(required=True)
    pre_tax = MoneyField(required=True)
    tax = MoneyField(required=True)
    post_tax = MoneyField(required=True)
    invoice_id = fields.String(required=False)
    credit_note_id = fields.String(required=False)
    tax_details = fields.Nested(TaxDetailSchema, many=True)
    percentage = fields.Decimal(required=True)
    billed_entity_account = fields.Nested(BilledEntityAccountSchema)
    charge_type = fields.String(
        validate=OneOf(ChargeTypes.all()),
        allow_none=True,
        description='credit - Guest can check-out without paying. The expenses will be billed '
        'to the company non-credit - Guest has to clear the dues at the time of '
        'check-out',
    )
    bill_to_type = fields.String(
        validate=OneOf(ChargeBillToTypes.all()),
        allow_none=True,
        description='Whom should the expenses be billed to',
    )
    payment_id = fields.Integer()
    allowances = fields.Nested(AllowanceResponseSchema, many=True)
    posttax_amount_post_allowance = MoneyField(
        dump_only=True, description='Total posttax amount post allowances'
    )
    pretax_amount_post_allowance = MoneyField(
        dump_only=True, description='Total prettax amount post allowances'
    )
    tax_amount_post_allowance = MoneyField(
        dump_only=True, description='Total tax amount post allowances'
    )
    tax_details_post_allowance = fields.Nested(TaxDetailSchema, many=True)


@swag_schema
class ShallowChargeSplitSchema(Schema):
    charge_split_id = fields.Integer(required=True)
    charge_to = fields.String(required=True)
    invoice_id = fields.String(required=False)
    credit_note_id = fields.String(required=False)


@swag_schema
class ChargeSchema(Schema):
    """
    Charge Schema
    """

    charge_id = fields.Integer(required=True)
    created_by = fields.String(required=True, validate=validate_empty_string)
    applicable_date = fields.LocalDateTime(
        required=True,
        description="Date of consumption of the charge. "
        "A datetime string in ISO-8601 format. Timezone information is mandatory.",
    )
    item = fields.Nested(ChargeItemSchema, description='Details about the item')
    pretax_amount = MoneyField(required=True, description='The pretax amount')
    tax_amount = MoneyField(required=True, description='The tax amount')
    posttax_amount = MoneyField(
        required=True, description='The charge amount inclusive of tax'
    )
    tax_details = fields.Nested(TaxDetailSchema, many=True)
    type = fields.String(
        validate=OneOf(ChargeTypes.all()),
        allow_none=True,
        description='credit - Guest can check-out without paying. The expenses will be billed to the '
        'company non-credit - Guest has to clear the dues at the time of check-out',
    )
    bill_to_type = fields.String(
        validate=OneOf(ChargeBillToTypes.all()),
        allow_none=True,
        description='Whom should the expenses be billed to',
    )
    status = fields.String(
        validate=OneOf(ChargeStatus.all()),
        required=True,
        description='Status of the charge',
    )
    comment = fields.String(description='Any User/Client comment regarding the Charge')
    charge_split_type = fields.String(
        validate=OneOf(ChargeSplitType.all()),
        required=True,
        description='What is the charge split type',
    )
    charge_splits = fields.Nested(
        ChargeSplitSchema,
        many=True,
        description='Details of the split, in case of multiple invoices',
    )
    charge_components = fields.Nested(
        ChargeComponentSchema,
        many=True,
        description="In case a charge is built using multiple booking components - "
        "room_rent, other charges, this value will contain the breakup of "
        "posttax_amount of charge between those components",
    )
    charge_to = fields.List(fields.String, required=True)
    pretax_amount_post_allowance = MoneyField(
        required=True, description='Total pretax amount post allowance'
    )
    tax_amount_post_allowance = MoneyField(
        required=True, description='Total tax amount post allowance'
    )
    posttax_amount_post_allowance = MoneyField(
        required=True, description='Total posttax amount post allowance'
    )
    tax_details_post_allowance = fields.Nested(TaxDetailSchema, many=True)
    inclusion_charge_ids = fields.List(fields.Integer)
    linked_addon_charge_ids = fields.List(fields.Integer)
    split_allowed = fields.Boolean()
    is_inclusion_charge = fields.Boolean()

    @validates_schema
    def validate_data(self, data):
        if 'applicable_date' in data and dateutils.is_naive(data['applicable_date']):
            raise ValidationError(
                "Timezone information missing", field_names=['applicable_date']
            )


@swag_schema
class ShallowChargeSchema(Schema):
    charge_id = fields.Integer(required=True)
    applicable_date = fields.LocalDateTime(
        required=True,
        description="Date of consumption of the charge. "
        "A datetime string in ISO-8601 format. Timezone information is mandatory.",
    )
    item = fields.Nested(ChargeItemSchema, description='Details about the item')
    pretax_amount = MoneyField(required=True, description='The pretax amount')
    tax_amount = MoneyField(required=True, description='The tax amount')
    posttax_amount = MoneyField(
        required=True, description='The charge amount inclusive of tax'
    )
    type = fields.String(
        validate=OneOf(ChargeTypes.all()),
        required=True,
        description='credit - Guest can check-out without paying. The expenses will be billed to the '
        'company non-credit - Guest has to clear the dues at the time of check-out',
    )
    bill_to_type = fields.String(
        validate=OneOf(ChargeBillToTypes.all()),
        required=True,
        description='Whom should the expenses be billed to',
    )
    status = fields.String(
        validate=OneOf(ChargeStatus.all()),
        required=True,
        description='Status of the charge',
    )
    comment = fields.String(description='Any User/Client comment regarding the Charge')
    charge_splits = fields.Nested(
        ShallowChargeSplitSchema,
        many=True,
        description='Details of the split, in case of multiple invoices',
    )
    charge_components = fields.Nested(
        ChargeComponentSchema,
        many=True,
        description="In case a charge is built using multiple booking components - "
        "room_rent, other charges, this value will contain the breakup of "
        "posttax_amount of charge between those components",
    )


@register_schema()
@swag_schema
class CreditSummarySchema(ThsBaseSchema):
    total_credit = MoneyField(dump_only=True)
    total_confirmed_payment = MoneyField(dump_only=True)
    total_unconfirmed_payment = MoneyField(dump_only=True)
    total_pah_payment = MoneyField(dump_only=True, allow_none=True)
    total_credit_offered = MoneyField(
        dump_only=True,
        description="Total payment automatically recorded against pay "
        "after checkout charges",
    )
    total_refund = MoneyField(dump_only=True, description='refund amount')


@register_schema()
@swag_schema
class DebitSummarySchema(ThsBaseSchema):
    total_charge = MoneyField(dump_only=True)
    total_credit_charge = MoneyField(dump_only=True)
    total_non_credit_charge = MoneyField(dump_only=True)
    total_allowance = MoneyField(dump_only=True)
    total_credit_allowance = MoneyField(dump_only=True)
    total_non_credit_allowance = MoneyField(dump_only=True)
    total_debit = MoneyField(dump_only=True)
    total_debit_pretax = MoneyField(
        dump_only=True,
        description='Total pretax amount, exclusive of allowance (total_charge - total_allowance)',
    )
    total_debit_payable_after_checkout = MoneyField(dump_only=True)
    total_debit_payable_at_checkout = MoneyField(dump_only=True)
    total_spot_credit = MoneyField(dump_only=True)


@register_schema()
@swag_schema
class NewBillSummarySchema(ThsBaseSchema):
    version = fields.Integer()
    credit_summary = fields.Nested(CreditSummarySchema)
    debit_summary = fields.Nested(DebitSummarySchema)
    balance = MoneyField(
        dump_only=True,
        description="Total payment + Total Credit Offered - Total Refund - Total "
        "Charge",
    )
    balance_to_clear_after_checkout = MoneyField(
        dump_only=True,
        description="Total Credit Offered - (Total Credit Charge - total allowance against credit "
        "charge)",
    )
    balance_to_clear_before_checkout = MoneyField(
        dump_only=True,
        description="Total payment - Total Refund - (Total non-credit Charge - total allowance "
        "against non-credit charge)",
    )


@register_schema()
@swag_schema
class BillSummarySchema(ThsBaseSchema):
    app_id = fields.String(required=True)
    bill_id = fields.String(dump_only=True)
    bill_date = fields.LocalDateTime(
        required=True,
        description="A datetime string in ISO-8601 format. Timezone information is mandatory.",
    )
    parent_reference_number = fields.String(required=True)
    vendor_id = fields.String()
    total_non_credit_invoiced_amount = MoneyField(
        dump_only=True,
        description='Total invoiced amount till now' 'of type non-credit',
    )
    total_non_credit_reversal_amount = MoneyField(
        dump_only=True,
        description='Total reversal amount added till now' 'of type non-credit',
    )
    net_paid_amount = MoneyField(
        dump_only=True, description='Net paid amount(credit/advance - refund)'
    )
    paid_amount = MoneyField(
        dump_only=True, description='positive paid amount (credit/advance)'
    )
    refund_amount = MoneyField(dump_only=True, description='refund amount')
    total_posttax_amount = MoneyField(
        dump_only=True,
        description='Total posttax amount of the bill (total_charge - '
        'total_allowance)',
        attribute='total_debit',
    )
    total_pretax_amount = MoneyField(
        dump_only=True,
        description='Total pretax amount, exclusive of allowance ('
        'total_charge - total_allowance)',
        attribute='total_debit_pretax',
    )
    total_tax_amount = MoneyField(
        dump_only=True, description='total tax amount', attribute='total_debit_tax'
    )
    total_credit_posttax_amount = MoneyField(
        dump_only=True,
        description='total posttax amount'
        ' which are credit type. Should ideally be '
        'same as total_credit_offered',
        attribute='total_pay_after_checkout_debit',
    )
    net_payable = MoneyField(
        dump_only=True,
        description='total payable amount. '
        'This amount does not contain credit charges.',
    )
    version = fields.Integer(required=True)
    total_posttax_amount_post_allowance = MoneyField(
        dump_only=True,
        description='Total posttax amount post ' 'allowances (Deprecated field)',
        attribute='total_debit',
    )
    total_credit_posttax_amount_post_allowance = MoneyField(
        dump_only=True,
        description='Total credit posttax amount'
        'post allowance (Deprecated '
        'field)',
        attribute='total_pay_after_checkout_debit',
    )
    summary = fields.Nested(NewBillSummarySchema)


@register_schema()
@swag_schema
class BillSchema(BillSummarySchema):
    """
    Bill schema
    """

    # NOTE: The Nested Schemas will automatically derive `jit` value from this schema. So they'll also be Jitted.
    # Also, the schema object are only created once, and hence forward, for every serialization on this schema object,
    # nested jitted schema object is used.
    # Additionally we can also pass the Schema object to Nested, as parameter. But when Nested is with many=True, we
    # have to pass instance created with many=True
    vendor_details = fields.Nested(VendorDetailsSchema, required=True)
    grace_period = fields.Integer(required=True)
    gstin = fields.String(required=False)
    payments = fields.Nested(PaymentSchema, many=True, description='List of payments')
    charges = fields.Nested(ChargeSchema, many=True, description='List of charges')
    max_charge_id = fields.Integer()
    status = fields.String()
    parent_info = fields.Dict(required=True)

    @validates_schema
    def validate_data(self, data):
        if 'bill_date' in data and dateutils.is_naive(data['bill_date']):
            raise ValidationError(
                "Timezone information missing", field_names=['bill_date']
            )


@register_schema()
@swag_schema
class ShallowBillResponseSchema(BillSchema):
    charges = fields.Nested(
        ShallowChargeSchema, many=True, description='List of charges'
    )
    vendor_id = fields.String()

    class Meta:
        exclude = ('vendor_details',)


@swag_schema
class TemplateChargeItemTaxValue(Schema):
    percentage = fields.Decimal(required=True)
    amount = MoneyField(required=True)


@swag_schema
class TemplateChargeItemTaxBreakup(Schema):
    cgst = fields.Nested(TemplateChargeItemTaxValue, required=True)
    sgst = fields.Nested(TemplateChargeItemTaxValue, required=True)
    igst = fields.Nested(TemplateChargeItemTaxValue, required=True)
    kerala_flood_cess = fields.Nested(TemplateChargeItemTaxValue, required=False)


@swag_schema
class CreditNoteLineItemSchema(Schema):
    invoice_id = fields.String(required=True)
    invoice_charge_id = fields.Integer(required=True)
    charge_id = fields.Integer(required=True)
    credit_note_line_item_id = fields.Integer()
    applicable_date = fields.LocalDateTime(
        required=True,
        description="A datetime string in ISO-8601 format. Timezone information is mandatory.",
    )
    pretax_amount = MoneyField(required=True, description='The pretax amount')
    tax_amount = MoneyField(required=True, description='The tax amount')
    posttax_amount = MoneyField(
        required=True, description='The charge amount inclusive of tax'
    )
    tax_details = fields.Nested(TaxDetailSchema, many=True)
    item_code = fields.Nested(ItemCodeSchema, required=False)
    item_name = fields.String(required=False)
    sku_category_id = fields.String(required=False)


@swag_schema
class CreditNoteShallowSchema(Schema):
    bill_id = fields.String()
    created_at = fields.LocalDateTime()
    modified_at = fields.LocalDateTime()
    credit_note_id = fields.String(required=True)
    credit_note_number = fields.String(
        required=True,
        description='credit note number in series compliant with the GST Council rules',
    )
    credit_note_url = fields.Url(required=False)
    credit_note_date = fields.Date(
        required=True, description='Date of credit note generation'
    )
    pretax_amount = MoneyField(required=True, description='The pretax amount')
    tax_amount = MoneyField(required=True, description='The tax amount')
    posttax_amount = MoneyField(
        required=True, description='The charge amount inclusive of tax'
    )
    status = fields.String(
        validate=OneOf(CreditNoteStatus.all()),
        required=True,
        description='Status of the credit note',
    )
    version = fields.Integer()
    billed_entity_account = fields.Nested(BilledEntityAccountSchema, required=True)


@swag_schema
class CreditNoteSchema(Schema):
    bill_id = fields.String()
    created_at = fields.LocalDateTime()
    modified_at = fields.LocalDateTime()
    credit_note_id = fields.String(required=True)
    invoice_ids = fields.List(fields.String)
    credit_note_number = fields.String(
        required=True,
        description='credit note number in series compliant with the GST Council rules',
    )
    credit_note_url = fields.Url(required=False)
    credit_note_date = fields.Date(
        required=True, description='Date of credit note generation'
    )
    vendor_details = fields.Nested(VendorDetailsSchema, required=True)
    issued_to = fields.Nested(InvoiceBillToInfoSchema, required=True)
    pretax_amount = MoneyField(required=True, description='The pretax amount')
    tax_amount = MoneyField(required=True, description='The tax amount')
    posttax_amount = MoneyField(
        required=True, description='The charge amount inclusive of tax'
    )
    tax_details = fields.Nested(CumulativeTaxBreakupSchema, many=True)
    status = fields.String(
        validate=OneOf(CreditNoteStatus.all()),
        required=True,
        description='Status of the credit note',
    )
    comment = fields.String(
        description='Any User/Client comment regarding the credit note'
    )
    issued_to_type = fields.String(validate=OneOf(IssuedToType.all()))
    issued_by_type = fields.String(validate=OneOf(IssuedByType.all()))
    hotel_credit_note_id = fields.String(
        required=False,
        description="credit note id for credit note issued by hotel, "
        "to treebo, "
        "in case this invoice is generated for hotel on "
        "reseller",
    )
    hotel_credit_note_number = fields.String(
        required=False,
        description='credit note number of corresponding buy side invoice',
        dump_only=True,
        allow_none=True,
    )
    issued_by = fields.Nested(InvoiceIssuedByInfoSchema, required=True)
    credit_note_line_items = fields.Nested(CreditNoteLineItemSchema, many=True)
    version = fields.Integer()
    irn = fields.String(dump_only=True)
    qr_code = fields.String(dump_only=True)
    is_einvoice = fields.Boolean(dump_only=True)
    signed_url = fields.Url(required=False)
    billed_entity_account = fields.Nested(BilledEntityAccountSchema, required=True)

    @pre_dump
    def load_data(self, credit_note_aggregate):
        data = credit_note_aggregate.credit_note
        data.hotel_credit_note_number = credit_note_aggregate.hotel_credit_note_number
        data.credit_note_line_items = credit_note_aggregate.credit_note_line_items
        return data


@swag_schema
class CreditNoteLineItemSchemaForTemplate(CreditNoteLineItemSchema):
    tax_details = fields.Nested(
        TemplateChargeItemTaxBreakup, attribute='tax_details_for_template'
    )
    tax_breakup = fields.Nested(TaxDetailSchema, many=True, attribute='tax_details')
    item_code = fields.Nested(ItemCodeSchema)
    item_name = fields.String()
    charge_item_detail = fields.Dict()
    sku_category_id = fields.String(required=False)


@swag_schema
class CreditNoteSchemaForTemplate(CreditNoteSchema):
    tax_details = fields.Nested(
        TemplateChargeItemTaxBreakup, attribute='tax_details_for_template'
    )
    tax_breakup = fields.Nested(
        CumulativeTaxBreakupSchema, many=True, attribute='tax_details'
    )
    credit_note_line_items = fields.Nested(
        CreditNoteLineItemSchemaForTemplate, many=True
    )


@swag_schema
class BookingInvoiceInfoSchema(Schema):
    invoice_number = fields.String(
        required=True,
        description='Invoice number in series compliant with the GST Council rules',
    )
    invoice_date = fields.Date(required=True, description='Date of invoice generation')


@swag_schema
class VendorMetaDetailsSchema(Schema):
    vendor_logo = fields.String()
    vendor_msme_number = fields.String()


@swag_schema
class BookingMetaDetailsSchema(Schema):
    channel_code = fields.String()
    travel_agent_name = fields.String(allow_none=True)
    company_name = fields.String(allow_none=True)


@swag_schema
class CreditNoteTemplateSchema(Schema):
    credit_note = fields.Nested(CreditNoteSchemaForTemplate, required=True)
    booking = fields.Nested(BookingBillParentInfoSchema, required=True)
    invoice_details = fields.Nested(BookingInvoiceInfoSchema, many=True)
    vendor_context = fields.Dict()
    vendor_meta = fields.Nested(VendorMetaDetailsSchema)
    booking_meta = fields.Nested(BookingMetaDetailsSchema)


@swag_schema
class BillingCreditNoteTemplateResponse(Schema):
    template = fields.Dict()  # resembles CreditNoteTemplateSchema
    credit_note_url = fields.String()
    credit_note_signed_url = fields.String()


@swag_schema
class BookingInvoicePaymentInfoSchema(Schema):
    date_of_payment = fields.Date()
    id = fields.String(required=True)
    payment_type = fields.String()
    payment_mode = fields.String()
    amount = MoneyField()
    comment = fields.String()


@swag_schema
class PosBillTemplateChargeItemSchema(Schema):
    applicable_date = fields.Date(required=True)
    item_name = fields.String(required=True)
    hsn_code = fields.String(required=True)
    item_details = fields.Dict(required=False)
    pretax_amount = MoneyField(required=True)
    posttax_amount = MoneyField(required=True)
    tax = fields.Nested(TemplateChargeItemTaxBreakup, required=True)
    tax_breakup = fields.Nested(TaxDetailSchema, many=True)


@swag_schema
class ShallowPosBillTemplate(BillSchema):
    """
    Shallow Pos Bill schema
    """

    class Meta:
        exclude = (
            'charges',
            'payments',
        )


@register_schema(many=True)
@swag_schema
class PosBillTemplateSchema(ThsBaseSchema):
    bill = fields.Nested(ShallowPosBillTemplate, required=True)
    parent_info = fields.Dict(required=True)
    charge_items = fields.Nested(
        PosBillTemplateChargeItemSchema, many=True, required=True
    )
    payments = fields.Nested(BookingInvoicePaymentInfoSchema, many=True)


@swag_schema
class BilledEntityAccountResponseSchema(Schema):
    account_number = fields.Integer(required=True)
    folio_number = fields.Integer(required=True)
    locked = fields.Boolean(required=True)
    invoiced = fields.Boolean(required=True)
    account_type = fields.String(
        validate=OneOf(ChargeTypes.all()), allow_none=True, required=True
    )
    is_allowance_account = fields.Boolean(required=True)
    total_non_credit_charge = MoneyField()
    total_non_credit_charge_allowance = MoneyField()
    total_credit_charge = MoneyField()
    total_credit_charge_allowance = MoneyField()
    total_payment = MoneyField()
    total_refund = MoneyField()
    net_balance = MoneyField()


@swag_schema
class BilledEntitySchema(Schema):
    billed_entity_id = fields.Integer(required=True)
    name = fields.Nested(NameSchema, required=True)
    accounts = fields.Nested(
        BilledEntityAccountResponseSchema, many=True, required=True
    )
    category = fields.String(required=True)
    status = fields.String(validate=OneOf(BilledEntityStatus.all()))


@swag_schema
class TransferChargeResponseSchema(Schema):
    destination_bill_id = fields.String()
    charge = fields.Nested(ChargeSchema, required=True)


@swag_schema
class TransferChargesResponseSchema(Schema):
    destination_bill_id = fields.String()
    charges = fields.List(fields.Nested(ChargeSchema, required=True))


@swag_schema
class BilledEntityAccountSummarySchema(Schema):
    billed_entity_account = fields.Nested(BilledEntityAccountSchema, required=True)
    folio_number = fields.Integer()
    account_summary = fields.Nested(NewBillSummarySchema, required=True)


@swag_schema
class BilledEntityBalanceSchema(Schema):
    billed_entity_id = fields.Integer()
    entity_account_summary = fields.Nested(BilledEntityAccountSummarySchema, many=True)


@swag_schema
class FlatChargeSchema(Schema):
    charge_id = fields.Integer()
    charge_split_id = fields.Integer()
    status = fields.String()
    billed_entity_account = fields.Nested(BilledEntityAccountSchema)
    posttax_amount = MoneyField()
    pretax_amount = MoneyField()
    tax_amount = MoneyField()
    tax_details = fields.Nested(TaxDetailSchema)
    charge_type = fields.String()
    allowances = fields.Nested(AllowanceResponseSchema, many=True)
    item_name = fields.String()
    sku_category_id = fields.String()
    charge_to = fields.List(fields.String)
    inclusion_charge_ids = fields.List(fields.Integer)
    room_no = fields.String()
    room_type = fields.String()
    room_stay_id = fields.Integer()
    applicable_date = fields.LocalDateTime()


@swag_schema
class FlatPaymentSchema(Schema):
    payment_id = fields.Integer()
    payment_split_id = fields.Integer()
    amount = MoneyField()
    payment_type = fields.String()
    payment_mode = fields.String()
    billed_entity_account = fields.Nested(BilledEntityAccountSchema)
    date_of_payment = fields.LocalDateTime()
    status = fields.String()
    paid_to = fields.String()
    payment_channel = fields.String()
    payment_ref_id = fields.String()
    paid_by = fields.String()
    payment_mode_sub_type = fields.String()
    payment_business_date = fields.LocalDateTime()
    payor_billed_entity_id = fields.Integer()


@swag_schema
class FolioSummarySchema(Schema):
    bill_summary = fields.Nested(NewBillSummarySchema, required=True)
    charges = fields.Nested(FlatChargeSchema, many=True)
    payments = fields.Nested(FlatPaymentSchema, many=True)
    is_invoiced = fields.Boolean()


@swag_schema
class FolioResponseSchema(Schema):
    billed_entity_id = fields.Integer(required=True)
    folio_number = fields.Integer(required=True)
    account_number = fields.Integer(required=True)


@swag_schema
class PaymentDetailsSchema(Schema):
    """
    Payment Details response schema
    """

    bill_id = fields.String(required=True)
    amount = fields.String(required=True)
    payment_mode = fields.String(required=True)
    payment_ref_id = fields.String(required=False, allow_none=True)
    payment_type = fields.String(required=True)
    payment_channel = fields.String(required=True)


@swag_schema
class RefundSplitSummarySchema(Schema):
    amount = MoneyField(required=True)
    refund_mode = fields.String(required=True)
    payment_id = fields.Integer(required=False, allow_none=True)


@swag_schema
class RefundSummaryResponseSchema(Schema):
    item_id = fields.Integer()
    refund_mode = fields.String(allow_none=True)
    refund_splits = fields.Nested(RefundSplitSummarySchema, required=False, many=True)


@swag_schema
class PaymentFailureSchema(Schema):
    """
    Payment failure response schema
    """

    amount = MoneyField()
    refund_mode = fields.String()
    error_message = fields.String()

    @post_dump
    def rename_error_message_to_message(self, data):
        if 'error_message' in data:
            data['message'] = data.pop('error_message')
        return data


@swag_schema
class RefundDetailsResponseSchema(Schema):
    """
    Payment failure response schema
    """

    total_pah_amount = MoneyField()
    total_ptt_amount = MoneyField()


@swag_schema
class FolioEInvoiceEligibilityResponseSchema(Schema):
    """
    Payment failure response schema
    """

    folio_number = fields.Integer()
    e_invoicing_eligibility = fields.Boolean()
