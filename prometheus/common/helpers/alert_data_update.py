from object_registry import locate_instance
from prometheus.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)


def alert_company_details_update(booking_aggregate, old_booking_owner):
    company_details = booking_aggregate.get_company_details()
    travel_agent_details = booking_aggregate.get_travel_agent_details()
    booking_owner = booking_aggregate.get_booking_owner()
    entity = None
    if (
        company_details
        and company_details.legal_details
        and booking_owner.company_billed_entity_id == company_details.billed_entity_id
    ):
        entity = 'Company Details'
    elif (
        travel_agent_details
        and travel_agent_details.legal_details
        and booking_owner.company_billed_entity_id
        == travel_agent_details.billed_entity_id
    ):
        entity = 'Travel Agent'
    old_legal_details, new_legal_details = (
        old_booking_owner.gst_details,
        booking_owner.gst_details,
    )
    if entity and old_legal_details != new_legal_details:
        source, target = (
            (old_legal_details, new_legal_details)
            if old_legal_details
            else (new_legal_details, old_legal_details)
        )
        if source.has_same_tax_determiners(target):
            return
        alert_client = locate_instance(SlackAlertServiceClient)
        payload = {
            "entity": entity,
            "old_value": old_legal_details.to_json() if old_legal_details else {},
            "new_value": new_legal_details.to_json() if new_legal_details else {},
            "booking_reference_id": booking_aggregate.booking.reference_number,
            "hotel_id": booking_aggregate.hotel_id,
        }
        alert_client.record_event('company_details_update', payload)
