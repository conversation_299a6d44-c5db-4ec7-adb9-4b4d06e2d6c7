import logging

from treebo_commons.utils import dateutils

from ths_common.constants.booking_constants import AgeGroup
from ths_common.value_objects import Occupancy, RoomChargeItemDetails

logger = logging.getLogger(__name__)


def update_occupancy_details_in_booked_charges(
    booking_aggregate,
    bill_aggregate,
    room_stay=None,
    edited_charges=None,
    room_stay_ids=None,
):
    if not room_stay and edited_charges:
        edited_charges = None

    if room_stay:
        room_stays = [room_stay]
    elif room_stay_ids:
        room_stays = booking_aggregate.get_room_stays(room_stay_ids)
    else:
        room_stays = booking_aggregate.get_active_room_stays()

    for room_stay in room_stays:
        try:
            if edited_charges:
                charges = edited_charges
            else:
                all_applicable_charges_ids = (
                    booking_aggregate.get_all_applicable_charges_on_room_stay(
                        room_stay.room_stay_id
                    )
                )
                charges = bill_aggregate.get_active_charges(all_applicable_charges_ids)

            date_wise_occupancies = room_stay.date_wise_occupancies
            for charge in charges:
                charge_date = dateutils.to_date(charge.applicable_date)
                if charge.is_consumed:
                    continue
                checked_in_guests = room_stay.get_all_current_guest_ids(
                    charge_date, age_group=AgeGroup.ADULT
                )

                bill_aggregate.update_consuming_guests(charge, checked_in_guests)

                occupancy = date_wise_occupancies.get(charge_date)
                if not occupancy:
                    # If charge is on checkout date, then occupancy on last date won't be found.
                    # Use date - 1, for occupancy
                    occupancy = date_wise_occupancies.get(
                        dateutils.subtract(charge_date, days=1), Occupancy(0)
                    )

                charge_item_details = RoomChargeItemDetails.from_dict(
                    charge.item.details
                )
                charge_item_details.update_occupancy(occupancy.adult)
                bill_aggregate.update_charge_item_details(charge, charge_item_details)
        except:
            logger.exception(
                "[Handled Exception] Couldn't update occupancy details in room_stay: %s, in booking: "
                "%s",
                room_stay.room_stay_id,
                booking_aggregate.booking_id,
            )
