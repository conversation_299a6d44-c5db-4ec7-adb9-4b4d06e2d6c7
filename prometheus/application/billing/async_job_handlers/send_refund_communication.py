import datetime
import datetime as dt
import logging

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.core.globals import crs_context
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.infrastructure.external_clients.authn_service_client import (
    AuthNServiceClient,
)
from prometheus.infrastructure.external_clients.payment_service_client import (
    PaymentServiceClient,
)
from prometheus.infrastructure.external_clients.pygmy_client import PygmyClient
from shared_kernel.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from shared_kernel.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from ths_common.constants.billing_constants import (
    CommunicationTypeIdentifer,
    PayoutDetailsStatus,
)
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.value_objects import PayoutDetails

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CommunicationServiceClient,
        BookingRepository,
        HotelRepository,
        BillRepository,
        CatalogServiceClient,
        PaymentServiceClient,
        JobSchedulerService,
        AuthNServiceClient,
        TenantSettings,
        PygmyClient,
    ]
)
class ScheduleRefundCommunicationJobHandler:
    REMINDER_TIME = 9

    def __init__(
        self,
        communication_service_client: CommunicationServiceClient,
        booking_repository: BookingRepository,
        hotel_repository: HotelRepository,
        bill_repository: BillRepository,
        catalog_service_client: CatalogServiceClient,
        payment_service_client: PaymentServiceClient,
        job_scheduler_service: JobSchedulerService,
        authn_service_client: AuthNServiceClient,
        tenant_settings: TenantSettings,
        pygmy_client: PygmyClient,
    ):
        self.communication_service_client = communication_service_client
        self.booking_repository = booking_repository
        self.hotel_repository = hotel_repository
        self.bill_repository = bill_repository
        self.catalog_service_client = catalog_service_client
        self.payment_service_client = payment_service_client
        self.job_scheduler_service = job_scheduler_service
        self.authn_service_client = authn_service_client
        self.tenant_settings = tenant_settings
        self.pygmy_client = pygmy_client

    def send_payout_link_communication(
        self,
        booking_id,
        refund_amount,
        expire_by=None,
        link=None,
        is_reminder=False,
        pg_payout_id=None,
        phone_number=None,
        email=None,
    ):
        booking_aggregate = self.booking_repository.load(booking_id)
        payout_link_response = self.payment_service_client.fetch_payout_link(
            pg_payout_id
        )
        payout_link = PayoutDetails.from_json(payout_link_response)
        expiry_datetime = dt.datetime.utcfromtimestamp(expire_by)
        current_time = datetime.datetime.now()
        if (
            payout_link.status != PayoutDetailsStatus.ISSUED
            or current_time > expiry_datetime
        ):
            return JobResultDto.success()
        self._send_refund_communication(
            booking_aggregate,
            refund_amount,
            is_reminder,
            expire_by,
            link,
            phone_number,
            email,
        )
        is_reminder = True
        self._schedule_reminder_communication(
            booking_aggregate,
            current_time,
            expire_by,
            expiry_datetime,
            is_reminder,
            link,
            pg_payout_id,
            refund_amount,
            phone_number,
            email,
        )

    def _schedule_reminder_communication(
        self,
        booking_aggregate,
        current_time,
        expire_by,
        expiry_datetime,
        is_reminder,
        link,
        pg_payout_id,
        refund_amount,
        phone_number,
        email,
    ):
        next_reminder_datetime = (expiry_datetime - dt.timedelta(days=3)).replace(
            hour=self.REMINDER_TIME, minute=0, second=0
        )
        if next_reminder_datetime.date() <= current_time.date():
            next_reminder_datetime = (current_time + dt.timedelta(days=1)).replace(
                hour=self.REMINDER_TIME, minute=0, second=0
            )
        if next_reminder_datetime < expiry_datetime:
            self.job_scheduler_service.schedule_payout_link_communication(
                booking_aggregate,
                refund_amount,
                expire_by,
                link,
                pg_payout_id,
                is_reminder=is_reminder,
                eta=next_reminder_datetime,
                phone_number=phone_number,
                email=email,
            )

    def send_direct_refund_communication(
        self,
        booking_id,
        refund_amount,
    ):
        booking_aggregate = self.booking_repository.load(booking_id)
        self._send_refund_communication(
            booking_aggregate, refund_amount, is_direct_refund_communication=True
        )

    def _send_refund_communication(
        self,
        booking_aggregate,
        refund_amount,
        is_reminder=False,
        expire_by=None,
        link=None,
        phone_number=None,
        email=None,
        is_direct_refund_communication=False,
        is_payout_link_communication_pending=False,
    ):
        """
        Send payout link communication to guests via email or SMS/WhatsApp.
        Args:
            booking_aggregate.
            refund_amount: Details of the refund.
            is_reminder: reminder communication or not.
            expire_by: payout expiry timestamp
            link: payout link
        Returns:
            None
        """
        bill_aggregate = self.bill_repository.load(booking_aggregate.booking.bill_id)
        hotel_aggregate = self.hotel_repository.load(booking_aggregate.hotel_id)
        crs_context.set_hotel_context(hotel_aggregate)
        hotel_name = self.catalog_service_client.get_property_name(
            hotel_id=booking_aggregate.booking.hotel_id
        )

        (
            booking_owner_email,
            booking_owner_phone_number,
            guest_name,
        ) = booking_aggregate.get_contact_details()
        email = email or booking_owner_email
        phone_number = phone_number or booking_owner_phone_number
        if link:
            link = self.pygmy_client.shorten_url(link)

        if email:
            email_data, template_identifier = self._get_email_payload_and_template(
                bill_aggregate,
                booking_aggregate,
                guest_name,
                hotel_name,
                refund_amount,
                is_reminder,
                link,
                expire_by,
                is_payout_link_communication_pending,
            )

            reference_number = booking_aggregate.booking.reference_number
            if is_reminder:
                email_subject = f"[Important] Your refund of ₹{refund_amount} is expiring soon. Claim now!"
            elif is_direct_refund_communication:
                email_subject = (
                    "Refund has been initiated for your booking with Treebo Hotels"
                )
            elif is_payout_link_communication_pending:
                email_subject = "[Important] Your refund for booking with Treebo Hotels is under process"
            else:
                email_subject = f"[Important] Claim your refund for Treebo Hotels booking ID {reference_number}"

            self.communication_service_client.send_email(
                identifier=template_identifier,
                context_data=email_data,
                to_emails=[email],
                subject=email_subject,
            )

        if (
            phone_number
            and crs_context.get_hotel_context().is_sms_or_whatsapp_enabled()
        ):
            (
                whatsapp_data,
                template_identifier,
            ) = self._get_whatsapp_payload_and_template(
                booking_aggregate,
                refund_amount,
                guest_name,
                is_reminder,
                link,
                expire_by,
                is_payout_link_communication_pending,
                hotel_name,
            )
            try:
                self.communication_service_client.send_sms_or_whatsapp(
                    identifier=template_identifier,
                    context_data=whatsapp_data,
                    receivers=[phone_number],
                )
            except Exception as ex:
                if 'Invalid phone number in identifier' in str(
                    ex
                ) or 'too short to be a phone number' in str(ex):
                    return
                else:
                    raise

    @staticmethod
    def _get_whatsapp_payload_and_template(
        booking_aggregate,
        refund_amount,
        guest_name,
        is_reminder,
        link,
        expire_by,
        is_payout_link_communication_pending,
        hotel_name,
    ):
        template_identifier = CommunicationTypeIdentifer.DIRECT_REFUND_COMM_TEMPLATE
        whatsapp_data = dict(
            booking_id=booking_aggregate.booking.reference_number,
            amount=refund_amount,
            guest_name=guest_name.first_name,
            hotel_name=hotel_name,
        )
        if link:
            template_identifier = (
                (CommunicationTypeIdentifer.REFUND_PAYOUT_LINK_REMINDER_COMM_TEMPLATE)
                if is_reminder
                else CommunicationTypeIdentifer.REFUND_PAYOUT_LINK_COMM_TEMPLATE
            )
            whatsapp_data.update(
                dict(
                    link=link,
                    expiry_time=str(
                        dt.datetime.utcfromtimestamp(expire_by).strftime(
                            '%d %b %I:%M %p'
                        )
                    ),
                )
            )
        if is_payout_link_communication_pending:
            template_identifier = (
                CommunicationTypeIdentifer.NON_APPROVED_PAYOUT_LINK_COMM_TEMPLATE
            )
        return whatsapp_data, template_identifier

    @staticmethod
    def _get_email_payload_and_template(
        bill_aggregate,
        booking_aggregate,
        guest_name,
        hotel_name,
        refund_amount,
        is_reminder,
        link,
        expire_by,
        is_payout_link_communication_pending=False,
    ):
        template_identifier = CommunicationTypeIdentifer.DIRECT_REFUND_MAIL_TEMPLATE
        email_data = dict(
            refund_amount=refund_amount,
            booking=dict(
                booking_id=booking_aggregate.booking.reference_number,
                name=guest_name.first_name,
                status=booking_aggregate.booking.status.value,
                checkin_date=str(
                    dateutils.to_date(booking_aggregate.booking.checkin_date)
                ),
                checkout_date=str(
                    dateutils.to_date(booking_aggregate.booking.checkout_date)
                ),
                booking_amount=str(bill_aggregate.total_posttax_amount().amount),
                hotel_name=hotel_name,
            ),
            is_reminder=is_reminder,
        )
        if link:
            if is_payout_link_communication_pending:
                template_identifier = (
                    CommunicationTypeIdentifer.NON_APPROVED_PAYOUT_LINK_MAIL_TEMPLATE
                )
            else:
                template_identifier = (
                    CommunicationTypeIdentifer.REFUND_PAYOUT_LINK_MAIL_TEMPLATE
                )
                email_data.update(
                    dict(
                        payout=dict(
                            link=link,
                            expire_by=str(dt.datetime.utcfromtimestamp(expire_by)),
                        )
                    )
                )
        return email_data, template_identifier

    def send_payment_failure_communication(
        self,
        booking_id,
        amount,
        payment_mode,
        error_message,
        user_auth_id=None,
    ):
        booking_aggregate = self.booking_repository.load(booking_id)
        email_ids = self._get_user_email(user_auth_id)
        email_ids_for_failure_communication = self._get_failure_communication_email_ids(
            booking_aggregate
        )
        if email_ids_for_failure_communication:
            email_ids.extend(email_ids_for_failure_communication)
        if email_ids:
            self._send_payment_failure_communication(
                booking_aggregate.booking.reference_number,
                amount,
                payment_mode,
                error_message,
                email_ids,
                user_auth_id,
            )

    def _get_user_email(self, user_auth_id):
        email_ids = []
        if user_auth_id:
            user_details = self.authn_service_client.get_user_details(user_auth_id)
            if user_details:
                email_ids.append(user_details.get('email'))
        return email_ids

    def _get_failure_communication_email_ids(self, booking_aggregate):
        hotel_id = booking_aggregate.hotel_id
        return self.tenant_settings.get_setting_value(
            TenantSettingName.EMAIL_IDS_FOR_FAILURE_COMMUNICATION.value, hotel_id
        )

    def _send_payment_failure_communication(
        self,
        booking_id,
        amount,
        payment_mode,
        error_message,
        email_ids,
        user_auth_id,
    ):
        template_identifier = CommunicationTypeIdentifer.PAYMENT_FAILURE_MAIL_TEMPLATE
        email_id = email_ids[0] if user_auth_id else "External Service"
        email_data = dict(
            booking_id=booking_id,
            amount=amount,
            payment_mode=payment_mode,
            error_message=error_message,
            email_id=email_id,
        )

        email_subject = (
            f"[Important] Unable to auto-refund ({payment_mode}) | {booking_id}"
        )
        self.communication_service_client.send_email(
            identifier=template_identifier,
            context_data=email_data,
            to_emails=email_ids,
            subject=email_subject,
        )

    def send_payout_link_not_approved_communication(
        self,
        booking_id,
        refund_amount,
        expire_by=None,
        link=None,
        pg_payout_id=None,
        phone_number=None,
        email=None,
        retry_count=0,
    ):
        booking_aggregate = self.booking_repository.load(booking_id)
        payout_link_response = self.payment_service_client.fetch_payout_link(
            pg_payout_id
        )
        payout_link = PayoutDetails.from_json(payout_link_response)
        expiry_datetime = dt.datetime.utcfromtimestamp(expire_by)
        current_time = datetime.datetime.now()

        # payout link is not approved, send communication on first try
        if payout_link.status == PayoutDetailsStatus.PENDING:
            if retry_count == 0:
                self._send_refund_communication(
                    booking_aggregate,
                    refund_amount,
                    expire_by=expire_by,
                    link=link,
                    phone_number=phone_number,
                    email=email,
                    is_payout_link_communication_pending=True,
                )
            self._schedule_payout_link_approval_reminder_communication(
                booking_aggregate,
                current_time,
                expire_by,
                expiry_datetime,
                link,
                pg_payout_id,
                refund_amount,
                phone_number=phone_number,
                email=email,
                retry_count=retry_count + 1,
            )

        if (
            payout_link.status == PayoutDetailsStatus.ISSUED
            or current_time > expiry_datetime
        ):
            self._send_refund_communication(
                booking_aggregate,
                refund_amount,
                expire_by=expire_by,
                link=link,
                phone_number=phone_number,
                email=email,
            )
            is_reminder = True
            self._schedule_reminder_communication(
                booking_aggregate,
                current_time,
                expire_by,
                expiry_datetime,
                is_reminder,
                link,
                pg_payout_id,
                refund_amount,
                phone_number,
                email,
            )

    def _schedule_payout_link_approval_reminder_communication(
        self,
        booking_aggregate,
        current_time,
        expire_by,
        expiry_datetime,
        link,
        pg_payout_id,
        refund_amount,
        phone_number,
        email,
        retry_count,
    ):
        reminder_interval = dt.timedelta(hours=6)
        next_reminder_datetime = current_time + reminder_interval

        if next_reminder_datetime < expiry_datetime:
            self.job_scheduler_service.schedule_payout_link_not_approved_communication(
                booking_aggregate,
                refund_amount,
                expire_by,
                link,
                pg_payout_id,
                eta=next_reminder_datetime,
                phone_number=phone_number,
                email=email,
                retry_count=retry_count,
            )

    def send_blacklisted_user_communication(
        self, reference_number, payment_mode, hotel_id, phone=None, email=None
    ):
        logger.info(
            f"Communication for blacklisted user initialized : {reference_number}"
        )
        email_data = dict(
            reference_number=reference_number,
            payment_mode=payment_mode,
            phone=phone,
            email=email,
        )
        receiver_emails = self.tenant_settings.get_setting_value(
            TenantSettingName.EMAIL_IDS_FOR_FAILURE_COMMUNICATION.value, hotel_id
        )
        email_subject = f"[Important] Blacklisted user attempting auto-refund ({payment_mode}) | {reference_number}"
        self.communication_service_client.send_email(
            identifier=CommunicationTypeIdentifer.AUTO_REFUND_BLACKLISTED_USER,
            context_data=email_data,
            to_emails=receiver_emails,
            subject=email_subject,
        )

    def send_partial_refund_communication(
        self,
        total_ptt_amount,
        total_pah_amount,
        total_amount,
        name,
        reference_number,
        hotel_id,
        phone=None,
        email=None,
    ):
        logger.info(
            f"Communication for partial refund communciation for booking_id: {reference_number}"
        )
        context_data = dict(
            reference_number=reference_number,
            total_ptt_amount=total_ptt_amount,
            total_pah_amount=total_pah_amount,
            refund_amount=total_amount,
            name=name,
            phone=phone,
            email=email,
        )
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        crs_context.set_hotel_context(hotel_aggregate)

        # Email to guest
        email_subject = (
            f"[Important] Your refund for booking with Treebo Hotels is under process"
        )
        self.communication_service_client.send_email(
            identifier=CommunicationTypeIdentifer.GUEST_PARTIAL_REFUND_MAIL_TEMPLATE,
            context_data=context_data,
            to_emails=[email],
            subject=email_subject,
        )

        # Email to reservation team
        email_ids_for_failure_communication = self.tenant_settings.get_setting_value(
            TenantSettingName.EMAIL_IDS_FOR_FAILURE_COMMUNICATION.value, hotel_id
        )

        email_subject = f"Refund the surplus payment | {reference_number} | {hotel_id}"
        self.communication_service_client.send_email(
            identifier=CommunicationTypeIdentifer.RESERVATION_TEAM_PARTIAL_REFUND_MAIL_TEMPLATE,
            context_data=context_data,
            to_emails=email_ids_for_failure_communication,
            subject=email_subject,
        )

        # Whatsapp to guest
        if phone and crs_context.get_hotel_context().is_sms_or_whatsapp_enabled():
            try:
                self.communication_service_client.send_sms_or_whatsapp(
                    identifier=CommunicationTypeIdentifer.GUEST_PARTIAL_REFUND_WHATSAPP_TEMPLATE,
                    context_data=context_data,
                    receivers=[phone],
                )
            except Exception as ex:
                if 'Invalid phone number in identifier' in str(
                    ex
                ) or 'too short to be a phone number' in str(ex):
                    return
                else:
                    raise
