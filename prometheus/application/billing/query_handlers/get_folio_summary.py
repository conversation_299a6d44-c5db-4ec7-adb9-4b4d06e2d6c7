from object_registry import register_instance
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.billing.dto.folio_summary_dto import (
    ChargeSummaryDto,
    FolioSummaryDto,
    PaymentSummaryDto,
)
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import BookingInvoiceGroupRepository
from ths_common.exceptions import ValidationException


@register_instance(dependencies=[BillRepository, BookingInvoiceGroupRepository])
class GetFolioSummaryQueryHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        booking_invoice_group_repository: BookingInvoiceGroupRepository,
    ):
        self.bill_repository = bill_repository
        self.booking_invoice_group_repository = booking_invoice_group_repository

    @session_manager()
    @set_hotel_context()
    def handle(
        self, bill_id, folio_number, booking_invoice_group_id=None, hotel_aggregate=None
    ):
        bill_aggregate = self.bill_repository.load(bill_id)
        folio = bill_aggregate.get_folio(folio_number=folio_number)
        if not folio:
            raise ValidationException(BillingErrors.FOLIO_NOT_FOUND)
        (
            booked_charges_invoiced,
            booked_allowances_invoiced,
            booked_charges_to_be_cancelled,
            booked_allowances_to_be_cancelled,
        ) = (None, None, None, None)
        if booking_invoice_group_id:
            booking_invoice_group_aggregate = (
                self.booking_invoice_group_repository.load(booking_invoice_group_id)
            )
            booked_charges_and_allowances_invoiced = (
                booking_invoice_group_aggregate.booking_invoice_group.charges_and_allowances_to_be_posted
            )
            if booked_charges_and_allowances_invoiced:
                booked_charges_invoiced = booked_charges_and_allowances_invoiced.get(
                    'booked_charges_to_post', []
                )
                booked_allowances_invoiced = booked_charges_and_allowances_invoiced.get(
                    'booked_allowances_to_post', []
                )
                booked_charges_to_be_cancelled = (
                    booked_charges_and_allowances_invoiced.get(
                        'booked_charge_to_exclude', []
                    )
                )
                booked_allowances_to_be_cancelled = (
                    booked_charges_and_allowances_invoiced.get(
                        'booked_allowances_to_exclude', []
                    )
                )

        charges_mapped_to_folio = ChargeSummaryDto.create_for_folio(
            bill_aggregate.charges,
            folio,
            booked_charges_to_be_cancelled=booked_charges_to_be_cancelled,
            booked_allowances_to_be_cancelled=booked_allowances_to_be_cancelled,
        )
        payments_mapped_to_folio = PaymentSummaryDto.create_for_folio(
            bill_aggregate.payments, folio
        )
        folio_bill_summary = bill_aggregate.get_account_summary(
            folio.get_billed_entity_account(),
            invoiced_booked_charges=booked_charges_invoiced,
            invoiced_booked_allowances=booked_allowances_invoiced,
        )
        return FolioSummaryDto(
            bill_summary=folio_bill_summary,
            charges=charges_mapped_to_folio,
            payments=payments_mapped_to_folio,
            is_invoiced=bill_aggregate.get_billed_entity(folio.billed_entity_id)
            .get_account(folio.account_number)
            .is_invoiced(),
        )
