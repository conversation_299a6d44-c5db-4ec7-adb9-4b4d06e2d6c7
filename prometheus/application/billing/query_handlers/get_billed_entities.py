from typing import List

from object_registry import register_instance
from prometheus.application.billing.dtos.billed_entity_response_dto import (
    AccountDto,
    BilledEntityResponseDto,
)
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.repositories import BillRepository
from ths_common.constants.billing_constants import ChargeTypes


@register_instance(dependencies=[BillRepository])
class GetBilledEntitiesQueryHandler:
    def __init__(self, bill_repository: BillRepository):
        self.bill_repository = bill_repository

    def handle(self, bill_id) -> List[BilledEntityResponseDto]:
        bill_aggregate: BillAggregate = self.bill_repository.load(bill_id)
        billed_entity_response_dtos = []
        for billed_entity in bill_aggregate.billed_entities:
            account_dtos = []
            for account in billed_entity.accounts:
                billed_entity_account = BilledEntityAccountVO(
                    billed_entity.billed_entity_id, account.account_number
                )
                account_dtos.append(
                    AccountDto(
                        account_number=account.account_number,
                        folio_number=bill_aggregate.get_folio(
                            billed_entity_account=BilledEntityAccountVO(
                                billed_entity.billed_entity_id, account.account_number
                            )
                        ).folio_number
                        if bill_aggregate.get_folio(
                            billed_entity_account=BilledEntityAccountVO(
                                billed_entity.billed_entity_id, account.account_number
                            )
                        )
                        else None,
                        locked=account.locked,
                        invoiced=account.invoiced,
                        account_type=account.account_type,
                        is_allowance_account=account.is_allowance_account(),
                        total_non_credit_charge=bill_aggregate.total_posttax_amount(
                            {ChargeTypes.NON_CREDIT}, billed_entity_account
                        ),
                        total_non_credit_charge_allowance=bill_aggregate.get_total_allowances(
                            {ChargeTypes.NON_CREDIT}, billed_entity_account
                        ),
                        total_credit_charge=bill_aggregate.total_posttax_amount(
                            {ChargeTypes.CREDIT}, billed_entity_account
                        ),
                        total_credit_charge_allowance=bill_aggregate.get_total_allowances(
                            {ChargeTypes.CREDIT}, billed_entity_account
                        ),
                        total_payment=bill_aggregate.get_total_payment(
                            billed_entity_account
                        ),
                        total_refund=bill_aggregate.get_total_refund(
                            billed_entity_account
                        ),
                        net_balance=bill_aggregate.get_net_balance(
                            billed_entity_account
                        ),
                    )
                )

            billed_entity_response_dtos.append(
                BilledEntityResponseDto(
                    billed_entity_id=billed_entity.billed_entity_id,
                    category=billed_entity.category,
                    name=billed_entity.name,
                    accounts=account_dtos,
                    status=billed_entity.status,
                )
            )
        return billed_entity_response_dtos
