from object_registry import register_instance
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.billing.repositories import InvoiceRepository


@register_instance(dependencies=[InvoiceRepository, SignedUrlGenerator])
class GetInvoiceByIdQueryHandler:
    def __init__(
        self,
        invoice_repository: InvoiceRepository,
        signed_url_generator: SignedUrlGenerator,
    ):
        self.invoice_repository = invoice_repository
        self.signed_url_generator = signed_url_generator

    @session_manager()
    @set_hotel_context()
    def handle(self, invoice_id, hotel_aggregate=None):
        invoice_aggregate = self.invoice_repository.load(invoice_id)
        signed_url, expiration = self.signed_url_generator.generate_signed_url(
            invoice_aggregate.invoice.invoice_url
        )
        invoice_aggregate.set_signed_url(signed_url, expiration)
        return invoice_aggregate
