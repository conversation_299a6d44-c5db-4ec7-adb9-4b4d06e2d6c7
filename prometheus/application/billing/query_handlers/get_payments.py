from object_registry import register_instance
from prometheus.application.billing.helpers.payment_receipt_service import (
    PaymentReceiptService,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.repositories.payment_receipt_repository import (
    PaymentReceiptRepository,
)


@register_instance(
    dependencies=[BillRepository, PaymentReceiptRepository, PaymentReceiptService]
)
class GetPaymentsQueryHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        payment_receipt_repository: PaymentReceiptRepository,
        payment_receipt_service: PaymentReceiptService,
    ):
        self.bill_repository = bill_repository
        self.payment_receipt_repository = payment_receipt_repository
        self.payment_receipt_service = payment_receipt_service

    @staticmethod
    def _set_payment_receipt_url_in_payments(payments, payment_receipt_map):
        for payment in payments:
            payment_receipt = payment_receipt_map.get(payment.payment_id)
            if payment_receipt:
                payment.payment_receipt_url = payment_receipt.signed_url

    @session_manager()
    @set_hotel_context()
    def handle(self, bill_id, hotel_aggregate=None):
        bill_aggregate = self.bill_repository.load(bill_id)
        payment_receipts = self.payment_receipt_service.get_payment_receipts(bill_id)
        self._set_payment_receipt_url_in_payments(
            bill_aggregate.payments, payment_receipts
        )
        return bill_aggregate.payments, bill_aggregate.bill.version
