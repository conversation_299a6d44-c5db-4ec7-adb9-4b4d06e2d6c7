from object_registry import register_instance
from prometheus.domain.billing.repositories.credit_shell_repository import (
    CreditShellRepository,
)
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        CreditShellRepository,
    ]
)
class GetCreditShellTransactionLogQueryHandler:
    def __init__(
        self,
        credit_shell_repository: CreditShellRepository,
    ):
        self.credit_shell_repository = credit_shell_repository

    def handle(self, credit_shell_id):
        credit_shell_aggregate = self.credit_shell_repository.load(credit_shell_id)
        if not credit_shell_aggregate:
            raise ValidationException(
                message="Credit Shell with credit_shell_id: {} not found.".format(
                    credit_shell_id
                )
            )
        return credit_shell_aggregate.credit_shell_audit_trails
