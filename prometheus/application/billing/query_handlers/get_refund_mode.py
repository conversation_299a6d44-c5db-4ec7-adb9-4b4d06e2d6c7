from collections import defaultdict
from decimal import Decimal
from typing import List

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.billing.helpers.auto_refund_service_helpers import (
    AutoRefundServiceHelpers,
)
from prometheus.application.decorators import session_manager
from prometheus.application.hotel_settings.dtos.refund_rule_dto import RefundRuleDto
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.dto.refund_summary_dto import RefundSummaryDto
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services.refund_service import RefundService
from prometheus.domain.policy.errors import PolicyError
from prometheus.infrastructure.external_clients.catalog_service.user_defined_enums import (
    UserDefinedEnums,
)
from ths_common.constants.billing_constants import PaymentModes
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


@register_instance(
    dependencies=[
        BillRepository,
        RefundService,
        TenantSettings,
        AutoRefundServiceHelpers,
    ]
)
class GetRefundModeQueryHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        refund_service: RefundService,
        tenant_settings: TenantSettings,
        auto_refund_service_helpers: AutoRefundServiceHelpers,
    ):
        self.bill_repository = bill_repository
        self.refund_service = refund_service
        self.tenant_settings = tenant_settings
        self.auto_refund_service_helpers = auto_refund_service_helpers

    def handle(
        self, bill_id, refund_summary_items: List[RefundSummaryDto], user_data=None
    ):
        bill_aggregate = self.bill_repository.load(bill_id)
        hotel_id = bill_aggregate.vendor_id
        dummy_hotels = self.tenant_settings.get_dummy_hotels()
        if hotel_id in dummy_hotels:
            for refund_summary in refund_summary_items:
                refund_summary.refund_mode = refund_summary.requested_refund_mode
            return refund_summary_items
        refund_rule: RefundRuleDto = self.tenant_settings.get_refund_rule(hotel_id)
        can_add_refund_mode = (
            PrivilegeCode.CAN_ADD_REFUND_MODE in crs_context.privileges_as_dict
        )
        payments_and_refunds = bill_aggregate.get_active_payments()
        credit_shell_to_billed_entity_map = (
            self.auto_refund_service_helpers.get_credit_shell_to_billed_entity_map(
                bill_id
            )
        )
        (
            refundable_amount,
            non_refundable_amount,
        ) = self.auto_refund_service_helpers.get_total_refundable_and_non_refundable_amounts(
            bill_aggregate,
            refund_rule,
            credit_shell_to_billed_entity_map=credit_shell_to_billed_entity_map,
        )

        if refundable_amount <= 0 < non_refundable_amount:
            for refund_summary in refund_summary_items:
                if can_add_refund_mode:
                    refund_summary.refund_mode = refund_summary.requested_refund_mode
                else:
                    refund_summary.refund_mode = PaymentModes.RAZORPAY_API
            return refund_summary_items

        exhausted_amount_mapping = defaultdict(Decimal)
        for refund_summary in refund_summary_items:
            requested_refund_mode = refund_summary.requested_refund_mode
            if not refund_rule:
                refund_summary.refund_mode = requested_refund_mode
                continue
            if not can_add_refund_mode:
                requested_refund_mode, refund_summary.requested_refund_mode = None, None
            refund_summary.amount = min(refundable_amount, refund_summary.amount)
            refund_mode_priority_list = refund_rule.refund_mode_priority_list
            allowed_refund_modes_for_current_user = set(
                self._get_allowed_refund_modes(hotel_id)
            )
            refund_mode_priority_list = [
                mode
                for mode in refund_mode_priority_list
                if mode in allowed_refund_modes_for_current_user
            ]
            if requested_refund_mode:
                if requested_refund_mode not in allowed_refund_modes_for_current_user:
                    # user don't have the privilege to use this refund mode
                    raise PolicyAuthException(
                        error=PolicyError.NOT_AUTHORIZED_TO_USE_THIS_REFUND_MODE,
                    )
                if requested_refund_mode not in refund_mode_priority_list:
                    # user has the privilege but requested mode is not present in the automatic refund modes
                    refund_summary.refund_mode = requested_refund_mode
                    continue
                refund_mode_priority_list = [requested_refund_mode]
            self.refund_service.apply_refund_rules(
                payments_and_refunds,
                refund_summary,
                refund_mode_priority_list,
                refund_rule,
                exhausted_amount_mapping,
            )
            refundable_amount -= refund_summary.amount
        return refund_summary_items

    def _get_allowed_refund_modes(self, hotel_id):
        role_based_enum_payment_mode_enum = (
            self.tenant_settings.catalog_service_client.get_enums(hotel_id)
        )
        payment_mode_enum = None
        for enum in role_based_enum_payment_mode_enum:
            if enum.get('enum_name') == UserDefinedEnums.REFUND_MODE:
                return enum.get('enum_values')
            if enum.get('enum_name') == UserDefinedEnums.PAYMENT_MODE:
                payment_mode_enum = enum.get('enum_values')
        return payment_mode_enum
