from object_registry import register_instance
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.application.booking.helpers import invoice_confirmation_preconditions
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.common.utils import split_on_condition
from prometheus.domain.billing.repositories import BillRepository, InvoiceRepository
from prometheus.domain.booking.repositories import BookingInvoiceGroupRepository
from prometheus.domain.catalog.repositories import SkuCategoryRepository
from ths_common.constants.billing_constants import InvoiceStatus, IssuedToType


@register_instance(
    dependencies=[
        InvoiceRepository,
        BillRepository,
        SkuCategoryRepository,
        SignedUrlGenerator,
        BookingInvoiceGroupRepository,
    ]
)
class GetInvoicesForBillV2QueryHandler:
    def __init__(
        self,
        invoice_repository: InvoiceRepository,
        bill_repository: BillRepository,
        sku_category_repository: SkuCategoryRepository,
        signed_url_generator: SignedUrlGenerator,
        booking_invoice_group_repository: BookingInvoiceGroupRepository,
    ):
        self.invoice_repository = invoice_repository
        self.bill_repository = bill_repository
        self.sku_category_repository = sku_category_repository
        self.signed_url_generator = signed_url_generator
        self.booking_invoice_group_repository = booking_invoice_group_repository

    @session_manager()
    @set_hotel_context()
    def handle(
        self,
        bill_id,
        invoice_ids=None,
        include_preview=None,
        shallow_response=False,
        hotel_aggregate=None,
    ):
        """
        get the invoices for a given booking
        Args:
            bill_id:
            invoice_ids:
            include_preview:
            shallow_response:
            hotel_aggregate:

        Returns:

        """
        exclude_statuses = {InvoiceStatus.CANCELLED.value, InvoiceStatus.PREVIEW.value}
        if include_preview:
            exclude_statuses.remove(InvoiceStatus.PREVIEW.value)

        hotel_invoice_id_to_invoice_number_mapping = dict()

        if invoice_ids:
            invoice_aggregates = self.invoice_repository.load_all(
                invoice_ids=invoice_ids, load_charges=not shallow_response
            )
        else:
            invoice_aggregates = self.invoice_repository.load_for_bill_id(
                bill_id,
                load_charges=not shallow_response,
                exclude_statuses=exclude_statuses,
                exclude_issued_to_reseller=False,
            )
            hotel_invoice_aggregates, customer_invoice_aggregates = split_on_condition(
                invoice_aggregates,
                predicate=lambda x: x.invoice.issued_to_type == IssuedToType.RESELLER,
            )
            invoice_aggregates = customer_invoice_aggregates

            hotel_invoice_id_to_invoice_number_mapping = {
                aggregate.invoice_id: aggregate.invoice_number
                for aggregate in hotel_invoice_aggregates
            }

        booking_invoice_group_aggregates = (
            self.booking_invoice_group_repository.load_all(
                [invoice.invoice_id for invoice in invoice_aggregates]
            )
        )

        if not shallow_response:
            sku_category_aggregates = self.sku_category_repository.load_all()
            grouped_sku_categories = {
                aggregate.sku_category.sku_category_id: aggregate.sku_category
                for aggregate in sku_category_aggregates
            }
            self._update_hsn_details(invoice_aggregates, grouped_sku_categories)

            bill_aggregate = self.bill_repository.load(bill_id)
            invoice_id_wise_net_payable = invoice_confirmation_preconditions.get_pending_payment_to_confirm_invoices(
                bill_aggregate,
                invoice_aggregates,
                booking_invoice_group_aggregates=booking_invoice_group_aggregates,
            )

            for invoice_aggregate in invoice_aggregates:
                account_summary = invoice_id_wise_net_payable.get(
                    invoice_aggregate.invoice.invoice_id
                )
                invoice_aggregate.net_payable = -account_summary.balance
                invoice_aggregate.summary = account_summary

                signed_url, expiration = self.signed_url_generator.generate_signed_url(
                    invoice_aggregate.invoice.invoice_url
                )
                invoice_aggregate.set_signed_url(signed_url, expiration)
                self._set_hotel_invoice_number(
                    hotel_invoice_id_to_invoice_number_mapping, invoice_aggregate
                )

        else:
            for invoice_aggregate in invoice_aggregates:
                signed_url, expiration = self.signed_url_generator.generate_signed_url(
                    invoice_aggregate.invoice.invoice_url
                )
                invoice_aggregate.set_signed_url(signed_url, expiration)
                self._set_hotel_invoice_number(
                    hotel_invoice_id_to_invoice_number_mapping, invoice_aggregate
                )

        return invoice_aggregates

    @staticmethod
    def _set_hotel_invoice_number(
        hotel_invoice_id_to_invoice_number_mapping, invoice_aggregate
    ):
        if invoice_aggregate.hotel_invoice_id:
            hotel_invoice_number = hotel_invoice_id_to_invoice_number_mapping.get(
                invoice_aggregate.hotel_invoice_id
            )
            invoice_aggregate.set_hotel_invoice_number(hotel_invoice_number)

    def _update_hsn_details(self, invoice_aggregates, grouped_sku_categories):
        for invoice_aggregate in invoice_aggregates:
            invoice_aggregate.update_hsn_details(grouped_sku_categories)
