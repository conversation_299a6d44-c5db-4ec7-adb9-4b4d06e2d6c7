from object_registry import register_instance
from prometheus.application.decorators import session_manager
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.domain.billing.repositories import BillRepository, InvoiceRepository
from prometheus.domain.billing.services.pos_invoice_template_service import (
    PosInvoiceTemplateService,
)
from ths_common.constants.billing_constants import InvoiceStatus
from ths_common.constants.integration_event_constants import IntegrationEventType


@register_instance(
    dependencies=[BillRepository, InvoiceRepository, PosInvoiceTemplateService]
)
class GeneratePosInvoiceTemplateCommandHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        invoice_repository: InvoiceRepository,
        pos_invoice_template_service: PosInvoiceTemplateService,
    ):
        self.bill_repository = bill_repository
        self.invoice_repository = invoice_repository
        self.pos_invoice_template_service = pos_invoice_template_service

    @session_manager(commit=True)
    def handle(
        self,
        invoice_ids,
        bill_id=None,
        should_upload=True,
        should_save=True,
    ) -> JobResultDto:
        bill_aggregate = self.bill_repository.load(bill_id)
        invoice_aggregates = self.invoice_repository.load_all(invoice_ids)
        if not invoice_aggregates:
            return JobResultDto.success()

        (
            updated_invoice_aggregates,
            templates,
        ) = self._generate_and_upload_invoice_templates(
            bill_aggregate,
            invoice_aggregates,
            should_upload,
        )

        if should_upload and should_save:
            self.invoice_repository.update_all(updated_invoice_aggregates)

            if any(
                invoice_aggregate.status != InvoiceStatus.PREVIEW
                for invoice_aggregate in updated_invoice_aggregates
            ):
                confirmed_invoices = [
                    invoice_aggregate
                    for invoice_aggregate in updated_invoice_aggregates
                    if invoice_aggregate.status != InvoiceStatus.PREVIEW
                ]
                IntegrationEventApplicationService.create_invoice_event(
                    event_type=IntegrationEventType.INVOICE_UPDATED,
                    invoice_aggregates=confirmed_invoices,
                    user_action="update_invoice_template",
                )

        return JobResultDto.success(
            invoice_aggregates=updated_invoice_aggregates, templates=templates
        )

    def _generate_and_upload_invoice_templates(
        self,
        bill_aggregate,
        invoice_aggregates,
        should_upload,
    ):
        invoices_to_update_url = {
            aggregate.invoice_id: aggregate for aggregate in invoice_aggregates
        }
        updated_invoice_aggregates = []
        all_templates = []

        while invoices_to_update_url:
            pending_invoice_aggregates = invoices_to_update_url.values()
            (
                templates,
                invoice_url_map,
            ) = self.pos_invoice_template_service.generate_invoice_templates(
                bill_aggregate,
                pending_invoice_aggregates,
                should_upload=should_upload,
            )

            for invoice_aggregate, template in zip(
                pending_invoice_aggregates, templates
            ):
                invoice_id = invoice_aggregate.invoice_id
                invoice_url, invoice_version = invoice_url_map.get(invoice_id)
                invoice_aggregate = self.invoice_repository.load_for_update(invoice_id)
                if invoice_aggregate.invoice.version != invoice_version:
                    invoices_to_update_url[invoice_id] = invoice_aggregate
                    continue

                if invoice_url.url:
                    invoice_aggregate.update_invoice_url(invoice_url.url)
                if invoice_url.signed_url and invoice_url.expiration:
                    invoice_aggregate.set_signed_url(
                        invoice_url.signed_url, invoice_url.expiration
                    )

                updated_invoice_aggregates.append(
                    invoices_to_update_url.pop(invoice_id)
                )
                all_templates.append(template)

        return updated_invoice_aggregates, all_templates
