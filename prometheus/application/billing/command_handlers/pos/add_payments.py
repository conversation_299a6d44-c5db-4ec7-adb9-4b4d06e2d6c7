from typing import List

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.cashiering.command_handlers.create_cashier_payments_from_bill import (
    CreateCashierPaymentFromBillCommandHandler,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.dto import PaymentData
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.catalog.repositories import SellerRepository
from ths_common.constants.billing_constants import (
    PaymentChannels,
    PaymentReceiverTypes,
    PaymentTypes,
)
from ths_common.constants.tenant_settings_constants import TenantSettingName


@register_instance(
    dependencies=[
        SellerRepository,
        TenantSettings,
        BillRepository,
        CreateCashierPaymentFromBillCommandHandler,
    ]
)
class PosBillAddPaymentsCommandHandler:
    def __init__(
        self,
        seller_repository: SellerRepository,
        tenant_settings: TenantSettings,
        bill_repository: BillRepository,
        create_cashier_payments_from_bill_command_handler: <PERSON>reateCashierPaymentFromBillCommandHandler,
    ):
        self.seller_repository = seller_repository
        self.tenant_settings = tenant_settings
        self.bill_repository = bill_repository
        self.create_cashier_payments_from_bill_command_handler = (
            create_cashier_payments_from_bill_command_handler
        )

    def handle(self, bill_aggregate, payment_dtos: List[PaymentData]):
        seller_aggregate = self.seller_repository.load(bill_aggregate.vendor_id)
        business_date = seller_aggregate.seller.current_business_date
        payment_date = dateutils.datetime_at_given_time(
            business_date, dateutils.current_time()
        )
        payments = []
        for payment_dto in payment_dtos:
            payment_dto.date_of_payment = payment_date
        for payment_dto in payment_dtos:
            payment = None
            if payment_dto.payment_type == PaymentTypes.REFUND:
                payment = self._add_refund(bill_aggregate, payment_dto)
            elif payment_dto.payment_type == PaymentTypes.PAYMENT:
                payment = self._add_payment(bill_aggregate, payment_dto)
            # In case cashiering is enabled for hotel then it would be enabled for seller too
            if (
                self.tenant_settings.get_setting_value(
                    TenantSettingName.CASHIERING_ENABLED.value,
                    hotel_id=seller_aggregate.seller.hotel_id,
                )
                and payment.payment_channel == PaymentChannels.FRONT_DESK
                and (
                    (
                        payment.payment_type == PaymentTypes.PAYMENT
                        and payment.paid_to == PaymentReceiverTypes.SELLER
                    )
                    or (
                        payment.payment_type == PaymentTypes.REFUND
                        and payment.paid_by == PaymentReceiverTypes.SELLER
                    )
                )
            ):
                self.create_cashier_payments_from_bill_command_handler.handle(
                    payment, booking_aggregate=None, bill_aggregate=bill_aggregate
                )
            payments.append(payment)
        self.bill_repository.update(bill_aggregate)
        # IntegrationEventApplicationService.create_bill_updated_event(**updated_aggregates, user_action="add_payment")
        return payments, bill_aggregate.bill.version, None

    @staticmethod
    def _add_refund(bill_aggregate, payment_dto):
        return bill_aggregate.add_payments([payment_dto])[0]

    @staticmethod
    def _add_payment(bill_aggregate, payment_dto):
        return bill_aggregate.add_payments([payment_dto])[0]
