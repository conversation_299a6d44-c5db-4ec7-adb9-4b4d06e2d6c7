from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.billing.dtos.new_bill_dto import NewBillDto
from prometheus.domain.billing.factories import BillFactory
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.repositories.invoice_series_repository import (
    InvoiceSequenceRepository,
)
from prometheus.domain.billing.services import TaxService
from prometheus.domain.catalog.repositories import SellerRepository
from ths_common.constants.billing_constants import IssuedByType
from ths_common.value_objects import BillParentInfo, VendorDetails


@register_instance(
    dependencies=[
        SellerRepository,
        TaxService,
        BillRepository,
        InvoiceSequenceRepository,
    ]
)
class PosCreateNewBillCommandHandler:
    def __init__(
        self,
        seller_repository: SellerRepository,
        tax_service: TaxService,
        bill_repository: BillRepository,
        invoice_series_repository: InvoiceSequenceRepository,
    ):
        self.seller_repository = seller_repository
        self.tax_service = tax_service
        self.bill_repository = bill_repository
        self.invoice_series_repository = invoice_series_repository

    def _get_next_bill_number(self, seller_id):
        seller_aggregate = self.seller_repository.load(seller_id)
        invoice_number = self.invoice_series_repository.get_next_invoice_number(
            IssuedByType.SELLER, seller_id, seller_aggregate.seller.gstin_num
        )
        return invoice_number

    def _create_bill(self, new_bill_dto: NewBillDto, base_currency):
        # NOTE: Sending charges under a list, because BillFactory expects nested list
        # Eventually order of charges in bill_aggregate will be same as order of charge_dtos in new_bill_dto
        bill_aggregate, _ = BillFactory.create_new_bill(
            vendor_id=new_bill_dto.vendor_id,
            vendor_details=new_bill_dto.vendor_details,
            app_id=new_bill_dto.app_id,
            nested_charge_dtos=[new_bill_dto.charges],
            bill_date=new_bill_dto.bill_date,
            base_currency=base_currency,
        )
        bill_aggregate.update_parent_info(
            new_bill_dto.parent_info,
            parent_reference_number=new_bill_dto.parent_reference_number,
        )
        self.bill_repository.save(bill_aggregate)
        return bill_aggregate

    def handle(self, new_bill_dto: NewBillDto):
        seller_aggregate = self.seller_repository.load(new_bill_dto.vendor_id)
        seller_context = crs_context.set_seller_context(seller_aggregate)
        new_bill_dto.vendor_details = VendorDetails.from_json(
            new_bill_dto.vendor_details
        )

        tax_updated_charge_dtos = self.tax_service.update_taxes(
            new_bill_dto.charges,
            buyer_gst_details=None,
            seller_has_lut=None,
            seller_id=seller_aggregate.seller.seller_id,
        )
        bill_parent_info_to_json = new_bill_dto.parent_info.to_json()['data']
        bill_parent_info_to_json['bill_number'] = self._get_next_bill_number(
            seller_aggregate.seller.seller_id
        )
        new_bill_dto.set_parent_info(BillParentInfo(bill_parent_info_to_json))
        new_bill_dto.charges = tax_updated_charge_dtos
        return self._create_bill(new_bill_dto, seller_context.base_currency)
