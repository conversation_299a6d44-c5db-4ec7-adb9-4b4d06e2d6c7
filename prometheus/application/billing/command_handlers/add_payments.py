from typing import List

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.billing.command_handlers.crs.add_payments import (
    CrsBillAddPaymentsCommandHandler,
)
from prometheus.application.billing.command_handlers.pos.add_payments import (
    PosBillAddPaymentsCommandHandler,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.billing.dto import PaymentData
from prometheus.domain.billing.repositories import BillRepository
from ths_common.constants.billing_constants import BillAppId


@register_instance(
    dependencies=[
        BillRepository,
        PosBillAddPaymentsCommandHandler,
        CrsBillAddPaymentsCommandHandler,
    ]
)
class AddPaymentsCommandHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        pos_add_payments_command_handler: PosBillAddPaymentsCommandHandler,
        crs_add_payments_command_handler: CrsBillAddPaymentsCommandHandler,
    ):
        self.bill_repository = bill_repository
        self.pos_add_payments_command_handler = pos_add_payments_command_handler
        self.crs_add_payments_command_handler = crs_add_payments_command_handler

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(
        self,
        bill_id,
        version,
        payment_dtos: List[PaymentData],
        user_data,
        hotel_aggregate=None,
    ):
        bill_aggregate = self.bill_repository.load_for_update(bill_id)
        crs_context.set_current_bill(bill_aggregate)
        if bill_aggregate.bill.app_id == BillAppId.POS_APP.value:
            return self.pos_add_payments_command_handler.handle(
                bill_aggregate, payment_dtos
            )
        elif bill_aggregate.bill.app_id == BillAppId.CRS_APP.value:
            return self.crs_add_payments_command_handler.handle(
                bill_aggregate, payment_dtos, user_data
            )
