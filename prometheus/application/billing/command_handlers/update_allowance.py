from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.ta_commission_helper import TACom<PERSON>Helper
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto.allowance_data import EditAllowanceData
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.allowance_facts import AllowanceFacts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import BillAppId, ChargeStatus
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        BillRepository,
        BookingRepository,
        TACommissionHelper,
    ]
)
class UpdateAllowanceCommandHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        booking_repository: BookingRepository,
        ta_commission_helper: TACommissionHelper,
    ):
        self.bill_repository = bill_repository
        self.booking_repository = booking_repository
        self.ta_commission_helper = ta_commission_helper

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(
        self,
        bill_id,
        version,
        charge_id,
        charge_split_id,
        allowance_id,
        edit_data: EditAllowanceData,
        hotel_aggregate=None,
    ):
        if version is None:
            raise ValidationException(ApplicationErrors.BILL_VERSION_MANDATORY)
        bill_aggregate = self.bill_repository.load_for_update(bill_id, version)
        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(bill_aggregate.bill.vendor_id)

        if bill_aggregate.bill.app_id == BillAppId.POS_APP.value:
            raise ValidationException(
                ApplicationErrors.UPDATE_ALLOWANCE_NOT_ALLOWED_FOR_POS
            )
        booking_aggregate = self.booking_repository.load_booking_by_bill_id(bill_id)
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)
        return self._update_allowance(
            bill_aggregate,
            booking_aggregate,
            charge_id,
            charge_split_id,
            allowance_id,
            edit_data,
        )

    def _update_allowance(
        self,
        bill_aggregate,
        booking_aggregate,
        charge_id,
        charge_split_id,
        allowance_id,
        edit_data,
    ):
        if edit_data.status == ChargeStatus.CONSUMED:
            RuleEngine.action_allowed(
                action="post_allowance",
                facts=AllowanceFacts(
                    bill_aggregate=bill_aggregate,
                    action_payload=dict(
                        charge_id=charge_id,
                        charge_split_id=charge_split_id,
                        allowance_id=allowance_id,
                    ),
                ),
                fail_on_error=True,
            )
            allowance = self._post_allowance(
                bill_aggregate, charge_id, charge_split_id, allowance_id, edit_data
            )
        elif edit_data.status == ChargeStatus.CANCELLED:
            RuleEngine.action_allowed(
                action="cancel_allowance",
                facts=AllowanceFacts(
                    bill_aggregate=bill_aggregate,
                    action_payload=dict(
                        charge_id=charge_id,
                        charge_split_id=charge_split_id,
                        allowance_id=allowance_id,
                    ),
                ),
                fail_on_error=True,
            )
            allowance = self._cancel_allowance(
                bill_aggregate,
                booking_aggregate,
                charge_id,
                charge_split_id,
                allowance_id,
                edit_data,
            )
        else:
            raise ValidationException(
                ApplicationErrors.INVALID_STATUS_REQUEST_FOR_ALLOWANCE
            )

        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)
        IntegrationEventApplicationService.create_bill_updated_event(
            bill_aggregate=bill_aggregate, user_action="update_allowance"
        )
        return allowance

    @audit(audit_type=AuditType.ALLOWANCE_CANCELLED)
    def _cancel_allowance(
        self,
        bill_aggregate,
        booking_aggregate,
        charge_id,
        charge_split_id,
        allowance_id,
        edit_data,
    ):
        allowances = bill_aggregate.update_allowance(
            charge_id,
            charge_split_id,
            allowance_id,
            edit_data,
            posting_date=crs_context.get_hotel_context().current_date(),
        )
        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_for_room_stay_from_charge_ids(
                bill_aggregate, booking_aggregate, charge_ids=[charge_id]
            )
        return allowances

    @audit(audit_type=AuditType.ALLOWANCE_POSTED)
    def _post_allowance(
        self, bill_aggregate, charge_id, charge_split_id, allowance_id, edit_data
    ):
        return bill_aggregate.update_allowance(
            charge_id,
            charge_split_id,
            allowance_id,
            edit_data,
            posting_date=crs_context.get_hotel_context().current_date(),
        )
