from object_registry import register_instance
from prometheus.domain.billing.dto.credit_shell_data import AddBalanceInCreditShellData
from prometheus.domain.billing.repositories.credit_shell_repository import (
    CreditShellRepository,
)


@register_instance(
    dependencies=[
        CreditShellRepository,
    ]
)
class CreditBalanceToCreditShellCommandHandler:
    def __init__(
        self,
        credit_shell_repository: CreditShellRepository,
    ):
        self.credit_shell_repository = credit_shell_repository

    def handle(
        self,
        edit_credit_shell_data: AddBalanceInCreditShellData,
        target_booking_reference_number,
    ):
        # Only allowed via payment edits which was done via credit shell
        credit_shell_aggregate = self.credit_shell_repository.load_for_update(
            credit_shell_id=edit_credit_shell_data.credit_shell_id
        )
        credit_shell_aggregate.add_balance_in_credit_shell(
            balance_amount=edit_credit_shell_data.credit_amount,
            target_booking_reference_number=target_booking_reference_number,
        )
        self.credit_shell_repository.update(credit_shell_aggregate)
        return credit_shell_aggregate
