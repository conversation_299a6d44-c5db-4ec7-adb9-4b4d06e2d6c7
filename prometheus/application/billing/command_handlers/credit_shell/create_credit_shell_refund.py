from treebo_commons.money import Money

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.billing.dtos.auto_refund_dtos import AutoRefundResponseDto
from prometheus.application.decorators import session_manager
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.attachment_service import AttachmentService
from prometheus.application.services.auto_refund_service import AutoRefundService
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.billing.domain_events.credit_shell_redeemed import (
    CreditShellRedeemedEvent,
)
from prometheus.domain.billing.dto.credit_shell_refund_data import CreditShellRefundData
from prometheus.domain.billing.factories.credit_shell_refund_factory import (
    CreditShellRefundFactory,
)
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.repositories.credit_shell_refund_repository import (
    CreditShellRefundRepository,
)
from prometheus.domain.billing.repositories.credit_shell_repository import (
    CreditShellRepository,
)
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import PaymentModes
from ths_common.constants.booking_constants import AttachmentGroup, AttachmentStatus
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.utils.id_generator_utils import generate_short_random_id
from ths_common.value_objects import AttachmentDetails, PayoutDetails


@register_instance(
    dependencies=[
        BillRepository,
        BookingRepository,
        CreditShellRepository,
        CreditShellRefundRepository,
        JobSchedulerService,
        AutoRefundService,
        TenantSettings,
        AttachmentService,
    ]
)
class CreditShellRefundCommandHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        booking_repository: BookingRepository,
        credit_shell_repository: CreditShellRepository,
        credit_shell_refund_repository: CreditShellRefundRepository,
        job_scheduler_service: JobSchedulerService,
        auto_refund_service: AutoRefundService,
        tenant_settings: TenantSettings,
        attachment_service: AttachmentService,
    ):
        self.bill_repository = bill_repository
        self.booking_repository = booking_repository
        self.credit_shell_repository = credit_shell_repository
        self.credit_shell_refund_repository = credit_shell_refund_repository
        self.job_scheduler_service = job_scheduler_service
        self.auto_refund_service = auto_refund_service
        self.tenant_settings = tenant_settings
        self.attachment_service = attachment_service

    @session_manager(commit=True)
    @audit(audit_type=AuditType.CREDIT_SHELL_REDEEMED)
    def handle(
        self,
        credit_shell_refund_data: CreditShellRefundData,
        user_data,
    ):
        """
        :param credit_shell_refund_data:
        :param user_data:
        :return:
        Redeem Credit Shell Summary
        """
        credit_shell_refund_aggregates = []
        credit_shell_aggregate = self.credit_shell_repository.load(
            credit_shell_refund_data.credit_shell_id
        )
        RuleEngine.action_allowed(
            action='redeem_credit_shell',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        bill_aggregate = self.bill_repository.load(
            bill_id=credit_shell_aggregate.bill_id
        )
        booking_aggregate = self.booking_repository.load_booking_by_bill_id(
            bill_id=credit_shell_aggregate.bill_id
        )
        crs_context_middleware.set_hotel_context(bill_aggregate.bill.vendor_id)
        crs_context.set_current_booking(booking_aggregate)

        remarks = f"Refunded via {credit_shell_refund_data.payment_mode}"
        if credit_shell_refund_data.remarks:
            remarks += f": {credit_shell_refund_data.remarks}"

        # consume the amount from the credit shell
        credit_shell_aggregate.consume_credit_shell(
            debit_amount=credit_shell_refund_data.amount,
            target_booking_reference_number=booking_aggregate.booking.reference_number,
            remarks=remarks,
        )

        # add attachments to credit shell
        if (
            credit_shell_refund_data.attachment_details
            and user_data.application
            and user_data.user_auth_id
        ):
            privileges = crs_context.privileges_as_dict
            if (
                privileges
                and PrivilegeCode.EMAIL_ATTACHMENT_FOR_CREDIT_SHELL_REFUND in privileges
            ):
                attachment_details = credit_shell_refund_data.attachment_details
                attachment_details.attachment_group = (
                    AttachmentGroup.CREDIT_SHELL_REFUND_REQUEST
                )
                attachment = self.attachment_service.add_attachment(
                    booking_aggregate.booking_id,
                    [
                        AttachmentDetails.to_json(
                            credit_shell_refund_data.attachment_details
                        )
                    ],
                    user_data.application,
                    user_data,
                    status=AttachmentStatus.VERIFIED,
                )[0]

        # create a payout link for refund
        self._create_payout_link_for_credit_shell_refund(
            credit_shell_refund_data, bill_aggregate, booking_aggregate
        )

        # create a credit shell refund
        credit_shell_refund_aggregate = self._create_credit_shell_refund(
            bill_aggregate,
            booking_aggregate,
            credit_shell_refund_data,
        )
        billed_entity = bill_aggregate.get_billed_entity(
            int(credit_shell_aggregate.credit_shell.source_billed_entity_id)
        )
        register_event(
            CreditShellRedeemedEvent(
                credit_shell_refund_aggregate.credit_shell_refund, billed_entity
            )
        )
        self.credit_shell_repository.update(credit_shell_aggregate)
        credit_shell_refund_aggregates.append(credit_shell_refund_aggregate)
        return credit_shell_refund_aggregates

    def _create_payout_link_for_credit_shell_refund(
        self, credit_shell_refund_data, bill_aggregate, booking_aggregate
    ):
        dummy_hotels = self.tenant_settings.get_dummy_hotels()
        if booking_aggregate.hotel_id in dummy_hotels:
            return
        if credit_shell_refund_data.payment_mode in [
            PaymentModes.RAZORPAY_API,
            PaymentModes.PAYOUT_LINK,
        ]:
            refund_dto: AutoRefundResponseDto = (
                self.auto_refund_service.add_refund_via_payout_link_for_credit_shell(
                    booking_aggregate,
                    credit_shell_refund_data,
                )
            )

            if refund_dto:
                credit_shell_refund_data.payout_details = refund_dto.payout_link
                credit_shell_refund_data.payment_ref_id = (
                    refund_dto.payout_link.pg_payout_id
                )

                auto_approved_payout_link_amount = (
                    self.tenant_settings.get_auto_approved_payout_link_amount(
                        hotel_id=booking_aggregate.hotel_id
                    )
                )
                auto_approved_pl_amount = Money(
                    auto_approved_payout_link_amount, bill_aggregate.bill.base_currency
                )
                if auto_approved_pl_amount > credit_shell_refund_data.amount:
                    self.auto_refund_service.send_payout_link_communication(
                        booking_aggregate, credit_shell_refund_data
                    )
                else:
                    self.auto_refund_service.send_payout_link_not_approved_communication(
                        booking_aggregate, credit_shell_refund_data
                    )

    def _create_credit_shell_refund(
        self,
        bill_aggregate,
        booking_aggregate,
        credit_shell_refund_data,
    ):
        credit_shell_id = credit_shell_refund_data.credit_shell_id
        paid_by, paid_to = bill_aggregate.get_paid_by_and_paid_to_for_credit_shell(
            credit_shell_id
        )
        payment_mode = (
            PaymentModes.RAZORPAY_API
            if credit_shell_refund_data.payment_mode == PaymentModes.PAYOUT_LINK
            else credit_shell_refund_data.payment_mode
        )
        credit_shell_refund_data = CreditShellRefundData(
            amount=credit_shell_refund_data.amount,
            bill_id=booking_aggregate.booking.bill_id,
            booking_id=booking_aggregate.booking.booking_id,
            credit_shell_id=credit_shell_id,
            hotel_id=booking_aggregate.booking.hotel_id,
            paid_by=paid_by,
            paid_to=paid_to,
            payment_mode=payment_mode,
            payout_details=PayoutDetails.to_json(
                credit_shell_refund_data.payout_details
            )
            if hasattr(credit_shell_refund_data, 'payout_details')
            else None,
            payment_ref_id=credit_shell_refund_data.payment_ref_id
            if hasattr(credit_shell_refund_data, 'payment_ref_id')
            else generate_short_random_id(prefix='R', length=9),
            remarks=credit_shell_refund_data.remarks,
        )
        credit_shell_refund_aggregate = (
            CreditShellRefundFactory.create_credit_shell_refund(
                credit_shell_refund_data=credit_shell_refund_data,
            )
        )
        self.credit_shell_refund_repository.save(
            credit_shell_refund_aggregate=credit_shell_refund_aggregate
        )
        return credit_shell_refund_aggregate
