from object_registry import register_instance
from prometheus.domain.billing.dto.credit_shell_data import ConsumeCreditShellData
from prometheus.domain.billing.repositories.credit_shell_repository import (
    CreditShellRepository,
)


@register_instance(
    dependencies=[
        CreditShellRepository,
    ]
)
class ConsumeCreditShellCommandHandler:
    def __init__(
        self,
        credit_shell_repository: CreditShellRepository,
    ):
        self.credit_shell_repository = credit_shell_repository

    def handle(
        self,
        consume_credit_shell_data: ConsumeCreditShellData,
        target_booking_reference_number,
    ):
        credit_shell_aggregate = self.credit_shell_repository.load_for_update(
            credit_shell_id=consume_credit_shell_data.credit_shell_id
        )
        credit_shell_aggregate.consume_credit_shell(
            debit_amount=consume_credit_shell_data.debit_amount,
            target_booking_reference_number=target_booking_reference_number,
        )
        self.credit_shell_repository.update(credit_shell_aggregate)
        return credit_shell_aggregate
