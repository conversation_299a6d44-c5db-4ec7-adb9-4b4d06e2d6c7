import logging
from typing import List

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.billing.command_handlers.credit_shell.consume_credit_shell import (
    ConsumeCreditShellCommandHandler,
)
from prometheus.application.billing.command_handlers.credit_shell.credit_balance_to_credit_shell import (
    CreditBalanceToCreditShellCommandHandler,
)
from prometheus.application.billing.helpers.payment_validator import PaymentValidator
from prometheus.application.billing.template_generation.payment_receipt_template_service import (
    PaymentReceiptTemplateService,
)
from prometheus.application.booking.dtos.billing_sub_resource_change_event import (
    BillingSubResourcesChangeEvent,
)
from prometheus.application.cashiering.command_handlers.create_cashier_payments_from_bill import (
    CreateCashierPaymentFromBillCommandHandler,
)
from prometheus.application.cashiering.command_handlers.update_cashier_payments_from_bill import (
    UpdateCashierPaymentFromBillCommandHandler,
)
from prometheus.application.helpers.payment_data_sanitizer import PaymentDataSanitizer
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.billing.payment_receipt_generation_service import (
    PaymentReceiptGenerationService,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto import PaymentData
from prometheus.domain.billing.dto.credit_shell_data import (
    AddBalanceInCreditShellData,
    ConsumeCreditShellData,
)
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.repositories.payment_receipt_repository import (
    PaymentReceiptRepository,
)
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.payment_facts import PaymentFacts
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    PaymentChannels,
    PaymentModes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingStatus, GuaranteeTypes
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.exceptions import ValidationException
from ths_common.value_objects import GuaranteeInformation

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        HotelRepository,
        BillRepository,
        BookingRepository,
        HotelConfigRepository,
        CreateCashierPaymentFromBillCommandHandler,
        UpdateCashierPaymentFromBillCommandHandler,
        PaymentReceiptGenerationService,
        PaymentReceiptTemplateService,
        JobSchedulerService,
        PaymentReceiptRepository,
        AwsServiceClient,
        TenantSettings,
        CreditBalanceToCreditShellCommandHandler,
        ConsumeCreditShellCommandHandler,
        PaymentDataSanitizer,
        PaymentValidator,
    ]
)
class CrsUpdatePaymentsCommandHandler:
    DefaultSignedUrlExpirationHours = 2
    DefaultSignedUrlExpirationSeconds = DefaultSignedUrlExpirationHours * 3600

    def __init__(
        self,
        hotel_repository: HotelRepository,
        bill_repository: BillRepository,
        booking_repository: BookingRepository,
        hotel_config_repository: HotelConfigRepository,
        create_cashier_payments_from_bill_command_handler: CreateCashierPaymentFromBillCommandHandler,
        update_cashier_payments_from_bill_command_handler: UpdateCashierPaymentFromBillCommandHandler,
        payment_receipt_generation_service: PaymentReceiptGenerationService,
        payment_receipt_template_service: PaymentReceiptTemplateService,
        job_scheduler_service: JobSchedulerService,
        payment_receipt_repository: PaymentReceiptRepository,
        aws_service_client: AwsServiceClient,
        tenant_settings: TenantSettings,
        credit_balance_to_credit_shell_handler: CreditBalanceToCreditShellCommandHandler,
        consume_credit_shell_handler: ConsumeCreditShellCommandHandler,
        payment_data_sanitizer: PaymentDataSanitizer,
        payment_validator: PaymentValidator,
    ):
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.booking_repository = booking_repository
        self.hotel_config_repository = hotel_config_repository
        self.create_cashier_payments_from_bill_command_handler = (
            create_cashier_payments_from_bill_command_handler
        )
        self.update_cashier_payments_from_bill_command_handler = (
            update_cashier_payments_from_bill_command_handler
        )
        self.payment_receipt_generation_service = payment_receipt_generation_service
        self.payment_receipt_template_service = payment_receipt_template_service
        self.job_scheduler_service = job_scheduler_service
        self.payment_receipt_repository = payment_receipt_repository
        self.aws_service_client = aws_service_client
        self.tenant_settings = tenant_settings
        self.credit_balance_to_credit_shell_handler = (
            credit_balance_to_credit_shell_handler
        )
        self.consume_credit_shell_handler = consume_credit_shell_handler
        self.payment_data_sanitizer = payment_data_sanitizer
        self.payment_validator = payment_validator

    @audit(audit_type=AuditType.PAYMENT_CANCELLED)
    def _cancel_payment(self, bill_aggregate, payment):
        bill_aggregate.cancel_payment(payment)

    @audit(audit_type=AuditType.REFUND_CANCELLED)
    def _cancel_refund(self, bill_aggregate, refund):
        bill_aggregate.cancel_payment(refund)

    @audit(audit_type=AuditType.REFUND_ADDED)
    def _cancel_payment_via_refund(self, bill_aggregate, payment):
        hotel_context = crs_context.get_hotel_context()
        payment_date = dateutils.datetime_at_given_time(
            hotel_context.current_date(), hotel_context.checkin_time
        )
        refund = bill_aggregate.cancel_payment_via_refund(payment, payment_date)
        if refund.status == PaymentStatus.POSTED:
            bill_aggregate.update_posting_date_of_payment(
                refund.payment_id, payment_date
            )
        return refund

    @audit(audit_type=AuditType.PAYMENT_POSTED)
    def _post_payment(self, bill_aggregate, payment):
        bill_aggregate.post_payment(payment)

    @audit(audit_type=AuditType.PAYMENT_MODIFIED)
    def _update_payment(
        self,
        bill_aggregate,
        payment,
        edit_data: PaymentData,
        default_billed_entity_category: BilledEntityCategory,
        hotel_aggregate,
    ):
        self._sanitize_edit_data_if_required(
            bill_aggregate, default_billed_entity_category, edit_data, payment
        )
        refund_rule = self.tenant_settings.get_refund_rule(
            hotel_id=hotel_aggregate.hotel_id,
        )
        is_auto_refund_enabled = self.tenant_settings.get_setting_value(
            TenantSettingName.IS_AUTO_REFUND_ENABLED.value,
            hotel_id=hotel_aggregate.hotel_id,
        )

        if edit_data.comment:
            payment.comment = edit_data.comment

        if edit_data.confirmed:
            payment.confirmed = edit_data.confirmed

        if edit_data.amount and payment.payment_type == PaymentTypes.REFUND:
            for split in payment.payment_splits:
                self.payment_validator.validate_edit_refund_rules(
                    hotel_aggregate.hotel_id, bill_aggregate, edit_data
                )
                billed_entity_account = split.billed_entity_account
                change_in_refund_amount = edit_data.amount - split.amount

                if change_in_refund_amount > bill_aggregate.get_net_paid_amount(
                    split.billed_entity_account
                ):
                    raise ValidationException(
                        BillingErrors.REFUND_AMOUNT_GREATER_THAN_TOTAL_PAID_AMOUNT,
                        extra_payload=dict(
                            refund_amount=change_in_refund_amount,
                            net_paid_amount=bill_aggregate.get_net_paid_amount(
                                billed_entity_account
                            ),
                            billed_entity_account=billed_entity_account.to_json(),
                        ),
                    )

        if (
            edit_data.payment_mode and edit_data.payment_mode != payment.payment_mode
        ) and (edit_data.amount or edit_data.payment_splits):
            self.payment_validator.validate_edit_payment_mode(
                hotel_aggregate.hotel_id, bill_aggregate, edit_data
            )
            self.payment_validator.validate_edit_payment_rules(
                hotel_aggregate.hotel_id, bill_aggregate, edit_data
            )
            if payment.status == PaymentStatus.POSTED:
                raise ValidationException(ApplicationErrors.CANT_MODIFY_POSTED_PAYMENT)
            if edit_data.amount:
                amount = edit_data.amount
            else:
                amount = sum([ps.amount for ps in edit_data.payment_splits])

            if (
                payment.payment_mode == PaymentModes.CREDIT_SHELL
                and edit_data.payment_mode != PaymentModes.CREDIT_SHELL
            ):
                # Credit already paid amount when New Payment Mode has changed from Credit Shell.
                add_balance_in_credit_shell = AddBalanceInCreditShellData(
                    credit_amount=payment.amount, credit_shell_id=payment.payment_ref_id
                )
                self.credit_balance_to_credit_shell_handler.handle(
                    add_balance_in_credit_shell,
                    target_booking_reference_number=bill_aggregate.bill.parent_info.get(
                        'reference_number'
                    ),
                )

            if (
                payment.payment_mode != PaymentModes.CREDIT_SHELL
                and edit_data.payment_mode == PaymentModes.CREDIT_SHELL
            ):
                # Debit Credit Shell when New Payment Mode changes to Credit Shell.
                self.consume_credit_shell_handler.handle(
                    ConsumeCreditShellData(
                        debit_amount=amount, credit_shell_id=payment.payment_ref_id
                    ),
                    target_booking_reference_number=bill_aggregate.bill.parent_info.get(
                        'reference_number'
                    ),
                )
            payment.payment_mode = edit_data.payment_mode
            bill_aggregate.update_payment_amount(
                payment,
                edit_data.amount,
                edit_data.amount_in_payment_currency,
                edit_data.payment_splits,
            )

        elif (edit_data.amount or edit_data.payment_splits) and not (
            edit_data.payment_mode and edit_data.payment_mode != payment.payment_mode
        ):
            self.payment_validator.validate_edit_payment_rules(
                hotel_aggregate.hotel_id, bill_aggregate, edit_data
            )
            if payment.status == PaymentStatus.POSTED:
                raise ValidationException(ApplicationErrors.CANT_MODIFY_POSTED_PAYMENT)
            if edit_data.amount:
                amount = edit_data.amount
            else:
                amount = sum([ps.amount for ps in edit_data.payment_splits])
            if payment.payment_mode == PaymentModes.CREDIT_SHELL:
                if amount > payment.amount:
                    # Debit Credit Shell for extra amount to be paid.
                    self.consume_credit_shell_handler.handle(
                        ConsumeCreditShellData(
                            debit_amount=amount - payment.amount,
                            credit_shell_id=payment.payment_ref_id,
                        ),
                        target_booking_reference_number=bill_aggregate.bill.parent_info.get(
                            'reference_number'
                        ),
                    )
                else:
                    # Credit into Credit Shell for surplus amount.
                    add_balance_in_credit_shell = AddBalanceInCreditShellData(
                        credit_amount=payment.amount - amount,
                        credit_shell_id=payment.payment_ref_id,
                    )
                    self.credit_balance_to_credit_shell_handler.handle(
                        add_balance_in_credit_shell,
                        target_booking_reference_number=bill_aggregate.bill.parent_info.get(
                            'reference_number'
                        ),
                    )
            bill_aggregate.update_payment_amount(
                payment,
                edit_data.amount,
                edit_data.amount_in_payment_currency,
                edit_data.payment_splits,
            )

        elif (
            edit_data.payment_mode and edit_data.payment_mode != payment.payment_mode
        ) and not edit_data.amount:
            self.payment_validator.validate_edit_payment_mode(
                hotel_aggregate.hotel_id, bill_aggregate, edit_data
            )
            if (
                payment.payment_mode == PaymentModes.CREDIT_SHELL
                and edit_data.payment_mode != PaymentModes.CREDIT_SHELL
            ):
                # Credit into Credit Shell for amount.
                add_balance_in_credit_shell = AddBalanceInCreditShellData(
                    credit_amount=payment.amount, credit_shell_id=payment.payment_ref_id
                )
                self.credit_balance_to_credit_shell_handler.handle(
                    add_balance_in_credit_shell,
                    target_booking_reference_number=bill_aggregate.bill.parent_info.get(
                        'reference_number'
                    ),
                )
            if (
                payment.payment_mode != PaymentModes.CREDIT_SHELL
                and edit_data.payment_mode == PaymentModes.CREDIT_SHELL
            ):
                # Debit Credit Shell for amount.
                self.consume_credit_shell_handler.handle(
                    ConsumeCreditShellData(
                        debit_amount=payment.amount,
                        credit_shell_id=payment.payment_ref_id,
                    ),
                    target_booking_reference_number=bill_aggregate.bill.parent_info.get(
                        'reference_number'
                    ),
                )

            payment.payment_mode = edit_data.payment_mode

        if edit_data.payment_ref_id:
            if payment.status == PaymentStatus.POSTED:
                raise ValidationException(ApplicationErrors.CANT_MODIFY_POSTED_PAYMENT)
            payment.payment_ref_id = edit_data.payment_ref_id

        if edit_data.payment_details:
            payment.payment_details = edit_data.payment_details

        if edit_data.paid_by and payment.paid_by != edit_data.paid_by:
            if payment.status == PaymentStatus.POSTED:
                raise ValidationException(ApplicationErrors.CANT_MODIFY_POSTED_PAYMENT)
            payment.paid_by = edit_data.paid_by

        if edit_data.paid_to and payment.paid_to != edit_data.paid_to:
            if payment.status == PaymentStatus.POSTED:
                raise ValidationException(ApplicationErrors.CANT_MODIFY_POSTED_PAYMENT)
            if payment.payment_mode == PaymentModes.CREDIT_SHELL:
                edit_data.paid_to = payment.paid_to
            payment.paid_to = edit_data.paid_to

        if edit_data.payer:
            if payment.status == PaymentStatus.POSTED:
                raise ValidationException(ApplicationErrors.CANT_MODIFY_POSTED_PAYMENT)
            payment.payer = edit_data.payer

        if edit_data.payment_mode_sub_type:
            if payment.status == PaymentStatus.POSTED:
                raise ValidationException(ApplicationErrors.CANT_MODIFY_POSTED_PAYMENT)
            payment.payment_mode_sub_type = edit_data.payment_mode_sub_type

        if (
            edit_data.payor_billed_entity_id
            and edit_data.payor_billed_entity_id != payment.payor_billed_entity_id
        ):
            bill_aggregate.get_billed_entity(edit_data.payor_billed_entity_id)
            if payment.status == PaymentStatus.POSTED:
                raise ValidationException(ApplicationErrors.CANT_MODIFY_POSTED_PAYMENT)
            payment.payor_billed_entity_id = edit_data.payor_billed_entity_id

        if edit_data.refund_reason:
            payment.refund_reason = edit_data.refund_reason

        should_post_payment = False
        if (
            edit_data.payment_channel
            and edit_data.payment_channel != payment.payment_channel
        ):
            if payment.status == PaymentStatus.POSTED:
                raise ValidationException(ApplicationErrors.CANT_MODIFY_POSTED_PAYMENT)
            if (
                crs_context.is_treebo_tenant()
                and edit_data.payment_channel == PaymentChannels.ONLINE
            ):
                should_post_payment = True
            payment.payment_channel = edit_data.payment_channel

        if edit_data.payment_mode:
            if payment.status == PaymentStatus.POSTED:
                raise ValidationException(ApplicationErrors.CANT_MODIFY_POSTED_PAYMENT)
            if (
                is_auto_refund_enabled
                and refund_rule
                and edit_data.payment_mode in refund_rule.refund_mode_priority_list
            ):
                should_post_payment = True
            payment.payment_mode = edit_data.payment_mode

        if should_post_payment:
            bill_aggregate.post_payment_and_update_posting_date(
                payment.payment_id, payment.date_of_payment
            )

    def _sanitize_edit_data_if_required(
        self, bill_aggregate, default_billed_entity_category, edit_data, payment
    ):
        # we should sanitize the payment data on following cases
        #   1) either payment_mode or payor_billed_entity is changing on edit
        #   2) either paid to or paid by is provided in edit_data
        should_sanitize_data = (
            (edit_data.payment_mode and payment.payment_mode != edit_data.payment_mode)
            or (
                edit_data.payor_billed_entity_id
                and edit_data.payor_billed_entity_id != payment.payor_billed_entity_id
            )
            or edit_data.paid_to
            or edit_data.paid_by
        )
        if should_sanitize_data:
            edit_data.payor_billed_entity_id = (
                edit_data.payor_billed_entity_id or payment.payor_billed_entity_id
            )
            edit_data.payment_mode = edit_data.payment_mode or payment.payment_mode
            edit_data.payment_type = payment.payment_type
            edit_data.payment_channel = (
                edit_data.payment_channel or payment.payment_channel
            )
            self.payment_data_sanitizer.sanitize(
                [edit_data], bill_aggregate, default_billed_entity_category
            )

    def generate_or_refresh_payment_receipt(
        self,
        payment_id,
        payment,
        bill_aggregate,
        booking_aggregate,
        hotel_context,
        show_receipt,
    ):
        payment_receipt_aggregate = self.payment_receipt_repository.load_for_update(
            payment_id, bill_aggregate.bill.bill_id
        )
        if not payment_receipt_aggregate:
            payment_receipt_aggregate = (
                self.payment_receipt_generation_service.generate_payment_receipt(
                    payment, bill_aggregate, booking_aggregate, hotel_context
                )
            )
            if show_receipt:
                self.payment_receipt_template_service.create_and_upload_payment_receipts(
                    payment_receipt_aggregate, payment, bill_aggregate
                )
            self.payment_receipt_repository.save(payment_receipt_aggregate)
        else:
            payment_receipt_aggregate = (
                self.payment_receipt_generation_service.refresh_payment_receipt(
                    payment_receipt_aggregate,
                    payment,
                    bill_aggregate,
                    booking_aggregate,
                    hotel_context,
                )
            )
            if show_receipt:
                self.payment_receipt_template_service.create_and_upload_payment_receipts(
                    payment_receipt_aggregate, payment, bill_aggregate
                )
            self.payment_receipt_repository.update(payment_receipt_aggregate)
        if (
            not show_receipt
            and payment_receipt_aggregate
            and not payment_receipt_aggregate.deleted
        ):
            self.job_scheduler_service.schedule_payment_receipt_upload(
                payment_receipt_aggregate,
                booking_aggregate.is_reserved(),
            )
        return payment_receipt_aggregate

    def handle(self, bill_aggregate, payment_dtos: List[PaymentData], user_data):
        booking_aggregate = self.booking_repository.load_booking_by_bill_id(
            bill_aggregate.bill_id
        )
        hotel_id = booking_aggregate.booking.hotel_id
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)

        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
        )
        crs_context.set_current_booking(booking_aggregate)
        updated_payments = list()
        is_cashiering_enabled = self.tenant_settings.get_setting_value(
            TenantSettingName.CASHIERING_ENABLED.value,
            hotel_id=hotel_aggregate.hotel_id,
        )

        updated_aggregates, updated_payments_for_event = dict(), []
        for edit_data in payment_dtos:
            edit_data.set_currency_in_amount_if_missing()

            RuleEngine.action_allowed(
                action="edit_payment",
                facts=PaymentFacts(
                    user_type=user_data.user_type,
                    action_payload=edit_data,
                    booking_aggregate=booking_aggregate,
                    bill_aggregate=bill_aggregate,
                    hotel_context=crs_context.get_hotel_context(),
                ),
                fail_on_error=True,
            )
            payment = bill_aggregate.get_payment(edit_data.payment_id)

            if edit_data.status:
                if edit_data.status == PaymentStatus.CANCELLED:
                    if edit_data.comment:
                        payment.comment = edit_data.comment
                    if payment.status == PaymentStatus.POSTED:
                        # PROM-15717 : If we cancel posted refund we would not allow it.
                        if payment.payment_type == PaymentTypes.REFUND:
                            raise ValidationException(
                                ApplicationErrors.CANNOT_CANCEL_POSTED_REFUND
                            )
                        refund = self._cancel_payment_via_refund(
                            bill_aggregate, payment
                        )
                        if refund.payment_mode == PaymentModes.CREDIT_SHELL:
                            self.credit_balance_to_credit_shell_handler.handle(
                                AddBalanceInCreditShellData(
                                    credit_amount=refund.amount,
                                    credit_shell_id=refund.payment_ref_id,
                                ),
                                target_booking_reference_number=booking_aggregate.booking.reference_number,
                            )
                        if is_cashiering_enabled and refund.should_sync_to_cashier():
                            self.create_cashier_payments_from_bill_command_handler.handle(
                                refund, booking_aggregate, bill_aggregate=bill_aggregate
                            )
                    else:
                        if payment.payment_type == PaymentTypes.PAYMENT:
                            self._cancel_payment(bill_aggregate, payment)
                            if payment.payment_mode == PaymentModes.CREDIT_SHELL:
                                self.credit_balance_to_credit_shell_handler.handle(
                                    AddBalanceInCreditShellData(
                                        credit_amount=payment.amount,
                                        credit_shell_id=payment.payment_ref_id,
                                    ),
                                    target_booking_reference_number=booking_aggregate.booking.reference_number,
                                )
                        elif payment.payment_type == PaymentTypes.REFUND:
                            self._cancel_refund(bill_aggregate, payment)
                            if payment.payment_mode == PaymentModes.CREDIT_SHELL:
                                self.consume_credit_shell_handler.handle(
                                    ConsumeCreditShellData(
                                        debit_amount=payment.amount,
                                        credit_shell_id=payment.payment_ref_id,
                                    ),
                                    target_booking_reference_number=booking_aggregate.booking.reference_number,
                                )
                elif edit_data.status == PaymentStatus.POSTED:
                    self._post_payment(bill_aggregate, payment)
            self._update_payment(
                bill_aggregate,
                payment,
                edit_data,
                booking_aggregate.get_default_billed_entity_category(),
                hotel_aggregate,
            )
            if payment.payment_type in [PaymentTypes.PAYMENT, PaymentTypes.REFUND]:
                payment_receipt_aggregate = self.generate_or_refresh_payment_receipt(
                    edit_data.payment_id,
                    payment,
                    bill_aggregate,
                    booking_aggregate,
                    hotel_context,
                    edit_data.show_receipt,
                )
                if (
                    not edit_data.show_receipt
                    and payment_receipt_aggregate
                    and not payment_receipt_aggregate.deleted
                ):
                    updated_payments_for_event.append(payment.payment_id)

            if payment.is_dirty():
                if is_cashiering_enabled and payment.should_sync_to_cashier():
                    self.update_cashier_payments_from_bill_command_handler.handle(
                        payment, booking_aggregate, bill_aggregate=bill_aggregate
                    )

            updated_payments.append(payment)

        is_booking_modified = False
        if self.tenant_settings.is_guarantee_enabled(hotel_id):
            if booking_aggregate.guarantee_information == GuaranteeInformation(
                GuaranteeTypes.PAYMENT_GUARANTEE
            ) and not bill_aggregate.get_net_payment(
                exclude_modes=[PaymentModes.TREEBO_POINTS]
            ):
                booking_aggregate.update_guarantee_information(
                    guarantee_information=None, allow_state_transition=True
                )
                is_booking_modified = True

            if (
                bill_aggregate.get_net_payment(
                    exclude_modes=[PaymentModes.TREEBO_POINTS]
                )
                and not booking_aggregate.guarantee_information
            ):
                booking_aggregate.update_guarantee_information(
                    guarantee_information=GuaranteeInformation(
                        GuaranteeTypes.PAYMENT_GUARANTEE
                    ),
                    allow_state_transition=True,
                )
                if booking_aggregate.booking.status in [
                    BookingStatus.RESERVED,
                    BookingStatus.TEMPORARY,
                ]:
                    booking_aggregate.confirm()

                is_booking_modified = True

        if is_booking_modified:
            self.booking_repository.update(booking_aggregate)
            updated_aggregates['booking_aggregate'] = booking_aggregate

        if updated_payments_for_event:
            updated_aggregates[
                'billing_sub_resource_change_event'
            ] = BillingSubResourcesChangeEvent(
                bill_id=bill_aggregate.bill_id,
                affected_resource="billing.payments",
                affected_resource_ids=updated_payments_for_event,
            )
        self.bill_repository.update(bill_aggregate)
        updated_aggregates['bill_aggregate'] = bill_aggregate
        IntegrationEventApplicationService.create_bill_updated_event(
            **updated_aggregates, user_action="update_payment"
        )
        return updated_payments, bill_aggregate.bill.version
