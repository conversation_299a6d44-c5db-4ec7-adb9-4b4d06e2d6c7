from typing import List

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.ta_commission_helper import TAC<PERSON><PERSON>Helper
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto.allowance_data import AllowanceData
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services.add_allowance_service import AddAllowanceService
from prometheus.domain.booking.repositories import BookingRepository
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import BillAppId, ChargeStatus
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        BillRepository,
        BookingRepository,
        AddAllowanceService,
        TACommissionHelper,
    ]
)
class AddAllowancesCommandHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        booking_repository: BookingRepository,
        add_allowance_service: AddAllowanceService,
        ta_commission_helper: TACommissionHelper,
    ):
        self.bill_repository = bill_repository
        self.booking_repository = booking_repository
        self.add_allowance_service = add_allowance_service
        self.ta_commission_helper = ta_commission_helper

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(
        self,
        bill_id,
        resource_version,
        allowance_dtos: List[AllowanceData],
        hotel_aggregate=None,
    ):
        bill_aggregate = self.bill_repository.load_for_update(bill_id, resource_version)
        crs_context.set_current_bill(bill_aggregate)
        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(bill_aggregate.bill.vendor_id)

        if bill_aggregate.bill.app_id == BillAppId.POS_APP.value:
            raise ValidationException(
                ApplicationErrors.ADD_ALLOWANCE_NOT_ALLOWED_FOR_POS
            )

        booking_aggregate = self.booking_repository.load_booking_by_bill_id(bill_id)
        crs_context.set_current_booking(booking_aggregate)
        return self._add_allowances(
            bill_aggregate,
            booking_aggregate,
            allowance_dtos,
            self.ta_commission_helper,
        )

    @audit(audit_type=AuditType.ALLOWANCE_PASSED)
    def _add_allowances(
        self, bill_aggregate, booking_aggregate, allowance_dtos, ta_commission_helper
    ):
        allowances = list()
        for allowance_dto in allowance_dtos:
            charge: Charge = bill_aggregate.get_charge(allowance_dto.charge_id)
            if charge.status != ChargeStatus.CONSUMED:
                raise ValidationException(
                    BillingErrors.INVALID_CHARGE_STATUS_FOR_ADDING_ALLOWANCE
                )

            allowance = self.add_allowance_service.add_allowance_to_charge_split(
                bill_aggregate,
                charge,
                allowance_dto.charge_split_id,
                allowance_dto,
                booking_aggregate,
            )
            allowances.append(allowance)
        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            ta_commission_helper.recalculate_commission_for_room_stay_from_charge_ids(
                bill_aggregate,
                booking_aggregate,
                charge_ids=[a.charge_id for a in allowance_dtos],
            )
        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)
        IntegrationEventApplicationService.create_bill_updated_event(
            bill_aggregate=bill_aggregate, user_action="add_allowance"
        )
        return allowances, bill_aggregate.bill.version
