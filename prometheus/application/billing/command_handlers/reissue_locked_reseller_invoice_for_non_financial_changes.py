from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.billing.command_handlers.modify_locked_invoices import (
    ModifyLockedInvoicesCommandHandler,
)
from prometheus.application.billing.dtos.invoice_reissue_dto import (
    ModifyLockedInvoicesDto,
)
from prometheus.application.booking.command_handlers.invoice.invoice_accounts import (
    InvoiceAccountsCommandHandler,
)
from prometheus.application.decorators import session_manager
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories import (
    BillRepository,
    CreditNoteRepository,
    InvoiceRepository,
)
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.integration_event_constants import IntegrationEventType

# This handler can only be used for non financial modification of reseller to customer invoice
# will nullify old sell side invoice and reissue new one.
# corresponding hotel invoice remains untouched
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        ModifyLockedInvoicesCommandHandler,
        InvoiceAccountsCommandHandler,
        BillRepository,
        InvoiceRepository,
        BookingRepository,
        CreditNoteRepository,
        JobSchedulerService,
    ]
)
class ModifyLockedResellerInvoiceCommandHandler:
    def __init__(
        self,
        modify_locked_invoice_command_handler: ModifyLockedInvoicesCommandHandler,
        invoice_account_command_handler: InvoiceAccountsCommandHandler,
        bill_repository: BillRepository,
        invoice_repository: InvoiceRepository,
        booking_repository: BookingRepository,
        credit_note_repository: CreditNoteRepository,
        job_scheduler_service: JobSchedulerService,
    ):
        self.modify_locked_invoice_command_handler = (
            modify_locked_invoice_command_handler
        )
        self.invoice_account_command_handler = invoice_account_command_handler
        self.bill_repository = bill_repository
        self.invoice_repository = invoice_repository
        self.booking_repository = booking_repository
        self.credit_note_repository = credit_note_repository
        self.job_scheduler_service = job_scheduler_service

    @session_manager(commit=True)
    @audit(audit_type=AuditType.INVOICE_REISSUED_FOR_NON_FINANCIAL_CHANGES)
    def handle(
        self,
        bill_id,
        resource_version,
        modify_locked_invoices_dto: ModifyLockedInvoicesDto,
        user_data,
        generate_hotel_side=False,
        hotel_aggregate=None,
    ) -> JobResultDto:
        RuleEngine.action_allowed(
            action='reissue_invoice_without_buy_side',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        self._disallow_merge_and_split_of_invoices(modify_locked_invoices_dto)
        (
            accounts_summary,
            bill_aggregate,
            booking_aggregate,
            credit_note_aggregates,
            _,
            invoice_aggregates,
        ) = self.modify_locked_invoice_command_handler.handle_invoice_modification(
            bill_id,
            generate_hotel_side,
            hotel_aggregate,
            modify_locked_invoices_dto,
            resource_version,
            user_data,
            non_financial_modification_of_reseller_invoice=True,
        )
        bea_to_hotel_invoice_map = self._build_bea_to_hotel_invoice_mapping(
            invoice_aggregates, modify_locked_invoices_dto
        )
        RuleEngine.action_allowed(
            action='generate_invoice_for_billed_entity_accounts',
            facts=Facts(
                user_type=user_data.user_type, booking_aggregate=booking_aggregate
            ),
            fail_on_error=True,
        )
        billed_entity_accounts = [
            account.billed_entity_account for account in accounts_summary
        ]
        hotel_context = crs_context.get_hotel_context()
        new_invoice_aggregates = None
        if len(billed_entity_accounts) > 0:
            (
                _,
                new_invoice_aggregates,
            ) = self.invoice_account_command_handler.generate_invoices_for_billed_entity_accounts(
                bill_aggregate,
                billed_entity_accounts,
                booking_aggregate,
                hotel_context,
                generate_hotel_side=False,
            )
            self._map_hotel_invoice_to_newly_created_sales_invoice(
                bea_to_hotel_invoice_map, new_invoice_aggregates
            )
        self.bill_repository.update(bill_aggregate)
        self.booking_repository.update(booking_aggregate)
        self.invoice_repository.update_all(invoice_aggregates)
        self.credit_note_repository.save_all(credit_note_aggregates)
        if new_invoice_aggregates:
            self.invoice_repository.save_all(new_invoice_aggregates)
            self.job_scheduler_service.schedule_invoice_upload(
                bill_aggregate, new_invoice_aggregates, send_invoices_to_guest=False
            )

        self.job_scheduler_service.schedule_credit_note_upload(
            bill_aggregate.bill_id, credit_note_aggregates
        )
        updated_aggregates = dict(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            invoice_aggregates=invoice_aggregates,
        )
        IntegrationEventApplicationService.create_booking_updated_event(
            **updated_aggregates, user_action="modify_locked_invoices"
        )
        if credit_note_aggregates:
            IntegrationEventApplicationService.create_credit_note_event(
                event_type=IntegrationEventType.CREDIT_NOTE_GENERATED,
                credit_note_aggregates=credit_note_aggregates,
                invoice_aggregates=invoice_aggregates,
                user_action="modify_locked_invoices",
            )
        if new_invoice_aggregates:
            IntegrationEventApplicationService.create_invoice_event(
                event_type=IntegrationEventType.INVOICE_GENERATED,
                invoice_aggregates=new_invoice_aggregates,
                user_action="generate_invoice_accounts",
            )

        self.modify_locked_invoice_command_handler.refresh_account_summary(
            bill_aggregate, account_summary_dtos=accounts_summary
        )
        return JobResultDto.success(
            credit_note_aggregates=credit_note_aggregates,
            accounts_summary=accounts_summary,
        )

    @staticmethod
    def _map_hotel_invoice_to_newly_created_sales_invoice(
        bea_to_hotel_invoice_map, new_invoice_aggregates
    ):
        for new_invoice_aggregate in new_invoice_aggregates:
            bea = new_invoice_aggregate.invoice.billed_entity_account
            new_invoice_aggregate.set_hotel_invoice(bea_to_hotel_invoice_map[bea])

    @staticmethod
    def _build_bea_to_hotel_invoice_mapping(
        invoice_aggregates, modify_locked_invoices_dto
    ):
        invoice_id_to_hotel_invoice_id_map = {
            invoice_aggregates.invoice.invoice_id: invoice_aggregates.invoice.hotel_invoice_id
            for invoice_aggregates in invoice_aggregates
        }
        bea_to_hotel_invoice_map = {}
        for invoice_modification in modify_locked_invoices_dto.modify_invoices:
            for modification in invoice_modification['invoice_charge_modification']:
                hotel_invoice_id = invoice_id_to_hotel_invoice_id_map[
                    invoice_modification['invoice_id']
                ]
                bea_to_hotel_invoice_map[
                    modification['billed_entity_account']
                ] = hotel_invoice_id
        return bea_to_hotel_invoice_map

    @staticmethod
    def _disallow_merge_and_split_of_invoices(modify_locked_invoices_dto):
        bea_across_the_modification = set()
        for invoice_modification in modify_locked_invoices_dto.modify_invoices:
            bea = {
                modification['billed_entity_account']
                for modification in invoice_modification['invoice_charge_modification']
            }
            if len(bea) > 1:
                raise ValidationException(
                    ApplicationErrors.CANNOT_SPLIT_INVOICE_IN_NON_FINANCIAL_REISSUE
                )
            if bea_across_the_modification.intersection(bea):
                raise ValidationException(
                    ApplicationErrors.CANNOT_MERGE_INVOICE_IN_NON_FINANCIAL_REISSUE
                )
            bea_across_the_modification.update(bea)
