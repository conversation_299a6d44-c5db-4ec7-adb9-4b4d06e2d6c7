from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.invoice_issuer_service import InvoiceIssuerService
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto import CreditNoteData
from prometheus.domain.billing.dto.credit_note_data import CreditNoteLineItemData
from prometheus.domain.billing.repositories import (
    BillRepository,
    CreditNoteRepository,
    InvoiceRepository,
)
from prometheus.domain.billing.services import CreditNoteGenerationService
from prometheus.domain.billing.services.add_allowance_service import AddAllowanceService
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import ResellerGstRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import CreditNoteFacts
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        InvoiceRepository,
        CreditNoteGenerationService,
        AddAllowanceService,
        JobSchedulerService,
        CreditNoteRepository,
        ResellerGstRepository,
    ]
)
class GenerateCreditNoteCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        invoice_repository: InvoiceRepository,
        credit_note_generation_service: CreditNoteGenerationService,
        add_allowance_service: AddAllowanceService,
        job_scheduler_service: JobSchedulerService,
        credit_note_repository: CreditNoteRepository,
        reseller_gst_repository: ResellerGstRepository,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.invoice_repository = invoice_repository
        self.credit_note_generation_service = credit_note_generation_service
        self.add_allowance_service = add_allowance_service
        self.job_scheduler_service = job_scheduler_service
        self.credit_note_repository = credit_note_repository
        self.reseller_gst_repository = reseller_gst_repository

    def _get_issuer_details_of_invoices(self, grouped_invoices, hotel_context):
        vendor_details = hotel_context.build_vendor_details()

        (
            issued_by_type,
            issued_to_type,
            issued_to,
        ) = self.credit_note_generation_service.validate_and_get_issued_details_from_invoices(
            grouped_invoices
        )
        reseller_gst_detail_aggregate = self.reseller_gst_repository.load(
            hotel_context.legal_state_id
        )
        issued_by = InvoiceIssuerService.get_issuer(
            issued_by_type, vendor_details, reseller_gst_detail_aggregate
        )
        return issued_by_type, issued_by, issued_to_type, issued_to

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(self, bill_id, credit_note_schema, user_data, hotel_aggregate=None):
        """

        # TODO: Not Used Anymore, from PMS

        :param bill_id: Bill containing the invoices on which credit notes needs to be generated
        :param credit_note_schema: NewCreditNoteSchema object
        :param user_data:
        :return:
        """
        hotel_context = crs_context.get_hotel_context() if hotel_aggregate else None
        booking_aggregate = self.booking_repository.load_booking_by_bill_id(bill_id)
        bill_aggregate = self.bill_repository.load(bill_id)

        if not hotel_aggregate:
            (
                hotel_aggregate,
                _,
                hotel_context,
            ) = crs_context_middleware.set_hotel_context(bill_aggregate.bill.vendor_id)

        credit_note_data_list = []
        invoice_ids_in_credit_note = set()
        for line_item in credit_note_schema['credit_note_line_items']:
            if not line_item['posttax_amount'].currency:
                line_item['posttax_amount'] = Money(
                    line_item['posttax_amount'].amount,
                    bill_aggregate.bill.base_currency,
                )
            invoice_id, invoice_charge_id, posttax_amount = (
                line_item['invoice_id'],
                line_item['invoice_charge_id'],
                line_item['posttax_amount'],
            )
            invoice_ids_in_credit_note.add(invoice_id)
            credit_note_data_list.append(
                CreditNoteLineItemData(
                    invoice_id=invoice_id,
                    invoice_charge_id=invoice_charge_id,
                    posttax_amount=posttax_amount,
                )
            )

        if not credit_note_data_list:
            raise ValidationException(
                error=ApplicationErrors.EMPTY_CREDIT_NOTE_GENERATION
            )

        invoice_versions = credit_note_schema.get('invoice_versions', [])

        invoice_aggregates = self.invoice_repository.load_all_for_update(
            [invoice_version['invoice_id'] for invoice_version in invoice_versions],
            [invoice_version['version'] for invoice_version in invoice_versions],
        )
        if len(invoice_aggregates) > 1:
            raise ValidationException(
                error=ApplicationErrors.CANNOT_GENERATE_CREDIT_NOTE_ON_MULTIPLE_INVOICES
            )
        grouped_invoices = {
            invoice_aggregate.invoice.invoice_id: invoice_aggregate
            for invoice_aggregate in invoice_aggregates
        }

        if invoice_ids_in_credit_note - grouped_invoices.keys() != set():
            raise ValidationException(
                error=ApplicationErrors.INVOICE_VERSION_MANDATORY_FOR_UPDATE
            )

        RuleEngine.action_allowed(
            action="generate_credit_note",
            facts=CreditNoteFacts(
                user_type=user_data.user_type,
                current_time=dateutils.current_datetime(),
                action_payload=credit_note_schema,
                invoice_aggregates=invoice_aggregates,
                hotel_context=hotel_context,
            ),
            fail_on_error=True,
        )

        generate_hotel_credit_note = credit_note_schema['generate_hotel_credit_note']

        credit_note_data = CreditNoteData(credit_note_line_items=credit_note_data_list)

        (
            issued_by_type,
            issued_by,
            issued_to_type,
            issued_to,
        ) = self._get_issuer_details_of_invoices(grouped_invoices, hotel_context)
        (
            credit_note_aggregate,
            hotel_credit_note,
            updated_invoice_aggregates,
        ) = self.credit_note_generation_service.generate_credit_note(
            bill_aggregate,
            credit_note_data,
            grouped_invoices,
            generate_hotel_credit_note,
            issued_by_type,
            issued_by,
            issued_to_type,
            issued_to,
            hotel_context,
            invoice_aggregates[0].invoice.billed_entity_account,
        )
        self.add_allowance_service.add_allowances_for_credit_note(
            credit_note_aggregate, booking_aggregate, bill_aggregate, grouped_invoices
        )
        self.job_scheduler_service.schedule_credit_note_upload(
            bill_id, [credit_note_aggregate]
        )

        if hotel_credit_note:
            self.credit_note_repository.save(hotel_credit_note)
        self.credit_note_repository.save(credit_note_aggregate)
        self.invoice_repository.update_all(updated_invoice_aggregates)
        self.bill_repository.update(bill_aggregate)
        self.booking_repository.update(booking_aggregate)
        # send integration event
        IntegrationEventApplicationService.create_credit_note_event(
            event_type=IntegrationEventType.CREDIT_NOTE_GENERATED,
            credit_note_aggregates=[credit_note_aggregate],
            invoice_aggregates=updated_invoice_aggregates,
            user_action="generate_credit_note",
        )

        return credit_note_aggregate
