import logging

from object_registry import register_instance
from prometheus.application.decorators import session_manager
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.billing.aggregates.credit_note_aggregate import (
    CreditNoteAggregate,
)
from prometheus.domain.billing.aggregates.invoice_aggregate import InvoiceAggregate
from prometheus.domain.billing.exceptions import EInvoicingException
from prometheus.domain.billing.repositories import (
    BillRepository,
    CreditNoteRepository,
    InvoiceRepository,
)
from prometheus.domain.billing.services import CreditNoteGenerationService
from prometheus.domain.billing.services.einvoicing_service import EInvoicingService
from prometheus.domain.billing.services.invoice_service import InvoiceService
from ths_common.exceptions import ValidationException

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        InvoiceService,
        CreditNoteGenerationService,
        InvoiceRepository,
        CreditNoteRepository,
        EInvoicingService,
        BillRepository,
        JobSchedulerService,
    ]
)
class EInvoiceApplicationService(object):
    def __init__(
        self,
        invoice_service,
        credit_note_generation_service,
        invoice_repository,
        credit_note_repository,
        einvoicing_service,
        bill_repository,
        job_scheduler_service,
    ):
        self.invoice_service = invoice_service
        self.credit_note_generation_service = credit_note_generation_service
        self.invoice_repository = invoice_repository
        self.credit_note_repository = credit_note_repository
        self.einvoicing_service = einvoicing_service
        self.bill_repository = bill_repository
        self.job_scheduler_service = job_scheduler_service

    def generate_irn_and_report(
        self, invoice_ids=None, credit_note_ids=None, bill_ids=None
    ):
        invoice_aggregates = self.einvoicing_service.get_einvoices_without_irn(
            invoice_ids=invoice_ids, bill_ids=bill_ids
        )
        if invoice_ids and len(invoice_ids) != len(invoice_aggregates):
            raise ValidationException(
                description="Few invoices are not eligible for IRP or already submitted, recheck"
            )

        credit_note_aggregates = self.einvoicing_service.get_ecredit_notes_without_irn(
            credit_note_ids=credit_note_ids, bill_ids=bill_ids
        )
        if credit_note_ids and len(credit_note_ids) != len(credit_note_aggregates):
            raise ValidationException(
                description="Few credit notes are not eligible for IRP or already submitted, recheck"
            )

        return self._generate_irn_and_report(invoice_aggregates, credit_note_aggregates)

    @session_manager(commit=True)
    def _generate_irn_and_report(
        self, invoice_aggregates=None, credit_note_aggregates=None
    ):
        def gen_report_data(aggregate, credit_note=False):
            data = {
                'aggregate_id': aggregate.invoice_id
                if not credit_note
                else aggregate.credit_note_id,
                'irn_generation_status': None,
                'extra_info': None,
            }
            try:
                irp_response = self.einvoicing_service.generate_irn(aggregate)
                if not credit_note:
                    invoice_number = self._get_invoice_number(aggregate)
                    self.einvoicing_service.set_invoice_irp_details(
                        invoice_aggregate=aggregate,
                        irp_details=irp_response,
                        invoice_number=invoice_number,
                    )
                    self.invoice_repository.update(aggregate)
                    bill_aggregate = self.bill_repository.load(
                        bill_id=aggregate.invoice.bill_id
                    )
                    self.job_scheduler_service.schedule_invoice_upload(
                        bill_aggregate, [aggregate], send_invoices_to_guest=False
                    )
                else:
                    credit_note_number = self._get_credit_note_number(aggregate)
                    self.einvoicing_service.set_credit_note_irp_details(
                        credit_note_aggregate=aggregate,
                        irp_details=irp_response,
                        credit_note_number=credit_note_number,
                    )
                    self.credit_note_repository.update(aggregate)
                    self.job_scheduler_service.schedule_credit_note_upload(
                        aggregate.bill_id, [aggregate]
                    )
                data['irn_generation_status'] = 'success'
            except EInvoicingException as e:
                data['irn_generation_status'] = 'error'
                data['extra_info'] = str(e)
            return data

        invoice_report_data = (
            [gen_report_data(agg) for agg in invoice_aggregates]
            if invoice_aggregates
            else []
        )
        credit_note_report_data = (
            [gen_report_data(agg, credit_note=True) for agg in credit_note_aggregates]
            if credit_note_aggregates
            else []
        )
        data = invoice_report_data + credit_note_report_data
        return data

    def _get_invoice_number(self, aggregate: InvoiceAggregate):
        if aggregate.invoice_number:
            return None

        invoice_number = self.invoice_service.generate_invoice_number(
            issued_by_type=aggregate.invoice.issued_by_type,
            vendor_id=aggregate.invoice.vendor_id,
            gstin=aggregate.invoice.issued_by.gst_details.gstin_num,
        )
        return invoice_number

    def _get_credit_note_number(self, aggregate: CreditNoteAggregate):
        if aggregate.credit_note.credit_note_number:
            return None

        credit_note_number = (
            self.credit_note_generation_service.generate_credit_note_number(
                issued_by_type=aggregate.credit_note.issued_by_type,
                vendor_id=aggregate.credit_note.vendor_id,
                gstin=aggregate.credit_note.issued_by.gst_details.gstin_num,
            )
        )
        return credit_note_number
