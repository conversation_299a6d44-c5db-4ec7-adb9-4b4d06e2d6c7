from typing import Dict, List

from object_registry import register_instance
from prometheus.application.helpers.billed_entity_helper import (
    get_paid_by_from_billed_entity,
    get_paid_by_from_billed_entity_category,
)
from prometheus.application.hotel_settings.dtos.payment_config_dto import (
    PaymentConfigDto,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.dto import PaymentData
from prometheus.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    PaymentChannels,
    PaymentReceiverTypes,
    PaymentTypes,
)
from ths_common.exceptions import ValidationException


@register_instance(dependencies=[TenantSettings, SlackAlertServiceClient])
class PaymentDataSanitizer:
    def __init__(
        self,
        tenant_settings: TenantSettings,
        slack_alert_client: SlackAlertServiceClient,
    ):
        self.tenant_settings = tenant_settings
        self.slack_alert_client = slack_alert_client

    def sanitize(
        self,
        payments: List[PaymentData],
        bill_aggregate: BillAggregate,
        default_billed_entity_category: BilledEntityCategory,
    ):
        self.sanitize_payment_methods(
            payments, bill_aggregate, default_billed_entity_category
        )

    def sanitize_payment_methods(
        self,
        payments: List[PaymentData],
        bill_aggregate: BillAggregate,
        default_billed_entity_category: BilledEntityCategory,
    ):
        payment_configs: Dict[
            str, Dict[str, PaymentConfigDto]
        ] = self.tenant_settings.get_payment_configs_for_all_allowed_payment_methods()
        if not payment_configs:
            return
        for payment in payments:
            paid_by = self._derive_paid_by_from_payor_billed_entity(
                bill_aggregate, default_billed_entity_category, payment
            )
            config: PaymentConfigDto = payment_configs.get(
                payment.payment_type.value, {}
            ).get(payment.payment_mode)
            input_paid_by, input_paid_to = paid_by, payment.paid_to
            if payment.payment_type == PaymentTypes.REFUND:
                input_paid_by, input_paid_to = input_paid_to, input_paid_by
            if config:
                paid_by, paid_to = self._get_paid_by_and_paid_to_from_config(
                    bill_aggregate, config, input_paid_by, paid_by, payment
                )
            elif payment.payment_channel == PaymentChannels.FRONT_DESK:
                raise ValidationException(ApplicationErrors.INVALID_PAYMENT_METHOD)
            else:
                self.slack_alert_client.record_event('payment_config_missing', payment)
                paid_to = PaymentReceiverTypes.TREEBO
            if payment.payment_type == PaymentTypes.REFUND:
                paid_by, paid_to = paid_to, paid_by
            payment.paid_by, payment.paid_to = paid_by, paid_to

    def _get_paid_by_and_paid_to_from_config(
        self, bill_aggregate, config, input_paid_by, paid_by, payment
    ):
        paid_to = config.paid_to
        if config.should_override_paid_by_inputs:
            paid_by = config.default_paid_by
        elif config.allowed_paid_by and input_paid_by not in config.allowed_paid_by:
            if payment.payment_channel == PaymentChannels.FRONT_DESK:
                raise ValidationException(
                    ApplicationErrors.INVALID_PAYOR_FOR_PAYMENT_METHOD
                )
            paid_by = (
                self._default_paid_by_from_allowed_options(
                    bill_aggregate, config.allowed_paid_by
                )
                or input_paid_by
            )
        return paid_by, paid_to

    @staticmethod
    def _default_paid_by_from_allowed_options(bill_aggregate, allowed_paid_options):
        non_corporate_paid_by_options = {
            PaymentReceiverTypes.GUEST,
            PaymentReceiverTypes.TREEBO,
            PaymentReceiverTypes.HOTEL,
        }.intersection(allowed_paid_options)
        if non_corporate_paid_by_options:
            return non_corporate_paid_by_options.pop()
        if (
            PaymentReceiverTypes.TA in allowed_paid_options
            and bill_aggregate.get_billed_entity_for_category(
                BilledEntityCategory.TRAVEL_AGENT
            )
        ):
            return PaymentReceiverTypes.TA
        if (
            PaymentReceiverTypes.CORPORATE in allowed_paid_options
            and bill_aggregate.get_billed_entity_for_category(
                BilledEntityCategory.BOOKER_COMPANY
            )
        ):
            return PaymentReceiverTypes.CORPORATE
        return None

    @staticmethod
    def _derive_paid_by_from_payor_billed_entity(
        bill_aggregate, default_billed_entity_category, payment
    ):
        if payment.paid_by_billed_entity_id:
            paid_by_billed_entity = bill_aggregate.get_billed_entity(
                payment.paid_by_billed_entity_id
            )
            return get_paid_by_from_billed_entity(
                paid_by_billed_entity, default_billed_entity_category, bill_aggregate
            )
        return get_paid_by_from_billed_entity_category(
            default_billed_entity_category, bill_aggregate
        )
