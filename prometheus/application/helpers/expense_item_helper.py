from object_registry import register_instance
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)


@register_instance(
    dependencies=[
        CatalogServiceClient,
        ExpenseItemRepository,
    ]
)
class ExpenseItemHelper(object):
    def __init__(
        self,
        catalog_service_client,
        expense_item_repository,
    ):
        self.expense_item_repository = expense_item_repository
        self.catalog_service_client = catalog_service_client

    def add_missing_skus_if_sku_not_found(
        self,
        expense_items,
        expenses_data=None,
        rate_plan_inclusions=None,
    ):
        sku_ids, skus = set(), []

        if expenses_data:
            for expense_data in expenses_data:
                expense_item = next(
                    (
                        ex
                        for ex in expense_items
                        if ex.sku_id == expense_data.get('sku_id')
                    ),
                    None,
                )
                if expense_data.get('sku_id') and not expense_item:
                    sku_ids.add(expense_data.get('sku_id'))

        elif rate_plan_inclusions:
            for rate_plan_inclusion in rate_plan_inclusions:
                expense_item = next(
                    (
                        ex
                        for ex in expense_items
                        if ex.sku_id == rate_plan_inclusion.sku_id
                    ),
                    None,
                )
                if rate_plan_inclusion.sku_id and not expense_item:
                    sku_ids.add(rate_plan_inclusion.sku_id)

        if not sku_ids:
            return expense_items

        skus = self.catalog_service_client.get_sku_by_sku_id(codes=sku_ids)
        self.expense_item_repository.save_all(skus)
        expense_items.extend(skus)
        return expense_items
