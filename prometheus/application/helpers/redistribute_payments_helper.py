from treebo_commons.money import Money

from prometheus.application.audit_trail.decorator import audit
from prometheus.application.billing.dtos.invoice_reissue_dto import AccountSummaryDto
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import ChargeStatus


class RedistributePaymentHelper:
    @classmethod
    @audit(audit_type=AuditType.PAYMENT_REDISTRIBUTED)
    def redistribute_payments(
        cls,
        bill_aggregate,
        booked_charges_and_allowances_invoiced=None,
        billed_entity_account_completely_checked_out=None,
        is_last_checkout=True,
        room_stay_charges_to_exclude=None,
        room_stay_allowance_to_exclude=None,
        is_booking_cancelled=False,
    ):
        """
        :param bill_aggregate:
        :param booked_charges_and_allowances_invoiced:
        :param billed_entity_account_completely_checked_out:
        :param is_last_checkout:
        :param room_stay_charges_to_exclude:
        :param room_stay_allowance_to_exclude:
        :param is_booking_cancelled:
        :return billed_entity_wise_balance:
        1. For each billed entity, get all the accounts with respective balance.
        2. While performing this determine if all the account has surplus/deficit.
        3. Discard accounts having zero balance as redistribution of payment should not affect these accounts.
        4. if there is a single account for a billed entity, redistribution will not affect the account and hence add
           that account & balance details to the response
        5. if all the accounts for the billed_entity is having deficit balance, no redistribution should be done and
           hence add all the accounts & balance details to the response
        6. if there are some accounts having surplus & some with deficit, redistribution is done as below approach:
            a. Sort all the accounts on the basis of their balance in reverse order.(Account having highest surplus or
            minimum deficit to appear first)
            b. Loop over the sorted list as perform below:
                1. If the first account is having deficit break the loop as all the accounts will have deficit.
                   Generally, the flow will not reach here. Just a precautionary check.
                2. If the sum of balance for account with highest surplus(A1) and next account in list(A2) is less than
                   zero, this means that the surplus is lesser then the deficit amount in next account and hence,
                   transfer all surplus from A1 to A2. Break the iteration as all the following accounts will have
                   deficit.
                3. If the sum of balance for account with highest surplus(A1) and next account in list(A2) is not zero,
                   and if A2 balance is less than zero, this means the surplus in A1 is more than the deficit of A2.
                   Transfer the deficit amount from A1 to A2 to settle the balance of A2 to zero.
                4. If the sum of balance for account with highest surplus(A1) and next account in list(A2) is not zero,
                   and if A2 balance is greater than zero, this means the both A1 & A2 have surplus.
                   Transfer the surplus amount from A2 to A1 to settle the balance of A2 to zero.
        7. Re-fetch all the account balance after redistribution and add them to the response
        """
        billed_entity_wise_balance = []
        billed_entities = bill_aggregate.billed_entities

        (
            booked_charges_invoiced,
            booked_allowances_invoiced,
        ) = cls()._determine_booked_charge_and_allowance_to_invoice(
            bill_aggregate, booked_charges_and_allowances_invoiced, is_last_checkout
        )
        (
            account_wise_balances,
            is_all_account_having_deficit_balance,
            is_all_account_having_surplus_balance,
        ) = cls._get_balance_per_billed_entity_account(
            bill_aggregate,
            billed_entities,
            list(set(booked_charges_invoiced) - set(room_stay_charges_to_exclude))
            if room_stay_charges_to_exclude
            else booked_charges_invoiced,
            [
                item
                for item in booked_allowances_invoiced
                if item not in room_stay_allowance_to_exclude
            ]
            if room_stay_allowance_to_exclude
            else booked_allowances_invoiced,
            billed_entity_account_completely_checked_out,
            is_last_checkout,
        )

        entity_accounts_summary = set()
        if len(account_wise_balances) == 1:
            billed_entity_account = list(account_wise_balances.keys())[0]
            billed_entity_id = billed_entity_account.billed_entity_id
            account_number = billed_entity_account.account_number
            entity_accounts_summary.add(
                AccountSummaryDto(
                    billed_entity_account=billed_entity_account,
                    accounts_summary=bill_aggregate.get_account_summary(
                        billed_entity_account,
                        invoiced_booked_charges=booked_charges_invoiced,
                        invoiced_booked_allowances=booked_allowances_invoiced,
                    ),
                )
            )
            billed_entity_wise_balance.append(
                dict(
                    billed_entity_id=billed_entity_id,
                    entity_account_summary=list(entity_accounts_summary),
                )
            )
            return billed_entity_wise_balance

        if not account_wise_balances:
            return billed_entity_wise_balance

        if is_all_account_having_deficit_balance:
            billed_entity_ids = set(
                awb.billed_entity_id for awb in account_wise_balances
            )
            for billed_entity_id in billed_entity_ids:
                billed_entity_wise_balance.append(
                    dict(
                        billed_entity_id=billed_entity_id,
                        entity_account_summary=list(
                            cls._get_account_summary_for_billed_entity(
                                bill_aggregate,
                                bill_aggregate.get_billed_entity(billed_entity_id),
                                booked_charges_invoiced,
                                booked_allowances_invoiced,
                            )
                        ),
                    )
                )
            return billed_entity_wise_balance

        redistributed_folio_details = cls().redistribute_payments_across_all_accounts(
            bill_aggregate,
            account_wise_balances,
            billed_entity_account_completely_checked_out,
            is_last_checkout,
            is_booking_cancelled,
        )

        folio_bea = [
            BilledEntityAccountVO(
                billed_entity_id=rfd['billed_entity_id'],
                account_number=rfd['account_number'],
            )
            for rfd in redistributed_folio_details
        ]
        for billed_entity in billed_entities:
            entity_accounts_summary = []
            for account in billed_entity.accounts:
                bea = BilledEntityAccountVO(
                    billed_entity.billed_entity_id, account.account_number
                )
                if (
                    BilledEntityAccountVO(
                        billed_entity_id=billed_entity.billed_entity_id,
                        account_number=account.account_number,
                    )
                    in folio_bea
                ):
                    entity_accounts_summary.append(
                        AccountSummaryDto(
                            billed_entity_account=bea,
                            accounts_summary=bill_aggregate.get_account_summary(
                                bea,
                                invoiced_booked_charges=booked_charges_invoiced,
                                invoiced_booked_allowances=booked_allowances_invoiced,
                            ),
                        )
                    )
            if entity_accounts_summary:
                billed_entity_wise_balance.append(
                    dict(
                        billed_entity_id=billed_entity.billed_entity_id,
                        entity_account_summary=list(entity_accounts_summary),
                    )
                )
        return billed_entity_wise_balance

    @classmethod
    def _get_balance_per_billed_entity_account(
        cls,
        bill_aggregate,
        billed_entities,
        booked_charges_invoiced,
        booked_allowances_invoiced,
        billed_entity_account_completely_checked_out,
        is_last_checkout,
    ):
        account_wise_balances = dict()
        is_all_account_having_deficit_balance = True
        is_all_account_having_surplus_balance = True
        for billed_entity in billed_entities:
            for account in billed_entity.accounts:
                if not account.locked:
                    bea = BilledEntityAccountVO(
                        billed_entity.billed_entity_id, account.account_number
                    )
                    if (
                        not is_last_checkout
                        and bea not in billed_entity_account_completely_checked_out
                        or []
                    ):
                        net_balance = cls._calculate_net_balance(
                            bill_aggregate,
                            bea,
                            booked_charges_invoiced,
                            booked_allowances_invoiced,
                        )
                    else:
                        net_balance = bill_aggregate.get_net_balance(
                            billed_entity_account=bea,
                            include_booked_charge_ids=booked_charges_invoiced,
                            include_booked_allowances=booked_allowances_invoiced,
                        )
                    if net_balance == 0:
                        continue
                    if net_balance < 0:
                        is_all_account_having_deficit_balance = False
                    if net_balance > 0:
                        is_all_account_having_surplus_balance = False
                    account_wise_balances[bea] = net_balance

        return (
            account_wise_balances,
            is_all_account_having_deficit_balance,
            is_all_account_having_surplus_balance,
        )

    @staticmethod
    def _get_account_summary_for_billed_entity(
        bill_aggregate,
        billed_entity,
        booked_charges_invoiced,
        booked_allowances_invoiced,
    ):
        entity_accounts_summary = set()
        for account in billed_entity.accounts:
            bea = BilledEntityAccountVO(
                billed_entity.billed_entity_id, account.account_number
            )
            entity_accounts_summary.add(
                AccountSummaryDto(
                    billed_entity_account=bea,
                    accounts_summary=bill_aggregate.get_account_summary(
                        bea,
                        invoiced_booked_charges=booked_charges_invoiced,
                        invoiced_booked_allowances=booked_allowances_invoiced,
                    ),
                )
            )
        return entity_accounts_summary

    def redistribute_payments_across_all_accounts(
        self,
        bill_aggregate,
        account_wise_balances,
        billed_entity_account_completely_checked_out=None,
        is_last_checkout=True,
        is_booking_cancelled=False,
    ):
        # Flatten account_wise_balances to a list of dictionaries with billed_entity_id, account_number, and balance
        flattened_balances = [
            {
                'billed_entity_id': be.billed_entity_id,
                'account_number': be.account_number,
                'balance': balance,
            }
            for be, balance in account_wise_balances.items()
        ]

        # Sort the balances in descending order (highest surplus or lowest deficit first)
        if not billed_entity_account_completely_checked_out:
            sorted_balances = sorted(
                flattened_balances, key=lambda x: x['balance'], reverse=False
            )
        else:
            sorted_balances = self._get_sorted_balance_based_on_priority(
                flattened_balances, billed_entity_account_completely_checked_out
            )
        can_amount_be_moved, folio_with_maximum_balance = True, sorted_balances[0]
        for i in range(len(sorted_balances) - 1):
            if sorted_balances[i]['balance'] >= 0 and not can_amount_be_moved:
                break

            surplus_account = sorted_balances[i]
            for j in range(i + 1, len(sorted_balances)):
                deficit_account = sorted_balances[j]

                if surplus_account['balance'] + deficit_account['balance'] >= 0:
                    # Transfer surplus from surplus_account to deficit_account
                    bill_aggregate.transfer_surplus_payment_to_different_account(
                        from_billed_entity_account=BilledEntityAccountVO(
                            billed_entity_id=surplus_account['billed_entity_id'],
                            account_number=surplus_account['account_number'],
                        ),
                        to_billed_entity_account=BilledEntityAccountVO(
                            billed_entity_id=deficit_account['billed_entity_id'],
                            account_number=deficit_account['account_number'],
                        ),
                        amount=abs(surplus_account['balance']),
                    )

                    deficit_account['balance'] += surplus_account['balance']
                    surplus_account['balance'] = 0
                    can_amount_be_moved = False
                    break

                elif surplus_account['balance'] + deficit_account['balance'] != 0:
                    # Transfer deficit from surplus_account to deficit_account
                    bill_aggregate.transfer_surplus_payment_to_different_account(
                        from_billed_entity_account=BilledEntityAccountVO(
                            billed_entity_id=surplus_account['billed_entity_id'],
                            account_number=surplus_account['account_number'],
                        ),
                        to_billed_entity_account=BilledEntityAccountVO(
                            billed_entity_id=deficit_account['billed_entity_id'],
                            account_number=deficit_account['account_number'],
                        ),
                        amount=abs(surplus_account['balance']),
                    )

                    deficit_account['balance'] += surplus_account['balance']
                    surplus_account['balance'] = 0
                    break

        # for partial checkout cases, if the amount in last folio is excess after redistribute, send it back to folio
        if (
            not is_last_checkout and billed_entity_account_completely_checked_out
        ) or is_booking_cancelled:
            for b in sorted_balances:
                is_balance_negative = b['balance'] < 0
                is_account_checked_out = (
                    BilledEntityAccountVO(
                        billed_entity_id=b['billed_entity_id'],
                        account_number=b['account_number'],
                    )
                    in billed_entity_account_completely_checked_out
                )

                if is_balance_negative and is_account_checked_out:
                    from_account = BilledEntityAccountVO(
                        billed_entity_id=b['billed_entity_id'],
                        account_number=b['account_number'],
                    )
                    to_account = BilledEntityAccountVO(
                        billed_entity_id=folio_with_maximum_balance['billed_entity_id'],
                        account_number=folio_with_maximum_balance['account_number'],
                    )

                    bill_aggregate.transfer_surplus_payment_to_different_account(
                        from_billed_entity_account=from_account,
                        to_billed_entity_account=to_account,
                        amount=abs(b['balance']),
                    )

        # Update the original account_wise_balances with the redistributed balances
        for balance_info in sorted_balances:
            billed_entity_id = balance_info['billed_entity_id']
            account_number = balance_info['account_number']
            balance = balance_info['balance']

            account_wise_balances[(billed_entity_id, account_number)] = balance
        return sorted_balances

    def _determine_booked_charge_and_allowance_to_invoice(
        self, bill_aggregate, booked_charges_and_allowances_invoiced, is_last_checkout
    ):
        if is_last_checkout:
            if booked_charges_and_allowances_invoiced:
                booked_charges_to_post = booked_charges_and_allowances_invoiced.get(
                    'booked_charges_to_post'
                )
                booked_allowances_to_post = booked_charges_and_allowances_invoiced.get(
                    'booked_allowances_to_post'
                )
            else:
                booked_charges_to_post = self._get_booked_charges(bill_aggregate)
                booked_allowances_to_post = self._get_booked_allowance(bill_aggregate)
        else:
            booked_charges_to_post = self._get_filtered_booked_charges(
                bill_aggregate, booked_charges_and_allowances_invoiced
            )
            booked_allowances_to_post = self._get_filtered_booked_allowances(
                bill_aggregate, booked_charges_and_allowances_invoiced
            )

        return booked_charges_to_post, booked_allowances_to_post

    def _get_filtered_booked_charges(
        self, bill_aggregate, booked_charges_and_allowances_invoiced
    ):
        return list(
            set(self._get_booked_charges(bill_aggregate))
            - set(
                booked_charges_and_allowances_invoiced.get(
                    'booked_charge_to_exclude', []
                )
            )
        )

    def _get_filtered_booked_allowances(
        self, bill_aggregate, booked_charges_and_allowances_invoiced
    ):
        booked_allowances = self._get_booked_allowance(bill_aggregate)
        exclude_list = booked_charges_and_allowances_invoiced.get(
            'booked_allowances_to_exclude', []
        )
        return (
            [item for item in booked_allowances if item not in exclude_list]
            if exclude_list
            else booked_allowances
        )

    @staticmethod
    def _get_booked_charges(bill_aggregate):
        return [
            c.charge_id
            for c in bill_aggregate.charges
            if c.status in [ChargeStatus.CREATED, ChargeStatus.PREVIEW]
        ]

    @staticmethod
    def _get_booked_allowance(bill_aggregate):
        booked_allowances = []
        for c in bill_aggregate.charges:
            for cs in c.charge_splits:
                for a in cs.allowances or []:
                    if (
                        c.status == ChargeStatus.CONSUMED
                        and a.status == ChargeStatus.CREATED
                    ):
                        allowance = {
                            'charge_id': c.charge_id,
                            'charge_split_id': cs.charge_split_id,
                            'allowance_id': a.allowance_id,
                        }
                        booked_allowances.append(allowance)
        return booked_allowances

    def _get_sorted_balance_based_on_priority(
        self, flattened_balances, bea_completely_checked_out
    ):
        negative_balances = [
            balance for balance in flattened_balances if balance['balance'] < 0
        ]

        positive_balances = [
            fb
            for fb in flattened_balances
            if fb['balance'] > 0
            and self.bea_checked_out(bea_completely_checked_out, fb)
        ]

        remaining_balances = [
            fb
            for fb in flattened_balances
            if fb['balance'] > 0
            and not self.bea_checked_out(bea_completely_checked_out, fb)
        ]

        sorted_balances = (
            sorted(negative_balances, key=lambda x: x['balance'], reverse=False)
            + sorted(positive_balances, key=lambda x: x['balance'], reverse=False)
            + sorted(remaining_balances, key=lambda x: x['balance'], reverse=False)
        )

        return sorted_balances

    @staticmethod
    def bea_checked_out(billed_entity_account_completely_checked_out, fb):
        bea = BilledEntityAccountVO(
            billed_entity_id=fb['billed_entity_id'], account_number=fb['account_number']
        )
        return bea in billed_entity_account_completely_checked_out

    @staticmethod
    def _calculate_net_balance(
        bill_aggregate, bea, booked_charges_invoiced, booked_allowances_invoiced
    ):
        net_paid_amount = bill_aggregate.get_net_paid_amount(bea)
        total_charge_to_be_consumed = Money(0, bill_aggregate.bill.base_currency)

        for c in bill_aggregate.charges:
            if (
                c.status not in [ChargeStatus.CONSUMED, ChargeStatus.CANCELLED]
                and c.charge_id in booked_charges_invoiced
            ):
                for cs in c.charge_splits:
                    if cs.billed_entity_account == bea:
                        total_charge_to_be_consumed += cs.post_tax

            if c.status == ChargeStatus.CONSUMED:
                for cs in c.charge_splits:
                    for a in cs.allowances or []:
                        allowance = {
                            'charge_id': c.charge_id,
                            'charge_split_id': cs.charge_split_id,
                            'allowance_id': a.allowance_id,
                        }
                        if allowance in booked_allowances_invoiced:
                            total_charge_to_be_consumed -= total_charge_to_be_consumed
        return total_charge_to_be_consumed - net_paid_amount
