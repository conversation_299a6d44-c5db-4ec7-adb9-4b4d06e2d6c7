from treebo_commons.utils import dateutils

from prometheus.config.treebo_config import TreeboConfig
from prometheus.domain.catalog.dtos.hotel_dto import HotelDto
from prometheus.infrastructure.external_clients.core.constants import (
    RESELLER_CHANNEL_TENANT_CONFIG_KEY,
)
from ths_common.constants.billing_constants import InvoiceStatus, IssuedByType
from ths_common.constants.booking_constants import BookingChannels, ProfileTypes
from ths_common.constants.catalog_constants import SellerType
from ths_common.exceptions import ResourceNotFound
from ths_common.value_objects import InvoiceIssuedByInfo


class InvoiceIssuerService(object):
    @staticmethod
    def get_issuer(
        issued_by_type,
        vendor_details,
        reseller_gst_aggregate,
        catalog_service_client=None,
    ):
        if issued_by_type == IssuedByType.HOTEL:
            issued_by = InvoiceIssuedByInfo(
                gst_details=vendor_details.gst_details,
                phone=vendor_details.phone,
                email=vendor_details.email,
                url=vendor_details.url,
                legal_signature=vendor_details.legal_signature,
            )
        else:
            if not reseller_gst_aggregate:
                raise ResourceNotFound(
                    "ResellerGST",
                    description="Reseller GST not configured for hotel: {0}".format(
                        vendor_details.hotel_id
                    ),
                )
            issued_by = InvoiceIssuedByInfo(
                gst_details=reseller_gst_aggregate.gst_details,
                phone=TreeboConfig.TREEBO_PHONE_NUMBER,
                email=TreeboConfig.TREEBO_EMAIL_ID,
                url=TreeboConfig.TREEBO_SUPPORT_URL,
                legal_signature=TreeboConfig.TREEBO_LEGAL_SIGNATURE,
            )
        if not catalog_service_client:
            return issued_by
        catalog_hotel_response = catalog_service_client.fetch_hotel(
            vendor_details.hotel_id
        )
        hotel_dto = HotelDto.create_from_catalog_data(catalog_hotel_response)
        if (
            vendor_details.gst_details.gstin_num == hotel_dto.gstin_num
            or vendor_details.gst_details.gstin_num is None
        ):
            issued_by.bank_details = hotel_dto.bank_details

        return issued_by

    @staticmethod
    def get_issuer_type_for_new_invoice(
        bill_id,
        hotel_id,
        invoice_repository,
        catalog_service_client,
        booking_aggregate,
        tenant_settings,
    ):
        is_first_invoice = False
        # TODO: Optimise to just load first confirmed invoice 'issued_by_type' field.
        confirmed_invoices = invoice_repository.load_for_bill_id(
            bill_id,
            exclude_statuses={
                InvoiceStatus.PREVIEW.value,
                InvoiceStatus.CANCELLED.value,
            },
        )
        if not confirmed_invoices:
            is_first_invoice = True
            seller_type = InvoiceIssuerService.get_seller_model_for_new_booking(
                hotel_id,
                catalog_service_client,
                booking_aggregate.booking.source.channel_code,
                booking_aggregate.get_booking_owner().profile_type,
                booking_aggregate.booking_owner_gstin(),
                tenant_settings,
            )
            return (
                IssuedByType.RESELLER
                if seller_type == SellerType.RESELLER
                else IssuedByType.HOTEL,
                is_first_invoice,
            )

        first_invoice = confirmed_invoices[0].invoice
        return first_invoice.issued_by_type, is_first_invoice

    @staticmethod
    def get_seller_model_for_new_booking(
        hotel_id,
        catalog_service_client,
        booking_channel,
        booking_owner_profile_type,
        booking_owner_gstin,
        tenant_settings,
    ):
        reseller_channels = tenant_settings.get_setting_value(
            RESELLER_CHANNEL_TENANT_CONFIG_KEY, hotel_id
        )

        if booking_owner_profile_type == ProfileTypes.SME and booking_owner_gstin:
            return SellerType.RESELLER

        if not reseller_channels or booking_channel not in reseller_channels:
            return SellerType.MARKETPLACE

        seller_type_response = catalog_service_client.get_seller_type(
            hotel_id, dateutils.current_date()
        )
        return (
            SellerType(seller_type_response.get("seller_type"))
            if seller_type_response.get("seller_type")
            else SellerType.MARKETPLACE
        )

    @staticmethod
    def get_seller_model(invoice_issuer_type):
        return (
            SellerType.RESELLER
            if invoice_issuer_type == IssuedByType.RESELLER
            else SellerType.MARKETPLACE
        )
