from prometheus.common.error_codes.errors import ApplicationErrors
from ths_common.constants.billing_constants import ChargeBillToTypes
from ths_common.exceptions import ValidationException
from ths_common.value_objects import InvoiceChargeToInfo


class InvoiceBillToService(object):
    @staticmethod
    def derive_bill_to_id(group_key, booking_aggregate, user_info_map):
        bill_to_type = ChargeBillToTypes(group_key.bill_to_type)
        if bill_to_type == ChargeBillToTypes.COMPANY:
            bill_to_id = booking_aggregate.booking.owner_id
        elif bill_to_type == ChargeBillToTypes.GUEST:
            # Bill to group key user, or any one guest of the invoice
            bill_to_id = group_key.user if group_key.user else min(list(user_info_map))
        else:
            raise ValidationException(ApplicationErrors.BILL_TO_TYPE_NOT_SUPPORTED)
        return bill_to_id

    @staticmethod
    def compute_charge_to_info(charge_map, bill_aggregate, booking_aggregate):
        user_ids = bill_aggregate.get_assigned_users_for_charges(charge_map)

        user_info_map = dict()
        for user_id in user_ids:
            user = booking_aggregate.get_customer(user_id)
            is_booking_owner = booking_aggregate.is_booking_owner(user_id)
            user_info_map[user_id] = InvoiceChargeToInfo(
                customer_id=user_id,
                name=user.name,
                is_booking_owner=is_booking_owner,
                is_primary=user.is_primary,
                dummy=user.dummy,
            )
        return user_info_map
