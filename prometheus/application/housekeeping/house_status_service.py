from decimal import Decimal
from typing import List

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit_housekeeping
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.dtos.house_status_statistics_dto import (
    HouseStatusStatisticsDto,
    TotalRoomsAndGuestsDto,
)
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.booking.repositories.house_status_repository import (
    HouseStatusRepository,
)
from prometheus.domain.catalog.repositories import (
    HotelRepository,
    RoomRepository,
    RoomTypeRepository,
)
from prometheus.domain.inventory.dtos.house_status_dto import HouseStatusDto
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.catalog_constants import RoomStatus as CatalogRoomStatus
from ths_common.constants.inventory_constants import (
    HouseKeepingStatus,
    RoomReservationStatus,
    RoomStatus,
)
from ths_common.exceptions import ValidationException
from ths_common.utils.collectionutils import flatten_list


@register_instance(
    dependencies=[
        HotelRepository,
        BookingRepository,
        BillRepository,
        RoomAllotmentRepository,
        DNRRepository,
        HouseStatusRepository,
        RoomTypeRepository,
        RoomRepository,
    ]
)
class HouseStatusService:
    def __init__(
        self,
        hotel_repository,
        booking_repository,
        bill_repository,
        room_allotment_repository,
        dnr_repository,
        house_status_repository,
        room_type_repository,
        room_repository,
    ):
        self.hotel_repository = hotel_repository
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.room_allotment_repository = room_allotment_repository
        self.dnr_repository = dnr_repository
        self.house_status_repository = house_status_repository
        self.room_type_repository = room_type_repository
        self.room_repository = room_repository

    def _set_hotel_context(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        return crs_context.set_hotel_context(hotel_aggregate)

    @audit_housekeeping(action_performed="set_house_status_on_night_audit")
    def set_house_status(self, hotel_id, business_date):
        grouped_room_allotment_aggregates = (
            self.room_allotment_repository.load_multiple(
                hotel_id, skip_allotments=True, for_update=True
            )
        )

        self._update_housekeeping_status_after_night_audit(
            hotel_id, business_date, grouped_room_allotment_aggregates
        )

        all_room_ids = grouped_room_allotment_aggregates.keys()

        due_in_room_allocations = (
            self.booking_repository.load_room_allocations_for_reservation_status_type(
                reservation_status=RoomReservationStatus.DUE_IN,
                room_ids=all_room_ids,
                business_date=business_date,
            )
        )

        due_out_room_allocations = (
            self.booking_repository.load_room_allocations_for_reservation_status_type(
                reservation_status=RoomReservationStatus.DUE_OUT,
                room_ids=all_room_ids,
                business_date=business_date,
            )
        )

        stayover_room_allocations = (
            self.booking_repository.load_room_allocations_for_reservation_status_type(
                reservation_status=RoomReservationStatus.STAY_OVER,
                room_ids=all_room_ids,
                business_date=business_date,
            )
        )

        # this method used to set status after night audit and arrival/departed status is not possible at that time
        for (
            room_id,
            room_allotment_aggregate,
        ) in grouped_room_allotment_aggregates.items():
            room_inventory = room_allotment_aggregate.room_inventory
            reservation_statuses = set()

            if room_inventory.room_id in due_in_room_allocations:
                reservation_statuses.add(RoomReservationStatus.DUE_IN)

            if room_inventory.room_id in due_out_room_allocations:
                reservation_statuses.add(RoomReservationStatus.DUE_OUT)

            if room_inventory.room_id in stayover_room_allocations:
                reservation_statuses.add(RoomReservationStatus.STAY_OVER)

            if not reservation_statuses:
                reservation_statuses.add(RoomReservationStatus.NO_RESERVATION)

            room_inventory.update_reservation_statuses(reservation_statuses)

        self.room_allotment_repository.update_all(
            grouped_room_allotment_aggregates.values()
        )

    def set_house_status_for_rooms(self, hotel_id, room_ids, business_date=None):
        if business_date is None:
            business_date = self.hotel_repository.fetch_current_business_date(hotel_id)

        grouped_room_allotment_aggregates = (
            self.room_allotment_repository.load_multiple(
                hotel_id, skip_allotments=True, for_update=True, room_ids=room_ids
            )
        )

        all_room_ids = grouped_room_allotment_aggregates.keys()

        due_in_room_allocations = (
            self.booking_repository.load_room_allocations_for_reservation_status_type(
                reservation_status=RoomReservationStatus.DUE_IN,
                room_ids=all_room_ids,
                business_date=business_date,
            )
        )

        due_out_room_allocations = (
            self.booking_repository.load_room_allocations_for_reservation_status_type(
                reservation_status=RoomReservationStatus.DUE_OUT,
                room_ids=all_room_ids,
                business_date=business_date,
            )
        )

        stayover_room_allocations = (
            self.booking_repository.load_room_allocations_for_reservation_status_type(
                reservation_status=RoomReservationStatus.STAY_OVER,
                room_ids=all_room_ids,
                business_date=business_date,
            )
        )

        arrived_room_allocations = (
            self.booking_repository.load_room_allocations_for_reservation_status_type(
                reservation_status=RoomReservationStatus.ARRIVED,
                room_ids=all_room_ids,
                business_date=business_date,
            )
        )

        departed_room_allocations = (
            self.booking_repository.load_room_allocations_for_reservation_status_type(
                reservation_status=RoomReservationStatus.DEPARTED,
                room_ids=all_room_ids,
                business_date=business_date,
            )
        )

        for (
            room_id,
            room_allotment_aggregate,
        ) in grouped_room_allotment_aggregates.items():
            room_inventory = room_allotment_aggregate.room_inventory
            reservation_statuses = set()

            if room_inventory.room_id in due_in_room_allocations:
                reservation_statuses.add(RoomReservationStatus.DUE_IN)

            if room_inventory.room_id in arrived_room_allocations:
                reservation_statuses.add(RoomReservationStatus.ARRIVED)

            if room_inventory.room_id in due_out_room_allocations:
                reservation_statuses.add(RoomReservationStatus.DUE_OUT)

            if room_inventory.room_id in departed_room_allocations:
                reservation_statuses.add(RoomReservationStatus.DEPARTED)

            if room_inventory.room_id in stayover_room_allocations:
                reservation_statuses.add(RoomReservationStatus.STAY_OVER)

            if not reservation_statuses:
                reservation_statuses.add(RoomReservationStatus.NO_RESERVATION)

            room_inventory.update_reservation_statuses(reservation_statuses)

        self.room_allotment_repository.update_all(
            grouped_room_allotment_aggregates.values()
        )

    def _update_housekeeping_status_after_night_audit(
        self, hotel_id, business_date, grouped_room_allotment_aggregates
    ):
        # Once this room is checked out, check if there is a DNR starting current business date. In which case,
        # HK Status should be marked as Out Of Order
        starting_dnr_room_ids = (
            self.dnr_repository.get_rooms_with_dnr_starting_from_given_date(
                hotel_id, business_date
            )
        )
        ending_dnr_room_ids = (
            self.dnr_repository.get_rooms_with_dnr_ending_on_given_date(
                hotel_id, business_date
            )
        )

        for (
            room_id,
            room_allotment_aggregate,
        ) in grouped_room_allotment_aggregates.items():
            if not room_allotment_aggregate.is_occupied():
                if (
                    room_id in ending_dnr_room_ids
                    and room_id not in starting_dnr_room_ids
                ):
                    room_allotment_aggregate.update_housekeeping_record(
                        housekeeping_status=HouseKeepingStatus.DIRTY,
                        remarks="Room Out Of Order duration ended after Night Audit",
                    )

                elif (
                    room_id in starting_dnr_room_ids
                    and room_id not in ending_dnr_room_ids
                ):
                    room_allotment_aggregate.update_housekeeping_record(
                        housekeeping_status=HouseKeepingStatus.OUT_OF_ORDER,
                        remarks="Room Moved to Out Of Order on Night Audit",
                    )

                else:
                    continue

            else:
                room_allotment_aggregate.update_housekeeping_record(
                    housekeeping_status=HouseKeepingStatus.DIRTY,
                    remarks="Occupied Room marked Dirty after Night Audit",
                )

    def get_house_status(self, hotel_id) -> List[HouseStatusDto]:
        house_account_room_type_id = (
            self.room_type_repository.get_house_accounts_room_type_id()
        )

        return self.room_allotment_repository.get_house_status(
            hotel_id=hotel_id,
            exclude_room_type_id=house_account_room_type_id,
            exclude_inactive_rooms=True,
        )

    def generate_house_statistics(
        self, hotel_id, business_date=None
    ) -> HouseStatusStatisticsDto:
        # available_rooms -> All Room with Current Status as Vacant (minus) DNR Rooms
        # occupied rooms -> All rooms with current status as occupied

        # ----- Below fields can be stored date wise
        # early departures -> All room stay's room, which checked out, at least 1 business day earlier than expected
        # day use rooms -> All room stay's room, whose checkin and checkout are same
        # day of arrival cancellations -> All rooms stays which got cancelled on checkin date

        # ----- Below fields are EOD Projections. Again can store in DB, and keep on refreshing, if date based query
        # ----- is complex
        # rooms/guests/vips in individual bookings -> reserved bookings room & guest count
        # rooms/guests/vips in grp bookings -> reserved bookings room & guest count, on bookings having group_name
        # occupancy -> (total_rooms - available_inventory - dnr_inventory) / (total_rooms - dnr_inventory)
        # total room revenue -> sum of all room (and related) charges. HSN Code match
        # arr -> total_room_revenue / (total_rooms - available_inventory - dnr_inventory)
        hotel_context = self._set_hotel_context(hotel_id)
        current_business_date = hotel_context.current_date()
        base_currency = hotel_context.base_currency
        house_account_room_type_id = (
            self.room_type_repository.get_house_accounts_room_type_id()
        )

        if not business_date:
            business_date = current_business_date
        elif business_date > current_business_date:
            raise ValidationException(
                error=ApplicationErrors.FUTURE_DATE_STATISTICS_NOT_ALLOWED
            )
        elif business_date < current_business_date:
            return self.house_status_repository.load_house_statistics(
                hotel_id, base_currency, business_date
            )

        all_room_ids = self.room_repository.fetch_room_ids_for_hotel(
            hotel_id,
            statuses=[CatalogRoomStatus.ACTIVE.value],
            exclude_room_type_id=house_account_room_type_id,
        )
        status_wise_room_ids = (
            self.room_allotment_repository.get_rooms_grouped_by_current_status(
                hotel_id, business_date, exclude_room_type_id=house_account_room_type_id
            )
        )

        all_room_current_statuses = set(
            list(status_wise_room_ids[RoomStatus.AVAILABLE.value])
            + list(status_wise_room_ids[RoomStatus.BUSY.value])
            + list(status_wise_room_ids[RoomStatus.DNR.value])
        )

        if set(all_room_ids) - set(all_room_current_statuses):
            # Some rooms haven't been saved in RoomInventoryAvailability. Refresh the table as per current status
            self.refresh_availability_status_for_current_business_date(hotel_id)

            # Re-load current status
            status_wise_room_ids = (
                self.room_allotment_repository.get_rooms_grouped_by_current_status(
                    hotel_id,
                    business_date,
                    exclude_room_type_id=house_account_room_type_id,
                )
            )

        available_rooms = len(status_wise_room_ids[RoomStatus.AVAILABLE.value])
        occupied_rooms = len(status_wise_room_ids[RoomStatus.BUSY.value])
        dnr_rooms = len(status_wise_room_ids[RoomStatus.DNR.value])

        day_of_arrival_cancellations = self.booking_repository.get_total_day_of_arrival_cancellations_for_given_business_date(
            hotel_id, business_date, exclude_room_type_id=house_account_room_type_id
        )

        total_rooms = (
            self.booking_repository.get_total_rooms_for_group_and_individual_bookings(
                hotel_id, business_date, exclude_room_type_id=house_account_room_type_id
            )
        )
        total_guests = self.booking_repository.get_total_guests_and_vip_guests_for_group_and_individual_bookings(
            hotel_id, business_date, exclude_room_type_id=house_account_room_type_id
        )

        total_rooms_and_guests_under_group_booking = TotalRoomsAndGuestsDto(
            total_rooms.get('group_booking'),
            total_guests.get('group_booking'),
            total_guests.get('group_booking_vip'),
        )

        total_rooms_and_guests_under_individual_booking = TotalRoomsAndGuestsDto(
            total_rooms.get('individual_booking'),
            total_guests.get('individual_booking'),
            total_guests.get('individual_booking_vip'),
        )

        early_departures = (
            self.booking_repository.get_total_early_departures_on_given_business_date(
                hotel_id, business_date, exclude_room_type_id=house_account_room_type_id
            )
        )

        day_use_rooms = (
            self.booking_repository.get_total_day_use_room_stays_on_given_business_date(
                hotel_id, business_date, exclude_room_type_id=house_account_room_type_id
            )
        )

        total_room_revenue = (
            self.bill_repository.get_total_room_revenue_projected_for_business_date(
                hotel_id, business_date
            )
        )

        total_room_revenue = Money(total_room_revenue, CurrencyType(base_currency))
        total_projected_reservation_at_eod = total_rooms.get(
            'group_booking'
        ) + total_rooms.get('individual_booking')

        eod_projected_dnr_room_ids = self.dnr_repository.get_rooms_with_dnr_on_date(
            hotel_id, business_date
        )
        total_rooms = len(flatten_list(status_wise_room_ids.values()))
        eod_projected_rooms_available_for_sale = total_rooms - len(
            eod_projected_dnr_room_ids
        )

        if total_projected_reservation_at_eod == 0:
            occupancy = Decimal("0")
        else:
            occupancy = (
                Decimal(total_projected_reservation_at_eod)
                / Decimal(eod_projected_rooms_available_for_sale)
                * 100
            )
            occupancy = occupancy.quantize(Decimal("0.01"))  # ROUND to 2 decimal places

        if total_projected_reservation_at_eod != 0:
            arr = total_room_revenue / total_projected_reservation_at_eod
        else:
            arr = Money("0", CurrencyType(base_currency))

        return HouseStatusStatisticsDto(
            business_date=business_date,
            available_rooms=available_rooms,
            occupied_rooms=occupied_rooms,
            dnr_rooms=dnr_rooms,
            early_departures=early_departures,
            day_use_rooms=day_use_rooms,
            day_of_arrival_cancellations=day_of_arrival_cancellations,
            total_rooms_and_guests_under_group_booking=total_rooms_and_guests_under_group_booking,
            total_rooms_and_guests_under_individual_booking=total_rooms_and_guests_under_individual_booking,
            total_rooms=total_rooms,
            eod_projected_rooms_available_for_sale=eod_projected_rooms_available_for_sale,
            total_eod_projected_dnr_rooms=len(eod_projected_dnr_room_ids),
            occupancy=occupancy,
            total_room_revenue=total_room_revenue,
            arr=arr,
        )

    @set_hotel_context()
    def get_house_status_arrival_departure(
        self, hotel_id, business_date=None, hotel_aggregate=None
    ):
        if not hotel_aggregate:
            self._set_hotel_context(hotel_id)

        hotel_context = crs_context.get_hotel_context()

        if not business_date:
            business_date = hotel_context.current_date()

        house_account_room_type_id = (
            self.room_type_repository.get_house_accounts_room_type_id()
        )

        arrival_booking_aggregates = (
            self.booking_repository.get_arrival_bookings_on_given_business_date(
                hotel_id, business_date, exclude_room_type_id=house_account_room_type_id
            )
        )

        min_actual_checkout_date = dateutils.datetime_at_given_time(
            business_date, hotel_context.switch_over_time
        )
        max_actual_checkout_date = dateutils.datetime_at_given_time(
            dateutils.add(business_date, days=1), hotel_context.switch_over_time
        )

        departure_booking_aggregates = (
            self.booking_repository.get_departure_bookings_on_given_business_date(
                hotel_id,
                business_date,
                min_actual_checkout_date,
                max_actual_checkout_date,
                exclude_room_type_id=house_account_room_type_id,
            )
        )

        # [(bill_id, charge_id, posttax_amount)]
        bill_charge_amount_mappings = self._get_bill_charge_amount_mappings(
            arrival_booking_aggregates, departure_booking_aggregates, business_date
        )

        arrival_departure_stats = {
            'arrivals': {
                'due_in_room_assigned': {
                    'complimentary': 0,
                    'house_use': 0,
                    'others': 0,
                },
                'due_in_room_unassigned': {
                    'complimentary': 0,
                    'house_use': 0,
                    'others': 0,
                },
                'arrived': {
                    'complimentary': 0,
                    'house_use': 0,
                    'others': 0,
                },
            },
            'departures': {
                'due_out': {
                    'complimentary': 0,
                    'house_use': 0,
                    'others': 0,
                },
                'departed': {
                    'complimentary': 0,
                    'house_use': 0,
                    'others': 0,
                },
            },
        }

        for arrival_booking_aggregate in arrival_booking_aggregates:
            for room_stay in arrival_booking_aggregate.room_stays:
                if room_stay.room_type_id == house_account_room_type_id:
                    continue

                if business_date != dateutils.to_date(room_stay.checkin_date):
                    continue

                if not room_stay.is_active():
                    continue

                room_status = self._get_arrival_room_status_of_room_stay(room_stay)
                charge_type = self._get_charge_type_of_room_stay(
                    room_stay,
                    arrival_booking_aggregate,
                    business_date,
                    bill_charge_amount_mappings,
                )

                arrival_departure_stats['arrivals'][room_status][charge_type] += 1

        for departure_booking_aggregate in departure_booking_aggregates:
            for room_stay in departure_booking_aggregate.room_stays:
                if room_stay.room_type_id == house_account_room_type_id:
                    continue

                actual_checkout_date = room_stay.actual_checkout_date
                if actual_checkout_date and not (
                    min_actual_checkout_date
                    <= actual_checkout_date
                    < max_actual_checkout_date
                ):
                    continue

                if actual_checkout_date is None and business_date != dateutils.to_date(
                    room_stay.checkout_date
                ):
                    continue

                updated_business_date = (
                    dateutils.subtract(business_date, days=1)
                    if dateutils.to_date(actual_checkout_date)
                    != dateutils.to_date(room_stay.checkin_date)
                    else business_date
                )

                room_status = self._get_departure_room_status_of_room_stay(room_stay)
                charge_type = self._get_charge_type_of_room_stay(
                    room_stay,
                    departure_booking_aggregate,
                    updated_business_date,
                    bill_charge_amount_mappings,
                )

                arrival_departure_stats['departures'][room_status][charge_type] += 1

        return arrival_departure_stats

    def _get_arrival_room_status_of_room_stay(self, room_stay):
        if room_stay.is_checkin_performed():
            return 'arrived'
        elif room_stay.room_allocation:
            return 'due_in_room_assigned'
        else:
            return 'due_in_room_unassigned'

    def _get_departure_room_status_of_room_stay(self, room_stay):
        if room_stay.status == BookingStatus.CHECKED_OUT:
            return 'departed'

        return 'due_out'

    def _get_charge_type_of_room_stay(
        self, room_stay, booking_aggregate, business_date, bill_charge_amount_mappings
    ):
        charge_type = 'others'

        room_rate_plans = room_stay.room_rate_plans if room_stay.room_rate_plans else []
        for rate_plan_vo in room_rate_plans:
            if rate_plan_vo.stay_date == business_date:
                if (
                    booking_aggregate.rate_plan_dict[
                        rate_plan_vo.rate_plan_id
                    ].rate_plan_code
                    == 'HSE'
                ):
                    charge_type = 'house_use'
                    break
        else:
            if str(business_date) in room_stay.charge_id_map:
                charge_id = room_stay.charge_id_map[str(business_date)]
                if (
                    booking_aggregate.bill_id,
                    charge_id,
                    Decimal('0'),
                ) in bill_charge_amount_mappings:
                    charge_type = 'complimentary'

        return charge_type

    def _get_bill_charge_amount_mappings(
        self, arrival_booking_aggregates, departure_booking_aggregates, business_date
    ):
        bill_charge_mappings = []  # [(bill_id, charge_id)]
        for booking_aggregate in arrival_booking_aggregates:
            for room_stay in booking_aggregate.room_stays:
                if str(business_date) in room_stay.charge_id_map:
                    bill_charge_mapping = (
                        booking_aggregate.bill_id,
                        room_stay.charge_id_map[str(business_date)],
                    )
                    if bill_charge_mapping not in bill_charge_mappings:
                        bill_charge_mappings.append(bill_charge_mapping)

        for booking_aggregate in departure_booking_aggregates:
            for room_stay in booking_aggregate.room_stays:
                updated_business_date = (
                    dateutils.subtract(business_date, days=1)
                    if dateutils.to_date(room_stay.actual_checkout_date)
                    != dateutils.to_date(room_stay.checkin_date)
                    else business_date
                )
                if str(updated_business_date) in room_stay.charge_id_map:
                    bill_charge_mapping = (
                        booking_aggregate.bill_id,
                        room_stay.charge_id_map[str(updated_business_date)],
                    )
                    if bill_charge_mapping not in bill_charge_mappings:
                        bill_charge_mappings.append(bill_charge_mapping)

        return self.bill_repository.get_bill_posttax_amounts(bill_charge_mappings)

    @session_manager(commit=True)
    def refresh_availability_status_for_current_business_date(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        crs_context.set_hotel_context(hotel_aggregate)
        grouped_room_allotment_aggregates = (
            self.room_allotment_repository.load_multiple(hotel_id, skip_allotments=True)
        )
        self.room_allotment_repository.update_all(
            grouped_room_allotment_aggregates.values()
        )
