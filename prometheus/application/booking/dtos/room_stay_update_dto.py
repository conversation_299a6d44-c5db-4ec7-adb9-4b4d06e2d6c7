from prometheus.domain.booking.dtos.room_allocation_data import RoomAllocationData
from ths_common.value_objects import RoomStayConfig


class RoomStayUpdateDto(object):
    def __init__(self, room_stay, room_stay_update_data, room_allocation_checkin_date):
        self.room_stay = room_stay
        self.room_stay_update_data = room_stay_update_data
        self.room_allocation_checkin_date = room_allocation_checkin_date

    def is_update_for_room_type(self):
        room_type_change = self.room_stay_update_data.get('room_type', dict())
        return room_type_change and room_type_change.get('room_type_id')

    def is_update_for_stay_date(self):
        return self.room_stay_update_data.get(
            'checkin_date'
        ) or self.room_stay_update_data.get('checkout_date')

    def new_room_allocation_requested(self):
        return self.room_stay_update_data.get('room_allocation')

    def get_room_id_to_be_allocated(self):
        return self.room_stay_update_data.get('room_allocation', dict()).get('room_id')

    def create_room_allocation_data(self, room_aggregate):
        return RoomAllocationData(
            room=room_aggregate.room,
            room_id=self.get_room_id_to_be_allocated(),
            checkin_date=self.room_allocation_checkin_date,
            room_stay=self.room_stay,
            old_room_allocation=self.room_stay.room_allocation,
        )

    def is_update_for_rate_plan(self):
        if self.room_stay_update_data.get('rate_plan'):
            return self.room_stay_update_data.get('rate_plan')
        else:
            return self.room_stay_update_data.get('prices')

    def is_update_for_disallow_charge_addition(self):
        if 'disallow_charge_addition' in self.room_stay_update_data:
            if (
                self.room_stay_update_data.get('disallow_charge_addition')
                != self.room_stay.disallow_charge_addition
            ):
                return True
            else:
                return False

    @property
    def new_room_stay_config(self):
        room_type_change = self.room_stay_update_data.get('room_type', dict())
        return RoomStayConfig(
            room_type_id=room_type_change.get(
                'room_type_id', self.room_stay.room_type_id
            ),
            checkin_date=self.room_stay_update_data.get(
                'checkin_date', self.room_stay.checkin_date
            ),
            checkout_date=self.room_stay_update_data.get(
                'checkout_date', self.room_stay.checkout_date
            ),
        )
