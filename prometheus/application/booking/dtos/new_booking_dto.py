from prometheus.common.error_codes.errors import ApplicationErrors
from ths_common.constants.booking_constants import BookingStatus
from ths_common.exceptions import ValidationException


class NewBookingDto:
    def __init__(
        self,
        hotel_id,
        room_stays,
        booking_owner,
        source,
        reference_number,
        status=None,
        comments=None,
        extra_information=None,
        fees=None,
        hold_till=None,
        guests=None,
        payments=None,
        company_details=None,
        travel_agent_details=None,
        account_details=None,
        group_name=None,
        default_billed_entity_category=None,
        default_payment_instruction=None,
        default_billed_entity_category_for_extras=None,
        default_payment_instruction_for_extras=None,
        segments=None,
        guarantee_information=None,
        discount_details=None,
    ):
        self.hotel_id = hotel_id
        self.room_stays = room_stays
        self.booking_owner = booking_owner
        self.source = source
        self.reference_number = reference_number

        self.comments = comments
        self.extra_information = extra_information
        self.fees = fees
        self.hold_till = hold_till
        self.guests = guests if guests else []
        self.payments = payments
        self.guarantee_information = guarantee_information

        # Status in new booking dto can be reserved, temporary or confirmed
        # If it's not confirmed, then we'll use status as temporary or reserved internally, based on hold_till value
        self.status = (
            status if status == BookingStatus.CONFIRMED else BookingStatus.RESERVED
        )

        # If hold_till value is passed, then set the status as temporary
        if self.hold_till:
            # Only convert RESERVED status to TEMPORARY when hold till is passed
            if self.status == BookingStatus.CONFIRMED:
                raise ValidationException(
                    ApplicationErrors.CANT_CREATE_CONFIRMED_BOOKING_WITH_HOLD_TILL
                )

            self.status = BookingStatus.TEMPORARY

        # If payments is being passed, then by directly set status as confirmed
        if self.payments:
            self.status = BookingStatus.CONFIRMED
        # To be set later from booking creation application service method, while creating rate plan dto
        self.rate_plans = None
        self.company_details = company_details
        self.travel_agent_details = travel_agent_details
        self.account_details = account_details
        self.group_name = group_name
        self.default_billed_entity_category = default_billed_entity_category
        self.default_payment_instruction = default_payment_instruction
        self.default_billed_entity_category_for_extras = (
            default_billed_entity_category_for_extras
        )
        self.default_payment_instruction_for_extras = (
            default_payment_instruction_for_extras
        )
        self.segments = segments
        self.discount_details = discount_details or []

    @staticmethod
    def create_new(data):
        """
        :param data: This is the data returned from NewBookingSchemaV2
        """
        return NewBookingDto(
            hotel_id=data["hotel_id"],
            room_stays=data["room_stays"],
            booking_owner=data["booking_owner"],
            source=data["source"],
            reference_number=data["reference_number"],
            comments=data.get("comments"),
            status=data.get("status"),
            extra_information=data.get("extra_information"),
            fees=data.get("fees"),
            hold_till=data.get("hold_till"),
            guests=data.get("guests"),
            payments=data.get("payments"),
            company_details=data.get("company_details"),
            travel_agent_details=data.get("travel_agent_details"),
            account_details=data.get("account_details"),
            group_name=data.get("group_name"),
            default_billed_entity_category=data.get("default_billed_entity_category"),
            default_payment_instruction=data.get("default_payment_instruction"),
            default_billed_entity_category_for_extras=data.get(
                "default_billed_entity_category_for_extras"
            ),
            default_payment_instruction_for_extras=data.get(
                "default_payment_instruction_for_extras"
            ),
            segments=data.get("segments"),
            guarantee_information=data.get("guarantee_information"),
            discount_details=data.get("discount_details"),
        )
