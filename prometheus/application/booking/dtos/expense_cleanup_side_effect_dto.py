class ExpenseServiceCleanupSideEffects:
    # Represents the side effects of an expense cleanup operation.
    # Now it only contains the inventory blocks that were released as a result of the cleanup.
    # Any other side effects can be added in the future.
    def __init__(self, inventory_blocks_released=None):
        self.inventory_blocks_released = inventory_blocks_released or []

    def __add__(self, other):
        if other is None:
            return self
        if not isinstance(other, ExpenseServiceCleanupSideEffects):
            return NotImplemented
        inventory_blocks_released = (
            self.inventory_blocks_released + other.inventory_blocks_released
        )
        return ExpenseServiceCleanupSideEffects(inventory_blocks_released)

    def __bool__(self):
        return bool(self.inventory_blocks_released)
