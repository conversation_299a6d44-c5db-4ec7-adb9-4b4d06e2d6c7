import json

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.domain.billing.repositories import BillRepository, InvoiceRepository
from prometheus.domain.billing.services.invoice_template_service import (
    InvoiceTemplateService,
)
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import SkuCategoryRepository
from ths_common.utils.common_utils import DateTimeEncoder, json_dumps
from ths_common.value_objects import Occupancy


@register_instance(
    dependencies=[
        SkuCategoryRepository,
        InvoiceRepository,
        BillRepository,
        BookingRepository,
        InvoiceTemplateService,
    ]
)
class CrsBookingInvoiceTemplateService:
    def __init__(
        self,
        sku_category_repository: SkuCategoryRepository,
        invoice_repository: InvoiceRepository,
        bill_repository: BillRepository,
        booking_repository: BookingRepository,
        invoice_template_service: InvoiceTemplateService,
    ):
        self.sku_category_repository = sku_category_repository
        self.invoice_repository = invoice_repository
        self.bill_repository = bill_repository
        self.booking_repository = booking_repository
        self.invoice_template_service = invoice_template_service

    def generate_and_upload_proforma_invoice_template(
        self, booking_aggregate, bill_aggregate, invoice_aggregates, hotel_aggregate
    ):
        for invoice_aggregate in invoice_aggregates:
            invoice_aggregate.invoice.parent_info = json.loads(
                json.dumps(invoice_aggregate.invoice.parent_info, cls=DateTimeEncoder)
            )
            invoice_aggregate.invoice.tax_details_breakup = json.loads(
                json_dumps(invoice_aggregate.invoice.tax_details_breakup)
            )
            # Product Requirement: Excluding dummy guest details on proforma invoice
            user_info_map = dict()
            for user_id, user_info in invoice_aggregate.invoice.user_info_map.items():
                if user_info.dummy:
                    continue
                user_info_map[user_id] = user_info

            invoice_aggregate.invoice.set_user_info_map(user_info_map)

        return self._generate_invoice_templates(
            bill_aggregate,
            booking_aggregate,
            hotel_aggregate,
            invoice_aggregates,
            is_proforma_invoice=True,
        )

    def generate_and_upload_invoice_templates(
        self,
        bill_aggregate,
        invoice_aggregates,
        booking_aggregate,
        hotel_aggregate,
        should_upload,
        booked_charges_invoiced=None,
        booked_allowances_invoiced=None,
        **kwargs
    ):
        # Generate invoice templates json, and optionally create pdf, and upload
        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(bill_aggregate.vendor_id)

        invoices_to_update_url = {
            aggregate.invoice_id: aggregate for aggregate in invoice_aggregates
        }
        updated_invoice_aggregates = []
        all_templates = []

        while invoices_to_update_url:
            pending_invoice_aggregates = list(invoices_to_update_url.values())
            templates, invoice_url_map = self._generate_invoice_templates(
                bill_aggregate,
                booking_aggregate,
                hotel_aggregate,
                pending_invoice_aggregates,
                is_proforma_invoice=False,
                should_upload=should_upload,
                booked_charges_invoiced=booked_charges_invoiced,
                booked_allowances_invoiced=booked_allowances_invoiced,
            )

            for invoice_aggregate, template in zip(
                pending_invoice_aggregates, templates
            ):
                invoice_id = invoice_aggregate.invoice_id
                invoice_url, invoice_version = invoice_url_map.get(invoice_id)
                invoice_aggregate = self.invoice_repository.load_for_update(invoice_id)
                if invoice_aggregate.invoice.version != invoice_version:
                    invoices_to_update_url[invoice_id] = invoice_aggregate
                    continue

                if invoice_url.url:
                    invoice_aggregate.update_invoice_url(invoice_url.url)
                if invoice_url.signed_url and invoice_url.expiration:
                    invoice_aggregate.set_signed_url(
                        invoice_url.signed_url, invoice_url.expiration
                    )

                updated_invoice_aggregates.append(invoice_aggregate)
                invoices_to_update_url.pop(invoice_id)
                all_templates.append(template)

        return updated_invoice_aggregates, all_templates

    def _generate_invoice_templates(
        self,
        bill_aggregate,
        booking_aggregate,
        hotel_aggregate,
        invoice_aggregates,
        is_proforma_invoice=False,
        should_upload=True,
        booked_charges_invoiced=None,
        booked_allowances_invoiced=None,
    ):
        self._update_summary_in_invoices(
            bill_aggregate,
            invoice_aggregates,
            booked_charges_invoiced=booked_charges_invoiced,
            booked_allowances_invoiced=booked_allowances_invoiced,
        )

        occupancy = self._occupancy(booking_aggregate)
        travel_agent_details = booking_aggregate.get_travel_agent_details()
        company_details = booking_aggregate.get_company_details()

        (
            templates,
            invoice_url_map,
        ) = self.invoice_template_service.generate_invoice_templates(
            occupancy,
            bill_aggregate,
            booking_aggregate,
            invoice_aggregates,
            {
                aggregate.sku_category.sku_category_id: aggregate
                for aggregate in self.sku_category_repository.load_all()
            },
            include_bank_details=booking_aggregate.is_hotel_channel_booking(),
            should_upload=should_upload,
            hotel_aggregate=hotel_aggregate,
            booking_meta=dict(
                company_code=booking_aggregate.get_company_code(),
                membership_id=booking_aggregate.booking.membership_id,
                travel_agent_details=travel_agent_details.legal_details.to_json()
                if travel_agent_details
                else None,
                company_name=company_details.legal_details.legal_name
                if company_details
                else None,
                remarks=booking_aggregate.get_guest_visible_remarks(),
                channel_code=booking_aggregate.booking.source.channel_code,
            ),
            is_proforma_invoice=is_proforma_invoice,
            charge_room_stay_info_map=self._create_charge_room_stay_info_map(
                booking_aggregate
            ),
            rate_plan_mapping={
                rp.rate_plan_reference_id: rp for rp in booking_aggregate.rate_plans
            }
            if booking_aggregate.rate_plans
            else {},
            company_details=company_details if crs_context.is_treebo_tenant() else None,
            travel_agent_details=travel_agent_details
            if crs_context.is_treebo_tenant()
            else None,
            booked_allowances_invoiced=booked_allowances_invoiced,
        )
        return templates, invoice_url_map

    @staticmethod
    def _update_summary_in_invoices(
        bill_aggregate,
        invoice_aggregates,
        booked_charges_invoiced=None,
        booked_allowances_invoiced=None,
    ):
        for invoice_aggregate in invoice_aggregates:
            account_summary = bill_aggregate.get_account_summary(
                invoice_aggregate.invoice.billed_entity_account,
                invoiced_booked_charges=booked_charges_invoiced,
                invoiced_booked_allowances=booked_allowances_invoiced,
            )
            invoice_aggregate.net_payable = -account_summary.balance
            invoice_aggregate.summary = account_summary

    @staticmethod
    def _create_charge_room_stay_info_map(booking_aggragate):
        charge_room_stay_info_map = dict()
        for room_stay in booking_aggragate.room_stays:
            room_stay_info = dict(
                checkin_date=room_stay.stay_start, checkout_date=room_stay.stay_end
            )
            for charge_id in room_stay.charge_ids:
                charge_room_stay_info_map[charge_id] = room_stay_info

        for expense in booking_aggragate.expenses:
            room_stay = booking_aggragate.room_stay_dict.get(expense.room_stay_id)
            charge_room_stay_info_map[expense.charge_id] = dict(
                checkin_date=room_stay.stay_start, checkout_date=room_stay.stay_end
            )

        return charge_room_stay_info_map

    def _occupancy(self, booking_aggregate):
        adult, child = booking_aggregate.get_adult_and_child_count()
        occupancy = Occupancy(adult=adult, child=child)
        return occupancy
