import logging

from treebo_commons.utils import dateutils

from prometheus.domain.booking.exceptions import PriceError
from ths_common.value_objects import NotAssigned

logger = logging.getLogger(__name__)


def validate_prices_sent_only_for_dates(room_stay_prices, date_list):
    dates_for_new_prices = [
        dateutils.to_date(price.applicable_date) for price in room_stay_prices
    ]
    dates_without_price = set(date_list) - set(dates_for_new_prices)
    if dates_without_price:
        logger.debug(
            "Dates for which room stay occupancy has changed: %s but prices are not sent",
            [dateutils.date_to_ymd_str(d) for d in dates_without_price],
        )
        dates_without_price = list(str(d) for d in dates_without_price)
        raise PriceError(
            description="Please send prices for all the room stay dates where occupancy or room type has changed",
            extra_payload=dict(missing_dates=dates_without_price),
        )
    extra_prices = set(dates_for_new_prices) - set(date_list)
    if extra_prices:
        extra_prices = list(str(d) for d in extra_prices)
        logger.debug(
            "Dates for which room stay occupancy has not changed: %s but prices are sent",
            extra_prices,
        )
        raise PriceError(
            description="Please send prices only for the room stay dates where occupancy or room type has changed, "
            "and existing charges hasn't been consumed",
            extra_payload=dict(extra_dates=extra_prices),
        )


def validate_price_and_inclusions(prices, rate_plan_inclusions=None):
    if not prices:
        return
    price_pretax_amount_list = [
        price.pretax_amount
        for price in prices
        if price.pretax_amount is not NotAssigned
    ]
    price_posttax_amount_list = [
        price.posttax_amount
        for price in prices
        if price.posttax_amount is not NotAssigned
    ]
    if len(price_pretax_amount_list) > 0 and len(price_posttax_amount_list) > 0:
        raise PriceError(
            description='Please provide either of posttax_amount or pretax_amount'
        )
    if not rate_plan_inclusions:
        return
    for rate_plan_inclusion in rate_plan_inclusions:
        pretax_amount = (
            rate_plan_inclusion.pretax_amount.amount
            if rate_plan_inclusion.pretax_amount
            else 0
        )
        posttax_amount = (
            rate_plan_inclusion.posttax_amount.amount
            if rate_plan_inclusion.posttax_amount
            else 0
        )

        if len(price_pretax_amount_list) > 0 and posttax_amount != 0:
            raise PriceError(
                description='pretax_amount provided in price whereas '
                'posttax_amount provided in rate_plan_inclusions'
            )

        if len(price_posttax_amount_list) > 0 and pretax_amount != 0:
            raise PriceError(
                description='posttax_amount provided in price whereas '
                'pretax_amount provided in rate_plan_inclusions'
            )
