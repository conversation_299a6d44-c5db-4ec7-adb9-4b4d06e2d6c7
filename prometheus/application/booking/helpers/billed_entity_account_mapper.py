from prometheus.application.helpers.billed_entity_helper import (
    build_charge_to_billed_entity_map_for_extras,
    build_charge_to_billed_entity_map_for_primary_guest_category,
)
from ths_common.constants.billing_constants import BilledEntityCategory


def get_charges_to_attach_billed_entity(bill_aggregate, skip_charges):
    if not skip_charges:
        return bill_aggregate.charges

    skip_charge_ids = {charge.charge_id for charge in skip_charges}
    return [
        charge
        for charge in bill_aggregate.charges
        if charge.charge_id not in skip_charge_ids
    ]


def attach_default_billed_entity_account_to_charges_and_payments(
    booking_aggregate, bill_aggregate, skip_charges=None
):
    charges = get_charges_to_attach_billed_entity(bill_aggregate, skip_charges)
    if (
        booking_aggregate.get_default_billed_entity_category()
        == BilledEntityCategory.PRIMARY_GUEST
    ):
        charge_id_to_billed_entity_map = (
            build_charge_to_billed_entity_map_for_primary_guest_category(
                booking_aggregate, bill_aggregate
            )
        )

        bill_aggregate.attach_default_billed_entity_account_to_charges(
            charges=charges,
            charge_id_to_billed_entity_map=charge_id_to_billed_entity_map,
        )
        # NOTE: In case default billed entity category has given primary guest on
        # booking create/update.
        # The payments would be assigned to first room primary guest billed entity only
        # And charges would be assigned based on room's primary guest billed entity
        room_stay = booking_aggregate.get_room_stays()[0]
        charge_id = room_stay.get_charge_for_date(room_stay.checkin_date)
        billed_entity = charge_id_to_billed_entity_map.get(charge_id)
        bill_aggregate.attach_default_billed_entity_account_to_payments(billed_entity)

    else:
        billed_entity = bill_aggregate.get_billed_entity_for_category(
            booking_aggregate.get_default_billed_entity_category()
        )
        bill_aggregate.attach_default_billed_entity_account_to_charges(
            charges=charges, billed_entity=billed_entity
        )
        bill_aggregate.attach_default_billed_entity_account_to_payments(billed_entity)


def attach_default_billed_entity_account_to_extra_charges(
    booking_aggregate, bill_aggregate, existing_extra_charges
):
    if not existing_extra_charges:
        return

    if (
        booking_aggregate.get_default_billed_entity_category_for_extras()
        == BilledEntityCategory.PRIMARY_GUEST
    ):
        charge_id_to_billed_entity_map = build_charge_to_billed_entity_map_for_extras(
            booking_aggregate, bill_aggregate, BilledEntityCategory.PRIMARY_GUEST
        )
        bill_aggregate.attach_default_billed_entity_account_to_charges(
            charges=existing_extra_charges,
            charge_id_to_billed_entity_map=charge_id_to_billed_entity_map,
        )

    else:
        billed_entity = bill_aggregate.get_billed_entity_for_category(
            booking_aggregate.get_default_billed_entity_category_for_extras()
        )
        bill_aggregate.attach_default_billed_entity_account_to_charges(
            charges=existing_extra_charges, billed_entity=billed_entity
        )
