from object_registry import register_instance
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.services.invoice_service import InvoiceService


@register_instance(dependencies=[InvoiceRepository, InvoiceService])
class InvoiceCancellationService(object):
    def __init__(self, invoice_repository, invoice_service):
        self.invoice_repository = invoice_repository
        self.invoice_service = invoice_service

    def cancel_invoice(self, bill_aggregate, invoice_aggregate):
        """

        Args:
            bill_aggregate:
            invoice_aggregate:

        Returns:

        """
        self.invoice_service.cancel_invoice(bill_aggregate, invoice_aggregate)

        hotel_invoice_aggregate = None
        if (
            invoice_aggregate.is_reseller_issued_invoice()
            and invoice_aggregate.invoice.hotel_invoice_id
        ):
            hotel_invoice_aggregate = self.invoice_repository.load_for_update(
                invoice_aggregate.invoice.hotel_invoice_id
            )
            hotel_invoice_aggregate.cancel()

        return bill_aggregate, invoice_aggregate, hotel_invoice_aggregate
