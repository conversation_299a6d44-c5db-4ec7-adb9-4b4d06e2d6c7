import urllib

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from prometheus.infrastructure.external_clients.template_service_client import (
    TemplateFormat,
    TemplateNameSpace,
    TemplateService,
)
from ths_common.constants.tenant_settings_constants import ERegCardConfig, ERegCardLevel
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        CatalogServiceClient,
        RoomTypeRepository,
        TenantSettings,
        JobSchedulerService,
        HotelConfigRepository,
        SignedUrlGenerator,
    ]
)
class ERegCardTemplateService(object):
    def __init__(
        self,
        booking_repository,
        bill_repository,
        hotel_repository,
        catalog_service_client,
        room_type_repository,
        tenant_settings,
        job_scheduler_service,
        hotel_config_repository,
        signed_url_generator: SignedUrlGenerator,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.catalog_service_client = catalog_service_client
        self.room_type_repository = room_type_repository
        self.tenant_settings = tenant_settings
        self.job_scheduler_service = job_scheduler_service
        self.hotel_config_repository = hotel_config_repository
        self.signed_url_generator = signed_url_generator

    def _set_hotel_context(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate
        )
        return hotel_aggregate, hotel_config_aggregate, hotel_context

    def generate_guest_wise_eregcard(
        self,
        booking_aggregate,
        bill_aggregate,
        hotel_aggregate,
        eregcard_level,
        customers=None,
    ):
        booking_details = self._get_booking_details(booking_aggregate, bill_aggregate)
        hotel_details = self._get_hotel_details(hotel_aggregate)
        eregcard_guest_data = dict()
        if not eregcard_level:
            raise ValidationException(
                message="eregcard_level can't be null when eregcard is enabled"
            )

        if customers:
            customer_ids_to_update_ereg_card = {c.customer_id for c in customers}
        else:
            customer_ids_to_update_ereg_card = {
                c.customer_id for c in booking_aggregate.get_all_customers()
            }

        for room_stay in booking_aggregate.room_stays:
            average_room_stay_price = bill_aggregate.average_posttax_room_stay_amount(
                room_stay.charge_ids
            )
            currently_allocated_customer_ids = set(
                room_stay.get_current_guest_allocations()
            )

            # Skip if customer ids to update ereg card is not staying in this room
            if all(
                cid not in customer_ids_to_update_ereg_card
                for cid in currently_allocated_customer_ids
            ):
                continue

            guest_allocated_customers = [
                c
                for c in booking_aggregate.get_customers(
                    currently_allocated_customer_ids
                )
            ]
            if not guest_allocated_customers:
                continue
            guest_details = self._get_guest_details(
                guest_allocated_customers, eregcard_level
            )
            room_details = self._get_room_details(
                booking_aggregate, bill_aggregate, room_stay
            )
            print_rate = booking_aggregate.can_print_room_rate(
                room_details['room_stay_id']
            )
            if eregcard_level == ERegCardLevel.ROOM.value:
                eregcard_guest_data.update(
                    self._generate_guest_wise_eregcard_for_room_level_config(
                        hotel_details,
                        booking_details,
                        room_details,
                        guest_details,
                        print_rate,
                        average_room_stay_price,
                    )
                )
            elif eregcard_level == ERegCardLevel.GUEST.value:
                eregcard_guest_data.update(
                    self._generate_guest_wise_eregcard_for_guest_level_config(
                        hotel_details,
                        booking_details,
                        room_details,
                        guest_details,
                        print_rate,
                        average_room_stay_price,
                    )
                )

        eregcard_guest_mapping = TemplateService().generate_bulk(
            TemplateNameSpace.E_REG_CARD.value, eregcard_guest_data, TemplateFormat.PDF
        )
        return eregcard_guest_mapping

    def _get_booking_details(self, booking_aggregate, bill_aggregate):
        arrival_date = booking_aggregate.booking.checkin_date.date()
        departure_date = booking_aggregate.booking.checkout_date.date()
        checkin_time = booking_aggregate.booking.checkin_date.time()
        checkout_time = booking_aggregate.booking.checkout_date.time()
        channel_code = booking_aggregate.booking.source.channel_code
        booking_reference_id = booking_aggregate.booking.reference_number
        advance_payment = (
            bill_aggregate.net_paid_amount.amount
            if bill_aggregate and bill_aggregate.net_paid_amount
            else None
        )
        mode_of_payment = (
            bill_aggregate.payments[0].payment_mode
            if bill_aggregate and bill_aggregate.payments
            else None
        )
        return {
            "arrival_date": arrival_date,
            "departure_date": departure_date,
            "channel_code": channel_code,
            "advance_payment": advance_payment,
            "booking_reference_id": booking_reference_id,
            "mode_of_payment": mode_of_payment,
            "membership_id": booking_aggregate.booking.membership_id,
            "remarks": booking_aggregate.get_guest_visible_remarks(),
            "company_details": booking_aggregate.get_company_details(),
            "travel_agent_details": booking_aggregate.get_travel_agent_details(),
            "checkin_time": checkin_time,
            "checkout_time": checkout_time,
        }

    def _get_hotel_details(self, hotel_aggregate):
        if not hotel_aggregate:
            return
        catalog_hotel_response = CatalogServiceClient().fetch_hotel(
            hotel_aggregate.hotel_id
        )
        (
            hotel_reception_landline_number,
            hotel_reception_mobile_number,
        ) = self._get_hotel_phone_numbers(catalog_hotel_response)
        hotel_email = self._get_hotel_email(catalog_hotel_response)
        return {
            "hotel_address": hotel_aggregate.hotel.address if hotel_aggregate else None,
            "hotel_name": hotel_aggregate.hotel.name if hotel_aggregate else None,
            "hotel_reception_landline_number": hotel_reception_landline_number,
            "hotel_reception_mobile_number": hotel_reception_mobile_number,
            "hotel_email": hotel_email,
            "hotel_gstin": hotel_aggregate.hotel.gstin_num if hotel_aggregate else None,
            "hotel_logo": hotel_aggregate.hotel.logo if hotel_aggregate else None,
            "hotel_pan_number": hotel_aggregate.hotel.pan_number
            if hotel_aggregate
            else None,
            "hotel_tan_number": hotel_aggregate.hotel.tan_number
            if hotel_aggregate
            else None,
            "hotel_tin_number": hotel_aggregate.hotel.tin_number
            if hotel_aggregate
            else None,
            "timezone": hotel_aggregate.hotel.timezone,
        }

    @staticmethod
    def get_total_room_rate(booking_aggregate, bill_aggregate, room_stay):
        charges_on_room_stay = (
            booking_aggregate.get_all_applicable_charges_on_room_stay(
                room_stay.room_stay_id
            )
        )
        if not charges_on_room_stay:
            return 0
        active_addon_charges = bill_aggregate.get_active_charges(charges_on_room_stay)
        return sum(ch.posttax_amount_post_allowance for ch in active_addon_charges)

    def _get_room_details(self, booking_aggregate, bill_aggregate, room_stay):
        room_type_and_room_type_id_map = self.room_type_repository.load_type_map()
        room_type_id = room_stay.room_type_id
        room_type = room_type_and_room_type_id_map[room_type_id].room_type.type
        checkin_date_occupancies = room_stay.date_wise_occupancies.get(
            room_stay.actual_checkin_date.date()
            if room_stay.actual_checkin_date
            else room_stay.checkin_date.date()
        )
        adults = checkin_date_occupancies.adult if checkin_date_occupancies else 0
        childs = checkin_date_occupancies.child if checkin_date_occupancies else 0

        room_charge = self.get_total_room_rate(
            booking_aggregate, bill_aggregate, room_stay
        )

        return {
            'room_stay_id': room_stay.room_stay_id,
            'room_charge': room_charge,
            'adults': adults,
            "childs": childs,
            "room_type": room_type,
            "room_number": room_stay.room_allocation.room_no
            if room_stay.room_allocation
            else None,
        }

    def _get_hotel_phone_numbers(self, catalog_hotel_response):
        if not catalog_hotel_response.get('property_details'):
            return None, None
        reception_landline = catalog_hotel_response['property_details'].get(
            'reception_landline'
        )
        reception_mobile = catalog_hotel_response['property_details'].get(
            'reception_mobile'
        )
        return reception_landline, reception_mobile

    def _get_hotel_email(self, catalog_hotel_response):
        owners = catalog_hotel_response.get('owners')
        if not owners:
            return None
        email = ''
        for owner in owners:
            # picking up first non-null email for property
            if owner.get('email'):
                email = owner.get('email')
                break
        return email

    def _generate_guest_wise_eregcard_for_room_level_config(
        self,
        hotel_details,
        booking_details,
        room_details,
        guest_details,
        print_rate,
        average_room_stay_price,
    ):
        if not guest_details['primary_guests']:
            return dict()
        primary_guest = (
            guest_details['primary_guests'][0]
            if guest_details.get('primary_guests')
            else None
        )
        other_guests = (
            guest_details['other_guests'] if guest_details.get('other_guests') else None
        )

        eregcard = {
            "hotel_details": hotel_details,
            "booking_details": booking_details,
            "room_details": room_details,
            "primary_guest": primary_guest,
            "other_guests": other_guests,
            "print_rate": print_rate,
            "average_room_stay_price": average_room_stay_price,
        }

        # todo: Fix cyclic import error, serializer calling service -> service calling serializer.
        from prometheus.common.serializers import ERegCardTemplateResponseSchema

        template_json = ERegCardTemplateResponseSchema().dump(eregcard).data
        return {guest_details['primary_guests'][0].customer_id: {"data": template_json}}

    def _generate_guest_wise_eregcard_for_guest_level_config(
        self,
        hotel_details,
        booking_details,
        room_details,
        guest_details,
        print_rate,
        average_room_stay_price,
    ):
        primary_guests = guest_details['primary_guests']
        if not primary_guests:
            return dict()
        eregcard_guest_mapping = dict()
        for primary_guest in primary_guests:
            eregcard = {
                "hotel_details": hotel_details,
                "booking_details": booking_details,
                "room_details": room_details,
                "primary_guest": primary_guest,
                "print_rate": print_rate,
                "average_room_stay_price": average_room_stay_price,
            }

            from prometheus.common.serializers import ERegCardTemplateResponseSchema

            template_json = ERegCardTemplateResponseSchema().dump(eregcard).data
            eregcard_guest_mapping[primary_guest.customer_id] = {"data": template_json}

        return eregcard_guest_mapping

    def _get_guest_details(self, guest_allocated_customers, eregcard_level):
        primary_guests = []
        other_guests = []
        if not guest_allocated_customers:
            return
        if not eregcard_level:
            raise ValidationException(message="eregcard level can't be null")
        for guest_allocated_customer in guest_allocated_customers:
            if guest_allocated_customer.verifier_signature:
                (
                    guest_allocated_customer.verifier_signature,
                    _,
                ) = self.signed_url_generator.generate_signed_url(
                    urllib.parse.unquote(guest_allocated_customer.verifier_signature)
                )

            if eregcard_level == ERegCardLevel.ROOM.value:
                if guest_allocated_customer.is_primary:
                    primary_guests.append(guest_allocated_customer)
                else:
                    other_guests.append(guest_allocated_customer)

            if eregcard_level == ERegCardLevel.GUEST.value:
                primary_guests.append(guest_allocated_customer)

        return dict(primary_guests=primary_guests, other_guests=other_guests)

    def generate_eregcard_url(self, booking_aggregate, hotel_aggregate, customers=None):
        eregcard_config = self.tenant_settings.get_eregcard_config(
            booking_aggregate.hotel_id
        )

        if not (eregcard_config and eregcard_config.get(ERegCardConfig.ENABLED.value)):
            return JobResultDto.success()

        bill_aggregate = self.bill_repository.load(booking_aggregate.booking.bill_id)
        eregcard_guest_mapping = self.generate_guest_wise_eregcard(
            booking_aggregate,
            bill_aggregate,
            hotel_aggregate,
            eregcard_config.get(ERegCardConfig.LEVEL.value),
            customers,
        )

        for customer in booking_aggregate.get_all_customers():
            eregcard_url = eregcard_guest_mapping.get(customer.customer_id)
            if not eregcard_url:
                continue
            customer.update_eregcard_url(eregcard_url)
        return JobResultDto.success()

    def schedule_eregcard_url_generation(self, booking_id, hotel_id, customer_ids=None):
        return self.job_scheduler_service.schedule_eregcard_url_generation(
            booking_id, hotel_id, customer_ids=customer_ids
        )
