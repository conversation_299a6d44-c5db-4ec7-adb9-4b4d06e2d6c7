from object_registry import register_instance
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.domain.booking.entities import Customer
from prometheus.domain.booking.exceptions import CustomerMergeError
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    BilledEntityStatus,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.exceptions import ResourceNotFound


@register_instance(dependencies=[BilledEntityService])
class GuestHelperService(object):
    def __init__(self, billed_entity_service: BilledEntityService):
        self.billed_entity_service = billed_entity_service

    @staticmethod
    def _clear_secondary_be_category(bill_aggregate, customer):
        bill_aggregate.update_billed_entity_secondary_category(
            customer.billed_entity_id, None
        )

    @audit(audit_type=AuditType.GUEST_UN_MARKED_AS_BOOKER)
    def un_mark_guest_as_booker(
        self, booking_aggregate, bill_aggregate, booker_customer, customer_data=None
    ):
        if customer_data is None:
            customer_data = self._create_customer_data_from_booking_owner(
                booker_customer
            )
        if (
            'is_primary' not in customer_data
        ):  # derive is primary from existing customer (booker's customer)
            customer_data['is_primary'] = booker_customer.is_primary
        new_customer_for_guest = booking_aggregate.add_customer(customer_data)
        booking_aggregate.replace_guest_id_in_allocation_data(
            current_guest_id=booker_customer.customer_id,
            new_guest_id=new_customer_for_guest.customer_id,
        )
        self._create_billed_entity(
            booking_aggregate, bill_aggregate, new_customer_for_guest
        )
        self._clear_secondary_be_category(bill_aggregate, booker_customer)
        return new_customer_for_guest

    @audit(audit_type=AuditType.GUEST_MARKED_AS_BOOKER)
    def mark_guest_as_booker(self, booking_aggregate, bill_aggregate, guest_customer):
        booking_owner = booking_aggregate.get_booking_owner()
        if booking_owner.customer_id == guest_customer.customer_id:
            return booking_owner
        booking_id = booking_aggregate.booking.booking_id
        # At a time only one guest can exist as booker.
        if booking_aggregate.is_guest(booking_owner.customer_id):
            raise CustomerMergeError(
                description='At a time only one guest can exist as booker {}:{}'.format(
                    booking_id, booking_owner.customer_id
                )
            )
        self._fail_if_billed_entity_does_not_exist_for_booker(
            bill_aggregate, booking_owner, booking_id
        )
        booking_aggregate.replace_guest_id_in_allocation_data(
            current_guest_id=guest_customer.customer_id,
            new_guest_id=booking_owner.customer_id,
        )
        secondary_be_category = (
            BilledEntityCategory.PRIMARY_GUEST
            if guest_customer.is_primary
            else BilledEntityCategory.CONSUMING_GUESTS
        )
        # update the booking owners secondary category
        bill_aggregate.update_billed_entity_secondary_category(
            booking_owner.billed_entity_id, secondary_be_category
        )
        if guest_customer.billed_entity_id:
            self._deactivate_and_move_charges_out_from_billed_entity(
                bill_aggregate,
                source_be_id=guest_customer.billed_entity_id,
                target_be_id=booking_owner.billed_entity_id,
            )
        if guest_customer.company_billed_entity_id:
            self._deactivate_and_move_charges_out_from_billed_entity(
                bill_aggregate,
                source_be_id=guest_customer.company_billed_entity_id,
                target_be_id=booking_owner.company_billed_entity_id
                or booking_owner.billed_entity_id,
            )
        booking_aggregate.update_is_primary_status_in_customer(
            booking_owner.customer_id, guest_customer.is_primary
        )
        bill_aggregate.replace_consuming_guest_from_charges(
            old_customer=guest_customer.customer_id,
            new_customer=booking_owner.customer_id,
        )
        booking_aggregate.delete_customer(guest_customer.customer_id)
        return booking_owner

    @staticmethod
    def _deactivate_and_move_charges_out_from_billed_entity(
        bill_aggregate, source_be_id, target_be_id
    ):
        bill_aggregate.move_all_charges_and_payments_splits_from_one_billed_entity_to_another(
            source_be_id=source_be_id, target_be_id=target_be_id
        )
        cancelled_billed_entity_ids = bill_aggregate.safe_inactivate_billed_entities(
            [source_be_id], BilledEntityStatus.INACTIVE
        )
        if source_be_id not in cancelled_billed_entity_ids:
            raise CustomerMergeError(
                description='Unable to deactivate existing billed entity accounts of guest {}:{}'.format(
                    bill_aggregate.bill.bill_id, source_be_id
                )
            )

    @staticmethod
    def _fail_if_billed_entity_does_not_exist_for_booker(
        bill_aggregate, booking_owner, booking_id
    ):
        try:
            return bill_aggregate.get_billed_entity(booking_owner.billed_entity_id)
        except ResourceNotFound:
            raise CustomerMergeError(
                description='Billed entity not found for booker {}:{}'.format(
                    booking_id, booking_owner.customer_id
                )
            )

    def merge_customer_of_guest_and_booker_based_on_name(self, booking_aggregate):
        booking_owner = booking_aggregate.get_booking_owner()
        if booking_aggregate.is_guest(
            booking_owner.customer_id
        ) or not self.billed_entity_service.is_booking_owner_eligible_for_billing(
            booking_aggregate
        ):
            return
        for guest_id in booking_aggregate.get_customers_for_guest_stays(ids=True):
            if not guest_id:
                continue
            guest_customer = booking_aggregate.get_customer(guest_id)
            if guest_customer.name.has_case_insensitive_match(booking_owner.name):
                booking_aggregate.replace_guest_id_in_allocation_data(
                    current_guest_id=guest_customer.customer_id,
                    new_guest_id=booking_owner.customer_id,
                )
                booking_aggregate.update_is_primary_status_in_customer(
                    booking_owner.customer_id, guest_customer.is_primary
                )
                booking_aggregate.delete_customer(guest_customer.customer_id)
                return

    def handle_no_show_or_cancellation_action(
        self,
        booking_aggregate,
        bill_aggregate,
        cancelled_room_stay_id_guest_stay_ids_map=None,
        guest_ids=None,
    ):
        assert (
            cancelled_room_stay_id_guest_stay_ids_map or guest_ids
        ), "both cancelled_room_stay_id_guest_stay_ids_map and guest_ids can't be None"
        guest_ids = (
            booking_aggregate.extract_guest_ids(
                cancelled_room_stay_id_guest_stay_ids_map
            )
            if guest_ids is None
            else set(guest_ids)
        )
        booking_owner_id = booking_aggregate.booking.owner_id
        if booking_owner_id in guest_ids:
            booking_owner = booking_aggregate.get_booking_owner()
            new_customer = self.un_mark_guest_as_booker(
                booking_aggregate, bill_aggregate, booking_owner
            )
            guest_ids.remove(booking_owner_id)
            guest_ids.add(new_customer.customer_id)

    def _create_billed_entity(self, booking_aggregate, bill_aggregate, customer):
        status = BilledEntityStatus.ACTIVE
        guest_status = booking_aggregate.get_guest_status(customer.customer_id)
        if guest_status in (BookingStatus.NOSHOW, BookingStatus.CANCELLED):
            status = (
                BilledEntityStatus.CANCEL
                if guest_status == BookingStatus.CANCELLED
                else BilledEntityStatus.NOSHOW
            )
        self.billed_entity_service.add_billed_entity_for_guest(
            customer, bill_aggregate, status=status
        )

    @staticmethod
    def _create_customer_data_from_booking_owner(booking_owner: Customer):
        customer_data = booking_owner.__dict__
        customer_data['first_name'] = booking_owner.first_name
        customer_data['last_name'] = booking_owner.last_name
        return customer_data
