import datetime
from typing import Dict, Set

from treebo_commons.utils import dateutils

from prometheus.application.helpers.invoice_bill_to_service import InvoiceBillToService
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.factories.invoice_factory import InvoiceFactory
from ths_common.constants.billing_constants import ChargeTypes
from ths_common.value_objects import InvoiceBillToInfo


class CreatePreviewInvoiceCommand(object):
    def __init__(
        self,
        bill_aggregate,
        booking_aggregate,
        invoice_datetime,
        generated_by,
        generation_channel,
        issued_by,
        issued_by_type,
        issued_to_type,
        grouped_charge_maps: Dict[BillAggregate.BilledEntityGroupKey, Dict[int, Set]],
        grouped_sku_categories,
        proforma_invoice=False,
        booked_allowances_to_be_excluded=None,
    ):
        self.bill_aggregate = bill_aggregate
        self.booking_aggregate = booking_aggregate
        self.invoice_date = (
            dateutils.to_date(invoice_datetime)
            if isinstance(invoice_datetime, datetime.datetime)
            else invoice_datetime
        )
        self.generated_by = generated_by
        self.generation_channel = generation_channel
        self.issued_by = issued_by
        self.issued_by_type = issued_by_type
        self.issued_to_type = issued_to_type
        self.grouped_charge_maps = grouped_charge_maps
        self.grouped_sku_categories = grouped_sku_categories
        self.proforma_invoice = proforma_invoice
        self.booked_allowances_to_be_excluded = booked_allowances_to_be_excluded

    def execute(self):
        return [
            self._create_preview_invoice(group_key, charge_map)
            for group_key, charge_map in self.grouped_charge_maps.items()
            if charge_map
        ]

    def _create_preview_invoice(
        self, group_key: BillAggregate.BilledEntityGroupKey, charge_map
    ):
        user_info_map = InvoiceBillToService.compute_charge_to_info(
            charge_map, self.bill_aggregate, self.booking_aggregate
        )
        bill_to_info = InvoiceBillToInfo.create_from_billed_entity(
            self.bill_aggregate.get_billed_entity(
                group_key.billed_entity_account.billed_entity_id
            ),
            self.booking_aggregate,
        )
        is_reissue_allowed = self.bill_aggregate.is_reissue_allowed(
            charge_map, self.grouped_sku_categories
        )

        return InvoiceFactory.create_preview_invoice(
            bill_aggregate=self.bill_aggregate,
            charge_split_map=charge_map,
            bill_to=bill_to_info,
            generated_by=self.generated_by,
            generation_channel=self.generation_channel,
            user_info_map=user_info_map,
            invoice_date=self.invoice_date,
            bill_to_type=self.bill_aggregate.derive_bill_to_type_for_group_key(
                group_key
            ),
            allowed_charge_types=[ChargeTypes(group_key.charge_type)],
            allowed_charge_to_ids=None,
            issued_by_type=self.issued_by_type,
            issued_to_type=self.issued_to_type,
            issued_by=self.issued_by,
            grouped_sku_categories=self.grouped_sku_categories,
            proforma_invoice=self.proforma_invoice,
            billed_entity_account=group_key.billed_entity_account,
            booked_allowances_to_be_excluded=self.booked_allowances_to_be_excluded,
            is_reissue_allowed=is_reissue_allowed,
        )
