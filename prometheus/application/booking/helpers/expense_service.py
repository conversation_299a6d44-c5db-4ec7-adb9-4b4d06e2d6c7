from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.booking.helpers.inclusion_charge_service import (
    InclusionChargeService,
)
from prometheus.application.helpers.billed_entity_helper import (
    attach_default_billed_entity_account_to_room_stay_charges,
)
from prometheus.application.helpers.expense_item_helper import ExpenseItemHelper
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto.charge_data import ChargeData
from prometheus.domain.billing.services.tax_service import TaxService
from prometheus.domain.booking.dtos import ExpenseDto
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)
from prometheus.domain.catalog.repositories import SkuCategoryRepository
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        TaxService,
        InclusionChargeService,
        ExpenseItemRepository,
        SkuCategoryRepository,
        ExpenseItemHelper,
    ]
)
class ExpenseApplicationService(object):
    def __init__(
        self,
        tax_service,
        inclusion_charge_service,
        expense_item_repository,
        sku_category_repository,
        expense_item_helper,
    ):
        self.tax_service = tax_service
        self.inclusion_charge_service = inclusion_charge_service
        self.expense_item_repository = expense_item_repository
        self.sku_category_repository = sku_category_repository
        self.expense_item_helper = expense_item_helper

    def create_charges_for_expenses(
        self,
        bill_aggregate,
        expenses,
        addon,
        expense_item,
        room_stay,
        booking_aggregate,
        sku_category,
    ):
        charges = [
            ChargeData.create_for_addon_expense(
                expense, addon, expense_item, room_stay, sku_category
            )
            for expense in expenses
        ]
        tax_updated_charge_dtos = self.tax_service.update_taxes(
            charges,
            buyer_gst_details=booking_aggregate.booking_owner_gst_details(),
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, crs_context.get_hotel_context()
            ),
            hotel_id=crs_context.get_hotel_context().hotel_id,
        )
        updated_charges = bill_aggregate.add_charges(tax_updated_charge_dtos)
        attach_default_billed_entity_account_to_room_stay_charges(
            room_stay, updated_charges, booking_aggregate, bill_aggregate
        )
        for expense, charge in zip(expenses, updated_charges):
            expense.charge_id = charge.charge_id

        return updated_charges

    # This being used by UpdateRoomStayDateCommand and UpdateRoomStayRatePlanCommand
    def create_expense_and_charge_for_inclusions(
        self, rate_plan_inclusions, booking_aggregate, bill_aggregate, room_stay
    ):
        expense_items = self.expense_item_repository.load_all()
        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in self.sku_category_repository.load_all()
        }

        inclusion_charges = []
        if not rate_plan_inclusions:
            return inclusion_charges

        expense_items = self.expense_item_helper.add_missing_skus_if_sku_not_found(
            expense_items=expense_items,
            rate_plan_inclusions=rate_plan_inclusions,
        )

        for rate_plan_inclusion in rate_plan_inclusions:
            expense_item = next(
                (ex for ex in expense_items if ex.sku_id == rate_plan_inclusion.sku_id),
                None,
            )
            if not expense_item:
                raise ValidationException(ApplicationErrors.SKU_NOT_FOUND)
            expense_dtos = self._create_expenses_for_dates(
                rate_plan_inclusion, expense_item, room_stay
            )
            expenses = booking_aggregate.add_expenses(expense_dtos)
            sku_category = grouped_sku_categories.get(expense_item.sku_category_id)
            charges = self.inclusion_charge_service.create_charges_for_inclusions(
                bill_aggregate,
                expenses,
                expense_item,
                room_stay,
                booking_aggregate,
                sku_category,
                rate_plan_inclusion.pretax_amount,
                rate_plan_inclusion.posttax_amount,
            )
            inclusion_charges.extend(charges)
        return inclusion_charges

    @staticmethod
    def _create_expenses_for_dates(rate_plan_inclusion, expense_item, room_stay):
        expenses = []
        for _ in range(rate_plan_inclusion.quantity):
            date_range = dateutils.date_range(
                rate_plan_inclusion.start_date,
                rate_plan_inclusion.end_date,
                end_inclusive=True,
            )
            expenses.extend(
                [
                    ExpenseDto.from_rate_plan_inclusion(
                        rate_plan_inclusion, expense_item, date, room_stay
                    )
                    for date in date_range
                ]
            )
        return expenses
