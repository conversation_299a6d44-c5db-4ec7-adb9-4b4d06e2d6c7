from typing import List

from treebo_commons.utils import dateutils

from prometheus.domain.booking.exceptions import UnsupportedOccupancyError
from ths_common.value_objects import RoomStayConfig


def validate_max_occupancy_for_room_stays(
    hotel_aggregate, room_stay_configs: List[RoomStayConfig]
):
    unsupported_occupancies = []
    for room_stay_config in room_stay_configs:
        date_wise_occupancies = room_stay_config.date_wise_occupancies
        room_type_id = room_stay_config.room_type_id
        for date_, occupancy in date_wise_occupancies.items():
            if not hotel_aggregate.occupancy_supported(room_type_id, occupancy):
                max_occupancy = hotel_aggregate.get_room_type_config(
                    room_type_id
                ).max_occupancy
                unsupported_occupancies.append(
                    (room_type_id, date_, max_occupancy, occupancy)
                )

    if unsupported_occupancies:
        occupancy_breach = []
        for error in unsupported_occupancies:
            occupancy_breach.append(
                dict(
                    room_type_id=error[0],
                    date=dateutils.date_to_ymd_str(error[1]),
                    max_occupancy=dict(adult=error[2].adult, child=error[2].child),
                    requested_occupancy=dict(
                        adult=error[3].adult, child=error[3].child
                    ),
                )
            )
        raise UnsupportedOccupancyError(
            extra_payload=dict(
                hotel_id=hotel_aggregate.hotel.hotel_id,
                occupancy_breach=occupancy_breach,
            )
        )
