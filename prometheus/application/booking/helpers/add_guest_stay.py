import datetime
from typing import List

from treebo_commons.utils import dateutils

from object_registry import locate_instance
from prometheus.application.booking.helpers import (
    price_validators,
    rate_plan_validator,
    room_stay_validators,
)
from prometheus.application.booking.helpers.expense_service import (
    ExpenseApplicationService,
)
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_from_billed_entity,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.common.helpers import occupancy_change_handler
from prometheus.domain.booking.dtos.guest_stay_data import GuestStayData
from prometheus.domain.booking.entities import RoomStay
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.exceptions import InvalidStayDatesError
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import GuestStayFacts
from ths_common.constants.billing_constants import ChargeStatus
from ths_common.exceptions import ValidationException
from ths_common.value_objects import PriceData


class AddGuestStayCommand(object):
    """
    Adds one or more guest stays to a room stay. Doesn't allow the new guest stay to have their checkin checkout
    dates, outside of room stay stay date window.
    """

    def __init__(
        self,
        booking_aggregate,
        room_stay,
        new_guest_stays,
        new_room_stay_prices,
        bill_aggregate,
        hotel_aggregate,
        user_data,
        hotel_context,
        tax_service,
        billed_entity_service,
        charge_edit_service,
        room_stay_price_change_handler,
        rate_plan_inclusions=None,
    ):
        self.booking_aggregate = booking_aggregate
        if self.booking_aggregate.is_cancelled() or self.booking_aggregate.is_noshow():
            raise ValidationException(ApplicationErrors.INVALID_BOOKING_FOR_EDIT)

        self.room_stay = room_stay
        if self.room_stay.is_cancelled() or self.room_stay.is_noshow():
            raise ValidationException(ApplicationErrors.INVALID_ROOM_FOR_ADD_GUEST)

        self.new_guest_stays = new_guest_stays
        self.new_room_stay_prices = new_room_stay_prices
        self.bill_aggregate = bill_aggregate
        self.hotel_aggregate = hotel_aggregate
        self.user_data = user_data
        self.hotel_context = hotel_context

        self.tax_service = tax_service
        self.billed_entity_service = billed_entity_service
        self.charge_edit_service = charge_edit_service
        self.room_stay_price_change_handler = room_stay_price_change_handler
        self.rate_plan_inclusions = rate_plan_inclusions
        self.expense_app_service = locate_instance(ExpenseApplicationService)

    def execute(self):
        old_date_wise_occupancy = self.room_stay.date_wise_occupancies
        old_charges = self.bill_aggregate.filter_and_get_charges(
            self.room_stay.charge_ids
        )
        guest_stays = []

        # Load booking and corresponding bill
        for new_guest_stay in self.new_guest_stays:
            RuleEngine.action_allowed(
                action="add_guest_stay",
                facts=GuestStayFacts(
                    user_type=self.user_data.user_type,
                    action_payload=new_guest_stay,
                    booking_aggregate=self.booking_aggregate,
                    bill_aggregate=self.bill_aggregate,
                    hotel_context=self.hotel_context,
                    room_stay=self.room_stay,
                ),
                fail_on_error=True,
            )

            guest_stay_dto = GuestStayData.create(
                new_guest_stay,
                room_stay_checkin=self.room_stay.checkin_date,
                room_stay_checkout=self.room_stay.checkout_date,
                override_checkin_time=self.hotel_context.checkin_time,
                override_checkout_time=self.hotel_context.checkout_time,
            )

            # Add guest stay to booking and update charge_ids in room stay
            # New guest are added in CONFIRMED state. The booking which is already in checked-in state should move to
            # part checked in state.
            guest_stay = self.booking_aggregate.add_guest_stay(
                room_stay_id=self.room_stay.room_stay_id, guest_stay=guest_stay_dto
            )
            guest_stay.refresh_allowed_actions(
                self.booking_aggregate._is_soft_booking()
            )
            guest_stays.append(guest_stay)
        self.billed_entity_service.create_or_update_billed_entity_data_for_allocated_guest_stays(
            booking_aggregate=self.booking_aggregate,
            bill_aggregate=self.bill_aggregate,
            guest_stays=guest_stays,
        )
        room_stay_validators.validate_max_occupancy_for_room_stays(
            self.hotel_aggregate, [self.room_stay.room_stay_config]
        )

        edited_charges = self._update_bill_for_guest_stay_add(
            self.new_room_stay_prices, old_date_wise_occupancy, guest_stays
        )

        self.booking_aggregate.update_room_rents(
            self.room_stay.room_stay_id, edited_charges
        )
        self.room_stay_price_change_handler.on_room_stay_charge_edit(
            self.booking_aggregate,
            self.bill_aggregate,
            self.room_stay,
            edited_charges,
            delete_rate_plan_addons=True,
        )
        if (
            self.rate_plan_inclusions
            and len(self.rate_plan_inclusions) > 0
            and self.booking_aggregate.rate_plans
        ):
            price_validators.validate_price_and_inclusions(
                self.new_room_stay_prices, self.rate_plan_inclusions
            )
            rate_plan_validator.validate_inclusion_with_duration(
                self.rate_plan_inclusions,
                self.room_stay.checkin_date,
                self.room_stay.checkout_date,
            )
            self.expense_app_service.create_expense_and_charge_for_inclusions(
                self.rate_plan_inclusions,
                self.booking_aggregate,
                self.bill_aggregate,
                self.room_stay,
            )

        return guest_stays

    def _update_bill_for_guest_stay_add(
        self, new_room_stay_prices, old_date_wise_occupancy, guest_stays
    ):
        new_date_wise_occupancy = self.room_stay.date_wise_occupancies

        # Get Dates where occupancy has changed
        dates_with_changed_occupancy = [
            d
            for d, occ in old_date_wise_occupancy.items()
            if d in new_date_wise_occupancy and occ != new_date_wise_occupancy[d]
        ]

        charge_ids = self.room_stay.get_charges_for_stay_dates(
            dates_with_changed_occupancy
        )
        (
            non_consumed_charge_ids,
            non_consumed_charge_dates,
        ) = self._get_non_consumed_charges_and_dates(
            charge_ids, dates_with_changed_occupancy, self.bill_aggregate
        )

        consumed_charge_dates = set(dates_with_changed_occupancy) - set(
            non_consumed_charge_dates
        )

        for guest_stay in guest_stays:
            for date_ in dateutils.date_range(
                guest_stay.stay_start, guest_stay.stay_end
            ):
                if date_ in consumed_charge_dates:
                    raise InvalidStayDatesError(
                        error=BookingErrors.GUEST_STAY_ADDITION_ON_CONSUMED_CHARGE,
                        extra_payload=dict(
                            consumed_charge_dates=[
                                dateutils.date_to_ymd_str(d_)
                                for d_ in consumed_charge_dates
                            ]
                        ),
                    )

        edited_charges = self._update_room_stay_charge(
            self.bill_aggregate,
            non_consumed_charge_dates,
            new_room_stay_prices,
            self.room_stay,
            self.booking_aggregate,
        )
        occupancy_change_handler.update_occupancy_details_in_booked_charges(
            self.booking_aggregate,
            self.bill_aggregate,
            room_stay=self.room_stay,
        )
        return edited_charges

    def _update_room_stay_charge(
        self,
        bill_aggregate,
        dates_for_price_change,
        new_prices: List[PriceData],
        room_stay: RoomStay,
        booking_aggregate,
    ):
        price_validators.validate_prices_sent_only_for_dates(
            new_prices, dates_for_price_change
        )
        self._populate_gst_details_for_new_prices(new_prices, room_stay)
        return self.charge_edit_service.update_room_stay_charge(
            bill_aggregate, new_prices, room_stay, booking_aggregate
        )

    def _populate_gst_details_for_new_prices(
        self, new_prices: List[PriceData], room_stay: RoomStay
    ):
        for new_price in new_prices:
            charge_id = room_stay.get_charge_for_date(new_price.applicable_date)
            billed_entity = self.bill_aggregate.get_primary_billed_entity_of_charge(
                charge_id
            )
            new_price.buyer_gst_details = get_gst_details_from_billed_entity(
                self.booking_aggregate, billed_entity
            )

    @staticmethod
    def set_prices_with_default_applicable_time_for_stay_charges(prices):
        applicable_time = datetime.time(23, 59)
        for price in prices:
            price.set_applicable_time(applicable_time)

    @staticmethod
    def _get_non_consumed_charges_and_dates(charge_ids, dates, bill_aggregate):
        # Filter dates where charges are already CONSUMED. We don't want to change price for such charges
        consumed_charges = bill_aggregate.filter_and_get_charges(
            charge_ids, allowed_charge_status=[ChargeStatus.CONSUMED]
        )
        consumed_charge_ids = [c.charge_id for c in consumed_charges]
        allowed_charge_cancellation = list(set(charge_ids) - set(consumed_charge_ids))

        consumed_charges_dates = [
            dateutils.to_date(c.applicable_date) for c in consumed_charges
        ]
        final_dates_for_charge_change = list(set(dates) - set(consumed_charges_dates))
        return allowed_charge_cancellation, final_dates_for_charge_change
