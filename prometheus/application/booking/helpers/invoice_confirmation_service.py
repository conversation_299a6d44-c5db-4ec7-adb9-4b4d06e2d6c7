from typing import List

from object_registry import register_instance
from prometheus.application.booking.helpers import invoice_confirmation_preconditions
from prometheus.application.helpers.invoice_issuer_service import InvoiceIssuerService
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.common.helpers.invoice_generation_helper import InvoiceGenerationHelper
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.aggregates.invoice_aggregate import InvoiceAggregate
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.services.invoice_service import InvoiceService
from prometheus.domain.catalog.repositories.reseller_gst_repository import (
    ResellerGstRepository,
)
from prometheus.domain.catalog.repositories.sku_category_repository import (
    SkuCategoryRepository,
)
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from ths_common.constants.billing_constants import InvoiceStatus
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        InvoiceRepository,
        ResellerGstRepository,
        CatalogServiceClient,
        InvoiceService,
        SkuCategoryRepository,
        TenantSettings,
    ]
)
class InvoiceConfirmationService(object):
    def __init__(
        self,
        invoice_repository,
        reseller_gst_repository,
        catalog_service_client,
        invoice_service,
        sku_category_repository,
        tenant_settings,
    ):
        self.invoice_repository = invoice_repository
        self.reseller_gst_detail_repository = reseller_gst_repository
        self.catalog_service_client = catalog_service_client
        self.invoice_service = invoice_service
        self.sku_category_repository = sku_category_repository
        self.tenant_settings = tenant_settings

    def confirm_invoices(
        self, invoice_aggregates, bill_aggregate, booking_aggregate, hotel_context
    ):
        # validate the invoices first
        bill_aggregate.post_eligible_payments(
            hotel_context.current_date(),
            {
                aggregate.invoice.billed_entity_account.billed_entity_id
                for aggregate in invoice_aggregates
            },
        )

        for invoice_aggregate in invoice_aggregates:
            if invoice_aggregate.invoice.status != InvoiceStatus.PREVIEW:
                raise ValidationException(
                    ApplicationErrors.INVOICE_NOT_IN_PREVIEW_STATE_FOR_CHECKOUT,
                    description="Invoice_id: {} should be in preview state."
                    " Current state: {}.".format(
                        invoice_aggregate.invoice.invoice_id,
                        invoice_aggregate.invoice.status,
                    ),
                )

        self._ensure_payment_not_required_for_confirming_invoices(
            bill_aggregate, invoice_aggregates
        )

        reseller_gst_aggregate = self.reseller_gst_detail_repository.load(
            hotel_context.legal_state_id
        )
        (
            issued_by_type,
            is_first_invoice,
        ) = InvoiceIssuerService.get_issuer_type_for_new_invoice(
            bill_aggregate.bill_id,
            bill_aggregate.vendor_id,
            self.invoice_repository,
            self.catalog_service_client,
            booking_aggregate,
            self.tenant_settings,
        )

        if is_first_invoice:
            # TODO: If seller_model changes here, IGST related tax can change
            booking_aggregate.set_seller_model(
                InvoiceIssuerService.get_seller_model(issued_by_type)
            )

        hotel_invoices = []
        for invoice_aggregate in invoice_aggregates:
            # NOTE: Deleted code to re-populate invoice charges, as it didn't make any sense to update it again,
            # since InvoiceCharge are already there in preview invoices
            vendor_details = hotel_context.build_vendor_details()
            issued_by = InvoiceIssuerService.get_issuer(
                issued_by_type,
                vendor_details,
                reseller_gst_aggregate,
                catalog_service_client=self.catalog_service_client,
            )
            (
                bill_aggregate,
                invoice_aggregate,
                hotel_invoice,
            ) = self.invoice_service.confirm_invoice(
                bill_aggregate,
                invoice_aggregate,
                hotel_context.current_date(),
                hotel_context,
                issued_by_type,
                issued_by,
                generate_hotel_invoice=True,
            )

            InvoiceGenerationHelper.populate_actual_check_in_and_checkout_dates_in_invoice(
                invoice_aggregate,
                booking_aggregate,
            )

            if invoice_aggregate.invoice.is_einvoice:
                invoice_aggregate.lock()

            if hotel_invoice:
                hotel_invoices.append(hotel_invoice)

        return invoice_aggregates, hotel_invoices

    @staticmethod
    def _ensure_payment_not_required_for_confirming_invoices(
        bill_aggregate: BillAggregate, invoice_aggregates: List[InvoiceAggregate]
    ):
        # NOTE: Similar method as BookingInvoicingService._get_pending_payment_to_confirm_invoices
        invoice_wise_pending_payments = (
            invoice_confirmation_preconditions.get_pending_payment_to_confirm_invoices(
                bill_aggregate, invoice_aggregates
            )
        )

        pending_payments = []
        for invoice_aggregate in invoice_aggregates:
            account_summary = invoice_wise_pending_payments.get(
                invoice_aggregate.invoice_id
            )

            if account_summary.balance_to_clear_before_checkout > 0:
                pending_payments.append(
                    dict(
                        billed_entity_account=bill_aggregate.billed_entity_account_to_string(
                            invoice_aggregate.invoice.billed_entity_account
                        ),
                        pending_amount=-account_summary.balance_to_clear_before_checkout,
                    )
                )

        if pending_payments:
            extra_payload = dict(pending_payments=pending_payments)
            raise ValidationException(
                error=ApplicationErrors.PAYMENT_PENDING_FOR_CHECKOUT,
                extra_payload=extra_payload,
            )
