from object_registry import register_instance
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.booking.repositories import BookingRepository


@register_instance(
    dependencies=[
        BookingRepository,
    ]
)
class GetExpenseQueryHandler:
    def __init__(
        self,
        booking_repo,
    ):
        self.booking_repository = booking_repo

    @set_hotel_context()
    def handle(self, booking_id, expense_id_gt=None, limit=None, hotel_aggregate=None):
        expenses = self.booking_repository.load_expenses(
            booking_id, expense_id_gt, limit
        )
        max_expense_id = None
        if expense_id_gt == 0 and limit is not None:
            max_expense_id = self.booking_repository.get_max_expense_id(booking_id)
        current_booking_version = self.booking_repository.get_current_booking_version(
            booking_id
        )
        return expenses, current_booking_version, max_expense_id
