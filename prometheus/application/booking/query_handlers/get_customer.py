from object_registry import register_instance
from prometheus.domain.booking.repositories import BookingRepository


@register_instance(dependencies=[BookingRepository])
class GetCustomerQueryHandler:
    def __init__(self, booking_repository: BookingRepository):
        self.booking_repository = booking_repository

    def handle(self, booking_id, customer_id):
        customer, current_booking_version = self.booking_repository.load_customer(
            booking_id, customer_id
        )
        return customer, current_booking_version
