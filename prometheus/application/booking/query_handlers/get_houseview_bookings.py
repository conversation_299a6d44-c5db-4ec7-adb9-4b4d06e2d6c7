from typing import List, <PERSON><PERSON>

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.dtos.houseview_booking_dto import HouseViewBookingDto
from prometheus.domain.booking.repositories import (
    BookingRepository,
    RoomStayOverflowRepository,
)
from prometheus.domain.booking.repositories.web_checkin_repository import (
    WebCheckinRepository,
)
from prometheus.domain.catalog.repositories import HotelRepository


@register_instance(
    dependencies=[
        HotelRepository,
        BookingRepository,
        RoomStayOverflowRepository,
        BillRepository,
        WebCheckinRepository,
    ]
)
class GetHouseViewBookingsQueryHandler:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        booking_repository: BookingRepository,
        room_stay_overflow_repository: RoomStayOverflowRepository,
        bill_repository: BillRepository,
        web_checkin_repository: WebCheckinRepository,
    ):
        self.hotel_repository = hotel_repository
        self.booking_repository = booking_repository
        self.room_stay_overflow_repository = room_stay_overflow_repository
        self.bill_repository = bill_repository
        self.web_checkin_repository = web_checkin_repository

    def handle(
        self,
        hotel_id,
        from_date,
        to_date,
        limit=20,
        offset=0,
        total_count_required=False,
        with_balance_payable=False,
        booking_id=None,
    ) -> Tuple[List[HouseViewBookingDto], int]:
        hotel_dto_for_context = self.hotel_repository.get_hotel_dto_for_context(
            hotel_id
        )
        crs_context.set_basic_hotel_context(hotel_dto_for_context)
        houseview_booking_dtos = (
            self.booking_repository.load_booking_dtos_for_houseview(
                hotel_dto_for_context,
                from_date,
                to_date,
                limit,
                offset,
                booking_id=booking_id,
            )
        )

        booking_ids = [dto.booking_id for dto in houseview_booking_dtos]
        booking_wise_overflowing_room_stay_ids = self.room_stay_overflow_repository.get_overflowed_room_stay_ids_for_bookings(
            booking_ids
        )

        bill_wise_summary = dict()
        if with_balance_payable:
            bill_wise_summary = self.bill_repository.load_bill_wise_summary(
                bill_ids=[booking_dto.bill_id for booking_dto in houseview_booking_dtos]
            )

        booking_wise_web_checkin_status = (
            self.web_checkin_repository.get_web_checkin_statuses_for_bookings(
                booking_ids
            )
        )
        for houseview_booking_dto in houseview_booking_dtos:
            overflowing_room_stay_ids = booking_wise_overflowing_room_stay_ids[
                houseview_booking_dto.booking_id
            ]
            houseview_booking_dto.tag_room_stay_overflows(overflowing_room_stay_ids)
            houseview_booking_dto.update_web_checkin_status(
                booking_wise_web_checkin_status.get(houseview_booking_dto.booking_id)
            )
            houseview_booking_dto.set_room_allocation_houseview_labels()
            if with_balance_payable:
                bill_summary = bill_wise_summary[houseview_booking_dto.bill_id]
                houseview_booking_dto.update_balance_to_clear_before_checkout(
                    bill_summary.balance_to_clear_before_checkout
                )

        total_count = None
        if total_count_required:
            if offset == 0 and len(houseview_booking_dtos) < limit:
                total_count = len(houseview_booking_dtos)
            else:
                total_count = (
                    self.booking_repository.get_booking_dtos_count_for_houseview(
                        hotel_id, from_date, to_date
                    )
                )

        return houseview_booking_dtos, total_count
