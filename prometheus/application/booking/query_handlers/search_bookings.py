from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.services.booking_search_executor import (
    BookingSearchExecutor,
)
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.repositories import (
    BookingRepository,
    RoomStayOverflowRepository,
)
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.inventory.repositories import RoomAllotmentRepository
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from ths_common.constants.inventory_constants import AllottedFor
from ths_common.utils.common_utils import group_list


@register_instance(
    dependencies=[
        BookingRepository,
        HotelRepository,
        RoomStayOverflowRepository,
        NewrelicServiceClient,
        BookingSearchExecutor,
        RoomAllotmentRepository,
    ]
)
class SearchBookingQueryHandler:
    def __init__(
        self,
        booking_repo,
        hotel_repo,
        room_stay_overflow_repo,
        alerting_service,
        booking_search_executor: BookingSearchExecutor,
        room_allotment_repository: RoomAllotmentRepository,
    ):
        self.booking_repository = booking_repo
        self.hotel_repository = hotel_repo
        self.room_stay_overflow_repository = room_stay_overflow_repo
        self.alerting_service = alerting_service
        self.booking_search_executor: BookingSearchExecutor = booking_search_executor
        self.room_allotment_repository: RoomAllotmentRepository = (
            room_allotment_repository
        )

    def handle(self, search_query: BookingSearchQuery, user_data, only_count=False):
        if only_count:
            return self.count_bookings(search_query)

        self.alerting_service.record_event(
            "booking_search", {"hotel_id": search_query.hotel_id}
        )
        response = []
        hotel_id = search_query.hotel_id

        if search_query.checkin_guest_expected_today:
            hotel_aggregate = self.hotel_repository.load(hotel_id)
            crs_context.set_hotel_context(hotel_aggregate)
            booking_aggregates = (
                self.booking_repository.load_booking_with_today_expected_checkin(
                    hotel_id,
                    search_query.limit,
                    search_query.offset,
                    user_data=user_data,
                )
            )

        elif search_query.checkout_guest_expected_today:
            hotel_aggregate = self.hotel_repository.load(hotel_id)
            crs_context.set_hotel_context(hotel_aggregate)
            booking_aggregates = (
                self.booking_repository.load_booking_with_today_expected_checkout(
                    hotel_id,
                    search_query.limit,
                    search_query.offset,
                    user_data=user_data,
                )
            )

        elif search_query.has_pending_web_checkins:
            hotel_aggregate = self.hotel_repository.load(hotel_id)
            crs_context.set_hotel_context(hotel_aggregate)
            booking_aggregates = (
                self.booking_repository.load_bookings_with_pending_web_checkins(
                    hotel_id, search_query
                )
            )

        elif search_query.room_no:
            room_allotment = (
                self.room_allotment_repository.load_checked_in_room_allotment(
                    hotel_id, search_query.room_no
                )
            )
            booking_aggregates = []
            if room_allotment and room_allotment.allotted_for == AllottedFor.STAY.value:
                booking_aggregates = [
                    self.booking_repository.load_booking_for_room_allocation_id(
                        room_allotment.allotment_id
                    )
                ]

        else:
            booking_aggregates = self.booking_search_executor.execute(
                search_query, user_data=user_data
            )

        if not booking_aggregates:
            return response

        hotel_ids = {
            booking_aggregate.booking.hotel_id
            for booking_aggregate in booking_aggregates
        }
        if len(hotel_ids) == 1:
            hotel_id = booking_aggregates[0].booking.hotel_id
            hotel_aggregate = self.hotel_repository.load(hotel_id)
            crs_context.set_hotel_context(hotel_aggregate)
            single_hotel_search = True
        else:
            single_hotel_search = False

        room_stay_overflow_aggregates = (
            self.room_stay_overflow_repository.get_overflowed_room_stays_for_bookings(
                [aggregate.booking_id for aggregate in booking_aggregates]
            )
        )
        grouped_room_stay_overflows = group_list(
            [
                aggregate.room_stay_overflow
                for aggregate in room_stay_overflow_aggregates
            ],
            'booking_id',
        )

        for booking_aggregate in booking_aggregates:
            room_stay_overflows = grouped_room_stay_overflows.get(
                booking_aggregate.booking_id, []
            )
            overflowed_room_stay_ids = [
                overflow.room_stay_id for overflow in room_stay_overflows
            ]
            booking_aggregate.tag_room_stay_overflows(overflowed_room_stay_ids)
            if single_hotel_search:
                booking_aggregate.refresh_allowed_actions()
            response.append(booking_aggregate)
        return response

    def count_bookings(self, search_query: BookingSearchQuery):
        hotel_id = search_query.hotel_id
        if search_query.checkin_guest_expected_today:
            count = self.booking_repository.count_booking_with_today_expected_checkin(
                hotel_id
            )
        elif search_query.checkout_guest_expected_today:
            count = self.booking_repository.count_booking_with_today_expected_checkout(
                hotel_id
            )
        elif search_query.has_pending_web_checkins:
            count = self.booking_repository.count_bookings_with_pending_web_checkins(
                hotel_id
            )
        else:
            count = self.booking_search_executor.execute(search_query, count_only=True)
        return count
