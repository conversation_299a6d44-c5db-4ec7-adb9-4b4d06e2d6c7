from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.booking.repositories.web_checkin_repository import (
    WebCheckinRepository,
)
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository


@register_instance(
    dependencies=[HotelRepository, HotelConfigRepository, WebCheckinRepository]
)
class GetPendingWebCheckinQueryHandler:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        web_checkin_repository: WebCheckinRepository,
    ):
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.web_checkin_repository = web_checkin_repository

    def handle(self, hotel_id):
        crs_context.set_hotel_context(
            self.hotel_repository.load(hotel_id),
            hotel_config_aggregate=self.hotel_config_repository.load(hotel_id),
        )
        pending_web_checkin_booking_ids = (
            self.web_checkin_repository.load_pending_web_checkin_booking_ids(
                hotel_id=hotel_id
            )
        )
        return pending_web_checkin_booking_ids
