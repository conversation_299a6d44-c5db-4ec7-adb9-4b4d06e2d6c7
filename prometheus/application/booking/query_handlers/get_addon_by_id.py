from object_registry import register_instance
from prometheus.domain.booking.repositories import AddonRepository


@register_instance(
    dependencies=[
        AddonRepository,
    ]
)
class GetAddonByIdQueryHandler:
    def __init__(self, addon_repository):
        self.addon_repository = addon_repository

    def handle(self, booking_id, addon_id):
        return self.addon_repository.load(addon_id, booking_id)
