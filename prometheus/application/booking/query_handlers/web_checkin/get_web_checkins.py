from object_registry import register_instance
from prometheus.domain.booking.repositories.web_checkin_repository import (
    WebCheckinRepository,
)


@register_instance(dependencies=[WebCheckinRepository])
class GetWebCheckinsForBookingQueryHandler:
    def __init__(self, web_checkin_repository: WebCheckinRepository):
        self.web_checkin_repository = web_checkin_repository

    def handle(self, booking_ids):
        return self.web_checkin_repository.load_web_checkins_from_booking_ids(
            booking_ids=booking_ids
        )
