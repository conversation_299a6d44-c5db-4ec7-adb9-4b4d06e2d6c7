from object_registry import register_instance
from prometheus.application.booking.command_handlers.state_transitions.cancel_booking_action import (
    CancelBookingActionCommandHandler,
)
from prometheus.application.booking.command_handlers.state_transitions.no_show_booking_action import (
    NoShowBookingActionCommandHandler,
)
from prometheus.application.booking.command_handlers.state_transitions.reverse_checkin_guest import (
    ReverseCheckinCommandHandler,
)
from prometheus.application.booking.command_handlers.state_transitions.reverse_checkout_guest import (
    ReverseCheckoutCommandHandler,
)
from prometheus.application.booking.query_handlers.get_booking_action import (
    GetBookingActionQueryHandler,
)
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.billing.repositories import InvoiceRepository
from prometheus.domain.booking.repositories import (
    BookingActionRepository,
    BookingRepository,
)
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository


@register_instance(
    dependencies=[
        BookingActionRepository,
        BookingRepository,
        HotelRepository,
        HotelConfigRepository,
        InvoiceRepository,
        ReverseCheckinCommandHandler,
        ReverseCheckoutCommandHandler,
        CancelBookingActionCommandHandler,
        NoShowBookingActionCommandHandler,
    ]
)
class GetBookingActionsQueryHandler(GetBookingActionQueryHandler):
    @set_hotel_context()
    def handle(self, booking_id, user_data, hotel_aggregate=None):
        booking_action_aggregates = (
            self.booking_action_repository.load_all_actions_for_booking(booking_id)
        )
        latest_reversible_action_aggregate = next(
            (
                aggregate
                for aggregate in booking_action_aggregates
                if aggregate.booking_action.is_reversible
            ),
            None,
        )

        if latest_reversible_action_aggregate:
            booking_aggregate = self.booking_repository.load(
                booking_id, skip_customers=True
            )
            self.update_reversal_allowed(
                booking_aggregate, latest_reversible_action_aggregate, user_data
            )
            current_booking_version = booking_aggregate.current_version()
        else:
            current_booking_version = (
                self.booking_repository.get_current_booking_version(booking_id)
            )
        return booking_action_aggregates, current_booking_version
