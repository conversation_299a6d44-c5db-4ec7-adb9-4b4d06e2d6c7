import urllib

from object_registry import register_instance
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.booking.repositories.attachment_repository import (
    AttachmentRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts


@register_instance(
    dependencies=[
        SignedUrlGenerator,
        AttachmentRepository,
    ]
)
class GetAttachmentByIdQueryHandler:
    DefaultSignedUrlExpirationHours = 2
    DefaultSignedUrlExpirationSeconds = DefaultSignedUrlExpirationHours * 3600

    def __init__(
        self,
        signed_url_generator: SignedUrlGenerator,
        attachment_repository: AttachmentRepository,
    ):
        self.signed_url_generator = signed_url_generator
        self.attachment_repository = attachment_repository

    @set_hotel_context()
    def handle(self, booking_id, attachment_id, user_data, hotel_aggregate=None):
        attachment_aggregate = self.attachment_repository.load(
            attachment_id, booking_id, user_data
        )
        if RuleEngine.action_allowed(
            action="access_attachment",
            facts=Facts(
                user_type=attachment_aggregate.user_data.user_type,
                attachment_aggregate=attachment_aggregate,
            ),
            fail_on_error=False,
        ):
            signed_url, _ = self.signed_url_generator.generate_signed_url(
                urllib.parse.unquote(attachment_aggregate.attachment.original_url)
            )
            attachment_aggregate.attachment.signed_url = signed_url
        return attachment_aggregate
