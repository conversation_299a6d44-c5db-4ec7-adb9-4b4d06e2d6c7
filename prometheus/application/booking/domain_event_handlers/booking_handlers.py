from object_registry import locate_instance
from prometheus.core.domainbus.events.events import InventoryBlockReleaseEvent


def on_inventory_block_release(event: InventoryBlockReleaseEvent):
    from prometheus.application.booking.command_handlers.expenses.expense_service_handler import (
        ExpenseServiceHandlerOrchestrator,
    )

    expense_service_orchestrator = locate_instance(ExpenseServiceHandlerOrchestrator)
    expense_service_orchestrator.handle_inventory_block_release_side_effects(
        event.blocks_released
    )


def register_domain_bus_event_handlers(bus):
    bus.subscribe(InventoryBlockReleaseEvent, on_inventory_block_release)
