import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit, audit_housekeeping
from prometheus.application.booking.command_handlers.allocate_room import (
    AllocateRoomCommandHandler,
)
from prometheus.application.booking.command_handlers.helpers.update_room_stay_helpers import (
    UpdateRoomStayHelpers,
)
from prometheus.application.booking.command_handlers.update_room_stay_dates import (
    UpdateRoomStayDatesCommandHandler,
)
from prometheus.application.booking.command_handlers.update_room_stay_rate_plan import (
    UpdateRoomStayRatePlanCommandHandler,
)
from prometheus.application.booking.dtos.room_stay_update_dto import RoomStayUpdateDto
from prometheus.application.decorators import session_manager
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.inventory.inventory_requirement_service import (
    InventoryRequirementService,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.exceptions import ValidationException

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        UpdateRoomStayDatesCommandHandler,
        UpdateRoomStayRatePlanCommandHandler,
        AllocateRoomCommandHandler,
        UpdateRoomStayHelpers,
    ]
)
class UpdateRoomStayCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        update_room_stay_date_command_handler: UpdateRoomStayDatesCommandHandler,
        update_room_stay_rate_plan_command_handler: UpdateRoomStayRatePlanCommandHandler,
        allocate_room_command_handler: AllocateRoomCommandHandler,
        update_room_stay_helper: UpdateRoomStayHelpers,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.inventory_requirement_service = InventoryRequirementService()
        self.update_room_stay_date_command_handler = (
            update_room_stay_date_command_handler
        )
        self.update_room_stay_rate_plan_command_handler = (
            update_room_stay_rate_plan_command_handler
        )
        self.allocate_room_command_handler = allocate_room_command_handler
        self.update_room_stay_helper = update_room_stay_helper

    @session_manager(commit=True)
    @audit_housekeeping(action_performed="room_change")
    def handle(
        self,
        booking_id,
        room_stay_id,
        booking_version,
        room_stay_update_data,
        user_data,
    ):
        """
        :param booking_id:
        :param room_stay_id:
        :param booking_version:
        :param room_stay_update_data: The dictionary constructed from UpdateRoomStaySchema
        :param user_data:
        :return:
        """

        if room_stay_update_data.get('checkin_date') or room_stay_update_data.get(
            'checkout_date'
        ):
            return self.update_room_stay_date_command_handler.handle(
                booking_id,
                room_stay_id,
                booking_version,
                room_stay_update_data,
                user_data,
            )
        elif room_stay_update_data.get('room_type') or room_stay_update_data.get(
            'room_allocation'
        ):
            return self.allocate_room_command_handler.handle(
                booking_id,
                room_stay_id,
                booking_version,
                room_stay_update_data,
                user_data,
            )
        elif room_stay_update_data.get('rate_plan') or room_stay_update_data.get(
            'prices'
        ):
            return self.update_room_stay_rate_plan_command_handler.handle(
                booking_id,
                room_stay_id,
                booking_version,
                room_stay_update_data,
                user_data,
            )

        else:
            (
                bill_aggregate,
                booking_aggregate,
                hotel_aggregate,
                _,
                _,
                room_stay,
            ) = self._load_aggregates_for_update(
                booking_id, booking_version, room_stay_id, user_data
            )

            room_stay_update_dto = RoomStayUpdateDto(
                room_stay,
                room_stay_update_data,
                self.update_room_stay_helper.derive_checkin_datetime_for_new_room_allocation(
                    room_stay
                ),
            )

            if room_stay_update_dto.is_update_for_disallow_charge_addition():
                self.update_room_stay_disallow_charge_addition(
                    booking_aggregate, [room_stay_update_dto]
                )

                self.bill_repository.update(bill_aggregate)
                self.booking_repository.update(booking_aggregate)

                IntegrationEventApplicationService.create_booking_updated_event(
                    booking_aggregate=booking_aggregate,
                    bill_aggregate=bill_aggregate,
                    user_action="update_disallow_charge_addition",
                )

                room_stay.refresh_allowed_actions(booking_aggregate._is_soft_booking())

            return room_stay, booking_aggregate.current_version()

    def _load_aggregates_for_update(
        self, booking_id, booking_version, room_stay_id, user_data
    ):
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, user_data=user_data
        )
        booking_aggregate.fail_if_outdated_version(booking_version)
        if booking_aggregate.is_cancelled() or booking_aggregate.is_noshow():
            raise ValidationException(ApplicationErrors.INVALID_BOOKING_FOR_EDIT)

        hotel_id = booking_aggregate.booking.hotel_id
        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )

        room_stay = booking_aggregate.get_room_stay(room_stay_id)
        if room_stay.is_cancelled() or room_stay.is_noshow():
            raise ValidationException(ApplicationErrors.INVALID_ROOM_FOR_EDIT)

        hotel_aggregate = crs_context_middleware.set_hotel_context(hotel_id)

        hotel_context = crs_context.get_hotel_context()
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)
        return (
            bill_aggregate,
            booking_aggregate,
            hotel_aggregate,
            hotel_context,
            hotel_id,
            room_stay,
        )

    @audit(audit_type=AuditType.DISALLOW_CHARGE_ADDITION_CHANGED)
    def update_room_stay_disallow_charge_addition(
        self, booking_aggregate, room_stay_update_dtos
    ):
        for room_stay_update_dto in room_stay_update_dtos:
            room_stay = room_stay_update_dto.room_stay
            booking_aggregate.update_room_stay_disallow_charge_addition(
                room_stay.room_stay_id,
                room_stay_update_dto.room_stay_update_data.get(
                    'disallow_charge_addition'
                ),
            )
