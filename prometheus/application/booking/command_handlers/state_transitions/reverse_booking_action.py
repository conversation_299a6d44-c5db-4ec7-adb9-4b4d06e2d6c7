import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.booking.command_handlers.state_transitions.cancel_booking_action import (
    CancelBookingActionCommandHandler,
)
from prometheus.application.booking.command_handlers.state_transitions.no_show_booking_action import (
    NoShowBookingActionCommandHandler,
)
from prometheus.application.booking.command_handlers.state_transitions.reverse_checkin_guest import (
    ReverseCheckinCommandHandler,
)
from prometheus.application.booking.command_handlers.state_transitions.reverse_checkout_guest import (
    ReverseCheckoutCommandHandler,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.booking.exceptions import InvalidStateError
from prometheus.domain.booking.repositories.booking_action_repository import (
    BookingActionRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from ths_common.constants.booking_constants import BookingActions
from ths_common.exceptions import ValidationException

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BookingActionRepository,
        HotelRepository,
        HotelConfigRepository,
        ReverseCheckinCommandHandler,
        ReverseCheckoutCommandHandler,
        CancelBookingActionCommandHandler,
        NoShowBookingActionCommandHandler,
    ]
)
class ReverseBookingActionCommandHandler:
    def __init__(
        self,
        booking_repository,
        booking_action_repository,
        hotel_repository,
        hotel_config_repository,
        reverse_checkin_command_handler: ReverseCheckinCommandHandler,
        reverse_checkout_command_handler: ReverseCheckoutCommandHandler,
        cancel_booking_command_handler: CancelBookingActionCommandHandler,
        no_show_booking_command_handler: NoShowBookingActionCommandHandler,
    ):
        self.booking_repository = booking_repository
        self.booking_action_repository = booking_action_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.reverse_checkin_command_handler = reverse_checkin_command_handler
        self.reverse_checkout_command_handler = reverse_checkout_command_handler
        self.cancel_booking_command_handler = cancel_booking_command_handler
        self.no_show_booking_command_handler = no_show_booking_command_handler

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(self, booking_id, action_id, user_data, hotel_aggregate=None):
        booking_aggregate = self.booking_repository.load(booking_id)
        booking_action_aggregates = (
            self.booking_action_repository.load_all_actions_for_booking(booking_id)
        )

        if not hotel_aggregate:
            hotel_aggregate = self.hotel_repository.load(booking_aggregate.hotel_id)
            hotel_config_aggregate = self.hotel_config_repository.load(
                booking_aggregate.hotel_id
            )
            crs_context.set_hotel_context(
                hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
            )

        self._fail_if_action_not_in_booking(booking_action_aggregates, action_id)

        booking_action_aggregate = None
        for action_aggregate in booking_action_aggregates:
            if not action_aggregate.booking_action.is_reversible:
                if action_aggregate.booking_action.action_id == action_id:
                    raise ValidationException(
                        ApplicationErrors.ACTION_CANNOT_BE_CANCELLED
                    )
                continue
            if action_aggregate.booking_action.action_id != action_id:
                raise ValidationException(
                    ApplicationErrors.ONLY_LATEST_ACTION_CAN_BE_CANCELED
                )
            booking_action_aggregate = action_aggregate
            break

        if not booking_action_aggregate:
            raise ValidationException(ApplicationErrors.NO_ACTIVE_ACTION_FOUND)

        if booking_action_aggregate.booking_action.booking_id != booking_id:
            raise ValidationException(
                ApplicationErrors.ACTION_DOES_NOT_BELONG_TO_BOOKING
            )

        hotel_context = crs_context.get_hotel_context()
        if (
            hotel_context.current_date()
            > booking_action_aggregate.booking_action.business_date
        ):
            raise ValidationException(
                error=ApplicationErrors.CANNOT_REVERSE_PAST_DATED_ACTION,
                format_dict=dict(action=booking_action_aggregate.booking_action_type),
            )

        self._reverse_action(booking_aggregate, booking_action_aggregate, user_data)
        self.booking_action_repository.update(booking_action_aggregate)

        return booking_action_aggregate, booking_aggregate.booking.version

    def _reverse_action(self, booking_aggregate, booking_action_aggregate, user_data):
        if (
            booking_action_aggregate.booking_action.action_type
            == BookingActions.CHECKIN
        ):
            self.reverse_checkin_command_handler.handle(
                booking_aggregate, booking_action_aggregate, user_data
            )

        elif (
            booking_action_aggregate.booking_action.action_type
            == BookingActions.CHECKOUT
        ):
            self.reverse_checkout_command_handler.handle(
                booking_aggregate, booking_action_aggregate, user_data
            )

        elif (
            booking_action_aggregate.booking_action.action_type == BookingActions.CANCEL
        ):
            self.cancel_booking_command_handler.reverse_cancellation(
                booking_aggregate, booking_action_aggregate, user_data
            )
        elif (
            booking_action_aggregate.booking_action.action_type == BookingActions.NOSHOW
        ):
            if self._is_partial_no_show(booking_action_aggregate):
                self.no_show_booking_command_handler.partial_reverse_noshow(
                    booking_aggregate, booking_action_aggregate, user_data
                )
            else:
                self.no_show_booking_command_handler.booking_reverse_noshow(
                    booking_aggregate, booking_action_aggregate, user_data
                )
        else:
            raise InvalidStateError(
                description="The action type reversal in not supported."
            )

        booking_action_aggregate.booking_action.mark_reversed()

    @classmethod
    def _is_partial_no_show(cls, action_aggregate):
        return action_aggregate.booking_action.payload.get('room_stays')

    @staticmethod
    def _fail_if_action_not_in_booking(booking_action_aggregates, action_id):
        action_ids = [
            action.booking_action.action_id for action in booking_action_aggregates
        ]
        if action_id not in action_ids:
            raise ValidationException(
                ApplicationErrors.ACTION_DOES_NOT_BELONG_TO_BOOKING
            )
