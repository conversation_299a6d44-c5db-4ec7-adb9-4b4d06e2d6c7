import logging
from collections import namedtuple

from treebo_commons.utils import dateutils

from object_registry import locate_instance, register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.command_handlers.invoice.invoice_accounts import (
    InvoiceAccounts<PERSON>ommandHandler,
)
from prometheus.application.booking.helpers.guest_service import GuestHelperService
from prometheus.application.booking.helpers.no_show_and_cancellation_service import (
    NoShowAndCancellationService,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.housekeeping.house_status_service import HouseStatusService
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.helpers import occupancy_change_handler
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.credit_note_repository import (
    CreditNoteRepository,
)
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.factories.booking_action_factory import (
    BookingActionFactory,
)
from prometheus.domain.booking.repositories.booking_action_repository import (
    BookingActionRepository,
)
from prometheus.domain.booking.repositories.booking_invoice_group_repository import (
    BookingInvoiceGroupRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.facts.room_stay_facts import RoomStayFacts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityStatus,
    ChargeStatus,
    ChargeTypes,
)
from ths_common.constants.booking_constants import BookingActions, BookingStatus
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.exceptions import InvalidOperationError
from ths_common.value_objects import (
    ActionReversalSideEffects,
    BillSideEffect,
    BookingSideEffect,
)

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        BookingActionRepository,
        HotelRepository,
        HotelConfigRepository,
        InvoiceRepository,
        CreditNoteRepository,
        JobSchedulerService,
        BookingInvoiceGroupRepository,
        NoShowAndCancellationService,
        GuestHelperService,
        TACommissionHelper,
        InvoiceAccountsCommandHandler,
        InventoryBlockRepository,
    ]
)
class NoShowBookingActionCommandHandler(object):
    def __init__(
        self,
        booking_repository,
        bill_repository,
        booking_action_repository,
        hotel_repository,
        hotel_config_repository,
        invoice_repository,
        credit_note_repository,
        job_scheduler_service,
        booking_invoice_group_repository,
        no_show_and_cancellation_service: NoShowAndCancellationService,
        guest_service: GuestHelperService,
        ta_commission_helper: TACommissionHelper,
        invoice_account_command_handler: InvoiceAccountsCommandHandler,
        inventory_block_repository: InventoryBlockRepository,
    ):
        self.booking_repository = booking_repository
        self.booking_action_repository = booking_action_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.bill_repository = bill_repository
        self.invoice_repository = invoice_repository
        self.credit_note_repository = credit_note_repository
        self.job_scheduler_service = job_scheduler_service
        self.booking_invoice_group_repository = booking_invoice_group_repository
        self.no_show_and_cancellation_service = no_show_and_cancellation_service
        self.guest_service = guest_service
        self.ta_commission_helper = ta_commission_helper
        self.invoice_account_command_handler = invoice_account_command_handler
        self.inventory_block_repository = inventory_block_repository

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        booking_version,
        noshow_action_payload,
        user_data,
        hotel_aggregate=None,
    ):
        """

        :param booking_id:
        :param booking_version:
        :param noshow_action_payload:
        :param user_data:
        :param hotel_aggregate:
        :return:
        """
        logger.info(
            "NoShow action received on booking: %s with payload: %s",
            booking_id,
            noshow_action_payload,
        )

        booking_aggregate = self.booking_repository.load_for_update_v2(
            booking_id, version=booking_version
        )
        hotel_context = crs_context.get_hotel_context() if hotel_aggregate else None

        if not hotel_aggregate:
            hotel_aggregate = self.hotel_repository.load(booking_aggregate.hotel_id)
            hotel_config_aggregate = self.hotel_config_repository.load(
                booking_aggregate.hotel_id
            )
            hotel_context = crs_context.set_hotel_context(
                hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
            )

        crs_context.set_current_booking(booking_aggregate)

        RuleEngine.action_allowed(
            action="mark_noshow",
            facts=Facts(
                user_type=user_data.user_type,
                booking_aggregate=booking_aggregate,
                hotel_context=hotel_context,
            ),
            fail_on_error=True,
        )

        bill_aggregate = self.bill_repository.load_for_update(booking_aggregate.bill_id)
        booking_action_aggregate = BookingActionFactory.create_new_action(
            booking_id=booking_id,
            action_type=BookingActions.NOSHOW,
            payload=noshow_action_payload,
            previous_state=booking_aggregate.booking.status,
            business_date=hotel_context.current_date(),
        )

        crs_context.set_current_action_id(booking_action_aggregate)

        room_stays = noshow_action_payload.get('room_stays')
        invoice_aggregates, hotel_invoice_aggregates, invoice_group_aggregate = (
            [],
            [],
            None,
        )
        if not room_stays:
            (
                room_stay_configs,
                booking_side_effect,
                bill_side_effect,
                invoice_aggregates,
                hotel_invoice_aggregates,
                invoice_group_aggregate,
                room_ids_for_house_status_update,
                _,
                service_cleanup_side_effects,
            ) = self._mark_booking_noshow(
                booking_aggregate,
                bill_aggregate,
                noshow_action_payload,
            )
        else:
            (
                room_stay_configs,
                booking_side_effect,
                bill_side_effect,
                room_ids_for_house_status_update,
                service_cleanup_side_effects,
            ) = self._mark_partial_booking_noshow(
                booking_aggregate, bill_aggregate, room_stays
            )

        # In case booking is completely checked out, invoice existing non-locked credit accounts
        unlocked_folio_hotel_invoice_aggregates, unlocked_folio_invoice_aggregates = (
            None,
            None,
        )
        unlocked_folios = self.get_existing_unlocked_credit_accounts_with_charges(
            booking_aggregate, bill_aggregate
        )
        if unlocked_folios:
            (
                unlocked_folio_hotel_invoice_aggregates,
                unlocked_folio_invoice_aggregates,
            ) = self.invoice_account_command_handler.generate_invoices_for_billed_entity_accounts(
                bill_aggregate,
                unlocked_folios,
                booking_aggregate,
                hotel_context,
                generate_hotel_side=True,
            )

        booking_action_aggregate.update_side_effect(
            booking_side_effect=booking_side_effect, bill_side_effect=bill_side_effect
        )

        self.booking_action_repository.save(booking_action_aggregate)
        if unlocked_folio_hotel_invoice_aggregates:
            self.invoice_repository.save_all(unlocked_folio_hotel_invoice_aggregates)
        if unlocked_folio_invoice_aggregates:
            self.invoice_repository.save_all(unlocked_folio_invoice_aggregates)
            self.job_scheduler_service.schedule_invoice_upload(
                bill_aggregate,
                unlocked_folio_invoice_aggregates,
                send_invoices_to_guest=True,
            )
        if invoice_group_aggregate:
            self.booking_invoice_group_repository.save(invoice_group_aggregate)
        self.invoice_repository.save_all(invoice_aggregates)
        self.invoice_repository.save_all(hotel_invoice_aggregates)
        self.bill_repository.update(bill_aggregate)
        self.booking_repository.update(booking_aggregate)

        if room_ids_for_house_status_update:
            locate_instance(HouseStatusService).set_house_status_for_rooms(
                hotel_id=booking_aggregate.hotel_id,
                room_ids=room_ids_for_house_status_update,
            )

        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            user_action="noshow" if not room_stays else "noshow_partial_booking",
        )
        IntegrationEventApplicationService.create_invoice_event(
            event_type=IntegrationEventType.INVOICE_GENERATED,
            invoice_aggregates=invoice_aggregates,
            user_action="noshow" if not room_stays else "noshow_partial_booking",
        )
        if unlocked_folio_invoice_aggregates:
            IntegrationEventApplicationService.create_invoice_event(
                event_type=IntegrationEventType.INVOICE_GENERATED,
                invoice_aggregates=unlocked_folio_invoice_aggregates,
                user_action="noshow",
            )

        addon_inventories_to_release = None
        if service_cleanup_side_effects:
            self.inventory_block_repository.update_all(
                service_cleanup_side_effects.inventory_blocks_released
            )
            addon_inventories_to_release = (
                service_cleanup_side_effects.inventory_blocks_released
            )

        self.no_show_and_cancellation_service.release_inventory_on_room_stay_config_change(
            room_stay_configs,
            booking_aggregate,
            addon_inventories_to_release=addon_inventories_to_release,
            user_action="noshow" if not room_stays else "noshow_partial_booking",
        )

        return booking_action_aggregate

    @audit(audit_type=AuditType.BOOKING_MARKED_NOSHOW)
    def _mark_booking_noshow(
        self, booking_aggregate, bill_aggregate, noshow_action_payload
    ):
        return (
            self.no_show_and_cancellation_service.handle_booking_noshow_or_cancellation(
                booking_aggregate,
                bill_aggregate,
                payload=noshow_action_payload,
                action=BookingActions.NOSHOW,
            )
        )

    @audit(audit_type=AuditType.PARTIAL_BOOKING_MARKED_NOSHOW)
    def _mark_partial_booking_noshow(
        self, booking_aggregate, bill_aggregate, room_stays
    ):
        room_stay_config_for_inventory_release = []
        room_stays_to_mark_noshow = []
        bill_side_effect = BillSideEffect()
        booking_side_effect = BookingSideEffect()
        room_stay_id_guest_stay_ids_map = dict()
        for room_stay in room_stays:
            room_stay_id = room_stay.get('room_stay_id')
            guest_stay_ids = room_stay.get('guest_stay_ids')
            if not guest_stay_ids:
                room_stays_to_mark_noshow.append(room_stay_id)
                room_stay_detail = booking_aggregate.get_room_stay(room_stay_id)
                room_stay_id_guest_stay_ids_map[room_stay.get('room_stay_id')] = [
                    gs.guest_stay_id for gs in room_stay_detail.guest_stays
                ]
            else:
                room_stay_side_effect = booking_aggregate.mark_guest_stays_noshow(
                    room_stay_id=room_stay_id, guest_stay_ids=guest_stay_ids
                )
                booking_side_effect.add_room_stay(room_stay_side_effect)
                room_stay_id_guest_stay_ids_map[
                    room_stay.get('room_stay_id')
                ] = guest_stay_ids

        if booking_side_effect.room_stays:
            room_stay_ids = [
                room_stay_side_effect.room_stay_id
                for room_stay_side_effect in booking_side_effect.room_stays
            ]

            room_stays = booking_aggregate.get_room_stays(room_stay_ids)

            for room_stay in room_stays:
                charges = bill_aggregate.filter_and_get_charges(
                    room_stay.charge_ids, allowed_charge_status={ChargeStatus.CREATED}
                )
                occupancy_change_handler.update_occupancy_details_in_booked_charges(
                    booking_aggregate,
                    bill_aggregate,
                    room_stay=room_stay,
                    edited_charges=charges,
                )

        room_ids_for_house_status_update = []
        service_cleanup_side_effects = None
        if room_stays_to_mark_noshow:
            # Grouping room stays cancellation together, because we load inventory and release it in the below
            # method without committing the change.
            # We cannot do another `load_for_update` of same inventory, without committing the previous session.
            (
                room_stay_configs,
                booking_side_effect_on_complete_room_noshows,
                bill_side_effect,
                room_ids_for_house_status_update,
                cancelled_customers,
                service_cleanup_side_effects,
            ) = self._mark_room_stays_noshow(
                booking_aggregate,
                bill_aggregate,
                room_stays_to_mark_noshow,
                hotel_context=crs_context.get_hotel_context(),
            )
            if (
                len(set(booking_aggregate.get_non_checked_out_active_room_stay_ids()))
                == 0
            ):
                self.no_show_and_cancellation_service.ensure_payment_not_required_for_no_show_or_cancellation(
                    bill_aggregate
                )
            room_stays_to_be_charged = booking_aggregate.get_room_stays(
                room_stays_to_mark_noshow
            )
            noshow_cancellation_charge_map = self.no_show_and_cancellation_service.get_noshow_or_cancellation_charge_map_for_room_stays(
                booking_aggregate, bill_aggregate, room_stays_to_be_charged
            )
            action = BookingActions.NOSHOW
            (
                added_charges,
                _,
                _,
                _,
            ) = self.no_show_and_cancellation_service.handle_noshow_cancellation_expense_addition(
                booking_aggregate,
                bill_aggregate,
                action,
                room_stays_to_be_charged,
                noshow_cancellation_charge_map,
            )
            room_stay_config_for_inventory_release.extend(room_stay_configs)
            for (
                room_stay_side_effect
            ) in booking_side_effect_on_complete_room_noshows.room_stays:
                booking_side_effect.add_room_stay(room_stay_side_effect)
            bill_side_effect.added_charge_ids = [
                added_charge.charge_id for added_charge in added_charges
            ]
            self.guest_service.handle_no_show_or_cancellation_action(
                booking_aggregate, bill_aggregate, room_stay_id_guest_stay_ids_map
            )
            cancelled_billed_entity_ids = (
                bill_aggregate.safe_inactivate_billed_entities(
                    booking_aggregate.get_billed_entity_id_for_guest_stay_ids(
                        room_stay_id_guest_stay_ids_map
                    ),
                    BookingActions.NOSHOW,
                )
            )
            bill_side_effect.cancelled_billed_entity_ids = cancelled_billed_entity_ids
            bill_aggregate.safe_inactivate_billed_entities(
                [
                    cs.billed_entity_id
                    for cs in cancelled_customers
                    if cs.billed_entity_id
                ],
                BilledEntityStatus.CANCEL
                if action == BookingActions.CANCEL
                else BilledEntityStatus.NOSHOW,
            )
        return (
            room_stay_config_for_inventory_release,
            booking_side_effect,
            bill_side_effect,
            room_ids_for_house_status_update,
            service_cleanup_side_effects,
        )

    def _mark_room_stays_noshow(
        self, booking_aggregate, bill_aggregate, room_stay_ids, hotel_context
    ):
        (
            room_stay_configs,
            booking_side_effects,
            bill_side_effect,
            room_ids_for_house_status_update,
            cancelled_customers,
        ) = self.no_show_and_cancellation_service.handle_room_stay_noshow_or_cancellation(
            booking_aggregate,
            bill_aggregate,
            room_stay_ids,
            action=BookingActions.NOSHOW,
            hotel_context=hotel_context,
        )
        service_cleanup_side_effect = (
            self.no_show_and_cancellation_service.remove_room_stay_services(
                booking_aggregate,
                bill_aggregate,
                room_stay_ids,
            )
        )
        cancelled_charges = self.no_show_and_cancellation_service.cancel_addon_charges(
            booking_aggregate, bill_aggregate, room_stay_ids
        )
        bill_side_effect.cancelled_charge_ids.extend(
            [charge.charge_id for charge in cancelled_charges if charge.is_cancelled]
        )
        if cancelled_charges:
            for c in cancelled_charges:
                bill_side_effect.grouped_cancelled_charges.expenses.append(c.charge_id)
        return (
            room_stay_configs,
            booking_side_effects,
            bill_side_effect,
            room_ids_for_house_status_update,
            cancelled_customers,
            service_cleanup_side_effect,
        )

    @audit(audit_type=AuditType.BOOKING_NOSHOW_REVERSED)
    def booking_reverse_noshow(self, booking_aggregate, action_aggregate, user_data):
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_action_id(action_aggregate)
        reversal_side_effects = ActionReversalSideEffects()
        invoice_aggregates = self.invoice_repository.load_for_bill_id(
            booking_aggregate.bill_id
        )

        self.validate_undo_no_show_policy(
            booking_aggregate, action_aggregate, invoice_aggregates, user_data
        )
        bill_aggregate = self.bill_repository.load(booking_aggregate.bill_id)

        bill_side_effect = action_aggregate.booking_action.side_effects.bill
        (
            invoice_aggregates,
            credit_notes,
            hotel_credit_notes,
        ) = self.no_show_and_cancellation_service.handle_noshow_cancellation_charge_and_invoice_reversal(
            booking_aggregate, bill_aggregate, bill_side_effect, user_data.user_type
        )
        _, room_stay_configs, room_stays = self._reverse_complete_no_show(
            booking_aggregate, action_aggregate
        )
        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_for_room_stays(
                booking_aggregate, room_stays, bill_aggregate
            )

        self.booking_repository.update(booking_aggregate)

        self.job_scheduler_service.schedule_credit_note_upload(
            bill_aggregate.bill_id, credit_notes
        )
        self.credit_note_repository.save_all(credit_notes)
        self.credit_note_repository.save_all(hotel_credit_notes)
        self.invoice_repository.update_all(invoice_aggregates)
        self.bill_repository.update(bill_aggregate)

        hotel_id = booking_aggregate.hotel_id
        self.no_show_and_cancellation_service.block_inventory_on_room_stay_config_change(
            hotel_id,
            room_stay_configs,
            reversal_side_effects,
            user_action="undo_noshow",
        )
        updated_aggregates = dict(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            invoice_aggregates=invoice_aggregates,
        )
        IntegrationEventApplicationService.create_booking_updated_event(
            **updated_aggregates, user_action="undo_noshow"
        )
        if credit_notes:
            IntegrationEventApplicationService.create_credit_note_event(
                event_type=IntegrationEventType.CREDIT_NOTE_GENERATED,
                credit_note_aggregates=credit_notes,
                invoice_aggregates=invoice_aggregates,
                user_action="undo_noshow",
            )

        action_aggregate.booking_action.reversal_side_effects = reversal_side_effects
        return action_aggregate

    @audit(audit_type=AuditType.BOOKING_PARTIAL_NOSHOW_REVERSED)
    def partial_reverse_noshow(self, booking_aggregate, action_aggregate, user_data):
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_action_id(action_aggregate)
        reversal_side_effects = ActionReversalSideEffects()

        room_stay_configs = []
        invoice_aggregates = self.invoice_repository.load_for_bill_id(
            booking_aggregate.bill_id
        )
        self.validate_undo_no_show_policy(
            booking_aggregate, action_aggregate, invoice_aggregates, user_data
        )
        bill_aggregate = self.bill_repository.load(booking_aggregate.bill_id)

        bill_side_effect = action_aggregate.booking_action.side_effects.bill
        self.no_show_and_cancellation_service.handle_noshow_cancellation_charge_and_invoice_reversal(
            booking_aggregate, bill_aggregate, bill_side_effect, user_data.user_type
        )
        room_stay_side_effects = (
            action_aggregate.booking_action.side_effects.booking.room_stays
        )
        room_stays_for_ta_commission_recalculation = []
        for room_stay_side_effect in room_stay_side_effects:
            room_stay_config, room_stay = self._reverse_partial_no_show_on_room_stay(
                booking_aggregate,
                room_stay_side_effect.room_stay_id,
                [gsse.guest_stay_id for gsse in room_stay_side_effect.guest_stays],
                user_data,
            )
            if room_stay_config:
                room_stay_configs.append(room_stay_config)
            if (
                crs_context.is_treebo_tenant()
                and booking_aggregate.should_calculate_ta_commission()
            ):
                room_stays_for_ta_commission_recalculation.append(room_stay)

        if room_stays_for_ta_commission_recalculation:
            self.ta_commission_helper.recalculate_commission_for_room_stays(
                booking_aggregate,
                room_stays_for_ta_commission_recalculation,
                bill_aggregate,
            )

        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)

        hotel_id = booking_aggregate.hotel_id
        self.no_show_and_cancellation_service.block_inventory_on_room_stay_config_change(
            hotel_id,
            room_stay_configs,
            reversal_side_effects,
            user_action="undo_noshow",
        )
        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            user_action="undo_noshow",
        )

        action_aggregate.booking_action.reversal_side_effects = reversal_side_effects
        return action_aggregate

    @classmethod
    def validate_undo_no_show_policy(
        cls,
        booking_aggregate,
        action_aggregate,
        invoice_aggregates,
        user_data,
        fail_on_error=True,
    ):
        if cls._is_partial_no_show(action_aggregate):
            for (
                room_stay_side_effect
            ) in action_aggregate.booking_action.side_effects.booking.room_stays:
                room_stay = booking_aggregate.get_room_stay(
                    room_stay_side_effect.room_stay_id
                )
                allowed = RuleEngine.action_allowed(
                    action="undo_noshow_booking",
                    facts=RoomStayFacts(
                        room_stay=room_stay,
                        user_type=user_data.user_type,
                        current_time=dateutils.current_datetime(),
                        booking_aggregate=booking_aggregate,
                        hotel_context=crs_context.get_hotel_context(),
                        generated_invoice_aggregates=invoice_aggregates,
                        action_aggregate=action_aggregate,
                    ),
                    fail_on_error=fail_on_error,
                )
                if not allowed:
                    return False
            return True

        else:
            return RuleEngine.action_allowed(
                action="undo_noshow_booking",
                facts=Facts(
                    user_type=user_data.user_type,
                    current_time=dateutils.current_datetime(),
                    booking_aggregate=booking_aggregate,
                    hotel_context=crs_context.get_hotel_context(),
                    generated_invoice_aggregates=invoice_aggregates,
                ),
                fail_on_error=fail_on_error,
            )

    @classmethod
    def _is_partial_no_show(cls, action_aggregate):
        return action_aggregate.booking_action.payload.get('room_stays')

    @staticmethod
    def _fail_if_room_is_cancelled(booking_aggregate, room_stay_id):
        room_stay = booking_aggregate.get_room_stay(room_stay_id)
        if room_stay.is_cancelled():
            raise InvalidOperationError(
                error=BookingErrors.ROOM_CANCEL_AFTER_NOSHOW,
                description="Roomstay is cancelled after noshow",
            )

    def _reverse_partial_no_show_on_room_stay(
        self, booking_aggregate, room_stay_id, guest_stay_ids, user_data
    ):
        self._fail_if_room_is_cancelled(booking_aggregate, room_stay_id)
        room_stay_config = None
        if len(guest_stay_ids) < len(
            booking_aggregate.get_room_stay(room_stay_id).guest_stays
        ):
            booking_aggregate.undo_mark_guest_stays_noshow(room_stay_id, guest_stay_ids)
            room_stay = booking_aggregate.get_room_stay(room_stay_id)
        else:
            room_stay = booking_aggregate.undo_mark_room_stay_noshow(
                room_stay_id,
                booking_aggregate.get_guest_stays(room_stay_id, guest_stay_ids),
            )
            room_stay_config = room_stay.room_stay_config
        return room_stay_config, room_stay

    @staticmethod
    def _reverse_complete_no_show(booking_aggregate, action_aggregate):
        booking_side_effect = action_aggregate.booking_action.side_effects.booking
        room_stays = booking_aggregate.undo_booking_noshow(
            booking_side_effect.room_stays,
            action_aggregate.booking_action.previous_state,
        )
        room_stay_configs = [room_stay.room_stay_config for room_stay in room_stays]
        return booking_side_effect, room_stay_configs, room_stays

    @staticmethod
    def get_existing_unlocked_credit_accounts_with_charges(
        booking_aggregate, bill_aggregate
    ):
        if booking_aggregate.booking.status != BookingStatus.CHECKED_OUT:
            return []

        charges_billed_entity_account = {
            cs.billed_entity_account
            for charge in bill_aggregate.charges
            for cs in charge.charge_splits
        }

        return [
            BilledEntityAccountVO(be.billed_entity_id, acc.account_number)
            for be in bill_aggregate.billed_entities
            for acc in be.accounts
            if (
                acc.account_type == ChargeTypes.CREDIT
                and not acc.locked
                and BilledEntityAccountVO(be.billed_entity_id, acc.account_number)
                in charges_billed_entity_account
            )
        ]
