import logging

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.domain.booking.factories.booking_action_factory import (
    BookingActionFactory,
)
from prometheus.domain.booking.repositories.booking_action_repository import (
    BookingActionRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.facts import Facts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.booking_constants import BookingActions

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BookingActionRepository,
        HotelRepository,
        HotelConfigRepository,
        TenantSettings,
    ]
)
class ConfirmBookingCommandHandler(object):
    def __init__(
        self,
        booking_repository,
        booking_action_repository,
        hotel_repository,
        hotel_config_repository,
        tenant_settings,
    ):
        self.booking_repository = booking_repository
        self.booking_action_repository = booking_action_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.tenant_settings = tenant_settings

    @session_manager(commit=True)
    @audit(audit_type=AuditType.BOOKING_CONFIRMED)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        booking_version,
        confirm_action_payload,
        user_data,
        hotel_aggregate=None,
    ):
        """

        :param booking_id:
        :param booking_version:
        :param confirm_action_payload:
        :param user_data:
        :return:
        """
        logger.info(
            "Confirm action received on booking: %s with payload: %s",
            booking_id,
            confirm_action_payload,
        )

        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, version=booking_version
        )
        hotel_id = booking_aggregate.booking.hotel_id
        if not hotel_aggregate:
            hotel_aggregate = self.hotel_repository.load(hotel_id)
            hotel_config_aggregate = self.hotel_config_repository.load(
                booking_aggregate.hotel_id
            )
            crs_context.set_hotel_context(
                hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
            )
        crs_context.set_current_booking(booking_aggregate)
        hotel_context = crs_context.get_hotel_context()

        RuleEngine.action_allowed(
            action="confirm_booking",
            facts=Facts(
                user_type=user_data.user_type,
                current_time=dateutils.current_datetime(),
                booking_aggregate=booking_aggregate,
                hotel_context=crs_context.get_hotel_context(),
            ),
            fail_on_error=True,
        )

        if confirm_action_payload.get(
            'guarantee_information'
        ) and self.tenant_settings.is_guarantee_enabled(hotel_id):
            booking_aggregate.update_guarantee_information(
                confirm_action_payload['guarantee_information']
            )

        booking_action_aggregate = BookingActionFactory.create_new_action(
            booking_id=booking_id,
            action_type=BookingActions.CONFIRM,
            payload=confirm_action_payload,
            previous_state=booking_aggregate.booking.status,
            business_date=hotel_context.current_date(),
        )
        crs_context.set_current_action_id(booking_action_aggregate)
        booking_aggregate.confirm_booking()
        self.booking_repository.update(booking_aggregate)
        self.booking_action_repository.save(booking_action_aggregate)

        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate, user_action="confirm_booking"
        )

        return booking_action_aggregate
