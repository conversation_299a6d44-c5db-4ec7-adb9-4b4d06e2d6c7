import logging

from treebo_commons.utils import dateutils

from object_registry import locate_instance, register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit, audit_housekeeping
from prometheus.application.decorators import session_manager
from prometheus.application.housekeeping.house_status_service import HouseStatusService
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_invoice_group_repository import (
    BookingInvoiceGroupRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.room_stay_facts import RoomStayFacts
from ths_common.constants.action_reversal_alert_messages import (
    ActionReversalAlertMessage,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.value_objects import ActionReversalAlert, ActionReversalSideEffects

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        BookingInvoiceGroupRepository,
        RoomAllotmentRepository,
        InventoryApplicationService,
    ]
)
class ReverseCheckinCommandHandler:
    def __init__(
        self,
        booking_repository,
        bill_repository,
        hotel_repository,
        booking_invoice_group_repository,
        room_allotment_repository,
        inventory_service: InventoryApplicationService,
    ):
        self.booking_repository = booking_repository
        self.hotel_repository = hotel_repository
        self.booking_invoice_group_repository = booking_invoice_group_repository
        self.bill_repository = bill_repository
        self.room_allotment_repository = room_allotment_repository
        self.inventory_service = inventory_service

    @session_manager(commit=True)
    @audit(audit_type=AuditType.CHECKIN_REVERSED)
    @audit_housekeeping(action_performed="reverse_checkin")
    def handle(self, booking_aggregate, booking_action_aggregate, user_data):
        logger.info(
            "Checkin action reversal request received on booking: %s with payload: %s",
            booking_aggregate.booking_id,
            booking_action_aggregate.booking_action.payload,
        )
        self.validate_undo_checkin_policy(
            booking_aggregate, booking_action_aggregate, user_data
        )
        hotel_aggregate = self.hotel_repository.load(booking_aggregate.hotel_id)
        bill_aggregate = self.bill_repository.load_for_update(
            bill_id=booking_aggregate.bill_id
        )
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)
        crs_context.set_current_action_id(booking_action_aggregate)

        action_reversal_alerts = []
        all_deleted_room_allocations = []
        booking_side_effect = (
            booking_action_aggregate.booking_action.side_effects.booking
        )

        for room_stay_to_undo_checkin in booking_side_effect.room_stays:
            # NOTE: On reverse checkin of a guest in room_stay, we un-consume all room stay and addon charges ->
            # which in turns deletes all charge splits.
            # But, for expense, we only remove this guest from charge_split, if room stay is part checked in after
            # reversal. If room stay is in reserved state, after reverse checkin, we delete expense
            room_stay_id = room_stay_to_undo_checkin.room_stay_id
            (
                deleted_room_allocation,
                checkin_reverted_guest_allocations,
            ) = booking_aggregate.undo_checkin_guests(
                room_stay_id=room_stay_id,
                guest_checkin_data=room_stay_to_undo_checkin.guest_stays,
                previous_state=booking_action_aggregate.booking_action.previous_state,
            )
            if deleted_room_allocation:
                all_deleted_room_allocations.append(deleted_room_allocation)

            self._undo_expense_and_room_stay_charge_consumption(
                room_stay_id, booking_aggregate, bill_aggregate
            )

        rooms_freed_action_reversal_alerts = (
            self._delete_room_allotments_on_reverse_checkin(
                booking_aggregate, all_deleted_room_allocations, hotel_aggregate
            )
        )
        action_reversal_alerts.extend(rooms_freed_action_reversal_alerts)

        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)

        room_ids_for_house_status_update = [
            allocation.room_id for allocation in all_deleted_room_allocations
        ]
        if room_ids_for_house_status_update:
            locate_instance(HouseStatusService).set_house_status_for_rooms(
                hotel_id=hotel_aggregate.hotel_id,
                room_ids=room_ids_for_house_status_update,
            )
        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            user_action="undo_checkin",
        )
        reversal_side_effects = ActionReversalSideEffects(alerts=action_reversal_alerts)
        booking_action_aggregate.booking_action.reversal_side_effects = (
            reversal_side_effects
        )
        return booking_action_aggregate

    def validate_undo_checkin_policy(
        self, booking_aggregate, action_aggregate, user_data, fail_on_error=True
    ):
        # NOTE: Expense Not used
        generated_invoice_aggregates = (
            self.booking_invoice_group_repository.load_for_booking_id(
                booking_aggregate.booking_id, exclude_preview=True
            )
        )

        booking_side_effect = action_aggregate.booking_action.side_effects.booking

        for room_stay_to_undo_checkin in booking_side_effect.room_stays:
            room_stay_id = room_stay_to_undo_checkin.room_stay_id
            room_stay = booking_aggregate.get_room_stay(room_stay_id)
            allowed = RuleEngine.action_allowed(
                action="reverse_checkin",
                facts=RoomStayFacts(
                    room_stay,
                    user_type=user_data.user_type,
                    current_time=dateutils.current_datetime(),
                    booking_aggregate=booking_aggregate,
                    generated_invoice_aggregates=generated_invoice_aggregates,
                    hotel_context=crs_context.get_hotel_context(),
                ),
                fail_on_error=fail_on_error,
            )
            if not allowed:
                return False
        return True

    def _undo_expense_and_room_stay_charge_consumption(
        self, room_stay_id, booking_aggregate, bill_aggregate
    ):
        # revert all consumed charges to created by deleting the charge splits
        charge_ids_for_room_stay = booking_aggregate.get_room_stay_charges(
            [room_stay_id]
        )
        expenses_charge_ids = self._get_expenses_charge_ids(
            booking_aggregate, room_stay_id
        )
        unconsumed_charges = bill_aggregate.undo_charge_consumption(
            expenses_charge_ids + charge_ids_for_room_stay
        )
        return unconsumed_charges

    @staticmethod
    def _get_expenses_charge_ids(booking_aggregate, room_stay_id):
        expenses = booking_aggregate.get_expenses_of_room_stay(room_stay_id)
        expenses_charge_ids = [expense.charge_id for expense in expenses]
        return expenses_charge_ids

    def _delete_room_allotments_on_reverse_checkin(
        self, booking_aggregate, deleted_room_allocations, hotel_aggregate
    ):
        action_reversal_alerts = []

        deleted_rooms = self.inventory_service.delete_room_allotments(
            deleted_room_allocations=deleted_room_allocations,
            hotel_id=hotel_aggregate.hotel.hotel_id,
            remarks="Checkin Reversed",
        )

        if deleted_rooms:
            action_reversal_alerts.append(
                ActionReversalAlert(
                    message=ActionReversalAlertMessage.ROOMS_FREED.message(
                        freed_rooms=','.join(map(str, deleted_rooms))
                    ),
                    payload=[
                        dict(
                            booking_id=booking_aggregate.booking_id,
                            freed_rooms=deleted_rooms,
                        )
                    ],
                )
            )
        return action_reversal_alerts
