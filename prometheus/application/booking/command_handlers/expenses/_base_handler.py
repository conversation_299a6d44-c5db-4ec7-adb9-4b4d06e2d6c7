from abc import ABCMeta
from typing import Optional, Union

from prometheus.application.booking.dtos.expense_cleanup_side_effect_dto import (
    ExpenseServiceCleanupSideEffects,
)
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from ths_common.constants.booking_constants import ServiceTypes


class ServiceHandlerBase:
    @staticmethod
    def get_service_type() -> ServiceTypes:
        raise NotImplementedError


class ExpenseAddonHandlerBase(ServiceHandlerBase, metaclass=ABCMeta):
    def add_service(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate: BillAggregate,
        expense_data: dict,
        hotel_context,
    ):
        raise NotImplementedError


class ExpenseCleanupHandlerBase(ServiceHandlerBase, metaclass=ABCMeta):
    def cleanup_for_room_stay(
        self, booking_aggregate, bill_aggregate, room_stay_id, user_action=None
    ) -> Optional[ExpenseServiceCleanupSideEffects]:
        raise NotImplementedError

    def cleanup_for_expense(
        self, booking_aggregate, bill_aggregate, expense, user_action=None
    ) -> Optional[ExpenseServiceCleanupSideEffects]:
        raise NotImplementedError


UnifiedExpenseServiceType = Union[ExpenseAddonHandlerBase, ExpenseCleanupHandlerBase]
