from typing import Dict, List

from object_registry import locate_instance, register_instance
from prometheus.application.booking.command_handlers.expenses._base_handler import (
    UnifiedExpenseServiceType,
)
from prometheus.application.booking.command_handlers.expenses._eci_expense_handler import (
    ECIExpenseHandler,
)
from prometheus.application.booking.command_handlers.expenses._lco_expense_handler import (
    LOCExpenseHandler,
)
from prometheus.application.booking.dtos.expense_cleanup_side_effect_dto import (
    ExpenseServiceCleanupSideEffects,
)
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.entities import Expense
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.inventory.entities.inventory_block import InventoryBlock
from ths_common.constants.booking_constants import ExpenseStatus, ServiceTypes
from ths_common.constants.inventory_constants import InventoryBlockType
from ths_common.utils.common_utils import group_list
from ths_common.value_objects import ExpenseServiceContext


@register_instance(
    dependencies=[
        ECIExpense<PERSON><PERSON><PERSON>,
        LOCExpenseHandler,
        BookingRepository,
    ]
)
class ExpenseServiceHandlerOrchestrator:
    def __init__(
        self,
        eci_expense_handler: ECIExpenseHandler,
        lco_expense_handler: LOCExpenseHandler,
        booking_repository: BookingRepository,
    ):
        self._handlers_by_service_type: Dict[
            ServiceTypes, UnifiedExpenseServiceType
        ] = {}
        self._register_handlers(
            [
                eci_expense_handler,
                lco_expense_handler,
            ]
        )
        self.booking_repository = booking_repository

    def _register_handlers(self, handlers: List[UnifiedExpenseServiceType]):
        for handler in handlers:
            self._handlers_by_service_type[handler.get_service_type()] = handler

    def get_service_handler(
        self, service_type: ServiceTypes
    ) -> UnifiedExpenseServiceType:
        return self._handlers_by_service_type.get(service_type)

    def add_addon_service(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate: BillAggregate,
        expense_data: dict,
        hotel_context,
    ):
        service_context: ExpenseServiceContext = expense_data.get("service_context")
        handler = self._handlers_by_service_type.get(service_context.service_type)
        return handler.add_service(
            booking_aggregate,
            bill_aggregate,
            expense_data,
            hotel_context,
        )

    def handle_inventory_block_release_side_effects(
        self, inventory_block_released: List[InventoryBlock]
    ):
        block_types_to_handle = {
            InventoryBlockType.EARLY_CHECKIN_BLOCK: ServiceTypes.EARLY_CHECKIN,
            InventoryBlockType.LATE_CHECKOUT_BLOCK: ServiceTypes.LATE_CHECKOUT,
        }
        inventory_block_released = group_list(inventory_block_released, 'booking_id')
        for booking_id, released_blocks in inventory_block_released.items():
            blocks_to_handle = [
                block
                for block in released_blocks
                if block.block_type in block_types_to_handle
            ]
            if blocks_to_handle:
                booking_aggregate = self.booking_repository.load_for_update(booking_id)
                for block in blocks_to_handle:
                    booking_aggregate.remove_addon_service_offered_on_expense(
                        room_stay_id=block.room_stay_id,
                        service_type=block_types_to_handle[block.block_type],
                    )
                self.booking_repository.update(booking_aggregate)

    def is_addon_service_expense(self, expense_data: dict) -> bool:
        service_context: ExpenseServiceContext = expense_data.get("service_context")
        if not service_context:
            return False
        return self.get_service_handler(service_context.service_type) is not None

    def cleanup_services_for_room_stay(
        self,
        booking_aggregate,
        bill_aggregate,
        room_stay_id,
        user_action=None,
    ) -> ExpenseServiceCleanupSideEffects:
        side_effects = ExpenseServiceCleanupSideEffects()
        for service_type, handler in self._handlers_by_service_type.items():
            expenses: List[
                Expense
            ] = booking_aggregate.get_active_expenses_for_service_type(
                room_stay_id, service_type
            )
            if expenses:
                side_effects += handler.cleanup_for_room_stay(
                    booking_aggregate,
                    bill_aggregate,
                    room_stay_id,
                    user_action=user_action,
                )
        return side_effects

    def cleanup_services_for_charge_ids(
        self,
        booking_aggregate,
        bill_aggregate,
        charge_ids,
        user_action=None,
    ) -> ExpenseServiceCleanupSideEffects:
        side_effects = ExpenseServiceCleanupSideEffects()
        for charge_id in charge_ids:
            expense = booking_aggregate.get_expense_for_charge(charge_id)
            if self._is_active_service_linked_expense(expense):
                if handler := self.get_service_handler(
                    expense.service_context.service_type
                ):
                    side_effects += handler.cleanup_for_expense(
                        booking_aggregate,
                        bill_aggregate,
                        expense,
                        user_action=user_action,
                    )
        return side_effects

    @staticmethod
    def _is_active_service_linked_expense(expense):
        return (
            expense
            and expense.status != ExpenseStatus.CANCELLED
            and expense.service_context
        )


class StayServiceCleanupFacade:
    @staticmethod
    def clear_room_services(
        booking_aggregate,
        bill_aggregate,
        room_stay_id,
        user_action=None,
    ) -> ExpenseServiceCleanupSideEffects:
        orchestrator = locate_instance(ExpenseServiceHandlerOrchestrator)
        return orchestrator.cleanup_services_for_room_stay(
            booking_aggregate,
            bill_aggregate,
            room_stay_id,
            user_action=user_action,
        )

    @staticmethod
    def clear_services_by_charges(
        booking_aggregate,
        bill_aggregate,
        charge_ids,
        user_action=None,
    ) -> ExpenseServiceCleanupSideEffects:
        orchestrator = locate_instance(ExpenseServiceHandlerOrchestrator)
        return orchestrator.cleanup_services_for_charge_ids(
            booking_aggregate,
            bill_aggregate,
            charge_ids,
            user_action=user_action,
        )
