import logging
from typing import Dict, List, Optional

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.command_handlers.expenses.expense_service_handler import (
    StayServiceCleanupFacade,
)
from prometheus.application.booking.dtos.expense_cleanup_side_effect_dto import (
    ExpenseServiceCleanupSideEffects,
)
from prometheus.application.booking.helpers import room_stay_validators
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.booking.helpers.e_reg_card_template_service import (
    ERegCardTemplateService,
)
from prometheus.application.booking.helpers.room_stay_cancellation_handler import (
    RoomStayCancellationHandler,
)
from prometheus.application.booking.helpers.room_stay_helper import (
    RoomStayHelperService,
)
from prometheus.application.booking.helpers.web_checkin_service import WebCheckinService
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.entities.billed_entity import BilledEntity
from prometheus.domain.billing.entities.payment_split import PaymentSplit
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.entities import RoomStay
from prometheus.domain.booking.repositories import (
    BookingRepository,
    RoomStayOverflowRepository,
)
from prometheus.domain.catalog.repositories import (
    HotelRepository,
    SkuCategoryRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.inventory_requirement_service import (
    InventoryRequirementService,
)
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import RoomStayFacts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.exceptions import ValidationException
from ths_common.utils.common_utils import flatten_list
from ths_common.value_objects import RoomStayConfig

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        HotelRepository,
        HotelConfigRepository,
        BookingRepository,
        BillRepository,
        SkuCategoryRepository,
        WebCheckinService,
        ERegCardTemplateService,
        BilledEntityService,
        InventoryApplicationService,
        TACommissionHelper,
        RoomStayHelperService,
        RoomStayCancellationHandler,
        RoomStayOverflowService,
        RoomStayOverflowRepository,
        InventoryBlockRepository,
    ]
)
class ReplaceRoomStayCommandHandler:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        sku_category_repository: SkuCategoryRepository,
        web_checkin_application_service: WebCheckinService,
        eregcard_template_service: ERegCardTemplateService,
        billed_entity_service: BilledEntityService,
        inventory_service: InventoryApplicationService,
        ta_commission_helper: TACommissionHelper,
        room_stay_helper_service: RoomStayHelperService,
        room_stay_cancellation_handler: RoomStayCancellationHandler,
        room_stay_overflow_service: RoomStayOverflowService,
        room_stay_overflow_repository: RoomStayOverflowRepository,
        inventory_block_repository: InventoryBlockRepository,
    ):
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.sku_category_repository = sku_category_repository
        self.web_checkin_application_service = web_checkin_application_service
        self.eregcard_template_service = eregcard_template_service
        self.billed_entity_service = billed_entity_service
        self.inventory_requirement_service = InventoryRequirementService()
        self.inventory_service = inventory_service
        self.ta_commission_helper = ta_commission_helper
        self.room_stay_helper_service = room_stay_helper_service
        self.room_stay_cancellation_handler = room_stay_cancellation_handler
        self.room_stay_overflow_service = room_stay_overflow_service
        self.room_stay_overflow_repository = room_stay_overflow_repository
        self.inventory_block_repository = inventory_block_repository

    @session_manager(commit=True)
    @audit(audit_type=AuditType.ROOM_STAY_ADDED)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        room_stay_id,
        booking_version,
        room_stay_data,
        user_data,
        hotel_aggregate=None,
    ):
        (
            bill_aggregate,
            booking_aggregate,
            grouped_sku_categories,
            hotel_aggregate,
        ) = self._load_required_entities(
            booking_id, booking_version, hotel_aggregate, user_data
        )

        (
            billed_entity_ids_to_be_deleted,
            payments_splits_of_old_guests,
        ) = self._remove_current_room_details_and_memorize_payments(
            bill_aggregate,
            booking_aggregate,
            room_stay_id,
        )
        service_cleanup_effects: ExpenseServiceCleanupSideEffects = (
            StayServiceCleanupFacade.clear_room_services(
                booking_aggregate,
                bill_aggregate,
                room_stay_id,
                user_action='replace_room_stay',
            )
        )

        old_room_stay_config = booking_aggregate.room_stay_config(room_stay_id)

        new_room_stay: RoomStay = self.room_stay_helper_service.update_room_stay(
            booking_aggregate,
            bill_aggregate,
            room_stay_data,
            room_stay_id,
            user_data,
            grouped_sku_categories,
        )

        allow_overbooking = RuleEngine.action_allowed(
            action="overbooking",
            facts=RoomStayFacts(
                room_stay=new_room_stay,
                user_type=user_data.user_type,
                booking_aggregate=booking_aggregate,
                hotel_context=crs_context.get_hotel_context,
            ),
        )

        self._move_payments_to_new_billed_entity_and_delete_old_entities(
            booking_aggregate,
            bill_aggregate,
            new_room_stay,
            billed_entity_ids_to_be_deleted,
            payments_splits_of_old_guests,
        )

        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_for_room_stays(
                booking_aggregate, [new_room_stay], bill_aggregate
            )

        # Validate Max occupancy for each room
        room_stay_validators.validate_max_occupancy_for_room_stays(
            hotel_aggregate, [new_room_stay.room_stay_config]
        )
        self.bill_repository.update(bill_aggregate)
        self.booking_repository.update(booking_aggregate)

        room_stay_change_event = RoomStayHelperService.generate_room_stay_change_event(
            booking_aggregate,
            [new_room_stay.room_stay_id],
            user_action="update",
        )
        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            booking_sub_resource_change_event=room_stay_change_event,
            user_action="update_room_stay",
        )
        # Inventory Availability check
        new_room_stay_config = RoomStayConfig(
            new_room_stay.room_type_id,
            new_room_stay.checkin_date,
            new_room_stay.checkout_date,
        )

        old_inventory_requirement = (
            self.inventory_requirement_service.build_inventory_requirement(
                booking_aggregate.hotel_id, old_room_stay_config
            )
        )

        if service_cleanup_effects:
            release_requirement = self.inventory_requirement_service.build_inventory_requirement_from_inventory_blocks(
                booking_aggregate.hotel_id,
                service_cleanup_effects.inventory_blocks_released,
            )
            old_inventory_requirement.merge(release_requirement)
            self.inventory_block_repository.update_all(
                service_cleanup_effects.inventory_blocks_released
            )

        # Build new inventory requirement
        new_inventory_requirement = (
            self.inventory_requirement_service.build_inventory_requirement(
                booking_aggregate.hotel_id, [new_room_stay_config]
            )
        )

        overbooking_alerts = self.inventory_service.update_inventory(
            release_inventory=old_inventory_requirement,
            block_inventory=new_inventory_requirement,
            user_action="replace_room_stay",
            allow_overbooking=allow_overbooking,
        )
        if overbooking_alerts:
            logger.info("Mark overflow if present")
            # NOTE: This room stay overflow logic is too complicated to change. Plus this is
            # making lots of DB queries
            # Changing this to execute only for treebo tenant for now
            self.room_stay_overflow_service.check_and_mark_room_stay_overflows(
                booking_aggregate,
                room_stay_id=new_room_stay.room_stay_id,
                user_action="replace_room_stay",
            )

            logger.info("Mark overflow completed")
            room_stay_overflow_aggregates = self.room_stay_overflow_repository.get_overflowed_room_stays_for_booking(
                booking_aggregate.booking.booking_id
            )
            overflowed_room_stay_ids = [
                rs_overflow_agg.room_stay_overflow.room_stay_id
                for rs_overflow_agg in room_stay_overflow_aggregates
            ]
            booking_aggregate.tag_room_stay_overflows(overflowed_room_stay_ids)
        self.web_checkin_application_service.reject_completed_web_checkins(
            booking_id=booking_id
        )

        self.eregcard_template_service.schedule_eregcard_url_generation(
            booking_id,
            booking_aggregate.hotel_id,
            customer_ids=booking_aggregate.get_customer_ids_for_room_stays(
                [new_room_stay.room_stay_id]
            ),
        )

        return new_room_stay, booking_aggregate.current_version()

    def _load_required_entities(
        self, booking_id, booking_version, hotel_aggregate, user_data
    ):
        booking_aggregate: BookingAggregate = self.booking_repository.load_for_update(
            booking_id, user_data=user_data
        )
        booking_aggregate.fail_if_outdated_version(booking_version)
        if booking_aggregate.is_cancelled() or booking_aggregate.is_noshow():
            raise ValidationException(ApplicationErrors.INVALID_BOOKING_FOR_EDIT)
        if not hotel_aggregate:
            hotel_aggregate = crs_context_middleware.set_hotel_context(
                booking_aggregate.booking.hotel_id
            )
        bill_aggregate: BillAggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)
        sku_category_aggregates = self.sku_category_repository.load_all()
        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in sku_category_aggregates
        }
        return (
            bill_aggregate,
            booking_aggregate,
            grouped_sku_categories,
            hotel_aggregate,
        )

    def _remove_current_room_details_and_memorize_payments(
        self, bill_aggregate, booking_aggregate, room_stay_id
    ):
        room_stay = booking_aggregate.get_room_stay(room_stay_id)
        self.delete_charges_and_expenses_for_room_stay(
            bill_aggregate, booking_aggregate, room_stay
        )
        self.room_stay_cancellation_handler.delete_future_assign_room_allocations_and_allotments(
            room_stays=[room_stay],
            booking_aggregate=booking_aggregate,
        )
        (
            billed_entity_ids_to_be_deleted,
            payments_splits_of_old_guests,
        ) = self._extract_associated_payments_and_be_ids(
            bill_aggregate,
            booking_aggregate,
            room_stay,
        )
        return billed_entity_ids_to_be_deleted, payments_splits_of_old_guests

    @staticmethod
    def _extract_associated_payments_and_be_ids(
        bill_aggregate,
        booking_aggregate,
        room_stay,
    ):
        billed_entity_ids_of_current_guest = set()
        for customer in booking_aggregate.get_customer_for_room_stays(
            room_stay_ids=[room_stay.room_stay_id]
        ):
            billed_entity = bill_aggregate.get_billed_entity(customer.billed_entity_id)
            if billed_entity.category != BilledEntityCategory.BOOKER:
                billed_entity_ids_of_current_guest.add(customer.billed_entity_id)
                booking_aggregate.delete_customer(customer.customer_id)
        payments_splits_of_current_guests: Optional[
            Dict[int, List[PaymentSplit]]
        ] = None
        if billed_entity_ids_of_current_guest:
            payments_splits_of_current_guests: Dict[
                int, List[PaymentSplit]
            ] = bill_aggregate.get_payment_splits_of_billed_entities(
                billed_entity_ids_of_current_guest
            )
        return billed_entity_ids_of_current_guest, payments_splits_of_current_guests

    def _move_payments_to_new_billed_entity_and_delete_old_entities(
        self,
        booking_aggregate,
        bill_aggregate,
        new_room_stay,
        billed_entity_ids_to_be_deleted,
        payments_splits_of_old_guests,
    ):
        if payments_splits_of_old_guests:
            (
                target_billed_entity_id_to_move_payments
            ) = booking_aggregate.billed_entity_ids_associated_with_first_guest_in_room_stay(
                new_room_stay.room_stay_id,
            )
            billed_entity: BilledEntity = bill_aggregate.get_billed_entity(
                target_billed_entity_id_to_move_payments
            )
            bill_aggregate.update_bea_in_payment_splits(
                payments_splits_of_old_guests, billed_entity.latest_account()
            )
        if billed_entity_ids_to_be_deleted:
            self.billed_entity_service.deactivate_and_delete_billed_entity(
                billed_entity_ids_to_be_deleted, bill_aggregate
            )

    @staticmethod
    def delete_charges_and_expenses_for_room_stay(
        bill_aggregate: BillAggregate,
        booking_aggregate: BookingAggregate,
        room_stay: RoomStay,
    ):
        room_stay_charge_ids = room_stay.charge_ids
        room_stay_inclusion_charge_ids = flatten_list(
            charge.addon_charge_ids
            for charge in bill_aggregate.get_charges(room_stay_charge_ids)
        )
        room_stay_expense = booking_aggregate.get_expenses_of_room_stay(
            room_stay.room_stay_id
        )
        non_rate_plan_addon_charge_ids = [
            expense.charge_id
            for expense in room_stay_expense
            if not expense.via_rate_plan
        ]
        bill_aggregate.delete_charges(
            room_stay_charge_ids
            + non_rate_plan_addon_charge_ids
            + room_stay_inclusion_charge_ids
        )
        booking_aggregate.delete_expenses(
            [expense.expense_id for expense in room_stay_expense]
        )
