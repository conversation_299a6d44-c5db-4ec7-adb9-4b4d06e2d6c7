import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.booking.repositories.attachment_repository import (
    AttachmentRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.services.attachment_domain_service import (
    AttachmentDomainService,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from ths_common.constants.audit_trail_constants import AuditType

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[BookingRepository, AttachmentRepository, AttachmentDomainService]
)
class DeleteAttachmentCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        attachment_repository: AttachmentRepository,
        attachment_domain_service: AttachmentDomainService,
    ):
        self.booking_repository = booking_repository
        self.attachment_repository = attachment_repository
        self.attachment_domain_service = attachment_domain_service

    @session_manager(commit=True)
    @audit(audit_type=AuditType.ATTACHMENT_DELETED)
    @set_hotel_context()
    def handle(self, booking_id, attachment_id, user_data, hotel_aggregate=None):
        crs_context.set_current_booking(
            self.booking_repository.load(
                booking_id=booking_id,
                user_data=user_data,
                skip_expenses=True,
                skip_customers=False,
            )
        )
        attachment_aggregate = self.attachment_repository.load(
            attachment_id, booking_id, user_data
        )
        RuleEngine.action_allowed(
            action="delete_attachment",
            facts=Facts(
                user_type=user_data.user_type, attachment_aggregate=attachment_aggregate
            ),
            fail_on_error=True,
        )
        attachment_aggregate.mark_deleted()
        self.attachment_repository.update(attachment_aggregate)
        self.attachment_domain_service.register_attachment_deleted_event(
            attachment_aggregate
        )
        return attachment_aggregate
