from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.booking.guest_communication.invoice_mailer import (
    InvoiceMailer,
)
from prometheus.application.booking.template_generation.crs_booking_invoice_template_service import (
    CrsBookingInvoiceTemplateService,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.domain.billing.repositories import BillRepository, InvoiceRepository
from prometheus.domain.booking.repositories import (
    BookingInvoiceGroupRepository,
    BookingRepository,
)
from ths_common.constants.billing_constants import InvoiceStatus
from ths_common.constants.integration_event_constants import IntegrationEventType


@register_instance(
    dependencies=[
        BookingRepository,
        InvoiceRepository,
        BillRepository,
        CrsBookingInvoiceTemplateService,
        InvoiceMailer,
        BookingInvoiceGroupRepository,
    ]
)
class GenerateInvoiceTemplateCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        invoice_repository: InvoiceRepository,
        bill_repository: BillRepository,
        crs_booking_invoice_template_service: CrsBookingInvoiceTemplateService,
        invoice_mailer: InvoiceMailer,
        booking_invoice_group_repository: BookingInvoiceGroupRepository,
    ):
        self.booking_repository = booking_repository
        self.invoice_repository = invoice_repository
        self.bill_repository = bill_repository
        self.crs_booking_invoice_template_service = crs_booking_invoice_template_service
        self.invoice_mailer = invoice_mailer
        self.booking_invoice_group_repository = booking_invoice_group_repository

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(
        self,
        invoice_ids,
        booking_id=None,
        bill_id=None,
        should_upload=True,
        send_mail=True,
        should_save=True,
        hotel_aggregate=None,
        **kwargs
    ) -> JobResultDto:
        """
        receives a request for generate and upload the template for a given invoice
        Args:
            booking_id: the booking id for which the invoice belongs to
            invoice_ids: The string invoice id
            bill_id: the bill id to which the invoice belongs
            should_upload: if set to true will update the template else not
            send_mail: If true, the updated invoice template will be mailed to customer
            should_save: If True, save in DB else not
            hotel_aggregate: hotel aggregate
        Returns: None

        """
        assert booking_id or bill_id, "One of bill_id or booking_id is required"
        if booking_id:
            booking_aggregate = self.booking_repository.load(booking_id)
            bill_aggregate = self.bill_repository.load(
                booking_aggregate.booking.bill_id
            )
        else:
            bill_aggregate = self.bill_repository.load(bill_id)
            booking_aggregate = self.booking_repository.load_booking_by_bill_id(
                bill_aggregate.bill_id
            )

        assert (
            bill_aggregate.is_crs_bill()
        ), "This method should be called only for CRS Bill"

        if not hotel_aggregate:
            hotel_aggregate = crs_context_middleware.set_hotel_context(
                booking_aggregate.booking.hotel_id
            )

        send_invoices_to_guest = send_mail
        invoice_aggregates = self.invoice_repository.load_all(invoice_ids)
        if not invoice_aggregates:
            return JobResultDto.success()

        booking_invoice_group_aggregates = (
            self.booking_invoice_group_repository.load_for_booking_id(
                booking_aggregate.booking_id
            )
        )
        booked_charges_and_allowances_invoiced = [
            booking_invoice_group_aggregate.booking_invoice_group.charges_and_allowances_to_be_posted
            for booking_invoice_group_aggregate in booking_invoice_group_aggregates
            if booking_invoice_group_aggregate.booking_invoice_group.charges_and_allowances_to_be_posted
            and all(
                x in booking_invoice_group_aggregate.invoice_ids for x in invoice_ids
            )
        ]
        booked_charges_invoiced = booked_allowances_invoiced = None
        if booked_charges_and_allowances_invoiced:
            booked_charges_invoiced = (
                booked_charges_and_allowances_invoiced[0].get('booked_charges_to_post')
                if booked_charges_and_allowances_invoiced[0].get(
                    'booked_charges_to_post'
                )
                else []
            )
            booked_allowances_invoiced = (
                booked_charges_and_allowances_invoiced[0].get(
                    'booked_allowances_to_post'
                )
                if booked_charges_and_allowances_invoiced[0].get(
                    'booked_allowances_to_post'
                )
                else []
            )

        (
            updated_invoice_aggregates,
            templates,
        ) = self.crs_booking_invoice_template_service.generate_and_upload_invoice_templates(
            bill_aggregate,
            invoice_aggregates,
            booking_aggregate,
            hotel_aggregate,
            should_upload,
            booked_charges_invoiced=booked_charges_invoiced,
            booked_allowances_invoiced=booked_allowances_invoiced,
        )

        if should_upload and should_save:
            self.invoice_repository.update_all(updated_invoice_aggregates)

            # Send integration events and email notification
            if any(
                invoice_aggregate.status == InvoiceStatus.PREVIEW
                for invoice_aggregate in updated_invoice_aggregates
            ):
                IntegrationEventApplicationService.create_invoice_event(
                    event_type=IntegrationEventType.INVOICE_PREVIEW_UPDATED,
                    invoice_aggregates=updated_invoice_aggregates,
                    user_action="update_invoice_template",
                )

            if any(
                invoice_aggregate.status != InvoiceStatus.PREVIEW
                for invoice_aggregate in updated_invoice_aggregates
            ):
                confirmed_invoices = [
                    invoice_aggregate
                    for invoice_aggregate in updated_invoice_aggregates
                    if invoice_aggregate.status != InvoiceStatus.PREVIEW
                ]
                IntegrationEventApplicationService.create_invoice_event(
                    event_type=IntegrationEventType.INVOICE_UPDATED,
                    invoice_aggregates=confirmed_invoices,
                    user_action="update_invoice_template",
                )

                if bill_aggregate.is_crs_bill() and send_invoices_to_guest:
                    invoices_to_send = self._filter_invoices_to_send(
                        confirmed_invoices,
                        crs_context.get_hotel_context(),
                        booking_aggregate,
                    )
                    self.invoice_mailer.send_invoices_to_guest(
                        invoices_to_send, booking_aggregate
                    )

        return JobResultDto.success(
            invoice_aggregates=updated_invoice_aggregates, templates=templates
        )

    def _filter_invoices_to_send(
        self, confirmed_invoices_aggregates, hotel_context, booking_aggregate
    ):
        return [
            inv
            for inv in confirmed_invoices_aggregates
            if self.invoice_mailer.is_sending_email_required(
                booking_aggregate, hotel_context, inv
            )
        ]
