import itertools
import logging
from typing import List

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.billing.dtos.invoice_reissue_dto import AccountSummaryDto
from prometheus.application.booking.command_handlers.invoice.helpers import (
    preview_invoice_helpers,
)
from prometheus.application.booking.command_handlers.invoice.helpers.preview_invoice_helpers import (
    get_checkout_datetime,
)
from prometheus.application.booking.dtos.preview_invoice_response_dto import (
    PreviewInvoiceResponseDto,
)
from prometheus.application.booking.helpers import invoice_confirmation_preconditions
from prometheus.application.booking.helpers.calculate_cancellation_charge_v2_service import (
    CalculateCancellationChargeV2Service,
)
from prometheus.application.booking.helpers.create_preview_invoice import (
    CreatePreviewInvoiceCommand,
)
from prometheus.application.booking.helpers.invoice_confirmation_service import (
    InvoiceConfirmationService,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.billed_entity_helper import (
    build_charge_to_billed_entity_map_for_primary_guest_category,
    get_room_stay_default_billed_entity,
)
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_from_billed_entity,
)
from prometheus.application.helpers.invoice_issuer_service import InvoiceIssuerService
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto import ChargeData
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.services import TaxService
from prometheus.domain.booking.aggregates.booking_invoice_group_aggregate import (
    BookingInvoiceGroupAggregate,
)
from prometheus.domain.booking.dtos import (
    BookingCheckoutRequest,
    CheckoutActionRequest,
    ExpenseDto,
)
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.factories import BookingInvoiceGroupFactory
from prometheus.domain.booking.repositories import ExpenseItemRepository
from prometheus.domain.booking.repositories.booking_invoice_group_repository import (
    BookingInvoiceGroupRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.services.cancellation_charge_calculator import (
    CancellationChargeCalculator,
)
from prometheus.domain.catalog.repositories import (
    ResellerGstRepository,
    SkuCategoryRepository,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.infrastructure.external_clients.catalog_service.user_defined_enums import (
    UserDefinedEnums,
)
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeSplitType,
    ChargeStatus,
    IssuedToType,
)
from ths_common.constants.booking_constants import (
    CancellationPolicy,
    ExpenseTypes,
    InvoiceGroupStatus,
)
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import ValidationException
from ths_common.utils.common_utils import flatten_list
from ths_common.utils.dateutils import get_settlement_date
from ths_common.value_objects import ChargeItem, ExpenseChargeItemDetails, Occupancy

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        HotelConfigRepository,
        BookingInvoiceGroupRepository,
        RoomTypeRepository,
        InvoiceRepository,
        InvoiceConfirmationService,
        JobSchedulerService,
        SkuCategoryRepository,
        ResellerGstRepository,
        CatalogServiceClient,
        TenantSettings,
        ExpenseItemRepository,
        CancellationChargeCalculator,
        TaxService,
        CalculateCancellationChargeV2Service,
    ]
)
class GeneratePreviewInvoicesCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        booking_invoice_group_repository: BookingInvoiceGroupRepository,
        room_type_repository: RoomTypeRepository,
        invoice_repository: InvoiceRepository,
        invoice_confirmation_service: InvoiceConfirmationService,
        job_scheduler_service: JobSchedulerService,
        sku_category_repository: SkuCategoryRepository,
        reseller_gst_detail_repository: ResellerGstRepository,
        catalog_service_client: CatalogServiceClient,
        tenant_settings: TenantSettings,
        expense_item_repository: ExpenseItemRepository,
        cancellation_charge_calculator: CancellationChargeCalculator,
        tax_service: TaxService,
        cancellation_charge_v2_service: CalculateCancellationChargeV2Service,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.booking_invoice_group_repository = booking_invoice_group_repository
        self.room_type_repository = room_type_repository
        self.invoice_repository = invoice_repository
        self.invoice_confirmation_service = invoice_confirmation_service
        self.job_scheduler_service = job_scheduler_service
        self.sku_category_repository = sku_category_repository
        self.reseller_gst_detail_repository = reseller_gst_detail_repository
        self.catalog_service_client = catalog_service_client
        self.tenant_settings = tenant_settings
        self.expense_item_repository = expense_item_repository
        self.cancellation_charge_calculator = cancellation_charge_calculator
        self.tax_service = tax_service
        self.cancellation_charge_v2_service = cancellation_charge_v2_service

    def _set_hotel_context(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate
        )
        return hotel_aggregate, hotel_config_aggregate, hotel_context

    def _cancel_existing_preview_invoices(
        self, booking_aggregate, bill_aggregate, booking_checkout_request
    ):
        old_preview_invoice_group_aggregates: List[
            BookingInvoiceGroupAggregate
        ] = self.booking_invoice_group_repository.load_for_update_for_booking_id(
            booking_aggregate.booking.booking_id,
            order_by_request_time=True,
            include_statuses=[InvoiceGroupStatus.PREVIEW.value],
        )
        if not old_preview_invoice_group_aggregates:
            return

        invoice_ids = []
        for preview_invoice_group_aggregate in old_preview_invoice_group_aggregates:
            new_room_stay_checkout_requests = [
                charge_grouping_request.room_stay_id
                for charge_grouping_request in booking_checkout_request.charge_grouping_requests
            ]
            old_room_stay_checkout_requests = [
                charge_grouping_request.room_stay_id
                for charge_grouping_request in preview_invoice_group_aggregate.room_wise_invoice_request
            ]
            if any(
                room_stay in old_room_stay_checkout_requests
                for room_stay in new_room_stay_checkout_requests
            ):
                bill_aggregate.cancel_charges(
                    preview_invoice_group_aggregate.newly_added_charge_ids
                )
                preview_invoice_group_aggregate.update_status(
                    InvoiceGroupStatus.CANCELLED
                )

            if preview_invoice_group_aggregate.invoice_ids:
                invoice_ids.extend(preview_invoice_group_aggregate.invoice_ids)

        self.booking_invoice_group_repository.update_all(
            old_preview_invoice_group_aggregates
        )

        preview_invoice_aggregates = self.invoice_repository.load_all_for_update(
            invoice_ids=invoice_ids
        )
        if preview_invoice_aggregates:
            for invoice_aggregate in preview_invoice_aggregates:
                invoice_aggregate.cancel()

            self.invoice_repository.update_all(preview_invoice_aggregates)

    def add_early_checkout_charges_if_applicable(
        self,
        booking_aggregate,
        bill_aggregate,
        booking_checkout_request,
        checkout_datetime,
        hotel_context,
        grouped_sku_categories,
    ):
        rate_manager_enabled = self.tenant_settings.get_setting_value(
            TenantSettingName.RATE_MANAGER_ENABLED.value, booking_aggregate.hotel_id
        )
        hotel_uses_posttax = self.tenant_settings.get_setting_value(
            TenantSettingName.HOTEL_USES_POSTTAX_PRICE.value, booking_aggregate.hotel_id
        )
        if not rate_manager_enabled:
            return []
        early_check_out_room_stays_to_be_charged = [
            booking_aggregate.get_room_stay(cgr.room_stay_id)
            for cgr in booking_checkout_request.charge_grouping_requests
            if cgr.room_complete_checkout
            and dateutils.to_date(hotel_context.hotel_checkout_date(checkout_datetime))
            < dateutils.to_date(
                booking_aggregate.get_room_stay(cgr.room_stay_id).checkout_date
            )
        ]
        expense_item = self.expense_item_repository.load('booking_cancellation')
        early_checkout_charge_map = {
            room_stay.room_stay_id: self.cancellation_charge_calculator.calculate_room_stay_cancellation_charge(
                booking_aggregate=booking_aggregate,
                bill_aggregate=bill_aggregate,
                room_stay=room_stay,
                cancellation_datetime=checkout_datetime,
                cancellation_start_date=checkout_datetime,
                cancellation_end_date=room_stay.checkout_date,
                is_rate_manager_enabled=rate_manager_enabled,
                use_posttax_for_tax_calculation=hotel_uses_posttax
                if hotel_uses_posttax
                else False,
                cancellation_charge_expense_item=expense_item,
            )
            for room_stay in early_check_out_room_stays_to_be_charged
        }

        added_charges = self._add_early_checkout_expense_and_charge_for_room_stays(
            booking_aggregate,
            bill_aggregate,
            early_check_out_room_stays_to_be_charged,
            early_checkout_charge_map,
            hotel_context,
            checkout_datetime,
            grouped_sku_categories,
        )
        if (
            booking_aggregate.get_default_billed_entity_category()
            == BilledEntityCategory.PRIMARY_GUEST
        ):
            charge_id_to_billed_entity_map = (
                build_charge_to_billed_entity_map_for_primary_guest_category(
                    booking_aggregate, bill_aggregate
                )
            )
            bill_aggregate.attach_default_billed_entity_account_to_charges(
                charges=added_charges,
                charge_id_to_billed_entity_map=charge_id_to_billed_entity_map,
            )
        else:
            billed_entity = bill_aggregate.get_billed_entity_for_category(
                booking_aggregate.get_default_billed_entity_category()
            )
            bill_aggregate.attach_default_billed_entity_account_to_charges(
                charges=added_charges, billed_entity=billed_entity
            )
        return added_charges

    def _add_early_checkout_expense_and_charge_for_room_stays(
        self,
        booking_aggregate,
        bill_aggregate,
        room_stays,
        early_checkout_charge_map,
        hotel_context,
        checkout_datetime,
        grouped_sku_categories,
    ):
        # TODO: Duplicate method from noshow_cancellation_service for adding cancellation/noshow charge from charge map
        expense_item = self.expense_item_repository.load(
            ExpenseTypes.EARLY_CHECKOUT.value
        )
        sku_category = grouped_sku_categories.get(expense_item.sku_category_id)
        new_expenses, new_charge_dtos = [], []
        for room_stay in room_stays:
            if (
                early_checkout_charge_map.get(
                    room_stay.room_stay_id, Money(0, bill_aggregate.bill.base_currency)
                )
                == 0
            ):
                continue
            base_charge = bill_aggregate.get_charge(
                booking_aggregate.per_night_charge(room_stay.room_stay_id)
            )
            expense_dto = ExpenseDto.from_room_stay_and_expense_item(
                room_stay,
                [booking_aggregate.booking.owner_id],
                expense_item,
                checkout_datetime,
            )
            new_expense = booking_aggregate.add_expense(
                expense_dto,
                get_settlement_date(dateutils.current_datetime(), next_month=True),
            )
            new_charge_dto = ChargeData(
                posttax_amount=early_checkout_charge_map[room_stay.room_stay_id],
                charge_type=base_charge.type,
                bill_to_type=base_charge.bill_to_type,
                charge_split_type=ChargeSplitType.EQUAL_SPLIT,
                applicable_date=new_expense.applicable_date,
                item=ChargeItem(
                    expense_item.name,
                    sku_category.sku_category_id,
                    ExpenseChargeItemDetails(
                        room_stay_id=room_stay.room_stay_id,
                        room_type_code=room_stay.room_type_id,
                        occupancy=room_stay.date_wise_occupancies.get(
                            dateutils.to_date(new_expense.applicable_date),
                            Occupancy(1, 0),
                        ).adult,
                    ),
                    item_id=expense_item.expense_item_id,
                ),
                charge_to=new_expense.assigned_to,
            )
            new_expenses.append(new_expense)
            new_charge_dtos.append(new_charge_dto)
        tax_updated_charge_dtos = self.tax_service.update_taxes(
            new_charge_dtos,
            buyer_gst_details=booking_aggregate.booking_owner_gst_details(),
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, crs_context.get_hotel_context()
            ),
            hotel_id=crs_context.get_hotel_context().hotel_id,
        )
        added_charges = bill_aggregate.add_charges(tax_updated_charge_dtos)
        for expense, charge in zip(new_expenses, added_charges):
            expense.charge_id = charge.charge_id
        return added_charges

    @session_manager(commit=True)
    @audit(audit_type=AuditType.PREVIEW_INVOICE_GENERATED)
    @set_hotel_context()
    def handle(
        self,
        booking_id: str,
        version: str,
        room_wise_invoice_request: List[CheckoutActionRequest],
        generated_by: str,
        generation_channel: str,
        include_cancellation_no_show_charges,
        hotel_aggregate=None,
        booked_charges_to_post=None,
        booked_allowances_to_post=None,
        booked_charges_to_cancel=None,
        booked_allowances_to_cancel=None,
        cancellation_policy=None,
        cancellation_amount=None,
        is_booking_relocated=None,
    ) -> PreviewInvoiceResponseDto:
        if is_booking_relocated:
            privileges = crs_context.privileges_as_dict
            if not (
                privileges and PrivilegeCode.IS_BOOKING_RELOCATION_ALLOWED in privileges
            ):
                raise ValidationException(
                    error=ApplicationErrors.BOOKING_RELOCATION_NOT_ALLOWED,
                )
        if (
            booked_charges_to_cancel
            and booked_charges_to_post
            and len(
                set(booked_charges_to_cancel).intersection(set(booked_charges_to_post))
            )
            > 0
        ):
            raise ValidationException(
                error=BookingErrors.INVALID_DECISION_SENT_FOR_CHARGE
            )
        booking_aggregate = self.booking_repository.load_for_update(booking_id)
        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )
        if not hotel_aggregate:
            (
                hotel_aggregate,
                _,
                hotel_context,
            ) = self._set_hotel_context(booking_aggregate.booking.hotel_id)
        else:
            hotel_context = crs_context.get_hotel_context()

        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)

        checkout_datetime = get_checkout_datetime(hotel_context)

        booking_checkout_request = BookingCheckoutRequest.create_new(
            room_wise_invoice_request,
            checkout_datetime,
            generated_by,
            generation_channel,
            booking_aggregate,
        )

        sku_category_aggregates = self.sku_category_repository.load_all()
        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in sku_category_aggregates
        }

        (
            booked_charges_to_be_consumed,
            booked_allowance_to_be_consumed,
        ) = self._get_booked_charges_and_allowances(
            booking_aggregate, bill_aggregate, booking_checkout_request
        )

        (
            pending_booked_charges_for_today,
            pending_booked_allowances_for_today,
        ) = self._filter_booked_charges_and_allowances_for_checkout_time(
            checkout_datetime,
            booked_charges_to_be_consumed,
            booked_allowance_to_be_consumed,
        )

        if not (
            booked_charges_to_post
            or booked_allowances_to_post
            or booked_charges_to_cancel
            or booked_allowances_to_cancel
        ) and (
            len(pending_booked_charges_for_today) > 0
            or len(pending_booked_allowances_for_today) > 0
        ):
            return PreviewInvoiceResponseDto(
                booking_aggregate.current_version(),
                bill_aggregate.bill.base_currency,
                pending_booked_charges_for_today=pending_booked_charges_for_today,
                pending_booked_allowances_for_today=pending_booked_allowances_for_today,
            )

        self._cancel_existing_preview_invoices(
            booking_aggregate, bill_aggregate, booking_checkout_request
        )

        issued_by_type, _ = InvoiceIssuerService.get_issuer_type_for_new_invoice(
            bill_aggregate.bill.bill_id,
            hotel_aggregate.hotel.hotel_id,
            self.invoice_repository,
            self.catalog_service_client,
            booking_aggregate,
            self.tenant_settings,
        )

        (
            all_booked_charge_ids,
            booked_charges_to_post_with_inclusions,
            all_booked_allowance_details,
            booked_allowances_to_post,
        ) = self._validate_charges_and_allowances_to_be_posted(
            booked_charges_to_post,
            booked_charges_to_be_consumed,
            booked_allowances_to_post,
            booked_allowance_to_be_consumed,
            checkout_datetime,
        )
        booked_charges_to_be_excluded = None
        if booked_charges_to_post and all_booked_charge_ids:
            booked_charges_to_be_excluded = list(
                set(all_booked_charge_ids).difference(
                    set(booked_charges_to_post_with_inclusions)
                )
            )
        elif not booked_charges_to_post and all_booked_charge_ids:
            booked_charges_to_be_excluded = all_booked_charge_ids

        booked_allowance_to_be_excluded = None
        if booked_allowances_to_post and all_booked_allowance_details:
            booked_allowance_to_be_excluded = list(
                itertools.filterfalse(
                    lambda x: x in booked_allowances_to_post,
                    all_booked_allowance_details,
                )
            )
        elif not booked_allowances_to_post and all_booked_allowance_details:
            booked_allowance_to_be_excluded = all_booked_allowance_details

        charges_and_allowances_to_be_posted = dict(
            booked_charges_to_post=booked_charges_to_post_with_inclusions or [],
            booked_allowances_to_post=booked_allowances_to_post or [],
            booked_charge_to_exclude=booked_charges_to_be_excluded or [],
            booked_allowances_to_exclude=booked_allowance_to_be_excluded or [],
        )

        use_cancellation_policy = self.tenant_settings.get_use_cancellation_policy(
            booking_aggregate.hotel_id
        )
        cancellation_charge_id = []
        if use_cancellation_policy and not is_booking_relocated:
            if not cancellation_policy:
                cancellation_policy = (
                    CancellationPolicy.CANCELLATION_FEE_AS_PER_RATE_PLAN
                )
            else:
                cancellation_policy_allowed = self.get_cancellation_policy(
                    booking_aggregate.hotel_id
                )
                if cancellation_policy.value not in cancellation_policy_allowed:
                    raise ValidationException(
                        error=ApplicationErrors.INVALID_CANCELLATION_POLICY,
                    )
            room_stay_ids = {iq.room_stay_id for iq in room_wise_invoice_request}
            if cancellation_policy != CancellationPolicy.CUSTOM_CANCELLATION_FEE:
                cancellation_charge_by_policy = self.cancellation_charge_v2_service.calculate_cancellation_charge_for_early_checkout(
                    booking_id,
                    cancellation_datetime=checkout_datetime,
                    charges_and_allowances_to_be_posted=charges_and_allowances_to_be_posted,
                    room_stay_ids={iq.room_stay_id for iq in room_wise_invoice_request},
                )
                cancellation_charge = self.get_cancellation_charge(
                    cancellation_charge_by_policy, cancellation_policy
                )
            else:
                cancellation_charge = self.calculate_custom_cancellation_charge(
                    bill_aggregate,
                    cancellation_amount,
                    charges_and_allowances_to_be_posted,
                )
            if cancellation_charge > 0:
                added_charges = self.add_early_checkout_charges(
                    booking_aggregate,
                    bill_aggregate,
                    cancellation_charge,
                    checkout_datetime,
                    grouped_sku_categories,
                    room_stay_ids,
                )
                cancellation_charge_id = [c.charge_id for c in added_charges]
                charges_and_allowances_to_be_posted['booked_charges_to_post'].extend(
                    cancellation_charge_id
                )

        invoice_previews = CreatePreviewInvoiceCommand(
            bill_aggregate,
            booking_aggregate,
            hotel_context.current_date(),
            generated_by,
            generation_channel,
            InvoiceIssuerService.get_issuer(
                issued_by_type,
                crs_context.get_hotel_context().build_vendor_details(),
                self.reseller_gst_detail_repository.load(
                    crs_context.get_hotel_context().legal_state_id
                ),
                catalog_service_client=self.catalog_service_client,
            ),
            issued_by_type,
            IssuedToType.CUSTOMER,
            preview_invoice_helpers.get_charge_splits_to_be_invoiced(
                booking_checkout_request,
                booking_aggregate,
                bill_aggregate,
                include_cancellation_no_show_charges,
                booked_charges_to_be_excluded=booked_charges_to_be_excluded,
            ),
            grouped_sku_categories,
            booked_allowances_to_be_excluded=booked_allowance_to_be_excluded,
        ).execute()

        invoice_group_aggregate = BookingInvoiceGroupFactory.create_new_invoice_group(
            booking_checkout_request=booking_checkout_request,
            invoice_ids=[
                invoice_aggregate.invoice.invoice_id
                for invoice_aggregate in invoice_previews
            ],
            newly_added_charge_ids=cancellation_charge_id or [],
            charges_and_allowances_to_be_posted=charges_and_allowances_to_be_posted,
        )

        self.job_scheduler_service.schedule_invoice_upload(
            bill_aggregate, invoice_previews, send_invoices_to_guest=False
        )
        # Save booking only if there is early checkout expense added
        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)
        self.invoice_repository.save_all(invoice_previews)
        self.booking_invoice_group_repository.save(invoice_group_aggregate)

        IntegrationEventApplicationService.create_invoice_event(
            event_type=IntegrationEventType.INVOICE_PREVIEW_CREATED,
            invoice_aggregates=invoice_previews,
            user_action="generate_preview_invoice",
        )

        invoice_id_wise_net_payable = (
            invoice_confirmation_preconditions.get_pending_payment_to_confirm_invoices(
                bill_aggregate,
                invoice_previews,
                booking_invoice_group_aggregates=[invoice_group_aggregate],
            )
        )
        for invoice_aggregate in invoice_previews:
            account_summary = invoice_id_wise_net_payable.get(
                invoice_aggregate.invoice.invoice_id
            )
            invoice_aggregate.net_payable = -account_summary.balance
            invoice_aggregate.summary = account_summary

        return PreviewInvoiceResponseDto(
            booking_aggregate.current_version(),
            bill_aggregate.bill.base_currency,
            invoice_aggregates=invoice_previews,
            booking_invoice_group_id=invoice_group_aggregate.booking_invoice_group_id,
            accounts_with_only_payments=self.get_billed_entity_accounts_only_with_payments(
                booking_aggregate,
                bill_aggregate,
                room_wise_invoice_request,
                booking_checkout_request.is_last_checkout,
                booked_charges_to_post,
            ),
        )

    def _get_booked_charges_and_allowances(
        self,
        booking_aggregate,
        bill_aggregate,
        booking_checkout_request,
    ):
        guest_ids = self._get_guest_ids_for_checkout(
            booking_aggregate, booking_checkout_request
        )
        billed_entity_ids = [
            booking_aggregate.get_customer(guest_id).billed_entity_id
            for guest_id in guest_ids
            if booking_aggregate.get_customer(guest_id).billed_entity_id is not None
        ]
        charge_ids = charges_involved_with_current_guests = list(
            set(
                self._get_charges_mapped_to_checking_out_guest_and_billed_entity(
                    bill_aggregate,
                    guest_ids,
                    billed_entity_ids,
                    booking_checkout_request.is_last_checkout,
                )
            )
        )

        not_posted_charges = []
        not_posted_allowances = []
        rate_plan_mapping = (
            {rp.rate_plan_reference_id: rp for rp in booking_aggregate.rate_plans}
            if booking_aggregate.rate_plans
            else {}
        )
        charges = bill_aggregate.get_charges(charge_ids)
        for charge in charges:
            if charge.status != ChargeStatus.CREATED or charge.is_inclusion_charge:
                continue
            for charge_split in charge.charge_splits:
                total_posttax = total_pretax = total_tax = None
                if charge.inclusion_charge_ids:
                    (
                        total_posttax,
                        total_pretax,
                        total_tax,
                    ) = self._get_cumulative_charge_price(
                        charge, charge_split, bill_aggregate
                    )

                not_posted_charges.append(
                    dict(
                        charge_id=charge.charge_id,
                        billed_entity_account=charge_split.billed_entity_account,
                        posttax_amount=charge_split.post_tax
                        if not charge.inclusion_charge_ids
                        else total_posttax,
                        pretax_amount=charge_split.pre_tax
                        if not charge.inclusion_charge_ids
                        else total_pretax,
                        tax_amount=charge_split.tax
                        if not charge.inclusion_charge_ids
                        else total_tax,
                        item_name=charge.item.name
                        if not charge.inclusion_charge_ids
                        else self._get_charge_description(charge, rate_plan_mapping),
                        guest_id=self._get_applicable_guest_id(
                            booking_aggregate,
                            bill_aggregate,
                            guest_ids,
                            charge,
                            charge_split,
                            charges_involved_with_current_guests,
                        ),
                        sku_category_id=charge.item.sku_category_id,
                        room_no=charge.item.details.get('room_no'),
                        room_type=charge.item.details.get('room_type_code'),
                        room_stay_id=charge.item.details.get('room_stay_id'),
                        charge_split_id=charge_split.charge_split_id,
                        charge_to=self._derive_charge_to(
                            guest_ids, charge, charges_involved_with_current_guests
                        ),
                        applicable_date=charge.applicable_date,
                        inclusion_charge_ids=charge.addon_charge_ids,
                    )
                )

        for charge in charges:
            if charge.status != ChargeStatus.CONSUMED:
                continue
            for charge_split in charge.charge_splits:
                for allowance in charge_split.allowances:
                    if not (
                        allowance.is_active and allowance.status == ChargeStatus.CREATED
                    ):
                        continue
                    not_posted_allowances.append(
                        dict(
                            allowance_id=allowance.allowance_id,
                            charge_id=charge.charge_id,
                            billed_entity_account=charge_split.billed_entity_account,
                            posttax_amount=allowance.posttax_amount,
                            pretax_amount=allowance.pretax_amount,
                            tax_amount=allowance.tax_amount,
                            item_name=charge.item.name,
                            sku_category_id=charge.item.sku_category_id,
                            guest_id=self._get_applicable_guest_id(
                                booking_aggregate,
                                bill_aggregate,
                                guest_ids,
                                charge,
                                charge_split,
                                charges_involved_with_current_guests,
                            ),
                            room_no=charge.item.details.get('room_no'),
                            room_type=charge.item.details.get('room_type_code'),
                            room_stay_id=charge.item.details.get('room_stay_id'),
                            charge_split_id=charge_split.charge_split_id,
                            charge_to=self._derive_charge_to(
                                guest_ids, charge, charges_involved_with_current_guests
                            ),
                            applicable_date=charge.applicable_date,
                            inclusion_charge_ids=charge.addon_charge_ids,
                        )
                    )

        return not_posted_charges, not_posted_allowances

    @staticmethod
    def _derive_charge_to(guest_ids, charge, charges_involved_with_current_guests):
        if charge.charge_id not in charges_involved_with_current_guests:
            return charge.charge_to
        charge_to = list(set(guest_ids).intersection(set(charge.charge_to)))
        return charge_to if len(charge_to) > 0 else charge.charge_to

    def _validate_charges_and_allowances_to_be_posted(
        self,
        booked_charges_to_post,
        booked_charges_to_be_consumed,
        booked_allowances_to_post,
        booked_allowance_to_be_consumed,
        checkout_datetime,
    ):
        (
            all_booked_charge_ids,
            booked_charges_to_post_with_inclusions,
        ) = self._validate_charges_to_be_posted(
            booked_charges_to_post, booked_charges_to_be_consumed, checkout_datetime
        )

        all_booked_allowance_details = self._validate_allowance_to_be_posted(
            booked_allowances_to_post,
            booked_allowance_to_be_consumed,
            checkout_datetime,
        )

        return (
            all_booked_charge_ids,
            booked_charges_to_post_with_inclusions or [],
            all_booked_allowance_details,
            booked_allowances_to_post or [],
        )

    @staticmethod
    def _filter_booked_charges_and_allowances_for_checkout_time(
        checkout_datetime,
        booked_charges_to_be_consumed,
        booked_allowance_to_be_consumed,
    ):
        if dateutils.to_date(checkout_datetime) >= dateutils.current_date():
            pending_booked_charges_for_today = [
                charge
                for charge in booked_charges_to_be_consumed
                if dateutils.to_date(charge.get('applicable_date'))
                <= dateutils.to_date(checkout_datetime)
            ]
            pending_booked_allowances_for_today = [
                allowance
                for allowance in booked_allowance_to_be_consumed
                if dateutils.to_date(allowance.get('applicable_date'))
                <= dateutils.to_date(checkout_datetime)
            ]
        else:
            pending_booked_charges_for_today = [
                charge
                for charge in booked_charges_to_be_consumed
                if dateutils.datetime_at_given_time(
                    charge.get('applicable_date'),
                    dateutils.to_time(charge.get('applicable_date')),
                )
                <= dateutils.datetime_at_given_time(
                    checkout_datetime, dateutils.to_time(checkout_datetime)
                )
            ]
            pending_booked_allowances_for_today = [
                allowance
                for allowance in booked_allowance_to_be_consumed
                if dateutils.datetime_at_given_time(
                    allowance.get('applicable_date'),
                    dateutils.to_time(allowance.get('applicable_date')),
                )
                <= dateutils.datetime_at_given_time(
                    checkout_datetime, dateutils.to_time(checkout_datetime)
                )
            ]
        return pending_booked_charges_for_today, pending_booked_allowances_for_today

    @staticmethod
    def _get_charge_description(charge, rate_plan_mapping):
        description = charge.item.name
        if charge.item.details.get('rate_plan_reference_id'):
            rate_plan = rate_plan_mapping.get(
                charge.item.details['rate_plan_reference_id']
            )
            inclusions = (
                [inc.name for inc in rate_plan.package.inclusions if inc.name]
                if rate_plan.package.inclusions
                else None
            )
            if inclusions:
                rate_plan_inclusions = ' + '.join(inclusions)
                description = "{description} + {rate_plan_inclusions}".format(
                    description=description, rate_plan_inclusions=rate_plan_inclusions
                )
        return description

    @staticmethod
    def _get_cumulative_charge_price(charge, charge_split, bill_aggregate):
        inclusion_charges = [
            charge
            for charge in bill_aggregate.get_charges(charge.inclusion_charge_ids)
            if charge.is_active
        ]
        inclusion_total_posttax_amount = sum(
            [
                cs.post_tax
                for charge in inclusion_charges
                for cs in charge.charge_splits
                if cs.billed_entity_account.account_number
                == charge_split.billed_entity_account.account_number
                and cs.billed_entity_account.billed_entity_id
                == charge_split.billed_entity_account.billed_entity_id
            ]
        )
        inclusion_total_pretax_amount = sum(
            [
                cs.pre_tax
                for charge in inclusion_charges
                for cs in charge.charge_splits
                if cs.billed_entity_account.account_number
                == charge_split.billed_entity_account.account_number
                and cs.billed_entity_account.billed_entity_id
                == charge_split.billed_entity_account.billed_entity_id
            ]
        )
        inclusion_total_tax_amount = sum(
            [
                cs.tax
                for charge in inclusion_charges
                for cs in charge.charge_splits
                if cs.billed_entity_account.account_number
                == charge_split.billed_entity_account.account_number
                and cs.billed_entity_account.billed_entity_id
                == charge_split.billed_entity_account.billed_entity_id
            ]
        )
        posttax_amount = charge_split.post_tax + inclusion_total_posttax_amount
        pretax_amount = charge_split.pre_tax + inclusion_total_pretax_amount
        tax_amount = charge_split.tax + inclusion_total_tax_amount
        return posttax_amount, pretax_amount, tax_amount

    @staticmethod
    def get_billed_entity_accounts_only_with_payments(
        booking_aggregate,
        bill_aggregate,
        room_wise_invoice_request,
        is_last_checkout,
        booked_charges_to_post,
    ):
        guest_ids = flatten_list(
            [request.guest_ids for request in room_wise_invoice_request]
        )
        billed_entity_ids = [
            booking_aggregate.get_customer(guest_id).billed_entity_id
            for guest_id in guest_ids
            if booking_aggregate.get_customer(guest_id).billed_entity_id is not None
        ]
        billed_entity_accounts = []
        if billed_entity_ids:
            billed_entity_accounts = bill_aggregate.get_billed_entity_accounts(
                list(map(int, billed_entity_ids))
            )
        if is_last_checkout:
            default_billed_entity_account_for_category = [
                BilledEntityAccountVO(
                    billed_entity_id=be.billed_entity_id,
                    account_number=account.account_number,
                )
                for be in bill_aggregate.billed_entities
                for account in be.accounts
                if be.category == booking_aggregate.get_default_billed_entity_category()
                and be.deleted is False
            ]

            billed_entity_accounts.extend(default_billed_entity_account_for_category)

        accounts_summary_having_only_payments = []
        for billed_entity_account in set(billed_entity_accounts):
            if not (
                bill_aggregate.is_charge_associated_to_billed_entity_account(
                    billed_entity_account, booked_charges_to_post
                )
            ) and bill_aggregate.is_payment_associated_to_billed_entity_account(
                billed_entity_account
            ):
                accounts_summary_having_only_payments.append(
                    AccountSummaryDto(
                        billed_entity_account=billed_entity_account,
                        accounts_summary=bill_aggregate.get_account_summary(
                            billed_entity_account, invoiced_booked_charges=[]
                        ),
                    )
                )
        return accounts_summary_having_only_payments

    @staticmethod
    def _filter_charges_where_consuming_guest_is_not_checking_out(charges, guests_ids):
        filtered_charges = []
        for charge in charges:
            if charge.item.sku_category_id != 'stay':
                filtered_charges.append(charge)
            if charge.item.sku_category_id == 'stay' and not all(
                x for x in guests_ids for x in charge.charge_to
            ):
                filtered_charges.append(charge)
        return filtered_charges

    def _get_applicable_guest_id(
        self,
        booking_aggregate,
        bill_aggregate,
        guest_ids,
        charge,
        charge_split,
        charges_involved_with_current_guests,
    ):
        if bill_aggregate.get_billed_entity(
            charge_split.billed_entity_account.billed_entity_id
        ).category in (
            BilledEntityCategory.BOOKER_COMPANY,
            BilledEntityCategory.GUEST_COMPANY,
            BilledEntityCategory.TRAVEL_AGENT,
        ):
            return self._derive_charge_to(
                guest_ids, charge, charges_involved_with_current_guests
            )[0]
        else:
            return booking_aggregate.get_customer_for_billed_entity(
                charge_split.billed_entity_account.billed_entity_id
            ).customer_id

    @staticmethod
    def _get_guest_ids_for_checkout(booking_aggregate, booking_checkout_request):
        guest_ids = []
        for (
            charge_grouping_request
        ) in booking_checkout_request.charge_grouping_requests:
            if charge_grouping_request.room_complete_checkout:
                guest_ids.extend(
                    [
                        gs.guest_id
                        for gs in booking_aggregate.get_room_stay(
                            charge_grouping_request.room_stay_id
                        ).guest_stays
                    ]
                )
            else:
                guest_ids.extend(charge_grouping_request.guest_ids)
        return guest_ids

    def _get_charges_mapped_to_checking_out_guest_and_billed_entity(
        self, bill_aggregate, guest_ids, billed_entity_ids, is_last_checkout
    ):
        charge_ids = [
            charge.charge_id
            for charge in bill_aggregate.get_active_charges_for_guests(guest_ids)
        ]
        billed_entity_accounts = []
        if billed_entity_ids:
            billed_entity_accounts = bill_aggregate.get_billed_entity_accounts(
                list(map(int, billed_entity_ids))
            )
        charge_ids.extend(
            [
                charge.charge_id
                for charge in self._filter_charges_where_consuming_guest_is_not_checking_out(
                    bill_aggregate.get_active_charges_for_billed_entity_accounts(
                        billed_entity_accounts
                    ),
                    guest_ids,
                )
            ]
        )
        if is_last_checkout:
            charge_ids = [charge.charge_id for charge in bill_aggregate.charges]
        return charge_ids

    @staticmethod
    def _validate_charges_to_be_posted(
        booked_charges_to_post, booked_charges_to_be_consumed, checkout_datetime
    ):
        booked_charges_to_post = (
            booked_charges_to_post if booked_charges_to_post else []
        )
        all_booked_inclusions = set(
            flatten_list(
                [
                    charge.get('inclusion_charge_ids')
                    for charge in booked_charges_to_be_consumed
                    if charge.get('inclusion_charge_ids')
                ]
            )
        )
        if any(
            inclusion_charge in all_booked_inclusions
            for inclusion_charge in booked_charges_to_post
        ):
            raise ValidationException(
                error=BookingErrors.INCLUSION_CHARGE_CANNOT_BE_INVOICED
            )

        for charge in [
            charge
            for charge in booked_charges_to_be_consumed
            if charge.get('charge_id') in booked_charges_to_post
        ]:
            if dateutils.to_date(checkout_datetime) >= dateutils.current_date():
                if dateutils.to_date(charge.get('applicable_date')) > dateutils.to_date(
                    checkout_datetime
                ):
                    raise ValidationException(
                        error=BookingErrors.FUTURE_CHARGE_CANNOT_BE_INVOICED
                    )
            else:
                if dateutils.datetime_at_given_time(
                    charge.get('applicable_date'),
                    dateutils.to_time(charge.get('applicable_date')),
                ) > dateutils.datetime_at_given_time(
                    checkout_datetime, dateutils.to_time(checkout_datetime)
                ):
                    raise ValidationException(
                        error=BookingErrors.FUTURE_CHARGE_CANNOT_BE_INVOICED
                    )

        booked_inclusion_charges = set(
            flatten_list(
                [
                    charge.get('inclusion_charge_ids')
                    for charge in booked_charges_to_be_consumed
                    if charge.get('inclusion_charge_ids')
                ]
            )
        )
        all_booked_charge_ids = list(
            set(
                [charge.get('charge_id') for charge in booked_charges_to_be_consumed]
            ).union(booked_inclusion_charges)
        )

        booked_charges_to_post_with_inclusions = None
        if booked_charges_to_post:
            booked_inclusion_charges_to_post = set(
                flatten_list(
                    [
                        charge.get('inclusion_charge_ids')
                        for charge in booked_charges_to_be_consumed
                        if charge.get('inclusion_charge_ids')
                        and charge.get('charge_id') in booked_charges_to_post
                    ]
                )
            )

            booked_charges_to_post_with_inclusions = booked_charges_to_post + list(
                booked_inclusion_charges_to_post
            )

            if not all(
                x in all_booked_charge_ids
                for x in booked_charges_to_post_with_inclusions
            ):
                raise ValidationException(
                    error=BookingErrors.NOT_ALL_CHARGES_ARE_IN_BOOKED_STATE
                )
        return all_booked_charge_ids, booked_charges_to_post_with_inclusions

    @staticmethod
    def _validate_allowance_to_be_posted(
        booked_allowances_to_post, booked_allowance_to_be_consumed, checkout_datetime
    ):
        booked_allowances_to_post = (
            booked_allowances_to_post if booked_allowances_to_post else []
        )
        for allowance in booked_allowances_to_post:
            for allowance_to_be_consumed in booked_allowance_to_be_consumed:
                if (
                    allowance.get('allowance_id')
                    == allowance_to_be_consumed.get('allowance_id')
                    and allowance.get('charge_id')
                    == allowance_to_be_consumed.get('charge_id')
                    and allowance.get('charge_split_id')
                    == allowance_to_be_consumed.get('charge_split_id')
                ):
                    if dateutils.to_date(checkout_datetime) >= dateutils.current_date():
                        if dateutils.to_date(
                            allowance_to_be_consumed.get('applicable_date')
                        ) > dateutils.to_date(checkout_datetime):
                            raise ValidationException(
                                error=BookingErrors.FUTURE_ALLOWANCES_CANNOT_BE_INVOICED
                            )
                    else:
                        if dateutils.datetime_at_given_time(
                            allowance_to_be_consumed.get('applicable_date'),
                            dateutils.to_time(
                                allowance_to_be_consumed.get('applicable_date')
                            ),
                        ) > dateutils.datetime_at_given_time(
                            checkout_datetime, dateutils.to_time(checkout_datetime)
                        ):
                            raise ValidationException(
                                error=BookingErrors.FUTURE_ALLOWANCES_CANNOT_BE_INVOICED
                            )

        all_booked_allowance_details = [
            dict(
                allowance_id=allowance.get('allowance_id'),
                charge_id=allowance.get('charge_id'),
                charge_split_id=allowance.get('charge_split_id'),
            )
            for allowance in booked_allowance_to_be_consumed
        ]
        if booked_allowances_to_post:
            if not all(
                x in all_booked_allowance_details for x in booked_allowances_to_post
            ):
                raise ValidationException(
                    error=BookingErrors.NOT_ALL_ALLOWANCES_ARE_IN_BOOKED_STATE
                )
        return all_booked_allowance_details

    @staticmethod
    def get_cancellation_charge(cancellation_charge_by_policy, cancellation_policy):
        for charge in cancellation_charge_by_policy:
            if charge.policy == cancellation_policy.value:
                return charge.cancellation_and_refund_amount_details.cancellation_charge

    def add_early_checkout_charges(
        self,
        booking_aggregate,
        bill_aggregate,
        cancellation_charge,
        checkout_datetime,
        grouped_sku_categories,
        room_stay_ids,
    ):
        rate_manager_enabled = self.tenant_settings.get_setting_value(
            TenantSettingName.RATE_MANAGER_ENABLED.value, booking_aggregate.hotel_id
        )
        if not rate_manager_enabled:
            return []

        added_charges = self._add_early_checkout_expense_and_charge(
            booking_aggregate,
            bill_aggregate,
            cancellation_charge,
            checkout_datetime,
            grouped_sku_categories,
            room_stay_ids,
        )
        if (
            booking_aggregate.get_default_billed_entity_category()
            == BilledEntityCategory.PRIMARY_GUEST
        ):
            charge_id_to_billed_entity_map = (
                build_charge_to_billed_entity_map_for_primary_guest_category(
                    booking_aggregate, bill_aggregate
                )
            )
            bill_aggregate.attach_default_billed_entity_account_to_charges(
                charges=added_charges,
                charge_id_to_billed_entity_map=charge_id_to_billed_entity_map,
            )
        else:
            billed_entity = bill_aggregate.get_billed_entity_for_category(
                booking_aggregate.get_default_billed_entity_category()
            )
            bill_aggregate.attach_default_billed_entity_account_to_charges(
                charges=added_charges, billed_entity=billed_entity
            )
        return added_charges

    def _add_early_checkout_expense_and_charge(
        self,
        booking_aggregate,
        bill_aggregate,
        cancellation_charge,
        checkout_datetime,
        grouped_sku_categories,
        room_stay_ids,
    ):
        expense_item = self.expense_item_repository.load(
            ExpenseTypes.EARLY_CHECKOUT.value
        )
        sku_category = grouped_sku_categories.get(expense_item.sku_category_id)
        new_expenses, new_charge_dtos = [], []
        cancellation_charge_amount = cancellation_charge / len(room_stay_ids)
        for room_stay_id in room_stay_ids:
            room_stay = booking_aggregate.get_room_stay(room_stay_id)
            base_charge = bill_aggregate.get_charge(
                booking_aggregate.per_night_charge(room_stay_id)
            ).charge_splits[0]
            expense_dto = ExpenseDto.from_room_stay_and_expense_item(
                room_stay,
                [booking_aggregate.booking.owner_id],
                expense_item,
                checkout_datetime,
            )
            new_expense = booking_aggregate.add_expense(
                expense_dto,
                get_settlement_date(dateutils.current_datetime(), next_month=True),
            )
            default_billed_entity = get_room_stay_default_billed_entity(
                room_stay, booking_aggregate, bill_aggregate
            )
            new_charge_dto = ChargeData(
                posttax_amount=cancellation_charge_amount,
                charge_type=base_charge.charge_type,
                bill_to_type=base_charge.bill_to_type,
                charge_split_type=ChargeSplitType.EQUAL_SPLIT,
                status=ChargeStatus.PREVIEW,
                applicable_date=new_expense.applicable_date,
                item=ChargeItem(
                    expense_item.name,
                    sku_category.sku_category_id,
                    ExpenseChargeItemDetails(
                        room_stay_id=room_stay.room_stay_id,
                        room_type_code=room_stay.room_type_id,
                        occupancy=room_stay.date_wise_occupancies.get(
                            dateutils.to_date(new_expense.applicable_date),
                            Occupancy(1, 0),
                        ).adult,
                    ),
                    item_id=expense_item.expense_item_id,
                ),
                charge_to=new_expense.assigned_to,
                buyer_gst_details=get_gst_details_from_billed_entity(
                    booking_aggregate, default_billed_entity
                ),
            )
            new_expenses.append(new_expense)
            new_charge_dtos.append(new_charge_dto)
        tax_updated_charge_dtos = self.tax_service.update_taxes(
            new_charge_dtos,
            buyer_gst_details=booking_aggregate.booking_owner_gst_details(),
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, crs_context.get_hotel_context()
            ),
            hotel_id=crs_context.get_hotel_context().hotel_id,
        )
        added_charges = bill_aggregate.add_charges(tax_updated_charge_dtos)
        for expense, charge in zip(new_expenses, added_charges):
            expense.charge_id = charge.charge_id
        return added_charges

    def calculate_custom_cancellation_charge(
        self, bill_aggregate, cancellation_amount, charge_and_allowances_to_be_exclude
    ):
        if not cancellation_amount:
            raise ValidationException(
                error=ApplicationErrors.CANCELLATION_CHARGE_REQUIRED_FOR_CUSTOM_CANCELLATION_FEE,
            )
        booking_amount = self.get_booking_amount_for_custom_pricing_cancellation_policy(
            bill_aggregate, charge_and_allowances_to_be_exclude
        )
        if cancellation_amount > booking_amount:
            raise ValidationException(
                error=ApplicationErrors.CANCELLATION_CHARGE_GREATER_THAN_BOOKING_AMOUNT,
            )
        return cancellation_amount

    @staticmethod
    def get_booking_amount_for_custom_pricing_cancellation_policy(
        bill_aggregate, charge_and_allowances_to_be_exclude
    ):
        booking_amount = Money(0, bill_aggregate.bill.base_currency)
        for charge in bill_aggregate.charges:
            if charge.charge_id in charge_and_allowances_to_be_exclude[
                'booked_charge_to_exclude'
            ] and (charge.item.name == 'RoomStay' or charge.is_inclusion_charge):
                booking_amount += charge.posttax_amount_post_allowance
        return booking_amount

    def get_cancellation_policy(self, hotel_id):
        role_based_enum_payment_mode_enum = (
            self.tenant_settings.catalog_service_client.get_enums(hotel_id)
        )
        cancellation_policy_enum = None
        for enum in role_based_enum_payment_mode_enum:
            if enum.get('enum_name') == UserDefinedEnums.CANCELLATION_POLICY:
                return enum.get('enum_values')
        return cancellation_policy_enum
