import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.booking.helpers.invoice_confirmation_service import (
    InvoiceConfirmationService,
)
from prometheus.application.commands.consume_charge import ConsumeChargeCommand
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.booking.repositories.booking_invoice_group_repository import (
    BookingInvoiceGroupRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.facts import Facts
from ths_common.constants.booking_constants import InvoiceGroupStatus
from ths_common.exceptions import ValidationException

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        HotelConfigRepository,
        BookingInvoiceGroupRepository,
        RoomTypeRepository,
        InvoiceRepository,
        InvoiceConfirmationService,
        JobSchedulerService,
    ]
)
class ConfirmPreviewInvoiceCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        booking_invoice_group_repository: BookingInvoiceGroupRepository,
        room_type_repository: RoomTypeRepository,
        invoice_repository: InvoiceRepository,
        invoice_confirmation_service: InvoiceConfirmationService,
        job_scheduler_service: JobSchedulerService,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.booking_invoice_group_repository = booking_invoice_group_repository
        self.room_type_repository = room_type_repository
        self.invoice_repository = invoice_repository
        self.invoice_confirmation_service = invoice_confirmation_service
        self.job_scheduler_service = job_scheduler_service

    def _set_hotel_context(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate
        )
        return hotel_aggregate, hotel_config_aggregate, hotel_context

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        booking_version,
        confirm_invoice_payload,
        user_data,
        hotel_aggregate=None,
    ):
        hotel_context = crs_context.get_hotel_context() if hotel_aggregate else None
        logger.debug(
            "Confirm previewed invoices received on booking: %s with payload: %s",
            booking_id,
            confirm_invoice_payload,
        )

        # Load everything
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, version=booking_version
        )

        if not hotel_aggregate:
            (
                hotel_aggregate,
                _,
                hotel_context,
            ) = self._set_hotel_context(booking_aggregate.booking.hotel_id)

        crs_context.set_current_booking(booking_aggregate)

        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )

        RuleEngine.action_allowed(
            action="confirm_invoices",
            facts=Facts(
                user_type=user_data.user_type,
                hotel_context=crs_context.get_hotel_context(),
            ),
            fail_on_error=True,
        )

        booking_invoice_group_aggregate = (
            self.booking_invoice_group_repository.load_for_update(
                confirm_invoice_payload.get('invoice_group_id')
            )
        )
        if booking_invoice_group_aggregate.booking_id != booking_id:
            raise ValidationException(ApplicationErrors.INVALID_INVOICE_GROUP)
        if booking_invoice_group_aggregate.status != InvoiceGroupStatus.PREVIEW:
            raise ValidationException(
                ApplicationErrors.INVOICE_GROUP_NOT_IN_PREVIEW_STATE
            )

        ConsumeChargeCommand(
            booking_aggregate,
            bill_aggregate,
            hotel_context.current_date(),
            crs_context.get_hotel_context(),
            self.room_type_repository.load_type_map(),
            is_checkout=True,
        ).execute()

        invoice_aggregates = self.invoice_repository.load_all_for_update(
            booking_invoice_group_aggregate.invoice_ids, nowait=False
        )
        (
            invoice_aggregates,
            hotel_invoices,
        ) = self.invoice_confirmation_service.confirm_invoices(
            invoice_aggregates, bill_aggregate, booking_aggregate, hotel_context
        )

        if hotel_invoices:
            self.invoice_repository.save_all(hotel_invoices)
        if invoice_aggregates:
            self.invoice_repository.update_all(invoice_aggregates)
            self.job_scheduler_service.schedule_invoice_upload(
                bill_aggregate, invoice_aggregates, send_invoices_to_guest=False
            )

        booking_invoice_group_aggregate.update_status(InvoiceGroupStatus.GENERATED)
        self.booking_invoice_group_repository.update(booking_invoice_group_aggregate)
        self.booking_repository.update(booking_aggregate)

        updated_aggregates = dict(booking_aggregate=booking_aggregate)

        self.bill_repository.update(bill_aggregate)
        updated_aggregates['bill_aggregate'] = bill_aggregate
        updated_aggregates['invoice_aggregates'] = invoice_aggregates

        IntegrationEventApplicationService.create_booking_updated_event(
            **updated_aggregates, user_action="confirm_preview_invoice"
        )

        return invoice_aggregates, booking_aggregate.current_version()
