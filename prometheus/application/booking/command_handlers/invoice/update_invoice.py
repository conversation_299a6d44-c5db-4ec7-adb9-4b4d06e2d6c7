from object_registry import register_instance
from prometheus.application.decorators import session_manager
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories import InvoiceRepository
from prometheus.domain.booking.repositories import BookingRepository
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.exceptions import ValidationException


@register_instance(dependencies=[BookingRepository, InvoiceRepository])
class UpdateInvoiceCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        invoice_repository: InvoiceRepository,
    ):
        self.booking_repository = booking_repository
        self.invoice_repository = invoice_repository

    @session_manager(commit=True)
    def handle(self, booking_id, invoice_id, version, edit_dict):
        if version is None:
            raise ValidationException(
                ApplicationErrors.INVOICE_VERSION_MANDATORY_FOR_UPDATE
            )

        booking_aggregate = self.booking_repository.load(booking_id)
        invoice_aggregate = self.invoice_repository.load_for_update(invoice_id, version)

        if booking_aggregate.booking.bill_id != invoice_aggregate.invoice.bill_id:
            raise ValidationException(ApplicationErrors.INVOICE_DOES_NOT_BELONG_TO_BILL)

        invoice_aggregate.set_is_downloaded(edit_dict.get('is_downloaded'))
        IntegrationEventApplicationService.create_invoice_event(
            event_type=IntegrationEventType.INVOICE_UPDATED,
            invoice_aggregates=[invoice_aggregate],
            user_action="edit_invoice",
        )
        self.invoice_repository.update(invoice_aggregate)

        return invoice_aggregate
