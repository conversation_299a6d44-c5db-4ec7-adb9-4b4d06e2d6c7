import logging

from object_registry import locate_instance, register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit, audit_housekeeping
from prometheus.application.booking.command_handlers.allocate_room import (
    Allocate<PERSON>oom<PERSON>ommandHandler,
)
from prometheus.application.booking.command_handlers.helpers.update_room_stay_helpers import (
    Update<PERSON>oomStayHelpers,
)
from prometheus.application.booking.command_handlers.update_room_stay import (
    UpdateRoomStayCommandHandler,
)
from prometheus.application.booking.command_handlers.update_room_stay_dates import (
    UpdateRoomStayDatesCommandHandler,
)
from prometheus.application.booking.command_handlers.update_room_stay_rate_plan import (
    UpdateRoomStayRatePlanCommandHandler,
)
from prometheus.application.booking.dtos.room_stay_update_dto import RoomStayUpdateDto
from prometheus.application.booking.helpers.room_stay_helper import (
    RoomStayHelperService,
)
from prometheus.application.decorators import session_manager
from prometheus.application.housekeeping.house_status_service import HouseStatusService
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.common.helpers import occupancy_change_handler
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.inventory.inventory_requirement import InventoryRequirement
from prometheus.domain.inventory.inventory_requirement_service import (
    InventoryRequirementService,
)
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.exceptions import ValidationException
from ths_common.value_objects import RoomStayConfig

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        UpdateRoomStayDatesCommandHandler,
        UpdateRoomStayRatePlanCommandHandler,
        AllocateRoomCommandHandler,
        UpdateRoomStayHelpers,
        InventoryApplicationService,
        UpdateRoomStayCommandHandler,
        InventoryBlockRepository,
    ]
)
class UpdateRoomStaysCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        update_room_stay_date_command_handler: UpdateRoomStayDatesCommandHandler,
        update_room_stay_rate_plan_command_handler: UpdateRoomStayRatePlanCommandHandler,
        allocate_room_command_handler: AllocateRoomCommandHandler,
        update_room_stay_helper: UpdateRoomStayHelpers,
        inventory_application_service: InventoryApplicationService,
        update_room_stay_command_handler: UpdateRoomStayCommandHandler,
        inventory_block_repository: InventoryBlockRepository,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.update_room_stay_date_command_handler = (
            update_room_stay_date_command_handler
        )
        self.update_room_stay_rate_plan_command_handler = (
            update_room_stay_rate_plan_command_handler
        )
        self.allocate_room_command_handler = allocate_room_command_handler
        self.update_room_stay_helper = update_room_stay_helper
        self.inventory_application_service = inventory_application_service
        self.update_room_stay_command_handler = update_room_stay_command_handler
        self.inventory_block_repository = inventory_block_repository

    @session_manager(commit=True)
    @audit(audit_type=AuditType.ROOM_STAY_DATES_CHANGED)
    @audit_housekeeping(action_performed="room_change")
    def handle(
        self,
        booking_id,
        booking_version,
        multiple_room_stay_update_data,
        user_data,
    ):
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, user_data=user_data
        )
        booking_aggregate.fail_if_outdated_version(booking_version)
        if booking_aggregate.is_cancelled() or booking_aggregate.is_noshow():
            raise ValidationException(ApplicationErrors.INVALID_BOOKING_FOR_EDIT)

        hotel_id = booking_aggregate.booking.hotel_id
        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )

        hotel_aggregate = crs_context_middleware.set_hotel_context(hotel_id)

        hotel_context = crs_context.get_hotel_context()
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)

        (
            new_inventory_requirements,
            old_inventory_requirements,
            room_stays,
            room_ids_for_house_status_update,
        ) = self._update_multiple_room_stays(
            bill_aggregate,
            booking_aggregate,
            hotel_aggregate,
            hotel_context,
            hotel_id,
            multiple_room_stay_update_data,
            user_data,
        )

        self.inventory_application_service.update_inventory(
            release_inventory=old_inventory_requirements,
            block_inventory=new_inventory_requirements,
            user_action="update_room_stay_dates",
        )

        self.update_room_stay_helper.refresh_room_stay_overflow(
            old_inventory_requirements, new_inventory_requirements, booking_aggregate
        )

        self.bill_repository.update(bill_aggregate)
        self.booking_repository.update(booking_aggregate)

        if room_ids_for_house_status_update:
            locate_instance(HouseStatusService).set_house_status_for_rooms(
                hotel_id=hotel_id, room_ids=room_ids_for_house_status_update
            )

        room_stay_change_event = RoomStayHelperService.generate_room_stay_change_event(
            booking_aggregate,
            [room_stay.room_stay_id for room_stay in room_stays],
            user_action="update",
        )
        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            booking_sub_resource_change_event=room_stay_change_event,
            user_action="update_room_stay_dates",
        )
        return room_stays, booking_aggregate.current_version()

    def _update_multiple_room_stays(
        self,
        bill_aggregate,
        booking_aggregate,
        hotel_aggregate,
        hotel_context,
        hotel_id,
        multiple_room_stay_update_data,
        user_data,
    ):
        room_stays = []
        old_inventory_requirements = InventoryRequirement(hotel_id)
        new_inventory_requirements = InventoryRequirement(hotel_id)
        room_ids_for_house_status_update = []
        disallow_charge_addition_rooms = []
        for room_stay_update_data in multiple_room_stay_update_data:
            room_stay_id = room_stay_update_data['room_stay_id']
            room_stay = booking_aggregate.get_room_stay(room_stay_id)
            if room_stay.is_cancelled() or room_stay.is_noshow():
                raise ValidationException(ApplicationErrors.INVALID_ROOM_FOR_EDIT)

            room_stay_update_dto = RoomStayUpdateDto(
                room_stay,
                room_stay_update_data,
                self.update_room_stay_helper.derive_checkin_datetime_for_new_room_allocation(
                    room_stay
                ),
            )

            new_room_stay_config = room_stay_update_dto.new_room_stay_config

            current_room_stay_config = RoomStayConfig(
                room_type_id=room_stay.room_type_id,
                checkin_date=room_stay.checkin_date,
                checkout_date=room_stay.checkout_date,
            )

            new_room_allocation = None
            service_cleanup_effects = None
            if room_stay_update_dto.is_update_for_stay_date():
                side_effects = (
                    self.update_room_stay_date_command_handler.update_room_stay_dates(
                        booking_aggregate,
                        bill_aggregate,
                        room_stay,
                        new_room_stay_config,
                        user_data,
                        room_stay_update_dto,
                        hotel_aggregate,
                        hotel_context,
                    )
                )
                room_ids_for_house_status_update.extend(
                    side_effects.room_ids_for_house_status_update
                )
                service_cleanup_effects = side_effects.service_cleanup_effects

            elif room_stay_update_dto.is_update_for_room_type():
                side_effects = (
                    self.allocate_room_command_handler.update_room_stay_room_type(
                        booking_aggregate,
                        bill_aggregate,
                        room_stay,
                        new_room_stay_config,
                        room_stay_update_dto,
                        hotel_aggregate,
                        hotel_context,
                        user_data,
                    )
                )
                new_room_allocation = side_effects.new_room_allocation
                room_ids_for_house_status_update.extend(
                    side_effects.room_ids_for_house_status_update
                )
                service_cleanup_effects = side_effects.service_cleanup_effects

            elif room_stay_update_dto.is_update_for_rate_plan():
                side_effects = self.update_room_stay_rate_plan_command_handler.update_room_stay_rate_plan(
                    booking_aggregate,
                    bill_aggregate,
                    room_stay,
                    room_stay_update_dto,
                    user_data,
                    hotel_aggregate,
                    hotel_context,
                )
                service_cleanup_effects = side_effects
            elif room_stay_update_dto.is_update_for_disallow_charge_addition():
                disallow_charge_addition_rooms.append(room_stay_update_dto)

            room_stay.refresh_allowed_actions(booking_aggregate._is_soft_booking())

            if new_room_stay_config != current_room_stay_config:
                (
                    new_inventory_requirement,
                    old_inventory_requirement,
                ) = self.update_room_stay_helper.get_change_in_inventory(
                    current_room_stay_config,
                    hotel_id,
                    new_room_stay_config,
                    new_room_allocation,
                )

                old_inventory_requirements.merge(old_inventory_requirement)
                new_inventory_requirements.merge(new_inventory_requirement)

            if service_cleanup_effects:
                release_requirement = InventoryRequirementService.build_inventory_requirement_from_inventory_blocks(
                    booking_aggregate.hotel_id,
                    service_cleanup_effects.inventory_blocks_released,
                )
                old_inventory_requirements.merge(release_requirement)
                self.inventory_block_repository.update_all(
                    service_cleanup_effects.inventory_blocks_released
                )

            occupancy_change_handler.update_occupancy_details_in_booked_charges(
                booking_aggregate, bill_aggregate, room_stay=room_stay
            )
            room_stays.append(room_stay)

        if disallow_charge_addition_rooms:
            self.update_room_stay_command_handler.update_room_stay_disallow_charge_addition(
                booking_aggregate, disallow_charge_addition_rooms
            )

        return (
            new_inventory_requirements,
            old_inventory_requirements,
            room_stays,
            room_ids_for_house_status_update,
        )
