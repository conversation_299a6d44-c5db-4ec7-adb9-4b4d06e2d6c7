import logging

from object_registry import register_instance
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.services.attachment_service import AttachmentService
from ths_common.constants.audit_trail_constants import AuditType

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        AttachmentService,
    ]
)
class CreateAttachmentCommandHandler:
    def __init__(
        self,
        attachment_service: AttachmentService,
    ):
        self.attachment_service = attachment_service

    @session_manager(commit=True)
    @audit(audit_type=AuditType.ATTACHMENT_ADDED)
    @set_hotel_context()
    def handle(
        self, booking_id, attachment_data_list, source, user_data, hotel_aggregate=None
    ):
        attachment_aggregates = self.attachment_service.add_attachment(
            booking_id, attachment_data_list, source, user_data
        )
        return attachment_aggregates
