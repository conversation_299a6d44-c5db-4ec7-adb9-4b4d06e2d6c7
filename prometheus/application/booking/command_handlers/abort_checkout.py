from object_registry import register_instance
from prometheus.application.decorators import session_manager
from prometheus.domain.billing.repositories import BillRepository, InvoiceRepository
from prometheus.domain.booking.repositories import (
    BookingInvoiceGroupRepository,
    BookingRepository,
)
from ths_common.constants.booking_constants import InvoiceGroupStatus


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        BookingInvoiceGroupRepository,
        InvoiceRepository,
    ]
)
class AbortCheckoutCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        booking_invoice_group_repository: BookingInvoiceGroupRepository,
        invoice_repository: InvoiceRepository,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.booking_invoice_group_repository = booking_invoice_group_repository
        self.invoice_repository = invoice_repository

    @session_manager(commit=True)
    def handle(self, booking_id, booking_invoice_group_id):
        # TODO Add Audit
        booking_aggregate = self.booking_repository.load(booking_id)
        bill_aggregate = self.bill_repository.load_for_update(booking_aggregate.bill_id)
        preview_invoice_group_aggregate, _ = self._cancel_preview_invoices(
            booking_invoice_group_id
        )
        deleted_charges = []
        if preview_invoice_group_aggregate.booking_invoice_group.newly_added_charge_ids:
            deleted_charges = bill_aggregate.cancel_charges(
                preview_invoice_group_aggregate.newly_added_charge_ids
            )
        self.bill_repository.update(bill_aggregate)
        return deleted_charges

    def _cancel_preview_invoices(self, booking_invoice_group_id):
        preview_invoice_group_aggregate = (
            self.booking_invoice_group_repository.load_for_update(
                booking_invoice_group_id
            )
        )

        invoice_ids = preview_invoice_group_aggregate.invoice_ids

        preview_invoice_group_aggregate.update_status(InvoiceGroupStatus.CANCELLED)

        self.booking_invoice_group_repository.update(preview_invoice_group_aggregate)

        preview_invoice_aggregates = self.invoice_repository.load_all_for_update(
            invoice_ids=invoice_ids
        )
        if preview_invoice_aggregates:
            for invoice_aggregate in preview_invoice_aggregates:
                invoice_aggregate.cancel()
            self.invoice_repository.update_all(preview_invoice_aggregates)
        return preview_invoice_group_aggregate, preview_invoice_aggregates
