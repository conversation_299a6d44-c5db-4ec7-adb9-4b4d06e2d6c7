import copy
import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.ta_commission_helper import T<PERSON><PERSON><PERSON>Helper
from prometheus.application.helpers.tax_calculation_helper import Tax<PERSON><PERSON><PERSON>tionHelper
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.room_stay_overflow_repository import (
    RoomStayOverflowRepository,
)
from prometheus.domain.catalog.repositories import SkuCategoryRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.policy.context import TenantFactsContext
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.facts import Facts
from prometheus.infrastructure.external_clients.core.constants import CORPORATE_CHANNELS
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import BilledEntityCategory, PaymentModes
from ths_common.constants.booking_constants import BookingStatus, GuaranteeTypes
from ths_common.value_objects import BookingBillParentInfo, GuaranteeInformation

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        HotelConfigRepository,
        JobSchedulerService,
        RoomStayOverflowRepository,
        BilledEntityService,
        TACommissionHelper,
        TaxCalculationHelper,
        SkuCategoryRepository,
        TenantSettings,
    ]
)
class PatchBookingCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        job_scheduler_service: JobSchedulerService,
        room_stay_overflow_repository: RoomStayOverflowRepository,
        billed_entity_service: BilledEntityService,
        ta_commission_helper: TACommissionHelper,
        tax_calculation_helper: TaxCalculationHelper,
        sku_category_repository: SkuCategoryRepository,
        tenant_settings: TenantSettings,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.job_scheduler_service = job_scheduler_service
        self.room_stay_overflow_repository = room_stay_overflow_repository
        self.billed_entity_service = billed_entity_service
        self.ta_commission_helper = ta_commission_helper
        self.tax_calculation_helper = tax_calculation_helper
        self.sku_category_repository = sku_category_repository
        self.tenant_settings = tenant_settings

    @session_manager(commit=True)
    @audit(audit_type=AuditType.BOOKING_DETAILS_MODIFIED)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        booking_version,
        booking_change,
        user_data,
        hotel_aggregate=None,
    ):
        """
        Validations:
            - Booking Version check -- done

        :param booking_id:
        :param booking_version:
        :param booking_change: Dictionary loaded from EditBookingSchema
        :param user_data:
        :param hotel_aggregate:
        :return:
        """
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, user_data=user_data
        )
        hotel_id = booking_aggregate.booking.hotel_id

        if not hotel_aggregate:
            crs_context.set_hotel_context(
                self.hotel_repository.load(hotel_id),
                hotel_config_aggregate=self.hotel_config_repository.load(
                    booking_aggregate.booking.hotel_id
                ),
            )

        crs_context.set_current_booking(booking_aggregate)
        corporate_channels = self.tenant_settings.get_setting_value(CORPORATE_CHANNELS)
        RuleEngine.action_allowed(
            action="edit_booking",
            facts=Facts(
                user_type=user_data.user_type,
                action_payload=booking_change,
                hotel_context=crs_context.get_hotel_context(),
                booking_aggregate=booking_aggregate,
                tenant_facts_context=TenantFactsContext(
                    corporate_channels=corporate_channels
                ),
            ),
            fail_on_error=True,
        )

        booking_changed = False
        bill_changed = False
        bill_aggregate = None

        if (
            'comments' in booking_change
            and booking_change.get('comments') != booking_aggregate.booking.comments
        ):
            booking_changed = True
            booking_aggregate.update_comments(booking_change.get('comments'))

        if (
            'extra_information' in booking_change
            and booking_change.get('extra_information')
            != booking_aggregate.booking.extra_information
        ):
            booking_changed = True
            booking_aggregate.update_extra_information(
                booking_change.get('extra_information')
            )

        if (
            'guarantee_information' in booking_change
            and booking_change['guarantee_information']
            != booking_aggregate.guarantee_information
        ):
            RuleEngine.action_allowed(
                action="edit_guarantee_information",
                facts=Facts(
                    user_type=user_data.user_type, booking_aggregate=booking_aggregate
                ),
                fail_on_error=True,
            )
            booking_changed = True
            new_guarantee_information = booking_change['guarantee_information']
            is_guarantee_enabled = self.tenant_settings.is_guarantee_enabled(hotel_id)
            if is_guarantee_enabled and not new_guarantee_information:
                bill_aggregate = self.bill_repository.load_for_update(
                    booking_aggregate.bill_id
                )
                if bill_aggregate.get_net_payment(
                    exclude_modes=[PaymentModes.TREEBO_POINTS]
                ):
                    new_guarantee_information = GuaranteeInformation(
                        guarantee_type=GuaranteeTypes.PAYMENT_GUARANTEE
                    )
            is_state_transition_allowed_with_guarantee = is_guarantee_enabled
            booking_aggregate.update_guarantee_information(
                new_guarantee_information, is_state_transition_allowed_with_guarantee
            )
            if (
                booking_aggregate.guarantee_information
                and is_state_transition_allowed_with_guarantee
                and booking_aggregate.booking.status
                in [BookingStatus.RESERVED, BookingStatus.TEMPORARY]
            ):
                booking_aggregate.confirm()

        if (
            'reference_number' in booking_change
            and booking_change.get('reference_number')
            != booking_aggregate.booking.reference_number
        ):
            booking_changed = True
            bill_changed = True
            bill_aggregate = (
                self.bill_repository.load_for_update(booking_aggregate.bill_id)
                if not bill_aggregate
                else bill_aggregate
            )
            booking_aggregate.update_reference_number(
                booking_change.get('reference_number')
            )
            parent_info = BookingBillParentInfo(
                booking_aggregate.booking.booking_id,
                booking_aggregate.booking.reference_number,
                creation_date=booking_aggregate.booking.created_at,
                checkin_date=booking_aggregate.booking.checkin_date,
                checkout_date=booking_aggregate.booking.checkout_date,
            )
            bill_aggregate.update_parent_info(parent_info)

        if (
            'hold_till' in booking_change
            and booking_change.get('hold_till') != booking_aggregate.booking.hold_till
        ):
            booking_changed = True
            booking_aggregate.update_hold_till(booking_change.get('hold_till'))
            booking_aggregate.fail_if_invalid_hold_till_value()
            self.job_scheduler_service.schedule_soft_booking_job(booking_aggregate)

        if (
            'group_name' in booking_change
            and booking_change.get('group_name') != booking_aggregate.booking.group_name
        ):
            booking_changed = True
            booking_aggregate.update_group_name(booking_change.get('group_name'))
        ta_commission_rule_before_update = copy.deepcopy(
            booking_aggregate.get_ta_commission_rule()
        )
        booking_owner = booking_aggregate.get_booking_owner()
        sku_category_aggregates = self.sku_category_repository.load_all()
        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in sku_category_aggregates
        }
        if (
            booking_change.get('travel_agent_details')
            and booking_change.get('travel_agent_details')
            != booking_aggregate.get_travel_agent_details()
        ):
            booking_changed = True
            bill_changed = True
            booking_aggregate.update_travel_agent_details(
                booking_change.get('travel_agent_details')
            )
            bill_aggregate = (
                self.bill_repository.load_for_update(booking_aggregate.booking.bill_id)
                if not bill_aggregate
                else bill_aggregate
            )
            self.billed_entity_service.create_or_update_billed_entity_from_booking_travel_agent_details(
                booking_aggregate, bill_aggregate
            )
            travel_agent_details = booking_aggregate.get_travel_agent_details()
            if travel_agent_details.is_tax_determiners_changed:
                self._recalculate_tax_for_billed_entity(
                    BilledEntityCategory.TRAVEL_AGENT,
                    bill_aggregate,
                    booking_aggregate,
                    grouped_sku_categories,
                )
            if (
                travel_agent_details.legal_details
                and booking_owner.company_billed_entity_id
                == travel_agent_details.billed_entity_id
            ):
                booking_aggregate.update_gst_details_of_booking_owner_from_legal_details(
                    travel_agent_details.legal_details
                )
                booking_owner = booking_aggregate.get_booking_owner()
                if booking_owner.is_tax_determiners_changed:
                    self._recalculate_tax_for_billed_entity(
                        BilledEntityCategory.BOOKER,
                        bill_aggregate,
                        booking_aggregate,
                        grouped_sku_categories,
                    )
        if (
            booking_change.get('company_details')
            and booking_change.get('company_details')
            != booking_aggregate.get_company_details()
        ):
            booking_changed = True
            bill_changed = True
            bill_aggregate = (
                self.bill_repository.load_for_update(booking_aggregate.booking.bill_id)
                if not bill_aggregate
                else bill_aggregate
            )
            booking_aggregate.update_company_details(
                booking_change.get('company_details')
            )
            self.billed_entity_service.create_or_update_billed_entity_from_booking_company_details(
                booking_aggregate, bill_aggregate
            )
            company_details = booking_aggregate.get_company_details()
            if company_details.is_tax_determiners_changed:
                self._recalculate_tax_for_billed_entity(
                    BilledEntityCategory.BOOKER_COMPANY,
                    bill_aggregate,
                    booking_aggregate,
                    grouped_sku_categories,
                )
            if (
                company_details.legal_details
                and booking_owner.company_billed_entity_id
                == company_details.billed_entity_id
            ):
                booking_aggregate.update_gst_details_of_booking_owner_from_legal_details(
                    company_details.legal_details
                )
                booking_owner = booking_aggregate.get_booking_owner()
                if booking_owner.is_tax_determiners_changed:
                    self._recalculate_tax_for_billed_entity(
                        BilledEntityCategory.BOOKER,
                        bill_aggregate,
                        booking_aggregate,
                        grouped_sku_categories,
                    )

        if 'segments' in booking_change:
            booking_changed = True
            booking_aggregate.update_segments(booking_change['segments'])

        if 'account_details' in booking_change:
            booking_changed = True
            booking_aggregate.update_account_details(booking_change['account_details'])

        if (
            'discount_details' in booking_change
            and booking_change['discount_details']
            != booking_aggregate.booking.discount_details
        ):
            booking_changed = True
            booking_aggregate.update_discount_details(
                booking_change['discount_details']
            )

        if booking_changed:
            bill_aggregate = (
                self.bill_repository.load_for_update(booking_aggregate.booking.bill_id)
                if not bill_aggregate
                else bill_aggregate
            )
            if (
                crs_context.is_treebo_tenant()
                and booking_aggregate.is_commission_applicable()
            ):
                self.ta_commission_helper.handle_ta_commission_recalculation_on_booking(
                    booking_aggregate, bill_aggregate, ta_commission_rule_before_update
                )
            self.booking_repository.update(booking_aggregate)
            if bill_changed:
                crs_context.set_current_bill(bill_aggregate)
                self.bill_repository.update(bill_aggregate)
            IntegrationEventApplicationService.create_booking_updated_event(
                booking_aggregate=booking_aggregate,
                bill_aggregate=bill_aggregate if bill_changed else None,
                user_action="edit_booking",
            )
        booking_aggregate.refresh_allowed_actions()
        return booking_aggregate

    def _recalculate_tax_for_billed_entity(
        self,
        billed_entity_category,
        bill_aggregate,
        booking_aggregate,
        grouped_sku_categories,
    ):
        billed_entity = bill_aggregate.get_billed_entity_for_category(
            billed_entity_category
        )
        if billed_entity:
            self.tax_calculation_helper.recalculate_taxes_on_billed_entity(
                billed_entity, bill_aggregate, booking_aggregate, grouped_sku_categories
            )
