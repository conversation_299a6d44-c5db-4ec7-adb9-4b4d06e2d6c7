from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.exceptions import InvalidActionError
from prometheus.domain.inventory.inventory_requirement_service import (
    InventoryRequirementService,
)


@register_instance(
    dependencies=[
        InventoryApplicationService,
        RoomStayOverflowService,
    ]
)
class UpdateRoomStayHelpers:
    def __init__(
        self,
        inventory_application_service: InventoryApplicationService,
        room_stay_overflow_service: RoomStayOverflowService,
    ):
        self.inventory_application_service = inventory_application_service
        self.inventory_requirement_service = InventoryRequirementService()
        self.room_stay_overflow_service = room_stay_overflow_service

    @staticmethod
    def derive_checkin_datetime_for_new_room_allocation(room_stay):
        hotel_context = crs_context.get_hotel_context()
        current_business_date = hotel_context.current_date()

        if current_business_date > room_stay.stay_end:
            raise InvalidActionError(
                error=BookingErrors.INVALID_ROOM_ALLOCATION_CHECKIN_DATE,
                description="Room Allocation not allowed after room stay checkout date",
            )

        if current_business_date < room_stay.stay_start:
            # Future room allocation
            return room_stay.checkin_date

        else:
            if current_business_date == dateutils.current_date():
                time_of_checkin = dateutils.current_time()
                if time_of_checkin < hotel_context.switch_over_time:
                    time_of_checkin = hotel_context.switch_over_time
                room_allocation_checkin_time = dateutils.datetime_at_given_time(
                    current_business_date, time_of_checkin
                )

            else:
                # Business date is less than current date
                # Business Date is 11th, current date is 12th, or 13th
                room_allocation_checkin_time = dateutils.datetime_at_max_time_of_day(
                    current_business_date
                )

            (
                min_room_allocation_checkin,
                max_room_allocation_checkin,
            ) = room_stay.get_allowed_room_allocation_checkin_window()

            if (
                max_room_allocation_checkin
                > room_allocation_checkin_time
                >= min_room_allocation_checkin
            ):
                return room_allocation_checkin_time

            elif room_allocation_checkin_time < min_room_allocation_checkin:
                return min_room_allocation_checkin

            else:
                return max_room_allocation_checkin

    def create_and_update_room_allotment(
        self, room_stay, new_room_allocation, previous_room_allocation, hotel_context
    ):
        room_ids = []
        if new_room_allocation:
            room_allocation = new_room_allocation

            allotment_id = self.inventory_application_service.allot_room_inventory(
                hotel_id=hotel_context.hotel_id,
                room_id=room_allocation.room_id,
                start_time=room_allocation.checkin_date,
                expected_end_time=room_stay.checkout_date,
                hotel_context=hotel_context,
                is_booking_checked_in=room_stay.is_checked_in()
                or room_stay.is_part_checked_in(),
            )
            room_allocation.set_allotment_id(allotment_id)
            room_ids.append(new_room_allocation.room_id)

        if previous_room_allocation:
            room_allotment_aggregate = (
                self.inventory_application_service.set_room_allotment_actual_end_time(
                    hotel_id=hotel_context.hotel_id,
                    room_id=previous_room_allocation.room_id,
                    allotment_id=previous_room_allocation.room_allotment_id,
                    actual_end_time=previous_room_allocation.checkout_date,
                    overridden_room_allotment=previous_room_allocation.room_allotment_id
                    if previous_room_allocation.is_overridden()
                    else None,
                )
            )

            hotel_context = crs_context.get_hotel_context()
            if hotel_context.housekeeping_enabled:
                housekeeping_record = room_allotment_aggregate.housekeeping_record
                IntegrationEventApplicationService.create_housekeeping_status_event(
                    housekeeping_record=housekeeping_record,
                    user_action="update_room_stay_room_type",
                )
            room_ids.append(previous_room_allocation.room_id)

        return room_ids

    def update_inventory_on_room_update(
        self,
        hotel_id,
        booking_aggregate,
        current_room_stay_config,
        new_room_stay_config,
        new_room_allocation,
        addon_inventories_to_release=None,
        user_action="update_room_stay_dates",
    ):
        if (
            new_room_stay_config == current_room_stay_config
            and addon_inventories_to_release is None
        ):
            return

        new_inventory_requirement = None
        old_inventory_requirement = None

        if new_room_stay_config != current_room_stay_config:
            (
                new_inventory_requirement,
                old_inventory_requirement,
            ) = self.get_change_in_inventory(
                current_room_stay_config,
                hotel_id,
                new_room_stay_config,
                new_room_allocation,
            )

        if addon_inventories_to_release:
            old_inventory_requirement = self._merge_addon_inventory_requirements(
                addon_inventories_to_release,
                hotel_id,
                old_inventory_requirement,
            )

        self.inventory_application_service.update_inventory(
            release_inventory=old_inventory_requirement,
            block_inventory=new_inventory_requirement,
            user_action=user_action,
        )

        self.refresh_room_stay_overflow(
            old_inventory_requirement, new_inventory_requirement, booking_aggregate
        )

    def _merge_addon_inventory_requirements(
        self, addon_inventories_to_release, hotel_id, old_inventory_requirement
    ):
        inventory_requirement = self.inventory_requirement_service.build_inventory_requirement_from_inventory_blocks(
            hotel_id, addon_inventories_to_release
        )
        if old_inventory_requirement:
            old_inventory_requirement.merge(inventory_requirement)
        else:
            old_inventory_requirement = inventory_requirement
        return old_inventory_requirement

    def get_change_in_inventory(
        self,
        current_room_stay_config,
        hotel_id,
        new_room_stay_config,
        new_room_allocation=None,
    ):
        if current_room_stay_config.room_type_id != new_room_stay_config.room_type_id:
            if new_room_allocation:
                # If this is room type change post checkin, the inventory release should happen starting from
                # when the new room allocation is created
                release_inventory_config = [
                    current_room_stay_config.with_checkin_date(
                        new_room_allocation.checkin_date
                    )
                ]
                block_inventory_config = [
                    new_room_stay_config.with_checkin_date(
                        new_room_allocation.checkin_date
                    )
                ]
            else:
                release_inventory_config = [current_room_stay_config]
                block_inventory_config = [new_room_stay_config]
        else:
            release_inventory_config = current_room_stay_config.difference(
                new_room_stay_config
            )
            block_inventory_config = new_room_stay_config.difference(
                current_room_stay_config
            )

        old_inventory_requirement = (
            self.inventory_requirement_service.build_inventory_requirement(
                hotel_id, release_inventory_config
            )
            if release_inventory_config
            else None
        )
        new_inventory_requirement = (
            self.inventory_requirement_service.build_inventory_requirement(
                hotel_id, block_inventory_config
            )
            if block_inventory_config
            else None
        )

        return new_inventory_requirement, old_inventory_requirement

    def refresh_room_stay_overflow(
        self, old_inventory_requirement, new_inventory_requirement, booking_aggregate
    ):
        if not (old_inventory_requirement or new_inventory_requirement):
            return
        start_date, end_date = None, None
        room_type_ids = set()
        if old_inventory_requirement:
            start_date = old_inventory_requirement.min_date
            end_date = old_inventory_requirement.max_date
            room_type_ids = old_inventory_requirement.room_type_ids

        if new_inventory_requirement:
            start_date = (
                min(start_date, new_inventory_requirement.min_date)
                if start_date
                else new_inventory_requirement.min_date
            )
            end_date = (
                max(end_date, new_inventory_requirement.max_date)
                if end_date
                else new_inventory_requirement.max_date
            )
            room_type_ids.update(new_inventory_requirement.room_type_ids)
            room_type_ids = list(room_type_ids)

        self.room_stay_overflow_service.refresh_overflow_recompute_unmarks(
            start_date, end_date, room_type_ids, booking_aggregate
        )
