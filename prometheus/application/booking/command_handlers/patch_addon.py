import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.helpers.expense_service import (
    ExpenseApplicationService,
)
from prometheus.application.commands.consume_charge import ConsumeChargeCommand
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories import AddonRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)
from prometheus.domain.booking.services.addon_domain_service import AddonDomainService
from prometheus.domain.catalog.repositories import SkuCategoryRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.policy.context import TenantFactsContext
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import AddonFacts
from prometheus.infrastructure.external_clients.core.constants import CORPORATE_CHANNELS
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.exceptions import InvalidOperationError

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        RoomTypeRepository,
        ExpenseItemRepository,
        SkuCategoryRepository,
        ExpenseApplicationService,
        AddonRepository,
        TenantSettings,
    ]
)
class PatchAddonCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        room_type_repository: RoomTypeRepository,
        expense_item_repository: ExpenseItemRepository,
        sku_category_repository: SkuCategoryRepository,
        expense_application_service: ExpenseApplicationService,
        addon_repository: AddonRepository,
        tenant_settings: TenantSettings,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.room_type_repository = room_type_repository
        self.expense_item_repository = expense_item_repository
        self.sku_category_repository = sku_category_repository
        self.expense_application_service = expense_application_service
        self.addon_repository = addon_repository
        self.addon_domain_service = AddonDomainService()
        self.tenant_settings = tenant_settings

    @staticmethod
    def _get_linked_charges(expenses):
        return [expense.charge_id for expense in expenses]

    @session_manager(commit=True)
    @audit(audit_type=AuditType.ADDON_MODIFIED)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        addon_id,
        addon_version,
        addon_update_data,
        user_data=None,
        hotel_aggregate=None,
    ):
        booking_aggregate = self.booking_repository.load_for_update(booking_id)

        if not hotel_aggregate:
            hotel_aggregate = crs_context_middleware.set_hotel_context(
                booking_aggregate.booking.hotel_id
            )

        hotel_context = crs_context.get_hotel_context()
        crs_context.set_current_booking(booking_aggregate)

        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )
        addon_aggregate = self.addon_repository.load_for_update(
            addon_id,
            booking_id=booking_id,
            version=addon_version,
            include_deleted=False,
        )

        if addon_aggregate.linked:
            raise InvalidOperationError(
                error=ApplicationErrors.LINKED_ADDON_UPDATE_API_NOT_SUPPORTED
            )

        elif addon_aggregate.is_rate_plan_addon:
            raise InvalidOperationError(
                error=ApplicationErrors.RATE_PLAN_ADDON_UPDATE_API_NOT_SUPPORTED
            )
        corporate_channels = self.tenant_settings.get_setting_value(CORPORATE_CHANNELS)
        RuleEngine.action_allowed(
            action="edit_addon",
            facts=AddonFacts(
                addon=addon_aggregate.addon,
                user_type=user_data.user_type,
                action_payload=addon_update_data,
                hotel_aggregate=hotel_aggregate,
                booking_aggregate=booking_aggregate,
                hotel_context=hotel_context,
                tenant_facts_context=TenantFactsContext(
                    corporate_channels=corporate_channels
                ),
            ),
            fail_on_error=True,
        )

        new_expenses, old_expenses = self.addon_domain_service.update_addon(
            booking_aggregate, addon_aggregate, addon_update_data
        )

        old_charges = [expense.charge_id for expense in old_expenses]
        bill_aggregate.cancel_charges(
            old_charges, posting_date=crs_context.get_hotel_context().current_date()
        )

        expense_item = self.expense_item_repository.load(
            addon_aggregate.addon.expense_item_id
        )
        sku_category_aggregate = self.sku_category_repository.load(
            expense_item.sku_category_id
        )
        room_stay = booking_aggregate.get_room_stay(addon_aggregate.addon.room_stay_id)

        self.expense_application_service.create_charges_for_expenses(
            bill_aggregate,
            new_expenses,
            addon_aggregate.addon,
            expense_item,
            room_stay,
            booking_aggregate,
            sku_category_aggregate.sku_category,
        )

        if addon_aggregate.start_date < hotel_context.current_date():
            ConsumeChargeCommand(
                booking_aggregate,
                bill_aggregate,
                hotel_context.current_date(),
                hotel_context,
                self.room_type_repository.load_type_map(),
                is_checkout=room_stay.is_checked_out() if room_stay else False,
                exclude_charge_ids=None,
            ).execute()

        self.addon_repository.update(addon_aggregate)
        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)

        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            user_action="update_addon",
        )
        return addon_aggregate
