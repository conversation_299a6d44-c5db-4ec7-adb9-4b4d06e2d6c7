from object_registry import register_instance
from prometheus.application import crs_context_middleware
from prometheus.application.booking.helpers.e_reg_card_template_service import (
    ERegCardTemplateService,
)
from prometheus.application.decorators import session_manager
from prometheus.domain.booking.repositories import BookingRepository


@register_instance(dependencies=[BookingRepository, ERegCardTemplateService])
class ERegCardGenerationCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        eregcard_template_service: ERegCardTemplateService,
    ):
        self.booking_repository = booking_repository
        self.eregcard_template_service = eregcard_template_service

    @session_manager(commit=True)
    def handle(
        self,
        booking_id,
        customer_ids=None,
        async_mode=False,
    ):
        booking_aggregate = self.booking_repository.load(booking_id)

        hotel_id = booking_aggregate.booking.hotel_id
        hotel_aggregate = crs_context_middleware.set_hotel_context(hotel_id)

        if async_mode:
            return self.eregcard_template_service.schedule_eregcard_url_generation(
                booking_id, hotel_id, customer_ids=customer_ids
            )

        customers = None
        if customer_ids:
            customers = booking_aggregate.get_customers(customer_ids)

        self.eregcard_template_service.generate_eregcard_url(
            booking_aggregate, hotel_aggregate, customers=customers
        )

        self.booking_repository.save_eregcard_urls(booking_aggregate)
        return booking_aggregate
