from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit, audit_housekeeping
from prometheus.application.booking.command_handlers.expenses.expense_service_handler import (
    StayServiceCleanupFacade,
)
from prometheus.application.booking.command_handlers.helpers.update_room_stay_helpers import (
    UpdateRoomStayHelpers,
)
from prometheus.application.booking.dtos.expense_cleanup_side_effect_dto import (
    ExpenseServiceCleanupSideEffects,
)
from prometheus.application.booking.dtos.room_stay_update_dto import RoomStayUpdateDto
from prometheus.application.booking.helpers.room_stay_dto_creator import (
    RoomStayDtoCreator,
)
from prometheus.application.booking.helpers.room_stay_helper import (
    RoomStayHelperService,
)
from prometheus.application.booking.helpers.room_stay_price_change_handler import (
    RoomStayPriceChangeHandler,
)
from prometheus.application.booking.helpers.update_room_stay_rate_plan import (
    UpdateRoomStayRatePlanCommand,
)
from prometheus.application.decorators import session_manager
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.inventory.command_handlers.update_inventory_block import (
    UpdateInventoryBlockCommandHandler,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services import TaxService
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import SkuCategoryRepository
from shared_kernel.infrastructure.external_clients.rate_manager_client import (
    RateManagerClient,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        RoomStayPriceChangeHandler,
        TaxService,
        RateManagerClient,
        SkuCategoryRepository,
        TenantSettings,
        RoomStayDtoCreator,
        UpdateRoomStayHelpers,
        TACommissionHelper,
        UpdateInventoryBlockCommandHandler,
    ]
)
class UpdateRoomStayRatePlanCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        room_stay_price_change_handler: RoomStayPriceChangeHandler,
        tax_service: TaxService,
        rate_manager_client: RateManagerClient,
        sku_category_repository: SkuCategoryRepository,
        tenant_settings: TenantSettings,
        room_stay_dto_creator: RoomStayDtoCreator,
        update_room_stay_helper: UpdateRoomStayHelpers,
        ta_commission_helper: TACommissionHelper,
        inventory_block_update_handler: UpdateInventoryBlockCommandHandler,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.room_stay_price_change_handler = room_stay_price_change_handler
        self.tax_service = tax_service
        self.rate_manager_client = rate_manager_client
        self.sku_category_repository = sku_category_repository
        self.tenant_settings = tenant_settings
        self.room_stay_dto_creator = room_stay_dto_creator
        self.update_room_stay_helper = update_room_stay_helper
        self.ta_commission_helper = ta_commission_helper
        self.inventory_block_update_handler = inventory_block_update_handler

    @session_manager(commit=True)
    @audit(audit_type=AuditType.RATE_PLAN_CHANGED)
    @audit_housekeeping(action_performed="update_rate_plan")
    def handle(
        self,
        booking_id,
        room_stay_id,
        booking_version,
        room_stay_update_data,
        user_data,
    ):
        """
        :param booking_id:
        :param room_stay_id:
        :param booking_version:
        :param room_stay_update_data: The dictionary constructed from UpdateRoomStayRatePlan
        :param user_data:
        :return:
        """
        (
            bill_aggregate,
            booking_aggregate,
            hotel_aggregate,
            hotel_context,
            _,
            room_stay,
        ) = self._load_aggregates_for_update(
            booking_id, booking_version, room_stay_id, user_data
        )
        is_rate_manager_enabled = self.tenant_settings.get_setting_value(
            TenantSettingName.RATE_MANAGER_ENABLED.value, booking_aggregate.hotel_id
        )
        if not is_rate_manager_enabled:
            raise ValidationException(
                ApplicationErrors.CANT_UPDATE_RATE_PLAN_AS_RATE_MANAGER_IS_DISABLED
            )
        if not room_stay.is_reserved():
            raise ValidationException(ApplicationErrors.INVALID_ROOM_FOR_RATE_PLAN_EDIT)

        room_stay_update_dto = RoomStayUpdateDto(
            room_stay,
            room_stay_update_data,
            self.update_room_stay_helper.derive_checkin_datetime_for_new_room_allocation(
                room_stay
            ),
        )

        service_cleanup_effects = self.update_room_stay_rate_plan(
            booking_aggregate,
            bill_aggregate,
            room_stay,
            room_stay_update_dto,
            user_data,
            hotel_aggregate,
            hotel_context,
        )

        if 'segments' in room_stay_update_data:
            booking_aggregate.update_segments(room_stay_update_data['segments'])

        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_for_room_stays(
                booking_aggregate, [room_stay], bill_aggregate
            )
        self.bill_repository.update(bill_aggregate)
        self.booking_repository.update(booking_aggregate)

        if service_cleanup_effects:
            self.inventory_block_update_handler.apply_inventory_block_release_updates(
                hotel_id=booking_aggregate.hotel_id,
                released_blocks=service_cleanup_effects.inventory_blocks_released,
                user_action="update_room_stay_rate_plan",
            )

        room_stay_change_event = RoomStayHelperService.generate_room_stay_change_event(
            booking_aggregate,
            [room_stay.room_stay_id],
            user_action="update",
        )
        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            booking_sub_resource_change_event=room_stay_change_event,
            user_action="update_room_stay_rate_plan",
        )

        room_stay.refresh_allowed_actions(booking_aggregate._is_soft_booking())
        return room_stay, booking_aggregate.current_version()

    def _load_aggregates_for_update(
        self, booking_id, booking_version, room_stay_id, user_data
    ):
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, user_data=user_data
        )
        booking_aggregate.fail_if_outdated_version(booking_version)
        if booking_aggregate.is_cancelled() or booking_aggregate.is_noshow():
            raise ValidationException(ApplicationErrors.INVALID_BOOKING_FOR_EDIT)

        hotel_id = booking_aggregate.booking.hotel_id
        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )

        room_stay = booking_aggregate.get_room_stay(room_stay_id)
        hotel_aggregate = crs_context_middleware.set_hotel_context(hotel_id)

        hotel_context = crs_context.get_hotel_context()
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)
        return (
            bill_aggregate,
            booking_aggregate,
            hotel_aggregate,
            hotel_context,
            hotel_id,
            room_stay,
        )

    def update_room_stay_rate_plan(
        self,
        booking_aggregate,
        bill_aggregate,
        room_stay,
        room_stay_update_dto,
        user_data,
        hotel_aggregate,
        hotel_context,
    ):
        sku_category_aggregates = self.sku_category_repository.load_all()
        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in sku_category_aggregates
        }

        service_cleanup_effects: ExpenseServiceCleanupSideEffects = (
            StayServiceCleanupFacade.clear_room_services(
                booking_aggregate,
                bill_aggregate,
                room_stay.room_stay_id,
                user_action='update_room_stay_rate_plan',
            )
        )

        UpdateRoomStayRatePlanCommand(
            booking_aggregate,
            bill_aggregate,
            room_stay,
            room_stay_update_dto,
            user_data,
            self.rate_manager_client,
            hotel_aggregate,
            hotel_context,
            self.tax_service,
            self.room_stay_price_change_handler,
            grouped_sku_categories,
            self.room_stay_dto_creator,
        ).execute()

        return service_cleanup_effects
