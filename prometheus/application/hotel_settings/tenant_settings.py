from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.hotel_settings.dtos.payment_config_dto import (
    PaymentConfigDto,
)
from prometheus.application.hotel_settings.dtos.refund_rule_dto import RefundRuleDto
from prometheus.domain.billing.dto.einvoice_config import EinvoiceConfig
from prometheus.domain.billing.dto.einvoicing_failure_handling_config import EInvoicingFailureHandlingConfig
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from ths_common.constants.tenant_settings_constants import TenantSettingName


@register_instance(dependencies=[CatalogServiceClient])
class TenantSettings(object):
    def __init__(self, catalog_service_client: CatalogServiceClient):
        self.catalog_service_client = catalog_service_client
        self.tenant_config_dict = dict()
        self.last_refresh_time = dict()
        # pylint: disable=not-an-iterable
        for tenant in TenantClient.get_active_tenants():
            self.tenant_config_dict[tenant.tenant_id] = dict()
            self.last_refresh_time[tenant.tenant_id] = dict()

    def _get_tenant_config(self, hotel_id=None):
        current_tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        tenant_config_dict = self.tenant_config_dict.get(current_tenant_id)
        last_refresh_time_dict = self.last_refresh_time.get(current_tenant_id)

        global_config = tenant_config_dict.get('global')
        global_config_last_refresh_time = last_refresh_time_dict.get('global')

        hotel_config = (
            tenant_config_dict.get(hotel_id)
            if tenant_config_dict and hotel_id
            else None
        )
        hotel_config_last_refresh_time = (
            last_refresh_time_dict.get(hotel_id)
            if last_refresh_time_dict and hotel_id
            else None
        )

        tenant_config = hotel_config if hotel_id else global_config
        last_refresh_time = (
            hotel_config_last_refresh_time
            if hotel_id
            else global_config_last_refresh_time
        )

        if tenant_config is None or last_refresh_time < dateutils.subtract(
            dateutils.current_datetime(), minutes=10
        ):
            tenant_configs = self.catalog_service_client.get_tenant_configs(hotel_id)
            tenant_config = {config.config_name: config for config in tenant_configs}
            if hotel_id:
                self.tenant_config_dict[current_tenant_id][hotel_id] = tenant_config
                self.last_refresh_time[current_tenant_id][
                    hotel_id
                ] = dateutils.current_datetime()
            else:
                self.tenant_config_dict[current_tenant_id]['global'] = tenant_config
                self.last_refresh_time[current_tenant_id][
                    'global'
                ] = dateutils.current_datetime()
        return tenant_config

    def get_setting_value(self, setting_name, hotel_id=None):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return None
        setting = tenant_config.get(setting_name)
        if not setting:
            return None
        return setting.get_config_value()

    def get_einvoicing_config(self, hotel_id):
        einvoicing_config = self.get_setting_value(
            TenantSettingName.E_INVOICING.value, hotel_id
        )
        return EinvoiceConfig(**einvoicing_config) if einvoicing_config else None

    def get_einvoicing_failure_handling_config(self, hotel_id=None):
        """
        {
            "enabled": true,
            "show_popup_on_failure": true,
            "required_privilege": "einvoicing_failure_handling"
        }
        """
        config = self.get_setting_value(
            TenantSettingName.E_INVOICING_FAILURE_HANDLING.value, hotel_id
        )
        return EInvoicingFailureHandlingConfig.from_dict(config) if config else None

    def get_disabled_jobs(self):
        tenant_config = self._get_tenant_config()
        if not tenant_config:
            return False

        disabled_jobs_config = tenant_config.get(TenantSettingName.DISABLED_JOBS.value)
        if not disabled_jobs_config:
            return False
        disabled_jobs = disabled_jobs_config.get_config_value()
        return disabled_jobs

    def get_refund_rule(self, hotel_id):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False
        refund_rule = tenant_config.get(TenantSettingName.REFUND_RULE.value)
        if not refund_rule:
            return None
        return RefundRuleDto(**refund_rule.get_config_value())

    def get_dummy_hotels(self):
        tenant_config = self._get_tenant_config()
        if not tenant_config:
            return []

        dummy_hotels = tenant_config.get(TenantSettingName.DUMMY_HOTELS.value)
        if not dummy_hotels:
            return []
        return dummy_hotels.get_config_value()

    def get_eregcard_config(self, hotel_id):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return None
        e_reg_card_config = tenant_configs.get(TenantSettingName.E_REG_CARD.value)
        if not e_reg_card_config:
            return None
        e_reg_card_config_value = e_reg_card_config.get_config_value()
        if not e_reg_card_config_value:
            return None
        return e_reg_card_config_value

    def hotel_uses_posttax_price(self, hotel_id):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return False

        hotel_uses_posttax_price = tenant_configs.get(
            "rate_config.hotel_uses_posttax_price"
        )
        if not hotel_uses_posttax_price:
            return False

        return hotel_uses_posttax_price.get_config_value()

    def club_inclusion_with_room_rate_for_taxation(self, hotel_id):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return False

        tax_inclusion_at_room_tax_rate = tenant_configs.get(
            "inclusion_config.club_with_room_rate_for_taxation"
        )
        if tax_inclusion_at_room_tax_rate:
            return tax_inclusion_at_room_tax_rate.get_config_value()

        return False

    def is_auto_night_audit_disabled(self, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return False

        auto_night_audit_disabled = tenant_configs.get(
            "night_audit.auto_night_audit_disabled"
        )
        if not auto_night_audit_disabled:
            return False

        return auto_night_audit_disabled.get_config_value()

    def disable_auto_cancellation_noshow_charges(self, hotel_id):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False

        disable_auto_cancellation_noshow_charges = tenant_config.get(
            TenantSettingName.DISABLE_AUTO_CANCELLATION_NOSHOW_CHARGES.value
        )
        if not disable_auto_cancellation_noshow_charges:
            return False
        return disable_auto_cancellation_noshow_charges.get_config_value()

    def show_allowance_as_separate_line_item(self, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return True

        show_allowance_as_separate_line_item = tenant_configs.get(
            TenantSettingName.SHOW_ALLOWANCE_AS_SEPARATE_LINE_ITEM.value
        )

        if not show_allowance_as_separate_line_item:
            return True

        return show_allowance_as_separate_line_item.get_config_value()

    def is_report_enabled(self, report_name, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not (tenant_configs and report_name):
            return False

        is_report_enabled_config = tenant_configs.get(
            TenantSettingName.IS_REPORT_ENABLED.value
        )
        if not is_report_enabled_config:
            return False

        is_report_enabled = is_report_enabled_config.get_config_value().get(report_name)
        return is_report_enabled if is_report_enabled else False

    def should_email_invoice_on_checkout(self, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return False

        send_invoice_to_booker_email_on_checkout_config = tenant_configs.get(
            TenantSettingName.SEND_INVOICE_TO_BOOKER_EMAIL_ON_CHECKOUT.value
        )
        if not send_invoice_to_booker_email_on_checkout_config:
            return False

        if not send_invoice_to_booker_email_on_checkout_config.get_config_value().get(
            'is_active'
        ):
            return False

        return send_invoice_to_booker_email_on_checkout_config.get_config_value().get(
            'is_active'
        )

    def allowed_channels_to_email_invoice_on_checkout(self, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return []

        send_invoice_to_booker_email_on_checkout_config = tenant_configs.get(
            TenantSettingName.SEND_INVOICE_TO_BOOKER_EMAIL_ON_CHECKOUT.value
        )
        if not send_invoice_to_booker_email_on_checkout_config:
            return []

        allowed_channels = (
            send_invoice_to_booker_email_on_checkout_config.get_config_value().get(
                'allowed_channels'
            )
        )
        if not allowed_channels:
            return []

        return allowed_channels

    def get_payment_configs_for_all_allowed_payment_methods(self, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return None
        allowed_paid_by_and_to = tenant_configs.get(
            TenantSettingName.PAYMENT_CONFIG.value
        )
        if not allowed_paid_by_and_to:
            return None
        allowed_paid_by_and_to_value = allowed_paid_by_and_to.get_config_value()
        if not allowed_paid_by_and_to_value:
            return None
        return {
            "payment": {
                rule['payment_method']: PaymentConfigDto(**rule)
                for rule in allowed_paid_by_and_to_value["payment"]
            },
            "refund": {
                rule['payment_method']: PaymentConfigDto(**rule)
                for rule in allowed_paid_by_and_to_value["refund"]
            },
        }

    def get_record_payment_max_value(self, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return False

        record_payment_max_value = tenant_configs.get(
            TenantSettingName.RECORD_PAYMENT_MAX_VALUE.value
        )

        if not record_payment_max_value:
            return False

        return record_payment_max_value.get_config_value()

    def get_issue_refund_max_condition(self, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return False
        issue_refund_max_condition = tenant_configs.get(
            TenantSettingName.ISSUE_REFUND_MAX_CONDITION.value
        )
        if not issue_refund_max_condition:
            return False
        issue_refund_max_value = issue_refund_max_condition.get_config_value()
        if not issue_refund_max_value:
            return None

        return {
            'hotel': issue_refund_max_value.get('hotel'),
            'treebo': issue_refund_max_value.get('treebo'),
        }

    def get_payment_modes_for_credit_shell(self, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if (
            tenant_configs is not None
            and TenantSettingName.CREDIT_SHELL_REFUND_PAYMENT_MODES.value
            in tenant_configs
        ):
            return tenant_configs[
                TenantSettingName.CREDIT_SHELL_REFUND_PAYMENT_MODES.value
            ]
        return None

    def get_use_cancellation_policy(self, hotel_id):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False

        use_cancellation_policy = tenant_config.get(
            TenantSettingName.USE_CANCELLATION_POLICY.value
        )
        if not use_cancellation_policy:
            return False
        return use_cancellation_policy

    def get_auto_approved_payout_link_amount(self, hotel_id):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False

        auto_approved_payout_link_amount = tenant_config.get(
            TenantSettingName.AUTO_APPROVED_PAYOUT_LINK_AMOUNT.value
        ).get_config_value()
        if not auto_approved_payout_link_amount:
            return 1000
        return auto_approved_payout_link_amount

    def get_bo_payment_mode_config_by_invoice_date(self, hotel_id):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False

        bo_payment_mode_config_by_invoice_date = tenant_config.get(
            TenantSettingName.BO_PAYMENT_MODE_CONFIG_BY_INVOICE_DATE.value
        )

        return (
            bo_payment_mode_config_by_invoice_date.get_config_value()
            if bo_payment_mode_config_by_invoice_date
            else None
        )

    def get_chain_manager_company_code(self, hotel_id):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False

        bo_treebo_company_code = tenant_config.get(
            TenantSettingName.BO_CHAIN_MANAGER_COMPANY_CODE.value
        )

        return (
            bo_treebo_company_code.get_config_value()
            if bo_treebo_company_code
            else None
        )

    def get_acq_disabled_channels(self):
        tenant_config = self._get_tenant_config()
        if not tenant_config:
            return False

        disabled_channels = tenant_config.get(
            TenantSettingName.ACQ_DISABLED_CHANNELS.value
        )

        return disabled_channels.get_config_value() if disabled_channels else None

    def get_acq_disabled_application_sources(self):
        tenant_config = self._get_tenant_config()
        if not tenant_config:
            return False

        disabled_application_sources = tenant_config.get(
            TenantSettingName.ACQ_DISABLED_APPLICATION_SOURCES.value
        )

        return (
            disabled_application_sources.get_config_value()
            if disabled_application_sources
            else None
        )

    def get_valid_guarantees_for_acq_exclusion(self):
        tenant_config = self._get_tenant_config()
        if not tenant_config:
            return False

        guarantees = tenant_config.get(
            TenantSettingName.ACQ_VALID_BOOKING_GUARANTEE.value
        )

        return guarantees.get_config_value() if guarantees else None

    def get_revman_enabled_hotels(self):
        tenant_config = self._get_tenant_config()
        if not tenant_config:
            return False

        revman_enabled_hotels = tenant_config.get(
            TenantSettingName.REVMAN_ENABLED_HOTELS.value
        )

        return (
            revman_enabled_hotels.get_config_value() if revman_enabled_hotels else None
        )

    def get_acq_cancellation_slots(self):
        tenant_config = self._get_tenant_config()
        if not tenant_config:
            return False

        cancellation_slots = tenant_config.get(
            TenantSettingName.ACQ_CANCELLATION_SLOTS.value
        )

        return cancellation_slots.get_config_value() if cancellation_slots else None

    def get_acq_payment_reminder_ivr_settings(self, hotel_id):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False

        payment_reminder_ivr_config = tenant_config.get(
            TenantSettingName.PAYMENT_REMINDER_IVR_SETTINGS.value
        )

        return (
            payment_reminder_ivr_config.get_config_value()
            if payment_reminder_ivr_config
            else None
        )

    def get_knowlarity_base_settings(self, hotel_id):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False

        ivr_config = tenant_config.get(
            TenantSettingName.KNOWLARITY_IVR_BASE_SETTINGS.value
        )

        return ivr_config.get_config_value() if ivr_config else None

    def get_is_financial_data_sync_enabled(self, hotel_id):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False

        is_back_office_enabled = tenant_config.get(
            TenantSettingName.IS_BO_ENABLED.value
        )

        return (
            is_back_office_enabled.get_config_value()
            if is_back_office_enabled
            else False
        )

    def is_guarantee_enabled(self, hotel_id=None):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False
        is_guarantee_enabled_config = tenant_config.get(
            TenantSettingName.IS_GUARANTEE_ENABLED.value
        )
        return (
            is_guarantee_enabled_config.get_config_value()
            if is_guarantee_enabled_config
            else None
        )

    def is_booking_funding_enabled(self, hotel_id=None):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return False
        is_booking_funding_enabled = tenant_config.get(
            TenantSettingName.BOOKING_FUNDING_ENABLED.value
        )
        return (
            is_booking_funding_enabled.get_config_value()
            if is_booking_funding_enabled
            else False
        )

    def get_acq_config_for_hotel(self, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return None
        config = tenant_configs.get(TenantSettingName.ACQ_CONFIGS.value)
        if not config:
            return None
        return config.get_config_value()

    def get_chain_manager_parent_company_code(self):
        tenant_config = self._get_tenant_config()
        if not tenant_config:
            return None

        chain_manager_parent_company_code = tenant_config.get(
            TenantSettingName.CHAIN_MANAGER_PARENT_COMPANY_CODE.value
        )

        return (
            chain_manager_parent_company_code.get_config_value()
            if chain_manager_parent_company_code
            else None
        )

    def get_maximum_amount_allowed_for_manual_funding(self, hotel_id=None):
        tenant_configs = self._get_tenant_config(hotel_id)
        if not tenant_configs:
            return False
        config = tenant_configs.get(
            TenantSettingName.MAXIMUM_AMOUNT_ALLOWED_FOR_MANUAL_FUNDING.value
        )
        if not config:
            return 5000
        return config.get_config_value()
