from object_registry import register_instance
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)


@register_instance(dependencies=[ExpenseItemRepository])
class ExpenseItemApplicationService(object):
    def __init__(self, expense_item_repository):
        self.expense_item_repository = expense_item_repository

    def get_expense_items(self, include_linked=None):
        return self.expense_item_repository.load_all(include_linked=include_linked)
