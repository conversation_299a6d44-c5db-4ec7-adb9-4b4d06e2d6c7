import logging

from sqlalchemy.orm.exc import NoResultFound
from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import session_manager
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.factories.cash_register_factory import (
    CashRegisterFactory,
)
from prometheus.domain.billing.factories.cashier_session_factory import (
    CashierSessionFactory,
)
from prometheus.domain.billing.repositories import CashierSessionRepository
from prometheus.domain.billing.repositories.cash_register_repository import (
    CashRegisterRepository,
)
from prometheus.domain.booking.exceptions import ExpenseItemNotFound
from prometheus.domain.booking.repositories import ExpenseItemRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.room_stay_overflow_repository import (
    RoomStayOverflowRepository,
)
from prometheus.domain.catalog.aggregates.hotel_aggregate import HotelAggregate
from prometheus.domain.catalog.dtos.hotel_dto import HotelDto
from prometheus.domain.catalog.dtos.reseller_gst_dto import ResellerGstDto
from prometheus.domain.catalog.dtos.room_dto import RoomDto
from prometheus.domain.catalog.dtos.room_type_config_dto import HotelRoomTypeConfigDto
from prometheus.domain.catalog.dtos.room_type_dto import RoomTypeDto
from prometheus.domain.catalog.dtos.seller_dto import SellerDto
from prometheus.domain.catalog.dtos.seller_table_dto import SellerTableDto
from prometheus.domain.catalog.dtos.sku_category_dto import SkuCategoryDto
from prometheus.domain.catalog.exceptions import CatalogException
from prometheus.domain.catalog.factories.hotel_factory import HotelFactory
from prometheus.domain.catalog.factories.reseller_gst_factory import ResellerGSTFactory
from prometheus.domain.catalog.factories.room_factory import RoomFactory
from prometheus.domain.catalog.factories.room_type_factory import RoomTypeFactory
from prometheus.domain.catalog.factories.seller_factory import SellerFactory
from prometheus.domain.catalog.factories.seller_table_factory import SellerTableFactory
from prometheus.domain.catalog.factories.sku_category_factory import SkuCategoryFactory
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.reseller_gst_repository import (
    ResellerGstRepository,
)
from prometheus.domain.catalog.repositories.room_repository import RoomRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.catalog.repositories.seller_repository import SellerRepository
from prometheus.domain.catalog.repositories.seller_table_repository import (
    SellerTableRepository,
)
from prometheus.domain.catalog.repositories.sku_category_repository import (
    SkuCategoryRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.factories.room_allotment_factory import (
    RoomAllotmentFactory,
)
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from prometheus.domain.inventory.repositories.room_type_inventory_repository import (
    RoomTypeInventoryRepository,
)
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from prometheus.middlewares.common_middlewares import exception_handler
from ths_common.constants.billing_constants import (
    CashierSessionStatus,
    CashRegisterNames,
)
from ths_common.constants.catalog_constants import RoomStatus
from ths_common.constants.inventory_constants import DNREffectiveStatus, DNRStatus
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import AggregateNotFound, ValidationException
from ths_common.value_objects import CashCounterAmount, NotAssigned, UserData
from thsc.crs.entities.inventory.dnr import DNR as ThscDNR

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        HotelRepository,
        RoomRepository,
        RoomTypeRepository,
        SkuCategoryRepository,
        CatalogServiceClient,
        RoomTypeInventoryRepository,
        HotelConfigRepository,
        InventoryApplicationService,
        RoomStayOverflowRepository,
        BookingRepository,
        RoomAllotmentRepository,
        SellerRepository,
        ResellerGstRepository,
        CashRegisterRepository,
        SellerTableRepository,
        ExpenseItemRepository,
        CashierSessionRepository,
        RoomStayOverflowService,
    ]
)
class CatalogApplicationService(object):
    def __init__(
        self,
        hotel_repository,
        room_repository,
        room_type_repository,
        sku_category_repository: SkuCategoryRepository,
        catalog_service_client,
        room_type_inventory_repository,
        hotel_config_repository,
        inventory_application_service,
        room_stay_overflow_repository,
        booking_repository,
        room_allotment_repository,
        seller_repository,
        reseller_gst_repository,
        cash_register_repository,
        seller_table_repository,
        expense_item_repository,
        cashier_session_repository,
        room_stay_overflow_service,
    ):
        self.hotel_repository = hotel_repository
        self.booking_repository = booking_repository
        self.room_repository = room_repository
        self.room_stay_overflow_repository = room_stay_overflow_repository
        self.room_type_repository = room_type_repository
        self.room_allotment_repository = room_allotment_repository
        self.sku_category_repository = sku_category_repository
        self.catalog_service_client = catalog_service_client
        self.room_type_inventory_repository = room_type_inventory_repository
        self.hotel_config_repository = hotel_config_repository
        self.inventory_application_service = inventory_application_service
        self.seller_repository = seller_repository
        self.reseller_gst_repository = reseller_gst_repository
        self.cash_register_repository = cash_register_repository
        self.seller_table_repository = seller_table_repository
        self.expense_item_repository = expense_item_repository
        self.cashier_session_repository = cashier_session_repository
        self.room_stay_overflow_service = room_stay_overflow_service

    def is_hotel_active(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        return hotel_aggregate.is_active()

    @session_manager(commit=True)
    def sync_hotel(self, hotel_id):
        (
            hotel_dto,
            room_type_config_dtos,
            room_dtos,
        ) = self._load_hotel_and_rooms_from_catalog(hotel_id)
        try:
            hotel_aggregate = self.hotel_repository.load_for_update(
                hotel_id=hotel_dto.hotel_id
            )
        except AggregateNotFound:
            hotel_aggregate = None

        if not hotel_aggregate:
            hotel_aggregate = HotelFactory.create_hotel(hotel_dto)
            for room_type_config_dto in room_type_config_dtos:
                try:
                    self.room_type_repository.load(room_type_config_dto.room_type_id)
                except AggregateNotFound:
                    # create room type config
                    room_type_dto = RoomTypeDto.create_from_catalog_data(
                        room_type_config_dto.room_type_data
                    )
                    room_type_aggregate = RoomTypeFactory.create_room_type(
                        room_type_dto
                    )
                    self.room_type_repository.save(room_type_aggregate)

                hotel_aggregate.add_room_type_config(room_type_config_dto)

            self.hotel_repository.save(hotel_aggregate)
            crs_context.set_hotel_context(hotel_aggregate)
            self._create_cash_register(
                hotel_aggregate.hotel_id,
                hotel_aggregate.hotel.base_currency,
                CashRegisterNames.HOTEL_CASH_REGISTER,
            )

            room_allotment_aggregates = []
            room_aggregates = []
            for room_dto in room_dtos:
                room_aggregate = RoomFactory.create_room(room_dto)
                room_aggregates.append(room_aggregate)
                room_allotment_aggregates.append(
                    RoomAllotmentFactory.create_empty_allotment(
                        hotel_id, room_aggregate.room.room_id
                    )
                )

            self.room_repository.save_all(room_aggregates)

            if room_allotment_aggregates:
                self.room_allotment_repository.save_all(room_allotment_aggregates)

        else:
            hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
            crs_context.set_hotel_context(
                hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
            )
            try:
                hotel_aggregate.update_hotel(hotel_dto)
            except CatalogException as exc:
                exception_handler(exc)

            for room_type_config_dto in room_type_config_dtos:
                room_type_config = hotel_aggregate.get_room_type_config(
                    room_type_config_dto.room_type_id
                )
                if not room_type_config:
                    hotel_aggregate.add_room_type_config(room_type_config_dto)

            room_aggregates = self.room_repository.load_multiple(hotel_dto.hotel_id)
            room_aggregate_map = {
                room_aggregate.room.room_id: room_aggregate
                for room_aggregate in room_aggregates
            }
            room_dto_map = {room_dto.room_id: room_dto for room_dto in room_dtos}

            created_room_aggregates = []
            room_allotment_aggregates = []
            # Add new room if received
            for room_id, room_dto in room_dto_map.items():
                if room_id not in room_aggregate_map:
                    room_aggregate = RoomFactory.create_room(room_dto)
                    created_room_aggregates.append(room_aggregate)
                    room_allotment_aggregates.append(
                        RoomAllotmentFactory.create_empty_allotment(hotel_id, room_id)
                    )

            if created_room_aggregates:
                self.room_repository.save_all(created_room_aggregates)
                self.inventory_application_service.update_room_type_inventory_availabilities(
                    hotel_aggregate,
                    created_room_aggregates,
                )

            if room_allotment_aggregates:
                self.room_allotment_repository.save_all(room_allotment_aggregates)

            self.hotel_repository.update(hotel_aggregate)

        return hotel_aggregate

    @staticmethod
    def load_hotel_from_catalog(hotel_id, catalog_service_client):
        catalog_hotel_response = catalog_service_client.fetch_hotel(hotel_id)
        hotel_dto = HotelDto.create_from_catalog_data(catalog_hotel_response)

        hotel_room_type_config_dtos = []
        for room_type_config in catalog_hotel_response.get("room_type_configs"):
            hotel_room_type_config_dto = (
                HotelRoomTypeConfigDto.create_from_catalog_data(
                    hotel_id, room_type_config
                )
            )
            hotel_room_type_config_dtos.append(hotel_room_type_config_dto)
        return hotel_dto, hotel_room_type_config_dtos

    def _load_hotel_and_rooms_from_catalog(self, hotel_id):
        hotel_dto, hotel_room_type_config_dtos = self.load_hotel_from_catalog(
            hotel_id, self.catalog_service_client
        )

        catalog_room_response = self.catalog_service_client.fetch_rooms(hotel_id)
        room_dtos = []
        for room_data in catalog_room_response:
            room_dto = RoomDto.create_from_catalog_data(hotel_id, room_data)
            room_dtos.append(room_dto)

        return hotel_dto, hotel_room_type_config_dtos, room_dtos

    @session_manager(commit=True)
    def add_property(self, hotel_dto: HotelDto):
        logger.info("Adding hotel with hotel id: %s", hotel_dto.hotel_id)
        hotel_aggregate = HotelFactory.create_hotel(hotel_dto)
        self.hotel_repository.save(hotel_aggregate)
        self._create_cash_register(
            hotel_aggregate.hotel_id,
            hotel_aggregate.hotel.base_currency,
            CashRegisterNames.HOTEL_CASH_REGISTER,
        )
        return hotel_aggregate

    def _create_cash_register(self, vendor_id, base_currency, cash_register_name):
        cash_register_aggregate = CashRegisterFactory.create_new_cash_register(
            vendor_id=vendor_id,
            default_opening_balance=CashCounterAmount([Money(0, base_currency)]),
            opened_by=UserType.BACKEND_SYSTEM.value,
            cash_register_name=cash_register_name,
            start_datetime=dateutils.current_datetime(),
            carry_balance_to_next_session=False,
            base_currency=base_currency,
        )
        self.cash_register_repository.save(cash_register_aggregate)
        return cash_register_aggregate

    def _create_cashier_session(
        self, vendor_id, base_currency, cash_register_aggregate
    ):
        opening_balance_in_base_currency = Money(0, base_currency)
        opening_balance = [Money(0, base_currency)]
        new_cashier_session_dto = dict(
            opening_balance=opening_balance,
            opening_balance_in_base_currency=opening_balance_in_base_currency,
            vendor_id=vendor_id,
        )
        cashier_session_aggregate = CashierSessionFactory.create_new_cashier_session(
            vendor_id=vendor_id,
            session_start_datetime=dateutils.current_datetime(),
            opening_balance=CashCounterAmount(
                new_cashier_session_dto['opening_balance']
            ),
            opening_balance_in_base_currency=new_cashier_session_dto.get(
                'opening_balance_in_base_currency'
            ),
            status=CashierSessionStatus.OPEN,
            opened_by=UserType.BACKEND_SYSTEM.value,
            cash_register_id=cash_register_aggregate.cash_register.cash_register_id,
            session_number=1,
            closed_by=None,
        )
        self.cashier_session_repository.save(cashier_session_aggregate)

    @session_manager(commit=True)
    def update_property(self, hotel_dto: HotelDto):
        logger.debug("Updating hotel with id: %s", hotel_dto.hotel_id)
        hotel_aggregate = self.hotel_repository.load_for_update(
            hotel_id=hotel_dto.hotel_id, wait_for_lock=True
        )
        try:
            hotel_aggregate.update_hotel(hotel_dto)
        except CatalogException as exc:
            exception_handler(exc, from_consumer=True)

        self.hotel_repository.update(hotel_aggregate)
        return hotel_aggregate

    @session_manager(commit=True)
    def update_room(self, room_dto: RoomDto):
        logger.info(
            "Updating room number: %s of type: %s in hotel_id: %s",
            room_dto.room_number,
            room_dto.room_type_id,
            room_dto.hotel_id,
        )
        if not self.room_repository.exists(room_dto.room_id):
            return self.add_room(room_dto)

        hotel_aggregate = self.hotel_repository.load_for_update(
            room_dto.hotel_id, wait_for_lock=True
        )
        user_data = UserData(UserType.BACKEND_SYSTEM.value)

        if not hotel_aggregate.has_room_type_config_for_room_type(
            room_dto.room_type_id
        ):
            raise ValidationException(
                error=ApplicationErrors.ROOM_TYPE_CONFIG_NOT_FOUND
            )

        hotel_config_aggregate = self.hotel_config_repository.load(room_dto.hotel_id)
        crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
        )
        room_aggregate = self.room_repository.load_for_update(
            room_dto.room_id, room_dto.hotel_id
        )
        room_aggregate.update_room_details(room_dto)

        if room_aggregate.room.status != room_dto.status:
            if room_dto.status == RoomStatus.INACTIVE:
                room_aggregate.room.mark_status_inactive()
                applicable_dnrs = self.get_current_applicable_dnrs(room_dto)
                self._remove_dnrs(applicable_dnrs)
                self._maybe_port_dnrs_to_linked_room(room_dto, applicable_dnrs)
                self.inventory_application_service.mark_dnr_for_inactive_room(
                    hotel_aggregate=hotel_aggregate,
                    room_id=room_dto.room_id,
                    room_type_id=room_dto.room_type_id,
                    user_data=user_data,
                    business_date=crs_context.get_hotel_context().current_date(),
                )
                self.room_stay_overflow_service.compute_and_mark_overflows_in_date_range(
                    hotel_aggregate.hotel.hotel_id,
                    room_aggregate.room.room_type_id,
                    dateutils.current_date(),
                    dateutils.add(dateutils.current_date(), months=11),
                )
            elif room_dto.status == RoomStatus.ACTIVE:
                room_aggregate.room.mark_status_active()
                self.inventory_application_service.remove_dnr_for_inactive_room(
                    hotel_aggregate, room_aggregate, user_data
                )
                self.room_stay_overflow_service.recompute_and_unmark_overflows(
                    hotel_aggregate.hotel.hotel_id,
                    dateutils.current_date(),
                    dateutils.add(dateutils.current_date(), months=11),
                    [room_aggregate.room.room_type_id],
                    user_action="update_room_from_catalog",
                )

        self.room_repository.update(room_aggregate)
        self.hotel_repository.update(hotel_aggregate)
        return hotel_aggregate

    @session_manager(commit=True)
    def add_room(self, room_dto: RoomDto):
        logger.info(
            "Adding room number: %s of type: %s in hotel_id: %s",
            room_dto.room_number,
            room_dto.room_type_id,
            room_dto.hotel_id,
        )
        hotel_aggregate = self.hotel_repository.load_for_update(
            room_dto.hotel_id, wait_for_lock=True
        )
        if not hotel_aggregate.has_room_type_config_for_room_type(
            room_dto.room_type_id
        ):
            raise ValidationException(
                error=ApplicationErrors.ROOM_TYPE_CONFIG_NOT_FOUND
            )

        if self.room_repository.exists(room_dto.room_id):
            raise ValidationException(error=ApplicationErrors.ROOM_ALREADY_EXISTS)

        hotel_config_aggregate = self.hotel_config_repository.load(room_dto.hotel_id)
        crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
        )

        room_aggregate = RoomFactory.create_room(room_dto)
        room_allotment_aggregate = RoomAllotmentFactory.create_empty_allotment(
            room_dto.hotel_id, room_aggregate.room.room_id
        )
        self.room_repository.save(room_aggregate)
        self.room_allotment_repository.save(room_allotment_aggregate)

        self.inventory_application_service.update_room_type_inventory_availabilities(
            hotel_aggregate, [room_aggregate]
        )

        self.room_stay_overflow_service.recompute_and_unmark_overflows(
            hotel_aggregate.hotel.hotel_id,
            dateutils.current_date(),
            dateutils.add(dateutils.current_date(), months=12),
            [room_aggregate.room.room_type_id],
            user_action="add_room_from_catalog",
        )
        return room_aggregate

    @session_manager(commit=True)
    def add_room_type_config(self, room_type_config_dto: HotelRoomTypeConfigDto):
        logger.info(
            "Adding RoomTypeConfig for hotel_id: %s, and room_type: %s",
            room_type_config_dto.hotel_id,
            room_type_config_dto.room_type_id,
        )
        hotel_aggregate = self.hotel_repository.load_for_update(
            room_type_config_dto.hotel_id, wait_for_lock=True
        )
        self.room_type_repository.load(room_type_config_dto.room_type_id)
        if hotel_aggregate.has_room_type_config_for_room_type(
            room_type_config_dto.room_type_id
        ):
            raise ValidationException(
                error=ApplicationErrors.ROOM_TYPE_CONFIG_ALREADY_EXISTS
            )

        hotel_aggregate.add_room_type_config(room_type_config_dto)
        self.hotel_repository.update(hotel_aggregate)
        return hotel_aggregate

    @session_manager(commit=True)
    def update_room_type_config(self, room_type_config_dto: HotelRoomTypeConfigDto):
        logger.debug(
            "Updating RoomTypeConfig for hotel_id: %s, and room_type: %s",
            room_type_config_dto.hotel_id,
            room_type_config_dto.room_type_id,
        )
        hotel_aggregate = self.hotel_repository.load_for_update(
            room_type_config_dto.hotel_id, wait_for_lock=True
        )
        self.room_type_repository.load(room_type_config_dto.room_type_id)
        if not hotel_aggregate.has_room_type_config_for_room_type(
            room_type_config_dto.room_type_id
        ):
            hotel_aggregate.add_room_type_config(room_type_config_dto)
        else:
            hotel_aggregate.update_room_type_config(room_type_config_dto)
        self.hotel_repository.update(hotel_aggregate)
        return hotel_aggregate

    @session_manager(commit=True)
    def add_sku_category(self, sku_category_dto):
        sku_category_aggregate = SkuCategoryFactory.create_sku_category(
            sku_category_dto
        )
        self.sku_category_repository.save(sku_category_aggregate)
        return sku_category_aggregate

    def get_sku_categories(self):
        return self.sku_category_repository.load_all()

    @session_manager(commit=True)
    def add_expense_item(self, expense_item_dto):
        return self.expense_item_repository.save(expense_item_dto)

    @session_manager(commit=True)
    def update_expense_item(self, expense_item_dto):
        try:
            expense_item_aggregate = self.expense_item_repository.load(
                sku_id=expense_item_dto.sku_id
            )
        except ExpenseItemNotFound:
            return self.add_expense_item(expense_item_dto)
        expense_item_aggregate.update(expense_item_dto)
        return self.expense_item_repository.update(expense_item_aggregate)

    @session_manager(commit=True)
    def delete_expense_item(self, expense_item_dto):
        try:
            expense_item_aggregate = self.expense_item_repository.load(
                sku_id=expense_item_dto.sku_id
            )
        except ExpenseItemNotFound:
            expense_item_dto.delete()
            return self.add_expense_item(expense_item_dto)
        expense_item_aggregate.delete()
        return self.expense_item_repository.update(expense_item_aggregate)

    @session_manager(commit=True)
    def create_room_type(self, room_type_dto: RoomTypeDto):
        room_type_aggregate = RoomTypeFactory.create_room_type(room_type_dto)
        self.room_type_repository.save(room_type_aggregate)

    def get_current_applicable_dnrs(self, room_dto):
        today = dateutils.current_date()
        tomorrow = dateutils.add(today, days=1)

        # Filter dnrs from tomorrow instead of today. if we use today, from_date and to_date
        # for dnrs will be same for dnrs whose ending is today which is a rule violation.
        dnr_filter = {
            'room_ids': [room_dto.room_id],
            'effective_status': DNREffectiveStatus.ACTIVE,
            'from_date': tomorrow,
        }

        existing_active_dnrs_for_room, _ = self.inventory_application_service.get_dnrs(
            hotel_id=room_dto.hotel_id,
            get_dnr_data=dnr_filter,
        )
        return existing_active_dnrs_for_room

    def _remove_dnrs(self, dnrs):
        """
        Differs from remove_dnr service call since we are calling from thsc.
        We are using thsc to preserve event context from having only events related to dnr removal.
        If we call via service it preserves all the event context from the start of this flow along with
        the dnr removal events.

        Also we are using thsc since updating inventory takes a lock and releases it in the end of a transaction.
        Executing it via service means the next lock cannot happen unless the session is committed which only happens
        at the end of the request. Hence we resorted to calling via thsc
        """
        for dnr in dnrs:
            ThscDNR(
                hotel_id=dnr.hotel_id,
                dnr_id=dnr.dnr_id,
                version=dnr.version,
            ).remove()

    def _maybe_port_dnrs_to_linked_room(self, room_dto, dnrs):
        """
        Differs from create_dnr service call since we are calling from thsc.
        We are using thsc to preserve event context from having only events related to dnr creation.
        If we call via service it preserves all the event context from the start of this flow along with
        the dnr creation events.

        Also we are using thsc since updating inventory takes a lock and releases it in the end of a transaction.
        Executing it via service means the next lock cannot happen unless the session is committed which only happens
        at the end of the request. Hence we resorted to calling via thsc
        """

        if not room_dto.linked_room_id:
            return

        logger.info("Porting dnrs for room <{r}: {dnrs}>".format(r=room_dto, dnrs=dnrs))
        today = dateutils.current_date()
        for dnr in dnrs:
            from_date = max(today, dnr.start_date)
            to_date = max(from_date, dnr.end_date)
            ThscDNR(
                hotel_id=dnr.hotel_id,
                room_id=room_dto.linked_room_id,
                from_date=from_date,
                to_date=to_date,
                type=dnr.dnr_type.type,
                subtype=dnr.dnr_type.subtype,
                comments=f"{dnr.dnr_type.comments}. Ported from {dnr.dnr_id}",
                source=dnr.source,
                assigned_by=dnr.assigned_by,
                status=DNRStatus.ACTIVE,
            ).create()

    def _load_seller_from_catalog(self, hotel_id):
        catalog_seller_response = self.catalog_service_client.fetch_seller(hotel_id)
        return SellerDto.create_sellers_from_catalog_data(catalog_seller_response)

    @session_manager(commit=True)
    def add_seller_details(self, seller_detail_dto: SellerDto):
        logger.info(
            "Adding seller details with seller_id: {seller_id}".format(
                seller_id=seller_detail_dto.seller_id
            )
        )
        seller_aggregate = SellerFactory.create_seller(seller_detail_dto)
        self.seller_repository.save(seller_aggregate)
        self._create_cash_register(
            vendor_id=seller_aggregate.seller.seller_id,
            base_currency=seller_aggregate.seller.base_currency,
            cash_register_name=" ".join([seller_aggregate.seller.name, "register"]),
        )
        return seller_aggregate

    @session_manager(commit=True)
    def update_seller_details(self, seller_detail_dto: SellerDto):
        logger.debug(
            "Updating seller details with seller_id: {seller_id}".format(
                seller_id=seller_detail_dto.seller_id
            )
        )
        seller_aggregate = self.seller_repository.load(
            seller_id=seller_detail_dto.seller_id
        )
        try:
            seller_aggregate.update_seller(seller_detail_dto)
            self.seller_repository.update(seller_aggregate)
        except Exception as e:
            raise

    @session_manager(commit=True)
    def sync_seller(self, hotel_id):
        seller_dtos = self._load_seller_from_catalog(hotel_id)

        for seller_dto in seller_dtos:
            try:
                seller_aggregate = self.seller_repository.load(
                    seller_id=seller_dto.seller_id
                )
            except NoResultFound:
                seller_aggregate = None

            if not seller_aggregate:
                seller_aggregate = SellerFactory.create_seller(seller_dto)
                self.seller_repository.save(seller_aggregate)
            else:
                seller_aggregate = self.seller_repository.load_for_update(
                    seller_id=seller_dto.seller_id
                )
                seller_aggregate.update_seller(seller_dto)
                self.seller_repository.update(seller_aggregate)

    @session_manager(commit=True)
    def add_reseller_gst_details(self, reseller_gst_detail_dto: ResellerGstDto):
        logger.info(
            "Adding reseller gst details with state_id: {state_id}".format(
                state_id=reseller_gst_detail_dto.state_id
            )
        )
        reseller_gst_aggregate = ResellerGSTFactory.create_reseller_gst(
            reseller_gst_detail_dto
        )
        self.reseller_gst_repository.save(reseller_gst_aggregate)
        return reseller_gst_aggregate

    @session_manager(commit=True)
    def update_reseller_gst_details(self, reseller_gst_detail_dto: ResellerGstDto):
        logger.debug(
            "Updating reseller gst details with state_id: {state_id}".format(
                state_id=reseller_gst_detail_dto.state_id
            )
        )
        reseller_gst_aggregate = self.reseller_gst_repository.load(
            state_id=reseller_gst_detail_dto.state_id
        )
        try:
            reseller_gst_aggregate.update_reseller_gst(
                reseller_gst_detail_dto, reseller_gst_aggregate
            )
            self.reseller_gst_repository.update(reseller_gst_aggregate)
        except Exception as e:
            raise

    def get_sellers(self, hotel_id):
        seller_dtos = self._load_seller_from_catalog(hotel_id)
        seller_ids = [seller_dto.seller_id for seller_dto in seller_dtos]
        return self.seller_repository.load_all(seller_ids=seller_ids)

    @session_manager(commit=True)
    def add_seller_table(self, seller_table_dto: SellerTableDto):
        logger.info(
            "Adding seller table with table_id: {table_id}".format(
                table_id=seller_table_dto.table_id
            )
        )
        seller_table = SellerTableFactory.create_seller_table(seller_table_dto)
        self.seller_table_repository.save(seller_table)

    @session_manager(commit=True)
    def update_seller_table(self, seller_table_dto: SellerTableDto):
        logger.info(
            "Updating seller table with table_id: {table_id}".format(
                table_id=seller_table_dto.table_id
            )
        )

        if not self.seller_table_repository.table_exists(
            table_id=seller_table_dto.table_id, seller_id=seller_table_dto.seller_id
        ):
            self.add_seller_table(seller_table_dto=seller_table_dto)
            return

        seller_table = self.seller_table_repository.load(
            seller_id=seller_table_dto.seller_id, table_id=seller_table_dto.table_id
        )

        if (
            seller_table_dto.table_id != NotAssigned
            and seller_table.table_id != seller_table_dto.table_id
        ):
            seller_table.table_id = seller_table_dto.table_id

        if (
            seller_table_dto.table_number != NotAssigned
            and seller_table.table_number != seller_table_dto.table_number
        ):
            seller_table.table_number = seller_table_dto.table_number

        if (
            seller_table_dto.name != NotAssigned
            and seller_table.name != seller_table_dto.name
        ):
            seller_table.name = seller_table_dto.name

        self.seller_table_repository.update(seller_table)

    def rollover_current_business_date(self, hotel_aggregate: HotelAggregate):
        date_from_catalog = self.catalog_service_client.rollover_current_business_date(
            hotel_aggregate.hotel_id,
            dateutils.date_to_ymd_str(hotel_aggregate.hotel.current_business_date),
        )
        logger.info(
            "Catalog property business date for hotel id: %s rolled over to: %s",
            hotel_aggregate.hotel_id,
            date_from_catalog,
        )

    @session_manager(commit=True)
    def create_of_update_sku_category(self, sku_category_dto: SkuCategoryDto):
        try:
            sku_category_aggregate = self.sku_category_repository.load(
                sku_category_dto.sku_category_id
            )
            sku_category_aggregate.update_from_catalog(sku_category_dto)
            self.sku_category_repository.update(sku_category_aggregate)
        except AggregateNotFound:
            sku_category_aggregate = SkuCategoryFactory.create_sku_category(
                sku_category_dto
            )
            self.sku_category_repository.save(sku_category_aggregate)
