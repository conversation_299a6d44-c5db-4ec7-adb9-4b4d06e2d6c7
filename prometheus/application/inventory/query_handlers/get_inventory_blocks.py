from object_registry import register_instance
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.inventory.dtos.inventory_block_dto import InventoryBlockFilterDTO
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)


@register_instance(dependencies=[HotelRepository, InventoryBlockRepository])
class SearchInventoryBlockQueryHandler:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        inventory_block_repository: InventoryBlockRepository,
    ):
        self.hotel_repository: HotelRepository = hotel_repository
        self.inventory_block_repository: InventoryBlockRepository = (
            inventory_block_repository
        )

    def handle(self, hotel_id, request_dto: InventoryBlockFilterDTO):
        self.hotel_repository.load(hotel_id)
        return self.inventory_block_repository.search(request_dto)
