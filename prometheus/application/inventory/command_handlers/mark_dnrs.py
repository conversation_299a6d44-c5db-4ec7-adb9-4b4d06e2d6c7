import logging
from typing import List

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit_dnr, audit_housekeeping
from prometheus.application.decorators import session_manager
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.catalog.repositories import HotelRepository, RoomRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.errors import InventoryError
from prometheus.domain.inventory.exceptions import InventoryException
from prometheus.domain.inventory.factories.room_allotment_factory import (
    RoomAllotmentFactory,
)
from prometheus.domain.inventory.repositories import (
    RoomAllotmentRepository,
    RoomTypeInventoryRepository,
)
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from prometheus.domain.inventory.services import DNRService
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from ths_common.constants.audit_trail_constants import DNRAuditType
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.exceptions import ValidationException

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        HotelRepository,
        HotelConfigRepository,
        RoomRepository,
        RoomAllotmentRepository,
        RoomTypeInventoryRepository,
        DNRRepository,
        DNRService,
        NewrelicServiceClient,
    ]
)
class MarkDNRsCommandHandler:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        room_repository: RoomRepository,
        room_allotment_repository: RoomAllotmentRepository,
        room_type_inventory_repository: RoomTypeInventoryRepository,
        dnr_repository: DNRRepository,
        dnr_service: DNRService,
        alerting_service: NewrelicServiceClient,
    ):
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.room_repository = room_repository
        self.room_allotment_repository = room_allotment_repository
        self.room_type_inventory_repository = room_type_inventory_repository
        self.dnr_repository = dnr_repository
        self.dnr_service = dnr_service
        self.alerting_service = alerting_service

    @session_manager(commit=True)
    @audit_housekeeping(action_performed="mark_dnrs")
    def handle(self, hotel_id, mark_dnr_data: List[dict], user_data):
        logger.info("Marking bulk dnrs for hotel_id: %s", hotel_id)
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(
            hotel_aggregate.hotel.hotel_id
        )
        crs_context.set_hotel_context(hotel_aggregate, hotel_config_aggregate)
        room_ids = list({dnr_data['room_id'] for dnr_data in mark_dnr_data})
        room_aggregates = self.room_repository.load_multiple(
            hotel_aggregate.hotel_id, room_ids=room_ids
        )
        grouped_room_aggregates = {
            room_aggregate.room.room_id: room_aggregate
            for room_aggregate in room_aggregates
        }
        room_type_ids = list(
            {room_aggregate.room.room_type_id for room_aggregate in room_aggregates}
        )

        grouped_room_allotment_aggregates = (
            self.room_allotment_repository.load_multiple(
                hotel_id=hotel_aggregate.hotel.hotel_id,
                room_ids=grouped_room_aggregates.keys(),
                for_update=True,
            )
        )

        min_date = min(dnr_data.get('from_date') for dnr_data in mark_dnr_data)
        max_date = max(dnr_data.get('to_date') for dnr_data in mark_dnr_data)

        room_type_inventory_aggregates = (
            self.room_type_inventory_repository.load_for_update(
                hotel_id=hotel_aggregate.hotel_id,
                room_type_ids=room_type_ids,
                from_date=min_date,
                to_date=max_date,
            )
        )
        grouped_room_type_inventory_aggregates = {
            aggregate.room_type_inventory.room_type_id: aggregate
            for aggregate in room_type_inventory_aggregates
        }

        created_dnr_aggregates = []
        for dnr_data in mark_dnr_data:
            room_id = dnr_data['room_id']
            room_aggregate = grouped_room_aggregates[room_id]
            room_allotment_aggregate = grouped_room_allotment_aggregates.get(room_id)
            if not room_allotment_aggregate:
                room_allotment_aggregate = RoomAllotmentFactory.create_empty_allotment(
                    hotel_id=hotel_aggregate.hotel.hotel_id, room_id=room_id
                )
                grouped_room_allotment_aggregates[room_id] = room_allotment_aggregate

            room_type_inventory_aggregate = grouped_room_type_inventory_aggregates.get(
                room_aggregate.room.room_type_id
            )

            dnr_aggregate, date_wise_room_type_availability_change = self.create_dnr(
                dnr_data,
                hotel_aggregate,
                room_aggregate,
                room_allotment_aggregate,
                room_type_inventory_aggregate,
                user_data,
            )

            created_dnr_aggregates.append(dnr_aggregate)

            IntegrationEventApplicationService.create_dnr_events(
                hotel_id,
                dnr_aggregate,
                {
                    room_aggregate.room.room_type_id: date_wise_room_type_availability_change
                },
                event_type=IntegrationEventType.DNR_SET,
                user_action="mark_dnr",
            )

        self.room_allotment_repository.update_all(
            grouped_room_allotment_aggregates.values()
        )
        self.room_type_inventory_repository.update_all(
            grouped_room_type_inventory_aggregates.values()
        )
        logger.info(
            "Marked bulk dnrs for hotel_id: %s. Total dnrs: %s",
            hotel_id,
            len(created_dnr_aggregates),
        )
        return [dnr_aggregate.dnr for dnr_aggregate in created_dnr_aggregates]

    @audit_dnr(audit_type=DNRAuditType.DNR_CREATED, clear_events=True)
    def create_dnr(
        self,
        mark_dnr_data: dict,
        hotel_aggregate,
        room_aggregate,
        room_allotment_aggregate,
        room_type_inventory_aggregate,
        user_data,
    ):
        hotel_context = crs_context.get_hotel_context()
        from_date = mark_dnr_data.get('from_date')
        to_date = mark_dnr_data.get('to_date')

        if from_date < hotel_context.current_date():
            raise ValidationException(ApplicationErrors.CANT_MARK_PAST_DATES_AS_DNR)

        if not crs_context.should_bypass_privilege_checks():
            RuleEngine.action_allowed(
                action='mark_dnr',
                facts=Facts(
                    user_type=user_data.user_type,
                    action_payload=mark_dnr_data,
                    hotel_context=hotel_context,
                ),
                fail_on_error=True,
            )

        try:
            (
                dnr_aggregate,
                date_wise_room_type_availability_change,
            ) = self.dnr_service.mark_dnr(
                hotel_aggregate.hotel.hotel_id,
                mark_dnr_data,
                room_type_inventory_aggregate,
                room_allotment_aggregate,
                hotel_context,
            )
        except InventoryException as ie:
            if ie.error == InventoryError.ROOM_INVENTORY_NOT_CREATED:
                self.alerting_service.record_event(
                    "inventory_not_created",
                    {
                        "hotel_id": hotel_aggregate.hotel_id,
                        "room_id": room_aggregate.room.room_id,
                        "from_date": str(from_date),
                        "to_date": str(to_date),
                    },
                )
            raise
        self.dnr_repository.save(dnr_aggregate)

        if dnr_aggregate.dnr.start_date == hotel_context.current_date():
            if not room_allotment_aggregate.is_occupied():
                room_allotment_aggregate.update_housekeeping_status_on_dnr()

        return dnr_aggregate, date_wise_room_type_availability_change
