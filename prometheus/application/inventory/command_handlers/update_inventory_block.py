from typing import Any, Callable, List, Optional

from object_registry import register_instance
from prometheus.application import crs_context_middleware
from prometheus.application.decorators import session_manager
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.async_job.job.repositories.job_repository import JobRepository
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.core.domainbus.bus import get_domain_bus
from prometheus.core.domainbus.events.events import InventoryBlockReleaseEvent
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.inventory.dtos.inventory_block_dto import InventoryBlockDTO
from prometheus.domain.inventory.entities.inventory_block import InventoryBlock
from prometheus.domain.inventory.inventory_requirement import InventoryRequirement
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.inventory_block_facts import InventoryBlockFacts
from ths_common.constants.inventory_constants import UpdateInventoryBlockStatus


@register_instance(
    dependencies=[
        InventoryApplicationService,
        JobSchedulerService,
        JobRepository,
        InventoryBlockRepository,
        BookingRepository,
    ]
)
class UpdateInventoryBlockCommandHandler:
    def __init__(
        self,
        inventory_service: InventoryApplicationService,
        job_scheduler_service: JobSchedulerService,
        job_repository: JobRepository,
        inventory_block_repository: InventoryBlockRepository,
        booking_repository: BookingRepository,
    ):
        self.inventory_service = inventory_service
        self.job_scheduler_service = job_scheduler_service
        self.job_repository = job_repository
        self.inventory_block_repository = inventory_block_repository
        self.booking_repository = booking_repository

    @session_manager(commit=True)
    def handle(
        self,
        action,
        hotel_id,
        booking_id,
        inventory_block_data: Optional[List[InventoryBlockDTO]] = None,
        block_ids: Optional[List[str]] = None,
        user_data=None,
    ):
        crs_context_middleware.set_hotel_context(hotel_id)
        if booking_id:
            self.booking_repository.load(booking_id)

        if action == UpdateInventoryBlockStatus.RELEASE:
            RuleEngine.action_allowed(
                action="inventory_block_release",
                facts=InventoryBlockFacts(),
                fail_on_error=True,
            )
            inventory_blocks = self.release_inventory_blocks(
                block_ids,
                hotel_id,
                user_action='release_inventory_block',
            )
            if inventory_blocks:
                self._publish_inventory_block_release_domain_event(
                    released_inventory_blocks=inventory_blocks
                )
        else:
            RuleEngine.action_allowed(
                action="create_inventory_block",
                facts=InventoryBlockFacts(
                    user_data=user_data,
                    hotel_id=hotel_id,
                ),
                fail_on_error=True,
            )
            inventory_requirement = self.build_inventory_requirement(
                hotel_id, inventory_block_data
            )
            self.inventory_service.update_inventory(
                block_inventory=inventory_requirement,
                user_action='block_inventory',
            )
            inventory_blocks = self.inventory_service.create_inventory_blocks(
                hotel_id, inventory_block_data, booking_id
            )
            self.job_scheduler_service.schedule_release_inventory(
                hotel_id, block_ids=[block.block_id for block in inventory_blocks]
            )

        return inventory_blocks

    @staticmethod
    def _publish_inventory_block_release_domain_event(
        released_inventory_blocks: List[InventoryBlock],
    ):
        domain_bus = get_domain_bus()
        domain_bus.publish(
            InventoryBlockReleaseEvent(blocks_released=released_inventory_blocks)
        )

    def release_inventory_blocks(
        self,
        block_ids,
        hotel_id,
        user_action=None,
        apply_inventory_updates=True,
        block_types_to_release=None,
    ):
        blocked_inventory_blocks = self._get_eligible_inventory_blocks(
            block_ids,
            lambda _block: _block.is_releasable()
            and (
                not block_types_to_release
                or _block.block_type in block_types_to_release
            ),
        )
        if not blocked_inventory_blocks:
            return []

        for block in blocked_inventory_blocks:
            block.mark_released()

        if apply_inventory_updates:
            self.apply_inventory_block_release_updates(
                hotel_id,
                blocked_inventory_blocks,
                user_action=user_action,
            )
        return blocked_inventory_blocks

    def fulfill_inventory_blocks(
        self,
        block_ids,
        hotel_id,
        user_action=None,
    ):
        blocked_inventory_blocks = self._get_eligible_inventory_blocks(
            block_ids, lambda _block: _block.is_fulfillable()
        )
        if not blocked_inventory_blocks:
            return []

        for block in blocked_inventory_blocks:
            block.mark_fulfilled()

        self.apply_inventory_block_release_updates(
            hotel_id,
            blocked_inventory_blocks,
            user_action=user_action,
        )
        return blocked_inventory_blocks

    def _get_eligible_inventory_blocks(
        self,
        block_ids,
        predicate: Callable[[Any], bool],
    ):
        inventory_blocks = self.inventory_block_repository.load_all(block_ids)
        return [
            inventory_block
            for inventory_block in inventory_blocks
            if predicate(inventory_block)
        ]

    def apply_inventory_block_release_updates(
        self,
        hotel_id,
        released_blocks,
        user_action,
    ):
        inventory_requirement = self.build_inventory_requirement(
            hotel_id,
            released_blocks,
        )
        self.inventory_service.update_inventory(
            release_inventory=inventory_requirement,
            user_action=user_action,
        )
        self.inventory_block_repository.update_all(released_blocks)

    @staticmethod
    def build_inventory_requirement(
        hotel_id,
        inventory_block_data,
    ):
        inventory_requirement = InventoryRequirement(hotel_id)
        for data in inventory_block_data:
            inventory_requirement.add(data.room_type_id, data.start_date, data.end_date)
        return inventory_requirement
