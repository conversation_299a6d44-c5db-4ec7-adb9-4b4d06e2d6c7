from typing import Dict, List


class NightAuditDto:
    def __init__(
        self,
        night_audit_id,
        hotel_id,
        business_date,
        status,
        failure_message=None,
        scheduled_time=None,
        vendors_with_pending_critical_tasks: List[Dict] = None,
    ):
        self.night_audit_id = night_audit_id
        self.hotel_id = hotel_id
        self.business_date = business_date
        self.status = status
        self.failure_message = failure_message
        self.scheduled_time = scheduled_time
        self.vendors_with_pending_critical_tasks = vendors_with_pending_critical_tasks
