import logging
from datetime import datetime

from treebo_commons.multitenancy.tenant_client import <PERSON>ant<PERSON><PERSON>
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.commands.consume_charge import ConsumeChargeCommand
from prometheus.application.decorators import session_manager
from prometheus.application.end_of_day.critical_task_service import CriticalTaskService
from prometheus.application.end_of_day.dtos.night_audit_dto import NightAuditDto
from prometheus.application.hotel_settings.catalog_application_service import (
    CatalogApplicationService,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.housekeeping.house_status_service import HouseStatusService
from prometheus.application.services.billing_document_sequence_service import (
    BillingDocumentSequenceService,
)
from prometheus.application.services.communication_application_service import (
    CommunicationApplicationService,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job.repositories.job_repository import JobRepository
from prometheus.async_job.job_registry import JobReg<PERSON>ry
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.repositories import InvoiceRepository
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.house_status_repository import (
    HouseStatusRepository,
)
from prometheus.domain.catalog.aggregates.night_audit_aggregate import (
    NightAuditAggregate,
)
from prometheus.domain.catalog.factories.night_audit_factory import NightAuditFactory
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.night_audit_repository import (
    NightAuditRepository,
)
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.catalog.repositories.seller_repository import SellerRepository
from prometheus.domain.critical_task.dtos.critical_task_search_query import (
    CriticalTaskSearchQuery,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.night_audit_facts import NightAuditFacts
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from prometheus.infrastructure.external_clients.authn_service_client import (
    AuthNServiceClient,
)
from prometheus.infrastructure.external_clients.authz_service_client import (
    AuthZServiceClient,
)
from prometheus.infrastructure.external_clients.pos_critical_task_client import (
    PosCriticalTaskClient,
)
from prometheus.reporting.end_of_day_report.service import EndOfDayReportService
from prometheus.reporting.trail_balance_report.service import (
    TrailBalanceReportingService,
)
from ths_common.constants.billing_constants import ChargeStatus, InvoiceStatus
from ths_common.constants.catalog_constants import NightAuditStatus
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.constants.scheduled_job_constants import JobName
from ths_common.exceptions import ValidationException
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        HotelRepository,
        BookingRepository,
        BillRepository,
        RoomTypeRepository,
        NewrelicServiceClient,
        JobSchedulerService,
        TenantSettings,
        CatalogApplicationService,
        TrailBalanceReportingService,
        JobRepository,
        CriticalTaskService,
        SellerRepository,
        PosCriticalTaskClient,
        NightAuditRepository,
        JobRegistry,
        AuthNServiceClient,
        AuthZServiceClient,
        CommunicationApplicationService,
        InvoiceRepository,
        HotelConfigRepository,
        EndOfDayReportService,
        HouseStatusService,
        HouseStatusRepository,
        BillingDocumentSequenceService,
    ]
)
class NightAuditService(object):
    INVOICE_UPDATE_PUBLISHER_BATCH_SIZE = 10

    def __init__(
        self,
        hotel_repo,
        booking_repo: BookingRepository,
        bill_repo,
        room_type_repo,
        alerting_service,
        job_scheduler_service,
        tenant_settings,
        catalog_application_service,
        trail_balance_reporting_service,
        job_repository: JobRepository,
        critical_task_service: CriticalTaskService,
        seller_repository: SellerRepository,
        pos_critical_task_client: PosCriticalTaskClient,
        night_audit_repository: NightAuditRepository,
        job_registry: JobRegistry,
        authn_service_client: AuthNServiceClient,
        authz_service_client: AuthZServiceClient,
        communication_application_service: CommunicationApplicationService,
        invoice_repository: InvoiceRepository,
        hotel_config_repository: HotelConfigRepository,
        end_of_day_report_service: EndOfDayReportService,
        house_status_service: HouseStatusService,
        house_status_repository: HouseStatusRepository,
        billing_document_sequence_service: BillingDocumentSequenceService,
    ):
        self.hotel_repo = hotel_repo
        self.booking_repo = booking_repo
        self.bill_repo = bill_repo
        self.alerting_service = alerting_service
        self.job_scheduler_service = job_scheduler_service
        self.room_type_repository = room_type_repo
        self.tenant_settings = tenant_settings
        self.catalog_application_service = catalog_application_service
        self.trail_balance_reporting_service = trail_balance_reporting_service
        self.job_repository = job_repository
        self.critical_task_service = critical_task_service
        self.seller_repository = seller_repository
        self.pos_critical_task_client = pos_critical_task_client
        self.night_audit_repository = night_audit_repository
        self.authn_service_client = authn_service_client
        self.authz_service_client = authz_service_client
        self.communication_application_service = communication_application_service
        self.invoice_repository = invoice_repository
        self.hotel_config_repository = hotel_config_repository
        self.end_of_day_report_service = end_of_day_report_service
        self.house_status_service = house_status_service
        self.house_status_repository = house_status_repository
        self.billing_document_sequence_service = billing_document_sequence_service
        job_registry.register(
            JobName.PERFORM_NIGHT_AUDIT_ASYNC_JOB_NAME.value, self.perform_night_audit
        )
        job_registry.register(
            JobName.CRITICAL_TASK_REMINDER.value, self.critical_task_reminder
        )

    def perform_night_audit(self, hotel_id) -> JobResultDto:
        logger.info(
            "perform_night_audit process begin for hotel_id=%s. Taking lock on hotel",
            hotel_id,
        )
        hotel_aggregate = self.hotel_repo.load_for_update(hotel_id, wait_for_lock=True)
        logger.info(
            "Lock taken on hotel. Hotel Business Date at the beginning of perform_night_audit hotel_id=%s: %s",
            hotel_id,
            hotel_aggregate.hotel.current_business_date,
        )
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        crs_context.set_hotel_context(hotel_aggregate, hotel_config_aggregate)

        if crs_context.is_treebo_tenant():
            if hotel_aggregate.hotel.current_business_date == dateutils.current_date():
                raise Exception(
                    "Can't rollover business date greater than current calendar date"
                )

        seller_aggregates = self.seller_repository.load_for_hotel_id(
            hotel_id=hotel_aggregate.hotel_id
        )
        vendors_with_pending_critical_tasks = (
            self._get_vendors_with_pending_critical_tasks(
                hotel_aggregate, seller_aggregates
            )
        )

        if vendors_with_pending_critical_tasks:
            self._night_audit_blocked_by_critical_task(
                vendors_with_pending_critical_tasks
            )
            return JobResultDto(
                run_successful=False,
                should_retry=True,
                retry_at_eta=dateutils.add(dateutils.current_datetime(), hours=1),
            )

        else:
            self._move_night_audit_to_in_progress()
            hotel_aggregate = self._perform_night_audit(
                hotel_aggregate, seller_aggregates
            )

            logger.info(
                "Outside _perform_night_audit method for hotel_id=%s, business_date=%s",
                hotel_aggregate.hotel_id,
                hotel_aggregate.hotel.current_business_date,
            )
            return JobResultDto.success()

    def _get_vendors_with_pending_critical_tasks(
        self, hotel_aggregate, seller_aggregates
    ) -> list:
        critical_tasks = self.critical_task_service.search_critical_tasks(
            CriticalTaskSearchQuery(
                hotel_aggregate.hotel_id,
                types=['critical_checkin', 'critical_checkout'],
            )
        )

        pending_critical_tasks = []
        if any(len(x.entity_ids) > 0 for x in critical_tasks):
            pending_critical_tasks.append(dict(hotel_id=hotel_aggregate.hotel_id))

        for seller_aggregate in seller_aggregates:
            if self.pos_critical_task_client.has_pending_critical_tasks(
                seller_aggregate.seller.seller_id
            ):
                pending_critical_tasks.append(
                    dict(seller_id=seller_aggregate.seller.seller_id)
                )

        return pending_critical_tasks

    def _night_audit_blocked_by_critical_task(
        self, vendors_with_pending_critical_tasks
    ):
        hotel_context = crs_context.get_hotel_context()
        night_audit_aggregate = self.night_audit_repository.load(
            hotel_context.hotel_id, hotel_context.current_date()
        )
        night_audit_aggregate.blocked_by_critical_task(
            vendors_with_pending_critical_tasks
        )
        self.night_audit_repository.update(night_audit_aggregate)
        job_aggregate = self.job_repository.get_job(night_audit_aggregate.job_id)

        # TODO: should send reminder to super-admins that there are pending critical tasks
        # schedule a task for critical task reminder for mid-night
        # job_dto = ScheduledJobDTO(
        #     job_name=JobName.CRITICAL_TASK_REMINDER.value,
        #     hotel_id=hotel_context.hotel_id,
        #     data=dict(hotel_id=hotel_context.hotel_id),
        #     eta=dateutils.datetime_at_midnight(dateutils.tomorrow()))
        # job_aggregate = self.job_scheduler_service.schedule(job_dto)
        IntegrationEventApplicationService.create_night_audit_scheduled_event(
            night_audit_aggregate, job_aggregate
        )

    def _move_night_audit_to_in_progress(self):
        hotel_context = crs_context.get_hotel_context()
        night_audit_aggregate = self.night_audit_repository.load(
            hotel_context.hotel_id, hotel_context.current_date()
        )
        logger.info(
            "_move_night_audit_to_in_progress for night_audit_id: %s",
            night_audit_aggregate.night_audit_id,
        )
        job_aggregate = self.job_repository.get_job(night_audit_aggregate.job_id)
        night_audit_aggregate.in_progress(start_time=dateutils.current_datetime())
        self.night_audit_repository.update(night_audit_aggregate)
        # NOTE: One off case where we're publishing integration event without storing, as we've to publish in
        # progress event in the mid of the entire night audit flow. And we can't commit, because it'll commit
        # everything, and release locks
        logger.info(
            "Publishing night audit started event for night_audit_id: %s",
            night_audit_aggregate.night_audit_id,
        )
        IntegrationEventApplicationService.publish_night_audit_started_event(
            night_audit_aggregate, job_aggregate, publish_without_storing=True
        )

    def _perform_night_audit(self, hotel_aggregate, seller_aggregates):
        logger.info(
            "_perform_night_audit method called for hotel_id=%s, business_date=%s",
            hotel_aggregate.hotel_id,
            hotel_aggregate.hotel.current_business_date,
        )
        if crs_context.is_treebo_tenant():
            if hotel_aggregate.hotel.current_business_date == dateutils.current_date():
                raise Exception(
                    "Can't rollover business date greater than current calendar date"
                )

        night_audit_aggregate = self.night_audit_repository.load(
            hotel_aggregate.hotel_id, hotel_aggregate.hotel.current_business_date
        )
        job_aggregate = self.job_repository.get_job(night_audit_aggregate.job_id)

        logger.info("posting past charges hotel_id: %s", hotel_aggregate.hotel_id)
        self._post_past_charges_and_payments_and_freeze_invoices(
            hotel_aggregate, seller_aggregates
        )

        night_audit_aggregate.completed(end_time=dateutils.current_datetime())
        self.night_audit_repository.update(night_audit_aggregate)

        IntegrationEventApplicationService.create_night_audit_completed_event(
            night_audit_aggregate, job_aggregate
        )

        self._save_current_business_date_house_statistics(
            hotel_aggregate.hotel_id, hotel_aggregate.hotel.current_business_date
        )
        logger.info(
            f"Business Date of Hotel: {hotel_aggregate.hotel_id} before rollover "
            f"is: {hotel_aggregate.hotel.current_business_date}"
        )

        hotel_aggregate.rollover_current_business_date()

        if hotel_aggregate.is_first_day_of_fiscal_year():
            self._process_fiscal_year_rollover(hotel_aggregate)

        logger.info(
            f"Business Date of Hotel: {hotel_aggregate.hotel_id} after rollover "
            f"is: {hotel_aggregate.hotel.current_business_date}"
        )
        for seller_aggregate in seller_aggregates:
            logger.info(
                f"Updating the Business Date of seller: {seller_aggregate.seller.seller_id} to "
                f"date: {hotel_aggregate.hotel.current_business_date}"
            )
            seller_aggregate.rollover_current_business_date(
                hotel_current_business_date=hotel_aggregate.hotel.current_business_date
            )

        self.job_scheduler_service.schedule_virtual_inventory_block_manager_job(
            hotel_id=hotel_aggregate.hotel_id,
            business_date=hotel_aggregate.hotel.current_business_date,
            user_action='night_audit',
        )

        self.hotel_repo.update(hotel_aggregate)
        self.seller_repository.update_all(seller_aggregates)

        logger.info(
            "Hotel business date for hotel_id: %s rolled over to: %s. Scheduling report generation",
            hotel_aggregate.hotel_id,
            hotel_aggregate.hotel.current_business_date,
        )

        self._update_house_status(hotel_aggregate)
        self._generate_report_on_night_audit(hotel_aggregate)
        self._process_financial_data_sync_on_night_audit(hotel_aggregate)
        self.catalog_application_service.rollover_current_business_date(hotel_aggregate)

        logger.info(
            "Night audit Completed - _perform_night_audit for hotel_id=%s, business_date=%s",
            hotel_aggregate.hotel_id,
            hotel_aggregate.hotel.current_business_date,
        )
        return hotel_aggregate

    def _post_past_charges_and_payments_and_freeze_invoices(
        self, hotel_aggregate, seller_aggregates
    ):
        vendors_id = [hotel_aggregate.hotel_id]
        for seller_aggregate in seller_aggregates:
            vendors_id.append(seller_aggregate.seller.seller_id)

        current_business_date = hotel_aggregate.hotel.current_business_date

        bill_aggregates = self.bill_repo.get_all_bills_for_night_audit(
            vendors_id, current_business_date
        )
        for bill_aggregate in bill_aggregates:
            bill_aggregate.post_eligible_payments(current_business_date)
            bill_aggregate.post_allowances(current_business_date)

        booking_aggregates = self.booking_repo.load_for_bill_ids_with_yield_per(
            bill_ids=[aggregate.bill.bill_id for aggregate in bill_aggregates]
        )

        grouped_bill_aggregates = {ba.bill_id: ba for ba in bill_aggregates}

        failed = False

        for booking_aggregate in booking_aggregates:
            bill_aggregate: BillAggregate = grouped_bill_aggregates.get(
                booking_aggregate.bill_id
            )
            bill_aggregate.cancel_preview_charges()
            try:
                ConsumeChargeCommand(
                    booking_aggregate,
                    bill_aggregate,
                    current_business_date,
                    crs_context.get_hotel_context(),
                    self.room_type_repository.load_type_map(),
                    allowed_charge_status=[ChargeStatus.CREATED],
                ).execute()
            except Exception as ex:
                logger.exception(
                    "Night audit failed for booking=%s hotel_id=%s",
                    booking_aggregate.booking.booking_id,
                    hotel_aggregate.hotel_id,
                )
                failed = True

        if failed:
            self.alerting_service.record_event(
                "night_audit_failure", {"hotel_id": hotel_aggregate.hotel_id}
            )
            # TODO collect and log all bookings that failed night audit here
            raise ValidationException(ApplicationErrors.NIGHT_AUDIT_FAILURE)

        self.bill_repo.update_all(bill_aggregates)

        invoice_aggregates = self.invoice_repository.load_for_night_audit(
            vendor_ids=vendors_id,
            current_business_date=current_business_date,
            exclude_statuses=[
                InvoiceStatus.PREVIEW.value,
                InvoiceStatus.LOCKED.value,
                InvoiceStatus.CANCELLED.value,
            ],
            for_update=True,
        )
        updated_invoice_aggregates = []
        for invoice_aggregate in invoice_aggregates:
            invoice_aggregate.lock()
            updated_invoice_aggregates.append(invoice_aggregate)
        self._publish_integration_events_for_locked_invoices(invoice_aggregates)
        self.invoice_repository.update_all(updated_invoice_aggregates)

    def _publish_integration_events_for_locked_invoices(self, invoice_aggregates):
        invoice_aggregates = [
            invoice_aggregate
            for invoice_aggregate in invoice_aggregates
            if invoice_aggregate.is_customer_invoice()
            and not invoice_aggregate.is_pos_seller_invoice()
        ]
        for _invoice_aggregate in chunks(
            invoice_aggregates, self.INVOICE_UPDATE_PUBLISHER_BATCH_SIZE
        ):
            IntegrationEventApplicationService.create_invoice_event(
                event_type=IntegrationEventType.INVOICE_UPDATED,
                invoice_aggregates=_invoice_aggregate,
                user_action="night_audit",
            )

    def _generate_report_on_night_audit(self, hotel_aggregate):
        report_run_date = dateutils.subtract(
            hotel_aggregate.hotel.current_business_date, days=1
        )
        self.end_of_day_report_service.schedule_report_generations(
            start_date=report_run_date,
            end_date=report_run_date,
            hotel_id=hotel_aggregate.hotel_id,
        )

    def _process_financial_data_sync_on_night_audit(self, hotel_aggregate):
        if self.tenant_settings.get_is_financial_data_sync_enabled(
            hotel_aggregate.hotel.hotel_id
        ):
            report_run_date = dateutils.subtract(
                hotel_aggregate.hotel.current_business_date, days=1
            )
            self.end_of_day_report_service.schedule_financial_data_sync(
                report_date=report_run_date,
                hotel_id=hotel_aggregate.hotel_id,
            )

    def _save_current_business_date_house_statistics(self, hotel_id, business_date):
        house_statistics_dto = self.house_status_service.generate_house_statistics(
            hotel_id, business_date
        )
        self.house_status_repository.save_house_statistics(
            hotel_id, house_statistics_dto
        )

    def _update_house_status(self, hotel_aggregate):
        self.house_status_service.set_house_status(
            hotel_aggregate.hotel_id, hotel_aggregate.hotel.current_business_date
        )

    def _process_fiscal_year_rollover(self, hotel_aggregate):
        self.billing_document_sequence_service.reset_billing_documents_sequence(
            hotel_aggregate
        )

    @session_manager(commit=True)
    def perform_night_audit_for_booking(self, hotel_id, booking_id):
        booking_aggregate = self.booking_repo.load(booking_id)
        hotel_aggregate = self.hotel_repo.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate
        )
        current_business_date = hotel_context.current_date()
        bill_aggregate = self.bill_repo.load_for_update(
            booking_aggregate.booking.bill_id
        )
        try:
            ConsumeChargeCommand(
                booking_aggregate,
                bill_aggregate,
                current_business_date,
                crs_context.get_hotel_context(),
                self.room_type_repository.load_type_map(),
            ).execute()
            self.bill_repo.update(bill_aggregate)
        except Exception as ex:
            logger.exception(
                "Night audit failed for booking=%s hotel_id=%s",
                booking_aggregate.booking.booking_id,
                hotel_id,
            )
            raise ex

    def get_next_scheduled_night_audit_job(self, hotel_id) -> NightAuditDto:
        hotel_aggregate = self.hotel_repo.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        crs_context.set_hotel_context(hotel_aggregate, hotel_config_aggregate)
        night_audit_aggregate = self.night_audit_repository.load(
            hotel_id, hotel_aggregate.hotel.current_business_date
        )
        if not night_audit_aggregate:
            if not self.tenant_settings.is_auto_night_audit_disabled(hotel_id):
                night_audit_aggregate = self.schedule_night_audit(hotel_id)
            else:
                return None

        job_aggregate = self.job_repository.get_job(night_audit_aggregate.job_id)
        if not job_aggregate.job_entity:
            return None
        return NightAuditDto(
            night_audit_id=night_audit_aggregate.night_audit_id,
            hotel_id=hotel_id,
            business_date=night_audit_aggregate.night_audit.business_date,
            scheduled_time=job_aggregate.job_entity.eta,
            status=night_audit_aggregate.night_audit.status,
            failure_message=job_aggregate.job_entity.failure_message,
            vendors_with_pending_critical_tasks=night_audit_aggregate.night_audit.vendors_with_pending_critical_tasks,
        )

    def get_last_completed_night_audit(self, hotel_id) -> NightAuditDto:
        hotel_aggregate = self.hotel_repo.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        crs_context.set_hotel_context(hotel_aggregate, hotel_config_aggregate)
        night_audit_aggregate = (
            self.night_audit_repository.get_last_completed_night_audit(hotel_id)
        )
        if not night_audit_aggregate:
            return None

        job_aggregate = self.job_repository.get_job(night_audit_aggregate.job_id)
        return NightAuditDto(
            night_audit_id=night_audit_aggregate.night_audit_id,
            hotel_id=hotel_id,
            business_date=night_audit_aggregate.night_audit.business_date,
            scheduled_time=job_aggregate.job_entity.eta,
            status=night_audit_aggregate.night_audit.status,
            failure_message=job_aggregate.job_entity.failure_message,
            vendors_with_pending_critical_tasks=night_audit_aggregate.night_audit.vendors_with_pending_critical_tasks,
        )

    def _schedule_night_audit(self, hotel_aggregate, eta):
        job_aggregate = self.job_scheduler_service.schedule_night_audit_job(
            hotel_aggregate.hotel_id, eta=eta
        )

        if get_current_tenant_id() == TenantClient.get_default_tenant():
            if hotel_aggregate.hotel.current_business_date == dateutils.to_date(eta):
                raise Exception(
                    "Can't rollover business date greater than current calendar date"
                )

        night_audit_aggregate = NightAuditFactory.create_new(
            hotel_aggregate.hotel_id,
            hotel_aggregate.hotel.current_business_date,
            job_id=job_aggregate.job_id,
        )
        self.night_audit_repository.save(night_audit_aggregate)
        IntegrationEventApplicationService.create_night_audit_scheduled_event(
            night_audit_aggregate, job_aggregate
        )
        return night_audit_aggregate, job_aggregate

    @session_manager(commit=True)
    def schedule_night_audit(self, hotel_id) -> NightAuditAggregate:
        hotel_aggregate = self.hotel_repo.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate
        )
        night_audit_aggregate = self.night_audit_repository.load(
            hotel_id, hotel_aggregate.hotel.current_business_date
        )
        if night_audit_aggregate:
            return night_audit_aggregate

        if self.tenant_settings.is_auto_night_audit_disabled(hotel_id):
            return None

        switch_over_time_string = hotel_aggregate.hotel.switch_over_time
        switch_over_time = datetime.strptime(switch_over_time_string, "%H:%M:%S").time()
        night_audit_time = dateutils.datetime_at_given_time(
            dateutils.add(hotel_context.current_date(), days=1), switch_over_time
        )

        night_audit_aggregate, _ = self._schedule_night_audit(
            hotel_aggregate, night_audit_time
        )
        return night_audit_aggregate

    @session_manager(commit=True)
    def reschedule_night_audit(self, hotel_id, eta, run_now=False) -> NightAuditDto:
        hotel_aggregate = self.hotel_repo.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        crs_context.set_hotel_context(hotel_aggregate, hotel_config_aggregate)
        night_audit_aggregate = self.night_audit_repository.load(
            hotel_id, hotel_aggregate.hotel.current_business_date
        )
        if run_now:
            eta = dateutils.current_datetime()

        if hotel_aggregate.hotel.current_business_date == dateutils.to_date(eta):
            raise ValidationException(
                error=ApplicationErrors.BUSINESS_DATE_ROLLOVER_TO_FUTURE_NOT_ALLOWED
            )

        hotel_context = crs_context.get_hotel_context()
        business_date = (
            night_audit_aggregate.night_audit.business_date
            if night_audit_aggregate
            else hotel_context.current_date()
        )
        RuleEngine.action_allowed(
            action="reschedule_night_audit",
            facts=NightAuditFacts(
                business_date=business_date, hotel_context=hotel_context, eta=eta
            ),
            fail_on_error=True,
        )
        if not night_audit_aggregate:
            night_audit_aggregate, job_aggregate = self._schedule_night_audit(
                hotel_aggregate, eta
            )
        else:
            job_aggregate = self.job_repository.get_job(night_audit_aggregate.job_id)
            job_aggregate.reschedule(eta=eta)
            if night_audit_aggregate.night_audit.status in {
                NightAuditStatus.FAILED,
                NightAuditStatus.BLOCKED_BY_CRITICAL_TASK,
            }:
                night_audit_aggregate.night_audit.status = NightAuditStatus.SCHEDULED
                self.night_audit_repository.update(night_audit_aggregate)
            self.job_repository.update(job_aggregate)
            if not run_now:
                IntegrationEventApplicationService.create_night_audit_scheduled_event(
                    night_audit_aggregate, job_aggregate
                )

        return NightAuditDto(
            night_audit_id=night_audit_aggregate.night_audit_id,
            hotel_id=hotel_id,
            business_date=night_audit_aggregate.night_audit.business_date,
            scheduled_time=job_aggregate.job_entity.eta,
            status=night_audit_aggregate.night_audit.status,
            failure_message=job_aggregate.job_entity.failure_message,
            vendors_with_pending_critical_tasks=night_audit_aggregate.night_audit.vendors_with_pending_critical_tasks,
        )

    def critical_task_reminder(self, hotel_id):
        hotel_aggregate = self.hotel_repo.load(hotel_id)
        seller_aggregates = self.seller_repository.load_for_hotel_id(
            hotel_id=hotel_aggregate.hotel_id
        )
        hotel_name = hotel_aggregate.hotel.name
        # check if the hotel still has pending critical tasks
        critical_task_pending = self._get_vendors_with_pending_critical_tasks(
            hotel_aggregate, seller_aggregates
        )
        # if no, then do nothing
        if not critical_task_pending:
            return
        # if yes, fetch all super-admins for the hotel from AuthZ & AuthN
        super_admin_ids = self.authz_service_client.get_all_users_with_role(
            hotel_id, "super-admin"
        )
        super_admin_users = self.authn_service_client.get_all_user_details(
            super_admin_ids['data']['users']
        )
        # send them emails
        self.communication_application_service.send_critical_task_reminder_communication(
            hotel_name, super_admin_users['data']['users']
        )
        # schedule another task for critical task reminder for next mid-night
        self.job_scheduler_service.schedule_critical_task_reminder(hotel_id)

    def get_hotels_with_pending_night_audit(self, pending_days=None):
        hotel_details = self.hotel_repo.get_hotel_with_pending_night_audit(pending_days)
        hotel_details_dto = {}
        for hotel in hotel_details:
            hotel_dto = {
                "name": hotel.name,
                "current_business_date": hotel.current_business_date.strftime(
                    '%Y-%m-%d'
                ),
                "city": hotel.city.name,
            }
            hotel_details_dto[hotel.hotel_id] = hotel_dto
        return hotel_details_dto
