from treebo_commons.money.money import Money

from prometheus import crs_context
from prometheus.application.funding.funding_amount_calculator_helper import (
    FundingAmountCalculatorHelper,
)
from prometheus.application.funding.strategy.base_strategy import AutoFundingStrategy


class LowestFirstStrategy(AutoFundingStrategy):
    def calculate_funding_amount(
        self,
        booking_aggregate,
        bill_aggregate,
        funding_amount_details=None,
        booking_funding_config=None,
    ):
        room_stay_funding_deltas = self._fetch_negative_deltas(
            booking_aggregate,
            bill_aggregate,
            booking_funding_config,
        )
        if not funding_amount_details or not room_stay_funding_deltas:
            return []

        manual_funding_amount = funding_amount_details.manual_funding_amount
        auto_funding_amount = funding_amount_details.auto_funding_amount

        total_negative_delta = sum(
            abs(funding_detail.delta.amount)
            for funding_detail in room_stay_funding_deltas
        )

        if manual_funding_amount >= total_negative_delta:
            return self._apply_manual_funding_only(
                room_stay_funding_deltas, manual_funding_amount
            )
        elif manual_funding_amount + auto_funding_amount >= total_negative_delta:
            return self._apply_manual_funding_first(
                room_stay_funding_deltas, manual_funding_amount, auto_funding_amount
            )
        else:
            return self._apply_auto_funding_first(
                room_stay_funding_deltas, manual_funding_amount, auto_funding_amount
            )

    @staticmethod
    def _fetch_negative_deltas(
        booking_aggregate,
        bill_aggregate,
        booking_funding_config,
    ):
        return [
            funding_detail
            for funding_detail in FundingAmountCalculatorHelper.fetch_room_stay_funding_deltas(
                booking_aggregate,
                bill_aggregate,
                booking_funding_config,
            )
            if funding_detail.delta.amount < 0
        ]

    def _apply_manual_funding_only(
        self, room_stay_funding_deltas, manual_funding_amount
    ):
        sorted_funding_details = sorted(
            room_stay_funding_deltas, key=lambda x: abs(x.delta.amount)
        )
        funding_adjustments = []

        for funding_detail in sorted_funding_details:
            manual_funding_applied = min(
                manual_funding_amount, abs(funding_detail.delta)
            )
            manual_funding_amount -= manual_funding_applied

            funding_adjustments.append(
                self._create_funding_adjustment(
                    funding_detail,
                    manual_funding_applied,
                    Money(0, crs_context.hotel_context.base_currency),
                )
            )

        return funding_adjustments

    def _apply_manual_funding_first(
        self, room_stay_funding_deltas, manual_funding_amount, auto_funding_amount
    ):
        sorted_funding_details = sorted(
            room_stay_funding_deltas,
            key=lambda x: (x.is_auto_funding_applicable, abs(x.delta.amount)),
        )
        funding_adjustments = []

        for funding_detail in sorted_funding_details:
            manual_funding_applied = min(
                manual_funding_amount, abs(funding_detail.delta)
            )
            manual_funding_amount -= manual_funding_applied
            remaining_delta = abs(funding_detail.delta) - manual_funding_applied

            auto_funding_applied = (
                min(auto_funding_amount, remaining_delta)
                if funding_detail.is_auto_funding_applicable
                else Money(0, crs_context.hotel_context.base_currency)
            )
            auto_funding_amount -= auto_funding_applied

            funding_adjustments.append(
                self._create_funding_adjustment(
                    funding_detail, manual_funding_applied, auto_funding_applied
                )
            )

        return funding_adjustments

    def _apply_auto_funding_first(
        self, room_stay_funding_deltas, manual_funding_amount, auto_funding_amount
    ):
        sorted_funding_details = sorted(
            room_stay_funding_deltas,
            key=lambda x: (not x.is_auto_funding_applicable, abs(x.delta.amount)),
        )
        funding_adjustments = []

        for funding_detail in sorted_funding_details:
            auto_funding_applied = (
                min(auto_funding_amount, abs(funding_detail.delta))
                if funding_detail.is_auto_funding_applicable
                else Money(0, crs_context.hotel_context.base_currency)
            )
            auto_funding_amount -= auto_funding_applied
            remaining_delta = abs(funding_detail.delta) - auto_funding_applied

            manual_funding_applied = min(manual_funding_amount, remaining_delta)
            manual_funding_amount -= manual_funding_applied

            funding_adjustments.append(
                self._create_funding_adjustment(
                    funding_detail, manual_funding_applied, auto_funding_applied
                )
            )

        return funding_adjustments

    @staticmethod
    def _create_funding_adjustment(
        funding_detail, manual_funding_applied, auto_funding_applied
    ):
        return {
            "room_stay_id": funding_detail.room_stay_id,
            "date": funding_detail.date,
            "manual_funding_amount": manual_funding_applied,
            "auto_funding_amount": auto_funding_applied,
            "is_auto_funding_applicable": funding_detail.is_auto_funding_applicable,
            "delta": (
                abs(funding_detail.delta)
                - manual_funding_applied
                - auto_funding_applied
            ),
        }
