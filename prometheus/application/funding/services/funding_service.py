from treebo_commons.money import Money
from treebo_commons.utils.dateutils import ymd_str_to_date

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.funding.funding_amount_calculator_helper import (
    FundingAmountCalculatorHelper,
)
from prometheus.application.funding.strategy.strategy_context import (
    AutoFundingStrategyContext,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.domain.billing.dto.taxable_item import TaxableItem
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services import TaxService
from prometheus.domain.booking.entities.booking_funding import BookingFundingRequest
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.booking.repositories.booking_funding_repository import (
    BookingFundingRepository,
)
from prometheus.domain.catalog.repositories import ResellerGstRepository
from prometheus.infrastructure.external_clients.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.funding_constants import (
    TREEBO_EMAIL_ID,
    TREEBO_FUNDING_CUSTOMER_LEGAL_NAME,
    TREEBO_PHONE_NUMBER,
    FundingExpenseTypes,
    FundingStatus,
    FundingStrategy,
    FundingType,
)
from ths_common.value_objects import Address, GSTDetails


@register_instance(
    dependencies=[
        BookingFundingRepository,
        FundingAmountCalculatorHelper,
        ResellerGstRepository,
        BilledEntityService,
        CompanyProfileServiceClient,
        TenantSettings,
        BookingRepository,
        BillRepository,
        IntegrationEventApplicationService,
        TaxService,
    ]
)
class FundingService:
    def __init__(
        self,
        booking_funding_repository: BookingFundingRepository,
        funding_amount_calculator_helper: FundingAmountCalculatorHelper,
        reseller_gst_repository: ResellerGstRepository,
        billed_entity_service: BilledEntityService,
        company_profile_service_client: CompanyProfileServiceClient,
        tenant_settings: TenantSettings,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        integration_event_application_service: IntegrationEventApplicationService,
        tax_service: TaxService,
    ):
        self.booking_funding_repository = booking_funding_repository
        self.funding_amount_calculator_helper = funding_amount_calculator_helper
        self.reseller_gst_repository = reseller_gst_repository
        self.billed_entity_service = billed_entity_service
        self.company_profile_service_client = company_profile_service_client
        self.tenant_settings = tenant_settings
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.integration_event_application_service = (
            integration_event_application_service
        )
        self.tax_service = tax_service

    def calculate_amount_for_funding(
        self, booking_aggregate, bill_aggregate, auto_funding_applicable=True
    ):
        funding_amount_details = (
            self.funding_amount_calculator_helper.get_funding_amounts(
                booking_aggregate, auto_funding_applicable
            )
        )
        booking_funding_config = self.booking_funding_repository.load_funding_config(
            booking_id=booking_aggregate.booking_id
        )
        strategy_to_be_used = self._get_strategy(funding_amount_details)
        strategy = AutoFundingStrategyContext.get_strategy(strategy_to_be_used)
        return strategy.calculate_funding_amount(
            booking_aggregate,
            bill_aggregate,
            funding_amount_details,
            booking_funding_config,
        )

    @staticmethod
    def _get_strategy(funding_amount_details):
        if (
            funding_amount_details.auto_funding_amount
            and not funding_amount_details.manual_funding_amount
        ):
            return FundingStrategy.LOWEST_FIRST.value
        return FundingStrategy.EQUAL_DISTRIBUTION.value

    def invalidate_or_lock_funding_requests(self, booking_id, lock=False):
        funding_requests = self.booking_funding_repository.load_all_funding_requests(
            booking_id=booking_id,
            status=FundingStatus.CREATED,
        )
        if not funding_requests:
            return
        new_status = FundingStatus.LOCKED if lock else FundingStatus.CANCELLED
        for funding_request in funding_requests:
            funding_request.status = new_status
        self.booking_funding_repository.update_funding_requests(funding_requests)

    def add_customer_and_billed_entity_for_auto_funding(
        self, booking_aggregate, bill_aggregate
    ):
        reseller_gst_details = self._get_reseller_gst_details()
        company_detail = self._get_company_by_gstin(reseller_gst_details.gstin_num)

        customer_info = self._build_customer_info(
            reseller_gst_details=reseller_gst_details,
            company_detail=company_detail,
        )

        dummy_customer = booking_aggregate.add_dummy_customer(customer_info)

        self.billed_entity_service.add_billed_entity_for_auto_funding(
            dummy_customer, bill_aggregate
        )

    def _get_reseller_gst_details(self):
        state_id = crs_context.hotel_context.legal_state_id
        return self.reseller_gst_repository.load(state_id=state_id).gst_details

    def _get_company_by_gstin(self, gstin_num):
        if not gstin_num:
            return None

        company_data = (
            self.company_profile_service_client.search_company_profile_by_gstin(
                gstin=gstin_num
            )
        )
        sub_entities = company_data.get("sub_entities") if company_data else None
        return sub_entities[0] if sub_entities else None

    def _build_customer_info(
        self,
        reseller_gst_details,
        company_detail=None,
    ):
        external_ref_id = getattr(company_detail, "superhero_company_code", None)

        address = (
            self._convert_to_thsc_address(company_detail.registered_address)
            if company_detail and company_detail.registered_address
            else reseller_gst_details.address
        )

        if company_detail:
            gst_details = GSTDetails(
                legal_name=TREEBO_FUNDING_CUSTOMER_LEGAL_NAME,
                address=address,
                gstin_num=reseller_gst_details.gstin_num,
                has_lut=getattr(company_detail, "has_lut", False),
                is_sez=getattr(company_detail, "is_sez_applicable", False),
            )
        else:
            gst_details = GSTDetails(
                legal_name=TREEBO_FUNDING_CUSTOMER_LEGAL_NAME,
                address=reseller_gst_details.address,
                gstin_num=reseller_gst_details.gstin_num,
            )

        return {
            "first_name": TREEBO_FUNDING_CUSTOMER_LEGAL_NAME,
            "gst_details": gst_details,
            "email": TREEBO_EMAIL_ID,
            "phone": TREEBO_PHONE_NUMBER,
            "external_ref_id": external_ref_id,
        }

    @staticmethod
    def _convert_to_thsc_address(address):
        return Address(
            field_1=address.address_line_1,
            field_2=address.address_line_2,
            city=address.city,
            state=address.state,
            country=address.country,
            pincode=address.pincode,
        )

    def get_funding_reason(self, booking_id):
        """
        Logic:
            There can't be multiple manual funding or auto funding request.
            If manual funding is present then auto funding won't be applied.
        """
        funding_requests = self.booking_funding_repository.load_all_funding_requests(
            booking_id, status=FundingStatus.CREATED
        )

        for funding_request in funding_requests:
            if (
                funding_request.funding_type == FundingType.MANUAL_FUNDING
                and funding_request.amount > 0
            ):
                return funding_request.reason or "Processed Via Manual Funding"

        return "Lower price due to loyalty discount"

    def compute_and_publish_funding_amount_delta(
        self, booking_id, hotel_aggregate=None, old_manual_funding_amount=None
    ):
        """
        Calculate funding amount details and publish events only if there are changes
        in either AUTO_FUNDING or MANUAL_FUNDING.

        Args:
            booking_id: ID of the booking to calculate funding for
            hotel_aggregate: Optional hotel context
            old_manual_funding_amount: Optional old manual funding amount

        Returns:
            bool: True if changes were made, False otherwise
        """
        booking_aggregate = self.booking_repository.load(booking_id)
        bill_aggregate = self.bill_repository.load(booking_aggregate.booking.bill_id)

        if booking_aggregate.booking.status in [
            BookingStatus.CHECKED_OUT,
            BookingStatus.CANCELLED,
            BookingStatus.NOSHOW,
        ]:
            return False

        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(booking_aggregate.hotel_id)
        crs_context.set_current_booking(booking_aggregate)

        (
            existing_auto_funding_amount,
            existing_manual_funding_amount,
        ) = self._get_existing_funding_amounts(booking_id)

        if old_manual_funding_amount is not None:
            existing_manual_funding_amount = old_manual_funding_amount

        new_funding_details = self.calculate_amount_for_funding(
            booking_aggregate, bill_aggregate
        )
        enriched_funding_details = self.enrich_funding_details_with_tax(
            new_funding_details
        )

        (
            new_auto_funding_amount,
            new_manual_funding_amount,
        ) = self._calculate_new_funding_amounts(enriched_funding_details)

        if (new_auto_funding_amount != existing_auto_funding_amount) or (
            new_manual_funding_amount != existing_manual_funding_amount
        ):
            self.create_or_update_auto_funding_requests(
                booking_id, enriched_funding_details
            )
            funding_changed_details = self._build_funding_change_event_payload(
                existing_auto_funding_amount,
                new_auto_funding_amount,
                existing_manual_funding_amount,
                new_manual_funding_amount,
            )
            self.integration_event_application_service.create_funding_details_event(
                funding_changed_details
            )
            return True
        return False

    def _get_existing_funding_amounts(self, booking_id):
        funding_request_details = (
            self.booking_funding_repository.load_all_funding_requests(
                booking_id=booking_id,
                status=FundingStatus.CREATED,
            )
        )
        existing_auto_funding_amount = Money(0, crs_context.hotel_context.base_currency)
        existing_manual_funding_amount = Money(
            0, crs_context.hotel_context.base_currency
        )
        for funding_request in funding_request_details:
            if funding_request.funding_type == FundingType.AUTO_FUNDING:
                existing_auto_funding_amount += funding_request.amount
            else:
                existing_manual_funding_amount += funding_request.amount
        return existing_auto_funding_amount, existing_manual_funding_amount

    @staticmethod
    def _calculate_new_funding_amounts(funding_details):
        auto_funding_amount = Money(0, crs_context.hotel_context.base_currency)
        manual_funding_amount = Money(0, crs_context.hotel_context.base_currency)
        for fd in funding_details:
            manual_funding_amount += fd.get("manual_funding_amount")
            auto_funding_amount += fd.get("auto_funding_amount")
        return auto_funding_amount, manual_funding_amount

    def enrich_funding_details_with_tax(self, funding_details):
        """
        For each funding detail, if auto funding is applicable,
        calculate the tax and override 'auto_funding_amount' with the post-tax amount.
        """
        taxable_items_map = {}

        for idx, detail in enumerate(funding_details):
            if detail.get('is_auto_funding_applicable'):
                auto_amount = detail['auto_funding_amount']
                if auto_amount.amount == 0:
                    continue
                applicable_date = ymd_str_to_date(detail['date'])
                taxable_item = TaxableItem(
                    sku_category_id=FundingExpenseTypes.STAY.value,
                    applicable_date=applicable_date,
                    pretax_amount=auto_amount,
                    posttax_amount=None,
                )
                taxable_items_map[idx] = taxable_item

        taxed_items = self.tax_service.calculate_taxes(
            list(taxable_items_map.values()),
            hotel_id=crs_context.hotel_context.hotel_id,
        )

        # Override the original auto_funding_amount with taxed amount
        for idx, taxed_item in zip(taxable_items_map.keys(), taxed_items):
            funding_details[idx]['auto_funding_amount'] = taxed_item.posttax_amount

        return funding_details

    def create_or_update_auto_funding_requests(
        self, booking_id, applicable_funding_details
    ):
        total_amount = Money(0, crs_context.hotel_context.base_currency)

        for funding_details in applicable_funding_details:
            if funding_details.get('is_auto_funding_applicable'):
                total_amount += funding_details.get('auto_funding_amount')

        self._create_or_update_auto_funding_request(booking_id, total_amount)

    def _create_or_update_auto_funding_request(self, booking_id, amount):
        booking_funding_request = self.booking_funding_repository.load_funding_request(
            booking_id=booking_id,
            status=FundingStatus.CREATED,
            funding_type=FundingType.AUTO_FUNDING,
        )
        if booking_funding_request:
            booking_funding_request.update_amount(amount)
            self.booking_funding_repository.update_funding_request(
                booking_funding_request
            )
            return

        if amount > 0:
            booking_funding_request = BookingFundingRequest(
                booking_id=booking_id,
                amount=amount,
                funding_type=FundingType.AUTO_FUNDING,
                status=FundingStatus.CREATED,
                deleted=False,
            )
            self.booking_funding_repository.save_funding_request(
                booking_funding_request
            )
            return

    @staticmethod
    def _build_funding_change_event_payload(
        existing_auto_amount,
        new_auto_amount,
        existing_manual_amount,
        new_manual_amount,
    ):
        """
        Build a payload containing only the changed funding types with old and new values.

        Returns:
            dict: A dictionary with keys for changed funding types and their old/new amounts.
        """
        payload = {}

        if new_auto_amount != existing_auto_amount:
            payload["auto_funding"] = {
                "old_amount": existing_auto_amount,
                "new_amount": new_auto_amount,
            }

        if new_manual_amount != existing_manual_amount:
            payload["manual_funding"] = {
                "old_amount": existing_manual_amount,
                "new_amount": new_manual_amount,
            }

        return payload
