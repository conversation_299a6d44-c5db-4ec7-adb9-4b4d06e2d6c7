from datetime import datetime

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from shared_kernel.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from shared_kernel.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from shared_kernel.infrastructure.external_clients.treebo_platform_client import (
    TreeboPlatformClient,
)
from ths_common.constants.booking_constants import RatePlanCodes, RatePlanTags
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.constants.web_checkin_constants import (
    WEB_CHECKIN_LINK_HASH_NAME,
    EmailTemplates,
    MessageTemplates,
)


@register_instance(
    dependencies=[
        TreeboPlatformClient,
        CatalogServiceClient,
        CommunicationServiceClient,
        RoomTypeRepository,
        BillRepository,
        TenantSettings,
    ]
)
class CommunicationApplicationService:
    def __init__(
        self,
        treebo_platform_client,
        catalog_service_client,
        communication_service_client,
        room_type_repository,
        bill_repository,
        tenant_settings,
    ):
        self.treebo_platform_client = treebo_platform_client
        self.catalog_service_client = catalog_service_client
        self.communication_service_client = communication_service_client
        self.room_type_repository = room_type_repository
        self.bill_repository = bill_repository
        self.tenant_settings = tenant_settings

    def _build_payload_for_web_checkin_communication(self, booking_aggregate):
        booking_owner = booking_aggregate.get_booking_owner()
        adult_count, child_count = booking_aggregate.get_adult_and_child_count()
        room_count = len(booking_aggregate.room_stays)
        bill_aggregate = self.bill_repository.load(booking_aggregate.bill_id)
        room_type_list = []
        room_type_id_aggregate_map = self.room_type_repository.load_type_map()
        for room_stay in booking_aggregate.room_stays:
            room_type_aggregate = room_type_id_aggregate_map[room_stay.room_type_id]
            room_type_list.append(room_type_aggregate.room_type.type)

        first_name = getattr(booking_owner, "first_name", "Guest")
        hash_value = self.treebo_platform_client.get_hash_value(
            original_data=booking_aggregate.booking_id,
            hash_name=WEB_CHECKIN_LINK_HASH_NAME,
            expiry=dateutils.isoformat_datetime(
                dateutils.datetime_at_max_time_of_day(
                    booking_aggregate.booking.checkin_date
                )
            ),
        )

        web_checkin_url = self.tenant_settings.get_setting_value(
            TenantSettingName.URL_FOR_WEB_CHECKIN.value
        )
        hash_link = web_checkin_url + hash_value if hash_value else None
        hotel_details = self.catalog_service_client.get_property_details(
            hotel_id=booking_aggregate.hotel_id
        )
        rate_plan = self._get_rate_plan_for_booking(booking_aggregate)
        is_rate_plan_refundable = self._is_rate_plan_refundable(
            rate_plan,
            booking_aggregate.booking.checkin_date,
            hotel_details["guest_facing_details"]["checkin_time"],
            booking_aggregate.booking.created_at,
        )
        hotel_number = (
            hotel_details["property_details"]["reception_landline"]
            if hotel_details["property_details"].get("reception_landline")
            else hotel_details["property_details"].get("reception_mobile")
        )
        hotel_address = (
            f"{hotel_details['location']['postal_address']}, {hotel_details['location']['city']['name']}, "
            f"{hotel_details['location']['state']['name']}"
        )
        image_url = None
        if hotel_details.get("property_images"):
            image = min(
                hotel_details.get("property_images"),
                key=lambda image: image['sort_order'],
            )
            image_url = image["path"]
        context_data = dict(
            first_name=first_name,
            booking_id=booking_aggregate.booking.reference_number,
            hotel_name=hotel_details["name"]["new_name"].title(),
            hotel_number=hotel_number,
            address=hotel_address,
            city=hotel_details['location']['city']['name'],
            google_link=hotel_details["location"]["maps_link"],
            image_url=image_url,
            checkin_date=booking_aggregate.booking.checkin_date.strftime("%d %b, %Y"),
            checkin_day=booking_aggregate.booking.checkin_date.strftime("%A"),
            checkin_time=hotel_details["guest_facing_details"]["checkin_time"],
            checkout_date=booking_aggregate.booking.checkout_date.strftime("%d %b, %Y"),
            checkout_day=booking_aggregate.booking.checkout_date.strftime("%A"),
            checkout_time=hotel_details["guest_facing_details"]["checkout_time"],
            web_checkin_link=hash_link,
            adults=adult_count,
            children=child_count,
            room_count=room_count,
            room_type=", ".join(set(room_type_list)),
            total_tariff=str(bill_aggregate.total_posttax_amount()),
            pretax_price=str(bill_aggregate.total_pretax_amount()),
            total_tax=str(bill_aggregate.total_tax_amount()),
            prepaid_amount=str(bill_aggregate.paid_amount),
            balance_amount=str(bill_aggregate.net_payable),
            rate_plan_type=RatePlanTags.REFUNDABLE.value
            if is_rate_plan_refundable
            else RatePlanTags.NON_REFUNDABLE.value,
            logo=hotel_details["logo"],
        )
        return context_data

    def send_communication_for_web_checkin(
        self,
        booking_aggregate: BookingAggregate,
        sms_template=None,
        email_template=None,
    ):
        email, phone_number, _ = booking_aggregate.get_contact_details()
        if not self.tenant_settings.get_setting_value(
            TenantSettingName.WEB_CHECKIN_ENABLED.value,
            hotel_id=booking_aggregate.hotel_id,
        ):
            return
        if not (email or phone_number):
            return
        context_data = self._build_payload_for_web_checkin_communication(
            booking_aggregate=booking_aggregate
        )
        if email and email_template:
            subject = email_template.subject.format(
                booking_id=booking_aggregate.booking.reference_number
            )
            self.communication_service_client.send_email(
                identifier=email_template.identifier,
                context_data=context_data,
                to_emails=[email],
                subject=subject,
            )
        if (
            phone_number
            and sms_template
            and self.tenant_settings.get_setting_value(
                TenantSettingName.WEB_CHECKIN_SMS_ENABLED.value,
                hotel_id=booking_aggregate.hotel_id,
            )
        ):
            try:
                self.communication_service_client.send_sms_or_whatsapp(
                    identifier=sms_template.value,
                    context_data=context_data,
                    receivers=[phone_number],
                )
            except Exception as ex:
                if 'Invalid phone number in identifier' in str(ex):
                    return
                elif 'too short to be a phone number' in str(ex):
                    return
                else:
                    raise

    def send_rejection_web_checkin_communication(
        self, booking_aggregate: BookingAggregate, rejection_reason
    ):
        email, phone_number, _ = booking_aggregate.get_contact_details()
        if not (email or phone_number):
            return
        context_data = self._build_payload_for_web_checkin_communication(
            booking_aggregate=booking_aggregate
        )
        rejection_reasons_with_msg = self.tenant_settings.get_setting_value(
            TenantSettingName.WEB_CHECKIN_REJECTION_REASONS_WITH_MSG.value,
            hotel_id=booking_aggregate.hotel_id,
        )
        rejection_reasons_with_msg = (
            rejection_reasons_with_msg if rejection_reasons_with_msg else []
        )
        for reason in rejection_reasons_with_msg:
            if reason['label'] == rejection_reason:
                context_data['rejection_reason'] = reason['value']
        subject = EmailTemplates.WEB_CHECKIN_REJECTION.subject.format(
            booking_id=booking_aggregate.booking.reference_number
        )
        if email:
            self.communication_service_client.send_email(
                identifier=EmailTemplates.WEB_CHECKIN_REJECTION.identifier,
                context_data=context_data,
                to_emails=[email],
                subject=subject,
            )
        if phone_number and self.tenant_settings.get_setting_value(
            TenantSettingName.WEB_CHECKIN_SMS_ENABLED.value,
            hotel_id=booking_aggregate.hotel_id,
        ):
            self.communication_service_client.send_sms_or_whatsapp(
                identifier=MessageTemplates.WEB_CHECKIN_REJECTION.value,
                context_data=context_data,
                receivers=[phone_number],
            )

    def send_critical_task_reminder_communication(self, hotel_name, users):
        context_data = (
            {}
        )  # TODO: add link to the pending web checking page for the hotel in context_data
        for user in users:
            email = user['email']
            subject = EmailTemplates.CRITICAL_TASKS_REMINDER.subject.format(
                hotel_name=hotel_name
            )
            if email:
                self.communication_service_client.send_email(
                    identifier=EmailTemplates.CRITICAL_TASKS_REMINDER.identifier,
                    context_data=context_data,
                    to_emails=[email],
                    subject=subject,
                )

    @staticmethod
    def _get_rate_plan_for_booking(booking_aggregate):
        rate_plans = booking_aggregate.rate_plans
        if rate_plans:
            return rate_plans[0].name
        return RatePlanCodes.TRB_DEFAULT

    @staticmethod
    def _is_rate_plan_refundable(
        rate_plan, checkin, hotel_checkin_time, booking_creation_datetime
    ):
        is_refundable = True
        checkin = dateutils.to_date(checkin)
        if isinstance(hotel_checkin_time, str):
            hotel_checkin_time = datetime.strptime(
                hotel_checkin_time, "%H:%M:%S"
            ).time()
        checkin_time = (
            hotel_checkin_time or datetime.strptime("12:00:00", "%H:%M:%S").time()
        )
        hours_to_checkin = (
            dateutils.datetime_at_given_time(checkin, checkin_time)
            - booking_creation_datetime
        ).total_seconds() / 3600
        if rate_plan == RatePlanCodes.TRB_DEFAULT_NRP.value:
            is_refundable = False
        elif rate_plan == RatePlanCodes.TRB_DEFAULT.value and hours_to_checkin < 24:
            is_refundable = False
        elif (
            rate_plan == RatePlanCodes.TRB_DEFAULT_D_5.value and hours_to_checkin < 120
        ):
            is_refundable = False
        return is_refundable
