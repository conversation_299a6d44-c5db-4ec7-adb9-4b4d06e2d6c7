import logging

from object_registry import register_instance
from ths_common.constants.option_constants import OPTION_KEY_MAP
from ths_common.exceptions import ResourceNotFound

logger = logging.getLogger(__name__)


@register_instance()
class OptionsApplicationService(object):
    def build_options_list(self, user_data, keys=None):
        try:
            final_options_list = list()

            if keys is None:
                keys = OPTION_KEY_MAP.keys()

            for key in keys:
                final_options_list.append(
                    (
                        OPTION_KEY_MAP[key],
                        OPTION_KEY_MAP[key].get_enums(user_data.user_type),
                    )
                )
            from prometheus.common.serializers.response.options import (
                OptionResponseSchema,
            )

            response = OptionResponseSchema(many=True).dump(final_options_list).data
        except KeyError:
            raise ResourceNotFound("Options", "Options Key: {} not found".format(key))

        return response
