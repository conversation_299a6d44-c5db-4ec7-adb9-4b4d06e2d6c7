import logging
from collections import defaultdict
from typing import Dict, List, Union

from object_registry import register_instance
from prometheus.domain.billing.models import (
    CreditNoteSequenceModel,
    InvoiceSequenceModel,
)
from prometheus.domain.billing.repositories.credit_note_series_repository import (
    CreditNoteSequenceRepository,
)
from prometheus.domain.billing.repositories.invoice_series_repository import (
    InvoiceSequenceRepository,
)
from prometheus.domain.catalog.aggregates.hotel_aggregate import HotelAggregate
from prometheus.domain.catalog.repositories import (
    ResellerGstRepository,
    SellerRepository,
)
from ths_common.constants.billing_constants import IssuedByType

logger = logging.getLogger(__name__)

# Type alias
# sequence entry is either an invoice or credit note sequence
SequenceEntry = Union[InvoiceSequenceModel, CreditNoteSequenceModel]
SequenceLookupTable = Dict[str, Dict[IssuedByType, List[SequenceEntry]]]
BillingSequenceRepository = Union[
    InvoiceSequenceRepository, CreditNoteSequenceRepository
]


@register_instance(
    dependencies=[
        InvoiceSequenceRepository,
        CreditNoteSequenceRepository,
        SellerRepository,
        ResellerGstRepository,
    ]
)
class BillingDocumentSequenceService:
    def __init__(
        self,
        invoice_sequence_repository: InvoiceSequenceRepository,
        credit_note_sequence_repository: CreditNoteSequenceRepository,
        seller_repository: SellerRepository,
        reseller_gst_repository: ResellerGstRepository,
    ):
        self.invoice_repo: InvoiceSequenceRepository = invoice_sequence_repository
        self.credit_note_repo: CreditNoteSequenceRepository = (
            credit_note_sequence_repository
        )
        self.seller_repo: SellerRepository = seller_repository
        self.reseller_gst_repo: ResellerGstRepository = reseller_gst_repository

    def reset_billing_documents_sequence(self, hotel_aggregate: HotelAggregate):
        """Resets invoice and credit note sequences for a hotel's billing cycle."""
        vendor_ids = self._load_vendors(hotel_aggregate)
        issued_by_types: List[IssuedByType] = [
            IssuedByType.HOTEL,
            IssuedByType.RESELLER,
            IssuedByType.SELLER,
        ]

        self._reset_sequence(
            doc_type="invoice",
            repository=self.invoice_repo,
            hotel_aggregate=hotel_aggregate,
            vendor_ids=vendor_ids,
            issued_by_types=issued_by_types,
        )
        self._reset_sequence(
            doc_type="credit_note",
            repository=self.credit_note_repo,
            hotel_aggregate=hotel_aggregate,
            vendor_ids=vendor_ids,
            issued_by_types=issued_by_types,
        )

    def _reset_sequence(
        self,
        doc_type: str,
        repository: BillingSequenceRepository,
        hotel_aggregate: HotelAggregate,
        vendor_ids: List[str],
        issued_by_types: List[IssuedByType],
    ):
        """
        Resets sequence numbers for invoices or credit notes.
        """
        sequence_lookup_table: SequenceLookupTable = self._load_existing_sequences(
            repository, vendor_ids
        )
        entries_to_update: List["SequenceEntry"] = []

        for vendor_id in vendor_ids:
            for issued_by_type in issued_by_types:
                sequences = self._find_available_sequence(
                    doc_type,
                    sequence_lookup_table,
                    vendor_id,
                    issued_by_type,
                )
                for sequence in sequences:
                    sequence.last_sequence_number = 0
                    sequence_generation_strategy = repository.get_generation_strategy()
                    sequence.prefix = repository.get_default_prefix(
                        vendor_id,
                        issued_by_type,
                        business_date=hotel_aggregate.hotel.current_business_date,
                        sequence_generation_strategy=sequence_generation_strategy,
                    )
                    sequence.sequence_generation_strategy = (
                        repository.get_generation_strategy()
                    )
                    entries_to_update.append(sequence)

        if entries_to_update:
            repository.bulk_update(entries_to_update)

    @staticmethod
    def _load_existing_sequences(
        repository: BillingSequenceRepository, vendor_ids: List[str]
    ) -> SequenceLookupTable:
        """
        Loads all existing invoice or credit note sequences for the given vendors.
        Returns a structured lookup dictionary.
        """
        sequence_lookup: SequenceLookupTable = defaultdict(lambda: defaultdict(list))
        sequences = repository.load_all(vendor_ids=vendor_ids)

        for sequence in sequences:
            sequence_lookup[sequence.vendor_id][
                IssuedByType(sequence.issued_by_type)
            ].append(sequence)

        return sequence_lookup

    @staticmethod
    def _find_available_sequence(
        doc_type: str,
        sequence_lookup_table: SequenceLookupTable,
        vendor_id: str,
        issued_by_type: IssuedByType,
    ) -> List[SequenceEntry]:
        """
        Finds an available sequence for the given vendor and issued_by_type.
        """

        sequences = sequence_lookup_table[vendor_id][issued_by_type]
        if not sequences:
            logger.info(
                f"No {doc_type} sequence found for vendor_id: {vendor_id},"
                f" issued_by_type: {issued_by_type}"
            )

        return sequences

    def _load_vendors(self, hotel_aggregate: HotelAggregate) -> List[str]:
        """
        Loads vendor IDs and their respective GSTIN mappings.
        """
        vendor_ids: List[str] = [hotel_aggregate.hotel_id]

        # Load sellers linked to the hotel
        seller_aggregates = self.seller_repo.load_for_hotel_id(
            hotel_id=hotel_aggregate.hotel_id
        )
        vendor_ids.extend(seller.seller.seller_id for seller in seller_aggregates)
        return vendor_ids
