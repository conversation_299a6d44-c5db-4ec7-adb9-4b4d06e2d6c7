from object_registry import register_instance
from prometheus.application.cashiering.helpers.auth_helper import (
    fail_if_user_not_authorized_to_access_cashier_module,
)
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.billing.repositories import CashierSessionRepository


@register_instance(
    dependencies=[
        CashierSessionRepository,
    ]
)
class GetCashierSessionSummaryQueryHandler:
    def __init__(
        self,
        cashier_session_repository: CashierSessionRepository,
    ):
        self.cashier_session_repository = cashier_session_repository

    @set_hotel_context()
    def handle(
        self,
        cashier_session_id,
        use_raw_query=None,
        user_data=None,
        hotel_aggregate=None,
    ):
        fail_if_user_not_authorized_to_access_cashier_module(user_data)
        cashier_session_aggregate = self.cashier_session_repository.load(
            cashier_session_id, use_raw_query=use_raw_query
        )
        return cashier_session_aggregate.session_summary
