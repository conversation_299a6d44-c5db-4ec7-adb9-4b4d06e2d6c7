from object_registry import register_instance
from prometheus.domain.billing.dto.cashier_session_search_query import (
    CashierSessionSearchQuery,
)
from prometheus.domain.billing.repositories import CashierSessionRepository


@register_instance(
    dependencies=[
        CashierSessionRepository,
    ]
)
class CountCashierSessionQueryHandler:
    def __init__(
        self,
        cashier_session_repository: CashierSessionRepository,
    ):
        self.cashier_session_repository = cashier_session_repository

    def handle(self, cash_register_id, search_query: CashierSessionSearchQuery):
        count = self.cashier_session_repository.count(cash_register_id, search_query)
        return count
