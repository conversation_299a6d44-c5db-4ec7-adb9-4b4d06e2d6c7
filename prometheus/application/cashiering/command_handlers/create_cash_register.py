import logging

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.factories.cash_register_factory import (
    CashRegisterFactory,
)
from prometheus.domain.billing.repositories.cash_register_repository import (
    CashRegisterRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from ths_common.exceptions import ValidationException
from ths_common.value_objects import CashCounterAmount

logger = logging.getLogger(__name__)


@register_instance(dependencies=[CashRegisterRepository])
class CreateCashRegisterCommandHandler:
    def __init__(
        self,
        cash_register_repository: CashRegisterRepository,
    ):
        self.cash_register_repository = cash_register_repository

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(self, cash_register_dto, user_data, hotel_aggregate):
        # Assumption here is hotel and all its seller would have same base currency.
        # Set seller base currency separately once different use cases come to break this assumption
        if not hotel_aggregate:
            raise ValidationException(ApplicationErrors.HOTEL_NOT_FOUND)
        base_currency = hotel_aggregate.hotel.base_currency
        current_datetime = dateutils.current_datetime()
        cash_register_aggregate = CashRegisterFactory.create_new_cash_register(
            vendor_id=cash_register_dto['vendor_id'],
            default_opening_balance=CashCounterAmount(
                cash_register_dto.get('default_opening_balance')
            ),
            opened_by=user_data.user,
            cash_register_name=cash_register_dto['cash_register_name'],
            start_datetime=current_datetime,
            carry_balance_to_next_session=cash_register_dto.get(
                'carry_balance_to_next_session'
            ),
            base_currency=base_currency,
        )
        RuleEngine.action_allowed(
            action="open_cash_register",
            facts=Facts(
                user_type=user_data.user_type,
                action_payload=cash_register_dto,
                cashier_session_aggregate=cash_register_aggregate,
                hotel_context=crs_context.get_hotel_context(),
            ),
            fail_on_error=True,
        )
        self.cash_register_repository.save(cash_register_aggregate)
        return cash_register_aggregate
