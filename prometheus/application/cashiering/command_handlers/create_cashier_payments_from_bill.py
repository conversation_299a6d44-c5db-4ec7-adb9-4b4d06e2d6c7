import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.cashiering.command_handlers.create_cashier_session_payment import (
    CreateCashierSessionPaymentCommandHandler,
)
from prometheus.application.cashiering.helpers import cashier_payment_dto_creator
from prometheus.application.decorators import set_hotel_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories import CashierSessionRepository
from prometheus.domain.billing.repositories.cash_register_repository import (
    CashRegisterRepository,
)
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ths_common.exceptions import ValidationException

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CashierSessionRepository,
        CashRegisterRepository,
        AwsServiceClient,
        CreateCashierSessionPaymentCommandHandler,
    ]
)
class CreateCashierPaymentFromBillCommandHandler:
    DefaultSignedUrlExpirationSeconds = 7200

    def __init__(
        self,
        cashier_session_repository: CashierSessionRepository,
        cash_register_repository: CashRegisterRepository,
        aws_service_client,
        create_cashier_payment_command_handler: CreateCashierSessionPaymentCommandHandler,
    ):
        self.cashier_session_repository = cashier_session_repository
        self.cash_register_repository = cash_register_repository
        self.aws_service_client = aws_service_client
        self.create_cashier_payment_command_handler = (
            create_cashier_payment_command_handler
        )

    # TODO: Duplicate Code
    @set_hotel_context()
    def latest_cashier_session(self, cash_register_id, hotel_aggregate=None):
        cashier_session_aggregate = (
            self.cashier_session_repository.get_latest_cashier_session(cash_register_id)
        )
        if not cashier_session_aggregate:
            return 0, None
        return (
            int(cashier_session_aggregate.cashier_session.session_number),
            cashier_session_aggregate,
        )

    @set_hotel_context()
    def handle(self, payment, booking_aggregate, bill_aggregate, hotel_aggregate):
        cash_register_aggregate = (
            self.cash_register_repository.get_cash_register_for_vendor(
                bill_aggregate.vendor_id
            )
        )
        if not cash_register_aggregate:
            raise ValidationException(ApplicationErrors.NO_CASH_REGISTER_FOUND)

        latest_cashier_session_aggregate = (
            self.cashier_session_repository.get_latest_cashier_session(
                cash_register_aggregate.cash_register.cash_register_id
            )
        )

        if not latest_cashier_session_aggregate:
            raise ValidationException(ApplicationErrors.NO_CASHIER_SESSION_FOUND)

        payment_dto = cashier_payment_dto_creator.create_cashier_payment_dto(
            bill_aggregate, booking_aggregate, payment
        )
        self.create_cashier_payment_command_handler.add_cashier_payment(
            latest_cashier_session_aggregate.cashier_session.cashier_session_id,
            payment_dto,
            crs_context.user_data,
            hotel_aggregate,
        )
