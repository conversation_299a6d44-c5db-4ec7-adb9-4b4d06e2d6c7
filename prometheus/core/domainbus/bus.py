from collections import defaultdict
from typing import Callable, Dict, List, Type

from flask import g

from prometheus.core.domainbus.types import DomainEvent


class DomainBus:
    def __init__(self):
        self._listeners: Dict[
            Type[DomainEvent], List[Callable[[DomainEvent], None]]
        ] = defaultdict(list)

    def subscribe(
        self, event_type: Type[DomainEvent], handler: Callable[[DomainEvent], None]
    ):
        self._listeners[event_type].append(handler)

    def publish(self, event: DomainEvent):
        for handler in self._listeners[type(event)]:
            handler(event)


def get_domain_bus() -> DomainBus:
    if 'domain_bus' not in g:
        # pylint: disable=assigning-non-slot
        g.domain_bus = DomainBus()
    return g.domain_bus
