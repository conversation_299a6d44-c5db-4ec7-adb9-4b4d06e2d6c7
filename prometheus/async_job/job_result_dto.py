class JobResultDto:
    def __init__(
        self,
        run_successful=True,
        should_retry=False,
        retry_at_eta=None,
        remarks=None,
        **kwargs
    ):
        if run_successful and should_retry:
            raise ValueError("Both run_successful and should_retry cannot be True")
        self.run_successful = run_successful
        self.should_retry = should_retry
        self.retry_at_eta = retry_at_eta
        self.remarks = remarks
        self.kwargs = kwargs

    @staticmethod
    def success(**kwargs):
        return JobResultDto(**kwargs)
