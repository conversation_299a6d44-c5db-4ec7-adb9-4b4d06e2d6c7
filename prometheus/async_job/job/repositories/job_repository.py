from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.async_job.job.aggregates.job_aggregate import JobAggregate
from prometheus.async_job.job.entities.job_entity import JobEntity
from prometheus.async_job.job.models import JobModel
from prometheus.common.decorators import timed
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.scheduled_job_constants import JOB_PRIORITY, JobStatus


class JobAdapter:
    @staticmethod
    def to_db_model(job_entity):
        job = JobModel()
        job.hotel_id = job_entity.hotel_id
        job.serialized_job = job_entity.data
        job.job_name = job_entity.job_name
        job.eta = job_entity.eta
        job.generated_at = job_entity.generated_at
        job.job_id = job_entity.job_id
        job.status = job_entity.status.value
        job.picked_at = job_entity.picked_at
        job.failure_message = job_entity.failure_message
        job.total_tries = job_entity.total_tries
        job.seller_id = job_entity.seller_id
        job.priority = JOB_PRIORITY.get(job_entity.job_name, 10)

        return job

    @staticmethod
    def to_entity(job_model):
        return JobEntity(
            job_model.job_name,
            job_model.hotel_id,
            job_model.serialized_job,
            job_model.eta,
            generated_at=job_model.generated_at,
            job_id=job_model.job_id,
            status=JobStatus(job_model.status),
            seller_id=job_model.seller_id,
            picked_at=job_model.picked_at,
            failure_message=job_model.failure_message,
            total_tries=job_model.total_tries,
        )


@register_instance()
class JobRepository(BaseRepository):
    def to_aggregate(self, job_model):
        if not job_model:
            entity = None
        else:
            entity = JobAdapter.to_entity(job_model)

        return JobAggregate(entity)

    def from_aggregate(self, aggregate):
        return JobAdapter.to_db_model(aggregate.job_entity)

    @timed
    def get_oldest_schedulable_job(self):
        query = (
            self.session()
            .query(JobModel)
            .filter(
                JobModel.status.in_(
                    [JobStatus.CREATED.value, JobStatus.RESCHEDULED.value]
                )
            )
            .filter(JobModel.eta <= dateutils.current_datetime())
            .order_by(JobModel.priority, JobModel.generated_at, JobModel.eta)
        )

        query = query.with_for_update(skip_locked=True)  # Skips rows that are locked

        return self.to_aggregate(job_model=query.first())

    def get_job(self, pk, with_lock=False):
        if with_lock:
            return self.to_aggregate(
                job_model=self.session()
                .query(JobModel)
                .with_for_update(nowait=False)
                .get(pk)
            )

        return self.to_aggregate(job_model=self.session().query(JobModel).get(pk))

    def save(self, job_aggregate):
        job_model = self.from_aggregate(job_aggregate)
        self._save(job_model)
        self.flush_session()

    @timed
    def update(self, job_aggregate):
        job_model = self.from_aggregate(job_aggregate)
        self._update(job_model)
        self.flush_session()

    def get_all_jobs(self, job_name=None):
        query = self.session().query(JobModel)
        if job_name:
            query = query.filter(JobModel.job_name == job_name)
        return [self.to_aggregate(job) for job in query.all()]

    def get_active_scheduled_job(self, hotel_id, job_name):
        query = (
            self.session()
            .query(JobModel)
            .filter(
                JobModel.job_name == job_name,
                JobModel.status.in_(
                    [JobStatus.CREATED.value, JobStatus.PROCESSING.value]
                ),
                JobModel.hotel_id == hotel_id,
            )
            .order_by(JobModel.generated_at, JobModel.eta)
        )
        return [self.to_aggregate(job) for job in query.all()]

    def delete_jobs(self, job_aggregates):
        self.session().query(JobModel).filter(
            JobModel.job_id.in_([aggregate.job_id for aggregate in job_aggregates])
        ).delete(synchronize_session=True)
