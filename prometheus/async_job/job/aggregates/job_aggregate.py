from treebo_commons.utils import dateutils

from prometheus.async_job.job.entities.job_entity import JobEntity
from ths_common.constants.scheduled_job_constants import JobStatus


class JobAggregate:
    def __init__(self, job_entity: JobEntity):
        self.job_entity = job_entity

    @property
    def status(self):
        return self.job_entity.status

    @property
    def job_id(self):
        return self.job_entity.job_id

    def update_status(self, success, failure_message=None):
        status = JobStatus.FINISHED if success else JobStatus.FAILED
        self.job_entity.status = status
        self.job_entity.failure_message = failure_message
        self.job_entity.total_tries += 1

    def failed(self, failure_message):
        self.job_entity.status = JobStatus.FAILED
        self.job_entity.failure_message = failure_message
        self.job_entity.total_tries += 1

    def completed(self):
        self.job_entity.status = JobStatus.FINISHED
        self.job_entity.failure_message = None

    def is_completed(self):
        return self.job_entity.status == JobStatus.FINISHED

    def is_failed(self):
        return self.job_entity.status == JobStatus.FAILED

    def reschedule(self, eta, reason=None):
        self.job_entity.eta = eta
        self.job_entity.status = JobStatus.RESCHEDULED

        if reason:
            self.job_entity.failure_message = reason

    def mark_picked_for_execution(self):
        self.job_entity.status = JobStatus.PICKED_FOR_EXECUTION
        self.job_entity.picked_at = dateutils.current_datetime()
