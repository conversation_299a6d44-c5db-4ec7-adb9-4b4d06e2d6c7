#!/bin/bash

set -e
set -x

ENV="$1"
export VERSION="$2"
export APP="$3"
TARGET="$4"
export regions="$5"
export tenant_service_url="${6}"
export CLUSTER_IDENTIFIER="$7"
export AWS_SECRET_PREFIX="$8"

echo "Using env : $ENV"
echo "Current build directory: $BUILD_DIR"

if [[ -z "${BUILD_DIR}" ]]; then
  echo "Please set BUILD_DIR env variable, which should be path where code will be checked out to create build"
  exit 1
fi

allTenants=()

function loadActiveTenants {
  # Read Tenant Ids from TenantGateway
  echo "Loading active tenants"
  if [ "$ENV" = "staging" ]; then
      active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")
  elif [ "$ENV" = "production" ]; then
      active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")
  fi
  # allTenants=($(echo "$active_tenants" | jq -r '.data[] | select(.tenant_code | .!= "treebo") | .tenant_id'))
  allTenants=($(echo "$active_tenants" | jq -r '.data[].tenant_id'))
  echo $allTenants
}

loadActiveTenants

echo "Tenants loaded: ${allTenants[@]}"
echo "Deploying $APP app on $ENV environment"

if [ "$ENV" = "staging" ]; then
    source $BUILD_DIR/$APP/envrepo/prometheus/build_env/staging
    echo "Running prometheus-apiservice container"

    if [ $TARGET = "app" ]; then
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/stag-compose.yml up -d prometheus-apiservice

        echo "Running worker containers for each tenants"
        for tenant_id in ${allTenants[@]}; do
            export TENANT_ID=$tenant_id
            # -p option is to pass COMPOSE_PROJECT_NAME. That is the only way to run multiple containers for same compose service
            # With this option, docker attaches different networks, to these containers
            echo "Tenant ID: $tenant_id"
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/stag-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-event-daemon
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/stag-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-task-daemon
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/stag-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-task-executor
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/stag-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-catalog-consumer
        done

    elif [ $TARGET = "crs_reports" ]; then
        export ENV_FILE=$BUILD_DIR/$APP/envrepo/prometheus/docker_env/crs_reports_staging.env
        echo "Running prometheus-apiservice container for reports"
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/stag-compose.yml up -d prometheus-reports-apiservice
    fi

elif [ "$ENV" = "production" ]; then
    source $BUILD_DIR/$APP/envrepo/prometheus/build_env/prod
    if [ $TARGET = "app" ]; then
        echo "Running prometheus-apiservice container"
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml up -d prometheus-apiservice

    elif [ $TARGET = "worker" ]; then
        echo "Running worker containers for each tenants"
        for tenant_id in ${allTenants[@]}; do
            export TENANT_ID=$tenant_id
            echo "Tenant ID: $tenant_id"
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-integration-event-publisher
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-job-executor
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-job-scheduler
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-catalog-consumer
        done

    elif [ $TARGET = "serialized-workers" ]; then
        echo "Running worker containers for each tenants"
        for tenant_id in ${allTenants[@]}; do
            export TENANT_ID=$tenant_id
            echo "Tenant ID: $tenant_id"
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-integration-event-publisher
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-job-scheduler
        done

    elif [ $TARGET = "parallelized-workers" ]; then
        echo "Running worker containers for each tenants"
        for tenant_id in ${allTenants[@]}; do
            export TENANT_ID=$tenant_id
            echo "Tenant ID: $tenant_id"
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-job-executor
            docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml -p crs_tenant_${TENANT_ID} up -d prometheus-catalog-consumer
        done

    elif [ $TARGET = "reporting" ]; then
        export ENV_FILE=$BUILD_DIR/$APP/envrepo/prometheus/docker_env/reporting.env
        docker-compose -f $BUILD_DIR/$APP/docker/prod-compose.yml up -d prometheus-reporting-consumer

    elif [ $TARGET = "crs_reports" ]; then
        export ENV_FILE=$BUILD_DIR/$APP/envrepo/prometheus/docker_env/crs_reports_production.env
        echo "Running prometheus-apiservice container for reports"
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml up -d prometheus-apiservice
    fi
fi
