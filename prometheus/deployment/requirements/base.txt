# Transitive Dependency
# Brought in by Flask. Freeze Werkzeug, because next werkzeug version has introduced breaking change
Werkzeug==2.0.3

# Mandatory dependencies
psycopg2==2.9.2
psycogreen==1.0.2
Flask==2.0.2
SQLAlchemy==1.4.27
gunicorn[gevent]==20.1.0
requests==2.26.0
transitions==0.8.10
aenum==3.1.5
logstash-formatter==0.5.17
phonenumbers==8.12.37
marshmallow>=2,<3
object-mapper==1.1.0

# Fix this
# Kombu 5.2.0rc1 => Brings in Support for Python3.9
# Kombu 4.6.6 => Brings in Support for Python3.8
kombu==5.4.2
# easyjoblite==0.7.6

# API Documentation
flasgger==0.9.5
apispec==3.3.2,<4
apispec-webframeworks==0.5.2

# Reporting
pycountry==22.1.10

# Monitoring
newrelic==7.2.4.171
sentry-sdk[flask]==1.4.3
analytics-python==1.4.0

# Additional Optional Dependencies
# flask-swagger-ui==3.36.0
aiohttp==3.8.1
python-dateutil==2.8.2
jsonpickle==2.0.0
simplejson==3.17.6

# AWS
boto3==1.20.11


pydantic==2.10.6
#apispec-pydantic-plugin==0.6.0

