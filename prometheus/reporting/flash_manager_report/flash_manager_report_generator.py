from prometheus.reporting.base_report_generator import BaseReportGenerator


class FlashManagerReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows for a given flash manager report request
    """

    FLASH_MANAGER_REPORT_FOLDER_NAME = 'flash_manager_reports/'

    def generate(self):
        pass

    @staticmethod
    def generate_flash_manager_report_file_name(extension='csv'):
        return FlashManagerReportGenerator.generate_report_file_path(
            'Flash Manager Report', extension
        )

    @staticmethod
    def get_default_expiration_time():
        return 604800  # 7 days
