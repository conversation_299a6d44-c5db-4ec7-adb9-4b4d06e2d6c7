import logging

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.report.factories.guest_ledger_factory import GuestLedgerFactory
from prometheus.domain.report.repositories.guest_ledger_repository import (
    GuestLedgerRepository,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from prometheus.reporting.trail_balance_report.dtos.guest_ledger_dto import (
    GuestLedgerDTO,
)
from ths_common.constants.booking_constants import BookingStatus

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        ReportingApplicationService,
        GuestLedgerRepository,
    ]
)
class GuestLedgerService(object):
    def __init__(
        self,
        booking_repository,
        bill_repository,
        hotel_repository,
        reporting_application_service,
        guest_ledger_repository,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.reporting_application_service = reporting_application_service
        self.guest_ledger_repository = guest_ledger_repository

    def _base_report_data(
        self, start_date, end_date, hotel_aggregate, booking_status=None
    ):
        checked_in_booking_aggregates = (
            self.booking_repository.trail_balance_report_query(
                dateutils.date_to_ymd_str(start_date),
                dateutils.date_to_ymd_str(end_date),
                hotel_aggregate.hotel_id,
                booking_status=[
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                ],
            )
        )

        checked_out_booking_aggregates = (
            self.booking_repository.trail_balance_checked_out_booking_query(
                dateutils.date_to_ymd_str(start_date),
                end_date=None,
                hotel_id=hotel_aggregate.hotel_id,
                booking_status=[
                    BookingStatus.PART_CHECKOUT.value,
                    BookingStatus.CHECKED_OUT.value,
                ],
            )
        )

        business_date_created_booking_and_checked_out_booking_aggregates = self.booking_repository.trail_balance_current_business_date_booking_created_and_checkout_query(
            hotel_id=hotel_aggregate.hotel_id,
            business_date=hotel_aggregate.hotel.current_business_date,
        )

        checked_in_booking_supposed_to_be_checked_out = self.booking_repository.trail_balance_report_query_for_inconsistent_bookings(
            dateutils.date_to_ymd_str(start_date),
            dateutils.date_to_ymd_str(end_date),
            hotel_aggregate.hotel_id,
            booking_status=[
                BookingStatus.CHECKED_IN.value,
                BookingStatus.PART_CHECKIN.value,
            ],
        )

        bill_aggregates_for_past_dated_charge_addition = (
            self.bill_repository.trail_balance_past_dated_charge_addition_query(
                hotel_aggregate.hotel_id, dateutils.date_to_ymd_str(start_date)
            )
        )
        booking_aggregates_for_past_dated_charge_addition = (
            self.booking_repository.load_for_bill_ids_with_given_booking_status(
                bill_ids=[
                    bill_aggregate.bill_id
                    for bill_aggregate in bill_aggregates_for_past_dated_charge_addition
                ],
                exclude_booking_statuses=[BookingStatus.NOSHOW.value],
            )
        )

        bill_aggregates_for_payment_made_on_running_date = (
            self.bill_repository.trail_balance_payments_added_on_running_date(
                hotel_aggregate.hotel_id,
                dateutils.date_to_ymd_str(start_date),
                dateutils.date_to_ymd_str(end_date),
            )
        )
        booking_aggregates_for_payment_made_on_running_date = self.booking_repository.load_for_bill_ids_with_given_booking_status(
            bill_ids=[
                bill_aggregate.bill_id
                for bill_aggregate in bill_aggregates_for_payment_made_on_running_date
            ],
            exclude_booking_statuses=[BookingStatus.NOSHOW.value],
        )
        booking_aggregates_payment_added_for_checked_out_bookings = [
            booking
            for booking in booking_aggregates_for_payment_made_on_running_date
            if booking.booking.actual_checkout_date
            and dateutils.to_date(booking.booking.actual_checkout_date) < start_date
        ]

        bill_aggregates_for_allowance_added_on_running_date = (
            self.bill_repository.trail_balance_allowance_added_on_running_date(
                hotel_aggregate.hotel_id, dateutils.date_to_ymd_str(start_date)
            )
        )

        booking_aggregates_for_allowance_added_on_running_date = self.booking_repository.load_for_bill_ids_with_yield_per(
            [
                bill_aggregate.bill_id
                for bill_aggregate in bill_aggregates_for_allowance_added_on_running_date
            ]
        )

        previous_day_guest_ledger_aggregates = self.guest_ledger_repository.load_for_report_date_with_non_zero_closing_balance(
            dateutils.subtract(start_date, days=1), hotel_id=hotel_aggregate.hotel_id
        )

        guest_ledger_booking_ids = [
            gl.booking_id for gl in previous_day_guest_ledger_aggregates
        ]

        guest_ledger_booking_aggregates = (
            self.booking_repository.load_bookings_with_given_status(
                guest_ledger_booking_ids,
                booking_status=[BookingStatus.CHECKED_OUT.value],
            )
        )

        hotel_map = {hotel_aggregate.hotel_id: hotel_aggregate}

        return (
            checked_in_booking_aggregates
            + checked_out_booking_aggregates
            + checked_in_booking_supposed_to_be_checked_out
            + booking_aggregates_for_past_dated_charge_addition
            + booking_aggregates_payment_added_for_checked_out_bookings
            + booking_aggregates_for_allowance_added_on_running_date
            + guest_ledger_booking_aggregates
            + business_date_created_booking_and_checked_out_booking_aggregates
        ), hotel_map

    def generate_summary(
        self, start_date, end_date, hotel_aggregate, bookings_to_be_explicitly_included
    ):
        if self.is_business_date_guest_ledger_report_generated(
            start_date, end_date, hotel_aggregate.hotel_id
        ):
            return
        guest_ledger_dto = GuestLedgerDTO()
        base_currency = hotel_aggregate.hotel.base_currency
        booking_aggregates, hotel_map = self._base_report_data(
            start_date, end_date, hotel_aggregate
        )
        booking_aggregates += bookings_to_be_explicitly_included
        (
            bill_ids,
            booking_ids,
            booking_aggregates,
        ) = guest_ledger_dto.get_unique_bookings(booking_aggregates)
        guest_ledger_aggregates = self.guest_ledger_repository.load_all(booking_ids)

        booking_id_to_guest_ledger_aggregate_mapping = (
            guest_ledger_dto.booking_guest_ledger_aggregate_mapping(
                guest_ledger_aggregates
            )
        )

        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids, load_invoices_by_bill_id=True, hotel_map=hotel_map
        )
        for booking_aggregate in booking_aggregates:
            latest_guest_ledger = guest_ledger_dto.get_latest_guest_ledger(
                booking_aggregate.booking_id
            )
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )

            guest_ledger_dto.calculate_charge_components(
                booking_aggregate, bill_aggregate, start_date, end_date
            )
            (
                revenue_components,
                non_revenue_components,
                charges_transferred_to_ar,
                total_charges,
            ) = (
                guest_ledger_dto.revenue_components,
                guest_ledger_dto.non_revenue_components,
                guest_ledger_dto.charges_transferred_to_ar,
                guest_ledger_dto.total_charges,
            )

            guest_ledger_dto.calculate_payment_components(
                booking_aggregate, bill_aggregate, start_date, end_date
            )
            payment_components, deposit_transferred_at_checkin, total_payments = (
                guest_ledger_dto.payment_components,
                guest_ledger_dto.deposit_transferred_at_checkin,
                guest_ledger_dto.total_payments,
            )

            opening_balance_in_base_currency = Money(0, base_currency)
            if booking_id_to_guest_ledger_aggregate_mapping.get(
                booking_aggregate.booking_id
            ):
                guest_ledger = self.create_guest_ledger_for_existing_booking(
                    base_currency,
                    booking_aggregate,
                    deposit_transferred_at_checkin,
                    latest_guest_ledger,
                    non_revenue_components,
                    payment_components,
                    revenue_components,
                    start_date,
                    total_charges,
                    total_payments,
                    hotel_aggregate,
                    charges_transferred_to_ar,
                )
                guest_ledger_dto.new_guest_ledgers.append(guest_ledger)
            else:
                guest_ledger = self.create_guest_ledger_for_new_booking(
                    base_currency,
                    booking_aggregate,
                    deposit_transferred_at_checkin,
                    non_revenue_components,
                    opening_balance_in_base_currency,
                    payment_components,
                    revenue_components,
                    total_charges,
                    total_payments,
                    start_date,
                    hotel_aggregate,
                    charges_transferred_to_ar,
                )
                guest_ledger_dto.new_guest_ledgers.append(guest_ledger)

            guest_ledger_dto.reset_ledger_variables()

        logger.info(
            f"Successfully generated guest ledger report for hotel : {hotel_aggregate.hotel_id}"
        )
        self.guest_ledger_repository.save_all(guest_ledger_dto.new_guest_ledgers)

    def create_guest_ledger_for_new_booking(
        self,
        base_currency,
        booking_aggregate,
        deposit_transferred_at_checkin,
        non_revenue_components,
        opening_balance_in_base_currency,
        payment_components,
        revenue_components,
        total_charges,
        total_payments,
        start_date,
        hotel_aggregate,
        charges_transferred_to_ar,
    ):
        opening_balance = opening_balance_in_base_currency
        total_payments = (
            (sum(total_payments)) if total_payments else Money(0, base_currency)
        )
        guest_ledger = GuestLedgerFactory.create_new_guest_ledger(
            booking_id=booking_aggregate.booking_id,
            total_charges=sum(total_charges),
            total_payments=total_payments,
            opening_balance_in_base_currency=opening_balance,
            closing_balance_in_base_currency=opening_balance_in_base_currency
            + total_payments
            + sum(total_charges)
            - sum(deposit_transferred_at_checkin)
            + (
                sum(charges_transferred_to_ar)
                if charges_transferred_to_ar
                else Money(0, base_currency)
            ),
            deposit_transferred_at_checkin=-(sum(deposit_transferred_at_checkin))
            if deposit_transferred_at_checkin
            else Money(0, base_currency),
            payment_components=self.as_dict(payment_components),
            revenue_components=self.as_dict(revenue_components),
            non_revenue_components=self.as_dict(non_revenue_components),
            report_date=start_date,
            hotel_id=hotel_aggregate.hotel_id,
            base_currency=base_currency,
            charges_transferred_to_ar=sum(charges_transferred_to_ar)
            if charges_transferred_to_ar
            else Money(0, base_currency),
        )
        return guest_ledger

    def create_guest_ledger_for_existing_booking(
        self,
        base_currency,
        booking_aggregate,
        deposit_transferred_at_checkin,
        latest_guest_ledger,
        non_revenue_components,
        payment_components,
        revenue_components,
        start_date,
        total_charges,
        total_payments,
        hotel_aggregate,
        charges_transferred_to_ar,
    ):
        total_payments = (
            (sum(total_payments)) if total_payments else Money(0, base_currency)
        )
        opening_balance = latest_guest_ledger.closing_balance_in_base_currency
        guest_ledger = GuestLedgerFactory.create_new_guest_ledger(
            booking_id=booking_aggregate.booking_id,
            total_charges=sum(total_charges),
            total_payments=total_payments,
            opening_balance_in_base_currency=latest_guest_ledger.closing_balance_in_base_currency,
            closing_balance_in_base_currency=opening_balance
            + total_payments
            + sum(total_charges)
            - sum(deposit_transferred_at_checkin)
            + (
                sum(charges_transferred_to_ar)
                if charges_transferred_to_ar
                else Money(0, base_currency)
            ),
            deposit_transferred_at_checkin=-(sum(deposit_transferred_at_checkin))
            if deposit_transferred_at_checkin
            else Money(0, base_currency),
            payment_components=self.as_dict(payment_components),
            revenue_components=self.as_dict(revenue_components),
            non_revenue_components=self.as_dict(non_revenue_components),
            report_date=start_date,
            hotel_id=hotel_aggregate.hotel_id,
            base_currency=base_currency,
            charges_transferred_to_ar=sum(charges_transferred_to_ar)
            if charges_transferred_to_ar
            else Money(0, base_currency),
        )
        return guest_ledger

    @staticmethod
    def as_dict(components):
        result = {}
        for key, value in components.items():
            result[key] = str(sum(value).amount) if value else 0

        return result

    def is_business_date_guest_ledger_report_generated(
        self, start_date, end_date, hotel_id
    ):
        guest_ledger_aggregate = self.guest_ledger_repository.load_for_date_range(
            start_date=dateutils.date_to_ymd_str(start_date),
            end_date=dateutils.date_to_ymd_str(end_date),
            hotel_id=hotel_id,
        )
        if guest_ledger_aggregate:
            return True
        return False
