import logging
from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.report.factories.deposit_ledger_factory import (
    DepositLedgerFactory,
)
from prometheus.domain.report.repositories.deposit_ledger_repository import (
    DepositLedgerRepository,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from ths_common.constants.billing_constants import PaymentStatus, PaymentTypes
from ths_common.constants.booking_constants import BookingStatus

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        ReportingApplicationService,
        DepositLedgerRepository,
    ]
)
class DepositLedgerService(object):
    def __init__(
        self,
        booking_repository,
        bill_repository,
        hotel_repository,
        reporting_application_service,
        deposit_ledger_repository,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.reporting_application_service = reporting_application_service
        self.deposit_ledger_repository = deposit_ledger_repository

    def _base_report_data(self, start_date, end_date, hotel_aggregate):
        booking_aggregates_for_date_range = (
            self.booking_repository.deposit_report_booking_query(
                dateutils.date_to_ymd_str(start_date),
                end_date=None,
                hotel_id=hotel_aggregate.hotel_id,
            )
        )

        cancelled_booking_aggregates_for_date_range = (
            self.booking_repository.deposit_report_cancelled_and_no_show_booking_query(
                dateutils.date_to_ymd_str(start_date),
                end_date=None,
                hotel_id=hotel_aggregate.hotel_id,
            )
        )

        bill_aggregates_for_payment_made_on_running_date = (
            self.bill_repository.trail_balance_payments_added_on_running_date(
                hotel_aggregate.hotel_id,
                dateutils.date_to_ymd_str(start_date),
                dateutils.date_to_ymd_str(end_date),
            )
        )
        booking_aggregates_for_payment_made_on_running_date = self.booking_repository.load_for_bill_ids_with_yield_per(
            [
                bill_aggregate.bill_id
                for bill_aggregate in bill_aggregates_for_payment_made_on_running_date
            ]
        )

        booking_aggregates = list(
            set(
                booking_aggregates_for_date_range
                + cancelled_booking_aggregates_for_date_range
                + booking_aggregates_for_payment_made_on_running_date
            )
        )

        hotel_map = {hotel_aggregate.hotel_id: hotel_aggregate}

        return booking_aggregates, hotel_map

    def generate_summary(self, start_date, end_date, hotel_aggregate):
        if self.is_business_date_deposit_ledger_report_generated(
            start_date, end_date, hotel_aggregate.hotel_id
        ):
            return
        booking_aggregates, hotel_map = self._base_report_data(
            start_date, end_date, hotel_aggregate
        )

        booking_aggregates = self.get_unique_bookings(booking_aggregates)

        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids, load_invoices_by_bill_id=True, hotel_map=hotel_map
        )

        base_currency = hotel_aggregate.hotel.base_currency

        deposit_ledger_aggregate = self.deposit_ledger_repository.load_for_date_range(
            start_date=dateutils.subtract(start_date, days=1),
            end_date=dateutils.subtract(end_date, days=1),
            hotel_id=hotel_aggregate.hotel_id,
        )

        deposit_transferred_at_checkin = []
        total_payments = []
        total_charges = []
        revenue_components = defaultdict(list)
        non_revenue_components = defaultdict(list)
        payment_components = defaultdict(list)
        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            for payment in bill_aggregate.payments:
                if (
                    payment.status == PaymentStatus.POSTED
                    and not payment.paid_to == 'company'
                ):
                    if start_date <= dateutils.to_date(
                        payment.date_of_payment
                    ) <= end_date and booking_aggregate.booking.status in [
                        BookingStatus.RESERVED,
                        BookingStatus.TEMPORARY,
                        BookingStatus.CONFIRMED,
                        BookingStatus.CANCELLED,
                        BookingStatus.NOSHOW,
                    ]:
                        if payment.payment_type == PaymentTypes.PAYMENT:
                            do_payment_mode = f"do_{payment.payment_mode}"
                            payment_components[do_payment_mode].append(-payment.amount)
                            total_payments.append(-payment.amount)
                        elif payment.payment_type == PaymentTypes.REFUND:
                            do_payment_mode = f"do_{payment.payment_mode}"
                            payment_components[do_payment_mode].append(payment.amount)
                            total_payments.append(payment.amount)
                    else:
                        checkin_date = (
                            dateutils.to_date(
                                booking_aggregate.booking.actual_checkin_date
                            )
                            if booking_aggregate.booking.actual_checkin_date
                            else dateutils.to_date(
                                booking_aggregate.booking.checkin_date
                            )
                        )
                        if dateutils.to_date(
                            payment.date_of_payment
                        ) < checkin_date == start_date and (
                            booking_aggregate.booking.status
                            in [
                                BookingStatus.CHECKED_IN,
                                BookingStatus.PART_CHECKIN,
                                BookingStatus.CHECKED_OUT,
                            ]
                        ):
                            if payment.payment_type == PaymentTypes.PAYMENT:
                                deposit_transferred_at_checkin.append(payment.amount)
                            elif payment.payment_type == PaymentTypes.REFUND:
                                deposit_transferred_at_checkin.append(-payment.amount)

        opening_balance_in_base_currency = Money(0, base_currency)
        if deposit_ledger_aggregate and deposit_ledger_aggregate[0].deposit_ledger:
            deposit_ledger = self.update_deposit_ledger(
                base_currency,
                deposit_ledger_aggregate,
                deposit_transferred_at_checkin,
                hotel_aggregate,
                non_revenue_components,
                payment_components,
                revenue_components,
                start_date,
                total_charges,
                total_payments,
            )
        else:
            deposit_ledger = self.create_new_deposit_ledger(
                base_currency,
                deposit_transferred_at_checkin,
                hotel_aggregate,
                non_revenue_components,
                opening_balance_in_base_currency,
                payment_components,
                revenue_components,
                start_date,
                total_charges,
                total_payments,
            )

        logger.info(
            f"Successfully generated deposit ledger for hotel: {hotel_aggregate.hotel_id}"
        )
        self.deposit_ledger_repository.save(deposit_ledger)

    def create_new_deposit_ledger(
        self,
        base_currency,
        deposit_transferred_at_checkin,
        hotel_aggregate,
        non_revenue_components,
        opening_balance_in_base_currency,
        payment_components,
        revenue_components,
        start_date,
        total_charges,
        total_payments,
    ):
        opening_balance = opening_balance_in_base_currency
        deposit_ledger = DepositLedgerFactory.create_new_deposit_ledger(
            total_charges=sum(total_charges),
            total_payments=(sum(total_payments)),
            opening_balance_in_base_currency=opening_balance,
            closing_balance_in_base_currency=opening_balance
            + sum(total_charges)
            + (sum(total_payments))
            + sum(deposit_transferred_at_checkin),
            deposit_transferred_at_checkin=(sum(deposit_transferred_at_checkin)),
            hotel_id=hotel_aggregate.hotel_id,
            payment_components=self.as_dict(payment_components),
            revenue_components=self.as_dict(revenue_components),
            non_revenue_components=self.as_dict(non_revenue_components),
            base_currency=base_currency,
            report_date=start_date,
        )
        return deposit_ledger

    def update_deposit_ledger(
        self,
        base_currency,
        deposit_ledger_aggregate,
        deposit_transferred_at_checkin,
        hotel_aggregate,
        non_revenue_components,
        payment_components,
        revenue_components,
        start_date,
        total_charges,
        total_payments,
    ):
        opening_balance = deposit_ledger_aggregate[0].closing_balance_in_base_currency
        deposit_ledger = DepositLedgerFactory.create_new_deposit_ledger(
            total_charges=sum(total_charges),
            total_payments=(sum(total_payments)),
            opening_balance_in_base_currency=opening_balance,
            closing_balance_in_base_currency=opening_balance
            + sum(total_charges)
            + (sum(total_payments))
            + sum(deposit_transferred_at_checkin),
            deposit_transferred_at_checkin=(sum(deposit_transferred_at_checkin)),
            hotel_id=hotel_aggregate.hotel_id,
            payment_components=self.as_dict(payment_components),
            revenue_components=self.as_dict(revenue_components),
            non_revenue_components=self.as_dict(non_revenue_components),
            base_currency=base_currency,
            report_date=start_date,
        )
        return deposit_ledger

    @staticmethod
    def as_dict(components):
        result = {}
        for key, value in components.items():
            result[key] = str(sum(value).amount) if value else 0

        return result

    def is_business_date_deposit_ledger_report_generated(
        self, start_date, end_date, hotel_id
    ):
        deposit_ledger_aggregate = self.deposit_ledger_repository.load_for_date_range(
            start_date=dateutils.date_to_ymd_str(start_date),
            end_date=dateutils.date_to_ymd_str(end_date),
            hotel_id=hotel_id,
        )
        if deposit_ledger_aggregate:
            return True
        return False

    @staticmethod
    def get_unique_bookings(booking_aggregates):
        unique_booking_aggregates = []
        booking_ids = []
        for booking_aggregate in booking_aggregates:
            if booking_aggregate.booking_id not in booking_ids:
                booking_ids.append(booking_aggregate.booking_id)
                unique_booking_aggregates.append(booking_aggregate)
        return unique_booking_aggregates
