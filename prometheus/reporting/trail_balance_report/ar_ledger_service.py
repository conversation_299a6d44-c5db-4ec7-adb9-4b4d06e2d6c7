import logging
from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.report.factories.ar_ledger_factory import ARLedgerFactory
from prometheus.domain.report.repositories.ar_ledger_repository import (
    ARLedgerRepository,
)
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from prometheus.reporting.trail_balance_report.external_clients.account_receivables_client import (
    AccountReceivableServiceClient,
)
from ths_common.constants.billing_constants import ChargeStatus, ChargeTypes
from ths_common.constants.booking_constants import BookingStatus

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        ReportingApplicationService,
        ARLedgerRepository,
        AccountReceivableServiceClient,
        CatalogServiceClient,
    ]
)
class AccountReceivableLedgerService(object):
    def __init__(
        self,
        booking_repository,
        bill_repository,
        hotel_repository,
        reporting_application_service,
        ar_ledger_repository,
        ar_service_client,
        catalog_service_client,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.reporting_application_service = reporting_application_service
        self.ar_ledger_repository = ar_ledger_repository
        self.ar_service_client = ar_service_client
        self.catalog_service_client = catalog_service_client

    def _base_report_data(self, start_date, end_date, hotel_aggregate, booking_status):
        booking_aggregates_for_date_range = (
            self.booking_repository.trail_balance_checked_out_booking_query(
                dateutils.date_to_ymd_str(start_date),
                end_date=None,
                hotel_id=hotel_aggregate.hotel_id,
                booking_status=booking_status,
            )
        )

        hotel_map = {hotel_aggregate.hotel_id: hotel_aggregate}

        return booking_aggregates_for_date_range, hotel_map

    def generate_summary(
        self, start_date, end_date, hotel_aggregate, bookings_debited_in_ar
    ):
        if self.is_business_date_ar_ledger_report_generated(
            start_date, end_date, hotel_aggregate.hotel_id
        ):
            return
        booking_aggregates, hotel_map = self._base_report_data(
            start_date,
            end_date,
            hotel_aggregate,
            booking_status=[
                BookingStatus.CHECKED_OUT.value,
                BookingStatus.PART_CHECKOUT.value,
            ],
        )

        booking_aggregates += bookings_debited_in_ar
        booking_ids = []
        unique_booking_aggregates = []
        for booking_aggregate in booking_aggregates:
            if booking_aggregate.booking_id not in booking_ids:
                booking_ids.append(booking_aggregate.booking_id)
                unique_booking_aggregates.append(booking_aggregate)

        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in unique_booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids, load_invoices_by_bill_id=True, hotel_map=hotel_map
        )

        base_currency = hotel_aggregate.hotel.base_currency

        ar_collection_summary = self.ar_service_client.get_collection_report(
            hotel_id=hotel_aggregate.hotel_id,
            start_date=dateutils.date_to_ymd_str(start_date),
            end_date=dateutils.date_to_ymd_str(end_date),
        )
        payment_components, total_payments = self.extract_payment_details(
            ar_collection_summary, base_currency
        )

        ar_ledger_aggregate = self.ar_ledger_repository.load_for_date_range(
            start_date=dateutils.subtract(start_date, days=1),
            end_date=dateutils.subtract(end_date, days=1),
            hotel_id=hotel_aggregate.hotel_id,
        )

        revenue_components = defaultdict(list)
        non_revenue_components = defaultdict(list)
        total_charges = []
        for booking_aggregate in unique_booking_aggregates:
            checkout_date = (
                dateutils.to_date(booking_aggregate.booking.actual_checkout_date)
                if booking_aggregate.booking.actual_checkout_date
                else (dateutils.to_date(booking_aggregate.booking.checkout_date))
            )
            if checkout_date > start_date:
                continue
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            for charge in bill_aggregate.charges:
                for charge_split in charge.charge_splits:
                    if (
                        charge_split.charge_type == ChargeTypes.CREDIT
                        and charge.status == ChargeStatus.CONSUMED
                    ):
                        total_charges.append(
                            charge_split.get_posttax_amount_post_allowance()
                        )

                        revenue_components[charge.item.sku_category_id].append(
                            charge_split.get_pretax_amount_post_allowance()
                        )
                        for tax in charge_split.tax_details:
                            non_revenue_components[tax.tax_type].append(tax.amount)

                        applicable_allowances = [
                            allowance
                            for allowance in charge_split.allowances
                            if allowance.is_active
                        ]
                        for allowance in applicable_allowances:
                            for tax in allowance.tax_details or []:
                                non_revenue_components[tax.tax_type].append(-tax.amount)

        # Adding manual debits
        manual_debits = self.ar_service_client.get_debits(
            hotel_id=hotel_aggregate.hotel_id,
            from_date=start_date,
            to_date=end_date,
            only_manual_debits=True,
        )
        for manual_debit in manual_debits:
            total_charges.append(
                Money(manual_debit['debit_amount']['posttax_amount'], base_currency)
            )

        opening_balance_in_base_currency = Money(0, base_currency)
        if ar_ledger_aggregate and ar_ledger_aggregate[0].ar_ledger:
            ar_ledger = self.update_ar_ledger(
                ar_ledger_aggregate,
                base_currency,
                hotel_aggregate,
                non_revenue_components,
                payment_components,
                revenue_components,
                start_date,
                total_charges,
                total_payments,
            )

        else:
            ar_ledger = self.create_new_ar_ledger(
                base_currency,
                hotel_aggregate,
                non_revenue_components,
                opening_balance_in_base_currency,
                payment_components,
                revenue_components,
                start_date,
                total_charges,
                total_payments,
            )

        logger.info(
            f"Successfully generated ar ledger for hotel: {hotel_aggregate.hotel_id}"
        )
        self.ar_ledger_repository.save(ar_ledger)

    def update_ar_ledger(
        self,
        ar_ledger_aggregate,
        base_currency,
        hotel_aggregate,
        non_revenue_components,
        payment_components,
        revenue_components,
        start_date,
        total_charges,
        total_payments,
    ):
        ar_ledger = ARLedgerFactory.create_new_ar_ledger(
            hotel_id=hotel_aggregate.hotel_id,
            total_charges=sum(total_charges),
            total_payments=total_payments,
            opening_balance_in_base_currency=ar_ledger_aggregate[
                0
            ].ar_ledger.closing_balance_in_base_currency,
            closing_balance_in_base_currency=ar_ledger_aggregate[
                0
            ].ar_ledger.closing_balance_in_base_currency
            + sum(total_charges)
            + total_payments,
            base_currency=base_currency,
            payment_components=self.as_dict(payment_components),
            revenue_components=self.as_dict(revenue_components),
            non_revenue_components=self.as_dict(non_revenue_components),
            report_date=start_date,
        )
        return ar_ledger

    def create_new_ar_ledger(
        self,
        base_currency,
        hotel_aggregate,
        non_revenue_components,
        opening_balance_in_base_currency,
        payment_components,
        revenue_components,
        start_date,
        total_charges,
        total_payments,
    ):
        opening_balance = opening_balance_in_base_currency
        ar_ledger = ARLedgerFactory.create_new_ar_ledger(
            hotel_id=hotel_aggregate.hotel_id,
            total_charges=sum(total_charges),
            total_payments=total_payments,
            opening_balance_in_base_currency=opening_balance,
            closing_balance_in_base_currency=opening_balance_in_base_currency
            + sum(total_charges)
            + total_payments,
            base_currency=base_currency,
            payment_components=self.as_dict(payment_components),
            revenue_components=self.as_dict(revenue_components),
            non_revenue_components=self.as_dict(non_revenue_components),
            report_date=start_date,
        )
        return ar_ledger

    @staticmethod
    def extract_payment_details(ar_collection_summary, base_currency):
        total_payments = []
        payments = defaultdict(list)
        for payment in ar_collection_summary['data']['payments']:
            total_payments.append(-Money(payment['amount'], base_currency))
            payment_mode = f"ar_{payment['payment_mode']}"
            payments[payment_mode.lower()].append(
                -Money(payment['amount'], base_currency)
            )
        return payments, sum(total_payments)

    @staticmethod
    def as_dict(components):
        result = {}
        for key, value in components.items():
            result[key] = str(sum(value).amount) if value else 0
        return result

    def is_business_date_ar_ledger_report_generated(
        self, start_date, end_date, hotel_id
    ):
        ar_ledger_aggregate = self.ar_ledger_repository.load_for_date_range(
            start_date=dateutils.date_to_ymd_str(start_date),
            end_date=dateutils.date_to_ymd_str(end_date),
            hotel_id=hotel_id,
        )
        if ar_ledger_aggregate:
            return True
        return False
