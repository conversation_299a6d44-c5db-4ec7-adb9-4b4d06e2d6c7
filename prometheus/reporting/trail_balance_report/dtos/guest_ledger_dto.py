from collections import defaultdict

from treebo_commons.utils import dateutils

from ths_common.constants.billing_constants import (
    ChargeStatus,
    ChargeTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingStatus


class GuestLedgerDTO:
    def __init__(self):
        self.booking_ids = []
        self.unique_booking_aggregates = []
        self.new_guest_ledgers = []
        self.bill_ids = {}
        self.booking_id_to_guest_ledger_aggregate_mapping = defaultdict(list)

        self.total_payments = []
        self.total_charges = []
        self.charges_transferred_to_ar = []
        self.revenue_components = defaultdict(list)
        self.non_revenue_components = defaultdict(list)
        self.payment_components = defaultdict(list)
        self.deposit_transferred_at_checkin = []

    def get_unique_bookings(self, booking_aggregates):
        for booking_aggregate in booking_aggregates:
            if booking_aggregate.booking_id not in self.booking_ids:
                self.booking_ids.append(booking_aggregate.booking_id)
                self.unique_booking_aggregates.append(booking_aggregate)
        self.bill_ids = {
            booking_aggregate.bill_id
            for booking_aggregate in self.unique_booking_aggregates
        }
        return self.bill_ids, self.booking_ids, self.unique_booking_aggregates

    def booking_guest_ledger_aggregate_mapping(self, guest_ledger_aggregates):
        for guest_ledger_aggregate in guest_ledger_aggregates:
            self.booking_id_to_guest_ledger_aggregate_mapping[
                guest_ledger_aggregate.booking_id
            ].append(guest_ledger_aggregate.guest_ledger)
        return self.booking_id_to_guest_ledger_aggregate_mapping

    def get_latest_guest_ledger(self, booking_id):
        guest_ledgers = self.booking_id_to_guest_ledger_aggregate_mapping.get(
            booking_id
        )
        sorted_guest_ledgers = (
            sorted(guest_ledgers, key=lambda gl: gl.created_at, reverse=True)
            if guest_ledgers
            else None
        )
        return sorted_guest_ledgers[0] if sorted_guest_ledgers else None

    # Incorporate those allowance which is not yet incorporated and lie between last report date and running date
    def is_allowance_applicable(self, booking_aggregate, allowance, start_date):
        latest_guest_ledger = self.get_latest_guest_ledger(booking_aggregate.booking_id)
        if (
            allowance.is_active
            and allowance.posting_date
            and dateutils.to_date(allowance.posting_date) <= start_date
        ):
            return (
                dateutils.to_date(allowance.posting_date)
                > latest_guest_ledger.report_date
                if latest_guest_ledger
                else True
            )
        return False

    def incorporate_allowances_added_to_posted_charges(
        self, charge, charge_split, start_date, booking_aggregate
    ):
        applicable_allowances = [
            allowance
            for allowance in charge_split.allowances
            if self.is_allowance_applicable(booking_aggregate, allowance, start_date)
        ]

        allowance_pretax_amount = sum(
            [allowance.pretax_amount for allowance in applicable_allowances]
        )

        allowance_posttax_amount = sum(
            [allowance.posttax_amount for allowance in applicable_allowances]
        )

        if applicable_allowances:
            self.revenue_components[charge.item.sku_category_id].append(
                -allowance_pretax_amount
            )
            for allowance in applicable_allowances:
                for tax in allowance.tax_details or []:
                    self.non_revenue_components[tax.tax_type].append(-tax.amount)
            self.total_charges.append(-allowance_posttax_amount)

    def handle_credit_booking_checked_out_on_running_date(
        self, charge, charge_split, start_date, booking_aggregate
    ):
        if (
            booking_aggregate.booking.status == BookingStatus.CHECKED_OUT
            and charge_split.charge_type == ChargeTypes.CREDIT
            and (
                dateutils.to_date(booking_aggregate.booking.actual_checkout_date)
                == start_date
            )
        ):
            self.charges_transferred_to_ar.append(
                -charge_split.get_posttax_amount_post_allowance()
            )
            self.revenue_components[charge.item.sku_category_id].append(
                -charge_split.pre_tax
            )
            for tax in charge_split.tax_details or []:
                self.non_revenue_components[tax.tax_type].append(-tax.amount)

    @staticmethod
    def credit_booking_checked_out_earlier(charge_split, start_date, booking_aggregate):
        if (
            booking_aggregate.booking.status == BookingStatus.CHECKED_OUT
            and charge_split.charge_type == ChargeTypes.CREDIT
            and (
                dateutils.to_date(booking_aggregate.booking.actual_checkout_date)
                > start_date
                and dateutils.to_date(booking_aggregate.booking.created_at) > start_date
            )
        ):
            return True
        return False

    def charge_posting_date_less_than_latest_ledger_report_date(
        self, charge, booking_aggregate
    ):
        latest_guest_ledger = self.get_latest_guest_ledger(booking_aggregate.booking_id)
        if (
            latest_guest_ledger
            and dateutils.to_date(charge.posting_date)
            <= latest_guest_ledger.report_date
        ):
            return True
        return False

    def calculate_charge_components(
        self, booking_aggregate, bill_aggregate, start_date, end_date
    ):
        for charge in bill_aggregate.charges:
            for charge_split in charge.charge_splits:
                if charge.status == ChargeStatus.CONSUMED:
                    self.incorporate_allowances_added_to_posted_charges(
                        charge, charge_split, start_date, booking_aggregate
                    )

                    if self.credit_booking_checked_out_earlier(
                        charge_split, start_date, booking_aggregate
                    ):
                        continue

                    if self.charge_posting_date_less_than_latest_ledger_report_date(
                        charge, booking_aggregate
                    ):
                        checkout_date = (
                            booking_aggregate.booking.actual_checkout_date
                            or booking_aggregate.booking.checkout_date
                        )
                        if (
                            booking_aggregate.booking.status
                            == BookingStatus.CHECKED_OUT
                            and (charge_split.charge_type == ChargeTypes.CREDIT)
                            and (start_date == dateutils.to_date(checkout_date))
                        ):
                            self.charges_transferred_to_ar.append(
                                -charge_split.get_posttax_amount_post_allowance()
                            )
                        continue

                    if dateutils.to_date(charge.posting_date) > end_date:
                        continue

                    self.handle_credit_booking_checked_out_on_running_date(
                        charge, charge_split, start_date, booking_aggregate
                    )

                    self.total_charges.append(charge_split.post_tax)
                    self.revenue_components[charge.item.sku_category_id].append(
                        charge_split.pre_tax
                    )
                    for tax in charge_split.tax_details or []:
                        self.non_revenue_components[tax.tax_type].append(tax.amount)

    def calculate_deposit_transfers_at_check_in(
        self, payment, start_date, booking_aggregate
    ):
        checkin_date = (
            dateutils.to_date(booking_aggregate.booking.actual_checkin_date)
            if booking_aggregate.booking.actual_checkin_date
            else dateutils.to_date(booking_aggregate.booking.checkin_date)
        )

        if (
            dateutils.to_date(payment.date_of_payment) < checkin_date == start_date
            and payment.status == PaymentStatus.POSTED
        ):
            if payment.payment_type == PaymentTypes.PAYMENT:
                self.deposit_transferred_at_checkin.append(payment.amount)
            elif payment.payment_type == PaymentTypes.REFUND:
                self.deposit_transferred_at_checkin.append(-payment.amount)

    def calculate_payments_made_on_running_date(self, payment, start_date, end_date):
        if start_date <= dateutils.to_date(payment.date_of_payment) <= end_date:
            fo_payment_mode = f"fo_{payment.payment_mode}"
            if payment.status == PaymentStatus.CANCELLED:
                return
            elif payment.payment_type == PaymentTypes.REFUND:
                self.payment_components[fo_payment_mode].append(payment.amount)
                self.total_payments.append(payment.amount)
            elif payment.payment_type == PaymentTypes.PAYMENT:
                self.payment_components[fo_payment_mode].append(-payment.amount)
                self.total_payments.append(-payment.amount)

    def calculate_payment_components(
        self, booking_aggregate, bill_aggregate, start_date, end_date
    ):
        for payment in bill_aggregate.payments:
            if payment.status == PaymentStatus.CANCELLED and (
                dateutils.to_date(payment.date_of_payment)
                == dateutils.to_date(payment.modified_at)
            ):
                continue

            self.calculate_deposit_transfers_at_check_in(
                payment, start_date, booking_aggregate
            )

            self.calculate_payments_made_on_running_date(payment, start_date, end_date)

    def reset_ledger_variables(self):
        self.total_payments = []
        self.total_charges = []
        self.charges_transferred_to_ar = []
        self.revenue_components = defaultdict(list)
        self.non_revenue_components = defaultdict(list)
        self.payment_components = defaultdict(list)
        self.deposit_transferred_at_checkin = []
