import logging
from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.report.factories.ar_ledger_factory_v2 import ARLedgerFactoryV2
from prometheus.domain.report.repositories.ar_ledger_repository_v2 import (
    ARLedgerRepositoryV2,
)
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from prometheus.reporting.trail_balance_report.external_clients.account_receivables_client import (
    AccountReceivableServiceClient,
)

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        ReportingApplicationService,
        ARLedgerRepositoryV2,
        AccountReceivableServiceClient,
        CatalogServiceClient,
    ]
)
class AccountReceivableLedgerServiceV2(object):
    def __init__(
        self,
        booking_repository,
        bill_repository,
        hotel_repository,
        reporting_application_service,
        ar_ledger_repository_v2,
        ar_service_client,
        catalog_service_client,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.reporting_application_service = reporting_application_service
        self.ar_ledger_repository = ar_ledger_repository_v2
        self.ar_service_client = ar_service_client
        self.catalog_service_client = catalog_service_client

    def generate_summary(self, business_date, charges_transfer_to_ar, hotel_aggregate):
        if self.is_business_date_ar_ledger_report_generated(
            business_date, hotel_aggregate.hotel_id
        ):
            return
        base_currency = hotel_aggregate.hotel.base_currency

        ar_collection_summary = self.ar_service_client.get_collection_report(
            hotel_id=hotel_aggregate.hotel_id,
            start_date=dateutils.date_to_ymd_str(business_date),
            end_date=dateutils.date_to_ymd_str(business_date),
        )
        payment_components, total_payments = self.extract_payment_details(
            ar_collection_summary, base_currency
        )

        total_charges = [charges_transfer_to_ar]
        manual_debits = self.ar_service_client.get_debits(
            hotel_id=hotel_aggregate.hotel_id,
            from_date=business_date,
            to_date=business_date,
            only_manual_debits=True,
        )
        for manual_debit in manual_debits:
            total_charges.append(
                Money(manual_debit['debit_amount']['posttax_amount'], base_currency)
            )

        previous_day_ar_ledger_aggregate = (
            self.ar_ledger_repository.load_for_date_range(
                start_date=dateutils.subtract(business_date, days=1),
                end_date=dateutils.subtract(business_date, days=1),
                hotel_id=hotel_aggregate.hotel_id,
            )
        )
        opening_balance_in_base_currency = (
            previous_day_ar_ledger_aggregate[0].closing_balance_in_base_currency
            if previous_day_ar_ledger_aggregate
            else Money(0, base_currency)
        )
        ar_ledger = self.create_new_ar_ledger(
            base_currency,
            hotel_aggregate,
            opening_balance_in_base_currency,
            payment_components,
            business_date,
            total_charges,
            total_payments,
        )

        logger.info(
            f"Successfully generated ar ledger for hotel: {hotel_aggregate.hotel_id}"
        )
        self.ar_ledger_repository.save(ar_ledger)

    def create_new_ar_ledger(
        self,
        base_currency,
        hotel_aggregate,
        opening_balance_in_base_currency,
        payment_components,
        business_date,
        total_charges,
        total_payments,
    ):
        opening_balance = opening_balance_in_base_currency
        ar_ledger = ARLedgerFactoryV2.create_new_ar_ledger(
            hotel_id=hotel_aggregate.hotel_id,
            total_charges=sum(total_charges),
            total_payments=total_payments,
            opening_balance_in_base_currency=opening_balance,
            closing_balance_in_base_currency=opening_balance_in_base_currency
            + sum(total_charges)
            + total_payments,
            base_currency=base_currency,
            payment_components=self.as_dict(payment_components),
            revenue_components=self.as_dict(defaultdict(list)),
            non_revenue_components=self.as_dict(defaultdict(list)),
            business_date=business_date,
        )
        return ar_ledger

    @staticmethod
    def extract_payment_details(ar_collection_summary, base_currency):
        total_payments = []
        payments = defaultdict(list)
        for payment in ar_collection_summary['data']['payments']:
            total_payments.append(-Money(payment['amount'], base_currency))
            payment_mode = f"ar_{payment['payment_mode']}"
            payments[payment_mode.lower()].append(
                -Money(payment['amount'], base_currency)
            )
        return payments, sum(total_payments)

    @staticmethod
    def as_dict(components):
        result = {}
        for key, value in components.items():
            result[key] = str(sum(value).amount) if value else 0
        return result

    def is_business_date_ar_ledger_report_generated(self, business_date, hotel_id):
        ar_ledger_aggregate = self.ar_ledger_repository.load_for_date_range(
            start_date=dateutils.date_to_ymd_str(business_date),
            end_date=dateutils.date_to_ymd_str(business_date),
            hotel_id=hotel_id,
        )
        if ar_ledger_aggregate:
            return True
        return False
