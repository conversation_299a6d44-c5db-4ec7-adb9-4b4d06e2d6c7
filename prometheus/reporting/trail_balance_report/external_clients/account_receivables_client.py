from flask import current_app as app

from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from ths_common.constants.user_constants import UserType


@register_instance()
class AccountReceivableServiceClient(BaseExternalClient):
    def __init__(self):
        super().__init__(timeout=3000)

    page_map = {
        "get_collection_reports_credit_date": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/ar/v1/reports/collections-reports/credit-date?start_date={start_date}&"
            "end_date={end_date}&hotel_id={hotel_id}",
        ),
        "get_debits": dict(
            type=BaseExternalClient.CallTypes.GET, url_regex="/ar/v1/debits"
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_account_receivable_service_url()

    def get_headers(self):
        headers = BaseExternalClient.get_headers()
        headers['X-User-Type'] = UserType.BACKEND_SYSTEM.value
        return headers

    def get_collection_report(self, hotel_id, start_date, end_date):
        page_name = "get_collection_reports_credit_date"
        url_params = dict(hotel_id=hotel_id, start_date=start_date, end_date=end_date)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            # todo: should consumer this exception silently?
            raise Exception(
                "AR API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response

    def get_debits(
        self,
        hotel_id,
        only_manual_debits=False,
        debit_date=None,
        from_date=None,
        to_date=None,
    ):
        page_name = "get_debits"
        optional_url_params = dict()
        optional_url_params['hotel_id'] = hotel_id
        optional_url_params['only_manual_debits'] = only_manual_debits
        if debit_date:
            optional_url_params['debit_date'] = debit_date
        else:
            optional_url_params['from_date'] = from_date
            optional_url_params['to_date'] = to_date

        response = self.make_call(
            page_name=page_name, optional_url_params=optional_url_params
        )
        if not response.is_success():
            raise Exception(
                "AR API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response['data']['debits']
