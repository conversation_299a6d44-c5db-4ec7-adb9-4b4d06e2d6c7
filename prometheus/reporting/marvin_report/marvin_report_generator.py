import os
import uuid

from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.marvin_report.marvin_report_aggregate import (
    MarvinReportAggregate,
)
from ths_common.constants.booking_constants import BookingStatus, ExpenseTypes


class MarvinReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows for a given booking
    """

    REPORT_COLUMNS = [
        'hotel_id',
        'hotel_name',
        'booking_id',
        'bill_id',
        'reference_number',
        'room_stay_id',
        'room_stay_status',
        'guest_names',
        'adult_count',
        'child_count',
        'channel_code',
        'subchannel_code',
        'corporate_name',
        'room_number',
        'room_type',
        'guest_count',
        'invoice_numbers',
        'invoicing_model',
        'checkin_date',
        'checkout_date',
        'total_room_nights',
        'pretax_room_rent',
        'cgst_room_rent',
        'sgst_room_rent',
        'igst_room_rent',
        'flood_cess_room_rent',
        'total_lodging_taxes',
        'room_related_charges',
        'per_night_room_charges',
        'total_booking_amount',
        'paid_to_hotel',
        'paid_to_treebo',
        'payment_mode_cc_treebo',
        'payment_mode_cc_hotel',
        'payment_mode_dc_treebo',
        'payment_mode_dc_hotel',
        'payment_mode_bank_transfer_treebo',
        'payment_mode_bank_transfer_hotel',
        'payments',
        'non_food_expenses',
        'food_charge_pretax',
        'food_charge_tax_amount',
        'food_charge_posttax',
        'food_charge_added_by_treebo_pretax',
        'food_charge_added_by_treebo_tax_amount',
        'total_credit_charges',
        'total_non_credit_charges',
        'proration_amount',
    ]
    EVENT_TYPE = 'marvin_report'
    ROUTING_KEY = 'reporting.marvin'
    MARVIN_REPORT_FOLDER_NAME = 'marvin_reports/'

    def __init__(
        self,
        expense_items,
        booking_aggregate,
        bill_aggregate,
        invoice_aggregates,
        hotel_aggregate,
        room_type_map,
    ):
        self.expense_items = expense_items
        self.hotel_aggregate = hotel_aggregate
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.invoice_aggregates = invoice_aggregates
        self.room_type_map = room_type_map

    def generate(self):
        expense_item_ids = [
            expense_item.expense_item_id
            for expense_item in self.expense_items
            if expense_item.sku_category_id != ExpenseTypes.FOOD.value
        ]
        active_roomstays = (
            rs
            for rs in self.booking_aggregate.get_active_room_stays(as_generator=True)
            if rs.status
            in {
                BookingStatus.CHECKED_OUT,
                BookingStatus.PART_CHECKOUT,
                BookingStatus.CHECKED_IN,
                BookingStatus.PART_CHECKIN,
                BookingStatus.RESERVED,
            }
        )
        return [
            MarvinReportAggregate(
                self.booking_aggregate,
                self.bill_aggregate,
                self.hotel_aggregate,
                room_stay,
                self.invoice_aggregates,
                expense_item_ids,
                self.room_type_map,
            )
            for room_stay in active_roomstays
        ]

    @staticmethod
    def generate_marvin_report_file_name(extension='csv', identifier=None):
        if identifier is None:
            identifier = str(uuid.uuid4())

        file_name = f"marvin-report-{identifier}.{extension}"
        return (
            os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/reports/") + file_name
        )
