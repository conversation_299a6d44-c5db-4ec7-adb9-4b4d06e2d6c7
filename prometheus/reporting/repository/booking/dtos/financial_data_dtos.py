class FinancialDataBookingDetailsDto:
    def __init__(self, booking_data):
        self.bill_id = getattr(booking_data, 'bill_id', None)
        self.booking_id = getattr(booking_data, 'booking_id', None)
        self.booking_reference_number = getattr(booking_data, 'reference_number', None)
        self.travel_agent_external_reference_id = getattr(
            booking_data, 'travel_agent_id', None
        )
        self.company_external_reference_id = getattr(booking_data, 'company_id', None)
        self.room_number = getattr(booking_data, 'room_no', None)
        self.guest_name = getattr(booking_data, 'guest_name', 'Unknown')
        self.checkin_date = getattr(booking_data, 'checkin_date', None)
        self.checkout_date = getattr(booking_data, 'checkout_date', None)
        self.actual_checkin_date = getattr(booking_data, 'actual_checkin_date', None)
        self.actual_checkout_date = getattr(booking_data, 'actual_checkout_date', None)
