import json

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from prometheus.reporting.finance_erp_reporting.constants import (
    OTA_COMMISSION_API_BATCH_SIZE,
)
from ths_common.utils.collectionutils import chunks
from ths_common.utils.common_utils import json_dumps


@register_instance()
class TenantGatewayServiceClient(BaseExternalClient):
    def __init__(self):
        super().__init__(timeout=3000)

    page_map = {
        'fetch_ota_commissions_bulk': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex='/tenant-gateway/v1/su/ota-commissions-bulk',
        )
    }

    def get_domain(self):
        return ServiceRegistryClient.get_tenant_gateway_service_url()

    def fetch_ota_commission_percent(self, booking_aggregates):
        page_name = "fetch_ota_commissions_bulk"
        commission_map = {}
        for booking_aggregate_chunk in chunks(
            booking_aggregates, OTA_COMMISSION_API_BATCH_SIZE
        ):
            ota_data = [
                dict(
                    hotel_code=booking_aggregate.booking.hotel_id,
                    ota_code=booking_aggregate.booking.source.subchannel_code,
                    start_date=booking_aggregate.booking.created_at,
                    end_date=booking_aggregate.booking.created_at,
                )
                for booking_aggregate in booking_aggregate_chunk
            ]
            request_data = json.loads(json_dumps(dict(data=dict(ota_data=ota_data))))
            response = self.make_call(page_name, data=request_data)
            if not response.is_success():
                raise Exception(
                    "Tenant Gateway API Error. Status Code: {0}, Errors: {1}".format(
                        response.response_code, response.errors
                    )
                )
            for commission_data in response.data:
                commission_map[
                    (
                        commission_data['internal_hotel_id'],
                        commission_data['ota_code'],
                        dateutils.localize_datetime(
                            dateutils.isoformat_str_to_datetime(
                                commission_data['start_date']
                            )
                        ),
                        dateutils.localize_datetime(
                            dateutils.isoformat_str_to_datetime(
                                commission_data['end_date']
                            )
                        ),
                    )
                ] = commission_data['commission']

        return commission_map
