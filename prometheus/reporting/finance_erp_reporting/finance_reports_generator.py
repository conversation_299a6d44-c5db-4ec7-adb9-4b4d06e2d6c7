from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.finance_erp_reporting.external_finance_reports.customer_invoice_report.customer_invoice_report_generator import (
    CustomerInvoiceReportGenerator,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.marketplace_purchase_invoice_report.marketplace_purchase_invoice_report_generator import (
    MarketPlacePurchaseInvoiceReportGenerator,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.ota_commission_report.ota_commission_report_generator import (
    OtaCommissionReportGenerator,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.payment_gateway_report.payment_gateway_report_generator import (
    PaymentDataReportGenerator,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_report_generator import (
    SettlementReportGenerator,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.ta_commission.ta_commission_report_generator import (
    TACommissionReportGenerator,
)


class FinanceReportsGenerator(BaseReportGenerator):
    """
    Responsible for generating fiinance reports
    """

    FP_EVENT_TYPE = 'navision_reports_finance_portal_push'  # TODO clean this
    ROUTING_KEY = 'reporting.navision'

    def __init__(
        self,
        invoice_report_aggregates=None,
        settlement_data=None,
        payment_gateway_data=None,
        ota_booking_aggregates=None,
        ta_booking_aggregates=None,
    ):
        self.invoice_report_aggregates = invoice_report_aggregates
        self.settlement_data = settlement_data
        self.payment_gateway_data = payment_gateway_data
        self.ota_booking_aggregates = ota_booking_aggregates
        self.ta_booking_aggregates = ta_booking_aggregates

    def generate(self):
        pass

    def generate_customer_invoice_reports(self, buy_side_mappings):
        return CustomerInvoiceReportGenerator(
            self.invoice_report_aggregates, buy_side_mappings
        ).generate()

    def generate_marketplace_purchase_invoice_reports(self):
        return MarketPlacePurchaseInvoiceReportGenerator(
            self.invoice_report_aggregates
        ).generate()

    def generate_payment_reports(self, bill_ids=None):
        return PaymentDataReportGenerator(bill_ids).generate()

    def generate_ota_commission_reports(self):
        return OtaCommissionReportGenerator(self.ota_booking_aggregates).generate()

    def generate_settlement_reports(self):
        return SettlementReportGenerator(self.settlement_data['data']).generate()

    def generate_ta_commission_reports(self):
        return TACommissionReportGenerator(self.ta_booking_aggregates).generate()
