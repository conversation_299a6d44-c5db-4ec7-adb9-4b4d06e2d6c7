from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.finance_erp_reporting.constants import (
    ExpenseEntryTypes,
    HotelAdjustmentEntryTypes,
    HotelAdjustmentTypes,
    LoanEntryTypes,
    TaxEntryTypes,
    TreeboFeeEntryTypes,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.hotel_adjustment_aggregate import (
    HotelAdjustmentAggregate,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_expense_aggregate import (
    SettlementExpenseAggregate,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_loan_aggregate import (
    SettlementLoanAggregate,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_report_aggregate import (
    SettlementReportAggregate,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_tax_aggregate import (
    SettlementTaxAggregate,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_treebo_fee_aggregate import (
    SettlementTreeboFeeAggregate,
)


class SettlementReportGenerator(BaseReportGenerator):
    def __init__(self, settlement_data):
        self.settlement_data = settlement_data

    def generate(self):
        settlement_report_aggregates = []
        for hotel_id, settlement in self.settlement_data.items():
            treebo_fee_aggregates = self._generate_treebo_fee_aggregates(
                hotel_id, settlement
            )
            tax_aggregates = self._generate_tax_aggregates(hotel_id, settlement)
            loan_aggregates = self._generate_loan_aggregates(hotel_id, settlement)
            expense_aggregates = self._generate_expense_aggregates(hotel_id, settlement)
            hotel_adjustment_aggregates = self._generate_hotel_adjustment_aggregates(
                hotel_id, settlement
            )
            settlement_report_aggregates.append(
                SettlementReportAggregate(
                    treebo_fee_aggregates=treebo_fee_aggregates,
                    tax_aggregates=tax_aggregates,
                    loan_aggregates=loan_aggregates,
                    expense_aggregates=expense_aggregates,
                    hotel_adjustment_aggregates=hotel_adjustment_aggregates,
                )
            )
        return settlement_report_aggregates

    @staticmethod
    def _generate_treebo_fee_aggregates(hotel_id, settlement):
        return [
            SettlementTreeboFeeAggregate(
                hotel_id=hotel_id,
                entry_type_data=entry_type_data,
                settlement=settlement['settlement'],
            )
            for entry_type_data in TreeboFeeEntryTypes.all()
        ]

    @staticmethod
    def _generate_tax_aggregates(hotel_id, settlement):
        return [
            SettlementTaxAggregate(
                hotel_id=hotel_id,
                entry_type_data=entry_type_data,
                settlement=settlement['settlement'],
            )
            for entry_type_data in TaxEntryTypes.all()
        ]

    @staticmethod
    def _generate_loan_aggregates(hotel_id, settlement):
        return [
            SettlementLoanAggregate(
                hotel_id=hotel_id,
                entry_type_data=entry_type_data,
                settlement=settlement['settlement'],
            )
            for entry_type_data in LoanEntryTypes.all()
        ]

    @staticmethod
    def _generate_expense_aggregates(hotel_id, settlement):
        settlement_expense_aggregates = []
        for entry_type_data in ExpenseEntryTypes.all():
            invoice_data = next(
                iter(
                    [
                        invoice_data
                        for invoice_data in settlement['invoices']
                        if entry_type_data.expense_invoice_name
                        and invoice_data['type'] == entry_type_data.expense_invoice_name
                    ]
                ),
                None,
            )
            settlement_expense_aggregates.append(
                SettlementExpenseAggregate(
                    hotel_id=hotel_id,
                    entry_type_data=entry_type_data,
                    settlement=settlement['settlement'],
                    invoice_data=invoice_data if invoice_data else None,
                )
            )
        return settlement_expense_aggregates

    @staticmethod
    def _generate_hotel_adjustment_aggregates(hotel_id, settlement):
        hotel_adjustment_aggregates = []
        for adjustment_type_data in HotelAdjustmentTypes.all(
            exclude=[
                HotelAdjustmentTypes.GSTResellerDebit,
                HotelAdjustmentTypes.GSTResellerCredit,
            ]
        ):
            # Debit
            invoice_data = next(
                iter(
                    [
                        invoice_data
                        for invoice_data in settlement['invoices']
                        if adjustment_type_data.adjustment_invoice_name
                        and invoice_data['type']
                        == adjustment_type_data.adjustment_invoice_name
                    ]
                ),
                None,
            )
            hotel_adjustment_aggregates.append(
                HotelAdjustmentAggregate(
                    entry_type=HotelAdjustmentEntryTypes.DEBIT.value,
                    hotel_id=hotel_id,
                    adjustment_type_data=adjustment_type_data,
                    settlement=settlement['settlement'],
                    invoice_data=invoice_data if invoice_data else None,
                )
            )
            if not adjustment_type_data.reversal_adjustment_field:
                continue
            # Reversal
            hotel_adjustment_aggregates.append(
                HotelAdjustmentAggregate(
                    entry_type=HotelAdjustmentEntryTypes.REVERSAL.value,
                    hotel_id=hotel_id,
                    adjustment_type_data=adjustment_type_data,
                    settlement=settlement['settlement'],
                    is_reversal=True,
                )
            )
        return hotel_adjustment_aggregates
