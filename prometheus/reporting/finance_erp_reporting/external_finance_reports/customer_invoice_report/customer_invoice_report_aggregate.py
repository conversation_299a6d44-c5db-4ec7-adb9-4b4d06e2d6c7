from prometheus.reporting.base_report_aggregate import BaseReportAggregate
from prometheus.reporting.finance_erp_reporting.constants import (
    GSTType,
    InvoiceEntryType,
    NatureOfSupply,
    finance_context,
)
from prometheus.reporting.finance_erp_reporting.utils import (
    get_state_code,
    sanitize_string,
    to_dmy_str,
    to_finance_erp_room_type,
)
from prometheus.reporting.invoice_report.invoice_report_aggregate import (
    InvoiceReportAggregate,
)
from ths_common.constants.billing_constants import TaxTypes


class CustomerInvoiceReportAggregate(BaseReportAggregate):
    def __init__(
        self, customer_invoice_report: InvoiceReportAggregate, buy_side_invoice_number
    ):
        self.customer_invoice_report = customer_invoice_report
        self.buy_side_invoice_number = buy_side_invoice_number

    @property
    def entry_type(self):
        if self.customer_invoice_report.invoice_status == 'invoice':
            return InvoiceEntryType.ORDER.value
        return InvoiceEntryType.CREDIT.value

    @property
    def customer_number(self):
        return (
            sanitize_string(self.customer_invoice_report.corporate_id, 20)
            if self.customer_invoice_report.corporate_id
            else None
        )

    @property
    def booking_owner_legal_entity_id(self):
        return self.customer_invoice_report.booking_owner_legal_entity_id

    @property
    def order_date(self):
        return to_dmy_str(self.customer_invoice_report.invoice_date)

    @property
    def posting_date(self):
        return self.order_date

    @property
    def reference_number(self):
        return self.customer_invoice_report.reference_number

    @property
    def state_code(self):
        return get_state_code(self.customer_invoice_report.billed_by_state)

    @property
    def source(self):
        return self.customer_invoice_report.channel_code

    @property
    def sub_source(self):
        return self.customer_invoice_report.subchannel_code

    @property
    def billed_to_state_code(self):
        # As per gst rules, we have to give our billed by state code to calculate cgst and sgst (finance call)
        state_code = self.state_code
        if self.customer_invoice_report.is_sez:
            state_code = state_code + '-' + 'SEZ'
        return state_code

    @property
    def billed_to_gstin(self):
        return self.customer_invoice_report.billed_to_gstin_num

    @property
    def remarks(self):
        return "Reseller CRS {0} for {1}".format(
            'Invoices' if self.entry_type == InvoiceEntryType.ORDER.value else 'CN',
            finance_context.posting_date.strftime("%b %y"),
        )

    @property
    def billed_to_legal_name(self):
        return self.customer_invoice_report.billed_to_legal_name

    @property
    def gst_customer_type(self):
        return (
            GSTType.REGISTERED.value
            if self.billed_to_gstin
            else GSTType.UNREGISTERED.value
        )

    @property
    def nature_of_supply(self):
        return (
            NatureOfSupply.B2B.value
            if self.billed_to_gstin
            else NatureOfSupply.B2C.value
        )

    @property
    def unit_price(self):
        return self.customer_invoice_report.pretax_amount

    @property
    def tax_percentage(self):
        return int(self.customer_invoice_report.tax_percentage)

    @property
    def tax_type(self):
        if self.customer_invoice_report.item_igst_percent:
            return TaxTypes.IGST
        if (
            self.customer_invoice_report.item_cgst_percent
            or self.customer_invoice_report.item_sgst_percent
        ):
            return f"{TaxTypes.SGST}/{TaxTypes.CGST}"
        return None

    @property
    def igst(self):
        return self.customer_invoice_report.igst_amount

    @property
    def sgst(self):
        return self.customer_invoice_report.sgst_amount

    @property
    def cgst(self):
        return self.customer_invoice_report.cgst_amount

    @property
    def hsn_code(self):
        return self.customer_invoice_report.hsn_code.replace('HSN:', '')

    @property
    def hotel_name(self):
        if len(self.customer_invoice_report.hotel_trade_name) > 30:
            hotel_name = [
                word
                for word in self.customer_invoice_report.hotel_trade_name.split(' ')
                if word not in ['Treebo', 'Trip', 'Trend', 'Tryst']
            ]
            return ' '.join(hotel_name)
        return self.customer_invoice_report.hotel_trade_name

    @property
    def check_in(self):
        return to_dmy_str(self.customer_invoice_report.room_stay_checkin_date)

    @property
    def check_out(self):
        return to_dmy_str(self.customer_invoice_report.room_stay_checkout_date)

    @property
    def stay_days(self):
        return self.customer_invoice_report.stayed_room_nights

    @property
    def room_type(self):
        return to_finance_erp_room_type(self.customer_invoice_report.room_types)

    @property
    def occupancy(self):
        return self.customer_invoice_report.occupancy

    @property
    def guest_name(self):
        return sanitize_string(self.customer_invoice_report.sorted_guest_names, 50)

    @property
    def uvid_date(self):
        return self.order_date

    @property
    def invoice_number(self):
        return self.customer_invoice_report.invoice_number

    @property
    def total_invoice_amount(self):
        return self.customer_invoice_report.invoice_posttax_amount

    @property
    def hotel_code(self):
        return self.customer_invoice_report.hotel_id

    @property
    def original_invoice_number(self):
        return self.customer_invoice_report.original_invoice_number

    @property
    def unique_ref_id(self):
        return self.customer_invoice_report.unique_ref_id

    @property
    def invoice_charge_type(self):
        return self.customer_invoice_report.invoice_charge_type
