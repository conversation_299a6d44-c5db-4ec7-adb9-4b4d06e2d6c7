from prometheus.reporting.finance_erp_reporting.constants import (
    FinanceReports,
    InvoiceEntryType,
    finance_context,
)
from prometheus.reporting.finance_erp_reporting.utils import (
    get_state_code,
    sanitize_string,
    to_dmy_str,
    to_finance_erp_room_type,
)
from prometheus.reporting.invoice_report.invoice_report_aggregate import (
    InvoiceReportAggregate,
)
from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.booking_constants import BookingChannels
from ths_common.constants.reporting_constants import PurchaseInvoiceTypes


class MarketPlacePurchaseInvoiceReportAggregate:
    def __init__(self, invoice_report_aggregate: InvoiceReportAggregate):
        self.invoice_report_aggregate: InvoiceReportAggregate = invoice_report_aggregate

    TREEBO_LEGACY_BRANDS = ['Treebo', 'Trip', 'Trend', 'Tryst']

    @property
    def entry_type(self):
        if self.invoice_report_aggregate.is_credit_note_line_item():
            return InvoiceEntryType.CREDIT.value
        return InvoiceEntryType.ORDER.value

    @property
    def order_date(self):
        return to_dmy_str(self.invoice_report_aggregate.invoice_date)

    @property
    def posting_date(self):
        return self.order_date

    @property
    def due_date(self):
        return self.order_date

    @property
    def state_code(self):
        return get_state_code(self.invoice_report_aggregate.billed_by_state)

    @property
    def source(self):
        return self.invoice_report_aggregate.channel_code

    @property
    def sub_source(self):
        return self.invoice_report_aggregate.subchannel_code

    @property
    def purchase_type(self):
        if self.source == BookingChannels.TREEBO_INTERNAL.value:
            return PurchaseInvoiceTypes.TREEBO_STAY_INVOICE
        if (
            self.invoice_report_aggregate.billed_entity_category
            == BilledEntityCategory.FRANCHISER
        ):
            return PurchaseInvoiceTypes.FUNDING_INVOICE
        return None

    @property
    def remark(self):
        if self.purchase_type == PurchaseInvoiceTypes.FUNDING_INVOICE:
            remarks = self.invoice_report_aggregate.invoice_charge_remarks
            return remarks or PurchaseInvoiceTypes.FUNDING_INVOICE.value

        return "Purch {0} {1}/int".format(
            'Inv' if self.entry_type == InvoiceEntryType.ORDER.value else 'CN',
            finance_context.posting_date.strftime("%b %y"),
        )

    @property
    def reference_number(self):
        return self.invoice_report_aggregate.reference_number

    @property
    def unit_price(self):
        return self.invoice_report_aggregate.pretax_amount

    def tax_percentage(self):
        return int(self.invoice_report_aggregate.tax_percentage)

    @property
    def hsn_code(self):
        return self.invoice_report_aggregate.hsn_code.replace('HSN:', '')

    @property
    def hotel_name(self):
        if len(self.invoice_report_aggregate.hotel_trade_name) > 30:
            hotel_name = [
                word
                for word in self.invoice_report_aggregate.hotel_trade_name.split(' ')
                if word not in self.TREEBO_LEGACY_BRANDS
            ]
            return ' '.join(hotel_name)
        return self.invoice_report_aggregate.hotel_trade_name

    @property
    def check_in(self):
        return to_dmy_str(self.invoice_report_aggregate.room_stay_checkin_date)

    @property
    def check_out(self):
        return to_dmy_str(self.invoice_report_aggregate.room_stay_checkout_date)

    @property
    def stay_days(self):
        return self.invoice_report_aggregate.stayed_room_nights

    @property
    def room_type(self):
        return to_finance_erp_room_type(self.invoice_report_aggregate.room_types)

    @property
    def occupancy(self):
        return self.invoice_report_aggregate.occupancy

    @property
    def guest_name(self):
        return sanitize_string(self.invoice_report_aggregate.sorted_guest_names, 50)

    @property
    def uvid_date(self):
        return self.order_date

    @property
    def invoice_number(self):
        return self.invoice_report_aggregate.invoice_number

    @property
    def total_invoice_amount(self):
        return self.invoice_report_aggregate.invoice_posttax_amount

    @property
    def hotel_code(self):
        return self.invoice_report_aggregate.hotel_id

    @property
    def original_invoice_number(self):
        return self.invoice_report_aggregate.original_invoice_number

    @property
    def unique_ref_id(self):
        return self.invoice_report_aggregate.unique_ref_id

    @property
    def source_created_on(self):
        return self.invoice_report_aggregate.created_on

    @property
    def report_category(self):
        return FinanceReports.MARKETPLACE_PURCHASE_INVOICE_REPORT.value
