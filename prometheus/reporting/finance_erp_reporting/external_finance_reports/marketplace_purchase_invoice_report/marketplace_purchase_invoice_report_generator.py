from typing import List

from prometheus.reporting.finance_erp_reporting.external_finance_reports.marketplace_purchase_invoice_report.marketplace_purchase_invoice_aggregate import (
    MarketPlacePurchaseInvoiceReportAggregate,
)


class MarketPlacePurchaseInvoiceReportGenerator:
    def __init__(self, invoice_report_data):
        self.invoice_report_data = invoice_report_data

    def generate(self) -> List[MarketPlacePurchaseInvoiceReportAggregate]:
        return [
            MarketPlacePurchaseInvoiceReportAggregate(invoice_report_data_row)
            for invoice_report_data_row in self.invoice_report_data
        ]
