from datetime import datetime

from _decimal import Decimal
from treebo_commons.utils import dateutils

from prometheus.reporting.base_report_aggregate import BaseReportAggregate
from prometheus.reporting.finance_erp_reporting.constants import (
    CS_REFUND,
    PaymentGateways,
)
from prometheus.reporting.finance_erp_reporting.utils import sanitize_string, to_dmy_str
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    PaymentModes,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingChannels
from ths_common.constants.catalog_constants import SellerType


class PaymentGatewayReportAggregate(BaseReportAggregate):
    def __init__(
        self,
        payment_push_config,
        payment_charge_data=None,
        pg_data=None,
        booking_data=None,
        invoice_data=None,
        billed_entity_data=None,
        hotel_name=None,
    ):
        self.payment_push_config = payment_push_config
        self.payment_charge_data = payment_charge_data
        self.pg_data = pg_data
        self.booking_data = booking_data
        self.invoice_data = invoice_data
        self.billed_entity_data = billed_entity_data
        self.hotel_name = hotel_name
        self.proration_percentage = self.set_proration_percentage()

    @property
    def posting_date(self):
        if self._should_populate_posting_date():
            if self.paymode in self.payment_push_config["paymentdate_pay_modes"]:
                return self.payment_date
            return to_dmy_str(
                max(
                    datetime.strptime(self.check_out, "%d-%m-%Y").date(),
                    datetime.strptime(self.payment_date, "%d-%m-%Y").date(),
                )
            )

        return None

    @property
    def payment_date(self):
        return to_dmy_str(dateutils.to_date(self.payment_charge_data.created_date))

    @property
    def payment_amount(self):
        payment_amount = Decimal(self.payment_charge_data.amount)
        if self.is_payment_transaction():
            return round(payment_amount, 2) + (
                self.platform_fees if self.platform_fees else Decimal(0.00)
            )
        elif self.is_refund_transaction():
            return -round(payment_amount, 2)

    @property
    def pg_charges(self):
        if self.is_refund_transaction() or not self.pg_data:
            return None
        return (
            round(self._pg_charges() * self.proration_percentage, 2)
            if self._pg_charges()
            else None
        )

    @property
    def pg_tax(self):
        if self.is_refund_transaction() or not self.pg_data:
            return None
        if not self._pg_charges():
            return None
        return (
            round(self._pg_tax() * self.proration_percentage, 2)
            if self._pg_tax()
            else None
        )

    @property
    def platform_fees(self):
        if self.is_refund_transaction() or not self.pg_data:
            return None
        return round(Decimal(self.pg_data.platform_fee) * self.proration_percentage, 2)

    @property
    def pg_transaction_id(self):
        if self.is_payment_transaction():
            if self.pg_data:
                return sanitize_string(self.pg_data.pg_payment_id, 30)
            return (
                sanitize_string(self.payment_charge_data.payment_ref_id, 30)
                if self.payment_charge_data.payment_ref_id
                else None
            )
        elif self.is_refund_transaction():
            return (
                sanitize_string(self.payment_charge_data.payment_ref_id, 30)
                if self.payment_charge_data.payment_ref_id
                else None
            )
        return None

    @property
    def hotel_code(self):
        return self.booking_data.hotel_id

    @property
    def reference_number(self):
        return self.booking_data.reference_number

    @property
    def paid_by(self):
        return self.payment_charge_data.paid_by

    @property
    def paid_to(self):
        return self.payment_charge_data.paid_to

    @property
    def refund_reason(self):
        return self.payment_charge_data.refund_reason

    @property
    def payment_type(self):
        if self.payment_charge_data.payment_type == PaymentTypes.PAYMENT.value:
            if self.paid_to == 'hotel':
                return 'PAH'
            elif self.paid_to == 'treebo':
                return 'PTT'

        if self.payment_charge_data.payment_type in (
            PaymentTypes.REFUND.value,
            CS_REFUND,
        ):
            if self.paid_by == 'hotel':
                return 'PAH'
            elif self.paid_by == 'treebo':
                return 'PTT'

    @property
    def paymode(self):
        return self.payment_charge_data.payment_mode

    @property
    def paymode_type(self):
        if (
            self.payment_charge_data.payment_type == CS_REFUND
            and self.payment_charge_data.payment_mode == PaymentModes.RAZORPAY_API
        ):
            return PaymentModes.PAYOUT_LINK
        return self.payment_charge_data.payment_mode_sub_type

    @property
    def payor_entity(self):
        if self.billed_entity_data.category == "booker_company":
            if self.booking_data.company_details:
                return self.booking_data.company_details['legal_details'].get(
                    'external_reference_id', None
                )
        if self.billed_entity_data.category == "travel_agent":
            if self.booking_data.travel_agent_details:
                return self.booking_data.travel_agent_details['legal_details'].get(
                    'external_reference_id', None
                )
        return None

    @property
    def booker_entity(self):
        if self.booking_data.travel_agent_details:
            return self.booking_data.travel_agent_details['legal_details'].get(
                'external_reference_id', None
            )
        if self.booking_data.company_details:
            return self.booking_data.company_details['legal_details'].get(
                'external_reference_id', None
            )
        return None

    @property
    def booking_owner(self):
        return self.booking_data.booking_owner

    @property
    def athena_code(self):
        if (
            self.paymode == PaymentModes.PAID_AT_OTA
            or self.channel == BookingChannels.OTA.value
        ):
            if self.booking_data.travel_agent_details:
                return self.booking_data.travel_agent_details['legal_details'].get(
                    'external_reference_id', None
                )
            return None
        if (
            self.seller_model == SellerType.MARKETPLACE.value
            and self.channel != BookingChannels.OTA.value
        ):
            return None
        return self.booker_entity

    @property
    def payor_name(self):
        return (
            (self.billed_entity_data.first_name or '')
            + ' '
            + (self.billed_entity_data.last_name or '')
        )

    @property
    def invoice_id(self):
        if not self.invoice_data:
            return None
        return self.invoice_data.invoice_id

    @property
    def check_in(self):
        return to_dmy_str(self.booking_data.checkin_date)

    @property
    def check_out(self):
        return (
            to_dmy_str(dateutils.to_date(self.booking_data.actual_checkout_date))
            if self.booking_data.actual_checkout_date
            else to_dmy_str(self.booking_data.checkout_date)
        )

    @property
    def channel(self):
        return self.booking_data.channel_code

    @property
    def sub_channel(self):
        return self.booking_data.subchannel_code

    @property
    def seller_model(self):
        return self.booking_data.seller_model

    @property
    def original_booking_amount(self):
        return self._booking_charges() - self._booking_allowances()

    @property
    def is_advance(self):
        if dateutils.to_date(self.payment_charge_data.created_date) < dateutils.to_date(
            self.booking_data.checkout_date
        ):
            return True
        return False

    @property
    def uu_id(self):
        if self.payment_charge_data.payment_type == CS_REFUND:
            return f"{self.payment_charge_data.bill_id}-{self.payment_charge_data.credit_shell_refund_id}/{self.reference_number}"
        return (
            f"{self.payment_charge_data.bill_id}-{self.payment_charge_data.payment_id}-"
            f"{self.payment_charge_data.payment_split_id}/{self.reference_number}"
        )

    def _pg_charges(self):
        if not self.pg_data:
            return None
        return (
            round(Decimal(self.pg_data.calculated_gateway_charges), 2)
            if self.pg_data.calculated_gateway_charges
            else None
        )

    def _pg_tax(self):
        if not self.pg_data:
            return None
        return (
            round(Decimal(self.pg_data.calculated_gateway_charges_tax), 2)
            if self.pg_data.calculated_gateway_charges_tax
            else None
        )

    def _booking_charges(self):
        return (
            round(Decimal(self.payment_charge_data.total_charges), 2)
            if self.payment_charge_data.total_charges
            else Decimal(0.00)
        )

    def _booking_allowances(self):
        return (
            round(Decimal(self.payment_charge_data.total_allowances), 2)
            if self.payment_charge_data.total_allowances
            else Decimal(0.00)
        )

    def is_payment_transaction(self):
        return self.payment_charge_data.payment_type == PaymentTypes.PAYMENT.value

    def is_refund_transaction(self):
        return self.payment_charge_data.payment_type in (
            PaymentTypes.REFUND.value,
            CS_REFUND,
        )

    def set_proration_percentage(self):
        if not self.pg_data:
            return 1
        return self.get_proration_percentage(self.payment_charge_data.amount)

    def get_proration_percentage(self, payment_amount):
        return payment_amount / Decimal(self.pg_data.amount)

    def _is_payment_from_funding_invoice(self):
        return self.billed_entity_data.category == BilledEntityCategory.FRANCHISER.value

    def _should_populate_posting_date(self):
        if self._is_payment_from_funding_invoice():
            return False
        if (
            self.payment_charge_data.payment_mode
            not in self.payment_push_config["exclusive_pay_modes"]
        ):
            return False
        if self.booking_data.channel_code == BookingChannels.TREEBO_INTERNAL.value:
            return False
        if (
            (self.booking_data.seller_model == SellerType.RESELLER.value)
            and (self.payment_charge_data.payment_mode == PaymentModes.PAID_AT_OTA)
            and (self.booking_data.channel_code != BookingChannels.B2B.value)
        ):
            return False
        if (
            self.booking_data.seller_model == SellerType.RESELLER.value
            and self.payment_charge_data.payment_mode
            in self.payment_push_config["reseller_pay_modes"]
        ) or (
            self.booking_data.seller_model == SellerType.MARKETPLACE.value
            and self.payment_charge_data.payment_mode
            in self.payment_push_config["marketplace_pay_modes"]
        ):
            return True
        return False
