class PaymentGatewayDataDto:
    def __init__(self, pg_data):
        self.gateway_name = pg_data['gateway_name']
        self.calculated_gateway_charges = pg_data['calculated_gateway_charges']
        self.calculated_gateway_charges_tax = pg_data['calculated_gateway_charges_tax']
        self.pg_payment_id = pg_data['pg_payment_id']
        self.payment_id = pg_data['payment_id']
        self.amount = pg_data['amount']
        self.platform_fee = pg_data.get('platform_fee', 0.00)
        self.amount_after_platform_fee = pg_data.get('amount_after_platform_fee')
