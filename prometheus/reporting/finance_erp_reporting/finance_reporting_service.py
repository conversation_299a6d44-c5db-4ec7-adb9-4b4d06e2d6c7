import datetime
import logging
from typing import List

from flask import current_app as app
from imapclient.util import chunk
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.async_job.job_registry import JobRegistry
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.utils import split_on_condition
from prometheus.domain.billing.aggregates.credit_note_aggregate import (
    CreditNoteAggregate,
)
from prometheus.domain.billing.aggregates.invoice_aggregate import InvoiceAggregate
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.credit_note_repository import (
    CreditNoteRepository,
)
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.services.einvoicing_service import EInvoicingService
from prometheus.domain.booking.repositories import ExpenseItemRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories import (
    RoomTypeRepository,
    SkuCategoryRepository,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.company_profiles.dto.company_profiles_dto import (
    ParentEntity,
    SubEntity,
)
from prometheus.infrastructure.external_clients.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from prometheus.reporting.finance_erp_reporting.constants import (
    FinanceReports,
    OTASubchannels,
    finance_context,
)
from prometheus.reporting.finance_erp_reporting.external_clients.finance_service_client import (
    FinanceServiceClient,
)
from prometheus.reporting.finance_erp_reporting.external_clients.marvin_service_client import (
    MarvinServiceClient,
)
from prometheus.reporting.finance_erp_reporting.external_clients.payment_service_client import (
    PaymentServiceClient,
)
from prometheus.reporting.finance_erp_reporting.finance_reports_generator import (
    FinanceReportsGenerator,
)
from prometheus.reporting.finance_erp_reporting.financial_data_sync.service import (
    FinancialDataReportingService,
)
from prometheus.reporting.finance_erp_reporting.serialisers.finance_erp_client import (
    PGTransactionPushSchema,
    PurchaseInvoiceDataPushSchema,
    SaleInvoiceDataPushSchema,
)
from prometheus.reporting.invoice_report.invoice_report_generator import (
    InvoiceReportGenerator,
)
from prometheus.reporting.reporting_publisher import (
    ReportingEvent,
    ReportingJobPublisher,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from prometheus.reporting.repository.billing.credit_note_report_repository import (
    CreditNoteReportRepository,
)
from prometheus.reporting.repository.billing.invoice_report_repository import (
    InvoiceReportRepository,
)
from prometheus.reporting.utils import (
    alert_on_failure,
    send_finance_error_report,
    send_slack_alert,
)
from ths_common.constants.billing_constants import IssuedByType, IssuedToType
from ths_common.constants.booking_constants import BookingChannels, BookingStatus
from ths_common.constants.reporting_constants import ReportingTypes
from ths_common.utils.common_utils import group_list

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        InvoiceRepository,
        HotelRepository,
        RoomTypeRepository,
        SkuCategoryRepository,
        JobRegistry,
        CreditNoteRepository,
        ExpenseItemRepository,
        JobSchedulerService,
        ReportingJobPublisher,
        EInvoicingService,
        MarvinServiceClient,
        PaymentServiceClient,
        FinanceServiceClient,
        FinancialDataReportingService,
        InvoiceReportRepository,
        CreditNoteReportRepository,
        CompanyProfileServiceClient,
        TenantSettings,
    ]
)
class FinanceReportingService(ReportingApplicationService):
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository,
        invoice_repository: InvoiceRepository,
        hotel_repository,
        room_type_repository,
        sku_category_repository,
        job_registry,
        credit_note_repository: CreditNoteRepository,
        expense_item_repository,
        job_scheduler_service,
        reporting_job_publisher,
        einvoicing_service,
        marvin_service_client,
        payment_service_client,
        finance_service_client,
        financial_data_reporting_service,
        invoice_report_repository: InvoiceReportRepository,
        cn_report_repository: CreditNoteReportRepository,
        company_profile_service_client: CompanyProfileServiceClient,
        tenant_settings: TenantSettings,
    ):
        super().__init__(
            booking_repository,
            bill_repository,
            invoice_repository,
            hotel_repository,
            room_type_repository,
            sku_category_repository,
            job_registry,
            credit_note_repository,
            expense_item_repository,
            job_scheduler_service,
            reporting_job_publisher,
            einvoicing_service,
            financial_data_reporting_service,
            register_jobs=False,
        )
        self.marvin_service_client = marvin_service_client
        self.payment_service_client = payment_service_client
        self.finance_service_client = finance_service_client
        self.invoice_report_repository = invoice_report_repository
        self.cn_report_repository = cn_report_repository
        self.company_profile_service_client = company_profile_service_client
        self.tenant_settings = tenant_settings
        self.max_len_booking_reference_number = 50
        self.max_len_payment_reference_number = 30
        self.ota_commission_report_days_difference = 6

    def schedule_finance_reports_push(
        self,
        date,
        report_names=None,
        month=None,
        year=None,
        named_arg=None,
        event_type=FinanceReportsGenerator.FP_EVENT_TYPE,
    ):
        if not report_names:
            report_names = FinanceReports.all(
                exclude=[FinanceReports.SETTLEMENT_REPORT]
            )
        reporting_event = ReportingEvent(
            body=dict(
                event_type=event_type,
                date=date,
                report_names=report_names,
                month=month,
                year=year,
                named_arg=named_arg,
            ),
            routing_key=FinanceReportsGenerator.ROUTING_KEY,
        )
        self.reporting_job_publisher.publish(reporting_event)
        return True

    def push_finance_reports(self, date, report_names, month, year, named_arg=None):
        date = dateutils.ymd_str_to_date(date)
        for report_name in report_names:
            if report_name == FinanceReports.SETTLEMENT_REPORT.value:
                finance_context.set_report_name(report_name)
                finance_context.set_settlement_date(month, year)
                self.push_settlement_reports(month=month, year=year)
            else:
                finance_context.set_report_name(report_name)
                finance_context.set_posting_date(date)
                push_report_method = getattr(
                    FinanceReportingService, 'push_{0}s'.format(report_name)
                )
                alert_on_failure(push_report_method)(
                    self,
                    date,
                    reporting_type=ReportingTypes.FinanceERPReporting,
                    report_name=report_name,
                    posting_date=date,
                )
        return True

    def _generate_invoice_report_aggregate_data(self, date=None, bill_ids=None):
        if date and isinstance(date, str):
            date = dateutils.ymd_str_to_date(date)
        start_date, end_date, bill_ids = date, date, bill_ids
        credit_note_aggregates = self.credit_note_repository.credit_note_report_query(
            date, date, None, IssuedByType.RESELLER, bill_ids=bill_ids
        )
        purchase_side_cn_mapping = (
            self.credit_note_repository.get_hotel_credit_note_mapping(
                [
                    cn_aggregate.credit_note.credit_note_id
                    for cn_aggregate in credit_note_aggregates
                ]
            )
        )
        invoice_aggregates = self.invoice_repository.invoice_report_query(
            date, date, None, issued_by_type=IssuedByType.RESELLER, bill_ids=bill_ids
        )
        purchase_side_invoice_mapping = (
            self.invoice_repository.get_hotel_invoice_mapping(
                [
                    invoice_aggregate.invoice.invoice_id
                    for invoice_aggregate in invoice_aggregates
                ]
            )
        )
        report_aggregates = self._create_report_aggregates(
            credit_note_aggregates, invoice_aggregates
        )
        return report_aggregates, {
            **purchase_side_invoice_mapping,
            **purchase_side_cn_mapping,
        }

    def _create_report_aggregates(self, credit_note_aggregates, invoice_aggregates):
        credit_note_bill_map = group_list(credit_note_aggregates, 'bill_id')
        invoice_bill_map = group_list(invoice_aggregates, 'bill_id')
        credit_note_to_invoice_map = (
            {
                credit_note_aggregate.credit_note.credit_note_id: line_item.invoice_id
                for credit_note_aggregate in credit_note_aggregates
                for line_item in credit_note_aggregate.credit_note_line_items
            }
            if credit_note_aggregates
            else {}
        )
        hotel_ids = [
            invoice_aggregate.invoice.vendor_id
            for invoice_aggregate in invoice_aggregates
        ]
        hotel_ids.extend(
            [
                credit_note_aggregate.credit_note.vendor_id
                for credit_note_aggregate in credit_note_aggregates
            ]
        )
        hotel_aggregates = self.hotel_repository.load_all(hotel_ids=hotel_ids)
        room_type_map = self.room_type_repository.load_type_map()
        hotel_map = {
            hotel_aggregate.hotel_id: hotel_aggregate
            for hotel_aggregate in hotel_aggregates
        }
        sku_category_map = {
            aggregate.sku_category_id: aggregate
            for aggregate in self.sku_category_repository.load_all()
        }
        bills_to_be_loaded = list(invoice_bill_map.keys())
        bills_to_be_loaded.extend(credit_note_bill_map.keys())
        invoices_to_be_loaded = list(
            {
                invoice_aggregate.invoice.hotel_invoice_id
                for invoice_aggregate in invoice_aggregates
                if invoice_aggregate.invoice.hotel_invoice_id
            }
        )
        invoices_to_be_loaded.extend(credit_note_to_invoice_map.values())
        aggregates = self.build_aggregate_maps(
            bill_ids=bills_to_be_loaded,
            invoice_ids=invoices_to_be_loaded,
            credit_note_ids={
                credit_note_aggregate.credit_note.hotel_credit_note_id
                for credit_note_aggregate in credit_note_aggregates
            },
            load_bookings_by_bill_id=True,
            room_type_map=room_type_map,
            hotel_map=hotel_map,
            sku_category_map=sku_category_map,
        )
        report_aggregates = []
        for bill_id, invoice_aggregates in invoice_bill_map.items():
            bill_aggregate = aggregates.bill_map.get(bill_id)
            if bill_aggregate.is_pos_bill():
                continue
            booking_aggregate = aggregates.bill_to_booking_map.get(bill_id)
            for invoice_aggregate in invoice_aggregates:
                hotel_aggregate = aggregates.hotel_map.get(
                    invoice_aggregate.invoice.vendor_details.hotel_id
                )
                crs_context.set_hotel_context(hotel_aggregate)
                hotel_invoice_aggregate = aggregates.invoice_map.get(
                    invoice_aggregate.invoice.hotel_invoice_id
                )
                report_generator = InvoiceReportGenerator(
                    booking_aggregate,
                    bill_aggregate,
                    invoice_aggregate,
                    hotel_invoice_aggregate,
                    hotel_aggregate,
                    aggregates.room_type_map,
                    aggregates.sku_category_map,
                    None,
                    None,
                )

                invoice_report_aggregates = report_generator.generate(
                    for_credit_note=False
                )
                report_aggregates.extend(
                    invoice_report_aggregates
                ) if invoice_report_aggregates else None
        for bill_id, credit_note_aggregates in credit_note_bill_map.items():
            bill_aggregate = aggregates.bill_map.get(bill_id)
            if bill_aggregate.is_pos_bill():
                continue
            booking_aggregate = aggregates.bill_to_booking_map.get(bill_id)
            for credit_note_aggregate in credit_note_aggregates:
                invoice_aggregate = aggregates.invoice_map.get(
                    credit_note_to_invoice_map.get(
                        credit_note_aggregate.credit_note.credit_note_id
                    )
                )
                hotel_aggregate = aggregates.hotel_map.get(
                    credit_note_aggregate.credit_note.vendor_id
                )
                crs_context.set_hotel_context(hotel_aggregate)
                hotel_credit_note_aggregate = aggregates.credit_note_map.get(
                    credit_note_aggregate.credit_note.hotel_credit_note_id, None
                )
                report_generator = InvoiceReportGenerator(
                    booking_aggregate,
                    bill_aggregate,
                    invoice_aggregate,
                    None,
                    hotel_aggregate,
                    aggregates.room_type_map,
                    aggregates.sku_category_map,
                    credit_note_aggregate,
                    hotel_credit_note_aggregate,
                )
                invoice_report_aggregates = report_generator.generate(for_invoice=False)
                report_aggregates.extend(
                    invoice_report_aggregates
                ) if invoice_report_aggregates else None
        return report_aggregates

    def _get_bookings_for_ota_commission_push(self, date):
        booking_aggregates = (
            self.booking_repository.load_bookings_with_expected_checkout_date(
                date,
                channel_code=BookingChannels.OTA,
                subchannel_codes=OTASubchannels.all_values(),
                status=[
                    BookingStatus.CHECKED_OUT.value,
                    BookingStatus.NOSHOW.value,
                    BookingStatus.CANCELLED.value,
                ],
            )
        )
        return booking_aggregates

    def push_customer_invoice_reports(self, date, **kwargs):
        (
            invoice_report_aggregates,
            buy_side_mappings,
        ) = self._generate_invoice_report_aggregate_data(date)
        customer_invoice_report_aggregates = FinanceReportsGenerator(
            invoice_report_aggregates=invoice_report_aggregates
        ).generate_customer_invoice_reports(buy_side_mappings)
        return self.finance_service_client.push_sale_invoices(
            customer_invoice_report_aggregates
        )

    def push_marketplace_purchase_invoice_reports(self, date, **kwargs):
        purchase_reports = self._create_marketplace_purchase_invoice_reports(date)

        (
            valid_purchase_reports,
            invalid_purchase_report_without_purchase_type,
        ) = split_on_condition(
            purchase_reports,
            lambda x: x.purchase_type is not None,
        )
        if invalid_purchase_report_without_purchase_type:
            self._publish_market_place_purchase_report_errors(
                date, invalid_purchase_report_without_purchase_type
            )

        return self.finance_service_client.push_purchase_invoices(
            valid_purchase_reports
        )

    @staticmethod
    def _publish_market_place_purchase_report_errors(
        date,
        invalid_data_without_purchase_type,
    ):
        msg = (
            "Given Marketplace Purchase Invoice data failed to push because "
            "purchase type is not set for the following invoices: {0}".format(
                [report.invoice_number for report in invalid_data_without_purchase_type]
            )
        )
        send_finance_error_report(msg, date, finance_context.report_name)
        send_slack_alert(
            tenant_id=get_current_tenant_id(),
            slack_webhook_url=app.config['FINANCE_CRON_ALERTS_SLACK_WEBHOOK_URL'],
            msg=msg,
        )

    def _create_marketplace_purchase_invoice_reports(
        self,
        date=None,
        bill_ids=None,
    ):
        sh_company_codes_of_chain_manager = (
            self.get_all_sh_company_codes_of_chain_manager()
        )

        if not sh_company_codes_of_chain_manager:
            return []

        eligible_invoice_ids = (
            self.invoice_report_repository.fetch_invoice_ids_for_erp_sync(
                report_date=date,
                bill_ids=bill_ids,
                issued_by_type=IssuedByType.HOTEL,
                issued_to_type=IssuedToType.CUSTOMER,
                sh_company_codes=sh_company_codes_of_chain_manager,
            )
        )
        eligible_cn_ids = self.cn_report_repository.fetch_credit_note_ids_for_erp_sync(
            report_date=date,
            bill_ids=bill_ids,
            issued_by_type=IssuedByType.HOTEL,
            issued_to_type=IssuedToType.CUSTOMER,
            sh_company_codes=sh_company_codes_of_chain_manager,
        )

        credit_note_aggregates: List[
            CreditNoteAggregate
        ] = self.credit_note_repository.load_all(
            credit_note_ids=eligible_cn_ids,
        )
        invoice_aggregates: List[InvoiceAggregate] = self.invoice_repository.load_all(
            invoice_ids=eligible_invoice_ids,
        )
        invoice_report_aggregates = self._create_report_aggregates(
            credit_note_aggregates,
            invoice_aggregates,
        )
        return FinanceReportsGenerator(
            invoice_report_aggregates=invoice_report_aggregates
        ).generate_marketplace_purchase_invoice_reports()

    def get_all_sh_company_codes_of_chain_manager(self):
        chain_manager_parent_sh_company_code = (
            self.tenant_settings.get_chain_manager_parent_company_code()
        )

        if not chain_manager_parent_sh_company_code:
            logger.info(
                'Chain manager parent company code is not set in catalog tenant settings'
            )
            return []

        parent_entities: List[
            ParentEntity
        ] = self.company_profile_service_client.search_parent_entities(
            chain_manager_parent_sh_company_code
        )
        if not parent_entities:
            return []

        sub_entities: List[
            SubEntity
        ] = self.company_profile_service_client.get_sibling_sub_entities(
            parent_entity_id=parent_entities[0].parent_entity_id,
        )

        sh_company_codes_of_branches = [
            sub_entity.superhero_company_code for sub_entity in sub_entities or []
        ]

        return [
            chain_manager_parent_sh_company_code,
            *sh_company_codes_of_branches,
        ]

    def push_payment_gateway_reports(self, date, **kwargs):
        payment_data_report_aggregates = (
            FinanceReportsGenerator().generate_payment_reports()
        )
        reference_numbers = ', '.join(
            {
                payment.reference_number
                for payment in payment_data_report_aggregates
                if len(payment.reference_number) > self.max_len_payment_reference_number
            }
        )
        if reference_numbers:
            msg = (
                "Given Payment Gateway Data failed to push because length of reference number is exceeding 30 "
                "characters: {0}".format(reference_numbers)
            )
            send_finance_error_report(msg, date, finance_context.report_name)
            send_slack_alert(
                tenant_id=get_current_tenant_id(),
                slack_webhook_url=app.config['FINANCE_CRON_ALERTS_SLACK_WEBHOOK_URL'],
                msg=msg,
            )
        payment_data_report_aggregates = [
            payment
            for payment in payment_data_report_aggregates
            if len(payment.reference_number) <= self.max_len_payment_reference_number
        ]
        return self.finance_service_client.push_payments_data(
            payment_data_report_aggregates
        )

    def push_ota_commission_reports(self, date, **kwargs):
        date_time = datetime.datetime.strptime(
            dateutils.date_to_ymd_str(date), "%Y-%m-%d"
        )
        first_of_month = dateutils.to_date(
            dateutils.first_date_of_month(date=date_time)
        )
        last_of_month = dateutils.last_date_of_month(date_time)
        ota_booking_aggregates = []
        # Pushing data of x days back to minimise discrepancy in GB and NBV diff due to late refund in some cases.
        if (
            date_time.day - first_of_month.day
            < self.ota_commission_report_days_difference
        ):
            return
        if date_time.date() == last_of_month:
            for date in dateutils.date_range(
                dateutils.subtract(
                    date_time, days=self.ota_commission_report_days_difference
                ).date(),
                date_time.date(),
                end_inclusive=True,
            ):
                ota_booking_aggregates += self._get_bookings_for_ota_commission_push(
                    date
                )
        elif (
            date_time.day - first_of_month.day
            >= self.ota_commission_report_days_difference
        ):
            date = dateutils.date_to_ymd_str(
                dateutils.subtract(
                    date_time, days=self.ota_commission_report_days_difference
                ).date()
            )
            ota_booking_aggregates = self._get_bookings_for_ota_commission_push(date)
        ota_commission_report_aggregates = FinanceReportsGenerator(
            ota_booking_aggregates=ota_booking_aggregates
        ).generate_ota_commission_reports()
        reference_numbers = ', '.join(
            {
                f"{ota.reference_number} -> {ota.check_out}"
                for ota in ota_commission_report_aggregates
                if len(ota.reference_number) > self.max_len_booking_reference_number
            }
        )
        if reference_numbers:
            msg = "Given OTA bookings failed to push because length of reference number is exceeding 50 characters: {0}".format(
                reference_numbers
            )
            send_finance_error_report(msg, date, finance_context.report_name)
            send_slack_alert(
                tenant_id=get_current_tenant_id(),
                slack_webhook_url=app.config['FINANCE_CRON_ALERTS_SLACK_WEBHOOK_URL'],
                msg=msg,
            )
        ota_commission_report_aggregates = [
            ota
            for ota in ota_commission_report_aggregates
            if len(ota.reference_number) <= self.max_len_booking_reference_number
        ]
        return self.finance_service_client.push_ota_commission_data(
            ota_commission_report_aggregates
        )

    @alert_on_failure
    def push_settlement_reports(self, month, year, **kwargs):
        hotel_wise_settlement_data = self.marvin_service_client.fetch_settlement_data(
            month, year
        )
        settlement_report_aggregates = FinanceReportsGenerator(
            settlement_data=hotel_wise_settlement_data
        ).generate_settlement_reports()
        return self.finance_service_client.push_settlement_reports(
            settlement_report_aggregates
        )

    def push_ta_commission_reports(self, date, **kwargs):
        all_booking_ids = self.booking_repository.load_bookings_ids_consisting_all_ta_commissions_locked(
            date
        )
        records_processed = 0
        for booking_ids in chunk(all_booking_ids, 1500):
            booking_aggregates = self.booking_repository.load_all(
                booking_ids=booking_ids, skip_expenses=True, skip_rate_plans=True
            )
            records_processed += len(booking_aggregates)
            ta_commission_aggregates = FinanceReportsGenerator(
                ta_booking_aggregates=booking_aggregates
            ).generate_ta_commission_reports()
            self.finance_service_client.push_ta_commission_data(
                ta_commission_aggregates
            )
        return records_processed

    def fetch_finance_reports(self, request_data):
        report_name = request_data['report_name']

        if report_name == FinanceReports.PAYMENT_GATEWAY_REPORT.value:
            return self._generate_payments_data(request_data)

        if report_name == FinanceReports.CUSTOMER_INVOICE_REPORT.value:
            return self._generate_sales_data(request_data)

        if report_name == FinanceReports.MARKETPLACE_PURCHASE_INVOICE_REPORT.value:
            return self._generate_marketplace_purchase_report(request_data)
        return []

    def _generate_payments_data(self, request_data):
        reference_numbers = {
            res_data['resource_id'] for res_data in request_data['resource_data']
        }
        bill_ids = self.booking_repository.get_bill_ids_for_given_reference_numbers(
            reference_numbers
        )
        data_unique_ids = {
            res_data['resource_unique_id'] for res_data in request_data['resource_data']
        }
        payment_data_report_aggregates = (
            FinanceReportsGenerator().generate_payment_reports(bill_ids)
        )
        payment_data_report_aggregates = [
            aggregate
            for aggregate in payment_data_report_aggregates
            if aggregate.uu_id in data_unique_ids
        ]
        data_to_send = (
            PGTransactionPushSchema()
            .dump(payment_data_report_aggregates, many=True)
            .data
        )
        return data_to_send

    def _generate_sales_data(self, request_data):
        reference_numbers = {
            res_data['resource_id'] for res_data in request_data['resource_data']
        }
        bill_ids = self.booking_repository.get_bill_ids_for_given_reference_numbers(
            reference_numbers
        )
        data_unique_ids = {
            res_data['resource_unique_id'] for res_data in request_data['resource_data']
        }
        (
            invoice_report_aggregates,
            buy_side_mappings,
        ) = self._generate_invoice_report_aggregate_data(bill_ids=bill_ids)
        customer_invoice_report_aggregates = FinanceReportsGenerator(
            invoice_report_aggregates=invoice_report_aggregates
        ).generate_customer_invoice_reports(buy_side_mappings)
        customer_invoice_report_aggregates = [
            aggregate
            for aggregate in customer_invoice_report_aggregates
            if aggregate.unique_ref_id in data_unique_ids
        ]
        data_to_send = (
            SaleInvoiceDataPushSchema()
            .dump(customer_invoice_report_aggregates, many=True)
            .data
        )
        return data_to_send

    def _generate_marketplace_purchase_report(self, request_data):
        reference_numbers = {
            res_data['resource_id'] for res_data in request_data['resource_data']
        }
        bill_ids = self.booking_repository.get_bill_ids_for_given_reference_numbers(
            reference_numbers
        )
        data_unique_ids = {
            res_data['resource_unique_id'] for res_data in request_data['resource_data']
        }
        purchase_reports = self._create_marketplace_purchase_invoice_reports(
            bill_ids=bill_ids
        )

        purchase_reports = [
            aggregate
            for aggregate in purchase_reports
            if aggregate.unique_ref_id in data_unique_ids
        ]
        data_to_send = (
            PurchaseInvoiceDataPushSchema().dump(purchase_reports, many=True).data
        )
        return data_to_send
