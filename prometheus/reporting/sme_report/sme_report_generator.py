import os
import uuid

from prometheus.reporting.base_report_generator import Base<PERSON>eportGenerator
from prometheus.reporting.sme_report.sme_report_aggregate import SmeReportAggregate


class SmeReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows for a given booking
    """

    REPORT_COLUMNS = [
        'invoice_numbers',
        'booking_id',
        'booking_created_date',
        'booking_status',
        'booker_name',
        'booker_phone',
        'booker_email',
        'guest_names',
        'guest_phones',
        'guest_emails',
        'number_of_guests',
        'hotel_name',
        'city_name',
        'checkin_date',
        'checkout_date',
        'number_of_rooms',
        'number_of_room_nights',
        'pretax_amount',
        'cgst',
        'sgst',
        'igst',
        'flood_cess',
        'posttax_amount',
        'billed_by_hotel_gstin',
        'billed_by_hotel_state',
        'billed_to_customer_gstin',
        'billed_to_customer_state',
        'payments',
    ]
    EVENT_TYPE = 'sme_report'
    ROUTING_KEY = 'reporting.sme'
    SME_REPORT_FOLDER_NAME = 'sme_reports/'

    def __init__(
        self, booking_aggregate, bill_aggregate, invoice_aggregates, hotel_aggregate
    ):
        self.hotel_aggregate = hotel_aggregate
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.invoice_aggregates = invoice_aggregates

    def generate(self):
        return [
            SmeReportAggregate(
                self.booking_aggregate,
                self.bill_aggregate,
                self.hotel_aggregate,
                self.booking_aggregate.room_stays,
                self.invoice_aggregates,
            )
        ]

    @staticmethod
    def generate_sme_report_file_name(extension='csv', identifier=None):
        if identifier is None:
            identifier = str(uuid.uuid4())

        file_name = f"sme-report-{identifier}.{extension}"
        return (
            os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/reports/") + file_name
        )
