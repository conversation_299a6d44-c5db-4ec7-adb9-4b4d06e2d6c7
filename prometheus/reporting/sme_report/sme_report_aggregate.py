from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.reporting.base_report_aggregate import BaseReportAggregate
from ths_common.constants.billing_constants import PaymentStatus, TaxTypes


class SmeReportAggregate(BaseReportAggregate):
    def __init__(
        self,
        booking_aggregate,
        bill_aggregate,
        hotel_aggregate,
        room_stays,
        invoice_aggregates,
    ):
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.hotel_aggregate = hotel_aggregate
        self.invoice_aggregates = invoice_aggregates
        self.room_stays = room_stays
        self.hotel_context = crs_context.set_hotel_context(hotel_aggregate)

    @property
    def invoice_numbers(self):
        if self.invoice_aggregates:
            return ", ".join(
                [
                    inv_agg.invoice_number
                    for inv_agg in self.invoice_aggregates
                    if inv_agg.invoice_number is not None
                ]
            )
        return None

    @property
    def booking_id(self):
        return self.booking_aggregate.booking.reference_number

    @property
    def booking_created_date(self):
        return dateutils.to_date(self.booking_aggregate.booking.created_at)

    @property
    def booking_status(self):
        return self.booking_aggregate.booking.status

    @property
    def booker_name(self):
        return self.booking_owner().name

    @property
    def booker_phone(self):
        return self.booking_owner().phone

    @property
    def booker_email(self):
        return self.booking_owner().email

    @property
    def guest_names(self):
        customers_list = [
            self.booking_aggregate.get_customer(gs.guest_id)
            for room_stay in self.room_stays
            for gs in room_stay.all_guest_stays_except_cancelled()
            if gs.guest_id is not None
        ]
        return ", ".join({customer.first_name for customer in customers_list})

    @property
    def guest_phones(self):
        customers_list = [
            self.booking_aggregate.get_customer(gs.guest_id)
            for room_stay in self.room_stays
            for gs in room_stay.all_guest_stays_except_cancelled()
            if gs.guest_id is not None
        ]
        return ", ".join({customer.phone.number for customer in customers_list})

    @property
    def guest_emails(self):
        customers_list = [
            self.booking_aggregate.get_customer(gs.guest_id)
            for room_stay in self.room_stays
            for gs in room_stay.all_guest_stays_except_cancelled()
            if gs.guest_id is not None
        ]
        return ", ".join({customer.email for customer in customers_list})

    @property
    def number_of_guests(self):
        return len([room_stay.max_occupancy for room_stay in self.room_stays])

    @property
    def hotel_name(self):
        return self.hotel_aggregate.hotel.name

    @property
    def city_name(self):
        return self.hotel_aggregate.hotel.city.name

    @property
    def checkin_date(self):
        return self.hotel_context.hotel_checkin_date(
            self.booking_aggregate.booking.checkin_date
        )

    @property
    def checkout_date(self):
        return self.hotel_context.hotel_checkout_date(
            self.booking_aggregate.booking.checkout_date
        )

    @property
    def number_of_rooms(self):
        return len(self.room_stays)

    @property
    def number_of_room_nights(self):
        total_days = (self.checkout_date - self.checkin_date).days
        return 1 if total_days == 0 else total_days

    @property
    def pretax_amount(self):
        return sum(
            [
                charge.get_pretax_amount_post_allowance()
                for charge in self.bill_aggregate.active_charges
            ]
        )

    @property
    def cgst(self):
        cgst_charge_details = [
            SmeReportAggregate.get_tax_details_for_type(
                charge.tax_details, TaxTypes.CGST
            )
            for charge in self.bill_aggregate.active_charges
        ]
        return sum(
            [
                cgst_charge_detail.amount.amount
                for cgst_charge_detail in cgst_charge_details
            ]
        )

    @property
    def sgst(self):
        sgst_charge_details = [
            SmeReportAggregate.get_tax_details_for_type(
                charge.tax_details, TaxTypes.SGST
            )
            for charge in self.bill_aggregate.active_charges
        ]
        return sum(
            [
                sgst_charge_detail.amount.amount
                for sgst_charge_detail in sgst_charge_details
            ]
        )

    @property
    def igst(self):
        igst_charge_details = [
            SmeReportAggregate.get_tax_details_for_type(
                charge.tax_details, TaxTypes.IGST
            )
            for charge in self.bill_aggregate.active_charges
        ]
        return sum(
            [
                igst_charge_detail.amount.amount
                for igst_charge_detail in igst_charge_details
            ]
        )

    @property
    def flood_cess(self):
        flood_cess_charge_details = [
            SmeReportAggregate.get_tax_details_for_type(
                charge.tax_details, TaxTypes.KERALA_FLOOD_CESS
            )
            for charge in self.bill_aggregate.active_charges
        ]
        return sum(
            [
                flood_cess_charge_detail.amount.amount
                for flood_cess_charge_detail in flood_cess_charge_details
                if flood_cess_charge_detail is not None
            ]
        )

    @property
    def posttax_amount(self):
        return sum(
            [
                charge.get_posttax_amount_post_allowance()
                for charge in self.bill_aggregate.active_charges
            ]
        )

    @property
    def billed_by_hotel_gstin(self):
        return self.hotel_aggregate.hotel.gstin_num

    @property
    def billed_by_hotel_state(self):
        return self.hotel_aggregate.hotel.legal_state

    @property
    def billed_to_customer_gstin(self):
        return self.billed_to_customer_gst_details().gstin_num

    @property
    def billed_to_customer_state(self):
        return self.billed_to_customer_gst_details().address.state

    @property
    def payments(self):
        return sum(
            [
                payment.amount.amount
                for payment in self.bill_aggregate.payments
                if not payment.status == PaymentStatus.CANCELLED
            ]
        )

    def billed_to_customer_gst_details(self):
        return self.booking_aggregate.get_booking_owner().gst_details

    def booking_owner(self):
        return self.booking_aggregate.get_booking_owner()

    @staticmethod
    def get_tax_details_for_type(tax_details, tax_type):
        for tax_detail in tax_details:
            if tax_detail.tax_type == tax_type:
                return tax_detail
