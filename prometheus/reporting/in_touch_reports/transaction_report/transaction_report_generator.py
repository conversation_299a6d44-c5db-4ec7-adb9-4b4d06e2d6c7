import os
from datetime import datetime

from treebo_commons.utils import dateutils

from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.in_touch_reports.transaction_report.transaction_report_charge_aggregate import (
    InTouchTransactionReportChargeAggregate,
)
from prometheus.reporting.in_touch_reports.transaction_report.transaction_report_payment_aggregate import (
    InTouchTransactionReportPaymentAggregate,
)


class InTouchTransactionReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows for a given booking
    """

    REPORT_COLUMNS = [
        "hotel_code",
        "pms_hotel_id",
        "pms_transaction_id",
        "pms_visit_id",
        "pms_guest_id",
        "business_date",
        "transaction_time",
        "transaction_status",
        "transaction_type",
        "currency",
        "revenue",
        "tax",
        "transaction_reference",
        "pos_transaction_code",
        "package_flag",
        "transaction_code",
        "rate_plan_reference_id",
        "rate_plan_name",
        "rate_plan_code",
        "folio_type",
        "folio_id",
        "folio_number",
        "group_ref",
        "posttax_amount",
        "qty",
        "cvr_qty",
        "unit_code",
        "tax_rate_percentage",
        "total",
        "description",
        "percentage",
        "transaction_code_desc",
        "customer_ref",
    ]

    def __init__(self, booking_aggregate, bill_aggregate, hotel_aggregate, folios):
        self.hotel_aggregate = hotel_aggregate
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.folios = folios

    def generate(self):
        report_aggregates = []
        for charge in self.bill_aggregate.get_active_charges():
            for charge_split in charge.charge_splits:
                report_aggregates.append(
                    InTouchTransactionReportChargeAggregate(
                        self.booking_aggregate,
                        self.bill_aggregate,
                        self.hotel_aggregate,
                        charge,
                        charge_split,
                        self.folios,
                    )
                )
        for payment in self.bill_aggregate.payments:
            for payment_split in payment.payment_splits:
                report_aggregates.append(
                    InTouchTransactionReportPaymentAggregate(
                        self.booking_aggregate,
                        self.bill_aggregate,
                        self.hotel_aggregate,
                        payment,
                        payment_split,
                        self.folios,
                    )
                )
        return report_aggregates

    @staticmethod
    def generate_in_touch_transaction_report_file_name(report_date, extension='csv'):
        hotel_business_date_with_current_timestamp = datetime.combine(
            dateutils.ymd_str_to_date(report_date), dateutils.current_datetime().time()
        )
        hotel_business_datetime = datetime.strftime(
            hotel_business_date_with_current_timestamp, "%Y-%m-%d-%H:%M:%S"
        )
        file_name = f"/transaction_{hotel_business_datetime}.{extension}"

        return os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp") + file_name

    @staticmethod
    def get_default_expiration_time():
        return 604800  # 7 days
