class CustomerDto:
    def __init__(
        self,
        pms_profile_id,
        profile_type,
        name,
        title,
        legal_name,
        first_name,
        last_name,
        address,
        address_line_1,
        address_line_2,
        city,
        state,
        zip_code,
        country,
        nationality,
        phone,
        email,
        date_of_birth,
        gender,
        membership_id,
        membership_type,
        external_profile_identifier,
        is_vip,
        insert_date,
        update_date,
        doc_type,
        doc_id,
        doc_issue_location,
        doc_issue_country_code,
        is_primary_address,
        effective_date,
    ):
        self.pms_profile_id = pms_profile_id
        self.profile_type = profile_type
        self.name = name
        self.title = title
        self.first_name = first_name
        self.last_name = last_name
        self.legal_name = legal_name
        self.address = address
        self.address_line_1 = address_line_1
        self.address_line_2 = address_line_2
        self.city = city
        self.state = state
        self.zip_code = zip_code
        self.country = country
        self.nationality = nationality
        self.phone = phone
        self.email = email
        self.date_of_birth = date_of_birth
        self.gender = gender
        self.membership_id = membership_id
        self.membership_type = membership_type
        self.external_profile_identifier = external_profile_identifier
        self.is_vip = is_vip
        self.insert_date = insert_date
        self.update_date = update_date
        self.doc_type = doc_type
        self.doc_id = doc_id
        self.doc_issue_location = doc_issue_location
        self.doc_issue_country_code = doc_issue_country_code
        self.is_primary_address = is_primary_address
        self.effective_date = effective_date
