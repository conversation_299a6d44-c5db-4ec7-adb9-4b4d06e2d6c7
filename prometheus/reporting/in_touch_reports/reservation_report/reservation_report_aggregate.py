from collections import defaultdict

import pycountry
from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.reporting.base_report_aggregate import BaseReportAggregate
from prometheus.reporting.in_touch_reports.utils import build_pms_profile_id
from ths_common.constants.billing_constants import ChargeStatus, PaymentStatus
from ths_common.constants.booking_constants import ProfileTypes
from ths_common.constants.catalog_constants import SkuCategory
from ths_common.constants.reporting_constants import InTouchSkuCategories, SegmentName

HOUSE_USE_RATE_PLAN_CODE = 'HSE'


class InTouchReservationReportAggregate(BaseReportAggregate):
    def __init__(
        self,
        booking_aggregate,
        bill_aggregate,
        hotel_aggregate,
        room_stay,
        house_account_room_type_id,
    ):
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.hotel_aggregate = hotel_aggregate
        self.hotel_context = crs_context.set_hotel_context(hotel_aggregate)
        self.room_stay = room_stay
        self.house_account_room_type_id = house_account_room_type_id

    def _customer(self):
        def _get_customer(guest_stays):
            for guest_stay in guest_stays:
                if (
                    guest_stay.guest_details
                    and guest_stay.guest_details.is_primary
                    and guest_stay.guest_id
                ):
                    return self.booking_aggregate.get_customer(guest_stay.guest_id)
            if guest_stays and guest_stays[0].guest_id:
                return self.booking_aggregate.get_customer(guest_stays[0].guest_id)
            return None

        customer = _get_customer(self.room_stay.active_guest_stays())
        return (
            customer
            if customer is not None
            else _get_customer(self.room_stay.guest_stays)
        )

    def _get_loyalty_data(self):
        if not self.booking_aggregate.booking.extra_information:
            return ""
        loyalty_data = self.booking_aggregate.booking.extra_information.get(
            "unordered_external_service_info", {}
        ).get("loyalty_data")

        return loyalty_data

    @property
    def hotel_code(self):
        return self.hotel_aggregate.hotel.hotel_id

    @property
    def pms_hotel_id(self):
        return self.hotel_aggregate.hotel.hotel_id

    @property
    def confirmation_number(self):
        return self.booking_aggregate.booking.reference_number

    @property
    def hotel_name(self):
        return self.hotel_aggregate.hotel.name

    @property
    def pms_visit_id(self):
        return (
            f"{self.booking_aggregate.booking.booking_id}_{self.room_stay.room_stay_id}"
        )

    @property
    def booking_id(self):
        return self.booking_aggregate.booking.booking_id

    @property
    def reservation_date(self):
        return self.booking_aggregate.booking.created_at

    @property
    def arrival_date(self):
        checkin_date = self.room_stay.actual_checkin_date or self.room_stay.checkin_date
        return self.hotel_context.hotel_checkin_date(checkin_date)

    @property
    def departure_date(self):
        checkout_date = (
            self.room_stay.actual_checkout_date or self.room_stay.checkout_date
        )
        return self.hotel_context.hotel_checkout_date(checkout_date)

    @property
    def cancellation_date(self):
        cancellation_date = self.booking_aggregate.booking.cancellation_datetime
        return (
            self.hotel_context.hotel_checkout_date(cancellation_date)
            if cancellation_date
            else None
        )

    @property
    def reservation_status(self):
        return self.room_stay.status.value

    @property
    def adults(self):
        return len(self.room_stay.adult_guest_stays())

    @property
    def children(self):
        return len(self.room_stay.all_guest_stays_except_cancelled()) - len(
            self.room_stay.adult_guest_stays()
        )

    @property
    def is_walkin_booking(self):
        return self.booking_aggregate.booking.source.is_walk_in_channel()

    @property
    def room_revenue(self):
        return sum(
            [
                charge.get_posttax_amount_post_allowance()
                for charge in self.get_room_stay_charges()
            ]
        )

    @property
    def fnb_revenue(self):
        fnb_sku_categories = [
            InTouchSkuCategories.BEVERAGES.value,
            InTouchSkuCategories.FOOD.value,
            InTouchSkuCategories.ALCOHOL.value,
            InTouchSkuCategories.FOOD_AND_BEVERAGES.value,
        ]
        return sum(
            [
                charge.get_posttax_amount_post_allowance()
                for charge in self.charges()
                if charge.status != ChargeStatus.CANCELLED
                and charge.item.sku_category_id in fnb_sku_categories
            ]
        )

    @property
    def other_revenue(self):
        charges = self.charges()
        stay_and_fnb_sku_categories = [
            InTouchSkuCategories.STAY.value,
            InTouchSkuCategories.STAY12.value,
            InTouchSkuCategories.STAY18.value,
            InTouchSkuCategories.ROOM.value,
            InTouchSkuCategories.BEVERAGES.value,
            InTouchSkuCategories.FOOD.value,
            InTouchSkuCategories.ALCOHOL.value,
            InTouchSkuCategories.FOOD_AND_BEVERAGES.value,
        ]
        return sum(
            [
                charge.get_posttax_amount_post_allowance()
                for charge in charges
                if charge.status != ChargeStatus.CANCELLED
                and charge.item.sku_category_id not in stay_and_fnb_sku_categories
            ]
        )

    @property
    def total_revenue(self):
        return sum(
            [
                charge.get_posttax_amount_post_allowance()
                for charge in self.charges()
                if charge.status != ChargeStatus.CANCELLED
            ]
        )

    @property
    def booking_status(self):
        return self.booking_aggregate.booking.status

    def get_payments(self):
        return [
            payment
            for payment in self.bill_aggregate.payments
            if payment.status != PaymentStatus.CANCELLED
        ]

    @property
    def room_type(self):
        return (
            self.room_stay.room_allocation.room_type_id
            if self.room_stay.room_allocation
            else None
        )

    def room_stay_id(self):
        return self.room_stay.room_stay_id

    def get_room_stay_charges(self, room_stay_id=None):
        if room_stay_id is None:
            room_stay_id = self.room_stay_id()

        charge_ids = self.booking_aggregate.get_room_stay_charges([room_stay_id])
        charges = [
            charge
            for charge in self.bill_aggregate.filter_and_get_charges(charge_ids)
            if charge.status != ChargeStatus.CANCELLED
        ]
        return charges

    def get_room_stay_expenses(self, room_stay_id=None):
        if room_stay_id is None:
            room_stay_id = self.room_stay_id()

        charge_ids = [
            expense.charge_id
            for expense in self.booking_aggregate.get_active_expenses_for_room_stay(
                room_stay_id
            )
        ]
        charges = [
            charge
            for charge in self.bill_aggregate.filter_and_get_charges(charge_ids)
            if charge.status != ChargeStatus.CANCELLED
        ]
        return charges

    def charges(self):
        charges = self.get_room_stay_charges(self.room_stay_id())
        charges.extend(self.get_room_stay_expenses(self.room_stay_id()))
        return charges

    @property
    def date_wise_stay_charges_posttax(self):
        datewise_charge_amount = defaultdict(
            lambda: Money(0, self.bill_aggregate.bill.base_currency)
        )
        for charge in self.charges():
            if charge.item.sku_category_id == SkuCategory.STAY.value:
                applicble_date = dateutils.date_to_ymd_str(charge.applicable_date)
                datewise_charge_amount[applicble_date] = (
                    datewise_charge_amount[applicble_date]
                    + charge.get_posttax_amount_post_allowance()
                )
        return datewise_charge_amount

    @property
    def room_related_charges_posttax(self):
        total_room_charges = Money(0, self.bill_aggregate.bill.base_currency)
        for charge in self.charges():
            total_room_charges += charge.get_posttax_amount_post_allowance()
        return total_room_charges

    def room_related_charges_posttax_between_stay_dates(self, date1, date2):
        total_room_charges = Money(0, self.bill_aggregate.bill.base_currency)
        for charge in self.charges():
            if (
                charge.item.sku_category_id == SkuCategory.STAY.value
                and date1 <= charge.applicable_date.date() <= date2
            ):
                total_room_charges += charge.get_posttax_amount_post_allowance()
        return total_room_charges

    @property
    def stay_room_type(self):
        return (
            self.room_stay.room_allocation.room_type_id
            if self.room_stay.room_allocation
            else None
        )

    @property
    def booked_room_type(self):
        room_stay_id_to_room_allocation_history_map = {
            rs.room_stay_id: rs for rs in self.room_stay.room_allocation_history
        }
        return (
            room_stay_id_to_room_allocation_history_map.get(
                self.room_stay.room_stay_id
            ).room_type_id
            if room_stay_id_to_room_allocation_history_map
            else None
        )

    @property
    def channel_code(self):
        return self.booking_aggregate.booking.source.channel_id

    @property
    def market_code(self):
        segments = self.booking_aggregate.booking.segments
        if self.booking_aggregate.booking.segments:
            for segment in segments:
                if segment.name == SegmentName.MARKET_SEGMENT.value:
                    return segment.value.code + " : " + segment.value.name
        return None

    @property
    def rate_plan_ids(self):
        rate_plan_id_map = {
            rp.rate_plan_id: rp for rp in self.booking_aggregate.rate_plans
        }
        if self.room_stay.room_rate_plans:
            return [
                rate_plan_id_map.get(rp.rate_plan_id).rate_plan_reference_id
                for rp in self.room_stay.room_rate_plans
            ][0]
        return []

    @property
    def external_reference_id(self):
        return 'NA'

    @property
    def external_reference_type(self):
        return 'NA'

    @property
    def pms_company_id(self):
        return (
            self.booking_aggregate.booking.company_details
            and self.booking_aggregate.booking.company_details.legal_details.external_reference_id
        )

    @property
    def pms_company_name(self):
        company_details = self.booking_aggregate.booking.company_details
        return company_details.legal_details.legal_name if company_details else None

    @property
    def pms_travel_agent_id(self):
        return (
            self.booking_aggregate.booking.travel_agent_details
            and self.booking_aggregate.booking.travel_agent_details.legal_details.external_reference_id
        )

    @property
    def pms_travel_agent_name(self):
        travel_agent_details = self.booking_aggregate.booking.travel_agent_details
        return (
            travel_agent_details.legal_details.legal_name
            if travel_agent_details
            else None
        )

    @property
    def room_number(self):
        return (
            self.room_stay.room_allocation.room_no
            if self.room_stay.room_allocation
            else None
        )

    @property
    def insert_date(self):
        return self.booking_aggregate.booking.created_at

    @property
    def update_time(self):
        return self.booking_aggregate.booking.modified_at

    @property
    def pms_guest_id(self):
        customer = self._customer()
        return (
            build_pms_profile_id(customer, self.booking_aggregate) if customer else None
        )

    @property
    def nights(self):
        room_stay_time_delta = (
            self.room_stay.checkout_date - self.room_stay.checkin_date
        )
        nights = (
            (room_stay_time_delta.days + 1)
            if room_stay_time_delta.seconds
            else room_stay_time_delta.days
        )
        return nights

    @property
    def is_group_booking(self):
        return True if self.booking_aggregate.booking.group_name else False

    @property
    def group_name(self):
        return self.booking_aggregate.booking.group_name

    @property
    def loyalty_code(self):
        loyalty_data = self._get_loyalty_data()
        if loyalty_data:
            return loyalty_data.get("program_id")

        return ""

    @property
    def loyalty_number(self):
        loyalty_data = self._get_loyalty_data()
        if loyalty_data:
            return loyalty_data.get("membership_id")

        return ""

    @property
    def adult_free_count(self):
        return ""  # Unsupported field by Superhero, to be left empty

    @property
    def child_free_count(self):
        return ""  # Unsupported field by Superhero, to be left empty

    @property
    def purpose_tracking(self):
        segments = self.booking_aggregate.booking.segments
        if self.booking_aggregate.booking.segments:
            for segment in segments:
                if segment.name == SegmentName.ORIGIN.value:
                    return segment.value.code + " : " + segment.value.name
        return None

    @property
    def market_tracking(self):
        segments = self.booking_aggregate.booking.segments
        if segments:
            for segment in segments:
                if segment.name == SegmentName.MARKET_SEGMENT.value:
                    return segment.group_name
        return None

    @property
    def hom_tracking(self):
        customer = self._customer()
        if customer and customer.address:
            for country in pycountry.countries:
                if country.name == customer.address.country:
                    return country.alpha_2
        return None

    @property
    def currency(self):
        return self.bill_aggregate.bill.base_currency.value or ""

    @property
    def free_rate_ind(self):
        return ""  # Unsupported field by Superhero, to be left empty

    @property
    def house_use_rate_ind(self):
        return ""  # Unsupported field by Superhero, to be left empty

    @property
    def rooms_count(self):
        return len(self.booking_aggregate.get_active_room_stays())

    @property
    def tars_reservation_number(self):
        if not self.booking_aggregate.booking.extra_information:
            return ""
        external_booking_id = self.booking_aggregate.booking.extra_information.get(
            "unordered_external_service_info", {}
        ).get("external_booking_id")
        if external_booking_id:
            return external_booking_id

        return ""

    @property
    def is_tars_booking(self):
        if self.channel_code.lower() in ("tars-b2c", "tars"):
            return True
        else:
            return False

    @property
    def program_id(self):
        loyalty_data = self._get_loyalty_data()
        return loyalty_data.get("program_id") if loyalty_data else ""

    @property
    def program_name(self):
        loyalty_data = self._get_loyalty_data()
        return loyalty_data.get("program_name") if loyalty_data else ""

    @property
    def program_expiry_date(self):
        loyalty_data = self._get_loyalty_data()
        return loyalty_data.get("expiry_date") if loyalty_data else ""

    @property
    def memebership_id(self):
        loyalty_data = self._get_loyalty_data()
        return loyalty_data.get("membership_id") if loyalty_data else ""

    @property
    def tars_cancellation_date(self):
        return ""  # Unsupported field by Superhero, to be left empty

    @property
    def reservation_share(self):
        for room_stay in self.booking_aggregate.get_all_room_stays():
            if not room_stay.deleted:
                master_room_stay = room_stay
                break
        if master_room_stay == self.room_stay:
            return 'No'
        else:
            return 'Yes'

    @staticmethod
    def _get_rate_plan_id(booking_aggregate, rate_plan_code):
        for rate_plan in booking_aggregate.rate_plans:
            if rate_plan.rate_plan_code == rate_plan_code:
                return rate_plan.rate_plan_id
        return None

    @property
    def is_house_use_booking(self):
        house_use_rate_plan_id = self._get_rate_plan_id(
            self.booking_aggregate, HOUSE_USE_RATE_PLAN_CODE
        )
        if self.room_stay.room_type_id == self.house_account_room_type_id:
            return True

        if self.room_stay.room_rate_plans:
            for room_rate_plan in self.room_stay.room_rate_plans:
                if room_rate_plan.rate_plan_id == house_use_rate_plan_id:
                    return True
        return False

    @property
    def is_daily_use_room(self):
        checkin_date = self.room_stay.actual_checkin_date or self.room_stay.checkin_date
        checkout_date = (
            self.room_stay.actual_checkout_date or self.room_stay.checkout_date
        )
        return checkin_date.date() == checkout_date.date()
