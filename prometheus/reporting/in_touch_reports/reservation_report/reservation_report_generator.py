import os
from datetime import datetime

from treebo_commons.utils import dateutils

from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.in_touch_reports.reservation_report.reservation_report_aggregate import (
    InTouchReservationReportAggregate,
)


class InTouchReservationReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows for a given booking
    """

    REPORT_COLUMNS = [
        "hotel_code",
        "pms_hotel_id",
        "confirmation_number",
        "pms_visit_id",
        "booking_id",
        "reservation_date",
        "arrival_date",
        "departure_date",
        "cancellation_date",
        "reservation_status",
        "adults",
        "children",
        "is_walkin_booking",
        "room_revenue",
        "fnb_revenue",
        "other_revenue",
        "total_revenue",
        "stay_room_type",
        "nights",
        "booked_room_type",
        "channel_code",
        "market_code",
        "rate_plan_ids",
        "external_reference_id",
        "external_reference_type",
        "pms_company_id",
        "pms_company_name",
        "pms_travel_agent_id",
        "pms_travel_agent_name",
        "room_number",
        "pms_guest_id",
        "is_group_booking",
        "group_name",
        "insert_date",
        "update_time",
        "loyalty_code",
        "loyalty_number",
        "adult_free_count",
        "child_free_count",
        "purpose_tracking",
        "market_tracking",
        "hom_tracking",
        "currency",
        "free_rate_ind",
        "house_use_rate_ind",
        "rooms_count",
        "tars_reservation_number",
        "is_tars_booking",
        "program_id",
        "program_name",
        "program_expiry_date",
        "memebership_id",
        "tars_cancellation_date",
        "reservation_share",
        "is_house_use_booking",
        "is_daily_use_room",
    ]

    def __init__(
        self,
        booking_aggregate,
        bill_aggregate,
        hotel_aggregate,
        house_account_room_type_id,
    ):
        self.hotel_aggregate = hotel_aggregate
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.house_account_room_type_id = house_account_room_type_id

    def generate(self):
        return [
            InTouchReservationReportAggregate(
                self.booking_aggregate,
                self.bill_aggregate,
                self.hotel_aggregate,
                room_stay,
                self.house_account_room_type_id,
            )
            for room_stay in self.booking_aggregate.get_all_room_stays()
            if not room_stay.deleted
        ]

    @staticmethod
    def generate_in_touch_reservation_report_file_name(report_date, extension='csv'):
        hotel_business_date_with_current_timestamp = datetime.combine(
            dateutils.ymd_str_to_date(report_date), dateutils.current_datetime().time()
        )
        hotel_business_datetime = datetime.strftime(
            hotel_business_date_with_current_timestamp, "%Y-%m-%d-%H:%M:%S"
        )
        file_name = f"/reservation_{hotel_business_datetime}.{extension}"

        return os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp") + file_name

    @staticmethod
    def get_default_expiration_time():
        return 604800  # 7 days
