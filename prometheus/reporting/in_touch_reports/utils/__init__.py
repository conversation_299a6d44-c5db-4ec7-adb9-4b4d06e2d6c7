# putting this in common place, for single touch point
from ths_common.constants.booking_constants import ProfileTypes


def build_pms_profile_id(booking_customer, booking_aggregate):
    if booking_customer.profile_type == ProfileTypes.CORPORATE:
        return booking_customer.reference_id
    value = '{0}|{1}'.format(
        booking_aggregate.booking.booking_id, booking_customer.customer_id
    )
    return value
