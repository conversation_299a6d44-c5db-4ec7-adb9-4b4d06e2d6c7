import os
from datetime import datetime

from treebo_commons.utils import dateutils

from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.in_touch_reports.inventory_report.inventory_report_aggregate import (
    InTouchInventoryReportAggregate,
)


class InTouchInventoryReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows
    """

    REPORT_COLUMNS = [
        "room_type",
        "date",
        "inventory",
        "out_of_order_rooms",
        "out_of_service_rooms",
        "total_rooms",
        "hotel_open",
    ]

    def __init__(
        self,
        room_type_inventories_count,
        room_type_id,
        inventory_date,
        housekeeping_room_status_count,
        total_rooms,
        ooo_rooms,
    ):
        self.room_type_inventories_count = room_type_inventories_count
        self.room_type_id = room_type_id
        self.inventory_date = inventory_date
        self.housekeeping_room_status_count = housekeeping_room_status_count
        self.total_rooms = total_rooms
        self.ooo_rooms = ooo_rooms

    def generate(self):
        return [
            InTouchInventoryReportAggregate(
                self.room_type_inventories_count,
                self.room_type_id,
                self.inventory_date,
                self.housekeeping_room_status_count,
                self.total_rooms,
                self.ooo_rooms,
            )
        ]

    @staticmethod
    def generate_in_touch_inventory_report_file_name(report_date, extension='csv'):
        hotel_business_date_with_current_timestamp = datetime.combine(
            dateutils.ymd_str_to_date(report_date), dateutils.current_datetime().time()
        )
        hotel_business_datetime = datetime.strftime(
            hotel_business_date_with_current_timestamp, "%Y-%m-%d-%H:%M:%S"
        )
        file_name = f"/inventory_{hotel_business_datetime}.{extension}"

        return os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp") + file_name

    @staticmethod
    def get_default_expiration_time():
        return 604800  # 7 days
