from typing import List

from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.reporting.in_touch_reports.dtos.customer_dto import CustomerDto
from prometheus.reporting.in_touch_reports.profile_report.profile_report_generator import (
    InTouchProfileReportGenerator,
)
from prometheus.reporting.in_touch_reports.utils import build_pms_profile_id
from prometheus.reporting.reporting_service import ReportingApplicationService
from prometheus.reporting.utils import CsvWriter
from ths_common.constants.booking_constants import ProfileTypes
from ths_common.utils.collectionutils import chunks


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        ReportingApplicationService,
        HotelRepository,
    ]
)
class ProfileReportingService(object):
    def __init__(
        self,
        booking_repository,
        bill_repository,
        reporting_application_service,
        hotel_repo,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.reporting_application_service = reporting_application_service
        self.hotel_repo = hotel_repo

    @staticmethod
    def get_unique_bookings(booking_aggregates):
        booking_ids = set()
        unique_booking_aggregates = []
        for booking_aggregate in booking_aggregates:
            if booking_aggregate.booking_id not in booking_ids:
                booking_ids.add(booking_aggregate.booking_id)
                unique_booking_aggregates.append(booking_aggregate)
        return unique_booking_aggregates

    def profile_booking_aggregates(
        self, report_date, hotel_aggregate=None
    ) -> List[BookingAggregate]:
        bookings = self.booking_repository.in_touch_reservation_report_query(
            report_date, hotel_aggregate.hotel_id
        )
        future_bookings = (
            self.booking_repository.in_touch_future_reservation_report_query(
                report_date, hotel_aggregate.hotel_id
            )
        )
        created_or_modified_charge_bill_ids = (
            self.bill_repository.in_touch_charge_query(
                report_date, hotel_aggregate.hotel_id
            )
        )
        created_or_modified_payment_bill_ids = (
            self.bill_repository.in_touch_payment_query(
                report_date, hotel_aggregate.hotel_id
            )
        )
        bill_ids = (
            created_or_modified_charge_bill_ids + created_or_modified_payment_bill_ids
        )
        bill_ids = [x[0] for x in bill_ids]

        bookings_with_modified_charge_and_payments = (
            self.booking_repository.load_for_bill_ids_with_yield_per(
                list(set(bill_ids))
            )
        )
        bookings = (
            bookings + future_bookings + bookings_with_modified_charge_and_payments
        )
        bookings = self.get_unique_bookings(bookings)
        return bookings

    def _base_report_data(self, report_date, hotel_aggregate):
        profile_booking_aggregates = self.profile_booking_aggregates(
            report_date, hotel_aggregate
        )
        return profile_booking_aggregates

    def _generate_csv_report(self, booking_aggregates, csv_writer):
        customers = []
        for booking_aggregate in booking_aggregates:
            booking_customers = [customer for customer in booking_aggregate.customers]
            customer_dtos = [
                self.extract_customer_information(customer, booking_aggregate)
                for customer in booking_customers
            ]
            customers.extend(customer_dtos)
        unique_customers = list(
            {customer.pms_profile_id: customer for customer in customers}.values()
        )
        report_aggregates = InTouchProfileReportGenerator(unique_customers).generate()
        csv_writer.write_aggregates(
            report_aggregates, InTouchProfileReportGenerator.REPORT_COLUMNS
        )
        return report_aggregates

    def generate_csv_report(self, report_date, hotel_aggregate=None):
        booking_aggregates = self._base_report_data(report_date, hotel_aggregate)
        file_path = (
            InTouchProfileReportGenerator.generate_in_touch_profile_report_file_name(
                report_date
            )
        )
        folder_path = f"{get_current_tenant_id()}/in-touch-data-files/{hotel_aggregate.hotel_id}/{report_date}/"
        report_aggregates = []
        with CsvWriter(file_path) as csv_writer:
            for bookings in chunks(booking_aggregates, 1000):
                report_aggregates.extend(
                    self._generate_csv_report(bookings, csv_writer)
                )

            AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                folder_path,
                csv_writer.file_path,
                InTouchProfileReportGenerator.get_default_expiration_time(),
            )

        return report_aggregates

    @staticmethod
    def get_profile_type(booking_customer, booking_aggregate):
        if booking_customer.profile_type == ProfileTypes.CORPORATE:
            if (
                booking_aggregate.booking.travel_agent_details
                and booking_aggregate.booking.travel_agent_details.legal_details
                and booking_customer.reference_id
                == booking_aggregate.booking.travel_agent_details.legal_details.external_reference_id
            ):
                return "Travel Agent"

        return (
            booking_customer.profile_type.value
            if booking_customer.profile_type
            else ProfileTypes.INDIVIDUAL.value
        )

    def extract_customer_information(self, booking_customer, booking_aggregate):
        profile_type = self.get_profile_type(booking_customer, booking_aggregate)
        return CustomerDto(
            pms_profile_id=build_pms_profile_id(booking_customer, booking_aggregate),
            profile_type=profile_type,
            name=''.join(
                filter(
                    None,
                    (
                        booking_customer.name.first_name,
                        booking_customer.name.last_name,
                    ),
                )
            ),
            title=booking_customer.salutation,
            first_name=booking_customer.name.first_name,
            last_name=booking_customer.name.last_name,
            legal_name=booking_customer.gst_details
            and booking_customer.gst_details.legal_name,
            address=booking_customer.address,  # ?
            address_line_1=booking_customer.address
            and booking_customer.address.field_1,
            address_line_2=booking_customer.address
            and booking_customer.address.field_2,
            city=booking_customer.address and booking_customer.address.city,
            state=booking_customer.address and booking_customer.address.state,
            zip_code=booking_customer.address and booking_customer.address.pincode,
            country=booking_customer.address and booking_customer.address.country,
            nationality=booking_customer.nationality,
            phone=booking_customer.phone,
            email=booking_customer.email,
            date_of_birth=booking_customer.date_of_birth,
            gender=booking_customer.gender,
            membership_id=(
                booking_customer.loyalty_program_details
                and booking_customer.loyalty_program_details.membership_number
            ),
            membership_type=(
                booking_customer.loyalty_program_details
                and booking_customer.loyalty_program_details.membership_level
            ),
            external_profile_identifier=booking_customer.external_ref_id,
            is_vip=booking_customer.is_vip,
            insert_date=booking_customer.created_at,
            update_date=booking_customer.modified_at,
            doc_type=booking_customer.id_proof
            and booking_customer.id_proof.id_proof_type,
            doc_id=booking_customer.id_proof and booking_customer.id_proof.id_number,
            doc_issue_location=booking_customer.id_proof
            and booking_customer.id_proof.issued_place,
            doc_issue_country_code=booking_customer.id_proof
            and booking_customer.id_proof.id_proof_country_code,
            is_primary_address=booking_customer.is_primary,
            effective_date=booking_customer.id_proof
            and booking_customer.id_proof.issued_date,
        )
