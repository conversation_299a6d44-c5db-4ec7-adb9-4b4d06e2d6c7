import os
from datetime import datetime

from treebo_commons.utils import dateutils

from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.in_touch_reports.profile_report.profile_report_aggregate import (
    InTouchProfileReportAggregate,
)


class InTouchProfileReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows for a given booking
    """

    REPORT_COLUMNS = [
        "pms_profile_id",  # pms_profile_id
        "profile_type",  # profile_type
        "pms_profile_id_context",  # pms_profile_id_context
        "legal_name",  # legal_name
        "company_name",  # legal_name
        "name",  # first_name + last_name
        "title",  # salutation
        "first_name",  #
        "last_name",  #
        "address",  # profile_type
        "address_line_1",  # addr_field1
        "address_line_2",  # addr_field2
        "address_line_3",  #
        "city",  # addr_city
        "state",  # addr_state
        "zip_code",  # pincode
        "country",  # addr_country
        "nationality",  # nationality
        "phone",  # phone
        "email",  # email
        "date_of_birth",  # date_of_birth
        "gender",  # gender
        "membership_id",  #
        "membership_type",  #
        "external_profile_identifier",  #
        "insert_date",
        "update_date",
        "is_vip",
        "code",
        "doc_type",  # id_proof_type
        "doc_id",  # id_number
        "doc_issue_location",  # id_proof_issued_place
        "doc_issue_country_code",  # id_proof_country_code
        "third_party_communication",  # value="No"
        "receive_promotion",  # value="No"
        "is_primary_address",  # value="Yes"
        "effective_date",  # id_proof_issued_date
    ]

    def __init__(self, booking_customers):
        self.booking_customers = booking_customers

    def generate(self):
        return [
            InTouchProfileReportAggregate(booking_customer)
            for booking_customer in self.booking_customers
        ]

    @staticmethod
    def generate_in_touch_profile_report_file_name(report_date, extension='csv'):
        hotel_business_date_with_current_timestamp = datetime.combine(
            dateutils.ymd_str_to_date(report_date), dateutils.current_datetime().time()
        )
        hotel_business_datetime = datetime.strftime(
            hotel_business_date_with_current_timestamp, "%Y-%m-%d-%H:%M:%S"
        )
        file_name = f"/profile_{hotel_business_datetime}.{extension}"

        return os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp") + file_name

    @staticmethod
    def get_default_expiration_time():
        return 604800  # 7 days
