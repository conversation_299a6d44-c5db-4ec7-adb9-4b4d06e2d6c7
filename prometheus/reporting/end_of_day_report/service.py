import logging

from object_registry import register_instance
from prometheus.async_job.job_registry import JobRegistry
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.reporting.flash_manager_report.service import (
    FlashManagerReportingService,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from prometheus.reporting.trail_balance_report.service import (
    TrailBalanceReportingService,
)
from ths_common.constants.scheduled_job_constants import JobName

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        JobSchedulerService,
        JobRegistry,
        TrailBalanceReportingService,
        FlashManagerReportingService,
        ReportingApplicationService,
    ]
)
class EndOfDayReportService(object):
    def __init__(
        self,
        job_scheduler_service,
        job_registry,
        trail_balance_reporting_service,
        flash_manager_report_service,
        reporting_application_service,
    ):
        self.job_scheduler_service = job_scheduler_service
        self.trail_balance_reporting_service = trail_balance_reporting_service
        self.flash_manager_report_service = flash_manager_report_service
        self.reporting_application_service = reporting_application_service

        job_registry.register(
            JobName.GENERATE_TRIAL_BALANCE_REPORT.value,
            self.trail_balance_reporting_service.generate_report_summary_after_night_audit,
        )
        job_registry.register(
            JobName.GENERATE_FLASH_MANAGER_REPORT.value,
            self.flash_manager_report_service.generate_report_summary_after_night_audit,
        )
        job_registry.register(
            JobName.SCHEDULE_FINANCIAL_DATA_SYNC.value,
            self.reporting_application_service.schedule_financial_data_sync_generation,
        )

    def schedule_report_generations(self, start_date, end_date, hotel_id):
        self.job_scheduler_service.schedule_trial_balance_report_generation(
            start_date, end_date, hotel_id
        )
        self.job_scheduler_service.schedule_flash_manager_report_generation(
            start_date, hotel_id
        )

    def schedule_financial_data_sync(self, report_date, hotel_id):
        self.job_scheduler_service.schedule_financial_data_sync(report_date, hotel_id)
