import logging

from object_registry import locate_instance
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.dtos.finance_erp_dtos import BookingDetailsDto
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.reporting.accounts_receivable_reporting.constants import ar_context
from prometheus.reporting.accounts_receivable_reporting.external_ar_reports.payment_gateway_report.financial_report_aggregate import (
    FinancialReportAggregate,
)
from prometheus.reporting.base_report_generator import BaseReportGenerator
from ths_common.constants.billing_constants import PaymentModes
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.tenant_settings_constants import TenantSettingName

logger = logging.getLogger(__name__)


class PaymentDataReportGenerator(BaseReportGenerator):
    DATA_PUSH_AFTER_CHECKOUT = 'after_checkout'
    DATA_PUSH_AFTER_NA_POSTING = 'after_posting'

    def __init__(self, **aggregates):
        super().__init__(**aggregates)
        self.booking_repository = locate_instance(BookingRepository)
        self.bill_repository = locate_instance(BillRepository)
        self.tenant_settings = locate_instance(TenantSettings)

    def generate(self):
        pg_report_aggregates = []
        date = ar_context.posting_date
        (
            payment_modes_to_push_after_night_audit,
            payment_modes_to_push_after_checkout,
        ) = self._payment_modes_to_push()
        if (
            not payment_modes_to_push_after_night_audit
            and not payment_modes_to_push_after_checkout
        ):
            raise Exception(
                "Payment Modes for AR Reports not defined in Tenant Settings"
            )
        (
            payments_to_be_pushed_based_on_checkout,
            payments_posted_on_report_date,
        ) = ([], [])
        if payment_modes_to_push_after_checkout:
            payments_to_be_pushed_based_on_checkout = (
                self._get_payments_on_booking_checkout_basis(
                    payment_modes_to_push_after_checkout
                )
            )
        if (
            payment_modes_to_push_after_night_audit
            or payment_modes_to_push_after_checkout
        ):
            payments_posted_on_report_date = self.bill_repository.get_payments_posted_on_given_date_for_given_pay_modes(
                date,
                payment_modes_to_push_after_night_audit
                + payment_modes_to_push_after_checkout,
            )
        payments_data = (
            payments_to_be_pushed_based_on_checkout + payments_posted_on_report_date
        )
        bill_ids = list({payment.bill_id for payment in payments_data})
        bookings_data = self.booking_repository.get_booking_data_for_given_bills(
            bill_ids
        )
        bill_id_to_booking_data_mapping = {
            booking.bill_id: booking for booking in bookings_data
        }

        payments_to_be_pushed_based_on_na_posting = (
            self._get_payments_to_be_pushed_based_on_na_posting(
                payments_posted_on_report_date,
                bill_id_to_booking_data_mapping,
                payment_modes_to_push_after_checkout,
            )
        )
        payments_data_to_push = set(
            payments_to_be_pushed_based_on_checkout
            + payments_to_be_pushed_based_on_na_posting
        )

        for payment_data in payments_data_to_push:
            booking_data_for_payment = bill_id_to_booking_data_mapping.get(
                payment_data.bill_id
            )
            if (
                payment_data.payment_mode == PaymentModes.PAID_AT_OTA
                and not booking_data_for_payment.travel_agent_details
            ):
                continue
            pg_report_aggregates.append(
                FinancialReportAggregate(
                    payment_data,
                    booking_data_for_payment,
                )
            )
        return pg_report_aggregates

    def _payment_modes_to_push(self):
        debtor_config = self.tenant_settings.get_setting_value(
            TenantSettingName.AR_DEBTOR_CONFIG.value,
        )
        (
            payment_modes_to_push_after_night_audit,
            payment_modes_to_push_after_checkout,
        ) = (set(), set())
        for config in debtor_config:
            if config.get('data_push_trigger') == self.DATA_PUSH_AFTER_CHECKOUT:
                payment_modes_to_push_after_checkout.add(config.get('payment_mode'))
            elif config.get('data_push_trigger') == self.DATA_PUSH_AFTER_NA_POSTING:
                payment_modes_to_push_after_night_audit.add(config.get('payment_mode'))
        return list(payment_modes_to_push_after_night_audit), list(
            payment_modes_to_push_after_checkout
        )

    def _get_payments_on_booking_checkout_basis(self, payment_modes):
        bookings_data = self._get_bookings_based_on_checkout()
        bill_ids = [booking_data.bill_id for booking_data in bookings_data]
        payments = (
            self.bill_repository.get_payments_under_given_payment_modes_for_bills(
                bill_ids, payment_modes
            )
        )
        return payments

    def _get_bookings_based_on_checkout(self):
        date = ar_context.posting_date
        bookings = self.booking_repository.get_bookings_with_expected_checkout_date(
            date,
            status=[
                BookingStatus.CHECKED_OUT.value,
                BookingStatus.NOSHOW.value,
                BookingStatus.CANCELLED.value,
            ],
        )
        bookings = [BookingDetailsDto(booking) for booking in bookings]
        return bookings

    @staticmethod
    def _get_payments_to_be_pushed_based_on_na_posting(
        payments_posted_on_report_date,
        bill_id_to_booking_data_mapping,
        payment_modes_to_push_after_checkout,
    ):
        filtered_payments = []
        for payment in payments_posted_on_report_date:
            booking = bill_id_to_booking_data_mapping.get(payment.bill_id)
            if (
                payment.payment_mode in payment_modes_to_push_after_checkout
                and payment.payment_creation_date > booking.checkout_date_time
            ):
                filtered_payments.append(payment)
            elif payment.payment_mode not in payment_modes_to_push_after_checkout:
                filtered_payments.append(payment)

        return filtered_payments
