from _decimal import Decimal

from prometheus.reporting.base_report_aggregate import BaseReportAggregate


class FinancialReportAggregate(BaseReportAggregate):
    def __init__(
        self,
        financial_data=None,
        booking_data=None,
    ):
        self.financial_data = financial_data
        self.booking_data = booking_data

    @property
    def posted_date(self):
        return self.financial_data.posted_date

    @property
    def payment_date(self):
        return self.financial_data.payment_date

    @property
    def payment_reference_number(self):
        return self.financial_data.payment_ref_id

    @property
    def reference_id(self):
        return f"{self.financial_data.bill_id}/{self.financial_data.payment_id}/{self.financial_data.payment_split_id}"

    @property
    def payment_amount(self):
        payment_amount = Decimal(self.financial_data.amount)
        return round(payment_amount, 2)

    @property
    def payment_type(self):
        return self.financial_data.payment_type

    @property
    def payment_mode(self):
        return self.financial_data.payment_mode

    @property
    def hotel_code(self):
        return self.booking_data.hotel_id

    @property
    def booking_reference_number(self):
        return self.booking_data.reference_number

    @property
    def ta_ref_id(self):
        if self.booking_data.travel_agent_details:
            return self.booking_data.travel_agent_details.get('legal_details', {}).get(
                'external_reference_id', None
            )
        return None
