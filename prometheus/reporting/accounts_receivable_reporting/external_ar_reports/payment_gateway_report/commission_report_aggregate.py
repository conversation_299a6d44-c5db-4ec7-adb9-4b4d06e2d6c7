from _decimal import Decimal

from prometheus.reporting.accounts_receivable_reporting.external_ar_reports.payment_gateway_report.financial_report_aggregate import (
    FinancialReportAggregate,
)


class CommissionReportAggregate(FinancialReportAggregate):
    def __init__(self, commission_data=None, booking_data=None, invoice_number=None):
        self.invoice_number = invoice_number
        super().__init__(financial_data=commission_data, booking_data=booking_data)

    @property
    def reference_id(self):
        if self.invoice_number:
            return self.invoice_number
        return f"{self.financial_data.bill_id}/{self.financial_data.payment_ref_id}"
