from prometheus.reporting.accounts_receivable_reporting.external_ar_reports.payment_gateway_report.ota_payment_data_report_generator import (
    OTACommissionDataReportGenerator,
)
from prometheus.reporting.accounts_receivable_reporting.external_ar_reports.payment_gateway_report.payment_data_report_generator import (
    PaymentDataReportGenerator,
)
from prometheus.reporting.base_report_generator import BaseReportGenerator


class ARReportsGenerator(BaseReportGenerator):
    """
    Responsible for generating AR reports
    """

    FP_EVENT_TYPE = 'payments_report_push_ar'
    ROUTING_KEY = 'reporting.ar'

    def __init__(
        self,
    ):
        pass

    def generate(self):
        pass

    def generate_payment_reports(self):
        return PaymentDataReportGenerator().generate()

    def generate_ota_commission_reports(self):
        return OTACommissionDataReportGenerator().generate()
