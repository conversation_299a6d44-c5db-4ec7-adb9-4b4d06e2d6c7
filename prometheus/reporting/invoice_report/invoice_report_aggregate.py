from collections import defaultdict
from typing import Sequence

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus.domain.booking.exceptions import CustomerNotFound
from prometheus.reporting.base_report_aggregate import BaseReportAggregate
from ths_common.constants.billing_constants import (
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingStatus, ProfileTypes
from ths_common.value_objects import Address, GSTDetails


class InvoiceReportAggregate(BaseReportAggregate):
    """
    Each aggregate object is a row in the invoice report
    """

    def __init__(
        self,
        booking_aggregate,
        bill_aggregate,
        hotel_aggregate,
        room_stay,
        invoice_aggregate,
        hotel_invoice_aggregate,
        credit_note_aggregate,
        hotel_credit_note_aggregate,
        grouped_charge_item,
        room_type_map,
        unique_ref_id,
    ):
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.hotel_aggregate = hotel_aggregate
        self.invoice_aggregate = invoice_aggregate
        self.hotel_invoice_aggregate = hotel_invoice_aggregate
        self.credit_note_aggregate = credit_note_aggregate
        self.hotel_credit_note_aggregate = hotel_credit_note_aggregate
        self.room_stay = room_stay
        self.grouped_charge_item = grouped_charge_item
        self.room_type_map = room_type_map
        self.unique_ref_id = unique_ref_id

    CREDIT_NOTE = 'credit_note'
    INVOICE = 'invoice'

    @property
    def currency(self):
        return (
            self.bill_aggregate.bill.base_currency
            if self.bill_aggregate.bill.base_currency
            else CurrencyType.INR
        )

    @property
    def hotel_id(self):
        return self.hotel_aggregate.hotel.hotel_id

    @property
    def hotel_trade_name(self):
        return self.hotel_aggregate.hotel.name

    @property
    def city_name(self):
        return self.hotel_aggregate.hotel.city.name

    @property
    def gst_details(self):
        if self.is_credit_note_line_item():
            if not self.credit_note_aggregate.credit_note.issued_by.gst_details:
                return GSTDetails(
                    legal_name='',
                    gstin_num='',
                    address=InvoiceReportAggregate.empty_address(),
                )
            return self.credit_note_aggregate.credit_note.issued_by.gst_details
        if not self.invoice_aggregate.invoice.issued_by.gst_details:
            return GSTDetails(
                legal_name='',
                gstin_num='',
                address=InvoiceReportAggregate.empty_address(),
            )
        return self.invoice_aggregate.invoice.issued_by.gst_details

    @staticmethod
    def empty_address():
        return Address(
            field_1='', field_2='', city='', state='', country='', pincode=''
        )

    @property
    def gst_address(self):
        if not self.gst_details.address:
            return InvoiceReportAggregate.empty_address()
        return self.gst_details.address

    @property
    def billed_by_legal_name(self):
        return self.gst_details.legal_name

    @property
    def billed_by_gstin(self):
        return self.gst_details.gstin_num

    @property
    def billed_by_address(self):
        return self.gst_address.address

    @property
    def billed_by_city(self):
        return self.gst_address.city

    @property
    def billed_by_state(self):
        return self.gst_address.state

    @property
    def billed_by_pincode(self):
        return self.gst_address.pincode

    @property
    def room_stay_id(self):
        return self.room_stay.room_stay_id

    @property
    def unique_room_booking_id(self):
        return self.grouped_charge_item.get('unique_room_booking_id', '')

    @property
    def booking_id(self):
        return self.booking_aggregate.booking.booking_id

    @property
    def reference_number(self):
        return self.booking_aggregate.booking.reference_number

    @property
    def booking_status(self):
        return self.booking_aggregate.booking.status

    @property
    def booking_created_date(self):
        return dateutils.to_date(self.booking_aggregate.booking.created_at)

    @property
    def room_stay_checkin_date(self):
        return (
            dateutils.to_date(self.room_stay.actual_checkin_date)
            if self.room_stay.actual_checkin_date
            else dateutils.to_date(self.room_stay.checkin_date)
        )

    @property
    def room_stay_checkout_date(self):
        return (
            dateutils.to_date(self.room_stay.actual_checkout_date)
            if self.room_stay.actual_checkout_date
            else dateutils.to_date(self.room_stay.checkout_date)
        )

    @property
    def stayed_room_nights(self):
        if self.is_booking_cancelled_noshow():
            return None
        allocation = self.room_number_to_allocation_map().get(self.room_number)
        if not allocation:
            return None
        if dateutils.to_date(allocation.stay_end):
            stayed_room_nights = (
                dateutils.to_date(allocation.stay_end)
                - dateutils.to_date(allocation.stay_start)
            ).days
        else:
            stayed_room_nights = (
                dateutils.to_date(self.invoice_date)
                - dateutils.to_date(allocation.stay_start)
            ).days
        return stayed_room_nights

    @property
    def room_number(self):
        if self.is_booking_cancelled_noshow():
            return None
        return self.grouped_charge_item['room_number']

    @property
    def room_type_ids(self):
        if self.is_booking_cancelled_noshow():
            return None
        return self.grouped_charge_item['room_type_id']

    @property
    def room_types(self):
        if self.is_booking_cancelled_noshow():
            return None
        try:
            room_type_ids = self.room_type_ids.split(', ')
            room_type_names = ", ".join(
                {
                    self.room_type_map[room_type_id].room_type.type
                    for room_type_id in room_type_ids
                }
            )
            return room_type_names
        except (KeyError, AttributeError):
            return None

    @property
    def occupancy(self):
        if self.is_booking_cancelled_noshow():
            return 0
        return self.room_stay.max_occupancy

    @property
    def sorted_guest_names(self):
        customers_list = []
        for gs in self.room_stay.get_all_guest_allocations():
            try:
                customers_list.append(self.booking_aggregate.get_customer(gs.guest_id))
            except CustomerNotFound:
                continue
        return ", ".join([customer.name.full_name for customer in customers_list])

    @property
    def invoice_posttax_amount(self):
        if self.is_credit_note_line_item():
            return (
                -self.credit_note_aggregate.credit_note.posttax_amount.amount
                if not self.is_credit_note_cancelled()
                else Money(0, self.currency).amount
            )
        return (
            self.invoice_aggregate.invoice.posttax_amount.amount
            if not self.is_invoice_cancelled()
            else Money(0, self.currency).amount
        )

    @property
    def issued_by_type(self):
        if self.is_credit_note_line_item():
            return self.credit_note_aggregate.credit_note.issued_by_type.value
        return self.invoice_aggregate.invoice.issued_by_type.value

    @property
    def issued_to_type(self):
        if self.is_credit_note_line_item():
            return self.credit_note_aggregate.credit_note.issued_to_type.value
        return self.invoice_aggregate.invoice.issued_to_type.value

    @property
    def invoice_number(self):
        if self.is_credit_note_line_item():
            return self.credit_note_aggregate.credit_note.credit_note_number
        return self.invoice_aggregate.invoice_number

    @property
    def hotel_invoice_number(self):
        if self.is_credit_note_line_item():
            return (
                self.hotel_credit_note_aggregate.credit_note.credit_note_number
                if self.hotel_credit_note_aggregate
                else None
            )
        return (
            self.hotel_invoice_aggregate.invoice.invoice_number
            if self.hotel_invoice_aggregate
            else None
        )

    @property
    def invoice_date(self):
        if self.is_credit_note_line_item():
            return self.credit_note_aggregate.credit_note.credit_note_date
        return self.invoice_aggregate.invoice.invoice_date

    @property
    def original_invoice_number(self):
        return (
            self.invoice_aggregate.invoice_number
            if self.is_credit_note_line_item()
            else None
        )

    @property
    def original_invoice_date(self):
        return (
            self.invoice_aggregate.invoice.invoice_date
            if self.is_credit_note_line_item()
            else None
        )

    @property
    def type(self):
        try:
            return self.grouped_charge_item['charge_type'].value
        except AttributeError:
            return ''

    @property
    def sku_name(self):
        return self.grouped_charge_item['sku_name']

    @property
    def charge_components(self):
        try:
            return self.grouped_charge_item['charge_components']
        except (AttributeError, KeyError):
            return ''

    @property
    def charge_date(self):
        try:
            return self.grouped_charge_item['charge_date']
        except (AttributeError, KeyError):
            return ''

    @property
    def invoice_charge_remarks(self):
        comments = self.grouped_charge_item.get('comments', [])
        return ", ".join(comments)

    @property
    def billed_entity_category(self):
        billed_entity = self.bill_aggregate.get_billed_entity(
            self.invoice_aggregate.billed_entity_id
        )
        return billed_entity.category

    @property
    def created_on(self):
        return dateutils.to_date(self.invoice_aggregate.invoice.created_at)

    @property
    def hsn_code(self):
        return 'HSN:' + self.grouped_charge_item['hsn_code']

    @property
    def pretax_amount(self):
        return self.grouped_charge_item['pretax_amount'].amount

    @property
    def tax_amount(self):
        return self.grouped_charge_item['tax_amount'].amount

    @property
    def cgst_amount(self):
        return self.grouped_charge_item['cgst_amount'].amount

    @property
    def item_cgst_percent(self):
        return self.grouped_charge_item['cgst_percent']

    @property
    def sgst_amount(self):
        return self.grouped_charge_item['sgst_amount'].amount

    @property
    def item_sgst_percent(self):
        return self.grouped_charge_item['sgst_percent']

    @property
    def igst_amount(self):
        return self.grouped_charge_item['igst_amount'].amount

    @property
    def item_igst_percent(self):
        return self.grouped_charge_item['igst_percent']

    @property
    def flood_cess_amount(self):
        return self.grouped_charge_item['flood_cess_amount'].amount

    @property
    def flood_cess_percentage(self):
        return self.grouped_charge_item['flood_cess_percentage']

    @property
    def tax_percentage(self):
        return (
            self.item_cgst_percent
            + self.item_sgst_percent
            + self.item_igst_percent
            + self.flood_cess_percentage
        )

    @property
    def post_tax_amount(self):
        return self.grouped_charge_item['posttax_amount'].amount

    @property
    def bill_to_details(self):
        if self.is_credit_note_line_item():
            return self.credit_note_aggregate.credit_note.issued_to
        return self.invoice_aggregate.invoice.bill_to

    @property
    def billed_to_legal_name(self):
        try:
            return self.bill_to_details.name
        except AttributeError:
            return None

    @property
    def billed_to_gstin_num(self):
        try:
            return self.bill_to_details.gstin_num
        except AttributeError:
            return None

    @property
    def billed_to_address(self):
        try:
            return self.bill_to_details.address.address
        except AttributeError:
            return None

    @property
    def billed_to_city(self):
        try:
            return self.bill_to_details.address.city
        except AttributeError:
            return None

    @property
    def billed_to_state(self):
        try:
            return self.bill_to_details.address.state
        except AttributeError:
            return None

    @property
    def billed_to_pincode(self):
        try:
            return self.bill_to_details.address.pincode
        except AttributeError:
            return None

    @property
    def channel_code(self):
        return self.booking_aggregate.booking.source.channel_code

    @property
    def subchannel_code(self):
        return self.booking_aggregate.booking.source.subchannel_code

    @property
    def corporate_id(self):
        return (
            self.bill_to_details.external_ref_id
            if self.bill_to_details.external_ref_id
            else self.booking_aggregate.get_booking_owner().reference_id
        )

    @property
    def booking_owner_legal_entity_id(self):
        if self.booking_aggregate.booking.travel_agent_details is not None:
            return (
                self.booking_aggregate.booking.travel_agent_details.legal_details.external_reference_id
            )
        if self.booking_aggregate.booking.company_details is not None:
            return (
                self.booking_aggregate.booking.company_details.legal_details.external_reference_id
            )
        return self.booking_aggregate.get_booking_owner().reference_id

    @property
    def booking_owner_name(self):
        customer = self.booking_aggregate.get_booking_owner()
        return customer.name

    @property
    def paid_to_treebo(self):
        # keeping these receiver types for backward compatibility
        valid_payment_receiver_types = [
            PaymentReceiverTypes.TREEBO,
            PaymentReceiverTypes.OTA,
            PaymentReceiverTypes.CORPORATE,
        ]
        total_paid_to_treebo = self.total_paid_to_from_payments(
            valid_payment_receiver_types
        )
        return total_paid_to_treebo.amount

    @property
    def paid_to_hotel(self):
        # keeping these receiver types for backward compatibility
        valid_payment_receiver_types = [
            PaymentReceiverTypes.HOTEL,
            PaymentReceiverTypes.GUEST,
        ]
        total_paid_to_hotel = self.total_paid_to_from_payments(
            valid_payment_receiver_types
        )
        return total_paid_to_hotel.amount

    @property
    def payment_mode_bank_transfer_treebo(self):
        # keeping these receiver types for backward compatibility
        valid_payment_receiver_types = [
            PaymentReceiverTypes.TREEBO,
            PaymentReceiverTypes.OTA,
            PaymentReceiverTypes.CORPORATE,
        ]
        # keeping BANK_TRANSFER for backward compatibility
        total_paid_to_treebo = self.total_paid_to_from_payments(
            valid_payment_receiver_types,
            [PaymentModes.BANK_TRANSFER, PaymentModes.BANK_TRANSFER_TREEBO],
        )
        return total_paid_to_treebo.amount

    @property
    def payment_mode_bank_transfer_hotel(self):
        # keeping these receiver types for backward compatibility
        valid_payment_receiver_types = [
            PaymentReceiverTypes.HOTEL,
            PaymentReceiverTypes.GUEST,
        ]
        # keeping BANK_TRANSFER for backward compatibility
        total_paid_to_hotel = self.total_paid_to_from_payments(
            valid_payment_receiver_types,
            [PaymentModes.BANK_TRANSFER, PaymentModes.BANK_TRANSFER_HOTEL],
        )
        return total_paid_to_hotel.amount

    @property
    def payments(self):
        payments_group = defaultdict(Money)
        for mode in PaymentModes.all():
            payments_group[mode] = Money(
                0, self.currency
            ).amount  # should populate (report columns) automatically
        for payment in self.active_payments():
            if payment.payment_type == PaymentTypes.PAYMENT:
                payments_group[payment.payment_mode] += payment.amount.amount
            elif payment.payment_type == PaymentTypes.REFUND:
                payments_group[payment.payment_mode] -= payment.amount.amount
        return payments_group

    @property
    def invoice_status(self):
        return self.CREDIT_NOTE if self.is_credit_note_line_item() else self.INVOICE

    @property
    def invoice_charge_type(self):
        return self.invoice_aggregate.allowed_charge_types[0]

    @property
    def is_sez(self):
        return self.bill_to_details.is_sez

    @property
    def has_lut(self):
        return self.bill_to_details.has_lut

    def is_booking_cancelled_noshow(self):
        return self.booking_status in (BookingStatus.CANCELLED, BookingStatus.NOSHOW)

    def active_payments(self):
        return [
            payment
            for payment in self.bill_aggregate.payments
            if not payment.status == PaymentStatus.CANCELLED
        ]

    def total_paid_to_from_payments(
        self, payment_receiver_types: Sequence = None, payment_modes: Sequence = None
    ):
        def filter_check(p):
            matched_payment_type, matched_payment_mode = True, True
            if payment_receiver_types is not None:
                if p.payment_type == PaymentTypes.PAYMENT:
                    matched_payment_type = p.paid_to in payment_receiver_types
                else:
                    matched_payment_type = p.paid_by in payment_receiver_types
            if payment_modes is not None:
                matched_payment_mode = p.payment_mode in payment_modes
            return matched_payment_type and matched_payment_mode

        filtered_payments = [p for p in self.active_payments() if filter_check(p)]

        payments = [
            p.amount.amount
            for p in filtered_payments
            if p.payment_type == PaymentTypes.PAYMENT
        ]
        refunds = [
            p.amount.amount
            for p in filtered_payments
            if p.payment_type == PaymentTypes.REFUND
        ]
        return Money(sum(payments) - sum(refunds), self.currency)

    def is_credit_note_line_item(self):
        return self.grouped_charge_item.get('is_credit_note_line_item')

    def is_credit_note_cancelled(self):
        return self.credit_note_aggregate.credit_note.is_cancelled

    def is_invoice_cancelled(self):
        return self.invoice_aggregate.invoice.is_cancelled

    def room_number_to_allocation_map(self):
        return {
            room_allocation.room_no: room_allocation
            for room_allocation in self.room_stay.all_room_allocations(
                include_deleted=False
            )
        }
