import logging

from kombu import Exchange, Producer
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from prometheus.infrastructure.consumers.consumer_config import ReportingConfig
from prometheus.infrastructure.messaging.queue_service import BaseQueueService
from ths_common.exceptions import CRSException

logger = logging.getLogger(__name__)


class ReportingEvent:
    def __init__(self, body, routing_key):
        self.body = body
        self.routing_key = routing_key


@register_instance()
class ReportingJobPublisher(BaseQueueService):
    def _setup_entities(self):
        config = ReportingConfig()
        self._reporting_event_exchange = Exchange(
            config.exchange_name, type=config.exchange_type, durable=True
        )
        self._tenant_wise_producers = dict()
        for tenant_id, conn in self.tenant_wise_connection.items():
            self._tenant_wise_producers[tenant_id] = Producer(
                channel=conn.channel(), exchange=self._reporting_event_exchange
            )

    def publish(self, event):
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        self._initialize()

        payload = self.get_event_payload(event)
        routing_key = self.get_event_routing_key(event)
        logger.debug('Publishing event %s', payload)

        if not self._tenant_wise_producers[tenant_id]:
            raise CRSException(
                description="RMQ Producer not configured for tenant_id: {0}".format(
                    tenant_id
                )
            )

        self._publish(self._tenant_wise_producers[tenant_id], payload, routing_key)

    @staticmethod
    def get_event_payload(event):
        return event.body

    @staticmethod
    def get_event_routing_key(event):
        return event.routing_key
