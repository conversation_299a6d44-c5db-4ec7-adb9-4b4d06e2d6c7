import logging
from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories import RoomTypeRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.reporting.guest_inhouse_report.guest_inhouse_report_generator import (
    GuestInhouseReportGenerator,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from prometheus.reporting.utils import CsvWriter
from ths_common.constants.billing_constants import PaymentStatus, PaymentTypes
from ths_common.constants.booking_constants import BookingStatus
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        RoomTypeRepository,
        ReportingApplicationService,
    ]
)
class GuestInhouseReportingService(object):
    """
    Generic application service for reports/reporting
    """

    def __init__(
        self,
        booking_repository,
        bill_repository,
        hotel_repository,
        room_type_repository,
        reporting_application_service,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.room_type_repository = room_type_repository
        self.reporting_application_service = reporting_application_service

    @staticmethod
    def _fail_if_user_not_authorized_to_access_reports():
        return RuleEngine.action_allowed(
            action="access_report",
            facts=Facts(
                user_type=crs_context.user_data.user_type,
                hotel_context=crs_context.get_hotel_context(),
            ),
            fail_on_error=True,
        )

    def _base_report_data(self, start_date, end_date, hotel_aggregate):
        booking_aggregates_for_date_range = (
            self.booking_repository.guest_inhouse_report_query(
                dateutils.date_to_ymd_str(start_date),
                dateutils.date_to_ymd_str(end_date),
                hotel_aggregate.hotel_id,
            )
        )

        hotel_map = {hotel_aggregate.hotel_id: hotel_aggregate}
        room_type_map = self.room_type_repository.load_type_map()
        return booking_aggregates_for_date_range, hotel_map, room_type_map

    @set_hotel_context()
    def report_summary(
        self, start_date, end_date, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        booking_aggregates, hotel_map, room_type_map = self._base_report_data(
            start_date, end_date, hotel_aggregate
        )
        guest_inhouse_report_summary = {'summary': [], 'datewise_summary': []}
        for bookings in chunks(booking_aggregates, 1000):
            guest_inhouse_report_summary_chunk = self._generate_report_summary(
                bookings, hotel_map, room_type_map, start_date, end_date
            )
            guest_inhouse_report_summary['summary'].extend(
                guest_inhouse_report_summary_chunk['summary']
            )
            guest_inhouse_report_summary['datewise_summary'].extend(
                guest_inhouse_report_summary_chunk['datewise_summary']
            )

        return guest_inhouse_report_summary

    def _generate_report_summary(
        self, booking_aggregates, hotel_map, room_type_map, start_date, end_date
    ):
        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids,
            load_invoices_by_bill_id=True,
            hotel_map=hotel_map,
            room_type_map=room_type_map,
        )
        report_aggregates = []
        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            hotel_aggregate = aggregate_maps.hotel_map.get(
                booking_aggregate.booking.hotel_id
            )
            guest_inhouse_report_aggregates = GuestInhouseReportGenerator(
                booking_aggregate,
                bill_aggregate,
                hotel_aggregate,
                aggregate_maps.room_type_map,
                start_date=start_date,
                end_date=end_date,
            ).generate()
            if guest_inhouse_report_aggregates:
                report_aggregates.append(guest_inhouse_report_aggregates)

        return {
            'summary': self._channel_wise_report_summary(
                report_aggregates, start_date, end_date
            ),
            'datewise_summary': self._get_datewise_report_summary(
                report_aggregates, start_date, end_date
            ),
        }

    def _channel_wise_report_summary(self, report_aggregates, start_date, end_date):
        channel_wise_booking_summary = defaultdict(dict)
        for guest_inhouse_report_aggregates in report_aggregates:
            booking_counted = 0
            for guest_inhouse_report_aggregate in guest_inhouse_report_aggregates:
                channel = guest_inhouse_report_aggregate.get_channel
                post_tax_charges = guest_inhouse_report_aggregate.room_related_charges_posttax_between_stay_dates(
                    start_date, end_date
                )
                if post_tax_charges.amount == 0:
                    continue
                channel_wise_booking_summary[channel][
                    'total_booking_amount'
                ] = post_tax_charges + channel_wise_booking_summary[channel].get(
                    'total_booking_amount', 0
                )
                if not channel_wise_booking_summary[channel].get('number_of_guests'):
                    channel_wise_booking_summary[channel]['number_of_guests'] = {
                        'adults': 0,
                        'children': 0,
                    }
                channel_wise_booking_summary[channel]['number_of_guests']['adults'] = (
                    guest_inhouse_report_aggregate.adult_count
                    + channel_wise_booking_summary[channel]['number_of_guests'][
                        'adults'
                    ]
                )
                channel_wise_booking_summary[channel]['number_of_guests'][
                    'children'
                ] = (
                    guest_inhouse_report_aggregate.child_count
                    + channel_wise_booking_summary[channel]['number_of_guests'][
                        'children'
                    ]
                )
                channel_wise_booking_summary[channel]['channel_name'] = channel

                if booking_counted == 0:
                    booking_counted = 1
                    if not channel_wise_booking_summary[channel].get(
                        'number_of_bookings'
                    ):
                        channel_wise_booking_summary[channel]['number_of_bookings'] = 1
                    else:
                        channel_wise_booking_summary[channel]['number_of_bookings'] += 1

        return list(channel_wise_booking_summary.values())

    def _get_datewise_report_summary(self, report_aggregates, start_date, end_date):
        datewise_channel_wise_summary = defaultdict(lambda: defaultdict(dict))
        datewise_summary = []
        for guest_inhouse_report_aggregates in report_aggregates:
            booking_counted = 0
            for guest_inhouse_report_aggregate in guest_inhouse_report_aggregates:
                channel = guest_inhouse_report_aggregate.get_channel
                datewise_charge_amount = (
                    guest_inhouse_report_aggregate.date_wise_stay_charges_posttax
                )
                adult_count = guest_inhouse_report_aggregate.adult_count
                child_count = guest_inhouse_report_aggregate.child_count
                room_counted = 0
                guest_counted = 0
                for date, amount in datewise_charge_amount.items():
                    if not start_date <= dateutils.ymd_str_to_date(date) <= end_date:
                        continue
                    amount = datewise_charge_amount.get(date, 0)
                    booking_amount = amount + datewise_channel_wise_summary[date][
                        channel
                    ].get('total_booking_amount', 0)
                    if not datewise_channel_wise_summary[date][channel].get(
                        'number_of_guests'
                    ):
                        datewise_channel_wise_summary[date][channel][
                            'number_of_guests'
                        ] = {'adults': 0, 'children': 0}

                    datewise_channel_wise_summary[date][channel][
                        'total_booking_amount'
                    ] = booking_amount
                    datewise_channel_wise_summary[date][channel][
                        'date'
                    ] = dateutils.ymd_str_to_date(date)
                    datewise_channel_wise_summary[date][channel][
                        'channel_name'
                    ] = channel
                    if booking_counted == 0:
                        booking_counted = 1
                        if not datewise_channel_wise_summary[date][channel].get(
                            'booking_count'
                        ):
                            datewise_channel_wise_summary[date][channel][
                                'booking_count'
                            ] = 1
                        else:
                            datewise_channel_wise_summary[date][channel][
                                'booking_count'
                            ] += 1
                    if room_counted == 0:
                        room_counted = 1
                        if not datewise_channel_wise_summary[date][channel].get(
                            'number_of_rooms'
                        ):
                            datewise_channel_wise_summary[date][channel][
                                'number_of_rooms'
                            ] = 1
                        else:
                            datewise_channel_wise_summary[date][channel][
                                'number_of_rooms'
                            ] += 1
                    if guest_counted == 0:
                        guest_counted = 1
                        datewise_channel_wise_summary[date][channel][
                            'number_of_guests'
                        ]['adults'] = (
                            adult_count
                            + datewise_channel_wise_summary[date][channel][
                                'number_of_guests'
                            ]['adults']
                        )
                        datewise_channel_wise_summary[date][channel][
                            'number_of_guests'
                        ]['children'] = (
                            child_count
                            + datewise_channel_wise_summary[date][channel][
                                'number_of_guests'
                            ]['children']
                        )

        for date, summary_for_date in datewise_channel_wise_summary.items():
            for channel, booking_summary in summary_for_date.items():
                datewise_summary.append(booking_summary)

        return datewise_summary

    @set_hotel_context()
    def report_details(
        self, start_date, end_date, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        booking_aggregates, hotel_map, room_type_map = self._base_report_data(
            start_date, end_date, hotel_aggregate
        )

        channel_wise_booking_details = dict()
        for bookings in chunks(booking_aggregates, 1000):
            channel_wise_booking_details = self._generate_report_details(
                bookings, hotel_map, room_type_map, start_date, end_date
            )
        return {'reports': channel_wise_booking_details}

    def _generate_report_details(
        self, booking_aggregates, hotel_map, room_type_map, start_date, end_date
    ):
        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids,
            load_invoices_by_bill_id=True,
            hotel_map=hotel_map,
            room_type_map=room_type_map,
        )
        report_details_response = []
        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            hotel_aggregate = aggregate_maps.hotel_map.get(
                booking_aggregate.booking.hotel_id
            )

            billed_entity_account_payment_map = defaultdict(
                lambda: Money(0, bill_aggregate.bill.base_currency)
            )
            for payment in bill_aggregate.payments:
                if payment.status != PaymentStatus.CANCELLED:
                    for ps in payment.payment_splits:
                        if ps.payment_type == PaymentTypes.PAYMENT:
                            billed_entity_account_payment_map[
                                ps.billed_entity_account
                            ] += ps.amount
                        elif ps.payment_type == PaymentTypes.REFUND:
                            billed_entity_account_payment_map[
                                ps.billed_entity_account
                            ] += -ps.amount

            guest_inhouse_report_aggregates = GuestInhouseReportGenerator(
                booking_aggregate,
                bill_aggregate,
                hotel_aggregate,
                aggregate_maps.room_type_map,
                start_date,
                end_date,
                billed_entity_account_payment_map,
            ).generate()
            if guest_inhouse_report_aggregates:
                for guest_inhouse_report_aggregate in guest_inhouse_report_aggregates:
                    report_details_response.append(
                        {
                            "booking_id": guest_inhouse_report_aggregate.booking_id,
                            "booking_status": guest_inhouse_report_aggregate.booking_status,
                            "guest_name": guest_inhouse_report_aggregate.guest_name,
                            "number_of_guests": {
                                "adults": guest_inhouse_report_aggregate.adult_count,
                                "children": guest_inhouse_report_aggregate.child_count,
                            },
                            "room_number": guest_inhouse_report_aggregate.room_number,
                            "room_type": guest_inhouse_report_aggregate.room_type,
                            "checkin_date": guest_inhouse_report_aggregate.checkin_date,
                            "checkout_date": guest_inhouse_report_aggregate.checkout_date,
                            "booking_channel_type": guest_inhouse_report_aggregate.get_channel,
                            "booking_amount": guest_inhouse_report_aggregate.room_related_charges_posttax,
                            "amount_paid": guest_inhouse_report_aggregate.payment_amount,
                            "remaining_balance_due": guest_inhouse_report_aggregate.room_stay_due_payments,
                        }
                    )
        return report_details_response

    @set_hotel_context()
    def generate_csv_report(
        self, start_date, end_date, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()

        booking_aggregates, hotel_map, room_type_map = self._base_report_data(
            start_date, end_date, hotel_aggregate
        )

        file_path = (
            GuestInhouseReportGenerator.generate_guest_inhouse_report_file_name()
        )
        with CsvWriter(file_path) as csv_writer:
            for bookings in chunks(booking_aggregates, 1000):
                self._generate_csv_report(
                    bookings, hotel_map, room_type_map, csv_writer, start_date, end_date
                )
            presigned_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                GuestInhouseReportGenerator.GUEST_INHOUSE_REPORT_FOLDER_NAME,
                csv_writer.file_path,
                GuestInhouseReportGenerator.get_default_expiration_time(),
            )

        return presigned_url

    def _generate_csv_report(
        self,
        booking_aggregates,
        hotel_map,
        room_type_map,
        csv_writer,
        start_date,
        end_date,
    ):
        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids,
            load_invoices_by_bill_id=True,
            hotel_map=hotel_map,
            room_type_map=room_type_map,
        )
        report_aggregates = []
        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            hotel_aggregate = aggregate_maps.hotel_map.get(
                booking_aggregate.booking.hotel_id
            )
            csv_report_aggregates = GuestInhouseReportGenerator(
                booking_aggregate,
                bill_aggregate,
                hotel_aggregate,
                aggregate_maps.room_type_map,
                start_date,
                end_date,
            ).generate()
            if csv_report_aggregates:
                report_aggregates.extend(csv_report_aggregates)

        csv_writer.write_aggregates(
            report_aggregates, GuestInhouseReportGenerator.REPORT_COLUMNS
        )

    def _filter_booking_aggregates(
        self, booking_aggregates_for_date_range, hotel_aggregate, start_date, end_date
    ):
        booking_aggregates = []
        today = dateutils.current_date()
        hotel_context = crs_context.set_hotel_context(hotel_aggregate)

        for booking_aggregate in booking_aggregates_for_date_range:
            checkin_date = (
                booking_aggregate.booking.actual_checkin_date
                or booking_aggregate.booking.checkin_date
            )
            checkin_date = hotel_context.hotel_checkin_date(checkin_date)
            checkout_date = (
                booking_aggregate.booking.actual_checkout_date
                or booking_aggregate.booking.checkout_date
            )
            checkout_date = hotel_context.hotel_checkout_date(checkout_date)
            if not (checkin_date <= end_date and checkout_date > start_date):
                continue
            if checkin_date < today and booking_aggregate.booking.status in {
                BookingStatus.CHECKED_OUT,
                BookingStatus.CHECKED_IN,
                BookingStatus.PART_CHECKIN,
                BookingStatus.PART_CHECKOUT,
            }:
                booking_aggregates.append(booking_aggregate)

            if checkin_date >= today and booking_aggregate.booking.status in {
                BookingStatus.CHECKED_IN,
                BookingStatus.PART_CHECKIN,
            }:
                booking_aggregates.append(booking_aggregate)

        return booking_aggregates
