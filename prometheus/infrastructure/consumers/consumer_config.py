from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id


class CatalogConfig(object):
    """
    Catalog Message Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'cs_exchange'
        self.exchange_type = 'topic'
        self.queue_name = 'catalog-event-queue'
        self.routing_keys = [
            'com.cs.room.type',
            'com.cs.property',
            'com.cs.property.room',
            'com.cs.property.room.type.config',
            'com.cs.seller',
            'com.cs.ruptubdetails',
            'com.cs.restaurant.table',
            'com.cs.sku',
            'com.cs.sku.category',
        ]
        self.exclusive = False


class ReportingConfig(object):
    """
    Reporting Jobs Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'crs_reports'
        self.exchange_type = 'topic'
        self.queue_name = 'report-generation-requests-queue'
        self.routing_keys = [
            'reporting.marvin',
            'reporting.invoice',
            'reporting.sme',
            'reporting.navision',
            'reporting.ar',
            'reporting.financial_data_sync',
        ]
        self.exclusive = False


class InterfaceExchangeConsumerConfig(object):
    """
    Interface ExchangeConfig Jobs Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'erp_reports'
        self.exchange_type = 'topic'
        self.queue_name = 'interface-event-crs-queue'
        self.routing_keys = [
            'interface_exchange.erp.crs',
        ]
        self.exclusive = False


class ESDataSyncConsumerConfig(object):
    """
    ESDataSyncConsumerConfig Job queue config
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'es-data-sync-events'
        self.exchange_type = 'topic'
        self.queue_name = 'es-data-sync-queue'
        self.routing_keys = ['booking', 'es_data_sync']
        self.exclusive = False


class ESDataSyncPublisherConfig(object):
    """
    ESDataSyncConsumerConfig Job queue config
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'es-data-sync-events'
        self.exchange_type = 'topic'
        self.routing_keys = [
            'es_data_sync',
        ]
        self.exclusive = False


class InterfaceExchangePublisherConfig(object):
    """
    Interface ExchangeConfig Jobs Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'erp_reports'
        self.exchange_type = 'topic'
        self.queue_name = 'interface-event-crs-queue'
        self.routing_keys = [
            'crs.erp.interface_exchange',
        ]
        self.exclusive = False


class FundingConsumerConfig(object):
    """
    AutoFundingConsumer Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'crs-events'
        self.exchange_type = 'topic'
        self.queue_name = 'auto-funding-event-queue'
        self.routing_keys = ['booking']
        self.exclusive = False
