import io
import logging

from prometheus.core.globals import consumer_context

logger = logging.getLogger(__name__)


class SftpClient:
    def __init__(self):
        self._connection = None

        secret = consumer_context.get_sftp_secret()
        self.host = secret.get('SFTP_HOST', '127.0.0.1')
        self.port = int(secret.get('SFTP_PORT', 22))
        self.username = secret.get('SFTP_USERNAME')
        self.private_key = secret.get('PRIVATE_KEY')
        self.ssh_key_passphrase = secret.get('SFTP_KEY_PASSPHRASE')
        self.remote_dir = secret.get('REMOTE_DIR', '/tmp')

    def __enter__(self):
        # pylint: disable=import-error
        from paramiko import RSAKey, SFTPClient, Transport

        transport = Transport(sock=(self.host, self.port))

        private_key_str = io.StringIO()
        private_key_str.write(self.private_key)
        private_key_str.seek(0)
        pkey = RSAKey.from_private_key(private_key_str, self.ssh_key_passphrase)

        transport.connect(username=self.username, pkey=pkey)
        self._connection = SFTPClient.from_transport(transport)
        return self

    def __exit__(self, exc_type, exc_value, exc_traceback):
        if self._connection is None:
            return

        self._connection.close()

    @staticmethod
    def uploading_info(uploaded_file_size, total_file_size):
        logger.info(
            'uploaded_file_size : {} total_file_size : {}'.format(
                uploaded_file_size, total_file_size
            )
        )

    def upload_to_tenant_server(self, local_path, csv_name):
        remote_path = f'{self.remote_dir}/{csv_name}'

        self._connection.put(
            localpath=local_path,
            remotepath=remote_path,
            callback=self.uploading_info,
            confirm=True,
        )
