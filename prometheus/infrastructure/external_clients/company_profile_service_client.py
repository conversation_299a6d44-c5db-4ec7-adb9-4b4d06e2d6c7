import logging
from decimal import Decimal

import sentry_sdk

from object_registry import register_instance
from prometheus.domain.booking.dtos.commission_calculation_dtos import (
    CommissionQuery,
    CommissionResponse,
)
from prometheus.domain.company_profiles.dto.company_profiles_dto import (
    CompanyProfilesDTO,
    ParentEntity,
    SubEntitiesDTO,
)
from prometheus.domain.company_profiles.errors import CompanyProfileError
from prometheus.domain.company_profiles.exceptions import CompanyProfileException
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from ths_common.exceptions import DownstreamSystemFailure
from ths_common.value_objects import TACommissionDetails

logger = logging.getLogger(__name__)


@register_instance()
class CompanyProfileServiceClient(BaseExternalClient):
    page_map = {
        'fetch_profiles': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/company-profiles/v1/search",
        ),
        'search_entities_by_superhero_company_code': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/company-profiles/v1/search?superhero_company_code={"
            "superhero_company_code}&status={status}",
        ),
        'search_entities_by_parent_id': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/company-profiles/v1/parent-entities/{parent_entity_id}/sub-entities",
        ),
        'calculate_commission': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/company-profiles/v1/commission/calculate",
        ),
        'search_entities_by_gstin': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/company-profiles/v1/search?gstins={gstins}&status={status}",
        ),
        'search_parent_entity_by_superhero_company_code': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/company-profiles/v1/parent-entities/search?superhero_company_code={"
            "superhero_company_code}",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_company_profile_service_url()

    def fetch_ta_commission_details(self, superhero_company_code, hotel_id):
        page_name = "fetch_profiles"
        request_dict = dict(
            superhero_company_code=superhero_company_code,
            commission_hotel_id=hotel_id,
            load_commissions=True,
        )
        response = self.make_call(page_name, data=request_dict)

        if not response.is_success():
            raise DownstreamSystemFailure(
                message="Failed to fetch company profile.",
                description="Failed to fetch company profile for {0}: {1}".format(
                    superhero_company_code, response.response_code
                ),
                extra_payload=dict(
                    company_profile_response=response.errors,
                    company_profile_request=request_dict,
                ),
            )

        sub_entities = response.json_response['data'].get('sub_entities', [])
        if len(sub_entities) == 0:
            raise Exception(
                "Failed to fetch company profile for {}".format(superhero_company_code)
            )
        sub_entity = sub_entities[0]
        commissions = sub_entity.get('commissions', [])
        ota_commission_detail = commissions[0] if commissions else None
        return (
            TACommissionDetails.from_json(ota_commission_detail)
            if ota_commission_detail
            else None
        )

    def search_company_profile(self, external_reference_id, status='complete'):
        page_name = "search_entities_by_superhero_company_code"
        url_params = dict(superhero_company_code=external_reference_id, status=status)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise CompanyProfileException(
                error=CompanyProfileError.COMPANY_PROFILE_ERROR,
            )
        return CompanyProfilesDTO(**response.json_response)

    def search_parent_entities(self, external_reference_id):
        page_name = "search_parent_entity_by_superhero_company_code"
        url_params = dict(superhero_company_code=external_reference_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise CompanyProfileException(
                error=CompanyProfileError.COMPANY_PROFILE_ERROR,
            )
        entities = response.json_response.get("data", {}).get("parent_entities")
        return [ParentEntity(**entity) for entity in entities] if entities else []

    def get_treebo_poc_and_inside_sales_poc(self, external_reference_id):
        inside_sales_poc, treebo_poc, point_of_contacts = None, None, []
        try:
            company_profiles_dto = self.search_company_profile(external_reference_id)

        except Exception as e:
            sentry_sdk.capture_exception(e)
            return None, None

        sub_entities = company_profiles_dto.data["sub_entities"]
        if sub_entities:
            point_of_contacts = sub_entities[0].point_of_contacts
        if not point_of_contacts:
            return None, None

        for poc in point_of_contacts:
            if poc.poc_type != 'hotel':
                continue
            if poc.department == 'inside_sales':
                inside_sales_poc = poc
            elif poc.department == 'sales':
                treebo_poc = poc

        return inside_sales_poc, treebo_poc

    def get_sibling_sub_entities(self, parent_entity_id):
        page_name = "search_entities_by_parent_id"
        url_params = dict(parent_entity_id=parent_entity_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise CompanyProfileException(
                error=CompanyProfileError.COMPANY_PROFILE_ERROR,
            )
        sub_entities_dto = SubEntitiesDTO(response.json_response['data'])
        return sub_entities_dto.sub_entities

    def calculate_commission(
        self,
        hotel_id,
        booking_id,
        superhero_company_code,
        applicable_date,
        booking_commission,
        commission_items: [CommissionQuery],
    ):
        page_name = "calculate_commission"
        request_dict = dict(
            data=dict(
                hotel_id=hotel_id,
                booking_id=booking_id,
                superhero_company_code=superhero_company_code,
                applicable_date=applicable_date,
                booking_commission=booking_commission,
                commission_items=[
                    dict(
                        index=item.index,
                        pre_commission_amount=item.room_night_price.amount,
                    )
                    for item in commission_items
                ],
            )
        )
        response = self.make_call(page_name, data=request_dict)

        if response.is_success():
            commission = response.json_response['data'].get('commission')
            commission_items = response.json_response['data'].get(
                'commission_items', []
            )
        else:
            commission = None
            logger.info(
                "Failed to calculate commission for hotel_id: %s, booking_id: %s, superhero_company_code: %s, "
                "applicable_date: %s. Error: %s",
                hotel_id,
                booking_id,
                superhero_company_code,
                applicable_date,
                response.errors,
            )

        return commission, [
            CommissionResponse(
                index=item['index'],
                commission_amount=item['commission_amount'] or Decimal("0.00"),
                commission_tax_amount=item.get('commission_tax_amount')
                or Decimal("0.00"),
            )
            for item in commission_items
        ]

    def search_company_profile_by_gstin(self, gstin, status='complete'):
        page_name = "search_entities_by_gstin"
        url_params = dict(gstins=gstin, status=status)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise CompanyProfileException(
                error=CompanyProfileError.COMPANY_PROFILE_ERROR,
            )
        return CompanyProfilesDTO(**response.json_response).data
