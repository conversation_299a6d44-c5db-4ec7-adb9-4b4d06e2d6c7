import logging
from typing import List

from flask import current_app as app

from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from ths_common.exceptions import DownstreamSystemFailure

logger = logging.getLogger(__name__)


# NOTE: Temporarily adding this client in CRS, to send payment link communication to user.
# This will be used by scheduled job on checkin.
# Ideally this event should be created in realisation only, after receiving checkin event. But since there is no
# infra for scheduled job in Realisation, doing it here.


@register_instance()
class RealisationClient(BaseExternalClient):
    page_map = {
        'send_payment_link': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/growth/payment/send-pending-amount-communication/",
        ),
        'generate_cancellation_links': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/growth/cancellation/cancel-hash/",
        ),
        'generate_payment_links': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/growth-realization/paymentlink/generate-bulk/",
        ),
        'fetch_payment_links': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/growth-realization/paymentlink/fetch-payment-links/",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_realisation_service_url()

    def send_payment_link(self, crs_booking_id, hotel_id, phone, email=None):
        page_name = "send_payment_link"
        post_data = dict(
            group_code=crs_booking_id,
            hotel_code=hotel_id,
            full_payment=True,
            phone=phone,
        )
        if email:
            post_data['email'] = email
        response = self.make_call(page_name, data=post_data)
        if not response.is_success():
            try:
                if response.errors.get('status_code') != 'BAD_REQUEST':
                    logger.error(
                        "send_payment_link API ignored error: %s=>%s",
                        response.response_code,
                        response.errors,
                    )
            except:
                return

    def generate_cancellation_links(self, bookings: List[dict]):
        page_name = "generate_cancellation_links"
        post_data = dict(bookings=bookings)
        response = self.make_call(page_name, data=post_data)
        if not response.is_success():
            raise DownstreamSystemFailure(
                message="Failed to generate cancellation hash.",
                description="Failed to generate cancellation hash with code: {0} {1}".format(
                    response.response_code, response.errors
                ),
            )
        return response.json_response.get('data')

    def generate_payment_links(self, request_data: List[dict]):
        page_name = "generate_payment_links"
        response = self.make_call(page_name, data=request_data, timeout=150)
        if not response.is_success():
            raise DownstreamSystemFailure(
                message="Failed to generate Payment link.",
                description="Failed to generate payment link with code: {0} {1}".format(
                    response.response_code, response.errors
                ),
            )
        if not response.json_response or not response.json_response.get('data'):
            raise DownstreamSystemFailure("Failed to generate payment link")
        return response.json_response.get('data')

    def fetch_active_payment_links_on_bookings(self, booking_ids: list):
        page_name = "fetch_payment_links"
        response = self.make_call(page_name, data=dict(booking_ids=booking_ids))
        if not response.is_success():
            raise DownstreamSystemFailure(
                message="Failed to fetch Payment link.",
                description="Failed to generate fetch payment link with code: {0} {1}".format(
                    response.response_code, response.errors
                ),
            )
        if not response.json_response:
            raise DownstreamSystemFailure("Failed to fetch payment link")
        return response.json_response.get('data', [])
