import asyncio
import logging
from enum import Enum

import aiohttp

from object_registry import register_instance
from prometheus import crs_context
from prometheus.infrastructure.external_clients.core import constants
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from ths_common.exceptions import DownstreamSystemFailure
from ths_common.utils.common_utils import clean_null_terms
from ths_common.utils.id_generator_utils import random_id_generator

logger = logging.getLogger(constants.EXTERNAL_CLIENTS_LOGGER_PREFIX + __name__)


class TemplateFormat(Enum):
    PDF = 'pdf'
    HTML = 'html'


class TemplateNameSpace(Enum):
    THS_VOID_RESELLER_INVOICE = "ths_void_reseller_invoice"
    THS_VOID_HOTEL_INVOICE = "ths_void_hotel_invoice"
    THS_INDEPENDENT_VOID_HOTEL_INVOICE = "ths_independent_void_hotel_invoice"
    THS_RESELLER_INVOICE = "ths_reseller_invoice"
    THS_HOTEL_INVOICE = "ths_hotel_invoice"
    THS_INDEPENDENT_HOTEL_INVOICE = "ths_independent_hotel_invoice"
    THS_RESELLER_CREDIT_NOTE = "ths_reseller_credit_note"
    THS_HOTEL_CREDIT_NOTE = "ths_hotel_credit_note"
    THS_INDEPENDENT_HOTEL_CREDIT_NOTE = "ths_independent_hotel_credit_note"
    PMS_SHARE_INVOICE = "pms_share_invoice"
    HOUSEKEEPING_RECORD = "housekeeping_record"
    HOUSEKEEPING_STATUS = "housekeeping_status"
    THS_POS_INVOICE = "ths_pos_invoice"
    THS_VOID_POS_INVOICE = "ths_void_pos_invoice"
    THS_POS_BILL = "ths_pos_bill"
    E_REG_CARD = 'e_reg_card'
    PAYMENT_VOUCHER = 'payment_voucher'
    THS_HOTEL_PROFORMA_INVOICE = 'ths_hotel_proforma_invoice'
    THS_VOID_HOTEL_PROFORMA_INVOICE = 'ths_void_hotel_proforma_invoice'
    CURRENCY_EXCHANGE_ENCASHMENT_CERTIFICATE = 'currency_exchange_certificate'
    PAYMENT_RECEIPT = 'payment_receipt'
    DATE_AND_CATEGORY_WISE_SUMMARY_PROFORMA_INVOICE = (
        'date_and_category_wise_summary_proforma_invoice'
    )
    DATE_AND_CATEGORY_WISE_SUMMARY_INVOICE = 'date_and_category_wise_summary_invoice'
    BOOKING_CONFIRMATION_VIA_WHATSAPP = "booking_confirmation_crs"
    PAYMENT_CONFIRMATION_VOUCHER_VIA_EMAIL = "payment_confirmation_voucher"
    PAYMENT_CONFIRMATION_VIA_WHATSAPP = "payment_confirmation"
    BOOKING_CONFIRMATION_DIRECT_VIA_WHATSAPP = "booking_confirmation_crs_v2"
    PAYMENT_CONFIRMATION_DIRECT_VIA_WHATSAPP = "payment_confirmation_v2"


@register_instance()
class TemplateService(BaseExternalClient):
    page_map = {
        'generate': dict(
            type=BaseExternalClient.CallTypes.POST, url_regex="/api/v1/generate"
        ),
    }

    def __init__(self):
        super().__init__(timeout=300)

    def get_domain(self):
        return ServiceRegistryClient.get_template_service_url()

    def get_headers(self):
        headers = BaseExternalClient.get_headers()
        if crs_context.hotel_context:
            headers['X-Hotel-Id'] = crs_context.hotel_context.hotel_id

        # Removing None values from headers because asyncio fails while coverting None to str type.
        return clean_null_terms(headers)

    def generate(self, namespace: str, context: dict, format: TemplateFormat):
        """
        Generate the template in given format
        Args:
            namespace:
            context:
            format:
            upload_to_s3:

        Returns:

        """
        s3_file_path = "{}/{}".format(namespace, random_id_generator(namespace[:10]))
        logger.debug(
            "Generate template for namespace: %s of type: %s upload: %s context: %s",
            namespace,
            format.value,
            s3_file_path,
            context,
        )
        request_dict = dict(
            template=namespace,
            context=context,
            format=format.value,
            s3FilePath=s3_file_path,
        )

        response = self.make_call("generate", request_dict)

        if not response.is_success():
            raise DownstreamSystemFailure(
                message="Failed to upload template.",
                description="Failed to upload template with code: {0}".format(
                    response.response_code
                ),
                extra_payload=None,
            )

        link = response.json_response.get('link')
        if not link:
            logger.error(
                "No link from template service. Response: {}".format(
                    response.response.text
                )
            )

        return link

    def generate_email_body(
        self, namespace: str, context: dict, format: TemplateFormat
    ):
        """
        Generate the template in given format
        Args:
            namespace:
            context:
            format:

        Returns:

        """
        logger.debug(
            "Get email body for namespace: %s of type: %s context: %s",
            namespace,
            format.value,
            context,
        )
        request_dict = dict(template=namespace, context=context, format=format.value)
        response = self.make_call("generate", request_dict)

        if not response.is_success():
            extra_payload = request_dict
            raise DownstreamSystemFailure(
                message="Failed to get the email body.",
                description="Failed to get email body with code: {0}".format(
                    response.response_code
                ),
                extra_payload=extra_payload,
            )

        logger.debug(
            "Get invoice email body response from template service:{}".format(
                response.data
            )
        )

        return response.data["text"]

    def generate_bulk(self, namespace: str, context: dict, format: TemplateFormat):
        """
        Generate the template in bulk in given format. Works only with post apis
        Args:
            namespace:
            context:
            format:

        Returns:

        """
        ereg_url_dict = {}
        rs = []
        ids = []
        for id_, context_dict in context.items():
            s3_file_path = "{}/{}".format(
                namespace, random_id_generator(namespace[:10])
            )
            logger.debug(
                "Generate template for namespace: %s of type: %s upload: %s context: %s",
                namespace,
                format.value,
                s3_file_path,
                context,
            )
            request_dict = dict(
                template=namespace,
                context=context_dict,
                format=format.value,
                s3FilePath=s3_file_path,
            )
            request_obj = {'page_name': "generate", 'params': request_dict, "id": id_}
            rs.append(request_obj)
            ids.append(id_)

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        future = asyncio.ensure_future(self._fetch_all_urls_async(rs))
        results = loop.run_until_complete(future)

        for result in results:
            if result:
                ereg_url_dict[result[0]] = result[1]

        return ereg_url_dict

    async def _fetch_url_async(self, session, page_name, params, id_):
        api = self.get_api(page_name)
        url = self.get_domain() + api.get("url")
        resp = await session.post(url, json=params, headers=self.get_headers())
        if resp.status == 200:
            resp_json = await resp.json()
            return id_, resp_json['link']
        else:
            return id_, None

    async def _fetch_all_urls_async(self, packets):
        results = []
        async with aiohttp.ClientSession() as session:
            results = await asyncio.gather(
                *[
                    self._fetch_url_async(
                        session, item['page_name'], item['params'], item['id']
                    )
                    for item in packets
                ]
            )
        logger.debug("async print results " + str(results))
        return results
