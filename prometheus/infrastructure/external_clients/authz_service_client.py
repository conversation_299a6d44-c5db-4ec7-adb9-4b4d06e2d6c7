from flask import current_app as app

from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)


@register_instance()
class AuthZServiceClient(BaseExternalClient):
    page_map = {
        'get_all_users_with_role': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/api/v1/users?role_name={role_name}&resource_ids={"
            "resource_ids}&resource_type={resource_type}",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_authz_service_url()

    def get_all_users_with_role(self, hotel_id, role_name):
        page_name = "get_all_users_with_role"
        url_params = dict(
            resource_ids=hotel_id, role_name=role_name, resource_type='hotel'
        )
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "AuthZ API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response[0]['user_ids']
