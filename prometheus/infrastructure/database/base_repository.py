# coding=utf-8
"""
Base Repository
"""
import abc
import logging

import sqlalchemy
from sqlalchemy import not_
from sqlalchemy.orm import lazyload
from sqlalchemy.orm.exc import FlushError, MultipleResultsFound, NoResultFound

from prometheus import crs_context
from prometheus.infrastructure.database import db_engine
from ths_common.exceptions import (
    BookingReferenceIdCollision,
    DatabaseError,
    DatabaseLockError,
    PrimaryKeyCollision,
    UniqueIdCollision,
    ValidationException,
)

logger = logging.getLogger(__name__)


def handle_integrity_error(exception):
    # TODO There could be other integrity errors. Check for only collisions
    if (
        "reference_number".upper() in exception.__str__().upper()
        and "DUPLICATE KEY VALUE VIOLATES UNIQUE CONSTRAINT".upper()
        in exception.__str__().upper()
    ):
        # NOTE: Not correct. exception string contains columns names too. It can have reference_number anywhere
        raise BookingReferenceIdCollision(description=exception.__str__())
    if (
        "DUPLICATE KEY VALUE VIOLATES UNIQUE CONSTRAINT".upper()
        in exception.__str__().upper()
    ):
        raise UniqueIdCollision(description=exception.__str__())
    raise PrimaryKeyCollision(description=exception.__str__())


def handle_database_exception(exception):
    if isinstance(exception, sqlalchemy.exc.IntegrityError) or isinstance(
        exception, FlushError
    ):
        handle_integrity_error(exception)

    if isinstance(exception, sqlalchemy.exc.OperationalError):
        if "could not obtain lock".upper() in exception.__str__().upper():
            raise DatabaseLockError(description=exception.__str__())

    raise DatabaseError(exception.__str__())


class BaseRepository(object):
    """
    Base repository
    """

    @staticmethod
    def session():
        """
        returns session
        :return:
        """
        session = db_engine.get_scoped_session()
        return session

    def flush_session(self):
        try:
            self.session().flush()
        except Exception as exp:
            handle_database_exception(exp)

    def create(self, item):
        """
        create new entry in table
        :param item:
        :return:
        """
        self.session().add(item)
        return item

    def create_all(self, items):
        """
        creates multiple items
        :param items:
        :return:
        """
        self.session().add_all(items)
        return items

    def _bulk_save_or_update(self, items):
        try:
            self.session().bulk_save_objects(items)
        except Exception as exp:
            handle_database_exception(exp)

    def update(self, item):
        """
        update an item
        :param item:
        :return: item
        """
        try:
            self.session().add(item)
            return item
        except Exception as exp:
            handle_database_exception(exp)

    def _save(self, item):
        """
        :param item:
        :return:
        """
        try:
            self.session().add(item)
        except Exception as exp:
            handle_database_exception(exp)
        return item

    def _update(self, item):
        """
        :param item:
        :return:
        """
        try:
            self.session().merge(item)
        except Exception as exp:
            handle_database_exception(exp)
        return item

    def _update_all(self, items):
        """
        updates multiple items
        :param items:
        :return:
        """
        for item in items:
            self.session().merge(item)
        return items

    def _bulk_update_mappings(self, model, mapping_dicts):
        if not mapping_dicts:
            return
        try:
            self.session().bulk_update_mappings(model, mapping_dicts)
        except Exception as exp:
            handle_database_exception(exp)

    def _bulk_insert_mappings(self, model, mapping_dicts):
        if not mapping_dicts:
            return
        try:
            self.session().bulk_insert_mappings(model, mapping_dicts)
        except Exception as exp:
            handle_database_exception(exp)

    def dumb_save(self, item):
        """
        :param item:
        :return:
        """
        try:
            self.session().add(item)
        except Exception as exp:
            handle_database_exception(exp)

    def _save_all(self, items):
        """
        updates multiple items
        :param items:
        :return:booking
        """
        self.session().add_all(items)
        return items

    def filter(self, model, *queries, for_update=False, nowait=True):
        """
        :param model:
        :param queries:
        :param for_update:
        :param nowait:
        :return:
        """
        queryset = self.session().query(model)
        queryset = queryset.filter(*queries)
        if for_update:
            queryset = queryset.with_for_update(nowait=nowait)
        return queryset

    def exclude(self, query, *exclude_queries):
        real_excludes = [not_(ex) for ex in exclude_queries]
        return query.filter(*real_excludes)

    def mark_deleted(self, model, filter_queries, exclude=None):
        queryset = self.filter(model, *filter_queries)
        if exclude:
            exclude_queries = []
            for item in exclude:
                exclude_queries.append(~item)
            queryset = queryset.filter(*exclude_queries)
        queryset.update({'deleted': True}, synchronize_session=False)

    def filter_by_join(self, models, join_clause, *queries):
        """
        :param models:
        :param join_clause:
        :param queries:
        :return:
        """
        queryset = self.session().query(*models).filter(join_clause)
        items = queryset.filter(*queries)
        return items

    def get(self, model, **queries):
        """

        :param model:
        :param queries:
        :return:
        """
        queryset = self.session().query(model)

        for attr, value in queries.items():
            if value:
                value = "%s" % value
            queryset = queryset.filter(getattr(model, attr) == value)
        try:
            return queryset.one()
        except NoResultFound:
            return None
        except MultipleResultsFound:
            message = "Multiple objects %s found" % model
            raise ValidationException(description=message)
        except Exception as exp:
            handle_database_exception(exp)

    def get_for_update(self, model, nowait=True, **queries):
        """
        The query will lock the row that is returned.
        If the transaction cannot lock the row (which will happen when some other transactions have obtained the lock),
        then:
            - If `nowait=True`, the query will fail with error
            - If `nowait=False`, the query will wait for the lock to get released.

        :param model:
        :param nowait:
        :param queries:
        :return:
        """
        queryset = self.session().query(model)
        for attr, value in queries.items():
            if value:
                value = "%s" % value
            queryset = queryset.filter(getattr(model, attr) == value)

        # Forcing lazy load here because
        # https://www.postgresql.org/message-id/<EMAIL>
        queryset = queryset.options(lazyload("*"))
        try:
            return queryset.with_for_update(nowait=nowait).one()
        except NoResultFound:
            return None
        except MultipleResultsFound:
            message = "Multiple objects %s found" % model
            raise ValidationException(description=message)
        except Exception as exp:
            handle_database_exception(exp)

    def delete(self, item):
        """
        delete item
        :param item:
        :return:booking
        """
        self.session().delete(item)

    def rollback_transaction(self):
        """
        rollback transaction
        :return:
        """

        try:
            self.session().close()
        except Exception as exp:
            handle_database_exception(exp)

    @abc.abstractmethod
    def to_aggregate(self, **kwargs):
        pass

    @abc.abstractmethod
    def from_aggregate(self, aggregate=None):
        pass

    def query(self, *model):
        return self.session().query(*model)
