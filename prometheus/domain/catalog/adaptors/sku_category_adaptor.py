from prometheus.domain.catalog.entities.sku_category import SkuCategory
from prometheus.domain.catalog.models import SkuCategoryModel
from ths_common.constants.catalog_constants import SkuCategoryStatus


class SkuCategoryDBAdapter(object):
    @staticmethod
    def to_db_model(sku_category: SkuCategory):
        return SkuCategoryModel(
            item_code=sku_category.item_code,
            name=sku_category.name,
            sku_category_id=sku_category.sku_category_id,
            status=sku_category.status.value,
            has_slab_based_taxation=sku_category.has_slab_based_taxation,
        )

    @staticmethod
    def to_entity(sku_category_model):
        return SkuCategory(
            sku_category_id=sku_category_model.sku_category_id,
            item_code=sku_category_model.item_code,
            name=sku_category_model.name,
            status=SkuCategoryStatus(sku_category_model.status),
            has_slab_based_taxation=sku_category_model.has_slab_based_taxation,
        )
