# pylint: disable=no-member
from ths_common.exceptions import CRSException

from .errors import CatalogError


class CatalogException(CRSException):
    error = CatalogError.CATALOG_ERROR

    def __init__(self, error=None, description=None, extra_payload=None):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error.error_code
            self.message = self.error.message
        super().__init__(description=description, extra_payload=extra_payload)
