from typing import List

from object_registry import register_instance
from prometheus.domain.catalog.adaptors.seller_adaptor import SellerAdaptor
from prometheus.domain.catalog.aggregates.seller_aggregate import SellerAggregate
from prometheus.infrastructure.database.base_repository import BaseRepository
from shared_kernel.infrastructure.database.common_models import SellerModel


@register_instance()
class SellerRepository(BaseRepository):
    seller_adaptor = SellerAdaptor()

    def from_aggregate(self, aggregate: SellerAggregate = None):
        seller = aggregate.seller
        seller_model = self.seller_adaptor.to_db_entity(seller)
        return seller_model

    def save(self, aggregate):
        seller_model = self.from_aggregate(aggregate)
        self._save(seller_model)
        self.flush_session()

    def update(self, aggregate):
        seller_model = self.from_aggregate(aggregate)
        self._update(seller_model)
        self.flush_session()

    def update_all(self, aggregates):
        seller_models = [self.from_aggregate(aggregate) for aggregate in aggregates]
        self._update_all(seller_models)
        self.flush_session()

    def to_aggregate(self, **kwargs):
        seller_model = kwargs.get('seller_model')
        seller = self.seller_adaptor.to_domain_entity(seller_model)
        return SellerAggregate(seller)

    def load(self, seller_id):
        seller_model = (
            self.query(SellerModel).filter(SellerModel.seller_id == seller_id).one()
        )
        return self.to_aggregate(seller_model=seller_model)

    def load_for_update(self, seller_id):
        seller_model = (
            self.query(SellerModel).filter(SellerModel.seller_id == seller_id).one()
        )
        return self.to_aggregate(seller_model=seller_model)

    def load_all(self, seller_ids):
        seller_models = self.query(SellerModel).filter(
            SellerModel.seller_id.in_(seller_ids)
        )
        return [
            self.to_aggregate(seller_model=seller_model)
            for seller_model in seller_models
        ]

    def load_for_hotel_id(self, hotel_id) -> List[SellerAggregate]:
        seller_models = (
            self.query(SellerModel).filter(SellerModel.hotel_id == hotel_id).all()
        )
        return [self.to_aggregate(seller_model=model) for model in seller_models]

    def load_seller_ids_for_hotel(self, hotel_id):
        q = self.query(SellerModel.seller_id).filter(SellerModel.hotel_id == hotel_id)
        return [seller_id for (seller_id,) in q.all()]
