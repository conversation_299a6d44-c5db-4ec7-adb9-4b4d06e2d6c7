from sqlalchemy import func

from object_registry import register_instance
from prometheus.domain.catalog.adaptors.room_adaptor import Room<PERSON>daptor
from prometheus.domain.catalog.aggregates.room_aggregate import RoomAggregate
from prometheus.domain.catalog.errors import CatalogError
from prometheus.domain.catalog.models import RoomModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.catalog_constants import RoomStatus
from ths_common.exceptions import AggregateNotFound, ValidationException


@register_instance()
class RoomRepository(BaseRepository):
    room_adaptor = RoomAdaptor()

    def save(self, room_aggregate):
        room = self.room_adaptor.to_db_entity(room_aggregate.room)
        self._save(room)
        self.flush_session()

    def save_all(self, room_aggregates):
        rooms = [
            self.room_adaptor.to_db_entity(room_aggregate.room)
            for room_aggregate in room_aggregates
        ]
        self._save_all(rooms)
        self.flush_session()

    def update(self, room_aggregate):
        room = self.room_adaptor.to_db_entity(room_aggregate.room)
        self._update(room)
        self.flush_session()

    def update_all(self, room_aggregates):
        rooms = [
            self.room_adaptor.to_db_entity(room_aggregate.room)
            for room_aggregate in room_aggregates
        ]
        self._update_all(rooms)
        self.flush_session()

    def load(self, room_id, hotel_id=None):
        room = self.get(RoomModel, room_id=room_id)
        if not room:
            raise AggregateNotFound("Room", room_id)
        if hotel_id is not None and room.hotel_id != hotel_id:
            raise ValidationException(error=CatalogError.ROOM_NOT_FOUND_IN_HOTEL)
        room_entity = self.room_adaptor.to_domain_entity(room)
        return RoomAggregate(room_entity)

    def load_for_update(self, room_id, hotel_id=None):
        room = self.get_for_update(RoomModel, room_id=room_id)
        if not room:
            raise AggregateNotFound("Room", room_id)
        if hotel_id is not None and room.hotel_id != hotel_id:
            raise ValidationException(error=CatalogError.ROOM_NOT_FOUND_IN_HOTEL)
        room_entity = self.room_adaptor.to_domain_entity(room)
        return RoomAggregate(room_entity)

    def load_multiple(
        self, hotel_id, room_type_id=None, skip_inactive=False, room_ids=None
    ):
        query = [RoomModel.hotel_id == hotel_id]
        if room_type_id:
            query.append(RoomModel.room_type_id == room_type_id)
        if room_ids:
            query.append(RoomModel.room_id.in_(room_ids))
        if skip_inactive:
            query.append(RoomModel.status != RoomStatus.INACTIVE.value)
        rooms = self.filter(RoomModel, *query)
        room_aggregates = []
        for room in rooms:
            room_aggregates.append(
                RoomAggregate(self.room_adaptor.to_domain_entity(room))
            )
        return room_aggregates

    def exists(self, room_id):
        room = self.get(RoomModel, room_id=room_id)
        return True if room else False

    def get_room_count(self, hotel_id, room_type_id):
        room_ids = self.fetch_room_ids_for_hotel_and_room_type(hotel_id, room_type_id)
        return len(room_ids)

    def get_active_room_type_count_map(self, hotel_id):
        room_type_count_map = (
            self.query(
                RoomModel.room_type_id,
                func.count(RoomModel.room_type_id).label("room_count"),
            )
            .filter(
                RoomModel.hotel_id == hotel_id,
                RoomModel.deleted == False,
                RoomModel.status == RoomStatus.ACTIVE.value,
            )
            .group_by(RoomModel.room_type_id)
        )
        room_type_count_map = {
            room_type_id: room_count for room_type_id, room_count in room_type_count_map
        }

        return room_type_count_map

    def fetch_room_ids_for_hotel(
        self, hotel_id, statuses=None, exclude_room_type_id=None
    ):
        queries = [RoomModel.hotel_id == hotel_id]
        if statuses:
            queries.append(RoomModel.status.in_(statuses))
        if exclude_room_type_id:
            queries.append(RoomModel.room_type_id != exclude_room_type_id)
        return self._fetch_room_ids(queries)

    def fetch_room_ids_for_hotel_and_room_type(self, hotel_id, room_type_id):
        queries = [
            RoomModel.hotel_id == hotel_id,
            RoomModel.room_type_id == room_type_id,
        ]
        return self._fetch_room_ids(queries)

    def _fetch_room_ids(self, queries):
        queryset = self.session().query(RoomModel.room_id)
        room_ids = queryset.filter(*queries)
        return list(room_id[0] for room_id in room_ids)

    def get_total_rooms_count_of_hotels(self, hotel_ids):
        results = (
            self.query(
                RoomModel.hotel_id,
                func.count(RoomModel.room_id).label("total_rooms"),
            )
            .filter(RoomModel.hotel_id.in_(hotel_ids))
            .filter(RoomModel.status == RoomStatus.ACTIVE.value)
            .group_by(RoomModel.hotel_id)
            .all()
        )
        return results
