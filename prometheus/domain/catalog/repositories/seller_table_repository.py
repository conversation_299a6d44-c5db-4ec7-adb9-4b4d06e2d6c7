from object_registry import register_instance
from prometheus.domain.catalog.adaptors.seller_table_adapter import SellerTableAdaptor
from prometheus.infrastructure.database.base_repository import BaseRepository
from shared_kernel.infrastructure.database.common_models import SellerTableModel


@register_instance()
class SellerTableRepository(BaseRepository):
    seller_table_adaptor = SellerTableAdaptor()

    def load(self, seller_id, table_id):
        seller_table_model = (
            self.query(SellerTableModel)
            .filter(
                SellerTableModel.table_id == table_id,
                SellerTableModel.seller_id == seller_id,
            )
            .first()
        )
        seller_table = self.seller_table_adaptor.to_domain_entity(seller_table_model)

        return seller_table

    def table_exists(self, seller_id, table_id):
        return (
            self.query(SellerTableModel)
            .filter(
                SellerTableModel.table_id == table_id,
                SellerTableModel.seller_id == seller_id,
            )
            .first()
            is not None
        )

    def update(self, seller_table):
        if seller_table:
            seller_table_model = self.seller_table_adaptor.to_db_entity(seller_table)
            updated_seller_table = self._update(seller_table_model)
            self.flush_session()
            return self.seller_table_adaptor.to_domain_entity(updated_seller_table)

        return None

    def save(self, seller_table):
        seller_table_model = self.seller_table_adaptor.to_db_entity(seller_table)
        self._save(seller_table_model)
        self.flush_session()
