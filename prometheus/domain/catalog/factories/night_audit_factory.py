import uuid

from prometheus.domain.catalog.aggregates.night_audit_aggregate import (
    NightAuditAggregate,
)
from prometheus.domain.catalog.entities.night_audit import NightAudit
from ths_common.constants.catalog_constants import NightAuditStatus


class NightAuditFactory:
    @staticmethod
    def create_new(hotel_id, business_date, job_id):
        night_audit_id = uuid.uuid4().hex
        return NightAuditAggregate(
            NightAudit(
                night_audit_id=night_audit_id,
                hotel_id=hotel_id,
                business_date=business_date,
                status=NightAuditStatus.SCHEDULED,
                job_id=job_id,
                start_time=None,
                end_time=None,
            )
        )
