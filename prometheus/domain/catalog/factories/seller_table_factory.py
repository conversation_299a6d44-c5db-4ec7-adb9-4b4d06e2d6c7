from treebo_commons.utils import dateutils

from prometheus.domain.catalog.entities.seller_table import SellerTable
from ths_common.pos.constants.order_constants import SellerTableStatus


class SellerTableFactory:
    @staticmethod
    def create_seller_table(seller_table_dto):
        return SellerTable(
            table_id=seller_table_dto.table_id,
            seller_id=seller_table_dto.seller_id,
            name=seller_table_dto.name,
            table_number=seller_table_dto.table_number,
            current_status=SellerTableStatus.AVAILABLE,
            status_updated_at=dateutils.current_datetime(),
            created_at=seller_table_dto.created_at,
            modified_at=seller_table_dto.modified_at,
            deleted=seller_table_dto.deleted,
        )
