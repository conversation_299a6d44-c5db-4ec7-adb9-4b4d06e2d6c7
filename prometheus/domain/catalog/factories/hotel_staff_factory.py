from prometheus.domain.catalog.aggregates.hotel_staff_aggregate import (
    HotelStaffAggregate,
)
from prometheus.domain.catalog.entities.housekeeper import HouseKeeper


class HotelStaffFactory(object):
    @staticmethod
    def create_new_housekeeper(hotel_id, name):
        housekeeper_id = 1
        housekeeper = HouseKeeper(housekeeper_id=housekeeper_id, name=name)
        housekeeper_aggregate = HotelStaffAggregate(
            hotel_id=hotel_id, housekeepers=[housekeeper]
        )
        return housekeeper_aggregate
