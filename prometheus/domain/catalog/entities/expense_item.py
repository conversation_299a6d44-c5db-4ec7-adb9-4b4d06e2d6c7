from ths_common.base_entity import EntityChangeTracker


class ExpenseItem(EntityChangeTracker):
    """
    Expense Item
    """

    def __init__(
        self,
        name: str,
        description: str,
        short_name: str,
        sku_category_id: int,
        linked: bool,
        expense_item_id: str,
        sku_id: str = None,
        deleted=False,
        addon_code=None,
        external_item_code=None,
        dirty=True,
        new=True,
        id=None,
    ):
        super().__init__(dirty=dirty, new=new)
        self.expense_item_id = expense_item_id
        self.name = name
        self.description = description
        self.short_name = short_name
        self.sku_category_id = sku_category_id
        self.sku_id = sku_id
        self.deleted = deleted
        self.linked = linked
        self.addon_code = addon_code
        self.external_item_code = external_item_code
        self.id = id

    @property
    def charge_component_name(self):
        return self.name

    @staticmethod
    def create_from_catalog_data(catalog_sku_data):
        return ExpenseItem(
            name=catalog_sku_data.get('name'),
            description=catalog_sku_data.get('description'),
            short_name=catalog_sku_data.get('name'),
            sku_category_id=catalog_sku_data.get('sku_category_code'),
            linked=False,
            expense_item_id=catalog_sku_data.get('code'),
            sku_id=catalog_sku_data.get('code'),
        )

    def update(self, expense_item_dto):
        self.name = expense_item_dto.name
        self.description = expense_item_dto.description
        self.short_name = expense_item_dto.short_name
        self.sku_category_id = expense_item_dto.sku_category_id
        self.linked = expense_item_dto.linked
        self.expense_item_id = expense_item_dto.expense_item_id
        self.mark_dirty()

    def delete(self):
        self.deleted = True
        self.mark_dirty()
