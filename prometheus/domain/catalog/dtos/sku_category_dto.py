from ths_common.value_objects import ItemCode


class SkuCategoryDto(object):
    def __init__(
        self,
        sku_category_id: str,
        item_code: ItemCode,
        name: str,
        status: str,
        has_slab_based_taxation: bool,
    ):
        self.sku_category_id = sku_category_id
        self.item_code = item_code
        self.name = name
        self.status = status
        self.has_slab_based_taxation = has_slab_based_taxation

    @staticmethod
    def create_from_catalog_data(data):
        return SkuCategoryDto(
            sku_category_id=data['code'].strip(),
            item_code=ItemCode.from_string('HSN:{0}'.format(data['hsn_sac'].strip())),
            name=data['name'].strip(),
            status='ACTIVE',
            has_slab_based_taxation=data['has_slab_based_taxation'],
        )
