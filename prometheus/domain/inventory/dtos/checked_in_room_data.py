class CheckedInRoomData(object):
    def __init__(
        self,
        room_id,
        hotel_id,
        room_type_id,
        room_number,
        booking_id,
        room_stay_id,
        room_allocation_id,
        rate_plans=None,
        disallow_charge_addition=False,
    ):
        self.room_id = room_id
        self.hotel_id = hotel_id
        self.room_type_id = room_type_id
        self.room_number = room_number
        self.booking_id = booking_id
        self.room_stay_id = room_stay_id
        self.room_allocation_id = room_allocation_id
        self.rate_plans = rate_plans
        self.disallow_charge_addition = disallow_charge_addition
