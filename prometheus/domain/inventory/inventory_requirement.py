import logging
from collections import Counter, defaultdict
from itertools import chain

from treebo_commons.utils.dateutils import date_range

logger = logging.getLogger(__name__)


class InventoryRequirement(object):
    def __init__(self, hotel_id):
        self.hotel_id = hotel_id
        self.room_type_ids = set()
        self.min_date = None
        self.max_date = None
        self.room_wise_inventory_requirement = defaultdict(Counter)

    def __str__(self):
        return "InventoryRequirement [hotel_id={0}, room_wise_inventory_requirement={1}]".format(
            self.hotel_id, self.room_wise_inventory_requirement
        )

    def add(self, room_type_id, from_date, to_date):
        self.room_type_ids.add(room_type_id)

        if not self.min_date:
            self.min_date = from_date
        else:
            self.min_date = min(self.min_date, from_date)

        if not self.max_date:
            self.max_date = to_date
        else:
            self.max_date = max(self.max_date, to_date)

        new_requirements = []
        for d in date_range(from_date, to_date):
            new_requirements.append(d)

        self.room_wise_inventory_requirement[room_type_id].update(new_requirements)

    def merge(self, inventory_requirement):
        room_wise_inventory_requirement = defaultdict(Counter)
        self.room_type_ids.update(inventory_requirement.room_type_ids)
        self.min_date = (
            min(self.min_date, inventory_requirement.min_date)
            if self.min_date
            else inventory_requirement.min_date
        )
        self.max_date = (
            max(self.max_date, inventory_requirement.max_date)
            if self.max_date
            else inventory_requirement.max_date
        )

        for k, v in chain(
            self.room_wise_inventory_requirement.items(),
            inventory_requirement.room_wise_inventory_requirement.items(),
        ):
            room_wise_inventory_requirement[k].update(v)
        self.room_wise_inventory_requirement = room_wise_inventory_requirement

    def __len__(self):
        return len(self.room_type_ids)
