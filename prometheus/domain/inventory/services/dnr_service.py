from typing import <PERSON><PERSON>

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.inventory.aggregates.dnr_aggregate import DNRAggregate
from prometheus.domain.inventory.aggregates.room_type_inventory_aggregate import (
    DateWiseAvailabilityChange,
)
from prometheus.domain.inventory.domain_events.dnr_removed import DNRRemovedEvent
from prometheus.domain.inventory.errors import InventoryError
from prometheus.domain.inventory.exceptions import InventoryException
from prometheus.domain.inventory.factories.dnr_factory import DNRFactory
from ths_common.value_objects import DNRTypeValueObject


@register_instance()
class DNRService(object):
    @staticmethod
    def mark_dnr(
        hotel_id,
        mark_dnr_data,
        room_type_inventory_aggregate,
        room_allotment_aggregate,
        hotel_context,
    ) -> Tuple[DNRAggregate, DateWiseAvailabilityChange]:
        """
        dnr.to_date -> Exclusive
        dnr.from_date -> Inclusive

        If DNR from_date is current business date, then housekeeping status should move to DNR (Out Of Order),
        and Room Current Status would continue to be Vacant.

        Question: What happens if there is already a booking on this room, to be checked out today?

        Question: At the DNR end date, when night audit runs, room housekeeping status should come out of DNR?

        """
        allotment_start_time = hotel_context.attach_free_late_checkout_time_to_date(
            mark_dnr_data.get('from_date')
        )
        allotment_end_time = dateutils.subtract(
            hotel_context.attach_switch_over_time_to_date(mark_dnr_data.get('to_date')),
            minutes=1,
        )
        room_allotment = room_allotment_aggregate.add_dnr_allotment(
            allotment_start_time,
            allotment_end_time,
            hotel_context=hotel_context,
            strict=False,
        )

        dnr_aggregate = DNRFactory.create_new_dnr(
            hotel_id, mark_dnr_data, room_allotment.allotment_id
        )

        # NOTE: End Inclusive is false. So it won't block inventory for dnr.end_date
        block_inventory_requirement = {
            d: 1
            for d in dateutils.date_range(
                dnr_aggregate.dnr.start_date, dnr_aggregate.dnr.end_date
            )
        }
        try:
            date_wise_room_type_inventory_availability_change = (
                room_type_inventory_aggregate.block_inventory(
                    block_inventory_requirement
                )
            )
        except InventoryException:
            raise InventoryException(error=InventoryError.INVENTORY_UNAVAILABLE_ERROR)

        return dnr_aggregate, date_wise_room_type_inventory_availability_change

    @staticmethod
    def edit_dnr(
        dnr_aggregate,
        dnr_data,
        room_type_inventory_aggregate,
        room_allotment_aggregate,
        hotel_context,
    ) -> DateWiseAvailabilityChange:
        dnr = dnr_aggregate.dnr
        if 'type' in dnr_data or 'subtype' in dnr_data or 'comments' in dnr_data:
            dnr_type = dnr_data.get('type', dnr.dnr_type.type)
            dnr_subtype = dnr_data.get('subtype', dnr.dnr_type.subtype)
            comments = (
                dnr_data.get('comments')
                if 'comments' in dnr_data
                else dnr.dnr_type.comments
            )
            dnr_type_value = DNRTypeValueObject(dnr_type, dnr_subtype, comments)
            dnr.update_type(dnr_type_value)

        current_from_date = dnr.start_date
        current_end_date = dnr.end_date
        new_from_date = dnr_data.get('from_date', current_from_date)
        new_end_date = dnr_data.get('to_date', current_end_date)

        if new_from_date >= new_end_date:
            raise InventoryException(
                error=InventoryError.INVALID_DNR,
                description="DNR start date should be less than DNR end date",
                extra_payload=dict(start_date=new_from_date, end_date=new_end_date),
            )

        allotment_start_time, allotment_end_time = None, None
        if new_from_date and new_from_date != current_from_date:
            dnr.update_from_date(new_from_date)
            allotment_start_time = hotel_context.attach_free_late_checkout_time_to_date(
                new_from_date
            )

        if new_end_date and new_end_date != current_end_date:
            dnr.update_to_date(new_end_date)
            allotment_end_time = dateutils.subtract(
                hotel_context.attach_switch_over_time_to_date(new_end_date), minutes=1
            )

        room_allotment_aggregate.update_dnr_allotment_expected_times(
            dnr.room_allotment_id,
            allotment_start_time,
            allotment_end_time,
            hotel_context,
        )

        if not room_type_inventory_aggregate:
            return dict()

        current_dnr_dates = set(
            list(dateutils.date_range(current_from_date, current_end_date))
        )
        new_dnr_dates = set(list(dateutils.date_range(new_from_date, new_end_date)))

        block_inventory_requirement = {
            date_: 1 for date_ in new_dnr_dates - current_dnr_dates
        }
        release_inventory_requirement = {
            date_: 1 for date_ in current_dnr_dates - new_dnr_dates
        }

        final_inventory_availability_change = dict()
        date_wise_room_type_inventory_availability_change = (
            room_type_inventory_aggregate.release_inventory(
                release_inventory_requirement
            )
        )

        final_inventory_availability_change.update(
            date_wise_room_type_inventory_availability_change
        )

        try:
            date_wise_room_type_inventory_availability_change = (
                room_type_inventory_aggregate.block_inventory(
                    block_inventory_requirement
                )
            )
            final_inventory_availability_change.update(
                date_wise_room_type_inventory_availability_change
            )

        except InventoryException:
            raise InventoryException(
                error=InventoryError.INVENTORY_UNAVAILABLE_ERROR,
                extra_payload=dict(dnr_id=dnr.dnr_id, dnr_data=dnr_data),
            )

        return final_inventory_availability_change

    @staticmethod
    def resolve_dnr(
        dnr_aggregate, room_type_inventory_aggregate, room_allotment_aggregate
    ):
        current_business_date = dateutils.datetime_at_given_time(
            crs_context.get_hotel_context().current_date(),
            dateutils.current_datetime().time(),
        )
        dnr_aggregate.dnr.mark_status_inactive(current_business_date)
        start_date = max(
            dateutils.to_date(dnr_aggregate.dnr.date_inactivated),
            dnr_aggregate.dnr.start_date,
        )
        release_inventory_requirement = {
            d: 1 for d in dateutils.date_range(start_date, dnr_aggregate.dnr.end_date)
        }
        date_wise_room_type_inventory_availability_change = (
            room_type_inventory_aggregate.release_inventory(
                release_inventory_requirement
            )
        )

        room_allotment = room_allotment_aggregate.get_room_allotment(
            dnr_aggregate.dnr.room_allotment_id
        )
        if dnr_aggregate.dnr.date_inactivated > room_allotment.expected_end_time:
            raise InventoryException(error=InventoryError.INACTIVE_DNR)

        room_allotment_aggregate.update_allotment_actual_end_time(
            dnr_aggregate.dnr.room_allotment_id, dnr_aggregate.dnr.date_inactivated
        )
        if dnr_aggregate.dnr.effective_duration == 0:
            room_allotment_aggregate.delete_room_allotment(
                dnr_aggregate.dnr.room_allotment_id
            )
        return date_wise_room_type_inventory_availability_change

    @staticmethod
    def remove_dnr(
        dnr_aggregate, room_type_inventory_aggregate, room_allotment_aggregate
    ):
        if (
            dnr_aggregate.dnr.is_inactive()
            and dnr_aggregate.dnr.effective_duration == 0
        ):
            release_inventory_requirement = dict()
        else:
            release_inventory_requirement = {
                d: 1
                for d in dateutils.date_range(
                    dnr_aggregate.dnr.start_date, dnr_aggregate.dnr.effective_end_date
                )
            }

        date_wise_room_type_inventory_availability_change = (
            room_type_inventory_aggregate.release_inventory(
                release_inventory_requirement
            )
        )

        dnr_aggregate.dnr.deleted = True
        room_allotment_aggregate.delete_room_allotment(
            dnr_aggregate.dnr.room_allotment_id
        )
        register_event(DNRRemovedEvent(dnr_id=dnr_aggregate.dnr.dnr_id))
        return date_wise_room_type_inventory_availability_change
