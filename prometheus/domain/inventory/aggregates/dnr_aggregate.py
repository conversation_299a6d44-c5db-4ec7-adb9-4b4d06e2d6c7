from prometheus import crs_context
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.access_entity_facts import AccessEntityFacts


class DNRAggregate(object):
    def __init__(self, dnr):
        self.dnr = dnr

        RuleEngine.action_allowed(
            action='access_entity',
            facts=AccessEntityFacts(
                user_data=crs_context.user_data,
                entity_vendor_id=dnr.hotel_id,
                entity_type="dnr",
            ),
            fail_on_error=True,
        )

    def increment_version(self):
        self.dnr.version += 1
