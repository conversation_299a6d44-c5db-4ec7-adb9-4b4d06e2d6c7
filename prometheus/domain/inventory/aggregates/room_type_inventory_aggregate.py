import datetime
import logging
from collections import Counter
from typing import Dict

from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.inventory.domain_events.over_booking_created import (
    OverBookingCreatedEvent,
)
from prometheus.domain.inventory.entities.room_type_inventory import RoomTypeInventory
from prometheus.domain.inventory.entities.room_type_inventory_availability import (
    RoomTypeInventoryAvailability,
)
from prometheus.domain.inventory.errors import InventoryError
from prometheus.domain.inventory.exceptions import InventoryException

logger = logging.getLogger(__name__)

DateWiseAvailabilityChange = Dict[datetime.date, RoomTypeInventoryAvailability]


class RoomTypeInventoryAggregate(object):
    __slots__ = (
        'room_type_inventory',
        'room_type_inventory_availabilities',
        'availability_grouped_by_date',
    )

    def __init__(
        self,
        room_type_inventory: RoomTypeInventory,
        room_type_inventory_availabilities: [RoomTypeInventoryAvailability],
    ):
        self.room_type_inventory = room_type_inventory
        self.room_type_inventory_availabilities = room_type_inventory_availabilities
        self.availability_grouped_by_date = {
            av.date: av for av in self.room_type_inventory_availabilities
        }

    @property
    def hotel_id(self):
        return self.room_type_inventory.hotel_id

    @property
    def room_type_id(self):
        return self.room_type_inventory.room_type_id

    def availability_exists_for_date(self, date_):
        return date_ in self.availability_grouped_by_date

    def get_availability_for_date(self, date_):
        return self.availability_grouped_by_date.get(date_)

    def can_support_all_inventory_requirement(
        self, date_wise_inventory_requirement: Counter
    ):
        return all(
            self.availability_grouped_by_date[date_].actual_count >= requested_count
            for date_, requested_count in date_wise_inventory_requirement.items()
        )

    def block_inventory(
        self, date_wise_inventory_requirement: Counter, allow_overbooking=False
    ) -> DateWiseAvailabilityChange:
        date_wise_room_type_inventory_availability_change = dict()
        for date_, requested_count in date_wise_inventory_requirement.items():
            availability = self.availability_grouped_by_date.get(date_)
            if not availability:
                logger.error("Inventory not available for date: %s", date_)
                raise InventoryException(
                    error=InventoryError.INVENTORY_UNAVAILABLE_ERROR
                )

            new_count = availability.actual_count - requested_count
            if new_count < 0 and not allow_overbooking:
                raise InventoryException(
                    error=InventoryError.INVENTORY_UNAVAILABLE_ERROR
                )

            if new_count < 0:
                register_event(
                    OverBookingCreatedEvent(
                        room_type_id=self.room_type_inventory.room_type_id,
                        old_inventory_count=availability.actual_count,
                        new_inventory_count=new_count,
                    )
                )

            availability.actual_count = new_count
            date_wise_room_type_inventory_availability_change[date_] = availability
        return date_wise_room_type_inventory_availability_change

    def release_inventory(
        self, date_wise_inventory_requirement: Counter
    ) -> DateWiseAvailabilityChange:
        date_wise_room_type_inventory_availability_change = dict()
        for date_, requested_count in date_wise_inventory_requirement.items():
            availability = self.availability_grouped_by_date[date_]
            new_count = availability.actual_count + requested_count
            availability.actual_count = new_count
            date_wise_room_type_inventory_availability_change[date_] = availability
        return date_wise_room_type_inventory_availability_change

    def add_availability(
        self, room_type_inventory_availability: RoomTypeInventoryAvailability
    ):
        self.room_type_inventory_availabilities.append(room_type_inventory_availability)
        self.availability_grouped_by_date[
            room_type_inventory_availability.date
        ] = room_type_inventory_availability

    def add_availability_for_missing_dates(self, date_range, count):
        for d in date_range.as_list():
            if d not in self.availability_grouped_by_date:
                availability = RoomTypeInventoryAvailability(
                    self.room_type_id, d, count
                )
                self.room_type_inventory_availabilities.append(availability)
                self.availability_grouped_by_date[d] = availability
