from sqlalchemy import func

from object_registry import register_instance
from prometheus.domain.inventory.adaptors.dnr_adaptor import DNRAdaptor
from prometheus.domain.inventory.aggregates.dnr_aggregate import DNRAggregate
from prometheus.domain.inventory.errors import InventoryError
from prometheus.domain.inventory.exceptions import InventoryException
from prometheus.domain.inventory.models import DNRModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.inventory_constants import DNR<PERSON>tatus, DNRType
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import InvalidOperationError, OutdatedVersion


@register_instance()
class DNRRepository(BaseRepository):
    dnr_adaptor = DNRAdaptor()

    def to_aggregate(self, **kwargs):
        dnr = kwargs['dnr']
        dnr_entity = self.dnr_adaptor.to_domain_entity(dnr)
        return DNRAggregate(dnr=dnr_entity)

    def from_aggregate(self, aggregate=None):
        dnr_entity = aggregate.dnr
        dnr_model = self.dnr_adaptor.to_db_entity(domain_entity=dnr_entity)
        return dnr_model

    def save(self, dnr_aggregate):
        dnr_model = self.from_aggregate(dnr_aggregate)
        self._save(dnr_model)
        self.flush_session()

    def save_all(self, dnr_aggregates):
        dnr_models = [
            self.from_aggregate(dnr_aggregate) for dnr_aggregate in dnr_aggregates
        ]
        self._save_all(dnr_models)
        self.flush_session()

    def update(self, dnr_aggregate):
        dnr_aggregate.increment_version()
        dnr_model = self.from_aggregate(dnr_aggregate)
        self._update(dnr_model)
        self.flush_session()

    def update_all(self, dnr_aggregates):
        dnr_models = []
        for dnr_aggregate in dnr_aggregates:
            dnr_aggregate.increment_version()
            dnr_models.append(self.from_aggregate(dnr_aggregate))
        self._update_all(dnr_models)
        self.flush_session()

    def load_dnrs(
        self, hotel_id, room_ids, from_date, to_date, source=None, types=None
    ):
        dnr_query = [DNRModel.hotel_id == hotel_id, DNRModel.room_id.in_(room_ids)]
        if source:
            dnr_query.append(DNRModel.source == source)

        if to_date and from_date:
            if to_date < from_date:
                return []
            dnr_query.append(DNRModel.start_date <= to_date)
            dnr_query.append(DNRModel.end_date > from_date)
        if types:
            dnr_query.append(DNRModel.type.in_(types))
        q = (
            self.filter(DNRModel, *dnr_query)
            .filter(DNRModel.deleted == False)
            .order_by(DNRModel.created_at.desc())
        )
        dnr_models = q.all()
        dnr_aggregates = [self.to_aggregate(dnr=dnr_model) for dnr_model in dnr_models]
        return dnr_aggregates

    def load_non_inactive_room_dnrs_count(self, hotel_id, current_date):
        q = (
            self.query(func.count(DNRModel.dnr_id))
            .filter(DNRModel.hotel_id == hotel_id)
            .filter(DNRModel.start_date <= current_date)
            .filter(DNRModel.end_date > current_date)
            .filter(DNRModel.type != DNRType.INACTIVE_ROOM)
            .filter(DNRModel.status == DNRStatus.ACTIVE.value)
            .filter(DNRModel.deleted == False)
        )
        return q.scalar()

    def load_inactive_room_dnrs_for_update(
        self, hotel_id, room_ids, from_date, source, types=None
    ):
        dnr_query = [
            DNRModel.hotel_id == hotel_id,
            DNRModel.room_id.in_(room_ids),
            DNRModel.status == DNRStatus.ACTIVE.value,
        ]
        if source:
            dnr_query.append(DNRModel.source == source)
            dnr_query.append(DNRModel.end_date > from_date)
        if types:
            dnr_query.append(DNRModel.type.in_(types))
        q = (
            self.filter(DNRModel, *dnr_query, for_update=True)
            .filter(DNRModel.deleted == False)
            .order_by(DNRModel.end_date.desc())
        )
        dnr_models = q.all()
        dnr_aggregates = [self.to_aggregate(dnr=dnr_model) for dnr_model in dnr_models]
        return dnr_aggregates

    def load_last_inactive_room_dnr_for_update(
        self, hotel_id, room_id, from_date, source, types=None
    ):
        dnr_query = [
            DNRModel.hotel_id == hotel_id,
            DNRModel.room_id == room_id,
            DNRModel.status == DNRStatus.ACTIVE.value,
        ]
        if source:
            dnr_query.append(DNRModel.source == source)
        if from_date:
            dnr_query.append(DNRModel.end_date > from_date)
        if types:
            dnr_query.append(DNRModel.type.in_(types))

        q = (
            self.filter(DNRModel, *dnr_query, for_update=True)
            .filter(DNRModel.deleted == False)
            .order_by(DNRModel.end_date.desc())
        )

        dnr_model = q.first()
        if not dnr_model:
            return None
        return self.to_aggregate(dnr=dnr_model)

    def load_dnr_for_update(self, hotel_id, dnr_id, version=None):
        dnr = self.get_for_update(DNRModel, dnr_id=dnr_id)
        if not dnr or dnr.hotel_id != hotel_id:
            raise InventoryException(error=InventoryError.DNR_NOT_FOUND)

        if version is not None and dnr.version != version:
            raise OutdatedVersion('DNR', version, dnr.version)

        return self.to_aggregate(dnr=dnr)

    def load_dnr(self, dnr_id):
        dnr = self.get(DNRModel, dnr_id=dnr_id)
        if not dnr:
            raise InventoryException(error=InventoryError.DNR_NOT_FOUND)
        return self.to_aggregate(dnr=dnr)

    def load_dnrs_by_id(self, hotel_id, dnr_ids, for_update=False):
        queryset = self.filter(
            DNRModel,
            DNRModel.dnr_id.in_(dnr_ids),
            for_update=for_update,
        )
        dnr_aggregates = []
        for dnr_model in queryset:
            if dnr_model.hotel_id != hotel_id:
                raise InventoryException(error=InventoryError.DNR_NOT_FOUND)
            dnr_aggregates.append(self.to_aggregate(dnr=dnr_model))
        return dnr_aggregates

    def load_all_dnrs(self):
        dnr_models = self.filter(DNRModel).order_by(DNRModel.created_at).all()
        dnr_aggregates = [self.to_aggregate(dnr=dnr_model) for dnr_model in dnr_models]
        return dnr_aggregates

    def delete_dnrs_for_hotel_id(self, hotel_id, user_data):
        if not user_data or user_data.user_type != UserType.CRS_MIGRATION_USER.value:
            raise InvalidOperationError("Only CRS Migration User can delete DNRs")

        deleted_dnr_count = (
            self.query(DNRModel)
            .filter(DNRModel.hotel_id == hotel_id)
            .delete(synchronize_session=False)
        )
        self.flush_session()
        return deleted_dnr_count

    def has_dnr_starting_from_given_date(self, hotel_id, room_id, date):
        dnr_query = [
            DNRModel.hotel_id == hotel_id,
            DNRModel.room_id == room_id,
            DNRModel.status == DNRStatus.ACTIVE.value,
            DNRModel.start_date == date,
            DNRModel.deleted.is_(False),
        ]
        return bool(self.filter(DNRModel, *dnr_query).first())

    def get_rooms_with_dnr_starting_from_given_date(self, hotel_id, date):
        dnr_query = [
            DNRModel.hotel_id == hotel_id,
            DNRModel.status == DNRStatus.ACTIVE.value,
            DNRModel.start_date == date,
            DNRModel.deleted.is_(False),
        ]
        room_ids = self.filter(DNRModel.room_id, *dnr_query).all()
        return {tup[0] for tup in room_ids}

    def get_rooms_with_dnr_ending_on_given_date(self, hotel_id, date):
        dnr_query = [
            DNRModel.hotel_id == hotel_id,
            DNRModel.status == DNRStatus.ACTIVE.value,
            DNRModel.end_date == date,
            DNRModel.deleted.is_(False),
        ]
        room_ids = self.filter(DNRModel.room_id, *dnr_query).all()
        return {tup[0] for tup in room_ids}

    def get_rooms_with_dnr_on_date(self, hotel_id, date):
        dnr_query = [
            DNRModel.hotel_id == hotel_id,
            DNRModel.status == DNRStatus.ACTIVE.value,
            DNRModel.start_date <= date,
            DNRModel.end_date > date,
            DNRModel.type != DNRType.INACTIVE_ROOM,
            DNRModel.deleted.is_(False),
        ]
        room_ids = self.filter(DNRModel.room_id, *dnr_query).all()
        return {tup[0] for tup in room_ids}
