from prometheus.domain.inventory.value_objects.hk_occupancy import HKOccupancy
from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.inventory_constants import HouseKeepingStatus


class HouseKeepingRecord(EntityChangeTracker):
    def __init__(
        self,
        hotel_id,
        room_id,
        housekeeping_status: HouseKeepingStatus,
        hk_occupancy: HKOccupancy = None,
        remarks=None,
        housekeeper_id=None,
        dirty=True,
        new=True,
    ):
        super().__init__(dirty=dirty, new=new)
        self.hotel_id = hotel_id
        self.room_id = room_id
        self.housekeeping_status = housekeeping_status
        self.hk_occupancy = hk_occupancy
        self.remarks = remarks
        self.housekeeper_id = housekeeper_id

    def update_housekeeping_status(self, housekeeping_status):
        self.housekeeping_status = housekeeping_status
        self.mark_dirty()

    def update_remarks(self, remarks):
        self.remarks = remarks
        self.mark_dirty()

    def update_housekeeper_id(self, housekeeper_id):
        self.housekeeper_id = housekeeper_id
        self.mark_dirty()

    def update_hk_occupancy(self, hk_occupancy: HKOccupancy):
        self.hk_occupancy = hk_occupancy
        self.mark_dirty()
