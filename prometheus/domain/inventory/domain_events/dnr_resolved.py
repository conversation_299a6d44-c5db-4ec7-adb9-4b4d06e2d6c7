from prometheus.core.base_domain_event import BaseDomainEvent
from ths_common.constants.domain_event_constants import DomainEvent


class DNRResolvedEvent(BaseDomainEvent):
    def __init__(self, dnr_id):
        self.dnr_id = dnr_id

    def serialize(self):
        return dict()

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.DNR_RESOLVED
