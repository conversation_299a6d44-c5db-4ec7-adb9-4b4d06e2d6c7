from ths_common.exceptions import CRSError


class InventoryError(CRSError):
    INVENTORY_ERROR = "0601", "Something went wrong with inventory"
    INVENTORY_UNAVAILABLE_ERROR = (
        "0602",
        "Room type is not available for selected dates in this hotel",
    )
    ROOM_INVENTORY_UNAVAILABLE_ERROR = (
        "0603",
        "Room is not available for selected dates",
    )
    ROOM_CURRENT_STATUS_MISSING = (
        "0604",
        "Current status of room is not known for given room_id",
    )
    DNR_NOT_FOUND = (
        "0605",
        "DNR with given id cannot be located. Please contact escalations team.",
    )
    INVALID_DNR = "0606", "Please select valid dates for the DNR creation/modification"
    INACTIVE_DNR = "0607", "DNR is already resolved"
    ROOM_INVENTORY_NOT_CREATED = (
        "0608",
        "Room type availability for the given dates is unknown. Please contact "
        "escalations team",
    )
    ROOM_UNAVAILABLE_FOR_ALLOCATION = (
        "0609",
        "Room is already allocated. Please select some other room",
    )
    OUT_OF_ORDER_ROOM_UNAVAILABLE_FOR_ALLOCATION = (
        "0610",
        "Room is Out of Order and hence cannot be allocated.",
    )
    ROOM_ALREADY_OCCUPIED = "0611", "Room is already occupied."
