from treebo_commons.utils import dateutils

from prometheus.domain.inventory.entities.dnr import DNR
from prometheus.domain.inventory.models import DNRModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.inventory_constants import (
    DNRSource,
    DNRStatus,
    DNRSubType,
    DNRType,
)
from ths_common.value_objects import DNRTypeValueObject


class DNRAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity, **kwargs):
        return DNRModel(
            dnr_id=domain_entity.dnr_id,
            hotel_id=domain_entity.hotel_id,
            room_id=domain_entity.room_id,
            start_date=domain_entity.start_date,
            end_date=domain_entity.end_date,
            type=domain_entity.dnr_type.type,
            sub_type=domain_entity.dnr_type.subtype,
            source=domain_entity.source,
            status=domain_entity.status.value,
            assigned_by=domain_entity.assigned_by,
            comments=domain_entity.dnr_type.comments,
            date_inactivated=domain_entity.date_inactivated,
            deleted=domain_entity.deleted,
            version=domain_entity.version,
            room_allotment_id=domain_entity.room_allotment_id,
        )

    def to_domain_entity(self, db_entity):
        dnr_type = DNRTypeValueObject(
            type=db_entity.type, subtype=db_entity.sub_type, comments=db_entity.comments
        )
        return DNR(
            dnr_id=db_entity.dnr_id,
            start_date=db_entity.start_date,
            end_date=db_entity.end_date,
            hotel_id=db_entity.hotel_id,
            room_id=db_entity.room_id,
            dnr_type=dnr_type,
            status=DNRStatus(db_entity.status),
            source=db_entity.source,
            assigned_by=db_entity.assigned_by,
            date_inactivated=dateutils.localize_datetime(db_entity.date_inactivated),
            deleted=db_entity.deleted,
            version=db_entity.version,
            room_allotment_id=db_entity.room_allotment_id,
        )
