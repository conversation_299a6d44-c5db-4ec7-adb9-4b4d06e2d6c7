from prometheus.domain.inventory.entities.room_type_inventory_availability import (
    RoomTypeInventoryAvailability,
)
from prometheus.domain.inventory.models import RoomTypeInventoryAvailabilityModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class RoomTypeInventoryAvailabilityAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity, **kwargs):
        return RoomTypeInventoryAvailabilityModel(
            hotel_id=kwargs['hotel_id'],
            room_type_id=domain_entity.room_type_id,
            date=domain_entity.date,
            count=domain_entity.actual_count,
        )

    def to_domain_entity(self, db_entity):
        return RoomTypeInventoryAvailability(
            room_type_id=db_entity.room_type_id,
            date=db_entity.date,
            actual_count=db_entity.count,
            dirty=False,
            new=False,
        )
