from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.inventory.aggregates.dnr_aggregate import DNRAggregate
from prometheus.domain.inventory.domain_events.dnr_created import DNRCreatedEvent
from prometheus.domain.inventory.dtos.dnr_event_data import DNREventData
from prometheus.domain.inventory.entities.dnr import DNR
from ths_common.constants.inventory_constants import DNRStatus
from ths_common.utils.id_generator_utils import random_id_generator
from ths_common.value_objects import DNRTypeValueObject


class DNRFactory(object):
    @staticmethod
    def create_new_dnr(hotel_id, dnr_data, room_allotment_id):
        dnr_type = DNRTypeValueObject(
            dnr_data.get('type'), dnr_data.get('subtype'), dnr_data.get('comments')
        )
        dnr = DNR(
            dnr_id=random_id_generator('DNR'),
            start_date=dnr_data.get('from_date'),
            end_date=dnr_data.get('to_date'),
            hotel_id=hotel_id,
            room_id=dnr_data.get('room_id'),
            dnr_type=dnr_type,
            status=DNRStatus.ACTIVE,
            source=dnr_data.get('source'),
            assigned_by=dnr_data.get('assigned_by'),
            date_inactivated=None,
            version=1,
            room_allotment_id=room_allotment_id,
        )

        dnr_aggregate = DNRAggregate(dnr=dnr)

        dnr_event_data = DNREventData(
            dnr_id=dnr.dnr_id,
            start_date=dnr.start_date,
            end_date=dnr.end_date,
            hotel_id=dnr.hotel_id,
            room_id=dnr.room_id,
            dnr_type=dnr.dnr_type.type,
            dnr_subtype=dnr.dnr_type.subtype,
            comments=dnr.dnr_type.comments,
            source=dnr.source,
            assigned_by=dnr.assigned_by,
        )
        register_event(
            DNRCreatedEvent(dnr_id=dnr_aggregate.dnr.dnr_id, dnr=dnr_event_data)
        )
        return dnr_aggregate
