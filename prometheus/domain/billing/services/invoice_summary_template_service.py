import logging
from datetime import timed<PERSON><PERSON>
from typing import Dict, List

from treebo_commons.money import Money
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import local_timezone

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.billing.dto.invoice_url_data import InvoiceUrlData
from prometheus.domain.catalog.repositories import SellerRepository
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.infrastructure.external_clients.pos_critical_task_client import (
    PosCriticalTaskClient as PosServiceClient,
)
from prometheus.infrastructure.external_clients.template_service_client import (
    TemplateFormat,
    TemplateNameSpace,
    TemplateService,
)
from schema_instances import get_schema_obj
from ths_common.constants.billing_constants import PaymentStatus
from ths_common.value_objects import TaxDetail

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        AwsServiceClient,
        PosServiceClient,
        SellerRepository,
    ]
)
class InvoiceSummaryTemplateService(object):
    DefaultSignedUrlExpirationHours = 2
    DefaultSignedUrlExpirationSeconds = DefaultSignedUrlExpirationHours * 3600

    def __init__(
        self,
        aws_service_client,
        pos_service_client,
        seller_repository,
    ):
        self.aws_service_client = aws_service_client
        self.pos_service_client = pos_service_client
        self.seller_repository = seller_repository

    def generate_invoice_templates(
        self,
        bill_aggregate,
        booking_aggregate,
        invoice_aggregate,
        include_bank_details=False,
        should_upload=False,
        hotel_aggregate=None,
        booking_meta=None,
        is_proforma_invoice=False,
    ):
        charges, guest_name, total_guests, room_nos = self._get_invoice_summary(
            bill_aggregate, invoice_aggregate
        )
        average_room_stay_price = bill_aggregate.average_posttax_room_stay_amount(
            charge_ids=[inc.charge_id for inc in invoice_aggregate.invoice_charges]
        )
        invoice_template = dict(
            vendor_context=dict(
                time_zone=str(local_timezone()),
                currency=bill_aggregate.bill.base_currency.value,
            ),
            booking=invoice_aggregate.invoice.parent_info,
            invoice=invoice_aggregate,
            charge_items_summary=[charges],
            payments=self._get_payment_details_for_template(
                bill_aggregate, invoice_aggregate
            ),
            bank_details=invoice_aggregate.invoice.issued_by.bank_details
            if include_bank_details
            else None,
            booking_meta=booking_meta,
            vendor_meta=dict(
                vendor_logo=hotel_aggregate.hotel.logo if hotel_aggregate else None,
                vendor_pan_number=hotel_aggregate.hotel.pan_number
                if hotel_aggregate
                else None,
                vendor_tan_number=hotel_aggregate.hotel.tan_number
                if hotel_aggregate
                else None,
                vendor_tin_number=hotel_aggregate.hotel.tin_number
                if hotel_aggregate
                else None,
                vendor_msme_number=hotel_aggregate.hotel.msme_number
                if hotel_aggregate
                else None,
                vendor_cin_number=hotel_aggregate.hotel.cin_number
                if hotel_aggregate
                else None,
            ),
            printed_by=crs_context.user_data.user
            if crs_context.user_data and crs_context.user_data.user
            else invoice_aggregate.invoice.generated_by,
            printed_on=dateutils.current_datetime(local_timezone()),
            guest_name=guest_name,
            total_guests=total_guests,
            room_nos=room_nos,
            average_room_stay_price=average_room_stay_price,
        )

        invoice_template['booking']['checkin_date'] = str(
            booking_aggregate.booking.actual_checkin_date
            or booking_aggregate.booking.checkin_date
        )
        invoice_template['booking']['checkout_date'] = str(
            booking_aggregate.booking.actual_checkout_date
            or booking_aggregate.booking.checkout_date
        )

        invoice_url = None
        if should_upload:
            namespace = (
                self._get_invoice_template_namespace(
                    invoice_aggregate, crs_context.get_hotel_context()
                )
                if not is_proforma_invoice
                else self._get_proforma_invoice_template_namespace(invoice_aggregate)
            )
            from prometheus.common.serializers import (
                HotelBookingSummaryInvoiceTemplateSchema,
            )

            template_json = (
                get_schema_obj(HotelBookingSummaryInvoiceTemplateSchema)
                .dump(invoice_template)
                .data
            )
            invoice_url = TemplateService().generate(
                namespace, template_json, TemplateFormat.PDF
            )

        url_for_signed_url_gen = (
            invoice_url
            if invoice_url is not None
            else invoice_aggregate.invoice.invoice_url
        )
        signed_url, expiration = self._generate_signed_url(
            invoice_aggregate, url_for_signed_url_gen
        )

        return invoice_template, (
            InvoiceUrlData(invoice_url, signed_url, expiration),
            invoice_aggregate.invoice.version,
        )

    def _get_invoice_summary(self, bill_aggregate, invoice_aggregate):
        charge_entries_map = dict()
        template_charges = []
        zero_money = Money(0, crs_context.hotel_context.base_currency)

        # region POS charges
        pos_charges = [
            inv_charge
            for inv_charge in invoice_aggregate.invoice_charges
            if inv_charge.charge_item.is_pos_charge()
        ]
        grouped_pos_charges = dict()
        pos_order_id_seller_name_map = dict()

        if pos_charges:
            pos_order_id_seller_name_map = self._get_seller_name_for_pos_order_ids(
                list(
                    set(
                        pos_charge.charge_item.pos_order_id()
                        for pos_charge in pos_charges
                        if not pos_charge.charge_item.pos_seller_name()
                    )
                )
            )

        for pos_charge in pos_charges:
            seller_name = pos_charge.charge_item.pos_seller_name()
            seller_name = (
                seller_name
                if seller_name
                else pos_order_id_seller_name_map.get(
                    pos_charge.charge_item.pos_order_id(), None
                )
            )
            pos_charge_group_key = (
                str(dateutils.to_date(pos_charge.applicable_date)),
                seller_name,
            )

            grouped_pos_charge = grouped_pos_charges.get(pos_charge_group_key, dict())
            self._group_charge_items(grouped_pos_charge, pos_charge, zero_money)

            grouped_pos_charges.setdefault(pos_charge_group_key, grouped_pos_charge)
        # endregion POS charges

        # region room stay charges

        addon_charge_ids = []
        for charge in bill_aggregate.charges:
            if charge.addon_charge_ids:
                addon_charge_ids.extend(charge.addon_charge_ids)

        non_pos_charges_map = {
            inv_charge.charge_id: inv_charge
            for inv_charge in invoice_aggregate.invoice_charges
            if not inv_charge.charge_item.is_pos_charge()
        }
        grouped_room_stay_charges = dict()
        grouped_other_charges = dict()
        guest_name = None
        non_primary_non_dummy_guest_name = None
        total_guests = set()
        room_nos = set()

        for charge_id, invoice_charge in non_pos_charges_map.items():
            if charge_id in addon_charge_ids:
                continue

            if invoice_charge.charge_item.name == 'RoomStay':
                charge_group_key = (
                    str(invoice_charge.applicable_date),
                    invoice_charge.charge_item.name,
                    invoice_charge.charge_item.details.get('occupancy'),
                    invoice_charge.charge_item.details.get('room_type'),
                )
                grouped_charge = grouped_room_stay_charges.get(charge_group_key, dict())

                self._group_charge_items(grouped_charge, invoice_charge, zero_money)
                grouped_charge['room_count'] = grouped_charge.get('room_count', 0) + 1
                grouped_charge[
                    'room_occupancy'
                ] = f"{invoice_charge.charge_item.details.get('occupancy', 0)} occupancy"
                grouped_charge['room_type'] = invoice_charge.charge_item.details.get(
                    'room_type'
                )

                if not guest_name:
                    for guest_detail in invoice_aggregate.invoice.get_users(
                        invoice_charge.charge_to_ids
                    ):
                        if guest_detail.is_primary and not guest_detail.dummy:
                            guest_name = guest_detail.name
                            break
                        elif not guest_detail.dummy:
                            non_primary_non_dummy_guest_name = guest_detail.name

                total_guests.update(invoice_charge.charge_to_ids)
                room_nos.add(invoice_charge.charge_item.details.get('room_no'))

                add_on_charge_ids = bill_aggregate.get_charge(
                    charge_id
                ).addon_charge_ids
                for add_on_charge_id in add_on_charge_ids:
                    add_on_invoice_charge = non_pos_charges_map.get(add_on_charge_id)

                    grouped_charge['posttax_amount'] = (
                        grouped_charge.get('posttax_amount', zero_money)
                        + add_on_invoice_charge.posttax_amount
                    )
                    grouped_charge['pretax_amount'] = (
                        grouped_charge.get('pretax_amount', zero_money)
                        + add_on_invoice_charge.pretax_amount
                    )
                    self.concatenate_tax_details(
                        grouped_charge, add_on_invoice_charge.tax_details
                    )

                grouped_charge['avg_pretax_room_rate'] = (
                    grouped_charge['pretax_amount'] / grouped_charge['room_count']
                    if grouped_charge['room_count'] > 0
                    else zero_money
                )

                grouped_room_stay_charges.setdefault(charge_group_key, grouped_charge)
            else:
                charge_group_key = (
                    str(invoice_charge.applicable_date),
                    invoice_charge.charge_item.name,
                )
                grouped_charge = grouped_other_charges.get(charge_group_key, dict())
                self._group_charge_items(grouped_charge, invoice_charge, zero_money)

                grouped_other_charges.setdefault(charge_group_key, grouped_charge)
        # endregion room stay charges

        for item in grouped_room_stay_charges.values():
            template_charges.append(item)

        for item in grouped_pos_charges.values():
            template_charges.append(item)

        for item in grouped_other_charges.values():
            template_charges.append(item)

        charge_entries_map['charges'] = template_charges
        room_nos = ", ".join(room_nos)
        return (
            charge_entries_map,
            guest_name or non_primary_non_dummy_guest_name,
            len(total_guests),
            room_nos,
        )

    def _group_charge_items(self, grouped_charge, invoice_charge, zero_money):
        grouped_charge['posting_date'] = invoice_charge.applicable_date
        grouped_charge['sku_category'] = invoice_charge.charge_item.name
        grouped_charge['posttax_amount'] = (
            grouped_charge.get('posttax_amount', zero_money)
            + invoice_charge.posttax_amount
        )
        grouped_charge['pretax_amount'] = (
            grouped_charge.get('pretax_amount', zero_money)
            + invoice_charge.pretax_amount
        )

        self.concatenate_tax_details(grouped_charge, invoice_charge.tax_details)

    @staticmethod
    def _get_invoice_template_namespace(invoice_aggregate, hotel_context):
        if invoice_aggregate.is_void():
            if invoice_aggregate.is_reseller_issued_invoice():
                return TemplateNameSpace.THS_VOID_RESELLER_INVOICE.value
            else:
                return (
                    TemplateNameSpace.THS_INDEPENDENT_VOID_HOTEL_INVOICE.value
                    if hotel_context.is_independent_hotel()
                    else TemplateNameSpace.THS_VOID_HOTEL_INVOICE.value
                )

        else:
            if invoice_aggregate.is_reseller_issued_invoice():
                return TemplateNameSpace.THS_RESELLER_INVOICE.value
            else:
                return (
                    TemplateNameSpace.THS_INDEPENDENT_HOTEL_INVOICE.value
                    if hotel_context.is_independent_hotel()
                    else TemplateNameSpace.DATE_AND_CATEGORY_WISE_SUMMARY_INVOICE.value
                )

    @staticmethod
    def _get_proforma_invoice_template_namespace(invoice_aggregate):
        return (
            TemplateNameSpace.THS_VOID_HOTEL_PROFORMA_INVOICE.value
            if invoice_aggregate.is_void()
            else TemplateNameSpace.DATE_AND_CATEGORY_WISE_SUMMARY_PROFORMA_INVOICE.value
        )

    @staticmethod
    def _get_payment_details_for_template(bill_aggregate, invoice_aggregate):
        payments_for_template = []
        payment_splits = bill_aggregate.get_payment_splits(
            invoice_aggregate.invoice.billed_entity_account
        )
        for payment_id, payment_split in payment_splits.items():
            payment = bill_aggregate.get_payment(payment_id)
            if payment.status == PaymentStatus.CANCELLED:
                continue
            payments_for_template.append(
                dict(
                    date_of_payment=dateutils.to_date(payment.date_of_payment),
                    id="{}-{}-{}".format(
                        invoice_aggregate.invoice.bill_id,
                        payment.payment_id,
                        payment_split.payment_split_id,
                    ),
                    payment_type=payment_split.payment_type.label,
                    payment_mode=payment_split.payment_mode,
                    amount=payment_split.amount,
                )
            )
        return payments_for_template

    def _get_seller_name_for_pos_order_ids(self, pos_order_ids: List[str]):
        try:
            pos_orders_details = self.pos_service_client.get_pos_orders_details(
                pos_order_ids
            )
            data = pos_orders_details.get('data')

            if data:
                orders = data.get('orders')
                pos_order_seller_id_map = dict()

                for order in orders:
                    pos_order_seller_id_map[order.get('order_id')] = order.get(
                        'seller_id'
                    )

                seller_aggregates = self.seller_repository.load_all(
                    list(set(pos_order_seller_id_map.values()))
                )
                seller_id_name_map = {
                    seller_aggregate.seller.seller_id: seller_aggregate.seller.name
                    for seller_aggregate in seller_aggregates
                }

                return {
                    pos_order_id: seller_id_name_map.get(seller_id, None)
                    for pos_order_id, seller_id in pos_order_seller_id_map.items()
                }
        except Exception as e:
            logger.error(
                f"Error in getting seller names for pos_order_ids: {pos_order_ids}, Exception - [{str(e)}]"
            )

        return dict()

    @staticmethod
    def concatenate_tax_details(invoice_charge: Dict, tax_details: List[TaxDetail]):
        tax_breakup = invoice_charge.get('tax_breakup', None)
        if tax_breakup:
            for tax_detail in tax_details:
                for tax in tax_breakup:
                    if tax.get('tax_type') == tax_detail.tax_type:
                        tax['amount'] += Money(
                            tax_detail.amount, crs_context.hotel_context.base_currency
                        )
                        break
        else:
            invoice_charge['tax_breakup'] = []
            for tax_detail in tax_details:
                invoice_charge['tax_breakup'].append(
                    dict(amount=tax_detail.amount, tax_type=tax_detail.tax_type)
                )

    def _generate_signed_url(self, invoice_aggregate, invoice_url):
        if not invoice_url:
            logger.warning(
                "Not generating signed url."
                "No invoice url is present for invoice: {}".format(
                    invoice_aggregate.invoice.invoice_id
                )
            )
            return None, None

        presigned_url = self.aws_service_client.get_presigned_url_from_s3_url(
            s3_url=invoice_url, link_expires_in=self.DefaultSignedUrlExpirationSeconds
        )
        return presigned_url, dateutils.today() + timedelta(
            hours=self.DefaultSignedUrlExpirationHours
        )
