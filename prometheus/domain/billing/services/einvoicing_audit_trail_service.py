from datetime import datetime
from typing import Optional

from object_registry import register_instance
from prometheus.domain.billing.entities.einvoicing_audit_trail import (
    EInvoicingAuditTrail,
    EInvoicingAuditTrailEventType,
    EInvoicingAuditTrailActionType,
)
from prometheus.domain.billing.repositories.einvoicing_audit_trail_repository import (
    EInvoicingAuditTrailRepository,
)
from treebo_commons.request_tracing.context import get_current_request_id
from treebo_commons.utils import dateutils


@register_instance(dependencies=[EInvoicingAuditTrailRepository])
class EInvoicingAuditTrailService:
    def __init__(self, einvoicing_audit_trail_repository: EInvoicingAuditTrailRepository):
        self.einvoicing_audit_trail_repository = einvoicing_audit_trail_repository

    def record_cleartax_error(
        self,
        hotel_id: str,
        booking_id: Optional[str] = None,
        invoice_id: Optional[str] = None,
        credit_note_id: Optional[str] = None,
        bill_id: Optional[str] = None,
        cleartax_error_code: Optional[str] = None,
        cleartax_error_message: Optional[str] = None,
        cleartax_response_payload: Optional[dict] = None,
        user_id: Optional[str] = None,
        user_type: Optional[str] = None,
        auth_id: Optional[str] = None,
        application: Optional[str] = None,
        remarks: Optional[str] = None,
    ):
        """Record a cleartax error in the audit trail"""
        audit_trail = EInvoicingAuditTrail(
            einvoicing_audit_trail_id=None,  # Will be auto-generated
            hotel_id=hotel_id,
            booking_id=booking_id,
            invoice_id=invoice_id,
            credit_note_id=credit_note_id,
            bill_id=bill_id,
            event_type=EInvoicingAuditTrailEventType.CLEARTAX_ERROR,
            action_type=EInvoicingAuditTrailActionType.SYSTEM_ERROR,
            user_action="cleartax_error_occurred",
            user_id=user_id,
            user_type=user_type,
            auth_id=auth_id,
            application=application,
            request_id=get_current_request_id(),
            cleartax_error_code=cleartax_error_code,
            cleartax_error_message=cleartax_error_message,
            cleartax_response_payload=cleartax_response_payload,
            action_datetime=dateutils.current_datetime(),
            remarks=remarks,
        )
        self.einvoicing_audit_trail_repository.save(audit_trail)
        return audit_trail

    def record_user_action(
        self,
        hotel_id: str,
        event_type: str,
        action_type: str,
        user_action: str,
        booking_id: Optional[str] = None,
        invoice_id: Optional[str] = None,
        credit_note_id: Optional[str] = None,
        bill_id: Optional[str] = None,
        user_id: Optional[str] = None,
        user_type: Optional[str] = None,
        auth_id: Optional[str] = None,
        application: Optional[str] = None,
        remarks: Optional[str] = None,
    ):
        """Record a user action in the audit trail"""
        audit_trail = EInvoicingAuditTrail(
            einvoicing_audit_trail_id=None,  # Will be auto-generated
            hotel_id=hotel_id,
            booking_id=booking_id,
            invoice_id=invoice_id,
            credit_note_id=credit_note_id,
            bill_id=bill_id,
            event_type=event_type,
            action_type=action_type,
            user_action=user_action,
            user_id=user_id,
            user_type=user_type,
            auth_id=auth_id,
            application=application,
            request_id=get_current_request_id(),
            action_datetime=dateutils.current_datetime(),
            remarks=remarks,
        )
        self.einvoicing_audit_trail_repository.save(audit_trail)
        return audit_trail

    def record_proceed_without_einvoice(
        self,
        hotel_id: str,
        booking_id: str,
        invoice_id: str,
        user_id: str,
        user_type: str,
        auth_id: Optional[str] = None,
        application: Optional[str] = None,
        remarks: Optional[str] = None,
    ):
        """Record when user chooses to proceed without e-invoice"""
        return self.record_user_action(
            hotel_id=hotel_id,
            event_type=EInvoicingAuditTrailEventType.CHECKOUT_WITHOUT_EINVOICE,
            action_type=EInvoicingAuditTrailActionType.USER_SELECTED_PROCEED_WITHOUT_EINVOICE,
            user_action="proceed_checkout_without_einvoice",
            booking_id=booking_id,
            invoice_id=invoice_id,
            user_id=user_id,
            user_type=user_type,
            auth_id=auth_id,
            application=application,
            remarks=remarks,
        )

    def record_remove_gst_and_checkout(
        self,
        hotel_id: str,
        booking_id: str,
        invoice_id: str,
        user_id: str,
        user_type: str,
        auth_id: Optional[str] = None,
        application: Optional[str] = None,
        remarks: Optional[str] = None,
    ):
        """Record when user chooses to remove GST and proceed"""
        return self.record_user_action(
            hotel_id=hotel_id,
            event_type=EInvoicingAuditTrailEventType.REMOVE_GST_AND_CHECKOUT,
            action_type=EInvoicingAuditTrailActionType.USER_SELECTED_REMOVE_GST,
            user_action="remove_gst_and_checkout",
            booking_id=booking_id,
            invoice_id=invoice_id,
            user_id=user_id,
            user_type=user_type,
            auth_id=auth_id,
            application=application,
            remarks=remarks,
        )
