from typing import Optional
import uuid

from object_registry import register_instance
from prometheus.domain.billing.entities.einvoicing_audit_trail import (
    EInvoicingAuditTrail,
    EInvoicingAuditType,
)
from prometheus.domain.billing.repositories.einvoicing_audit_trail_repository import (
    EInvoicingAuditTrailRepository,
)
from treebo_commons.request_tracing.context import get_current_request_id


@register_instance(dependencies=[EInvoicingAuditTrailRepository])
class EInvoicingAuditTrailService:
    def __init__(self, einvoicing_audit_trail_repository: EInvoicingAuditTrailRepository):
        self.einvoicing_audit_trail_repository = einvoicing_audit_trail_repository

    def record_cleartax_error(
        self,
        hotel_id: str,
        invoice_id: str,
        cleartax_error_code: Optional[str] = None,
        cleartax_error_message: Optional[str] = None,
        cleartax_response_payload: Optional[dict] = None,
        user: Optional[str] = None,
        user_type: Optional[str] = None,
    ):
        """Record a cleartax error in the audit trail"""
        audit_payload = {
            "cleartax_error_code": cleartax_error_code,
            "cleartax_error_message": cleartax_error_message,
            "cleartax_response_payload": cleartax_response_payload,
        }

        audit_trail = EInvoicingAuditTrail(
            audit_id=str(uuid.uuid4()),
            hotel_id=hotel_id,
            invoice_id=invoice_id,
            audit_type=EInvoicingAuditType.CLEARTAX_ERROR,
            audit_payload=audit_payload,
            user=user,
            user_type=user_type,
            request_id=get_current_request_id(),
        )
        self.einvoicing_audit_trail_repository.save(audit_trail)
        return audit_trail

    def record_proceed_without_einvoice(
        self,
        hotel_id: str,
        invoice_id: str,
        user: str,
        user_type: str,
        booking_id: Optional[str] = None,
    ):
        """Record when user chooses to proceed without e-invoice"""
        audit_payload = {
            "action": "proceed_checkout_without_einvoice",
            "booking_id": booking_id,
            "description": "User selected to proceed with checkout without e-invoice. GST will be mentioned on invoice.",
        }

        audit_trail = EInvoicingAuditTrail(
            audit_id=str(uuid.uuid4()),
            hotel_id=hotel_id,
            invoice_id=invoice_id,
            audit_type=EInvoicingAuditType.USER_SELECTED_PROCEED_WITHOUT_EINVOICE,
            audit_payload=audit_payload,
            user=user,
            user_type=user_type,
            request_id=get_current_request_id(),
        )
        self.einvoicing_audit_trail_repository.save(audit_trail)
        return audit_trail

    def record_remove_gst_and_checkout(
        self,
        hotel_id: str,
        invoice_id: str,
        user: str,
        user_type: str,
        booking_id: Optional[str] = None,
    ):
        """Record when user chooses to remove GST and proceed"""
        audit_payload = {
            "action": "remove_gst_and_checkout",
            "booking_id": booking_id,
            "description": "User selected to remove GST and proceed with checkout.",
        }

        audit_trail = EInvoicingAuditTrail(
            audit_id=str(uuid.uuid4()),
            hotel_id=hotel_id,
            invoice_id=invoice_id,
            audit_type=EInvoicingAuditType.USER_SELECTED_REMOVE_GST,
            audit_payload=audit_payload,
            user=user,
            user_type=user_type,
            request_id=get_current_request_id(),
        )
        self.einvoicing_audit_trail_repository.save(audit_trail)
        return audit_trail
