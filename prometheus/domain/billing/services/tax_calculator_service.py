import logging
import os

from treebo_commons.money import Money

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.billing.dto.charge_data_for_tax_query import (
    ChargeDataForTaxQuery,
)
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from prometheus.infrastructure.external_clients.taxation_service_client import (
    TaxationServiceClient,
    TaxCalculatorType,
)
from ths_common.constants.billing_constants import TaxTypes
from ths_common.exceptions import DownstreamSystemFailure
from ths_common.value_objects import GSTDetails, NotAssigned, TaxDetail

logger = logging.getLogger(__name__)


class TaxResponse(object):
    def __init__(self, id, pretax_amount, posttax_amount, tax_amount, tax_details):
        self.id = id
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.tax_amount = tax_amount
        self.tax_details = tax_details


class TaxQueryItem(object):
    def __init__(self, category_sku_id, attributes, index=None):
        self.index = index
        self.category_id = category_sku_id
        self.attributes = attributes
        self.prices = []

    def __hash__(self):
        return hash(
            (
                self.category_id,
                frozenset([frozenset(item.items()) for item in self.attributes]),
            )
        )

    def __eq__(self, other):
        return self.__hash__() == other.__hash__()

    def add_price(self, index, date, price):
        self.prices.append(
            dict(index=str(index), date=date.strftime("%Y-%m-%d"), price=price)
        )

    def get_dict(self):
        return dict(
            index=self.index,
            category_id=self.category_id,
            attributes=self.attributes,
            prices=self.prices,
        )


class TaxQueryItemFactory:
    @staticmethod
    def create_tax_query_item(
        sku_category_id,
        buyer_is_sez=False,
        buyer_gstin_num='',
        buyer_has_lut=False,
        seller_has_lut=False,
        seller_id=None,
        hotel_id=None,
    ):
        assert (
            seller_id or hotel_id
        ), "Either Seller ID or Hotel ID is required to compute tax"
        attributes = [
            dict(key="seller_has_lut", value=str(True if seller_has_lut else False)),
            dict(key="buyer_has_lut", value=str(True if buyer_has_lut else False)),
            dict(key="is_buyer_in_sez", value=str(True if buyer_is_sez else False)),
            dict(
                key="buyer_gstin", value='' if not buyer_gstin_num else buyer_gstin_num
            ),
        ]

        if seller_id:
            attributes.append(dict(key="seller_id", value=seller_id))
        else:
            attributes.append(dict(key="hotel_id", value=hotel_id))

        return TaxQueryItem(sku_category_id, attributes)


@register_instance(dependencies=[TaxationServiceClient])
class TaxCalculatorService(object):
    def __init__(self, tax_service_client=None):
        # TODO move tax calculator service object creation out of all classes and inject
        # Inject alerting service into taxcalculatorservice when that is done
        environment = os.environ.get('APP_ENV', 'local')
        self.alerting_service = NewrelicServiceClient(environment)
        self.tax_service_client = (
            tax_service_client if tax_service_client else TaxationServiceClient()
        )

    @classmethod
    def create_tax_response(cls, computed_price):
        currency = (
            crs_context.hotel_context.base_currency
            if crs_context.hotel_context
            else crs_context.seller_context.base_currency
        )

        tax_details = []
        for tax_breakup in computed_price['tax_breakup']:
            # NOTE: tax_type can be percent/flat. Earlier `tax_type` was `cgst/sgst`, which has moved to `tax_code`
            # key in tax response.
            # If tax_type is flat, then tax_value == tax_amount. We'll keep percentage as `None` in `TaxDetail`
            # `None` value for `percentage` will mean that `TaxType` is FLAT. So we don't need to store `TaxType`
            # from tax response in CRS.
            # That way, we keep storing `tax_code` in `tax_type`, without having to introduce `tax_code` column in
            # CRS, and breaking all client
            tax_detail = TaxDetail(
                tax_type=tax_breakup['tax_code'],
                percentage=tax_breakup['tax_value']
                if tax_breakup['tax_type'] == 'PERCENT'
                else None,
                amount=Money(str(tax_breakup['tax_amount']), currency),
            )
            tax_details.append(tax_detail)

        # Todo: Handle different tax types (flat/percent) and remove TaxType enum for tax codes (cgst, sgst, etc)
        pre_tax_price = Money(str(computed_price['pretax_price']), currency)
        post_tax_price = Money(str(computed_price['posttax_price']), currency)
        tax_amount = Money(str(computed_price['tax_amount']), currency)
        return TaxResponse(
            id=int(computed_price['index']),
            pretax_amount=pre_tax_price,
            posttax_amount=post_tax_price,
            tax_amount=tax_amount,
            tax_details=tax_details,
        )

    def calculate_taxes(
        self,
        charge_query_dtos: [ChargeDataForTaxQuery],
        buyer_gst_details: GSTDetails = None,
        seller_has_lut=False,
        seller_id=None,
        hotel_id=None,
    ):
        buyer_is_sez, buyer_has_lut, buyer_gstin_num = False, False, ''
        if buyer_gst_details:
            buyer_is_sez, buyer_has_lut, buyer_gstin_num = (
                buyer_gst_details.is_sez,
                buyer_gst_details.has_lut,
                buyer_gst_details.gstin_num,
            )

        category_map_pretax = dict()
        category_map_posttax = dict()

        index = 0

        # create charge_items
        for query_dto in charge_query_dtos:
            if query_dto.buyer_gst_details:
                item_buyer_is_sez, item_buyer_has_lut, item_buyer_gstin_num = (
                    query_dto.buyer_gst_details.is_sez,
                    query_dto.buyer_gst_details.has_lut,
                    query_dto.buyer_gst_details.gstin_num,
                )
            else:
                item_buyer_is_sez, item_buyer_has_lut, item_buyer_gstin_num = (
                    buyer_is_sez,
                    buyer_has_lut,
                    buyer_gstin_num,
                )
            if (
                query_dto.pretax_amount is not None
                and query_dto.pretax_amount != NotAssigned
            ):
                category_map = category_map_pretax
                price = query_dto.pretax_amount.amount
            else:
                category_map = category_map_posttax
                price = query_dto.posttax_amount.amount

            item = TaxQueryItemFactory.create_tax_query_item(
                query_dto.sku_category_id,
                buyer_is_sez=item_buyer_is_sez,
                buyer_gstin_num=item_buyer_gstin_num,
                buyer_has_lut=item_buyer_has_lut,
                seller_has_lut=seller_has_lut,
                seller_id=seller_id,
                hotel_id=hotel_id,
            )

            if category_map.get(item):
                category_map[item].add_price(
                    query_dto.id, query_dto.applicable_date, price
                )
            else:
                index += 1
                item.index = str(index)
                item.add_price(query_dto.id, query_dto.applicable_date, price)
                category_map[item] = item

        # call tax service to calculate taxes
        try:
            computed_prices = []
            if category_map_pretax:
                item_list = [item.get_dict() for item in category_map_pretax.values()]
                computed_prices.extend(
                    self.tax_service_client.fetch_taxes(
                        item_list, TaxCalculatorType.PRE_TAX
                    )
                )

            if category_map_posttax:
                item_list = [item.get_dict() for item in category_map_posttax.values()]
                computed_prices.extend(
                    self.tax_service_client.fetch_taxes(
                        item_list, TaxCalculatorType.POST_TAX
                    )
                )
        except DownstreamSystemFailure as ex:
            self.alerting_service.record_event(
                "tax_computation_failure",
                {"message": ex.message, "description": ex.description},
            )
            raise

        # create responses
        responses = dict()
        for computed_price in computed_prices:
            for price in computed_price.get('prices'):
                responses[int(price['index'])] = self.create_tax_response(price)

        return responses
