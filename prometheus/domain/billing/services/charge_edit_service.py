from decimal import Decimal
from typing import List

from object_registry import register_instance
from prometheus.common.helpers import occupancy_change_handler
from prometheus.core.globals import crs_context
from prometheus.domain.billing.dto.edit_charge_data import EditChargeData
from prometheus.domain.billing.dto.taxable_item import TaxableItem
from prometheus.domain.billing.entities.billed_entity import BillingInstructionVO
from prometheus.domain.billing.entities.charge_split import ChargeSplit
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.exceptions import BillingError
from prometheus.domain.billing.services.tax_service import TaxService
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from ths_common.constants.billing_constants import (
    BillAppId,
    ChargeStatus,
    ChargeSubTypes,
    ChargeTypes,
    PaymentInstruction,
)
from ths_common.exceptions import ValidationException
from ths_common.utils.math_utils import logical_xor
from ths_common.value_objects import GSTDetails, NotAssigned, PriceData

# TODO: Move to application layer


@register_instance(dependencies=[TaxService])
class ChargeEditService(object):
    def __init__(self, tax_service):
        self.tax_service = tax_service

    @classmethod
    def edit_charges(
        cls,
        bill_aggregate,
        edit_charge_dtos: List[EditChargeData],
        attached_invoice_aggregates,
        user_data,
        hotel_context,
        grouped_sku_categories={},
    ):
        edited_charges = []
        for edit_dto in edit_charge_dtos:
            invoice_aggregates = (
                attached_invoice_aggregates.get(edit_dto.charge_id)
                if attached_invoice_aggregates
                else None
            )
            charge = cls._edit_charge(
                invoice_aggregates,
                bill_aggregate,
                edit_dto,
                user_data,
                hotel_context,
                grouped_sku_categories=grouped_sku_categories,
            )
            edited_charges.append(charge)

        return edited_charges

    def update_tax_amounts_of_charges(
        self,
        bill_aggregate,
        edit_charge_dtos,
        gst_details: GSTDetails = None,
        seller_model=None,
    ):
        """

        :param bill_aggregate:
        :param edit_charge_dtos:
        :param gst_details:
        :param seller_model:
        it once seller_context is set
        :return:
        """

        taxable_items = []
        edit_data_map = dict()
        for edit_data in edit_charge_dtos:
            if (
                edit_data.pretax_amount == NotAssigned
                and edit_data.posttax_amount == NotAssigned
            ):
                continue
            if not logical_xor(edit_data.pretax_amount, edit_data.posttax_amount):
                raise BillingError(
                    error=BillingErrors.CANT_HAVE_BOTH_PRETAX_AND_POSTTAX
                )

            edit_data_map[edit_data.charge_id] = edit_data
            charge = bill_aggregate.get_charge(edit_data.charge_id)
            taxable_items.append(
                TaxableItem(
                    charge_id=edit_data.charge_id,
                    pretax_amount=edit_data.pretax_amount,
                    posttax_amount=edit_data.posttax_amount,
                    applicable_date=charge.applicable_date,
                    sku_category_id=charge.item.sku_category_id,
                    buyer_gst_details=edit_data.buyer_gst_details,
                )
            )

        # TODO: Change tax_service signature to work for both pos and crs app, in the same way
        if bill_aggregate.bill.app_id == BillAppId.POS_APP.value:
            taxable_items = self.tax_service.calculate_taxes(
                taxable_items,
                buyer_gst_details=gst_details,
                seller_id=bill_aggregate.vendor_id,
            )
        else:
            taxable_items = self.tax_service.calculate_taxes(
                taxable_items,
                buyer_gst_details=gst_details,
                seller_has_lut=self.tax_service.seller_has_lut(
                    seller_model, crs_context.get_hotel_context()
                ),
                hotel_id=bill_aggregate.vendor_id,
            )

        for taxable_item in taxable_items:
            edit_data = edit_data_map.get(taxable_item.charge_id)
            edit_data.pretax_amount = taxable_item.pretax_amount
            edit_data.posttax_amount = taxable_item.posttax_amount
            edit_data.tax_amount = taxable_item.tax_amount
            edit_data.tax_details = taxable_item.tax_details

        return edit_charge_dtos

    def cancel_charges(
        self,
        bill_aggregate,
        charge_ids,
        attached_invoice_aggregates=None,
        posting_date=None,
    ):
        canceled_charges = bill_aggregate.cancel_charges(
            charge_ids, posting_date=posting_date
        )
        return canceled_charges

    def recompute_tax_on_all_charges(
        self,
        bill_aggregate,
        gst_details: GSTDetails = None,
        change_posttax=False,
        seller_model=None,
        charge_ids=None,
    ):
        if charge_ids:
            charges = [
                charge
                for charge in bill_aggregate.active_created_charges
                if charge.charge_id in charge_ids
            ]
        else:
            charges = bill_aggregate.active_created_charges
        if change_posttax:
            price_data = [
                PriceData(
                    pretax_amount=ch.pretax_amount,
                    posttax_amount=NotAssigned,
                    applicable_date=NotAssigned,
                    bill_to_type=NotAssigned,
                    type=NotAssigned,
                    charge_id=ch.charge_id,
                    charge_to=NotAssigned,
                )
                for ch in charges
            ]
        else:
            price_data = [
                PriceData(
                    pretax_amount=NotAssigned,
                    posttax_amount=ch.posttax_amount,
                    applicable_date=NotAssigned,
                    bill_to_type=NotAssigned,
                    type=NotAssigned,
                    charge_id=ch.charge_id,
                    charge_to=NotAssigned,
                )
                for ch in charges
            ]

        charge_dtos = [
            EditChargeData(charge_id=pd.charge_id, price_data=pd) for pd in price_data
        ]

        edit_dtos = self.update_tax_amounts_of_charges(
            bill_aggregate,
            charge_dtos,
            gst_details=gst_details,
            seller_model=seller_model,
        )
        for edit_dto in edit_dtos:
            self._edit_charge(None, bill_aggregate, edit_dto, None, None)

    @classmethod
    def _edit_charge(
        cls,
        invoice_aggregates,
        bill_aggregate,
        edit_data: EditChargeData,
        user_data,
        hotel_context,
        grouped_sku_categories={},
    ):
        if user_data and hotel_context:
            invoiced_charge_override_allowed = RuleEngine.action_allowed(
                action='invoiced_charge_edit',
                facts=Facts(
                    user_type=user_data.user_type,
                    action_payload=edit_data,
                    hotel_context=hotel_context,
                ),
            )
        else:
            invoiced_charge_override_allowed = True

        if invoice_aggregates:
            cls._fail_if_charge_in_non_editable_invoice(invoice_aggregates)

        charge = bill_aggregate.get_charge(edit_data.charge_id)

        if edit_data.bill_to_type != NotAssigned:
            bill_aggregate.update_charge_bill_to_type(
                edit_data.charge_id, edit_data.bill_to_type
            )

        if edit_data.type != NotAssigned:
            bill_aggregate.update_charge_type(edit_data.charge_id, edit_data.type)

        if edit_data.charge_item != NotAssigned:
            charge.update_charge_item_details(edit_data.charge_item.details)

        if edit_data.comments != NotAssigned:
            charge.update_comment(edit_data.comments)

        if edit_data.charge_to != NotAssigned:
            charge.update_charge_to(charge_to=edit_data.charge_to)

        if edit_data.pretax_amount != NotAssigned:
            bill_aggregate.update_charge_amounts(
                charge_id=edit_data.charge_id,
                pretax_amount=edit_data.pretax_amount,
                posttax_amount=edit_data.posttax_amount,
                tax_amount=edit_data.tax_amount,
                tax_details=edit_data.tax_details,
                override=invoiced_charge_override_allowed,
                grouped_sku_categories=grouped_sku_categories,
            )

        if edit_data.billing_instructions != NotAssigned:
            bill_aggregate.update_billing_instructions(
                edit_data.charge_id, edit_data.billing_instructions
            )

        return charge

    @staticmethod
    def _fail_if_charge_in_non_editable_invoice(invoice_aggregates):
        for invoice_aggregate in invoice_aggregates:
            if not invoice_aggregate.invoice.is_editable():
                raise ValidationException(BillingErrors.CANNOT_MODIFY_INVOICED_CHARGE)

    def update_room_stay_charge(
        self, bill_aggregate, new_prices: List[PriceData], room_stay, booking_aggregate
    ):
        edit_charge_dtos = []
        for new_price in new_prices:
            charge_id = room_stay.get_charge_for_date(new_price.applicable_date)
            if not bill_aggregate.filter_and_get_charges(
                [charge_id], allowed_charge_status=[ChargeStatus.CREATED]
            ):
                continue
            if new_price.billing_instructions == NotAssigned:
                # Room stay charge has only one split
                room_stay_charge = bill_aggregate.get_charge(charge_id)
                existing_charge_split = room_stay_charge.charge_splits[0]
                if (
                    existing_charge_split.charge_sub_type == ChargeSubTypes.SPOT_CREDIT
                    and existing_charge_split.charge_type != new_price.type
                ):
                    new_price.billing_instructions = self.update_billing_instruction_to_convert_spot_credit_to_non_credit(
                        existing_charge_split, bill_aggregate
                    )
            new_price.charge_id = charge_id
            edit_charge_dtos.append(
                EditChargeData(
                    charge_id=charge_id,
                    price_data=new_price,
                    buyer_gst_details=new_price.buyer_gst_details,
                )
            )

        self.update_tax_amounts_of_charges(
            bill_aggregate,
            edit_charge_dtos,
            gst_details=booking_aggregate.booking_owner_gst_details(),
            seller_model=booking_aggregate.booking.seller_model,
        )

        edited_charges = self.edit_charges(
            bill_aggregate, edit_charge_dtos, None, None, None
        )
        occupancy_change_handler.update_occupancy_details_in_booked_charges(
            booking_aggregate,
            bill_aggregate,
            room_stay=room_stay,
            edited_charges=edited_charges,
        )
        return edited_charges

    @staticmethod
    def update_billing_instruction_to_convert_spot_credit_to_non_credit(
        existing_charge_split: ChargeSplit, bill_aggregate
    ):
        payment_instruction = PaymentInstruction.PAY_AT_CHECKOUT
        charge_type = ChargeTypes.from_payment_instruction(payment_instruction)
        billed_entity = bill_aggregate.get_billed_entity(
            existing_charge_split.billed_entity_account.billed_entity_id
        )
        billed_entity_account = (
            bill_aggregate.get_billed_entity_account_for_new_assignment(
                billed_entity, charge_type
            )
        )

        billing_instructions = [
            (
                BillingInstructionVO(
                    billed_entity_account, payment_instruction, Decimal('100')
                )
            )
        ]
        return billing_instructions
