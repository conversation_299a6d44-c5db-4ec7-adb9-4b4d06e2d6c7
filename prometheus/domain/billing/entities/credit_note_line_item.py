from datetime import datetime

from treebo_commons.utils import dateutils

from ths_common.constants.billing_constants import TaxTypes
from ths_common.value_objects import TaxDetail


class CreditNoteLineItem(object):
    def __init__(
        self,
        credit_note_line_item_id,
        invoice_id,
        invoice_charge_id,
        applicable_date: datetime,
        pretax_amount,
        posttax_amount,
        tax_amount,
        tax_details: dict = None,
        deleted=False,
        charge_id=None,
        created_at=None,
        modified_at=None,
    ):
        """

        :param credit_note_line_item_id:
        :param invoice_id:
        :param invoice_charge_id:
        :param applicable_date:
        :param pretax_amount:
        :param posttax_amount:
        :param tax_amount:
        :param tax_details: {'cgst':{'percentage':val, 'amount':amount}, 'sgst':{'percentage':val, 'amount':amount}}
        :param deleted:
        """
        self.created_at = created_at if created_at else dateutils.current_datetime()
        self.modified_at = modified_at if modified_at else dateutils.current_datetime()
        self.credit_note_line_item_id = credit_note_line_item_id
        self.invoice_id = invoice_id
        self.invoice_charge_id = invoice_charge_id
        self.charge_id = charge_id
        self.applicable_date = applicable_date
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.tax_amount = tax_amount
        self._tax_details = tax_details
        self.deleted = deleted
        # Below attributes will be set for sending in response. Not saved to DB
        self.item_code = None
        self.item_name = None
        self.charge_item_detail = None
        self.sku_category_id = None

    @property
    def tax_details(self):
        return [
            TaxDetail(
                tax_type=tax_type,
                percentage=tax_detail['percentage'],
                amount=tax_detail['amount'],
            )
            for tax_type, tax_detail in self._tax_details.items()
        ]

    @tax_details.setter
    def tax_details(self, tax_details: list[TaxDetail]):
        self._tax_details = {
            tax.tax_type: {"percentage": tax.percentage, "amount": tax.amount}
            for tax in tax_details
        }

    @property
    def tax_details_for_template(self):
        return self._tax_details

    def set_item_code(self, item_code):
        self.item_code = item_code

    def set_item_name(self, item_name):
        self.item_name = item_name

    def set_charge_item_detail(self, charge_item_detail):
        self.charge_item_detail = charge_item_detail

    def set_sku_category_id(self, sku_category_id):
        self.sku_category_id = sku_category_id
