import copy
import itertools
from datetime import date, datetime
from typing import List

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.billing.domain_events.payment_amount_edited import (
    PaymentAmountEditedEvent,
)
from prometheus.domain.billing.domain_events.payment_billed_entity_edited import (
    PaymentBilledEntityEditedEvent,
)
from prometheus.domain.billing.domain_events.payment_cancelled import (
    PaymentCancelledEvent,
)
from prometheus.domain.billing.domain_events.payment_mode_edited import (
    PaymentModeEditedEvent,
)
from prometheus.domain.billing.domain_events.payment_modified import (
    PaymentModifiedEvent,
)
from prometheus.domain.billing.domain_events.payment_posted import PaymentPostedEvent
from prometheus.domain.billing.domain_events.payment_split_modified import (
    PaymentSplitModifiedEvent,
)
from prometheus.domain.billing.domain_events.payment_type_edited import (
    PaymentTypeEditedEvent,
)
from prometheus.domain.billing.domain_events.refund_account_edited import (
    RefundAccountEditedEvent,
)
from prometheus.domain.billing.domain_events.refund_amount_edited import (
    RefundAmountEditedEvent,
)
from prometheus.domain.billing.domain_events.refund_cancelled import (
    RefundCancelledEvent,
)
from prometheus.domain.billing.domain_events.refund_mode_type_edited_event import (
    RefundModeTypeEditedEvent,
)
from prometheus.domain.billing.dto.payment_event_data import PaymentEventData
from prometheus.domain.billing.dto.payment_split_data import PaymentSplitData
from prometheus.domain.billing.dto.payment_split_event_data import PaymentSplitEventData
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.entities.payment_split import PaymentSplit
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.exceptions import BillingError
from prometheus.domain.domain_events.domain_event_registry import register_event
from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.billing_constants import (
    PaymentChannels,
    PaymentModes,
    PaymentModeSubTypes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.exceptions import AuthorizationError, ValidationException
from ths_common.value_objects import PayoutDetails


class Payment(EntityChangeTracker):
    def __init__(
        self,
        payment_id: int,
        amount: Money,
        date_of_payment: datetime,
        payment_mode: PaymentModes,
        payment_type: PaymentTypes,
        payment_details: dict,
        status: PaymentStatus,
        paid_to: PaymentReceiverTypes,
        payment_channel: PaymentChannels,
        payment_ref_id: str,
        paid_by: PaymentReceiverTypes,
        comment: str,
        amount_in_payment_currency: Money,
        payment_mode_sub_type: PaymentModeSubTypes = None,
        payer: str = None,
        deleted: bool = False,
        created_at: datetime = None,
        modified_at: datetime = None,
        payment_splits: List[PaymentSplit] = None,
        payment_business_date: date = None,
        posting_date: date = None,
        posted_date: date = None,
        confirmed=True,
        payor_billed_entity_id: int = None,
        refund_reason=None,
        payout_details: PayoutDetails = None,
        source_id: int = None,
        dirty=True,
        new=True,
    ):
        super().__init__(dirty=dirty, new=new)
        if not isinstance(status, PaymentStatus):
            raise ValidationException(
                BillingErrors.INVALID_PAYMENT_STATUS,
                description="Payment::Invalid payment status: {} for Payment".format(
                    status
                ),
            )
        if not isinstance(payment_type, PaymentTypes):
            raise ValidationException(
                BillingErrors.INVALID_PAYMENT_TYPE,
                description="Payment::Invalid payment type: {} for Payment".format(
                    payment_type
                ),
            )
        if amount < 0:
            raise ValidationException(
                BillingErrors.INVALID_PAYMENT_AMOUNT,
                description="Payment::Invalid payment amount: {}".format(amount),
            )
        if amount == 0 and payment_type != PaymentTypes.CREDIT_OFFERED:
            raise ValidationException(
                BillingErrors.INVALID_PAYMENT_AMOUNT,
                description="Payment::Invalid payment amount: {}".format(amount),
            )
        self._payment_id = payment_id
        self._amount = amount
        self._amount_in_payment_currency = amount_in_payment_currency

        self.date_of_payment = date_of_payment
        self._payment_mode = payment_mode
        self._payment_type = payment_type
        self._payment_mode_sub_type = payment_mode_sub_type
        self._paid_to = paid_to
        self._paid_by = paid_by
        self._status = status
        self._deleted = deleted
        self._payment_details = payment_details
        self._payment_channel = payment_channel
        self._payment_ref_id = payment_ref_id
        self._comment = comment
        self._payer = payer
        self.created_at = created_at
        self.modified_at = modified_at
        self._payment_splits = [] if not payment_splits else payment_splits
        self._confirmed = confirmed
        self._payor_billed_entity_id = payor_billed_entity_id
        self.payment_business_date = payment_business_date
        if not self.payment_business_date:
            self.payment_business_date = dateutils.to_date(self.date_of_payment)
        self.posting_date = posting_date
        self.posted_date = posted_date
        self._refund_reason = refund_reason
        self.payout_details = payout_details
        self.source_id = source_id

    @property
    def confirmed(self):
        return self._confirmed

    @confirmed.setter
    def confirmed(self, value):
        old_value = self._confirmed
        self._confirmed = value
        if old_value != self._confirmed:
            register_event(
                PaymentModifiedEvent(
                    payment_id=self.payment_id,
                    attribute='confirmed',
                    old_value=old_value,
                    new_value=self._confirmed,
                )
            )
        self.mark_dirty()

    @property
    def payment_details(self):
        return self._payment_details

    @payment_details.setter
    def payment_details(self, value):
        self._payment_details = value
        self.mark_dirty()

    @property
    def payment_channel(self):
        return self._payment_channel

    @payment_channel.setter
    def payment_channel(self, value):
        old_value = self._payment_channel
        self._payment_channel = value
        if old_value != self._payment_channel:
            register_event(
                PaymentModifiedEvent(
                    payment_id=self.payment_id,
                    attribute='payment_channel',
                    old_value=old_value,
                    new_value=self._payment_channel,
                )
            )
        self.mark_dirty()

    @property
    def payment_ref_id(self):
        return self._payment_ref_id

    @payment_ref_id.setter
    def payment_ref_id(self, value):
        old_value = self._payment_ref_id
        self._payment_ref_id = value
        if old_value != self._payment_ref_id:
            register_event(
                PaymentModifiedEvent(
                    payment_id=self.payment_id,
                    attribute='payment_ref_id',
                    old_value=old_value,
                    new_value=self._payment_ref_id,
                )
            )
        self.mark_dirty()

    @property
    def comment(self):
        return self._comment

    @comment.setter
    def comment(self, value):
        old_value = self._comment
        self._comment = value
        if old_value != self._comment:
            register_event(
                PaymentModifiedEvent(
                    payment_id=self.payment_id,
                    attribute='comment',
                    old_value=old_value,
                    new_value=self._comment,
                )
            )
        self.mark_dirty()

    @property
    def payment_id(self):
        return self._payment_id

    @property
    def payment_splits(self):
        return [split for split in self._payment_splits if not split.deleted]

    @property
    def all_payment_splits(self):
        return self._payment_splits

    @property
    def amount(self):
        return self._amount

    @amount.setter
    def amount(self, value):
        if value <= 0:
            raise ValidationException(
                BillingErrors.INVALID_PAYMENT_AMOUNT,
                "Payment::Invalid payment amount: {}".format(value),
            )
        old_value = self._amount
        self._amount = value
        if (
            not self.is_new()
            and old_value != self._amount
            and self.payment_type == PaymentTypes.PAYMENT
        ):
            register_event(
                PaymentAmountEditedEvent(
                    payment=self, old_value=str(old_value), new_value=str(self._amount)
                )
            )
        if (
            not self.is_new()
            and old_value != self._amount
            and self.payment_type == PaymentTypes.REFUND
        ):
            register_event(
                RefundAmountEditedEvent(
                    payment=self, old_value=str(old_value), new_value=str(self._amount)
                )
            )
        self.mark_dirty()

    @property
    def amount_in_payment_currency(self):
        return self._amount_in_payment_currency

    @amount_in_payment_currency.setter
    def amount_in_payment_currency(self, value):
        if value <= 0:
            raise ValidationException(
                BillingErrors.INVALID_PAYMENT_AMOUNT,
                "Payment::Invalid payment amount: {}".format(value),
            )
        old_value = self._amount_in_payment_currency
        self._amount_in_payment_currency = value
        if not self.is_new() and old_value != self._amount_in_payment_currency:
            register_event(
                PaymentAmountEditedEvent(
                    payment=self,
                    old_value=str(old_value),
                    new_value=str(self._amount_in_payment_currency),
                )
            )
        self.mark_dirty()

    @property
    def payment_mode(self):
        return self._payment_mode

    @payment_mode.setter
    def payment_mode(self, value):
        if value:
            if value not in [
                PaymentModes.CREDIT_CARD,
                PaymentModes.DEBIT_CARD,
                PaymentModes.PHONE_PE,
                PaymentModes.AMAZON_PAY,
            ]:
                self._payment_mode_sub_type = None
        old_value = self._payment_mode
        self._payment_mode = value
        if (
            not self.is_new()
            and old_value != self._payment_mode
            and self._payment_type == PaymentTypes.PAYMENT
        ):
            register_event(
                PaymentModeEditedEvent(
                    payment=self,
                    old_value=str(old_value),
                    new_value=str(self._payment_mode),
                )
            )
        elif (
            not self.is_new()
            and old_value != self._payment_mode
            and self._payment_type == PaymentTypes.REFUND
        ):
            register_event(
                RefundModeTypeEditedEvent(
                    payment=self,
                    attribute='payment_mode',
                    old_value=str(old_value),
                    new_value=str(self._payment_mode),
                )
            )
        self.mark_dirty()

    @property
    def payment_mode_sub_type(self):
        return self._payment_mode_sub_type

    @payment_mode_sub_type.setter
    def payment_mode_sub_type(self, value):
        old_value = self._payment_mode_sub_type
        self._payment_mode_sub_type = value
        if old_value != self._payment_mode_sub_type:
            register_event(
                PaymentModifiedEvent(
                    payment_id=self.payment_id,
                    attribute='payment_mode_sub_type',
                    old_value=str(old_value),
                    new_value=str(self._payment_mode_sub_type),
                )
            )
        self.mark_dirty()

    @property
    def payment_type(self):
        return self._payment_type

    @payment_type.setter
    def payment_type(self, value):
        if not isinstance(value, PaymentTypes):
            raise ValidationException(
                BillingErrors.INVALID_PAYMENT_TYPE,
                description="Payment::Invalid payment type: {} for payment edit".format(
                    value
                ),
            )
        old_value = self._payment_type
        self._payment_type = value
        if (
            not self.is_new()
            and old_value != self._payment_type
            and self._payment_type == PaymentTypes.PAYMENT
        ):
            register_event(
                PaymentTypeEditedEvent(
                    payment=self,
                    old_value=str(old_value),
                    new_value=str(self._payment_type),
                )
            )
        elif (
            not self.is_new()
            and old_value != self._payment_type
            and self._payment_type == PaymentTypes.REFUND
        ):
            register_event(
                RefundModeTypeEditedEvent(
                    payment=self,
                    attribute="payment_type",
                    old_value=str(old_value),
                    new_value=str(self._payment_type),
                )
            )

        self.mark_dirty()

    @property
    def paid_to(self):
        return self._paid_to

    @paid_to.setter
    def paid_to(self, value):
        old_value = self._paid_to
        self._paid_to = value
        if old_value != self._paid_to:
            register_event(
                PaymentModifiedEvent(
                    payment_id=self.payment_id,
                    attribute='paid_to',
                    old_value=str(old_value),
                    new_value=str(self._paid_to),
                )
            )
        self.mark_dirty()

    @property
    def paid_by(self):
        return self._paid_by

    @paid_by.setter
    def paid_by(self, value):
        old_value = self._paid_by
        self._paid_by = value
        if old_value != self._paid_by:
            register_event(
                PaymentModifiedEvent(
                    payment_id=self.payment_id,
                    attribute='paid_by',
                    old_value=str(old_value),
                    new_value=str(self._paid_by),
                )
            )
        self.mark_dirty()

    @property
    def status(self):
        return self._status

    @status.setter
    def status(self, value):
        if not isinstance(value, PaymentStatus):
            raise ValidationException(
                BillingErrors.INVALID_PAYMENT_STATUS,
                description="Payment::Invalid payment status: {} for Payment edit".format(
                    value
                ),
            )
        self._status = value
        self.mark_dirty()

    @property
    def payor_billed_entity_id(self):
        return self._payor_billed_entity_id

    @payor_billed_entity_id.setter
    def payor_billed_entity_id(self, value):
        old_value = self._payor_billed_entity_id
        self._payor_billed_entity_id = value
        if old_value != self._payor_billed_entity_id:
            register_event(
                PaymentModifiedEvent(
                    payment_id=self.payment_id,
                    attribute='payor_billed_entity_id',
                    old_value=str(old_value),
                    new_value=str(self._payor_billed_entity_id),
                )
            )
        self.mark_dirty()

    def is_refund(self):
        return self.payment_type == PaymentTypes.REFUND

    def is_payment(self):
        return self.payment_type == PaymentTypes.PAYMENT

    def is_payment_or_refund(self):
        return self.is_payment() or self.is_refund()

    def is_credit_offered(self):
        return self.payment_type == PaymentTypes.CREDIT_OFFERED

    def is_posted(self):
        return not self.deleted and self.status == PaymentStatus.POSTED

    def is_active(self):
        return not self.deleted and self.status != PaymentStatus.CANCELLED

    def cancel(self):
        if self._status == PaymentStatus.POSTED:
            if self.is_refund():
                raise BillingError(error=BillingErrors.CANNOT_CANCEL_POSTED_REFUND)
            elif self.is_payment():
                raise BillingError(error=BillingErrors.CANNOT_CANCEL_POSTED_PAYMENT)
        self._status = PaymentStatus.CANCELLED
        self.mark_dirty()
        if self.payment_type == PaymentTypes.PAYMENT:
            register_event(PaymentCancelledEvent(self))
        elif self.payment_type == PaymentTypes.REFUND:
            register_event(RefundCancelledEvent(self))

    def post(self, business_date=None):
        if self._status in {PaymentStatus.CANCELLED, PaymentStatus.POSTED}:
            return

        self._status = PaymentStatus.POSTED
        self.posting_date = business_date
        if not self.posting_date:
            if crs_context.get_hotel_context():
                self.posting_date = crs_context.get_hotel_context().current_date()
            elif crs_context.get_seller_context():
                self.posting_date = crs_context.get_seller_context().current_date()

        self.posted_date = dateutils.current_date()

        self.mark_dirty()
        register_event(
            PaymentPostedEvent(
                PaymentEventData(
                    self.payment_id,
                    self.paid_by,
                    self.paid_to,
                    self.payment_mode,
                    self.payment_mode_sub_type,
                    self.amount,
                    self.date_of_payment,
                    self.payment_type,
                    self.amount_in_payment_currency,
                    self._payer,
                    self.payment_ref_id,
                    self.comment,
                    self.confirmed,
                    [ps.billed_entity_account for ps in self.payment_splits],
                    payor_billed_entity_id=self.payor_billed_entity_id,
                )
            )
        )

    @property
    def deleted(self):
        return self._deleted

    def delete(self):
        if self.status == PaymentStatus.POSTED:
            raise BillingError(BillingErrors.CANNOT_CANCEL_POSTED_PAYMENT)
        self._deleted = True
        for payment_split in self.payment_splits:
            payment_split.mark_deleted()
        self.mark_dirty()

    def mark_fully_refunded(self):
        if not self.payment_details:
            self.payment_details = dict(is_fully_refunded=True)
        else:
            self.payment_details['is_fully_refunded'] = True
        self.mark_dirty()

    def is_fully_refunded(self):
        if not self.payment_details:
            return False
        return self.payment_details.get('is_fully_refunded')

    def mark_mapped_to_invoice(self):
        self._status = PaymentStatus.MAPPED_TO_INVOICE
        self.mark_dirty()

    @property
    def is_payment_paid_to_treebo(self):
        return self.payment_type == PaymentTypes.PAYMENT and self.paid_to in (
            PaymentReceiverTypes.TA,
            PaymentReceiverTypes.TREEBO,
            PaymentReceiverTypes.OTA,
        )

    @property
    def is_refund_paid_by_treebo(self):
        # TODO: Fix this check for external hotel chain
        return self.payment_type == PaymentTypes.REFUND and self.paid_by in (
            PaymentReceiverTypes.TA,
            PaymentReceiverTypes.TREEBO,
            PaymentReceiverTypes.OTA,
        )

    @property
    def is_payment_paid_to_hotel(self):
        return (
            self.payment_type == PaymentTypes.PAYMENT
            and not self.is_payment_paid_to_treebo
        )

    @property
    def is_refund_paid_by_hotel(self):
        return (
            self.payment_type == PaymentTypes.REFUND
            and not self.is_refund_paid_by_treebo
        )

    @property
    def effective_amount(self):
        return (
            (0 - self.amount.amount)
            if self.payment_type == PaymentTypes.REFUND
            else self.amount
        )

    @property
    def payer(self):
        return self._payer

    @payer.setter
    def payer(self, value):
        old_value = self._payer
        self._payer = value
        if old_value != self._payer:
            register_event(
                PaymentModifiedEvent(
                    payment_id=self.payment_id,
                    attribute='payer',
                    old_value=str(old_value),
                    new_value=str(self._payer),
                )
            )
        self.mark_dirty()

    def attach_default_billed_entity(self, billed_entity_account):
        if not self.payment_splits:
            current_max_id = (
                max(
                    [
                        payment_split.payment_split_id
                        for payment_split in self._payment_splits
                    ]
                )
                if self._payment_splits
                else 0
            )
            if not self.payor_billed_entity_id:
                self.payor_billed_entity_id = billed_entity_account.billed_entity_id
            payment_split = PaymentSplit(
                current_max_id + 1,
                self.amount,
                self.payment_type,
                self.payment_mode,
                billed_entity_account=billed_entity_account,
            )
            self._payment_splits.append(payment_split)
            self.mark_dirty()
        else:
            for split in self.payment_splits:
                split.update_billed_entity_account(billed_entity_account)

    def update_billed_entity_account_in_payment_splits(self, payment_split_ids, bea):
        split_id_set = set(payment_split_ids)
        payment_splits = [
            split
            for split in self.payment_splits
            if split.payment_split_id in split_id_set
        ]
        for payment_split in payment_splits:
            payment_split.update_billed_entity_account(bea)

    def add_payment_splits(self, payment_split_dtos: List[PaymentSplitData]):
        max_split_id = max(
            [split.payment_split_id for split in self._payment_splits], default=0
        )

        payment_splits = [
            PaymentSplit(
                payment_split_id=max_split_id + split_no,
                amount=payment_split_dto.amount,
                payment_type=self.payment_type,
                payment_mode=self.payment_mode,
                billed_entity_account=payment_split_dto.billed_entity_account,
            )
            for split_no, payment_split_dto in enumerate(payment_split_dtos, 1)
        ]
        self._payment_splits.extend(payment_splits)
        self.mark_dirty()
        return payment_splits

    def link_source_id(self, source_id):
        self.source_id = source_id
        self.mark_dirty()

    def update_payment_splits(self, payment_split_dtos: List[PaymentSplitData]):
        old_payment_splits = copy.deepcopy(self.payment_splits)
        for old_split, new_split in itertools.zip_longest(
            self.payment_splits, payment_split_dtos
        ):
            if old_split and not new_split:
                old_split.mark_deleted()
                old_split.mark_dirty()
            elif not old_split:
                self.add_payment_splits([new_split])
            elif old_split and new_split:
                old_split.billed_entity_account = new_split.billed_entity_account
                old_split.amount = new_split.amount
                old_split.payment_mode = self.payment_mode
                old_split.mark_dirty()

        old_billed_entity_accounts = [
            ps.billed_entity_account
            for ps in old_payment_splits
            if ps.billed_entity_account
        ]
        new_billed_entity_accounts = [
            ps.billed_entity_account
            for ps in self.payment_splits
            if ps.billed_entity_account
        ]

        if (
            set(old_billed_entity_accounts) != set(new_billed_entity_accounts)
            and self.payment_type == PaymentTypes.PAYMENT
        ):
            register_event(
                PaymentBilledEntityEditedEvent(
                    payment=self,
                    old_value=old_billed_entity_accounts,
                    new_value=new_billed_entity_accounts,
                )
            )

        elif (
            set(old_billed_entity_accounts) != set(new_billed_entity_accounts)
            and self.payment_type == PaymentTypes.REFUND
        ):
            register_event(
                RefundAccountEditedEvent(
                    payment=self,
                    old_value=old_billed_entity_accounts,
                    new_value=new_billed_entity_accounts,
                )
            )

        if old_payment_splits != self.payment_splits:
            register_event(
                PaymentSplitModifiedEvent(
                    payment_id=self.payment_id,
                    old_splits=[
                        PaymentSplitEventData(
                            payment_split_id=ps.payment_split_id,
                            billed_entity_account=ps.billed_entity_account,
                            amount=ps.amount,
                        )
                        for ps in old_payment_splits
                    ],
                    new_splits=[
                        PaymentSplitEventData(
                            payment_split_id=ps.payment_split_id,
                            billed_entity_account=ps.billed_entity_account,
                            amount=ps.amount,
                        )
                        for ps in self.payment_splits
                    ],
                )
            )

    def get_payment_split(self, billed_entity_account: BilledEntityAccountVO):
        return next(
            (
                split
                for split in self.payment_splits
                if split.billed_entity_account == billed_entity_account
            ),
            None,
        )

    def get_payment_splits(self, billed_entity_account: BilledEntityAccountVO):
        return [
            split
            for split in self.payment_splits
            if split.billed_entity_account == billed_entity_account
        ]

    def is_associated_with_billed_entity(self, billed_entity_id):
        return bool(
            split
            for split in self.payment_splits
            if split.billed_entity_account.billed_entity_id == billed_entity_id
        )

    def get_proportionate_amount_in_payment_currency_for_split(
        self, payment_amount_in_base_currency: Money
    ):
        if not self.amount_in_payment_currency:
            return None

        return Money(
            payment_amount_in_base_currency.amount
            / self.amount.amount
            * self.amount_in_payment_currency.amount,
            self.amount_in_payment_currency.currency,
        )

    def get_proportionate_percentage_for_split(self, payment_split):
        if not self.amount.amount:
            return self.amount
        return (payment_split.amount.amount / self.amount.amount) * 100

    def update_amount(self, amount):
        for split in self.payment_splits:
            percentage = self.get_proportionate_percentage_for_split(split)
            split.update_amount((amount * percentage) / 100)
        amount_in_payment_currency = self.amount_in_payment_currency * (
            amount.amount / self.amount.amount
        )
        self._amount = amount
        self._amount_in_payment_currency = amount_in_payment_currency
        self.mark_dirty()

    def should_sync_to_cashier(self):
        return self.payment_channel == PaymentChannels.FRONT_DESK and (
            (self.is_payment() and self.paid_to == PaymentReceiverTypes.HOTEL)
            or (self.is_refund() and self.paid_by == PaymentReceiverTypes.HOTEL)
        )

    @property
    def refund_reason(self):
        return self._refund_reason

    @refund_reason.setter
    def refund_reason(self, value):
        self._refund_reason = value
        self.mark_dirty()
