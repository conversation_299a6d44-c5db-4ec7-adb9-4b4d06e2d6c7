from prometheus.domain.billing.entities.billed_entity import BilledEntityAccount<PERSON>
from ths_common.base_entity import EntityChangeTracker


class Folio(EntityChangeTracker):
    def __init__(
        self,
        bill_id,
        billed_entity_id,
        folio_number,
        account_number,
        deleted=False,
        dirty=True,
        new=True,
        created_at=None,
        modified_at=None,
    ):
        super().__init__(dirty=dirty, new=new)
        self.bill_id = bill_id
        self.billed_entity_id = billed_entity_id
        self.folio_number = folio_number
        self.account_number = account_number
        self.deleted = deleted
        self.created_at = created_at
        self.modified_at = modified_at

    def get_billed_entity_account(self):
        return BilledEntityAccountVO(self.billed_entity_id, self.account_number)

    def delete(self):
        self.mark_dirty()
        self.deleted = True
