from typing import Optional

from shared_kernel.domain.entity_change_tracker import EntityChangeTracker


class EInvoicingAuditTrail(EntityChangeTracker):
    def __init__(
        self,
        audit_id: str,
        hotel_id: str,
        audit_type: str,
        audit_payload: dict,
        user: Optional[str] = None,
        user_type: Optional[str] = None,
        request_id: Optional[str] = None,
        invoice_id: Optional[str] = None,
        dirty: bool = True,
        new: bool = True,
    ):
        super().__init__(dirty=dirty, new=new)
        self.audit_id = audit_id
        self.user = user
        self.user_type = user_type
        self.request_id = request_id
        self.invoice_id = invoice_id
        self.hotel_id = hotel_id
        self.audit_type = audit_type
        self.audit_payload = audit_payload


class EInvoicingAuditType:
    """Audit types for e-invoicing audit trail"""
    CLEARTAX_ERROR = "cleartax_error"
    USER_SELECTED_PROCEED_WITHOUT_EINVOICE = "user_selected_proceed_without_einvoice"
    USER_SELECTED_REMOVE_GST = "user_selected_remove_gst"
