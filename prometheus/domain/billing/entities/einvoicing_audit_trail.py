from datetime import datetime
from typing import Optional

from shared_kernel.domain.entity_change_tracker import EntityChangeTracker


class EInvoicingAuditTrail(EntityChangeTracker):
    def __init__(
        self,
        einvoicing_audit_trail_id: int,
        hotel_id: str,
        event_type: str,
        action_datetime: datetime,
        booking_id: Optional[str] = None,
        invoice_id: Optional[str] = None,
        credit_note_id: Optional[str] = None,
        bill_id: Optional[str] = None,
        action_type: Optional[str] = None,
        user_action: Optional[str] = None,
        user_id: Optional[str] = None,
        user_type: Optional[str] = None,
        auth_id: Optional[str] = None,
        application: Optional[str] = None,
        request_id: Optional[str] = None,
        cleartax_error_code: Optional[str] = None,
        cleartax_error_message: Optional[str] = None,
        cleartax_response_payload: Optional[dict] = None,
        remarks: Optional[str] = None,
        dirty: bool = True,
        new: bool = True,
    ):
        super().__init__(dirty=dirty, new=new)
        self.einvoicing_audit_trail_id = einvoicing_audit_trail_id
        self.booking_id = booking_id
        self.invoice_id = invoice_id
        self.credit_note_id = credit_note_id
        self.bill_id = bill_id
        self.hotel_id = hotel_id
        self.event_type = event_type
        self.action_type = action_type
        self.user_action = user_action
        self.user_id = user_id
        self.user_type = user_type
        self.auth_id = auth_id
        self.application = application
        self.request_id = request_id
        self.cleartax_error_code = cleartax_error_code
        self.cleartax_error_message = cleartax_error_message
        self.cleartax_response_payload = cleartax_response_payload
        self.action_datetime = action_datetime
        self.remarks = remarks


class EInvoicingAuditTrailEventType:
    """Event types for e-invoicing audit trail"""
    CLEARTAX_ERROR = "cleartax_error"
    CHECKOUT_WITHOUT_EINVOICE = "checkout_without_einvoice"
    REMOVE_GST_AND_CHECKOUT = "remove_gst_and_checkout"


class EInvoicingAuditTrailActionType:
    """Action types for e-invoicing audit trail"""
    USER_SELECTED_PROCEED_WITHOUT_EINVOICE = "user_selected_proceed_without_einvoice"
    USER_SELECTED_REMOVE_GST = "user_selected_remove_gst"
    SYSTEM_ERROR = "system_error"
