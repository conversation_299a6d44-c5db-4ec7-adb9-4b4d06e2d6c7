from datetime import datetime

from treebo_commons.utils import dateutils

from prometheus.domain.billing.errors import BillingErrors
from ths_common.constants.billing_constants import (
    CreditNoteStatus,
    IssuedByType,
    IssuedToType,
    TaxTypes,
)
from ths_common.exceptions import ValidationException
from ths_common.value_objects import (
    InvoiceBillToInfo,
    InvoiceIssuedByInfo,
    TaxDetail,
    VendorDetails,
)


class CreditNote(object):
    def __init__(
        self,
        bill_id,
        credit_note_id,
        credit_note_number,
        credit_note_date,
        vendor_details: VendorDetails,
        vendor_id,
        issued_to: InvoiceBillToInfo,
        issued_by: InvoiceIssuedByInfo,
        status: CreditNoteStatus,
        comment,
        issued_to_type: IssuedToType,
        issued_by_type: IssuedByType,
        tax_details: dict,
        posttax_amount,
        pretax_amount,
        tax_amount,
        hotel_credit_note_id,
        version,
        credit_note_url=None,
        deleted=False,
        created_at=None,
        modified_at=None,
        irn: str = None,
        qr_code: str = None,
        signed_invoice: str = None,
        irp_ack_number: str = None,
        irp_ack_date: datetime.date = None,
        is_einvoice: bool = False,
        signed_url: str = None,
        signed_url_expiry_time: datetime = None,
        billed_entity_account=None,
    ):
        """

        :param bill_id:
        :param credit_note_id:
        :param credit_note_number:
        :param credit_note_date:
        :param vendor_details:
        :param vendor_id:
        :param issued_to:
        :param issued_by:
        :param status:
        :param comment:
        :param issued_to_type:
        :param issued_by_type:
        :param tax_details:
        :param posttax_amount:
        :param pretax_amount:
        :param tax_amount:
        :param hotel_credit_note_id:
        :param version:
        :param credit_note_url:
        :param deleted:
        :param created_at:
        :param modified_at:
        """
        self.created_at = created_at if created_at else dateutils.current_datetime()
        self.modified_at = modified_at if modified_at else dateutils.current_datetime()
        self.bill_id = bill_id
        self.credit_note_id = credit_note_id
        self.credit_note_number = credit_note_number
        self.credit_note_date = credit_note_date
        self.vendor_details = vendor_details
        self.vendor_id = vendor_id
        self.issued_to = issued_to
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.tax_amount = tax_amount
        self._tax_details = tax_details
        self.status = status
        self.comment = comment
        self.issued_by_type = issued_by_type
        self.issued_to_type = issued_to_type
        self.issued_by = issued_by
        self.version = version
        self.credit_note_url = credit_note_url
        self.deleted = deleted
        if hotel_credit_note_id:
            if self.issued_by_type != IssuedByType.RESELLER:
                raise ValidationException(
                    BillingErrors.REQUESTED_HOTEL_CREDIT_NOTE_FOR_NON_RESELLER_CREDIT_NOTE
                )
        self.hotel_credit_note_id = hotel_credit_note_id
        self.signed_url = signed_url
        self.signed_url_expiry_time = signed_url_expiry_time

        self.irn = irn
        self.qr_code = qr_code
        self.signed_invoice = signed_invoice
        self.irp_ack_number = irp_ack_number
        self.irp_ack_date = irp_ack_date
        self.is_einvoice = is_einvoice
        self.billed_entity_account = billed_entity_account

    @property
    def tax_details(self):
        return [
            TaxDetail(tax_type=tax_type, percentage=0, amount=tax_detail)
            for tax_type, tax_detail in self._tax_details.items()
        ]

    @property
    def tax_details_for_template(self):
        tax_details = dict()
        for tax_type, tax_detail in self._tax_details.items():
            tax_details[tax_type] = {"percentage": 0, "amount": tax_detail}
        return tax_details

    @property
    def is_cancelled(self):
        return self.status == CreditNoteStatus.CANCELLED

    def set_hotel_credit_note(self, hotel_credit_note_id):
        if self.status == CreditNoteStatus.CANCELLED:
            raise ValidationException(BillingErrors.CREDIT_NOTE_IN_CANCELLED_STATE)
        if self.issued_by_type != IssuedByType.RESELLER:
            raise ValidationException(
                BillingErrors.REQUESTED_HOTEL_CREDIT_NOTE_FOR_NON_RESELLER_CREDIT_NOTE
            )
        if self.hotel_credit_note_id:
            raise ValidationException(BillingErrors.HOTEL_CREDIT_NOTE_ALREADY_EXISTS)
        self.hotel_credit_note_id = hotel_credit_note_id
