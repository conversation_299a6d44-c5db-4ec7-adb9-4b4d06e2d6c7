from treebo_commons.money.constants import CurrencyType

from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.billing_constants import BillStatus
from ths_common.value_objects import BillParentInfo


class Bill(EntityChangeTracker):
    """
    Bill entity
    """

    def __init__(
        self,
        bill_id,
        bill_date,
        vendor_id,
        app_id,
        parent_reference_number,
        vendor_details,
        parent_info,
        version,
        fees=None,
        status=None,
        deleted=False,
        base_currency=CurrencyType.INR,
        new=False,
        dirty=False,
    ):
        super().__init__(dirty=dirty, new=new)

        if parent_info is None or isinstance(parent_info, dict):
            parent_info = BillParentInfo(parent_info)

        self.bill_id = bill_id
        self.bill_date = bill_date
        self.app_id = app_id
        self.parent_reference_number = parent_reference_number
        self.parent_info = parent_info.dict()
        self.vendor_id = vendor_id
        self.vendor_details = vendor_details
        if status:
            self.status = status
        else:
            self.status = BillStatus.CREATED
        self.version = version
        self.fees = fees or dict()
        self.deleted = deleted
        _base_currency = base_currency if base_currency else CurrencyType.INR
        self.base_currency = (
            _base_currency
            if isinstance(_base_currency, CurrencyType)
            else CurrencyType(_base_currency)
        )

    def update_fee(self, fee_type, fee):
        self.fees[fee_type] = fee
        self.mark_dirty()

    def update_fees(self, fees):
        if self.fees != fees:
            self.fees = fees
            self.mark_dirty()

    def void(self):
        self.status = BillStatus.VOIDED
