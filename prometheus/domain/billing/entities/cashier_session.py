from treebo_commons.money import Money

from prometheus import crs_context
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.access_entity_facts import AccessEntityFacts
from ths_common.constants.billing_constants import CashierSessionStatus
from ths_common.value_objects import UserData


class CashierSession(object):
    """
    CashierSession entity
    """

    def __init__(
        self,
        cashier_session_id,
        session_number,
        start_datetime,
        end_datetime,
        vendor_id,
        opening_balance_in_base_currency: Money,
        status: CashierSessionStatus,
        opened_by: UserData,
        closed_by: UserData,
        cash_register_id: str,
        closing_balance_in_base_currency: Money = None,
        deleted=False,
    ):
        self.cashier_session_id = cashier_session_id
        self.start_datetime = start_datetime
        self.end_datetime = end_datetime
        self.opening_balance_in_base_currency = opening_balance_in_base_currency
        self.closing_balance_in_base_currency = closing_balance_in_base_currency
        self.status = status
        self.opened_by = opened_by
        self.closed_by = closed_by
        self.session_number = session_number
        self.deleted = deleted
        self.cash_register_id = cash_register_id
        self.vendor_id = vendor_id

        if not crs_context.should_bypass_access_entity_checks():
            RuleEngine.action_allowed(
                action='access_entity',
                facts=AccessEntityFacts(
                    user_data=crs_context.user_data,
                    entity_vendor_id=self.vendor_id,
                    entity_type="cashier_session",
                ),
                fail_on_error=True,
            )

    def is_open(self):
        return self.status == CashierSessionStatus.OPEN

    def is_closed(self):
        return self.status == CashierSessionStatus.CLOSED
