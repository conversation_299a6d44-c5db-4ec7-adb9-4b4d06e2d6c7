from decimal import Decimal
from typing import List, Set

from treebo_commons.utils import dateutils

from prometheus.domain.billing.errors import BillingErrors
from ths_common.base_entity import EntityChangeT<PERSON>
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    BilledEntityStatus,
    ChargeTypes,
    PaymentInstruction,
)
from ths_common.exceptions import ValidationException
from ths_common.value_objects import Name


class BilledEntityAccountVO:
    def __init__(self, billed_entity_id, account_number: int):
        self.billed_entity_id = billed_entity_id
        self.account_number = account_number

    def to_json(self):
        return dict(
            billed_entity_id=self.billed_entity_id, account_number=self.account_number
        )

    @staticmethod
    def from_json(json):
        if not json:
            return None
        return BilledEntityAccountVO(
            json.get('billed_entity_id'), json.get('account_number')
        )

    def __eq__(self, other):
        return (
            isinstance(other, BilledEntityAccountVO)
            and self.billed_entity_id == other.billed_entity_id
            and self.account_number == other.account_number
        )

    def __hash__(self):
        return hash(str(self.billed_entity_id) + "-" + str(self.account_number))

    def __str__(self):
        return 'B{0}-A{1}'.format(self.billed_entity_id, self.account_number)

    def __repr__(self):
        return 'B{0}-A{1}'.format(self.billed_entity_id, self.account_number)


class BillingInstructionVO:
    def __init__(
        self,
        billed_entity_account: BilledEntityAccountVO,
        payment_instruction: PaymentInstruction,
        split_percentage: Decimal,
        is_spot_credit: bool = False,
    ):
        self.billed_entity_account = billed_entity_account
        self.payment_instruction = payment_instruction
        self.split_percentage = split_percentage
        self.is_spot_credit = is_spot_credit
        self.bill_to_type = None
        self.charge_type = None

    def set_bill_to_type(self, bill_to_type):
        self.bill_to_type = bill_to_type

    def set_charge_type(self, charge_type):
        self.charge_type = charge_type

    def __eq__(self, other):
        return (
            isinstance(other, BillingInstructionVO)
            and self.billed_entity_account == other.billed_entity_account
            and self.payment_instruction == other.payment_instruction
            and self.split_percentage == other.split_percentage
        )


class BilledEntityAccount(EntityChangeTracker):
    def __init__(
        self,
        account_number: int,
        folio_number: int = 0,
        deleted=False,
        invoiced=False,
        locked=False,
        is_allowance_account=False,
        invoice_numbers_available_for_use=None,
        account_type=None,
        new=True,
        dirty=True,
    ):
        super().__init__(new=new, dirty=dirty)
        self.account_number = account_number
        self.folio_number = folio_number
        self.deleted = deleted
        self.invoiced = invoiced
        self.locked = locked
        self._is_allowance_account = is_allowance_account
        self.account_type = account_type
        self.invoice_numbers_available_for_use = invoice_numbers_available_for_use
        self.assigned_charge_types = set()

    def set_assigned_charge_types(self, charge_types: Set[ChargeTypes]):
        self.assigned_charge_types = charge_types
        if not self.account_type and charge_types and len(charge_types) == 1:
            if ChargeTypes.NON_CREDIT in charge_types:
                self.mark_as_non_credit_account()
            else:
                self.mark_as_credit_account()

    def mark_invoiced(self):
        self.invoiced = True
        self.locked = True
        self.mark_dirty()

    def mark_uninvoiced(self):
        self.invoiced = False
        self.locked = False
        self.mark_dirty()

    def mark_as_allowance_account(self):
        self._is_allowance_account = True
        self.mark_dirty()

    def mark_as_credit_account(self):
        self.account_type = ChargeTypes.CREDIT
        self.mark_dirty()

    def mark_as_non_credit_account(self):
        self.account_type = ChargeTypes.NON_CREDIT
        self.mark_dirty()

    def is_locked(self):
        return self.locked

    def is_invoiced(self):
        return self.invoiced

    def is_allowance_account(self):
        return self._is_allowance_account

    def get_account_type(self) -> ChargeTypes:
        return self.account_type

    def is_credit_account(self):
        return self.account_type == ChargeTypes.CREDIT or self.account_type is None

    def is_non_credit_account(self):
        return self.account_type == ChargeTypes.NON_CREDIT or self.account_type is None

    def unlock(self, mark_uninvoiced=False):
        self.locked = False
        if mark_uninvoiced:
            self.invoiced = False
        self.mark_dirty()

    def update_invoice_numbers_available_for_use(
        self, invoice_number=None, invoice_date=None, hotel_invoice_number=None
    ):
        if self.invoice_numbers_available_for_use is not None:
            if invoice_number is not None:
                self.invoice_numbers_available_for_use['invoice_numbers'].append(
                    invoice_number
                )
            if (
                self.invoice_numbers_available_for_use.get('hotel_invoice_numbers')
                and hotel_invoice_number is not None
            ):
                self.invoice_numbers_available_for_use['hotel_invoice_numbers'].append(
                    hotel_invoice_number
                )
        elif invoice_number is not None:
            self.invoice_numbers_available_for_use = dict(
                invoice_numbers=[invoice_number],
                invoice_date=dateutils.date_to_ymd_str(invoice_date),
            )
            if hotel_invoice_number is not None:
                self.invoice_numbers_available_for_use['hotel_invoice_numbers'] = [
                    hotel_invoice_number
                ]
        self.mark_dirty()

    def pop_invoice_numbers_available_for_use(self, current_date) -> str:
        if self.invoice_numbers_available_for_use:
            invoice_number = (
                self.invoice_numbers_available_for_use['invoice_numbers'].pop()
                if self.invoice_numbers_available_for_use.get("invoice_date")
                == dateutils.date_to_ymd_str(current_date)
                else None
            )

            if (
                len(self.invoice_numbers_available_for_use['invoice_numbers']) == 0
                or invoice_number is None
            ):
                self.invoice_numbers_available_for_use = None
            self.mark_dirty()
            return invoice_number

    def pop_hotel_invoice_numbers_available_for_use(self, current_date):
        if (
            self.invoice_numbers_available_for_use
            and self.invoice_numbers_available_for_use.get('hotel_invoice_numbers')
        ):
            hotel_invoice_number = (
                self.invoice_numbers_available_for_use['hotel_invoice_numbers'].pop()
                if self.invoice_numbers_available_for_use.get("invoice_date")
                == dateutils.date_to_ymd_str(current_date)
                else None
            )
            self.mark_dirty()
            return hotel_invoice_number
        return None


class BilledEntity(EntityChangeTracker):
    def __init__(
        self,
        billed_entity_id,
        name: Name,
        category: BilledEntityCategory,
        secondary_category: BilledEntityCategory = None,
        accounts: List[BilledEntityAccount] = None,
        status=BilledEntityStatus.ACTIVE,
        deleted=False,
        new=True,
        dirty=True,
    ):
        super().__init__(new=new, dirty=dirty)
        self.billed_entity_id = billed_entity_id
        self.name = name
        self.accounts = accounts if accounts else []
        self.secondary_category = secondary_category
        self.category = category
        self.deleted = deleted
        self.status = status

    def allow_pay_after_checkout(self):
        return self.category in {
            BilledEntityCategory.BOOKER_COMPANY,
            BilledEntityCategory.GUEST_COMPANY,
            BilledEntityCategory.TRAVEL_AGENT,
        }

    def add_new_account(
        self, charge_type=ChargeTypes.NON_CREDIT, is_allowance_account=False
    ) -> BilledEntityAccount:
        new_account_number = (
            max([int(account.account_number) for account in self.accounts], default=0)
            + 1
        )
        account = BilledEntityAccount(
            account_number=new_account_number,
            account_type=charge_type,
            is_allowance_account=is_allowance_account,
        )
        self.accounts.append(account)
        return account

    def get_or_add_new_account(
        self, charge_type=ChargeTypes.NON_CREDIT
    ) -> BilledEntityAccount:
        account = next(
            (
                acc
                for acc in self.accounts
                if acc.is_new() and acc.account_type == charge_type
            ),
            None,
        )
        if not account:
            return self.add_new_account(charge_type)
        return account

    def create_account_if_not_exists(
        self, charge_type, account_number, bypass: False = None
    ) -> BilledEntityAccount:
        account = self.get_account(account_number)
        if not account:
            if (not bypass) & (
                account_number
                != max([account.account_number for account in self.accounts]) + 1
            ):
                raise ValidationException(BillingErrors.INVALID_ACCOUNT_NUMBER)
            account = BilledEntityAccount(
                account_number=account_number, account_type=charge_type
            )
            self.accounts.append(account)

        if not account.account_type:
            account.assigned_charge_types.update({charge_type})
            account.set_assigned_charge_types(account.assigned_charge_types)
        return account

    def get_account_for_new_assignment(
        self, charge_type, allowance_account=False
    ) -> BilledEntityAccountVO:
        for account in self.accounts:
            if account.is_locked() or account.deleted:
                continue

            if not allowance_account and account.is_allowance_account():
                continue

            if allowance_account and not account.is_allowance_account():
                continue

            if charge_type == ChargeTypes.CREDIT and account.is_credit_account():
                if not account.account_type and not account.assigned_charge_types:
                    # Set account type as first charge type
                    account.mark_as_credit_account()

                return BilledEntityAccountVO(
                    billed_entity_id=self.billed_entity_id,
                    account_number=account.account_number,
                )

            if (
                charge_type == ChargeTypes.NON_CREDIT
                and account.is_non_credit_account()
            ):
                if not account.account_type and not account.assigned_charge_types:
                    # Set account type as first charge type
                    account.mark_as_non_credit_account()

                return BilledEntityAccountVO(
                    billed_entity_id=self.billed_entity_id,
                    account_number=account.account_number,
                )

        account = self.add_new_account(charge_type=charge_type)
        if allowance_account:
            account.mark_as_allowance_account()
        return BilledEntityAccountVO(
            billed_entity_id=self.billed_entity_id,
            account_number=account.account_number,
        )

    def update_secondary_category(self, secondary_category):
        self.secondary_category = secondary_category
        self.mark_dirty()

    def update_name(self, name):
        self.name = name
        self.mark_dirty()

    def delete(self):
        self.deleted = True
        self.mark_dirty()

    @property
    def is_active(self):
        return self.status == BilledEntityStatus.ACTIVE

    def update_category(self, category):
        self.category = category
        self.mark_dirty()

    def account_exists(self, account_number) -> bool:
        return bool(
            account_number in [account.account_number for account in self.accounts]
        )

    def get_account(self, account_number) -> BilledEntityAccount:
        return next(
            (
                account
                for account in self.accounts
                if account.account_number == account_number
            ),
            None,
        )

    def mark_account_as_invoiced(self, account_number):
        account = self.get_account(account_number)
        if not account:
            return
        account.mark_invoiced()

    def mark_account_as_uninvoiced(self, account_number):
        account = self.get_account(account_number)
        if not account:
            return
        account.mark_uninvoiced()

    def mark_account_as_allowance_account(self, account_number):
        account = self.get_account(account_number)
        if not account:
            return
        account.mark_as_allowance_account()

    def unlock_accounts(self, account_numbers, mark_uninvoiced=False):
        for account_number in account_numbers:
            account = self.get_account(account_number)
            if not account:
                continue
            account.unlock(mark_uninvoiced=mark_uninvoiced)

    def latest_account_number(self) -> int:
        return max([account.account_number for account in self.accounts])

    def latest_account(self) -> BilledEntityAccountVO:
        accounts = [
            account
            for account in self.accounts
            if not (account.is_locked() or account.deleted)
        ]
        if not accounts:
            account = self.add_new_account()
        else:
            account = max(accounts, key=lambda account: account.account_number)
        return BilledEntityAccountVO(
            billed_entity_id=self.billed_entity_id,
            account_number=account.account_number,
        )

    def get_name_with_account_number(self, account_number):
        return self.name.full_name + " " + "(Account: " + str(account_number) + ")"

    def update_account_invoice_numbers_available_for_use(
        self,
        account_number,
        invoice_number=None,
        invoice_date=None,
        hotel_invoice_number=None,
    ):
        account = self.get_account(account_number)
        if not account:
            return
        account.update_invoice_numbers_available_for_use(
            invoice_number=invoice_number,
            invoice_date=invoice_date,
            hotel_invoice_number=hotel_invoice_number,
        )

    def pop_account_invoice_numbers_available_for_use(
        self, account_number, current_date
    ) -> str:
        account = self.get_account(account_number)
        if not account:
            return None
        return account.pop_invoice_numbers_available_for_use(current_date)

    def pop_account_hotel_invoice_numbers_available_for_use(
        self, account_number, current_date
    ):
        account = self.get_account(account_number)
        if not account:
            return None
        return account.pop_hotel_invoice_numbers_available_for_use(current_date)

    def is_billed_entity_travel_agent(self):
        return self.category == BilledEntityCategory.TRAVEL_AGENT

    def mark_cancel(self):
        self.status = BilledEntityStatus.CANCEL
        self.mark_dirty()

    def mark_noshow(self):
        self.status = BilledEntityStatus.NOSHOW
        self.mark_dirty()

    def mark_inactive(self):
        self.status = BilledEntityStatus.INACTIVE
        self.mark_dirty()

    def mark_active(self):
        self.status = BilledEntityStatus.ACTIVE
        self.mark_dirty()
