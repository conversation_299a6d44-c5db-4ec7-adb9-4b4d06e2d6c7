from ths_common.base_entity import EntityChangeTracker


class Card(EntityChangeTracker):
    def __init__(
        self,
        card_id,
        bill_id,
        holder_name,
        expiry,
        card_type,
        token,
        last_digits,
        bin,
        brand,
        pre_auth_amount,
        pre_auth_code,
        billed_entity_id=None,
    ):
        super().__init__()

        self.card_id = card_id
        self.bill_id = bill_id
        self.holder_name = holder_name
        self.expiry = expiry
        self.card_type = card_type
        self.token = token
        self.last_digits = last_digits
        self.bin = bin
        self.brand = brand
        self.pre_auth_amount = pre_auth_amount
        self.pre_auth_code = pre_auth_code
        self.billed_entity_id = billed_entity_id
