from sqlalchemy import (
    DECIMAL,
    JSON,
    Boolean,
    Column,
    Date,
    DateTime,
    Index,
    Integer,
    String,
    Text,
    and_,
)
from sqlalchemy.dialects.postgresql import ARRAY, JSONB, UUID
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from prometheus.infrastructure.database.common_models import TaxDetailDBType
from shared_kernel.infrastructure.database.orm_base import DeleteMixin, TimeStampMixin
from ths_common.constants.billing_constants import (
    ChargeStatus,
    IssuedByType,
    IssuedToType,
    PaymentStatus,
)


class BillModel(Base, TimeStampMixin, DeleteMixin):
    """
    Bill
    """

    __tablename__ = "bill"

    bill_id = Column(String, primary_key=True)
    vendor_id = Column(String)
    app_id = Column(String)
    parent_reference_number = Column(String)
    vendor_details = Column(JSON)
    fees = Column(JSON)
    parent_info = Column(JSON, nullable=True)
    bill_date = Column(DateTime(timezone=True))
    grace_period = Column(Integer)
    gstin = Column(String, index=True)
    version = Column(Integer)
    base_currency = Column(String)
    status = Column(String)


class ChargeModel(Base, TimeStampMixin, DeleteMixin):
    """
    Charge
    """

    __tablename__ = "charge"

    charge_id = Column(Integer, primary_key=True)
    bill_id = Column(String, primary_key=True)
    pretax_amount = Column(DECIMAL(precision=15, scale=4))
    tax_details = Column(ARRAY(TaxDetailDBType))
    tax_amount = Column(DECIMAL(precision=15, scale=4))
    posttax_amount = Column(DECIMAL(precision=15, scale=4))
    type = Column(String)  # Enum(ChargeTypes))
    bill_to_type = Column(String)  # Enum(ChargeBillToTypes))
    # auth id of the user who created the charge item.
    created_by = Column(String)

    # item details
    item_id = Column(String)
    item_name = Column(String)
    sku_category_id = Column(String)
    item_detail = Column("charge_item_detail", JSON)

    status = Column(String)  # Enum(ChargeStatus))
    applicable_date = Column(DateTime(timezone=True))
    applicable_business_date = Column(Date)
    posting_date = Column(Date)
    comment = Column(String)
    charge_split_type = Column(String)
    charge_components = Column(JSON)
    addon_charge_ids = Column(ARRAY(Integer))
    charge_to = Column(ARRAY(String))
    split_allowed = Column(Boolean)
    linked_addon_charge_ids = Column(ARRAY(Integer))
    is_inclusion_charge = Column(Boolean)
    is_room_linked_addon_charge = Column(Boolean)

    __table_args__ = (
        Index('ix_charge_bill_app_date', 'bill_id', 'applicable_date'),
        Index('ix_charge_bill_del_app_date', 'bill_id', 'deleted', 'applicable_date'),
        Index('ix_charge_bill_del_charge_id', 'bill_id', 'deleted', 'charge_id'),
        Index(
            'ix_charge_modified_at_bill_id',
            'modified_at',
            'bill_id',
        ),
        Index(
            'ix_charge_status_posting_date_applicable_business_date',
            'status',
            'posting_date',
            'applicable_business_date',
        ),
        Index(
            'ix_charge_night_audit_partial',
            'bill_id',
            'status',
            'deleted',
            'applicable_date',
            postgresql_where=(
                and_(status == ChargeStatus.CREATED.value, Column("deleted").is_(False))
            ),
        ),
    )

    def mapping_dict(self):
        return {
            "charge_id": self.charge_id,
            "bill_id": self.bill_id,
            "pretax_amount": self.pretax_amount,
            "tax_details": self.tax_details,
            "tax_amount": self.tax_amount,
            "posttax_amount": self.posttax_amount,
            "type": self.type,
            "bill_to_type": self.bill_to_type,
            "created_by": self.created_by,
            "item_id": self.item_id,
            "item_name": self.item_name,
            "sku_category_id": self.sku_category_id,
            "item_detail": self.item_detail,
            "status": self.status,
            "applicable_date": self.applicable_date,
            "posting_date": self.posting_date,
            "comment": self.comment,
            "charge_split_type": self.charge_split_type,
            "charge_components": self.charge_components,
            "addon_charge_ids": self.addon_charge_ids,
            "charge_to": self.charge_to,
            "deleted": self.deleted,
            "split_allowed": self.split_allowed,
            "is_inclusion_charge": self.is_inclusion_charge,
            "is_room_linked_addon_charge": self.is_room_linked_addon_charge,
            "linked_addon_charge_ids": self.linked_addon_charge_ids,
            "applicable_business_date": self.applicable_business_date,
        }


class ChargeSplitModel(Base, TimeStampMixin, DeleteMixin):
    """
    Charge splits of charges
    """

    __tablename__ = "charge_split"

    # TODO add tax details attributes here
    charge_split_id = Column(Integer, primary_key=True)
    charge_id = Column(Integer, nullable=False, primary_key=True)
    bill_id = Column(String, primary_key=True)
    charge_to = Column(String)
    tax = Column(DECIMAL(precision=15, scale=4))
    pre_tax = Column(DECIMAL(precision=15, scale=4))
    post_tax = Column(DECIMAL(precision=15, scale=4))
    invoice_id = Column(String, nullable=True)
    credit_note_id = Column(String, nullable=True)
    tax_details = Column(ARRAY(TaxDetailDBType))
    percentage = Column(DECIMAL(precision=8, scale=4), nullable=False)
    billed_entity_id = Column(Integer)
    billed_entity_account_number = Column(Integer)
    charge_type = Column(String)
    charge_sub_type = Column(String)
    bill_to_type = Column(String)
    payment_id = Column(Integer)
    post_allowance_tax_details = Column(ARRAY(TaxDetailDBType))

    __table_args__ = (
        Index(
            'charge_split_idx_bill_charge_charge_split_ids',
            bill_id,
            charge_id,
            charge_split_id,
        ),
    )

    def mapping_dict(self):
        return {
            "charge_split_id": self.charge_split_id,
            "charge_id": self.charge_id,
            "bill_id": self.bill_id,
            "charge_to": self.charge_to,
            "tax": self.tax,
            "pre_tax": self.pre_tax,
            "post_tax": self.post_tax,
            "invoice_id": self.invoice_id,
            "credit_note_id": self.credit_note_id,
            "tax_details": self.tax_details,
            "percentage": self.percentage,
            "billed_entity_id": self.billed_entity_id,
            "billed_entity_account_number": self.billed_entity_account_number,
            "charge_type": self.charge_type,
            "charge_sub_type": self.charge_sub_type,
            "bill_to_type": self.bill_to_type,
            "payment_id": self.payment_id,
            "post_allowance_tax_details": self.post_allowance_tax_details,
            "deleted": self.deleted,
        }


class PaymentModel(Base, TimeStampMixin, DeleteMixin):
    """
    Payment
    """

    __tablename__ = "payment"

    bill_id = Column(String, primary_key=True)
    payment_id = Column(Integer, nullable=False, primary_key=True)
    amount = Column(DECIMAL(precision=15, scale=4))
    date_of_payment = Column(DateTime(timezone=True))
    payment_mode = Column(String)  # Enum(PaymentModes)
    payment_mode_sub_type = Column(String)  # Enum(PaymentModeSubTypes)
    payment_type = Column(String)  # Enum(PaymentTypes)
    payment_details = Column(JSON)
    status = Column(String)  # Enum(PaymentStatus))
    paid_by = Column(String)
    paid_to = Column(String)  # Enum(PaymentReceiverTypes)
    payment_channel = Column(String)  # Enum(PaymentChannels)
    payment_ref_id = Column(String)
    comment = Column(String)
    payment_currency = Column(String)  # Enum(CurrencyTypeenum)
    amount_in_payment_currency = Column(DECIMAL(precision=15, scale=4))
    payer = Column(String, nullable=True)
    confirmed = Column(Boolean)
    payment_business_date = Column(Date)
    posting_date = Column(Date)
    posted_date = Column(Date)
    payor_billed_entity_id = Column(Integer)
    refund_reason = Column(String)
    payout_details = Column(JSON)
    source_id = Column(Integer)

    __table_args__ = (
        Index(
            'ix_payment_created_at_ref_id_comment',
            'created_at',
            'payment_ref_id',
            'comment',
        ),
        Index(
            'ix_payment_modified_at_bill_id',
            'modified_at',
            'bill_id',
        ),
        Index(
            'ix_payment_for_night_audit_partial',
            'bill_id',
            'status',
            'deleted',
            'date_of_payment',
            postgresql_where=(
                and_(
                    status.in_(
                        [
                            PaymentStatus.DONE.value,
                            PaymentStatus.UNCONFIRMED.value,
                        ]
                    ),
                    Column("deleted").is_(False),
                )
            ),
        ),
    )

    def mapping_dict(self):
        return {
            "bill_id": self.bill_id,
            "payment_id": self.payment_id,
            "amount": self.amount,
            "date_of_payment": self.date_of_payment,
            "payment_mode": self.payment_mode,
            "payment_mode_sub_type": self.payment_mode_sub_type,
            "payment_type": self.payment_type,
            "payment_details": self.payment_details,
            "paid_by": self.paid_by,
            "status": self.status,
            "paid_to": self.paid_to,
            "payment_channel": self.payment_channel,
            "payment_ref_id": self.payment_ref_id,
            "comment": self.comment,
            "payment_currency": self.payment_currency,
            "amount_in_payment_currency": self.amount_in_payment_currency,
            "payer": self.payer,
            "deleted": self.deleted,
            "confirmed": self.confirmed,
            "payment_business_date": self.payment_business_date,
            "posting_date": self.posting_date,
            "posted_date": self.posted_date,
            "payor_billed_entity_id": self.payor_billed_entity_id,
            "refund_reason": self.refund_reason,
            "payout_details": self.payout_details,
            "source_id": self.source_id,
        }


###########
# Invoice #
###########


class InvoiceModel(Base, TimeStampMixin, DeleteMixin):
    """
    Invoice
    """

    __tablename__ = "invoice"

    bill_id = Column(String, index=True)
    vendor_id = Column(String)
    vendor_details = Column(JSON)
    invoice_id = Column(String, primary_key=True)
    invoice_number = Column(String)

    invoice_date = Column(Date, nullable=False)
    invoice_due_date = Column(Date, nullable=False)
    parent_info = Column(JSON, nullable=True)
    bill_to = Column(JSON)
    user_info_map = Column(JSON)
    status = Column(String)  # Enum(InvoiceStatus, name='invoice_status'))
    generated_by = Column(String)
    generation_channel = Column(String)
    pretax_amount = Column(DECIMAL(precision=15, scale=4))
    tax_amount = Column(DECIMAL(precision=15, scale=4))
    posttax_amount = Column(DECIMAL(precision=15, scale=4))
    tax_details_breakup = Column(JSON, nullable=True)
    invoice_url = Column(String)
    version = Column(Integer)
    signed_url = Column(String, nullable=True)
    signed_url_expiry_time = Column(DateTime(timezone=True), nullable=True)
    # TODO(Bibek):: Add userids prefixed to all charge_to keys
    allowed_charge_to_ids = Column(ARRAY(String), nullable=True)
    bill_to_type = Column(String, nullable=True)
    allowed_charge_types = Column(ARRAY(String), nullable=True)

    issued_by = Column(JSON, server_default='{}')
    issued_to_type = Column(String, server_default=IssuedToType.CUSTOMER.value)
    issued_by_type = Column(String, server_default=IssuedByType.HOTEL.value)
    hotel_invoice_id = Column(String, nullable=True)

    irn = Column(String)
    qr_code = Column(String)
    signed_invoice = Column(String)
    irp_ack_number = Column(String)
    irp_ack_date = Column(Date)
    is_einvoice = Column(Boolean, default=False, server_default='f')
    billed_entity_id = Column(Integer)
    billed_entity_account_number = Column(Integer)
    is_downloaded = Column(Boolean, nullable=True)
    is_reissue_allowed = Column(Boolean)
    is_spot_credit = Column(Boolean)

    __table_args__ = (
        Index('ix_invoice_date_vendor_id', 'invoice_date', 'vendor_id'),
        Index(
            'ix_invoice_vendor_id_status_created_at',
            'vendor_id',
            'status',
            'created_at',
        ),
    )


class InvoiceChargeModel(Base, TimeStampMixin, DeleteMixin):
    """
    Invoice to charges mapping
    """

    __tablename__ = "invoice_charges"

    invoice_charge_id = Column(Integer, primary_key=True)

    invoice_id = Column(String, primary_key=True)
    charge_id = Column(Integer)
    charge_split_ids = Column(ARRAY(Integer))
    charge_type = Column('type', String)
    bill_to_type = Column(String)
    # auth id of the user who created the charge item.
    created_by = Column(String)

    # amount details per user
    pretax_amount = Column(DECIMAL(precision=15, scale=4))
    tax_details = Column(ARRAY(TaxDetailDBType))
    tax_amount = Column(DECIMAL(precision=15, scale=4))
    posttax_amount = Column(DECIMAL(precision=15, scale=4))

    # item details
    item_name = Column(String)
    item_code = Column(String)
    item_detail = Column("charge_item_detail", JSON)
    item_hsn_code = Column(String)
    applicable_date = Column(DateTime(timezone=True))
    comment = Column(String)
    charge_to_ids = Column('charge_to', ARRAY(String))
    recorded_time = Column(DateTime(timezone=True))
    charge_status = Column(String)
    credit_note_generated_amount = Column(DECIMAL(precision=15, scale=4))


class InvoiceNumberCount(Base, TimeStampMixin, DeleteMixin):
    """
    Invoice Number count per hotel
    """

    __tablename__ = "invoice_number_count"

    vendor_id = Column(Integer, primary_key=True)
    sequence_number = Column(Integer)


class InvoiceSequenceModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "invoice_sequence"

    issued_by_type = Column(String, primary_key=True)
    vendor_id = Column(String, primary_key=True)
    gstin = Column(String, primary_key=True)
    prefix = Column(String)
    last_sequence_number = Column(Integer)
    sequence_generation_strategy = Column(String)

    def mapping_dict(self):
        return {
            "issued_by_type": self.issued_by_type,
            "vendor_id": self.vendor_id,
            "gstin": self.gstin,
            "prefix": self.prefix,
            "last_sequence_number": self.last_sequence_number,
            "sequence_generation_strategy": self.sequence_generation_strategy,
            "deleted": self.deleted,
        }


class BlockedInvoiceNumberModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "blocked_invoice_number"

    issued_by_type = Column(String, primary_key=True)
    vendor_id = Column(String, primary_key=True)
    gstin = Column(String, primary_key=True)
    invoice_number = Column(String, primary_key=True)
    hotel_invoice_number = Column(String)
    reason = Column(String)


class BlockedCreditNoteNumberModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "blocked_credit_note_number"

    issued_by_type = Column(String, primary_key=True)
    vendor_id = Column(String, primary_key=True)
    gstin = Column(String, primary_key=True)
    credit_note_number = Column(String, primary_key=True)
    hotel_credit_note_number = Column(String)
    reason = Column(String)


class CreditNoteSequenceModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "credit_note_sequence"

    issued_by_type = Column(String, primary_key=True)
    vendor_id = Column(String, primary_key=True)
    gstin = Column(String, primary_key=True)
    prefix = Column(String)
    last_sequence_number = Column(Integer)
    sequence_generation_strategy = Column(String)

    def mapping_dict(self):
        return {
            "issued_by_type": self.issued_by_type,
            "vendor_id": self.vendor_id,
            "gstin": self.gstin,
            "prefix": self.prefix,
            "last_sequence_number": self.last_sequence_number,
            "sequence_generation_strategy": self.sequence_generation_strategy,
            "deleted": self.deleted,
        }


class CreditNoteModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "credit_note"

    bill_id = Column(String, index=True)
    credit_note_id = Column(String, primary_key=True)
    credit_note_number = Column(String)
    credit_note_date = Column(Date, nullable=False)
    vendor_id = Column(String)
    vendor_details = Column(JSON)
    issued_to = Column(JSON)
    issued_by = Column(JSON)
    status = Column(String)
    comment = Column(String)
    pretax_amount = Column(DECIMAL(precision=15, scale=4))
    tax_amount = Column(DECIMAL(precision=15, scale=4))
    posttax_amount = Column(DECIMAL(precision=15, scale=4))
    tax_details = Column(JSON, nullable=True)
    credit_note_url = Column(String)
    issued_to_type = Column(String)
    issued_by_type = Column(String)
    hotel_credit_note_id = Column(String)
    version = Column(Integer)
    signed_url = Column(String, nullable=True)
    signed_url_expiry_time = Column(DateTime(timezone=True), nullable=True)
    irn = Column(String)
    qr_code = Column(String)
    signed_invoice = Column(String)
    irp_ack_number = Column(String)
    irp_ack_date = Column(Date)
    is_einvoice = Column(Boolean, default=False, server_default='f')
    billed_entity_id = Column(Integer)
    billed_entity_account_number = Column(Integer)


class CreditNoteLineItemModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "credit_note_line_item"

    credit_note_id = Column(String, primary_key=True)
    credit_note_line_item_id = Column(Integer, primary_key=True)
    invoice_id = Column(String)
    invoice_charge_id = Column(Integer)
    charge_id = Column(Integer, nullable=True)
    pretax_amount = Column(DECIMAL(precision=15, scale=4))
    tax_details = Column(JSON)
    tax_amount = Column(DECIMAL(precision=15, scale=4))
    posttax_amount = Column(DECIMAL(precision=15, scale=4))

    applicable_date = Column(DateTime(timezone=True))

    __table_args__ = (
        Index(
            'ix_credit_note_line_item_invoice_charge',
            'invoice_id',
            'invoice_charge_id',
            'credit_note_id',
        ),
    )


class PaymentSplitModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "payment_split"

    payment_split_id = Column(Integer, primary_key=True)
    payment_id = Column(Integer, nullable=False, primary_key=True)
    bill_id = Column(String, primary_key=True)
    payment_type = Column(String)  # Enum(PaymentTypes)
    amount = Column(DECIMAL(precision=15, scale=4))
    payment_mode = Column(String)
    billed_entity_id = Column(Integer)
    billed_entity_account_number = Column(Integer)

    def mapping_dict(self):
        return {
            "payment_split_id": self.payment_split_id,
            "payment_id": self.payment_id,
            "bill_id": self.bill_id,
            "payment_type": self.payment_type,
            "amount": self.amount,
            "payment_mode": self.payment_mode,
            "billed_entity_id": self.billed_entity_id,
            "billed_entity_account_number": self.billed_entity_account_number,
            "deleted": self.deleted,
        }


class CashierSessionModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "cashier_session"

    cashier_session_id = Column(String, primary_key=True)
    cash_register_id = Column(String)
    session_number = Column(Integer)
    vendor_id = Column(String)
    start_datetime = Column(DateTime(timezone=True))
    end_datetime = Column(DateTime(timezone=True), nullable=True)
    status = Column(String)  # CashierSessionStatus
    opening_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    closing_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    opened_by = Column(String)
    closed_by = Column(String, nullable=True)


class CashierSessionBalanceModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "cashier_session_balance"

    cash_counter_balance_id = Column(Integer, nullable=False, primary_key=True)
    cashier_session_id = Column(String, nullable=False, primary_key=True)
    amount = Column(DECIMAL(precision=15, scale=4))
    currency = Column(String)
    balance_type = Column(String)  # CashCounterBalance


class CashRegisterModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "cash_register"

    cash_register_id = Column(String, nullable=False, primary_key=True)
    cash_register_name = Column(String)
    vendor_id = Column(String, nullable=False)
    start_datetime = Column(DateTime(timezone=True), nullable=True)
    end_datetime = Column(DateTime(timezone=True), nullable=True)
    default_opening_balance = Column(ARRAY(String))
    opened_by = Column(String)
    carry_balance_to_next_session = Column(Boolean)
    base_currency = Column(String)
    lastest_voucher_sequence_number = Column(Integer)


class CashierPaymentModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "cashier_payment"

    payment_id = Column(Integer, nullable=False, primary_key=True)
    cashier_session_id = Column(String, nullable=False, primary_key=True)
    date_of_payment = Column(DateTime(timezone=True))
    payment_mode = Column(String)  # Enum(PaymentModes)
    payment_type = Column(String)  # Enum(CashCounterPaymentTypes)
    payment_details = Column(JSON)
    status = Column(String)  # Enum(CashCounterPaymentStatus))
    paid_to = Column(String)
    comment = Column(String)
    amount_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    payment_currency = Column(String)  # Enum(CurrencyTypeEnum)
    amount_in_payment_currency = Column(DECIMAL(precision=15, scale=4))
    payment_mode_sub_type = Column(String)  # Enum(PaymentModeSubTypes)
    booking_id = Column(String, nullable=True)
    pos_order_id = Column(String, nullable=True)
    voucher_url = Column(String, nullable=True)
    added_by = Column(String)
    current_session_balance = Column(DECIMAL(precision=15, scale=4))
    transaction_id = Column(String)
    booking_owner_name = Column(String)
    bill_id = Column(String)
    bill_payment_id = Column(Integer)
    voucher_number = Column(Integer)
    booking_reference_number = Column(String, nullable=True)


class BilledEntityModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "billed_entity"
    billed_entity_id = Column(Integer, nullable=False, primary_key=True)
    bill_id = Column(String, primary_key=True)
    first_name = Column('first_name', String)
    last_name = Column('last_name', String)
    salutation = Column('salutation', String)
    category = Column(String)  # Enum(BilledEntityCategory)
    status = Column(String)  # Enum(BilledEntityStatus)
    secondary_category = Column(String)

    def mapping_dict(self):
        return {
            "billed_entity_id": self.billed_entity_id,
            "bill_id": self.bill_id,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "salutation": self.salutation,
            "category": self.category,
            "secondary_category": self.secondary_category,
            "deleted": self.deleted,
            "status": self.status,
        }


class AccountModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "account"

    billed_entity_id = Column(Integer, nullable=False, primary_key=True)
    bill_id = Column(String, primary_key=True)
    account_number = Column(Integer, primary_key=True)
    invoiced = Column(Boolean)
    locked = Column(Boolean)
    is_allowance_account = Column(Boolean)
    account_type = Column(String)
    invoice_numbers_available_for_use = Column(JSONB)

    def mapping_dict(self):
        return {
            "billed_entity_id": self.billed_entity_id,
            "bill_id": self.bill_id,
            "account_number": self.account_number,
            "deleted": self.deleted,
            "invoiced": self.invoiced,
            "locked": self.locked,
            "is_allowance_account": self.is_allowance_account,
            "account_type": self.account_type,
            "invoice_numbers_available_for_use": self.invoice_numbers_available_for_use,
        }


class AllowanceModel(Base, TimeStampMixin, DeleteMixin):
    """
    Allowances of charge splits
    """

    __tablename__ = "allowance"

    allowance_id = Column(Integer, primary_key=True)
    charge_id = Column(Integer, nullable=False, primary_key=True)
    charge_split_id = Column(Integer, nullable=False, primary_key=True)
    bill_id = Column(String, primary_key=True)
    tax_amount = Column(DECIMAL(precision=15, scale=4))
    pretax_amount = Column(DECIMAL(precision=15, scale=4))
    posttax_amount = Column(DECIMAL(precision=15, scale=4))
    tax_details = Column(ARRAY(TaxDetailDBType))
    invoice_id = Column(String, nullable=True)
    credit_note_id = Column(String, nullable=True)
    billed_entity_id = Column(Integer)
    billed_entity_account_number = Column(Integer)
    remarks = Column(String)
    posting_date = Column(Date)
    status = Column(String)

    def mapping_dict(self):
        return {
            "allowance_id": self.allowance_id,
            "charge_id": self.charge_id,
            "bill_id": self.bill_id,
            "tax_amount": self.tax_amount,
            "pretax_amount": self.pretax_amount,
            "posttax_amount": self.posttax_amount,
            "remarks": self.remarks,
            "deleted": self.deleted,
            "billed_entity_id": self.billed_entity_id,
            "charge_split_id": self.charge_split_id,
            "billed_entity_account_number": self.billed_entity_account_number,
            "invoice_id": self.invoice_id,
            "credit_note_id": self.credit_note_id,
            "posting_date": self.posting_date,
            "tax_details": self.tax_details,
            "status": self.status,
        }


class CreditShellModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "credit_shell"

    credit_shell_id = Column(String, primary_key=True)
    bill_id = Column(String)
    credit_note_id = Column(String)
    billed_entity_id = Column(Integer)
    issue_date = Column(Date)
    total_credit = Column(DECIMAL(precision=15, scale=4))
    remaining_credit = Column(DECIMAL(precision=15, scale=4))
    currency = Column(String)  # CurrencyTypeEnum
    paid_by = Column(String)
    paid_to = Column(String)
    source_billed_entity_id = Column(String)
    source_billed_entity_account_number = Column(String)
    is_refundable_folio = Column(Boolean)
    deleted = Column(Boolean)
    version = Column(Integer)

    def mapping_dict(self):
        return {
            "credit_shell_id": self.credit_shell_id,
            "bill_id": self.bill_id,
            "credit_note_id": self.credit_note_id,
            "billed_entity_id": self.billed_entity_id,
            "issue_date": self.issue_date,
            "total_credit": self.total_credit,
            "remaining_credit": self.remaining_credit,
            "currency": self.currency,
            "paid_by": self.paid_by,
            "paid_to": self.paid_to,
            "source_billed_entity_id": self.source_billed_entity_id,
            "source_billed_entity_account_number": self.source_billed_entity_account_number,
            "is_refundable_folio": self.is_refundable_folio,
            "deleted": self.deleted,
            "version": self.version,
        }


class CardModel(Base, TimeStampMixin):
    __tablename__ = 'card'

    card_id = Column(UUID, primary_key=True)
    holder_name = Column(String)
    bin = Column(Integer)
    last_digits = Column(Integer)
    card_type = Column(String)  # Enum(CardTypes)
    brand = Column(String)
    expiry = Column(String)
    token = Column(String)
    pre_auth_amount = Column(DECIMAL(precision=15, scale=4))
    currency = Column(String)
    pre_auth_code = Column(String)
    bill_id = Column(String, nullable=False)
    billed_entity_id = Column(Integer)


class CurrencyExchangeModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = 'currency_exchange'

    currency_exchange_id = Column(String, primary_key=True)
    room_number = Column(String, nullable=False)
    guest_name = Column(String, nullable=False)
    booking_id = Column(String)
    id_proof_type = Column(String, nullable=False)  # Enum(IDProofType))
    id_number = Column(String, nullable=False)
    id_proof_country_code = Column(String, nullable=False)
    id_proof_issued_date = Column(DateTime(timezone=True), nullable=False)
    id_proof_issued_place = Column(String)
    id_proof_attachment_id = Column(String)
    amount_in_foreign_currency = Column(DECIMAL(precision=15, scale=4), nullable=False)
    foreign_currency_sold = Column(String, nullable=False)
    foreign_currency_payment_mode = Column(String, nullable=False)
    amount_in_base_currency = Column(DECIMAL(precision=15, scale=4), nullable=False)
    taxable_amount = Column(DECIMAL(precision=15, scale=4), nullable=False)
    tax_amount = Column(DECIMAL(precision=9, scale=4), nullable=False)
    tax_details = Column(ARRAY(TaxDetailDBType))
    # Since round_off will always be less than 1. Precision should just be 1 more than scale
    round_off = Column(DECIMAL(precision=5, scale=4), nullable=False)
    total_payable_in_base_currency = Column(
        DECIMAL(precision=15, scale=4), nullable=False
    )
    exchange_rate = Column(String, nullable=False)
    transaction_date = Column(Date, nullable=False)
    remarks = Column(String)
    transaction_id = Column(String, unique=True)
    certificate_number = Column(String, unique=True)
    encashment_certificate_url = Column(String)


class CurrencyExchangeCertificateNoSequence(Base, TimeStampMixin):
    __tablename__ = 'currency_exchange_certificate_no_sequence'

    vendor_id = Column(String, primary_key=True)
    last_sequence_number = Column(Integer)


class PaymentReceiptSequenceModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "payment_receipt_sequence"

    payment_type = Column(String, primary_key=True)
    vendor_id = Column(String, primary_key=True)
    prefix = Column(String)
    last_sequence_number = Column(Integer)


class PaymentReceiptModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "payment_receipt"

    payment_receipt_date = Column(Date)
    payment_receipt_number = Column(String)
    payment_receipt_receiver_info = Column(JSON)
    affected_room_nos = Column(JSON)
    vendor_id = Column(String)
    payment_receipt_url = Column(String)
    signed_url = Column(String, nullable=True)
    signed_url_expiry_time = Column(DateTime(timezone=True), nullable=True)
    # below fields are dereference from payment
    bill_id = Column(String, primary_key=True)
    payment_id = Column(Integer, nullable=False, primary_key=True)
    payment_type = Column(String)


class CreditShellAuditTrailModel(Base, TimeStampMixin):
    __tablename__ = "credit_shell_audit_trail"

    credit_shell_audit_trail_id = Column(Integer, primary_key=True)
    target_booking_reference_number = Column(String)
    credit_shell_id = Column(String)
    event_type = Column(String)
    transaction_amount = Column(DECIMAL(precision=15, scale=4))
    remaining_amount = Column(DECIMAL(precision=15, scale=4))
    action_datetime = Column(DateTime(timezone=True))
    request_id = Column(String)
    remarks = Column(String)


class FolioModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "folio"

    bill_id = Column(String, primary_key=True)
    billed_entity_id = Column(Integer, nullable=False)
    folio_number = Column(Integer, primary_key=True)
    account_number = Column(Integer, nullable=False)

    __table_args__ = (
        Index(
            'ix_folio_bill_id_billed_entity_account',
            'bill_id',
            'billed_entity_id',
            'account_number',
            'folio_number',
        ),
    )

    def mapping_dict(self):
        return {
            "bill_id": self.bill_id,
            "billed_entity_id": self.billed_entity_id,
            "folio_number": self.folio_number,
            "account_number": self.account_number,
            "deleted": self.deleted,
        }


class CreditShellRefundModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "credit_shell_refund"

    amount = Column(DECIMAL(precision=15, scale=4))
    bill_id = Column(String, nullable=False)
    booking_id = Column(String, nullable=False)
    credit_shell_id = Column(String, nullable=False)
    credit_shell_refund_id = Column(String, primary_key=True)
    paid_by = Column(String, nullable=False)
    paid_to = Column(String, nullable=False)
    payment_mode = Column(String, nullable=False)
    payout_details = Column(JSON, nullable=True)
    payment_ref_id = Column(String, nullable=True)
    remarks = Column(String, nullable=True)

    def mapping_dict(self):
        return {
            "amount": self.amount,
            "bill_id": self.bill_id,
            "booking_id": self.booking_id,
            "credit_shell_id": self.credit_shell_id,
            "credit_shell_refund_id": self.credit_shell_refund_id,
            "paid_by": self.paid_by,
            "paid_to": self.paid_to,
            "payment_mode": self.payment_mode,
            "payout_details": self.payout_details,
            "payment_ref_id": self.payment_ref_id,
            "remarks": self.remarks,
            "deleted": self.deleted,
        }
