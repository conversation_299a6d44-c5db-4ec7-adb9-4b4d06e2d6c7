from sqlalchemy import Column, DateTime, Integer, String, Text
from sqlalchemy.dialects.postgresql import JSON
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from shared_kernel.infrastructure.database.orm_base import TimeStampMixin

__all__ = ['EInvoicingAuditTrailModel']


class EInvoicingAuditTrailModel(Base, TimeStampMixin):
    __tablename__ = "einvoicing_audit_trail"

    einvoicing_audit_trail_id = Column('einvoicing_audit_trail_id', Integer, primary_key=True)
    booking_id = Column('booking_id', String, index=True)
    invoice_id = Column('invoice_id', String, index=True)
    credit_note_id = Column('credit_note_id', String)
    bill_id = Column('bill_id', String)
    hotel_id = Column('hotel_id', String, index=True)
    event_type = Column('event_type', String, index=True)
    action_type = Column('action_type', String)
    user_action = Column('user_action', String)
    user_id = Column('user_id', String)
    user_type = Column('user_type', String)
    auth_id = Column('auth_id', String)
    application = Column('application', String)
    request_id = Column('request_id', String)
    cleartax_error_code = Column('cleartax_error_code', String)
    cleartax_error_message = Column('cleartax_error_message', Text)
    cleartax_response_payload = Column('cleartax_response_payload', JSON)
    action_datetime = Column('action_datetime', DateTime(timezone=True), index=True)
    remarks = Column('remarks', Text)
