from sqlalchemy import Column, String
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from shared_kernel.infrastructure.database.orm_base import TimeStampMixin

__all__ = ['EInvoicingAuditTrailModel']


class EInvoicingAuditTrailModel(Base, TimeStampMixin):
    __tablename__ = "einvoicing_audit_trail"

    audit_id = Column('audit_id', String, primary_key=True)
    user = Column('user', String)
    user_type = Column('user_type', String)
    request_id = Column('request_id', String)
    invoice_id = Column('invoice_id', String, index=True)
    hotel_id = Column('hotel_id', String, index=True)
    audit_type = Column('audit_type', String, index=True)
    audit_payload = Column('audit_payload', JSON)
