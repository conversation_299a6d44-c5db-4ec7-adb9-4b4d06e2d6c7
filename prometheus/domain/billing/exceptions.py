# pylint: disable=no-member
from ths_common.exceptions import CRSException

from .errors import BillingErrors


class BillingError(CRSException):
    error = BillingErrors.BILLING_ERROR

    def __init__(
        self, error=None, description=None, extra_payload=None, format_dict=None
    ):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error.error_code
            self.message = (
                self.error.message.format(**format_dict)
                if format_dict
                else self.error.message
            )
        super().__init__(description=description, extra_payload=extra_payload)


class InvoiceInLockPeriod(BillingError):
    error = BillingErrors.INVOICE_IN_LOCK_PERIOD


class InvoiceInGStFilingPeriod(BillingError):
    error = BillingErrors.INVOICE_IN_GST_FILING_PERIOD


class PaymentReceiptNumberMaxLengthBreached(BillingError):
    error = BillingErrors.PAYMENT_RECEIPT_NUMBER_MAX_PREFIX_LENGTH_BREACHED

    def __init__(self, max_length, current_prefix, current_sequence):
        format_dict = {
            'max_length': max_length,
            'current_prefix': current_prefix,
            'current_sequence': current_sequence,
        }
        super().__init__(format_dict=format_dict)


class CreditNoteNumberMaxLengthBreached(BillingError):
    error = BillingErrors.CREDIT_NOTE_NUMBER_MAX_PREFIX_LENGTH_BREACHED

    def __init__(self, max_length, current_prefix, current_sequence):
        format_dict = {
            'max_length': max_length,
            'current_prefix': current_prefix,
            'current_sequence': current_sequence,
        }
        super().__init__(format_dict=format_dict)


class InvoiceNumberMaxLengthBreached(BillingError):
    error = BillingErrors.INVOICE_NUMBER_MAX_LENGTH_BREACHED

    def __init__(self, max_length, current_prefix, sequence_number):
        format_dict = {
            'max_length': max_length,
            'current_prefix': current_prefix,
            'current_sequence': sequence_number,
        }
        super().__init__(format_dict=format_dict)


class EInvoicingException(BillingError):
    error = BillingErrors.BILLING_ERROR


class EInvoicingError(EInvoicingException):
    """Known exceptions are categorized as errors"""

    pass


class EInvoicingValidationError(EInvoicingError):
    pass


class EInvoicingDuplicate(EInvoicingException):
    pass


class EInvoicingWrongGstin(EInvoicingException):
    pass


class WrongCurrencyError(BillingError):
    error = BillingErrors.CURRENCY_ERROR


class SpotCreditIssueError(BillingError):
    error = BillingErrors.CANNOT_ISSUE_SPOT_CREDIT
