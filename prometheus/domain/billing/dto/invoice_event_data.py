from typing import List

from prometheus.domain.billing.dto.stay_charge_item_event_data import (
    StayChargeItemEventData,
)


class InvoiceEventData(object):
    def __init__(
        self,
        invoice_id,
        invoice_number,
        invoice_amount,
        stay_charge_item_details: List[StayChargeItemEventData] = None,
        status=None,
    ):
        self.invoice_id = invoice_id
        self.invoice_number = invoice_number
        self.invoice_amount = invoice_amount
        self.stay_charge_item_details = stay_charge_item_details
        self.status = status
