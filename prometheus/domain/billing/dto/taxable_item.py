from decimal import Decimal
from typing import List

from treebo_commons.money import Money

from prometheus.common.helpers.tax_utils import round_money
from ths_common.value_objects import NotAssigned, TaxDetail


class TaxableItem:
    def __init__(
        self,
        sku_category_id,
        applicable_date,
        pretax_amount=NotAssigned,
        posttax_amount=NotAssigned,
        charge_id=None,
        index=None,
        charge_dtos=None,
        buyer_gst_details=None,
    ):
        self.sku_category_id = sku_category_id
        self.applicable_date = applicable_date
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.tax_amount = NotAssigned
        self.tax_details = NotAssigned
        self.charge_id = charge_id
        self.index = index
        # Charge Dtos for which this taxable item is built. After tax computation, tax amount will be split between
        # these charge dtos in the ratio of pretax or posttax price already present in charge dtos
        self.charge_dtos = charge_dtos
        # To uniquely identify a tax request object
        self.buyer_gst_details = buyer_gst_details
        self.tax_request_hash = hash(
            (
                self.sku_category_id,
                self.applicable_date,
                str(self.pretax_amount.amount)
                if self.pretax_amount not in (None, NotAssigned)
                else None,
                str(self.posttax_amount.amount)
                if self.posttax_amount not in (None, NotAssigned)
                else None,
                f'{buyer_gst_details.gstin_num}{buyer_gst_details.is_sez}{buyer_gst_details.is_sez}'
                if buyer_gst_details
                else None,
            )
        )

    def update_tax(
        self, pretax_amount, posttax_amount, tax_amount, tax_details: List[TaxDetail]
    ):
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.tax_amount = tax_amount
        self.tax_details = tax_details

        if self.charge_dtos:
            self._allocate_tax_proportionately_to_attached_charge_dtos()

    def _allocate_tax_proportionately_to_attached_charge_dtos(self):
        if not self.charge_dtos:
            return

        for charge_dto in self.charge_dtos:
            if (
                charge_dto.pretax_amount is not None
                and charge_dto.pretax_amount != NotAssigned
            ):
                if charge_dto.pretax_amount == 0:
                    charge_dto.posttax_amount = Money("0", self.pretax_amount.currency)
                    charge_dto.tax_details = [
                        TaxDetail(
                            tax_type=td.tax_type,
                            percentage=td.percentage,
                            amount=Money("0", self.pretax_amount.currency),
                        )
                        for td in self.tax_details
                    ]
                else:
                    tax_distribution_ratio = (
                        charge_dto.pretax_amount.amount / self.pretax_amount.amount
                    )
                    (
                        charge_dto.tax_details,
                        tax_amount,
                    ) = self._get_tax_items_and_total_tax_amount(tax_distribution_ratio)
                    charge_dto.tax_amount = Money(
                        tax_amount, self.pretax_amount.currency
                    )
                    charge_dto.posttax_amount = (
                        charge_dto.pretax_amount + charge_dto.tax_amount
                    )

            else:
                if charge_dto.posttax_amount == 0:
                    charge_dto.pretax_amount = Money("0", self.pretax_amount.currency)
                    charge_dto.tax_details = [
                        TaxDetail(
                            tax_type=td.tax_type,
                            percentage=td.percentage,
                            amount=Money("0", self.pretax_amount.currency),
                        )
                        for td in self.tax_details
                    ]
                else:
                    tax_distribution_ratio = (
                        charge_dto.posttax_amount.amount / self.posttax_amount.amount
                    )
                    (
                        charge_dto.tax_details,
                        tax_amount,
                    ) = self._get_tax_items_and_total_tax_amount(tax_distribution_ratio)
                    charge_dto.tax_amount = Money(
                        tax_amount, self.pretax_amount.currency
                    )
                    charge_dto.pretax_amount = (
                        charge_dto.posttax_amount - charge_dto.tax_amount
                    )

            if charge_dto.pretax_amount == 0:
                charge_dto.tax_amount = Money("0", self.pretax_amount.currency)

    def _get_tax_items_and_total_tax_amount(self, tax_distribution_ratio):
        tax_items = []
        tax_amount = Decimal('0')
        for td in self.tax_details:
            amount = round_money(td.amount * tax_distribution_ratio)
            tax_item = TaxDetail(
                tax_type=td.tax_type,
                percentage=td.percentage,
                amount=amount,
            )
            tax_items.append(tax_item)
            tax_amount += tax_item.amount
        return tax_items, tax_amount
