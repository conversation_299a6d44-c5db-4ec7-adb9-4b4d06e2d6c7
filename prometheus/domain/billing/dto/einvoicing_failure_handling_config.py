from typing import Optional


class EInvoicingFailureHandlingConfig:
    """Configuration for e-invoicing failure handling"""
    
    def __init__(
        self,
        enabled: bool = False,
        show_popup_on_failure: bool = False,
        required_privilege: Optional[str] = None,
    ):
        self.enabled = enabled
        self.show_popup_on_failure = show_popup_on_failure
        self.required_privilege = required_privilege

    @classmethod
    def from_dict(cls, config_dict: dict):
        """Create config from dictionary"""
        if not config_dict:
            return cls()
        
        return cls(
            enabled=config_dict.get('enabled', False),
            show_popup_on_failure=config_dict.get('show_popup_on_failure', False),
            required_privilege=config_dict.get('required_privilege'),
        )

    def to_dict(self) -> dict:
        """Convert config to dictionary"""
        return {
            'enabled': self.enabled,
            'show_popup_on_failure': self.show_popup_on_failure,
            'required_privilege': self.required_privilege,
        }

    def is_popup_enabled(self) -> bool:
        """Check if popup should be shown on e-invoicing failure"""
        return self.enabled and self.show_popup_on_failure

    def has_required_privilege(self, user_privileges: list) -> bool:
        """Check if user has required privilege to see the popup"""
        if not self.required_privilege:
            return True
        return self.required_privilege in user_privileges
