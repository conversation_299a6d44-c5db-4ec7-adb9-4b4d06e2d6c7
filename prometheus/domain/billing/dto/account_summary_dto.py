from prometheus.domain.billing.dto.bill_summary_dto import BillSummaryDto
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO


class AccountSummaryDomainDto:
    def __init__(
        self,
        billed_entity_account: BilledEntityAccountVO = None,
        accounts_summary: BillSummaryDto = None,
        folio_number=None,
    ):
        self.billed_entity_account = billed_entity_account
        self.account_summary = accounts_summary
        self.folio_number = folio_number

    def __eq__(self, other):
        return (
            isinstance(other, AccountSummaryDomainDto)
            and self.billed_entity_account.billed_entity_id
            == other.billed_entity_account.billed_entity_id
            and self.billed_entity_account.account_number
            == other.billed_entity_account.account_number
        )

    def __hash__(self):
        return hash(
            str(self.billed_entity_account.billed_entity_id)
            + '_'
            + str(self.billed_entity_account.account_number)
        )
