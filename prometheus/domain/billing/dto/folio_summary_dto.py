from prometheus.domain.billing.dto.bill_summary_dto import BillSummaryDto
from ths_common.constants.billing_constants import ChargeStatus


class FolioSummaryDto:
    def __init__(
        self,
        bill_summary: BillSummaryDto,
        charges,
        payments,
        is_invoiced,
    ):
        self.bill_summary = bill_summary
        self.charges = charges
        self.payments = payments
        self.is_invoiced = is_invoiced


class ChargeSummaryDto:
    def __init__(
        self,
        charge_id,
        charge_split_id,
        status,
        billed_entity_account,
        posttax_amount,
        pretax_amount,
        tax_amount,
        tax_details,
        charge_type,
        allowances,
        item_name,
        sku_category_id,
        charge_to,
        inclusion_charge_ids,
        room_no,
        room_type,
        room_stay_id,
        applicable_date,
    ):
        self.charge_id = charge_id
        self.charge_split_id = charge_split_id
        self.status = status
        self.billed_entity_account = billed_entity_account
        self.posttax_amount = posttax_amount
        self.pretax_amount = pretax_amount
        self.tax_amount = tax_amount
        self.tax_details = tax_details
        self.charge_type = charge_type
        self.allowances = allowances
        self.item_name = item_name
        self.sku_category_id = sku_category_id
        self.charge_to = charge_to
        self.inclusion_charge_ids = inclusion_charge_ids
        self.room_no = room_no
        self.room_type = room_type
        self.room_stay_id = room_stay_id
        self.applicable_date = applicable_date

    @staticmethod
    def create_for_folio(
        charges,
        folio,
        booked_charges_to_be_cancelled=None,
        booked_allowances_to_be_cancelled=None,
    ):
        charges_for_folio = []
        for charge in charges:
            for charge_split in charge.charge_splits:
                if (
                    charge_split.billed_entity_account
                    == folio.get_billed_entity_account()
                ):
                    charge_data = ChargeSummaryDto(
                        charge_id=charge.charge_id,
                        charge_split_id=charge_split.charge_split_id,
                        status=charge.status,
                        billed_entity_account=folio.get_billed_entity_account(),
                        posttax_amount=charge_split.post_tax,
                        pretax_amount=charge_split.pre_tax,
                        tax_amount=charge_split.tax,
                        tax_details=charge_split.tax_details,
                        charge_type=charge_split.charge_type,
                        allowances=[
                            allowance
                            for allowance in charge_split.allowances
                            if allowance.billed_entity_account
                            == folio.get_billed_entity_account()
                        ],
                        item_name=charge.item.name,
                        sku_category_id=charge.item.sku_category_id,
                        charge_to=charge.charge_to,
                        inclusion_charge_ids=charge.addon_charge_ids,
                        room_no=charge.item.details.get('room_no'),
                        room_type=charge.item.details.get('room_type_code'),
                        room_stay_id=charge.item.details.get('room_stay_id'),
                        applicable_date=charge.applicable_date,
                    )
                    if (
                        booked_charges_to_be_cancelled
                        and charge.charge_id in booked_charges_to_be_cancelled
                    ):
                        charge_data.status = ChargeStatus.CANCELLED

                    if booked_allowances_to_be_cancelled:
                        for allowance in charge_data.allowances:
                            if allowance.allowance_id in [
                                a.get('allowance_id')
                                for a in booked_allowances_to_be_cancelled
                            ]:
                                allowance.cancel()
                    charges_for_folio.append(charge_data)
        return charges_for_folio


class PaymentSummaryDto:
    def __init__(
        self,
        payment_id,
        payment_split_id,
        amount,
        payment_type,
        payment_mode,
        billed_entity_account,
        date_of_payment,
        status,
        paid_to,
        payment_channel,
        payment_ref_id,
        paid_by,
        payment_mode_sub_type,
        payment_business_date,
        payor_billed_entity_id,
    ):
        self.payment_id = payment_id
        self.payment_split_id = payment_split_id
        self.amount = amount
        self.payment_type = payment_type
        self.payment_mode = payment_mode
        self.billed_entity_account = billed_entity_account
        self.date_of_payment = date_of_payment
        self.status = status
        self.paid_to = paid_to
        self.payment_channel = payment_channel
        self.payment_ref_id = payment_ref_id
        self.paid_by = paid_by
        self.payment_mode_sub_type = payment_mode_sub_type
        self.payment_business_date = payment_business_date
        self.payor_billed_entity_id = payor_billed_entity_id

    @staticmethod
    def create_for_folio(payments, folio):
        payments_for_folio = []
        for payment in payments:
            for payment_split in payment.payment_splits:
                if (
                    payment_split.billed_entity_account
                    == folio.get_billed_entity_account()
                ):
                    payment_data = PaymentSummaryDto(
                        payment_id=payment.payment_id,
                        payment_split_id=payment_split.payment_split_id,
                        amount=payment_split.amount,
                        payment_type=payment_split.payment_type,
                        payment_mode=payment_split.payment_mode,
                        billed_entity_account=folio.get_billed_entity_account(),
                        date_of_payment=payment.date_of_payment,
                        status=payment.status,
                        paid_to=payment.paid_to,
                        payment_channel=payment.payment_channel,
                        payment_ref_id=payment.payment_ref_id,
                        paid_by=payment.paid_by,
                        payment_mode_sub_type=payment.payment_mode_sub_type,
                        payment_business_date=payment.payment_business_date,
                        payor_billed_entity_id=payment.payor_billed_entity_id,
                    )
                    payments_for_folio.append(payment_data)
        return payments_for_folio
