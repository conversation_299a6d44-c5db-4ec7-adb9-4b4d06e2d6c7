from typing import List

from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO


class RefundSplitsDto(object):
    def __init__(self, refund_mode, amount, payment_id=None):
        self.refund_mode = refund_mode
        self.amount = amount
        self.payment_id = payment_id


class RefundSummaryDto(object):
    """
    RefundSummaryDto
    """

    def __init__(
        self,
        item_id,
        amount,
        refund_mode=None,
        requested_refund_mode=None,
    ):
        self.item_id = item_id
        self.amount = amount
        self.refund_mode = refund_mode
        self.requested_refund_mode = requested_refund_mode
        self.should_process_auto_refund = False
        self.refund_splits: List[RefundSplitsDto] = []

    def add_refund_splits(self, refund_mode, amount, payment_id=None):
        self.refund_mode = refund_mode
        self.should_process_auto_refund = True
        self.refund_splits.append(RefundSplitsDto(refund_mode, amount, payment_id))


class RefundSplitsFailureDto(object):
    def __init__(self, amount, error_message, refund_mode):
        self.amount = amount
        self.error_message = error_message
        self.refund_mode = refund_mode
