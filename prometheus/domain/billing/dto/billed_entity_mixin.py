class BilledEntityMixin:
    def set_billed_entity_names(self, billed_entity_map, billed_entity_accounts):
        if not billed_entity_accounts:
            return
        self.billed_entity_names = [
            billed_entity_map.get(
                billed_entity_account.billed_entity_id
            ).get_name_with_account_number(billed_entity_account.account_number)
            for billed_entity_account in billed_entity_accounts
        ]

    def set_billed_entity_names_using_folio(
        self, folio_name_map, billed_entity_accounts
    ):
        if not billed_entity_accounts:
            return
        self.billed_entity_names = [
            folio_name_map.get(billed_entity_account)
            for billed_entity_account in billed_entity_accounts
        ]
