import abc
from decimal import Decimal

from treebo_commons.money import Money

from prometheus.common.helpers.tax_utils import round_money
from ths_common.value_objects import TaxDetail


class BaseChargeData(abc.ABC):
    def __init__(
        self,
    ):
        self.pretax_amount = None
        self.tax_amount = None
        self.posttax_amount = None
        self.tax_details = None

    def apply_tax_from_room_charge(self, charge):
        room_charge_tax_details = charge.tax_details
        total_tax_percentage = sum(td.percentage for td in room_charge_tax_details)

        hotel_uses_post_tax_price = True
        if self.pretax_amount is not None:
            self.tax_amount = total_tax_percentage * self.pretax_amount / Decimal("100")
            self.posttax_amount = self.pretax_amount + self.tax_amount
            hotel_uses_post_tax_price = False
        else:
            self.pretax_amount = (
                self.posttax_amount
                * Decimal('100')
                / (Decimal("100") + total_tax_percentage)
            )
            self.tax_amount = self.posttax_amount - self.pretax_amount

        tax_details = []
        for tax_detail in room_charge_tax_details:
            tax_details.append(
                TaxDetail(
                    tax_type=tax_detail.tax_type,
                    percentage=tax_detail.percentage,
                    amount=round_money(
                        self.pretax_amount * tax_detail.percentage / Decimal("100")
                    ),
                )
            )

        # applying correction due to round: eg post tax 21 will have 1.12 amd 1.12 in cgst and sgst after rounding
        # but calculated tax will be 2.25. making tax sum of the tax components
        if tax_details:
            self.tax_amount = Money(
                amount=sum(tax.amount.amount for tax in tax_details),
                currency=self.posttax_amount.currency,
            )
            if hotel_uses_post_tax_price:
                self.pretax_amount = self.posttax_amount - self.tax_amount
            else:
                self.posttax_amount = self.pretax_amount + self.tax_amount

        self.tax_details = tax_details
