# coding=utf-8
"""
ChargeSplit Event data
"""


class ChargeSplitEventData(object):
    def __init__(
        self,
        charge_split_id,
        posttax_amount,
        percentage,
        charge_type,
        bill_to_type,
        billed_entity_account,
        invoice_id=None,
        invoiced_date=None,
    ):
        self.charge_split_id = charge_split_id
        self.posttax_amount = posttax_amount
        self.percentage = percentage
        self.charge_type = charge_type
        self.bill_to_type = bill_to_type
        self.billed_entity_account = billed_entity_account
        self.invoice_id = invoice_id
        self.invoiced_date = invoiced_date
        self.billed_entity_name = None

    def set_billed_entity_name(self, billed_entity_map=None, folio_name_map=None):
        if billed_entity_map:
            self.billed_entity_name = billed_entity_map.get(
                self.billed_entity_account.billed_entity_id
            ).get_name_with_account_number(self.billed_entity_account.account_number)
        elif folio_name_map:
            self.billed_entity_name = folio_name_map.get(self.billed_entity_account)


class ChargeSplitModifiedEventData(object):
    def __init__(
        self,
        charge_id,
        applicable_date,
        item_name,
        room_number,
        sku_category_id,
        status,
        old_splits,
        new_splits,
    ):
        self.charge_id = charge_id
        self.applicable_date = applicable_date
        self.item_name = item_name
        self.room_number = room_number
        self.sku_category_id = sku_category_id
        self.status = status
        self.sku_category = None
        self.old_splits = old_splits
        self.new_splits = new_splits

    def set_sku_category(self, sku_category_map):
        sku_category = sku_category_map.get(self.sku_category_id)
        assert (
            sku_category is not None
        ), f"sku category id {self.sku_category_id} missing in db"
        self.sku_category = sku_category.name
