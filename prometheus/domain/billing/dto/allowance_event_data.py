# coding=utf-8
"""
Allowance Domain Events
"""


class AllowanceEventData(object):
    def __init__(
        self,
        charge_id,
        charge_split_id,
        allowance_id,
        item_name,
        room_number,
        applicable_date,
        posttax_amount,
        remarks,
        billed_entity_account,
        posting_date=None,
        sku_category_id=None,
    ):
        self.charge_id = charge_id
        self.charge_split_id = charge_split_id
        self.sku_category_id = sku_category_id
        self.allowance_id = allowance_id
        self.item_name = item_name
        self.room_number = room_number
        self.applicable_date = applicable_date
        self.posttax_amount = posttax_amount
        self.billed_entity_account = billed_entity_account
        self.remarks = remarks
        self.posting_date = posting_date
        self.billed_entity_name = None
        self.sku_category = None

    def set_sku_category(self, sku_category_map):
        sku_category = sku_category_map.get(self.sku_category_id)
        assert (
            sku_category is not None
        ), f"sku category id {self.sku_category_id} missing in db"
        self.sku_category = sku_category.name
