from ths_common.constants.billing_constants import (
    CashierPaymentStatus,
    CashierPaymentTypes,
)


class CashierPaymentData(object):
    """
    Payment Data
    """

    def __init__(
        self,
        date_of_payment,
        payment_mode,
        payment_mode_sub_type,
        payment_type,
        payment_details,
        status,
        paid_to,
        payment_ref_id,
        amount,
        amount_in_payment_currency,
        booking_id,
        voucher_number,
        booking_owner_name=None,
        bill_id=None,
        bill_payment_id=None,
        comment=None,
        voucher_url=None,
        should_generate_voucher=False,
        booking_reference_number=None,
        transaction_id=None,
    ):
        self.date_of_payment = date_of_payment
        self.payment_mode = payment_mode
        self.payment_mode_sub_type = payment_mode_sub_type
        self.payment_type = payment_type
        self.payment_details = payment_details
        self.status = status
        self.paid_to = paid_to
        self.payment_ref_id = payment_ref_id
        self.comment = comment
        self.amount_in_payment_currency = amount_in_payment_currency
        self.amount = amount
        self.booking_id = booking_id
        self.booking_owner_name = booking_owner_name
        self.bill_id = bill_id
        self.bill_payment_id = bill_payment_id
        self.voucher_url = voucher_url
        self.should_generate_voucher = should_generate_voucher
        self.voucher_number = voucher_number
        self.booking_reference_number = booking_reference_number
        self.transaction_id = transaction_id

    @classmethod
    def from_dict(cls, payment_dict):
        payment_mode = payment_dict['payment_mode']
        payment_mode_sub_type = payment_dict.get('payment_mode_sub_type')
        return CashierPaymentData(
            date_of_payment=payment_dict['date_of_payment'],
            payment_mode=payment_mode,
            payment_mode_sub_type=payment_mode_sub_type,
            payment_type=CashierPaymentTypes(payment_dict['payment_type']),
            payment_details=payment_dict.get('payment_details'),
            status=CashierPaymentStatus(payment_dict['status']),
            paid_to=payment_dict['paid_to'],
            payment_ref_id=payment_dict.get('payment_ref_id'),
            comment=payment_dict.get('comment'),
            amount_in_payment_currency=payment_dict.get('amount_in_payment_currency'),
            amount=payment_dict.get('amount'),
            booking_id=payment_dict.get('booking_id'),
            booking_owner_name=payment_dict.get('booking_owner_name'),
            bill_id=payment_dict.get('bill_id'),
            bill_payment_id=payment_dict.get('bill_payment_id'),
            voucher_url=payment_dict.get('voucher_url'),
            should_generate_voucher=payment_dict.get('should_generate_voucher'),
            voucher_number=payment_dict.get('voucher_number'),
            booking_reference_number=payment_dict.get('booking_reference_number'),
            transaction_id=payment_dict.get('transaction_id'),
        )
