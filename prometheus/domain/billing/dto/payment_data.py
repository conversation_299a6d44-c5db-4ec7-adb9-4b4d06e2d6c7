from typing import List

from treebo_commons.money import Money

from prometheus import crs_context
from prometheus.domain.billing.dto.payment_split_data import PaymentSplitData
from prometheus.domain.billing.entities.charge_split import ChargeSplit
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.exceptions import BillingError
from ths_common.constants.billing_constants import (
    ChargeTypes,
    PaymentModes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.value_objects import (
    AttachmentDetails,
    PayoutContactDetails,
    PayoutDetails,
)


class PaymentData(object):
    """
    Payment Data
    """

    def __init__(
        self,
        amount,
        date_of_payment,
        payment_mode,
        payment_mode_sub_type,
        payment_type,
        payment_details,
        status,
        paid_to,
        payment_channel,
        payment_ref_id,
        paid_by,
        comment,
        amount_in_payment_currency,
        payer=None,
        payment_splits: List[PaymentSplitData] = None,
        confirmed=True,
        show_receipt=False,
        payment_id=None,
        invoice_group_id=None,
        payor_billed_entity_id=None,
        refund_reason=None,
        payout_details=None,
        payout_contact_details=None,
        attachment_details=None,
        auto_refund_enabled=True,
    ):
        self.amount = amount
        self.date_of_payment = date_of_payment
        self.payment_mode = payment_mode
        self.payment_mode_sub_type = payment_mode_sub_type
        self.payment_type = payment_type
        self.payment_details = payment_details
        self.status = status
        self.paid_to = paid_to
        self.payment_channel = payment_channel
        self.payment_ref_id = payment_ref_id
        self.paid_by = paid_by
        self.comment = comment
        self.amount_in_payment_currency = amount_in_payment_currency
        self.payer = payer
        self.payment_splits = payment_splits
        self.confirmed = confirmed
        self.show_receipt = show_receipt
        self.payment_id = payment_id
        self.invoice_group_id = invoice_group_id
        self.payor_billed_entity_id = payor_billed_entity_id
        self.refund_reason = refund_reason
        self.payout_details = payout_details
        self.payout_contact_details = payout_contact_details
        self.attachment_details = attachment_details
        self.auto_refund_enabled = auto_refund_enabled

    def get_billed_entity_account_type(self):
        if self.payment_type in {PaymentTypes.PAYMENT, PaymentTypes.REFUND}:
            return ChargeTypes.NON_CREDIT
        else:
            return ChargeTypes.CREDIT

    def set_currency_in_amount_if_missing(self):
        if self.amount:
            if crs_context.hotel_context and not self.amount.currency:
                self.amount = Money(
                    self.amount.amount, crs_context.hotel_context.base_currency
                )

        if self.payment_splits:
            for payment_split in self.payment_splits:
                if crs_context.hotel_context and not payment_split.amount.currency:
                    payment_split.amount = Money(
                        payment_split.amount.amount,
                        crs_context.hotel_context.base_currency,
                    )

    def is_voucher_payment(self):
        if (
            self.payment_mode.lower() == PaymentModes.VOUCHER.lower()
            and self.payment_type == PaymentTypes.PAYMENT
        ):
            return True
        return False

    @property
    def paid_by_billed_entity_id(self):
        if self.payor_billed_entity_id:
            return self.payor_billed_entity_id
        if self.payment_splits:
            return self.payment_splits[0].billed_entity_account.billed_entity_id
        return None

    @classmethod
    def from_dict(self, payment_dict):
        payment_amount_in_base_currency = payment_dict.get('amount')
        if crs_context.hotel_context and not payment_amount_in_base_currency.currency:
            payment_amount_in_base_currency = Money(
                payment_amount_in_base_currency.amount,
                crs_context.hotel_context.base_currency,
            )
        return PaymentData(
            payment_id=payment_dict.get('payment_id'),
            amount=payment_amount_in_base_currency,
            date_of_payment=payment_dict.get('date_of_payment', None),
            payment_mode=payment_dict.get('payment_mode'),
            payment_mode_sub_type=payment_dict.get('payment_mode_sub_type'),
            payment_type=PaymentTypes(payment_dict['payment_type'])
            if payment_dict.get('payment_type')
            else None,
            payment_details=payment_dict.get('payment_details'),
            status=PaymentStatus(payment_dict['status'])
            if payment_dict.get('status')
            else None,
            paid_to=payment_dict.get('paid_to'),
            payment_channel=payment_dict.get('payment_channel'),
            payment_ref_id=payment_dict.get('payment_ref_id'),
            paid_by=payment_dict.get('paid_by'),
            comment=payment_dict.get('comment'),
            amount_in_payment_currency=payment_dict.get('amount_in_payment_currency'),
            payer=payment_dict.get('payer'),
            confirmed=payment_dict.get('confirmed'),
            payment_splits=[
                PaymentSplitData.from_dict(split_data)
                for split_data in payment_dict.get('payment_splits', [])
            ],
            show_receipt=payment_dict.get('show_receipt'),
            invoice_group_id=payment_dict.get('invoice_group_id'),
            payor_billed_entity_id=payment_dict.get('payor_billed_entity_id'),
            refund_reason=payment_dict.get('refund_reason'),
            payout_details=PayoutDetails.from_json(payment_dict["payout_details"])
            if payment_dict.get("payout_details")
            else None,
            payout_contact_details=PayoutContactDetails.from_json(
                payment_dict["payout_contact_details"]
            )
            if payment_dict.get("payout_contact_details")
            else None,
            attachment_details=AttachmentDetails.from_json(
                payment_dict["attachment_details"]
            )
            if payment_dict.get("attachment_details")
            else None,
            auto_refund_enabled=payment_dict['auto_refund_enabled']
            if payment_dict.get('auto_refund_enabled') is not None
            else True,
        )

    @staticmethod
    def for_credit_offer_from_split(
        payment_date, charge_split: ChargeSplit, posttax_amount_post_allowance
    ):
        return PaymentData(
            amount=posttax_amount_post_allowance,
            date_of_payment=payment_date,
            payment_mode=PaymentModes.OTHER,
            payment_mode_sub_type=None,
            payment_type=PaymentTypes.CREDIT_OFFERED,
            payment_details=dict(),
            status=PaymentStatus.DONE,
            confirmed=True,
            paid_to='hotel',
            payment_channel=None,
            payment_ref_id=None,
            paid_by='guest',
            comment=None,
            amount_in_payment_currency=posttax_amount_post_allowance,
            payment_splits=[
                PaymentSplitData(
                    billed_entity_account=charge_split.billed_entity_account,
                    amount=charge_split.post_tax,
                )
            ],
        )

    def set_payor_billed_entity_id_if_missing(self):
        if self.payor_billed_entity_id:
            return

        if self.payment_splits:
            # Setting the payor_billed_entity_id as the billed_entity for the payment split.
            self.payor_billed_entity_id = self.payment_splits[
                0
            ].billed_entity_account.billed_entity_id
        else:
            raise BillingError(error=BillingErrors.PAYOR_DETAILS_MISSING)


class PaymentDetailsDto:
    def __init__(self, payment_details):
        self.bill_id = payment_details.bill_id
        self.amount = payment_details.amount
        self.payment_ref_id = payment_details.payment_ref_id
        self.payment_mode = payment_details.payment_mode
        self.payment_type = payment_details.payment_type
        self.payment_channel = payment_details.payment_channel
