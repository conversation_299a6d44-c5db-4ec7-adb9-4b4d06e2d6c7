from decimal import Decimal
from typing import List

from treebo_commons.money.money import Money

from prometheus.domain.billing.entities.invoice_charge import InvoiceCharge


class CreditNoteLineItemData(object):
    def __init__(self, invoice_id, invoice_charge_id, posttax_amount):
        self.invoice_id = invoice_id
        self.invoice_charge_id = invoice_charge_id
        self.posttax_amount = posttax_amount
        self.pretax_amount = None
        self.tax_amount = None
        self.tax_details = None
        self._applicable_date = None

    @property
    def applicable_date(self):
        return self._applicable_date

    def _base_currency(self):
        return self.posttax_amount.currency

    @applicable_date.setter
    def applicable_date(self, value):
        self._applicable_date = value

    def update_tax_details(self, invoice_charge: InvoiceCharge):
        if invoice_charge.posttax_amount.amount != Decimal("0"):
            self.pretax_amount = Money(
                self.posttax_amount.amount
                / invoice_charge.posttax_amount.amount
                * invoice_charge.pretax_amount.amount,
                self._base_currency(),
            )
        else:
            self.pretax_amount = Money("0", self._base_currency())
        self.tax_amount = self.posttax_amount - self.pretax_amount

        tax_details = dict()
        for tax_detail in invoice_charge.tax_details:
            if invoice_charge.tax_amount.amount == Decimal("0"):
                amount = Money("0", self._base_currency())
            else:
                amount = Money(
                    self.tax_amount.amount
                    / invoice_charge.tax_amount.amount
                    * tax_detail.amount.amount,
                    self._base_currency(),
                )
            tax_details[tax_detail.tax_type] = dict(
                percentage=tax_detail.percentage, amount=amount
            )
        self.tax_details = tax_details


class CreditNoteData(object):
    def __init__(self, credit_note_line_items: List[CreditNoteLineItemData]):
        self.credit_note_line_items = credit_note_line_items
