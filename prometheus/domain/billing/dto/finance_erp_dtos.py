from treebo_commons.utils import dateutils

from prometheus.reporting.finance_erp_reporting.constants import CS_REFUND


class PaymentChargeDetailsDto:
    def __init__(self, payment_charge_details):
        self.posted_date = payment_charge_details.posted_date
        self.created_date = payment_charge_details.created_at
        self.bill_id = payment_charge_details.bill_id
        self.payment_type = payment_charge_details.payment_type
        self.payment_channel = payment_charge_details.payment_channel
        self.amount = payment_charge_details.amount
        self.payment_mode = payment_charge_details.payment_mode
        self.payment_mode_sub_type = payment_charge_details.payment_mode_sub_type
        self.payment_id = payment_charge_details.payment_id
        self.payment_split_id = payment_charge_details.payment_split_id
        self.payment_ref_id = payment_charge_details.payment_ref_id
        self.payor_billed_entity_id = payment_charge_details.payor_billed_entity_id
        self.paid_by = payment_charge_details.paid_by
        self.paid_to = payment_charge_details.paid_to
        self.refund_reason = payment_charge_details.refund_reason
        self.billed_entity_id = payment_charge_details.billed_entity_id
        self.billed_entity_account_number = (
            payment_charge_details.billed_entity_account_number
        )
        self.total_charges = payment_charge_details.total_charges
        self.total_allowances = payment_charge_details.total_allowances


class InvoiceDetailsDto:
    def __init__(self, invoice_details):
        self.bill_id = invoice_details.bill_id
        self.billed_entity_id = invoice_details.billed_entity_id
        self.billed_entity_account_number = invoice_details.billed_entity_account_number
        self.invoice_id = invoice_details.invoice_id


class BilledEntityDetailsDto:
    def __init__(self, billed_entity_details):
        self.bill_id = billed_entity_details.bill_id
        self.billed_entity_id = billed_entity_details.billed_entity_id
        self.category = billed_entity_details.category
        self.first_name = billed_entity_details.first_name
        self.last_name = billed_entity_details.last_name


class CreditShellRefundChargeDetailsDto:
    def __init__(self, cs_refund_charge_details):
        self.posted_date = cs_refund_charge_details.created_at
        self.created_date = cs_refund_charge_details.created_at
        self.bill_id = cs_refund_charge_details.bill_id
        self.payment_type = CS_REFUND
        self.amount = cs_refund_charge_details.amount
        self.payment_mode = cs_refund_charge_details.payment_mode
        self.payment_mode_sub_type = None
        self.credit_shell_refund_id = cs_refund_charge_details.credit_shell_refund_id
        self.payment_ref_id = cs_refund_charge_details.payment_ref_id
        self.payor_billed_entity_id = cs_refund_charge_details.billed_entity_id
        self.paid_by = cs_refund_charge_details.paid_by
        self.paid_to = cs_refund_charge_details.paid_to
        self.billed_entity_id = cs_refund_charge_details.billed_entity_id
        self.billed_entity_account_number = None
        self.refund_reason = None
        self.total_charges = cs_refund_charge_details.total_charges
        self.total_allowances = cs_refund_charge_details.total_allowances
