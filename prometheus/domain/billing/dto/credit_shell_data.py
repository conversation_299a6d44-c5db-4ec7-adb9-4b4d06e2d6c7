from treebo_commons.money import Money


class CreditShellData:
    def __init__(
        self,
        bill_id,
        billed_entity_id,
        issue_date,
        credit_note_id,
        amount: Money,
        paid_by=None,
        paid_to=None,
        source_billed_entity_id=None,
        source_billed_entity_account_number=None,
        is_refundable_folio=None,
    ):
        self.bill_id = bill_id
        self.billed_entity_id = billed_entity_id
        self.issue_date = issue_date
        self.amount = amount
        self.credit_note_id = credit_note_id
        self.paid_by = paid_by
        self.paid_to = paid_to
        self.source_billed_entity_id = source_billed_entity_id
        self.source_billed_entity_account_number = source_billed_entity_account_number
        self.is_refundable_folio = is_refundable_folio


class ConsumeCreditShellData:
    def __init__(
        self, debit_amount, credit_shell_id, billed_entity_id=None, bill_id=None
    ):
        self.bill_id = bill_id
        self.debit_amount = debit_amount
        self.credit_shell_id = credit_shell_id
        self.billed_entity_id = billed_entity_id


class AddBalanceInCreditShellData:
    def __init__(self, credit_amount, credit_shell_id):
        self.credit_amount = credit_amount
        self.credit_shell_id = credit_shell_id


class CreditShellConsumedEventData:
    def __init__(
        self,
        bill_id,
        credit_shell_id,
        total_credit,
        remaining_credit,
        used_credit,
        folio_number,
        owner_name,
    ):
        self.bill_id = bill_id
        self.credit_shell_id = credit_shell_id
        self.total_credit = total_credit
        self.remaining_credit = remaining_credit
        self.used_credit = used_credit
        self.folio_number = folio_number
        self.owner_name = owner_name


class CreditShellCreatedEventData:
    def __init__(self, bill_id, credit_shell_id, total_credit, owner_name):
        self.bill_id = bill_id
        self.credit_shell_id = credit_shell_id
        self.total_credit = total_credit
        self.owner_name = owner_name


class CreditShellRedeemedEventData:
    def __init__(
        self,
        bill_id,
        booking_id,
        credit_shell_id,
        amount,
        credit_shell_refund_id,
        payment_mode,
        remarks,
        owner_name,
    ):
        self.bill_id = bill_id
        self.booking_id = booking_id
        self.credit_shell_id = credit_shell_id
        self.amount = amount
        self.credit_shell_refund_id = credit_shell_refund_id
        self.payment_mode = payment_mode
        self.remarks = remarks
        self.owner_name = owner_name
