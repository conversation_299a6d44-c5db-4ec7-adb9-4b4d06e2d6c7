from prometheus.domain.billing.entities.cash_register import CashRegister
from ths_common.value_objects import CashCounterAmount


class CashRegisterAggregate(object):
    def __init__(self, cash_register: CashRegister):
        self.cash_register = cash_register

    def update_default_opening_balance(
        self, default_opening_balance: CashCounterAmount
    ):
        self.cash_register.default_opening_balance = default_opening_balance

    def update_carry_balance_to_next_session(self, carry_balance_to_next_session: bool):
        self.cash_register.carry_balance_to_next_session = carry_balance_to_next_session

    def update_opened_by(self, user):
        self.cash_register.opened_by = user

    def update_lastest_voucher_sequence_number(self, lastest_voucher_sequence_number):
        self.cash_register.lastest_voucher_sequence_number = (
            lastest_voucher_sequence_number
        )
