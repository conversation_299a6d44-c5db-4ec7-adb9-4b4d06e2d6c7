from prometheus.domain.billing.entities.currency_exchange import CurrencyExchange


class CurrencyExchangeAggregate:
    def __init__(self, currency_exchange: CurrencyExchange):
        self.currency_exchange = currency_exchange

    def update_encashment_certificate_url(self, encashment_certificate_url):
        self.currency_exchange.encashment_certificate_url = encashment_certificate_url

    @property
    def currency_exchange_id(self):
        return self.currency_exchange.currency_exchange_id

    @property
    def currency_seller_detail(self):
        return self.currency_exchange.currency_seller_detail

    @property
    def amount_in_foreign_currency(self):
        return self.currency_exchange.amount_in_foreign_currency

    @property
    def foreign_currency_payment_mode(self):
        return self.currency_exchange.foreign_currency_payment_mode

    @property
    def amount_in_base_currency(self):
        return self.currency_exchange.amount_in_base_currency

    @property
    def taxable_amount(self):
        return self.currency_exchange.taxable_amount

    @property
    def tax_amount(self):
        return self.currency_exchange.tax_amount

    @property
    def tax_details(self):
        return self.currency_exchange.tax_details

    @property
    def total_payable_in_base_currency(self):
        return self.currency_exchange.total_payable_in_base_currency

    @property
    def exchange_rate(self):
        return self.currency_exchange.exchange_rate

    @property
    def transaction_date(self):
        return self.currency_exchange.transaction_date

    @property
    def transaction_id(self):
        return self.currency_exchange.transaction_id

    @property
    def certificate_number(self):
        return self.currency_exchange.certificate_number

    @property
    def encashment_certificate_url(self):
        return self.currency_exchange.encashment_certificate_url

    @property
    def remarks(self):
        return self.currency_exchange.remarks

    @property
    def created_at(self):
        return self.currency_exchange.created_at

    @property
    def modified_at(self):
        return self.currency_exchange.modified_at
