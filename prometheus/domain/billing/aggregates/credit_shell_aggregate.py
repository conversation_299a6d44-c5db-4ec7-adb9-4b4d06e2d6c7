from treebo_commons.request_tracing.context import get_current_request_id
from treebo_commons.utils import dateutils

from prometheus.domain.billing.entities.credit_shell import CreditShell
from prometheus.domain.billing.entities.credit_shell_audit_trail import (
    CreditShellAuditTrail,
)


class CreditShellAggregate(object):
    def __init__(
        self,
        credit_shell: CreditShell,
        credit_shell_audit_trails: [CreditShellAuditTrail],
    ):
        self.credit_shell = credit_shell
        self.credit_shell_audit_trails = credit_shell_audit_trails or []

    @property
    def bill_id(self):
        return self.credit_shell.bill_id

    @property
    def credit_shell_id(self):
        return self.credit_shell.credit_shell_id

    def increment_version(self):
        self.credit_shell.version += 1

    def consume_credit_shell(
        self, debit_amount, target_booking_reference_number, remarks=None
    ):
        self.credit_shell.consume_credit_shell(amount=debit_amount)
        self.credit_shell_audit_trails.append(
            CreditShellAuditTrail(
                target_booking_reference_number,
                self.credit_shell_id,
                'debit',
                debit_amount,
                dateutils.current_datetime(),
                self.credit_shell.remaining_credit,
                get_current_request_id(),
                remarks=remarks,
            )
        )

    def add_balance_in_credit_shell(
        self, balance_amount, target_booking_reference_number
    ):
        self.credit_shell.add_balance(amount=balance_amount)
        self.credit_shell_audit_trails.append(
            CreditShellAuditTrail(
                target_booking_reference_number,
                self.credit_shell_id,
                'credit',
                balance_amount,
                dateutils.current_datetime(),
                self.credit_shell.remaining_credit,
                get_current_request_id(),
            )
        )

    def update_paid_by_and_paid_to(self, paid_by, paid_to):
        self.credit_shell.update_paid_by_and_paid_to(paid_by=paid_by, paid_to=paid_to)
