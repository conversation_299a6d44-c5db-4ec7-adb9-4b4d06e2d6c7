from typing import List

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from prometheus.domain.billing.dto.cash_counter_payment_data import CashierPaymentData
from prometheus.domain.billing.dto.cashier_session_summary import (
    CashierSessionSummaryData,
)
from prometheus.domain.billing.entities.cash_counter_amount import CashCounterBalance
from prometheus.domain.billing.entities.cash_counter_payment import CashierPayment
from prometheus.domain.billing.entities.cashier_session import CashierSession
from ths_common.constants.billing_constants import (
    CashierBalanceType,
    CashierPaymentTypes,
    CashierSessionStatus,
)
from ths_common.exceptions import NoOpenCashierSessionException, ResourceNotFound
from ths_common.utils.id_generator_utils import random_id_generator
from ths_common.value_objects import CashCounterAmount, UserData


class CashierSessionAggregate(object):
    """
    CashierSessionAggregate Aggregate
    Root entity: CashierSession
    """

    def __init__(
        self,
        cashier_session: CashierSession,
        opening_balance: List[CashCounterBalance],
        closing_balance: List[CashCounterBalance],
        cash_counter_payments: List[CashierPayment],
    ):
        self.cashier_session = cashier_session
        self._cashier_payments = cash_counter_payments if cash_counter_payments else []
        self._opening_balance = opening_balance
        self._closing_balance = closing_balance if closing_balance else []
        self.payment_dict = {p.payment_id: p for p in self._cashier_payments}
        self._session_summary = None

    @property
    def payments(self):
        return [payment for payment in self._cashier_payments if not payment.deleted]

    @property
    def non_cancelled_payments(self):
        return [
            payment
            for payment in self._cashier_payments
            if not payment.deleted and not payment.is_cancelled()
        ]

    @property
    def opening_balance(self):
        return [
            currency_wise_amount
            for currency_wise_amount in self._opening_balance
            if not currency_wise_amount.deleted
        ]

    @property
    def closing_balance(self):
        return [
            currency_wise_amount
            for currency_wise_amount in self._closing_balance
            if not currency_wise_amount.deleted
        ]

    @property
    def opening_balance_amount(self):
        return CashCounterAmount(
            amounts=[
                Money(currency_wise_amount.amount, currency_wise_amount.currency)
                for currency_wise_amount in self.opening_balance
            ]
        )

    @property
    def closing_balance_amount(self):
        return CashCounterAmount(
            amounts=[
                Money(currency_wise_amount.amount, currency_wise_amount.currency)
                for currency_wise_amount in self.closing_balance
            ]
        )

    def mark_session_as_closed(self):
        self.cashier_session.status = CashierSessionStatus.CLOSED

    def check_invariance(self):
        pass

    def inflow_amount_in_base_currency(self):
        return sum(
            [
                payment.amount
                for payment in self.payments
                if payment
                if payment.payment_type == CashierPaymentTypes.INFLOW
                and payment.is_done()
            ]
        )

    def outflow_amount_in_base_currency(self):
        return sum(
            [
                payment.amount
                for payment in self.payments
                if payment
                if payment.payment_type == CashierPaymentTypes.OUTFLOW
                and payment.is_done()
            ]
        )

    def inflow_cash_amount_in_base_currency(self):
        return sum(
            [
                payment.amount
                for payment in self.payments
                if payment
                if payment.payment_type == CashierPaymentTypes.INFLOW
                and payment.is_done()
                and payment.is_cash_payment()
            ]
        )

    def outflow_cash_amount_in_base_currency(self):
        return sum(
            [
                payment.amount
                for payment in self.payments
                if payment
                if payment.payment_type == CashierPaymentTypes.OUTFLOW
                and payment.is_done()
                and payment.is_cash_payment()
            ]
        )

    def inflow_non_cash_amount_in_base_currency(self):
        return sum(
            [
                payment.amount
                for payment in self.payments
                if payment
                if payment.payment_type == CashierPaymentTypes.INFLOW
                and payment.is_done()
                and payment.is_non_cash_payment()
            ]
        )

    def outflow_non_cash_amount_in_base_currency(self):
        return sum(
            [
                payment.amount
                for payment in self.payments
                if payment
                if payment.payment_type == CashierPaymentTypes.OUTFLOW
                and payment.is_done()
                and payment.is_non_cash_payment()
            ]
        )

    def current_balance_in_base_currency(self):
        inflow_amount = (
            self.cashier_session.opening_balance_in_base_currency
            + self.inflow_amount_in_base_currency()
        )
        return inflow_amount - self.outflow_amount_in_base_currency()

    def current_cash_balance_in_base_currency(self):
        inflow_amount = (
            self.cashier_session.opening_balance_in_base_currency
            + self.inflow_cash_amount_in_base_currency()
        )
        if not inflow_amount:
            return -self.outflow_cash_amount_in_base_currency()
        return inflow_amount - self.outflow_cash_amount_in_base_currency()

    def current_non_cash_balance_in_base_currency(self):
        inflow_amount = self.inflow_non_cash_amount_in_base_currency()
        if not inflow_amount:
            return -self.outflow_non_cash_amount_in_base_currency()
        return inflow_amount - self.outflow_non_cash_amount_in_base_currency()

    def current_session_balance(self):
        currency_wise_payment_amount = dict()
        for payment in self.payments:
            if payment.is_cancelled():
                continue
            factor = 1 if payment.payment_type == CashierPaymentTypes.INFLOW else -1
            if not currency_wise_payment_amount.get(
                payment.amount_in_payment_currency.currency
            ):
                currency_wise_payment_amount[
                    payment.amount_in_payment_currency.currency
                ] = (payment.amount_in_payment_currency * factor)
            else:
                currency_wise_payment_amount[
                    payment.amount_in_payment_currency.currency
                ] += (payment.amount_in_payment_currency * factor)
        for currency_wise_amount in self.opening_balance_amount.amounts:
            if not currency_wise_payment_amount.get(currency_wise_amount.currency):
                currency_wise_payment_amount[
                    currency_wise_amount.currency
                ] = currency_wise_amount
            else:
                currency_wise_payment_amount[
                    currency_wise_amount.currency
                ] += currency_wise_amount

        return CashCounterAmount(amounts=list(currency_wise_payment_amount.values()))

    def add_opening_balance(self, opening_balance: CashCounterAmount):
        all_balances = self._opening_balance + self._closing_balance
        next_balance_id = (
            max(
                [
                    opening_balance.cash_counter_balance_id
                    for opening_balance in self._opening_balance
                ]
            )
            if all_balances
            else 0
        )
        for opening_balance_amount in opening_balance.amounts:
            next_balance_id += 1
            self._opening_balance.append(
                CashCounterBalance(
                    cash_counter_balance_id=next_balance_id,
                    amount=opening_balance_amount.amount,
                    currency=opening_balance_amount.currency,
                    balance_type=CashierBalanceType.OPENING,
                )
            )

    def add_closing_balance(self, closing_balance: CashCounterAmount):
        all_balances = self._opening_balance + self._closing_balance
        next_balance_id = (
            max(
                [
                    opening_balance.cash_counter_balance_id
                    for opening_balance in self._opening_balance
                ]
            )
            if all_balances
            else 0
        )
        for closing_balance_amount in closing_balance.amounts:
            next_balance_id += 1
            self._closing_balance.append(
                CashCounterBalance(
                    cash_counter_balance_id=next_balance_id,
                    amount=closing_balance_amount.amount,
                    currency=closing_balance_amount.currency,
                    balance_type=CashierBalanceType.CLOSING,
                )
            )

    def add_payments(self, payment_dtos, user_data: UserData):
        if self.cashier_session.is_closed():
            raise NoOpenCashierSessionException()
        next_payment_id = (
            max([payment.payment_id for payment in self.payments])
            if self._cashier_payments
            else 0
        )
        current_session_balance = self.current_balance_in_base_currency()

        payments = []
        for payment_dto in payment_dtos:
            next_payment_id += 1
            if payment_dto.payment_type == CashierPaymentTypes.INFLOW:
                if current_session_balance == 0:
                    new_session_balance = payment_dto.amount
                else:
                    new_session_balance = current_session_balance + payment_dto.amount
            else:
                if current_session_balance == 0:
                    new_session_balance = -payment_dto.amount
                else:
                    new_session_balance = current_session_balance - payment_dto.amount

            transaction_id = payment_dto.transaction_id
            if not payment_dto.transaction_id:
                transaction_id = random_id_generator()
            payment = CashierPayment(
                payment_id=next_payment_id,
                date_of_payment=payment_dto.date_of_payment,
                payment_mode=payment_dto.payment_mode,
                payment_type=payment_dto.payment_type,
                status=payment_dto.status,
                paid_to=payment_dto.paid_to,
                added_by=user_data.user,
                comment=payment_dto.comment,
                amount_in_payment_currency=payment_dto.amount_in_payment_currency,
                amount=payment_dto.amount,
                booking_id=payment_dto.booking_id,
                payment_mode_sub_type=payment_dto.payment_mode_sub_type,
                payment_details=payment_dto.payment_details,
                current_session_balance=new_session_balance,
                booking_owner_name=payment_dto.booking_owner_name,
                bill_id=payment_dto.bill_id,
                transaction_id=transaction_id,
                bill_payment_id=payment_dto.bill_payment_id,
                voucher_url=payment_dto.voucher_url,
                voucher_number=payment_dto.voucher_number,
                booking_reference_number=payment_dto.booking_reference_number,
            )
            payments.append(payment)

        self._cashier_payments.extend(payments)
        return payments

    def update_payment(self, payment_id, payment_dto: CashierPaymentData):
        payment = self.get_cashier_payment(payment_id)
        if payment_dto.payment_mode:
            payment.payment_mode = payment_dto.payment_mode
        if payment_dto.status:
            payment.status = payment_dto.status
        if payment_dto.paid_to:
            payment.paid_to = payment_dto.paid_to
        if payment_dto.comment:
            payment.comment = payment_dto.comment
        if payment_dto.amount_in_payment_currency is not None:
            payment._amount_in_payment_currency = payment_dto.amount_in_payment_currency
        if payment_dto.amount is not None:
            payment.amount = payment_dto.amount
        if payment_dto.payment_mode_sub_type:
            payment.payment_mode_sub_type = payment_dto.payment_mode_sub_type
        if payment_dto.payment_details:
            payment.payment_details = payment_dto.payment_details
        return payment

    def cash_inflow_transactions(self):
        return sum(
            [
                payment.amount
                for payment in self.payments
                if not payment.is_cancelled()
                and payment.is_cash_payment()
                and payment.is_inflow_amount()
            ]
        )

    def cash_outflow_transactions(self):
        return sum(
            [
                payment.amount
                for payment in self.payments
                if not payment.is_cancelled()
                and payment.is_cash_payment()
                and payment.is_outflow_amount()
            ]
        )

    def non_cash_inflow_transactions(self):
        return sum(
            [
                payment.amount
                for payment in self.payments
                if not payment.is_cancelled()
                and payment.is_non_cash_payment()
                and payment.is_inflow_amount()
            ]
        )

    def non_cash_outflow_transactions(self):
        return sum(
            [
                payment.amount
                for payment in self.payments
                if not payment.is_cancelled()
                and payment.is_non_cash_payment()
                and payment.is_outflow_amount()
            ]
        )

    def get_cashier_payment(self, payment_id) -> CashierPayment:
        payment = self.payment_dict.get(payment_id)
        if not payment or payment.deleted:
            raise ResourceNotFound(
                "CashierPayment",
                description="CashierSessionAggregate:Payment not found in {}:{}".format(
                    self.cashier_session.cashier_session_id, payment_id
                ),
            )
        return payment

    def update_closing_balance_in_base_currency(self):
        self.cashier_session.closing_balance_in_base_currency = (
            self.current_balance_in_base_currency()
        )

    def update_closing_balance(self):
        self.add_closing_balance(self.current_session_balance())

    def reset_closing_balance(self):
        for currency_wise_amount in self._closing_balance:
            currency_wise_amount.deleted = True

    def update_session_end_time(self):
        self.cashier_session.end_datetime = dateutils.current_datetime()

    def update_session_closed_by(self, user):
        self.cashier_session.closed_by = user

    def update_session_opened_by(self, user):
        self.cashier_session.opened_by = user

    def reset_session_closed_by(self):
        self.cashier_session.closed_by = None

    def reset_session_end_time(self):
        self.cashier_session.end_datetime = None

    def reset_closing_balance_in_base_currency(self):
        self.cashier_session.closing_balance_in_base_currency = None

    @property
    def session_summary(self):
        return self._session_summary

    def set_session_summary(self, base_currency):
        cash_transaction_data = CashierSessionSummaryData(
            current_balance=self.current_cash_balance_in_base_currency()
            if self.current_cash_balance_in_base_currency() != 0
            else Money(0, base_currency),
            opening_balance=self.opening_balance_amount,
            opening_balance_in_base_currency=self.cashier_session.opening_balance_in_base_currency,
            inflow_amount=self.cash_inflow_transactions()
            if self.cash_inflow_transactions() != 0
            else Money(0, base_currency),
            outflow_amount=self.cash_outflow_transactions()
            if self.cash_outflow_transactions() != 0
            else Money(0, base_currency),
            transaction_type='cash',
        )
        non_cash_transaction_data = CashierSessionSummaryData(
            current_balance=self.current_non_cash_balance_in_base_currency()
            if self.current_non_cash_balance_in_base_currency()
            else Money(0, base_currency),
            opening_balance=CashCounterAmount([Money(0, base_currency)]),
            opening_balance_in_base_currency=Money(0, base_currency),
            inflow_amount=self.non_cash_inflow_transactions()
            if self.non_cash_inflow_transactions() != 0
            else Money(0, base_currency),
            outflow_amount=self.non_cash_outflow_transactions()
            if self.non_cash_outflow_transactions() != 0
            else Money(0, base_currency),
            transaction_type='non_cash',
        )
        self._session_summary = [cash_transaction_data, non_cash_transaction_data]
