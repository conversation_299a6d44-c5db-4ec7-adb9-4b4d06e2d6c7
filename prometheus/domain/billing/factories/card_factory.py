import uuid

from prometheus.domain.billing.aggregates.card_aggregate import CardAggregate
from prometheus.domain.billing.entities.card import Card
from ths_common.utils import id_generator_utils


class CardFactory(object):
    @staticmethod
    def create_card(bill_id, card_data: dict):
        card_id = uuid.uuid1().hex
        card_entity = Card(
            card_id=card_id,
            bill_id=bill_id,
            holder_name=card_data.get('holder_name'),
            expiry=card_data.get('expiry'),
            card_type=card_data.get('card_type'),
            token=card_data.get('token'),
            last_digits=card_data.get('last_digits'),
            bin=card_data.get('bin'),
            brand=card_data.get('brand'),
            pre_auth_amount=card_data.get('pre_auth_amount'),
            pre_auth_code=card_data.get('pre_auth_code'),
            billed_entity_id=card_data.get('billed_entity_id'),
        )
        return CardAggregate(card_entity=card_entity)
