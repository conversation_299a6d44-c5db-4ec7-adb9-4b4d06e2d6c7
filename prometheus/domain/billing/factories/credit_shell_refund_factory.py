import random
import re

from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.billing.aggregates.credit_shell_refund_aggregate import (
    CreditShellRefundAggregate,
)
from prometheus.domain.billing.dto.credit_shell_refund_data import CreditShellRefundData
from prometheus.domain.billing.entities.credit_shell_refund import CreditShellRefund
from ths_common.utils.id_generator_utils import generate_short_numerical_random_id


def generate_credit_shell_refund_id(input_var):
    hotel_context = crs_context.get_hotel_context()
    return re.sub(
        '[^A-Za-z0-9]+',
        '',
        (hotel_context.hotel_id + dateutils.current_date().isoformat() + input_var[-4:])
        + str(random.randint(0, 999)),
    )


class CreditShellRefundFactory(object):
    @staticmethod
    def create_credit_shell_refund(
        credit_shell_refund_data: CreditShellRefundData,
    ):
        credit_shell_refund_id = generate_short_numerical_random_id(
            length=12, prefix='CSR'
        )
        credit_shell_refund = CreditShellRefund(
            amount=credit_shell_refund_data.amount,
            bill_id=credit_shell_refund_data.bill_id,
            booking_id=credit_shell_refund_data.booking_id,
            credit_shell_id=credit_shell_refund_data.credit_shell_id,
            credit_shell_refund_id=credit_shell_refund_id,
            hotel_id=credit_shell_refund_data.hotel_id,
            paid_to=credit_shell_refund_data.paid_to,
            paid_by=credit_shell_refund_data.paid_by,
            payment_mode=credit_shell_refund_data.payment_mode,
            payout_details=credit_shell_refund_data.payout_details,
            payment_ref_id=credit_shell_refund_data.payment_ref_id,
            remarks=credit_shell_refund_data.remarks,
        )

        credit_shell_refund_aggregate = CreditShellRefundAggregate(
            credit_shell_refund=credit_shell_refund,
        )
        return credit_shell_refund_aggregate
