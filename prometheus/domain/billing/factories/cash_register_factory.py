# coding=utf-8
"""
cashier session factory
"""
from treebo_commons.utils import dateutils

from prometheus.domain.billing.aggregates.cash_register_aggregate import (
    CashRegisterAggregate,
)
from prometheus.domain.billing.entities.cash_register import CashRegister
from ths_common.utils.id_generator_utils import random_id_generator


class CashRegisterFactory:
    @classmethod
    def create_new_cash_register(
        cls,
        vendor_id,
        default_opening_balance,
        opened_by,
        cash_register_name,
        start_datetime,
        carry_balance_to_next_session,
        base_currency,
    ):
        cash_register_id = random_id_generator()
        cash_register = CashRegister(
            cash_register_id=cash_register_id,
            cash_register_name=cash_register_name,
            start_datetime=dateutils.localize_datetime(start_datetime),
            vendor_id=vendor_id,
            default_opening_balance=default_opening_balance,
            opened_by=opened_by,
            end_datetime=None,
            carry_balance_to_next_session=carry_balance_to_next_session,
            base_currency=base_currency,
            lastest_voucher_sequence_number=0,
        )
        return CashRegisterAggregate(cash_register=cash_register)
