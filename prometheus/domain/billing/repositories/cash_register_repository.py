from object_registry import register_instance
from prometheus.domain.billing.aggregates.cash_register_aggregate import (
    CashRegisterAggregate,
)
from prometheus.domain.billing.models import CashRegisterModel
from prometheus.domain.billing.repositories.adaptors.cash_register_adaptor import (
    CashRegisterDBAdaptor,
)
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.exceptions import AggregateNotFound


@register_instance()
class CashRegisterRepository(BaseRepository):
    """
    Cashier repository
    """

    def to_aggregate(self, **kwargs):
        cash_register_model = kwargs.get('cash_register_model')
        cash_register = CashRegisterDBAdaptor.to_entity(cash_register_model)
        return CashRegisterAggregate(cash_register=cash_register)

    def from_aggregate(self, aggregate=None):
        pass

    def verify_version_id(self, bill_id, version_id):
        pass

    def save(self, cash_register_aggregate):
        """
        Saves the cash_register in DB

        :param cash_register_aggregate:
        """
        cash_register_model = CashRegisterDBAdaptor.to_db_model(
            cash_register_aggregate.cash_register
        )
        self._save(cash_register_model)
        self.flush_session()

    def load(self, cash_register_id, use_raw_query=None):
        cash_register_model = self.get(
            CashRegisterModel, cash_register_id=cash_register_id
        )
        if not cash_register_model:
            raise AggregateNotFound("CashRegisterAggregate", cash_register_id)
        return self.to_aggregate(cash_register_model=cash_register_model)

    def load_for_update(self, cash_register_id, use_raw_query=None):
        cash_register_model = self.get_for_update(
            CashRegisterModel, cash_register_id=cash_register_id
        )
        if not cash_register_model:
            raise AggregateNotFound("CashRegisterAggregate", cash_register_id)
        return self.to_aggregate(cash_register_model=cash_register_model)

    def get_cash_register_for_vendor(self, vendor_id):
        cash_register_model = self.filter(
            CashRegisterModel, CashRegisterModel.vendor_id == vendor_id
        ).first()
        if not cash_register_model:
            return
        return self.to_aggregate(cash_register_model=cash_register_model)

    def update(self, cash_register_aggregate):
        """
        updates the given cash register
        :param cash_register_aggregate:
        :return:
        """
        cash_register_model = CashRegisterDBAdaptor.to_db_model(
            cash_register_aggregate.cash_register
        )
        self._update(cash_register_model)
        self.flush_session()

    def search(self, query):
        if not query:
            return
        q = self.query(CashRegisterModel)
        if query.get('vendor_id'):
            q = q.filter(CashRegisterModel.vendor_id == query['vendor_id'])
        if query.get('cash_register_ids'):
            q = q.filter(
                CashRegisterModel.cash_register_id.in_(query['cash_register_ids'])
            )
        cash_register_models = q.all()
        return [
            self.to_aggregate(cash_register_model=cash_register_model)
            for cash_register_model in cash_register_models
        ]
