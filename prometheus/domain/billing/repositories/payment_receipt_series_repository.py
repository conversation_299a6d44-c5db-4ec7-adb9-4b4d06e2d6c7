from object_registry import register_instance
from prometheus.domain.billing.exceptions import PaymentReceiptNumberMaxLengthBreached
from prometheus.domain.billing.models import PaymentReceiptSequenceModel
from prometheus.domain.catalog.models import HotelModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.billing_constants import PaymentTypes
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import AuthorizationError, ValidationException


@register_instance()
class PaymentReceiptNumberGenerator(object):
    @staticmethod
    def generate_payment_receipt_number(prefix, current_business_date, sequence_number):
        # eg: 'PR210928/00001' , 'RV210928/00001'
        max_payment_receipt_number_length = 14
        number_of_special_characters = 1
        formatted_current_business_date = current_business_date.strftime("%y%m%d")
        remaining_length_after_prefix = (
            max_payment_receipt_number_length
            - number_of_special_characters
            - len(formatted_current_business_date)
            - len(prefix)
        )
        adjusted_sequence_number = str(sequence_number).rjust(
            remaining_length_after_prefix, '0'
        )
        payment_receipt_number = (
            f"{prefix}{formatted_current_business_date}/{adjusted_sequence_number}"
        )
        if len(payment_receipt_number) > max_payment_receipt_number_length:
            raise PaymentReceiptNumberMaxLengthBreached(
                max_length=max_payment_receipt_number_length,
                current_prefix=prefix,
                current_sequence=sequence_number,
            )
        return payment_receipt_number


@register_instance()
class PaymentReceiptSequenceRepository(BaseRepository):
    def from_aggregate(self, aggregate=None):
        pass

    def to_aggregate(self, **kwargs):
        pass

    @staticmethod
    def get_default_prefix(payment_type):
        if payment_type == PaymentTypes.PAYMENT:
            return "PR"
        elif payment_type == PaymentTypes.REFUND:
            return "RV"
        else:
            raise ValidationException(
                description="PaymentReceipt::Invalid payment type "
                "{} for receipt generation".format(payment_type.value)
            )

    def get_current_payment_receipt_series_details(self, payment_type, vendor_id):
        sequence = self.get_for_update(
            PaymentReceiptSequenceModel,
            payment_type=payment_type.value,
            vendor_id=vendor_id,
        )
        if not sequence:
            sequence_number = 0
            sequence = PaymentReceiptSequenceModel(
                payment_type=payment_type.value,
                vendor_id=vendor_id,
                prefix=self.get_default_prefix(payment_type),
                last_sequence_number=sequence_number,
            )

        return sequence.prefix, sequence.last_sequence_number

    def get_next_payment_receipt_number(self, payment_type, vendor_id):
        sequence = self.get_for_update(
            PaymentReceiptSequenceModel,
            payment_type=payment_type.value,
            vendor_id=vendor_id,
        )
        if not sequence:
            locked_hotel = self.get_for_update(HotelModel, hotel_id=vendor_id)
            sequence = self.get_for_update(
                PaymentReceiptSequenceModel,
                payment_type=payment_type.value,
                vendor_id=vendor_id,
            )
            if not sequence:
                sequence_number = 1
                new_sequence = PaymentReceiptSequenceModel(
                    payment_type=payment_type.value,
                    vendor_id=vendor_id,
                    prefix=self.get_default_prefix(payment_type),
                    last_sequence_number=sequence_number,
                )
                current_sequence = self._save(new_sequence)
            else:
                sequence_number = sequence.last_sequence_number + 1
                sequence.last_sequence_number = sequence_number
                self._save(sequence)
                current_sequence = sequence
        else:
            sequence_number = sequence.last_sequence_number + 1
            sequence.last_sequence_number = sequence_number
            self._save(sequence)
            current_sequence = sequence

        hotel_model = self.get_for_update(HotelModel, hotel_id=vendor_id)
        payment_receipt_number = (
            PaymentReceiptNumberGenerator.generate_payment_receipt_number(
                current_sequence.prefix,
                hotel_model.current_business_date,
                sequence_number,
            )
        )

        self.flush_session()
        return payment_receipt_number

    def reset_payment_receipt_sequence(
        self, user_type: UserType, payment_type, vendor_id, prefix, sequence_number
    ):
        if user_type not in (
            UserType.SUPER_ADMIN.value,
            UserType.CRS_MIGRATION_USER.value,
        ):
            raise AuthorizationError("You're not authorized to perform this operation")

        sequence = self.get_for_update(
            PaymentReceiptSequenceModel,
            payment_type=payment_type.value,
            vendor_id=vendor_id,
        )

        if not sequence:
            locked_hotel = self.get_for_update(HotelModel, hotel_id=vendor_id)
            sequence = self.get_for_update(
                PaymentReceiptSequenceModel,
                payment_type=payment_type.value,
                vendor_id=vendor_id,
            )
            if not sequence:
                new_sequence = PaymentReceiptSequenceModel(
                    payment_type=payment_type.value,
                    vendor_id=vendor_id,
                    prefix=prefix,
                    last_sequence_number=sequence_number,
                )
                self._save(new_sequence)
                return

        # Sequence found. Reset values in it.
        sequence.prefix = prefix
        sequence.last_sequence_number = sequence_number

        self._save(sequence)
        self.flush_session()
