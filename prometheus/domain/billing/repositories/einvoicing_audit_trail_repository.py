from typing import List, Optional

from object_registry import register_instance
from prometheus.domain.billing.entities.einvoicing_audit_trail import EInvoicingAuditTrail
from prometheus.domain.billing.models.einvoicing_audit_trail_model import EInvoicingAuditTrailModel
from shared_kernel.infrastructure.database.repository_base import RepositoryBase


@register_instance()
class EInvoicingAuditTrailRepository(RepositoryBase):
    def __init__(self):
        super().__init__(EInvoicingAuditTrailModel, EInvoicingAuditTrail)

    def save(self, einvoicing_audit_trail: EInvoicingAuditTrail):
        """Save a new e-invoicing audit trail entry"""
        self._save(einvoicing_audit_trail)

    def load_by_booking_id(self, booking_id: str) -> List[EInvoicingAuditTrail]:
        """Load all audit trail entries for a booking"""
        models = self.session.query(EInvoicingAuditTrailModel).filter(
            EInvoicingAuditTrailModel.booking_id == booking_id
        ).order_by(EInvoicingAuditTrailModel.action_datetime.desc()).all()
        
        return [self._to_entity(model) for model in models]

    def load_by_invoice_id(self, invoice_id: str) -> List[EInvoicingAuditTrail]:
        """Load all audit trail entries for an invoice"""
        models = self.session.query(EInvoicingAuditTrailModel).filter(
            EInvoicingAuditTrailModel.invoice_id == invoice_id
        ).order_by(EInvoicingAuditTrailModel.action_datetime.desc()).all()
        
        return [self._to_entity(model) for model in models]

    def load_by_hotel_id(self, hotel_id: str, limit: Optional[int] = None) -> List[EInvoicingAuditTrail]:
        """Load audit trail entries for a hotel"""
        query = self.session.query(EInvoicingAuditTrailModel).filter(
            EInvoicingAuditTrailModel.hotel_id == hotel_id
        ).order_by(EInvoicingAuditTrailModel.action_datetime.desc())
        
        if limit:
            query = query.limit(limit)
            
        models = query.all()
        return [self._to_entity(model) for model in models]
