from treebo_commons.money.constants import CurrencyType

from object_registry import register_instance
from prometheus.domain.billing.aggregates.credit_shell_aggregate import (
    CreditShellAggregate,
)
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.models import (
    CreditShellAuditTrailModel,
    CreditShellModel,
)
from prometheus.domain.billing.repositories.adaptors.credit_shell_adaptor import (
    CreditShellAdaptor,
)
from prometheus.domain.billing.repositories.adaptors.credit_shell_audit_trail_adaptor import (
    CreditShellAuditTrailAdaptor,
)
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.exceptions import AggregateNotFound, ValidationException


@register_instance()
class CreditShellRepository(BaseRepository):
    def from_aggregate(self, aggregate=None):
        credit_shell = aggregate.credit_shell
        credit_shell_model = CreditShellAdaptor.to_db_model(credit_shell)
        credit_shell_audit_trail_models = CreditShellAuditTrailAdaptor.to_db_model(
            aggregate.credit_shell_audit_trails
        )
        return credit_shell_model, credit_shell_audit_trail_models

    def to_aggregate(self, **kwargs):
        credit_shell_model = kwargs.get('credit_shell_model')
        currency = (
            CurrencyType(credit_shell_model.currency)
            if credit_shell_model.currency
            else None
        )
        credit_shell_audit_trail_models = kwargs.get('credit_shell_audit_trail_model')
        credit_shell_aggregate = CreditShellAggregate(
            credit_shell=CreditShellAdaptor.to_entity(
                credit_shell_model=credit_shell_model
            ),
            credit_shell_audit_trails=CreditShellAuditTrailAdaptor.to_entity(
                credit_shell_audit_trail_models=credit_shell_audit_trail_models,
                currency=currency,
            )
            if credit_shell_audit_trail_models
            else None,
        )
        return credit_shell_aggregate

    def save(self, credit_shell_aggregate: CreditShellAggregate):
        credit_shell_model, credit_shell_audit_trail_models = self.from_aggregate(
            aggregate=credit_shell_aggregate
        )
        self._save(credit_shell_model)
        self._save_all(credit_shell_audit_trail_models)
        self.flush_session()

    def save_all(self, credit_shell_aggregates: [CreditShellAggregate]):
        credit_shell_models = []
        credit_shell_audit_trail_models = []
        for credit_shell_aggregate in credit_shell_aggregates:
            credit_shell_models.append(self.from_aggregate(credit_shell_aggregate))
            credit_shell_audit_trail_models.append(
                self.from_aggregate(credit_shell_aggregate)
            )
        self._save_all(credit_shell_models)
        self._save_all(credit_shell_audit_trail_models)
        self.flush_session()

    def update(self, credit_shell_aggregate):
        credit_shell_id = credit_shell_aggregate.credit_shell.credit_shell_id
        old_credit_shell_model = self.get(
            CreditShellModel, credit_shell_id=credit_shell_id
        )
        if (
            old_credit_shell_model.version
            != credit_shell_aggregate.credit_shell.version
        ):
            raise ValidationException(BillingErrors.CREDIT_SHELL_VERSION_MISMATCH)
        credit_shell_aggregate.increment_version()
        credit_shell_model, credit_shell_audit_trail_models = self.from_aggregate(
            credit_shell_aggregate
        )
        self._update(credit_shell_model)
        self._update_all(credit_shell_audit_trail_models)

    def update_all(self, credit_shell_aggregates: [CreditShellAggregate]):
        credit_shell_models = []
        for credit_shell_aggregate in credit_shell_aggregates:
            credit_shell_id = credit_shell_aggregate.credit_shell.credit_shell_id
            old_credit_shell_model = self.get(
                CreditShellModel, credit_shell_id=credit_shell_id
            )
            if (
                old_credit_shell_model.version
                != credit_shell_aggregate.credit_shell.version
            ):
                raise ValidationException(BillingErrors.CREDIT_SHELL_VERSION_MISMATCH)
            credit_shell_aggregate.increment_version()
            credit_shell_models.append(self.from_aggregate(credit_shell_aggregate))
        self._update_all(credit_shell_models)

    def _load(self, credit_shell_model):
        credit_shell_audit_trail_model = self.filter(
            CreditShellAuditTrailModel,
            CreditShellAuditTrailModel.credit_shell_id
            == credit_shell_model.credit_shell_id,
        ).all()
        return self.to_aggregate(
            credit_shell_model=credit_shell_model,
            credit_shell_audit_trail_model=credit_shell_audit_trail_model,
        )

    def _create_credit_shell_aggregates(self, credit_shell_models: [CreditShellModel]):
        if not credit_shell_models:
            return []
        return [
            self.to_aggregate(
                credit_shell_model=credit_shell_model,
                credit_shell_audit_trail_model=self.filter(
                    CreditShellAuditTrailModel,
                    CreditShellAuditTrailModel.credit_shell_id
                    == credit_shell_model.credit_shell_id,
                ).all(),
            )
            for credit_shell_model in credit_shell_models
        ]

    def load_for_bill_id(self, bill_id):
        if not bill_id:
            return []
        credit_shell_models = self.filter(
            CreditShellModel, CreditShellModel.bill_id == bill_id
        )
        return self._create_credit_shell_aggregates(credit_shell_models.all())

    def load(self, credit_shell_id):
        credit_shell_model = self.get(CreditShellModel, credit_shell_id=credit_shell_id)
        if not credit_shell_model:
            raise AggregateNotFound("CreditNote", credit_shell_id)
        return self._load(credit_shell_model=credit_shell_model)

    def load_all(self, credit_shell_ids):
        credit_shell_models = self.filter(
            CreditShellModel,
            CreditShellModel.credit_shell_id.in_(credit_shell_ids).order_by(
                CreditShellModel.credit_shell_id
            ),
        )
        return self._create_credit_shell_aggregates(credit_shell_models.all())

    def load_for_update(self, credit_shell_id):
        credit_shell_model = self.get_for_update(
            CreditShellModel, credit_shell_id=credit_shell_id
        )
        if not credit_shell_model:
            raise AggregateNotFound("CreditShell", credit_shell_id)
        return self._load(credit_shell_model=credit_shell_model)

    def load_all_for_update(self, credit_shell_ids):
        credit_shell_models = self.filter(
            CreditShellModel, CreditShellModel.credit_shell_id.in_(credit_shell_ids)
        )
        credit_shell_aggregates = self._create_credit_shell_aggregates(
            credit_shell_models=credit_shell_models
        )
        credit_shell_dict = {
            credit_shell_aggregates.credit_shell.credit_shell_id: credit_shell_aggregate
            for credit_shell_aggregate in credit_shell_aggregates
        }
        return credit_shell_aggregates, credit_shell_dict

    def load_all_by_id(self, credit_shell_id=None, bill_id=None):
        if credit_shell_id:
            return [self.load(credit_shell_id=credit_shell_id)]
        if bill_id:
            return self.load_for_bill_id(bill_id=bill_id)

    def load_all_for_paid_by_paid_to_migration(self):
        credit_shell_models = self.filter(
            CreditShellModel, CreditShellModel.deleted == False
        )
        return [cs.credit_shell_id for cs in credit_shell_models.all()]
