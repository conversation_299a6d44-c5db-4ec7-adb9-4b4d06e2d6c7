from sqlalchemy import func
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.billing.aggregates.cashier_session_aggregate import (
    CashierSessionAggregate,
)
from prometheus.domain.billing.dto.cashier_session_search_query import (
    CashierSessionSearchQuery,
)
from prometheus.domain.billing.models import (
    CashierPaymentModel,
    CashierSessionBalanceModel,
    CashierSessionModel,
    CashRegisterModel,
)
from prometheus.domain.billing.repositories.adaptors.cashier_payment_adaptor import (
    CashierPaymentDBAdapter,
)
from prometheus.domain.billing.repositories.adaptors.cashier_session_adaptor import (
    CashCounterBalanceDBAdaptor,
    CashierSessionDBAdapter,
)
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.billing_constants import (
    CashierBalanceType,
    CashierSessionStatus,
    PaymentModes,
)
from ths_common.exceptions import AggregateNotFound
from ths_common.utils.common_utils import group_list


@register_instance()
class CashierSessionRepository(BaseRepository):
    """
    Cashier repository
    """

    def to_aggregate(self, **kwargs):
        opening_balance = [
            CashCounterBalanceDBAdaptor.to_entity(currency_wise_balance)
            for currency_wise_balance in kwargs['cash_counter_balance_models']
            if currency_wise_balance.balance_type == CashierBalanceType.OPENING
        ]
        closing_balance = [
            CashCounterBalanceDBAdaptor.to_entity(currency_wise_balance)
            for currency_wise_balance in kwargs['cash_counter_balance_models']
            if currency_wise_balance.balance_type == CashierBalanceType.CLOSING
        ]
        base_currency = CurrencyType(kwargs['base_currency'])
        cashier_session = CashierSessionDBAdapter.to_entity(
            kwargs['cashier_session_model'], base_currency
        )
        cash_counter_payments = []

        if kwargs['payment_models']:
            cash_counter_payments = CashierPaymentDBAdapter.to_entities(
                kwargs['payment_models'], base_currency=base_currency
            )

        cashier_session_aggregate = CashierSessionAggregate(
            cashier_session, opening_balance, closing_balance, cash_counter_payments
        )
        cashier_session_aggregate.set_session_summary(base_currency)
        return cashier_session_aggregate

    def from_aggregate(self, aggregate=None):
        pass

    def verify_version_id(self, bill_id, version_id):
        bill = self.get(CashierSessionModel, bill_id=bill_id)
        return version_id == bill.version_id

    def save(self, cashier_session_aggregate):
        """
        Saves the cashier_session_aggregate in DB

        :param cashier_session_aggregate:
        """
        cashier_session_aggregate.check_invariance()
        cashier_session_model = CashierSessionDBAdapter.to_db_model(
            cashier_session_aggregate.cashier_session
        )
        opening_balance_models = [
            CashCounterBalanceDBAdaptor.to_db_model(
                currency_wise_balance,
                cashier_session_aggregate.cashier_session.cashier_session_id,
            )
            for currency_wise_balance in cashier_session_aggregate.opening_balance
        ]
        if cashier_session_aggregate.closing_balance:
            closing_balance_models = [
                CashCounterBalanceDBAdaptor.to_db_model(
                    currency_wise_balance,
                    cashier_session_aggregate.cashier_session.cashier_session_id,
                )
                for currency_wise_balance in cashier_session_aggregate.closing_balance
            ]
            self._save_all(closing_balance_models)

        # TODO: Add cashier session payments
        self._save_all(opening_balance_models)
        self._save(cashier_session_model)
        self.flush_session()
        self.mark_clean()

    def mark_clean(self):
        pass

    def _create_cashier_session_aggregates(self, cashier_session_models):
        cashier_session_id_to_model = {
            model.cashier_session_id: model for model in cashier_session_models
        }
        cash_counter_balance_models = self.query(CashierSessionBalanceModel).filter(
            CashierSessionBalanceModel.cashier_session_id.in_(
                cashier_session_id_to_model.keys()
            )
        )
        cashier_payment_models = (
            self.query(CashierPaymentModel)
            .filter(
                CashierPaymentModel.cashier_session_id.in_(
                    cashier_session_id_to_model.keys()
                )
            )
            .order_by(CashierPaymentModel.date_of_payment.desc())
        )

        cash_register_ids = {model.cash_register_id for model in cashier_session_models}
        cash_register_models = self.query(CashRegisterModel).filter(
            CashRegisterModel.cash_register_id.in_(cash_register_ids)
        )

        cashier_payment_group_by_session = group_list(
            cashier_payment_models, 'cashier_session_id'
        )
        cashier_balance_group_by_session = group_list(
            cash_counter_balance_models, 'cashier_session_id'
        )
        cash_register_group_by_register_id = {
            model.cash_register_id: model for model in cash_register_models
        }

        return [
            self.to_aggregate(
                cashier_session_model=session_model,
                cash_counter_balance_models=cashier_balance_group_by_session[
                    session_model.cashier_session_id
                ],
                payment_models=cashier_payment_group_by_session[
                    session_model.cashier_session_id
                ],
                base_currency=cash_register_group_by_register_id[
                    session_model.cash_register_id
                ].base_currency,
            )
            for session_model in cashier_session_models
        ]

    def _create_cashier_session_aggregate(self, cashier_session_model):
        cash_counter_balance_models = self.filter(
            CashierSessionBalanceModel,
            CashierSessionBalanceModel.cashier_session_id
            == cashier_session_model.cashier_session_id,
        )
        cashier_payment_models = self.filter(
            CashierPaymentModel,
            CashierPaymentModel.cashier_session_id
            == cashier_session_model.cashier_session_id,
        ).order_by(CashierPaymentModel.date_of_payment.desc())

        cash_register_model = self.get(
            CashRegisterModel, cash_register_id=cashier_session_model.cash_register_id
        )
        if not cash_register_model:
            raise AggregateNotFound(
                "CashRegisterModel", cashier_session_model.cash_register_id
            )

        return self.to_aggregate(
            cashier_session_model=cashier_session_model,
            cash_counter_balance_models=cash_counter_balance_models,
            payment_models=cashier_payment_models,
            base_currency=cash_register_model.base_currency,
        )

    def get_cashier_sessions_for_date(self, date):
        cashier_sessions = (
            self.filter(CashierSessionModel, CashierSessionModel.start_datetime >= date)
            .order_by(
                CashierSessionModel.start_datetime.desc(),
                CashierSessionModel.session_number.desc(),
            )
            .all()
        )

        return self._create_cashier_session_aggregates(cashier_sessions)

    def get_latest_cashier_session(self, cash_register_id, for_update=False):
        cashier_session = (
            self.filter(
                CashierSessionModel,
                CashierSessionModel.cash_register_id == cash_register_id,
                for_update=for_update,
            )
            .order_by(
                CashierSessionModel.start_datetime.desc(),
                CashierSessionModel.session_number.desc(),
            )
            .first()
        )
        if not cashier_session:
            return
        return self._create_cashier_session_aggregate(
            cashier_session_model=cashier_session
        )

    def get_cashier_session_for_bill_payment(self, bill_id, bill_payment_id):
        cashier_session_model = self.filter_by_join(
            [CashierPaymentModel, CashierSessionModel],
            CashierSessionModel.cashier_session_id
            == CashierPaymentModel.cashier_session_id,
            CashierPaymentModel.bill_id == bill_id,
            CashierPaymentModel.bill_payment_id == bill_payment_id,
        ).first()
        if not cashier_session_model:
            return
        return self._create_cashier_session_aggregate(
            cashier_session_model.CashierSessionModel
        )

    def load(self, cashier_session_id, use_raw_query=None):
        cashier_session_model = self.get(
            CashierSessionModel, cashier_session_id=cashier_session_id
        )
        if not cashier_session_model:
            raise AggregateNotFound("CashierSessionAggregate", cashier_session_id)
        return self._create_cashier_session_aggregate(
            cashier_session_model=cashier_session_model
        )

    def load_for_update(self, cashier_session_id, use_raw_query=None):
        cashier_session_model = self.get_for_update(
            CashierSessionModel, cashier_session_id=cashier_session_id
        )
        if not cashier_session_model:
            raise AggregateNotFound("CashierSessionAggregate", cashier_session_id)
        return self._create_cashier_session_aggregate(cashier_session_model)

    def update(self, cashier_session_aggregate):
        """
        updates the given bill aggregate
        :param cashier_session_aggregate:
        :return:
        """
        cashier_session_aggregate.check_invariance()
        cashier_session_id = (
            cashier_session_aggregate.cashier_session.cashier_session_id
        )
        cashier_session_model = CashierSessionDBAdapter.to_db_model(
            cashier_session_aggregate.cashier_session
        )
        payment_models = CashierPaymentDBAdapter.to_db_models(
            cashier_session_id, cashier_session_aggregate.payments
        )
        if cashier_session_aggregate._closing_balance:
            closing_balance_models = [
                CashCounterBalanceDBAdaptor.to_db_model(
                    currency_wise_balance,
                    cashier_session_aggregate.cashier_session.cashier_session_id,
                )
                for currency_wise_balance in cashier_session_aggregate._closing_balance
            ]
            self._update_all(closing_balance_models)

        self._update_all(payment_models)
        self._update(cashier_session_model)
        self.flush_session()

    def load_critical_cashier_session_ids(self, vendor_ids):
        q = self.query(CashierSessionModel.cashier_session_id).filter(
            CashierSessionModel.vendor_id.in_(vendor_ids),
            CashierSessionModel.deleted == False,
            CashierSessionModel.status == CashierSessionStatus.OPEN,
            func.date(
                func.timezone(
                    dateutils.get_timezone().zone, CashierSessionModel.start_datetime
                )
            )
            <= crs_context.get_hotel_context().current_date(),
        )
        return [cashier_session_id for (cashier_session_id,) in q.all()]

    def search(self, search_query: CashierSessionSearchQuery):
        if not search_query:
            return
        q = self.query(CashierSessionModel).filter(CashierSessionModel.deleted == False)
        q = self._build_search_query(q, search_query)
        sort_tuple = self._get_sort_tuple(search_query)
        q = q.order_by(*sort_tuple)
        if not search_query.limit:
            q = q.yield_per(1000)
        else:
            q = q.limit(search_query.limit).offset(search_query.offset)
        cashier_session_models = q.all()
        return self._create_cashier_session_aggregates(cashier_session_models)

    def _build_search_query(self, q, search_query: CashierSessionSearchQuery):
        sq = search_query
        if sq.vendor_id:
            q = q.filter(CashierSessionModel.vendor_id == sq.vendor_id)
        if sq.cash_register_ids:
            q = q.filter(CashierSessionModel.cash_register_id.in_(sq.cash_register_ids))
        if sq.cashier_session_ids:
            q = q.filter(
                CashierSessionModel.cashier_session_id.in_(sq.cashier_session_ids)
            )
        if sq.start_datetime and sq.end_datetime:
            q = q.filter(
                CashierSessionModel.start_datetime >= sq.start_datetime,
                CashierSessionModel.start_datetime <= sq.end_datetime,
            )
        if sq.transaction_type:
            payment_modes = (
                [
                    PaymentModes.CREDIT_CARD,
                    PaymentModes.DEBIT_CARD,
                    PaymentModes.PHONE_PE,
                    PaymentModes.AMAZON_PAY,
                ]
                if sq.transaction_type == 'non_cash'
                else [PaymentModes.CASH]
            )

            q = (
                q.join(
                    CashierPaymentModel,
                    CashierPaymentModel.cashier_session_id
                    == CashierSessionModel.cashier_session_id,
                )
                .filter(CashierPaymentModel.payment_mode.in_(payment_modes))
                .order_by(CashierPaymentModel.date_of_payment.desc())
            )
        if sq.status:
            q = q.filter(
                func.lower(CashierSessionModel.status) == func.lower(sq.status)
            )
        return q

    @staticmethod
    def _get_sort_tuple(search_query: CashierSessionSearchQuery):
        sort_tuple = (
            CashierSessionModel.start_datetime.desc(),
            CashierSessionModel.session_number.desc(),
        )

        if search_query.sort_by:
            if search_query.sort_by == "-created_at":
                sort_tuple = (CashierSessionModel.created_at.desc(),)
            elif search_query.sort_by == "created_at":
                sort_tuple = (CashierSessionModel.created_at.asc(),)
            elif search_query.sort_by == "start_datetime":
                sort_tuple = (CashierSessionModel.start_datetime.asc(),)
            elif search_query.sort_by == "session_number":
                sort_tuple = (CashierSessionModel.session_number.asc(),)
        return sort_tuple

    def count(self, cash_register_id, search_query: CashierSessionSearchQuery):
        q = self.query(CashierSessionModel).filter(
            CashierSessionModel.cash_register_id == cash_register_id,
            CashierSessionModel.deleted == False,
        )
        q = self._build_search_query(q, search_query)
        return q.count()
