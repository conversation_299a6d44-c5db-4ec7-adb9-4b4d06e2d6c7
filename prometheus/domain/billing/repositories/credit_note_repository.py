# coding=utf-8
import json
from typing import List

from sqlalchemy import and_, func
from sqlalchemy.dialects.postgresql import array
from sqlalchemy.orm import aliased
from treebo_commons.money.constants import CurrencyType
from treebo_commons.money.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.billing.aggregates.credit_note_aggregate import (
    CreditNoteAggregate,
)
from prometheus.domain.billing.dto.erp_event_details_dto import (
    ErpInvoiceAndCreditNoteDto,
)
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.entities.credit_note import CreditNote
from prometheus.domain.billing.entities.credit_note_line_item import CreditNoteLineItem
from prometheus.domain.billing.exceptions import WrongCurrencyError
from prometheus.domain.billing.models import (
    AccountModel,
    BillModel,
    CreditNoteLineItemModel,
    CreditNoteModel,
    FolioModel,
    InvoiceChargeModel,
)
from prometheus.domain.booking.models import BookingModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.billing_constants import (
    ChargeTypes,
    CreditNoteStatus,
    InvoiceStatus,
    IssuedByType,
    IssuedToType,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.exceptions import AggregateNotFound, OutdatedVersion
from ths_common.utils.common_utils import group_list, json_dumps
from ths_common.value_objects import (
    InvoiceBillToInfo,
    InvoiceIssuedByInfo,
    VendorDetails,
)


class CreditNoteDBAdaptor(object):
    @staticmethod
    def to_db_model(credit_note: CreditNote):
        return CreditNoteModel(
            bill_id=credit_note.bill_id,
            credit_note_id=credit_note.credit_note_id,
            credit_note_number=credit_note.credit_note_number,
            issued_to=credit_note.issued_to.to_json()
            if credit_note.issued_to
            else None,
            issued_by=credit_note.issued_by.to_json()
            if credit_note.issued_by
            else None,
            status=credit_note.status.value,
            pretax_amount=credit_note.pretax_amount.amount,
            tax_amount=credit_note.tax_amount.amount,
            posttax_amount=credit_note.posttax_amount.amount,
            credit_note_url=credit_note.credit_note_url,
            credit_note_date=credit_note.credit_note_date,
            vendor_id=credit_note.vendor_id,
            issued_to_type=credit_note.issued_to_type.value,
            vendor_details=credit_note.vendor_details.to_json(),
            tax_details=json.loads(json_dumps(credit_note._tax_details)),
            deleted=credit_note.deleted,
            version=credit_note.version,
            issued_by_type=credit_note.issued_by_type.value,
            comment=credit_note.comment,
            hotel_credit_note_id=credit_note.hotel_credit_note_id,
            irn=credit_note.irn,
            qr_code=credit_note.qr_code,
            signed_invoice=credit_note.signed_invoice,
            irp_ack_number=credit_note.irp_ack_number,
            irp_ack_date=credit_note.irp_ack_date,
            is_einvoice=credit_note.is_einvoice,
            signed_url=credit_note.signed_url,
            signed_url_expiry_time=credit_note.signed_url_expiry_time,
            billed_entity_id=credit_note.billed_entity_account.billed_entity_id
            if credit_note.billed_entity_account
            else None,
            billed_entity_account_number=credit_note.billed_entity_account.account_number
            if credit_note.billed_entity_account
            else None,
        )

    @staticmethod
    def to_entity(credit_note_model: CreditNoteModel, base_currency):
        vendor_details, issued_to, issued_by = (
            credit_note_model.vendor_details,
            credit_note_model.issued_to,
            credit_note_model.issued_by,
        )
        vendor_details = VendorDetails.from_json(
            json.loads(vendor_details)
            if isinstance(vendor_details, str)
            else vendor_details
        )

        if issued_to:
            issued_to = InvoiceBillToInfo.from_json(issued_to)
        if issued_by:
            issued_by = InvoiceIssuedByInfo.from_json(issued_by)

        tax_details = dict()
        for tax_type, tax_detail in credit_note_model.tax_details.items():
            tax_details[tax_type] = Money(tax_detail, base_currency)

        return CreditNote(
            bill_id=credit_note_model.bill_id,
            credit_note_id=credit_note_model.credit_note_id,
            credit_note_number=credit_note_model.credit_note_number,
            issued_to=issued_to,
            issued_by=issued_by,
            status=CreditNoteStatus(credit_note_model.status),
            pretax_amount=Money(credit_note_model.pretax_amount, base_currency),
            tax_amount=Money(credit_note_model.tax_amount, base_currency),
            posttax_amount=Money(credit_note_model.posttax_amount, base_currency),
            credit_note_url=credit_note_model.credit_note_url,
            credit_note_date=credit_note_model.credit_note_date,
            vendor_id=credit_note_model.vendor_id,
            issued_to_type=IssuedToType(credit_note_model.issued_to_type),
            vendor_details=vendor_details,
            tax_details=tax_details,
            deleted=credit_note_model.deleted,
            version=credit_note_model.version,
            comment=credit_note_model.comment,
            issued_by_type=IssuedByType(credit_note_model.issued_by_type),
            hotel_credit_note_id=credit_note_model.hotel_credit_note_id,
            created_at=dateutils.localize_datetime(credit_note_model.created_at),
            modified_at=dateutils.localize_datetime(credit_note_model.modified_at),
            irn=credit_note_model.irn,
            qr_code=credit_note_model.qr_code,
            signed_invoice=credit_note_model.signed_invoice,
            irp_ack_number=credit_note_model.irp_ack_number,
            irp_ack_date=credit_note_model.irp_ack_date,
            is_einvoice=credit_note_model.is_einvoice,
            signed_url=credit_note_model.signed_url,
            signed_url_expiry_time=dateutils.localize_datetime(
                credit_note_model.signed_url_expiry_time
            ),
            billed_entity_account=BilledEntityAccountVO(
                billed_entity_id=credit_note_model.billed_entity_id,
                account_number=credit_note_model.billed_entity_account_number,
            )
            if credit_note_model.billed_entity_id
            else None,
        )


class CreditNoteLineItemDBAdaptor(object):
    @staticmethod
    def to_db_models(credit_note_id, credit_note_line_items: List[CreditNoteLineItem]):
        credit_note_line_item_models = []
        for line_item in credit_note_line_items:
            credit_note_line_item_model = CreditNoteLineItemModel(
                credit_note_id=credit_note_id,
                credit_note_line_item_id=line_item.credit_note_line_item_id,
                invoice_id=line_item.invoice_id,
                invoice_charge_id=line_item.invoice_charge_id,
                charge_id=line_item.charge_id,
                pretax_amount=line_item.pretax_amount.amount,
                tax_amount=line_item.tax_amount.amount,
                posttax_amount=line_item.posttax_amount.amount,
                tax_details=json.loads(json_dumps(line_item._tax_details)),
                applicable_date=line_item.applicable_date,
                deleted=line_item.deleted,
            )
            credit_note_line_item_models.append(credit_note_line_item_model)
        return credit_note_line_item_models

    @staticmethod
    def to_entities(
        credit_note_line_item_models: List[CreditNoteLineItemModel], base_currency
    ):
        credit_note_line_items = []

        for line_item_model in credit_note_line_item_models:
            tax_details = dict()
            for tax_type, tax_detail in line_item_model.tax_details.items():
                tax_detail['amount'] = Money(tax_detail['amount'], base_currency)
                tax_details[tax_type] = tax_detail

            line_item = CreditNoteLineItem(
                credit_note_line_item_id=line_item_model.credit_note_line_item_id,
                invoice_id=line_item_model.invoice_id,
                created_at=dateutils.localize_datetime(line_item_model.created_at),
                modified_at=dateutils.localize_datetime(line_item_model.modified_at),
                invoice_charge_id=line_item_model.invoice_charge_id,
                applicable_date=dateutils.localize_datetime(
                    line_item_model.applicable_date
                ),
                pretax_amount=Money(line_item_model.pretax_amount, base_currency),
                posttax_amount=Money(line_item_model.posttax_amount, base_currency),
                tax_amount=Money(line_item_model.tax_amount, base_currency),
                tax_details=tax_details,
                deleted=line_item_model.deleted,
                charge_id=line_item_model.charge_id,
            )
            credit_note_line_items.append(line_item)
        return credit_note_line_items


@register_instance()
class CreditNoteRepository(BaseRepository):
    def from_aggregate(self, aggregate=None):
        credit_note = aggregate.credit_note
        credit_note_model = CreditNoteDBAdaptor.to_db_model(credit_note)
        credit_note_line_item_models = CreditNoteLineItemDBAdaptor.to_db_models(
            credit_note.credit_note_id, aggregate.credit_note_line_items
        )
        return credit_note_model, credit_note_line_item_models

    def to_aggregate(self, **kwargs):
        base_currency = (
            CurrencyType(kwargs.get('bill_base_currency'))
            if kwargs.get('bill_base_currency')
            else CurrencyType.INR
        )

        credit_note = CreditNoteDBAdaptor.to_entity(
            kwargs.get('credit_note_model'), base_currency
        )
        credit_note_line_items = CreditNoteLineItemDBAdaptor.to_entities(
            kwargs.get('credit_note_line_item_models')
            if kwargs.get('credit_note_line_item_models')
            else [],
            base_currency,
        )
        return CreditNoteAggregate(
            credit_note, credit_note_line_items, invoice_ids=kwargs.get('invoice_ids')
        )

    def save(self, credit_note_aggregate):
        credit_note_model, credit_note_line_item_models = self.from_aggregate(
            aggregate=credit_note_aggregate
        )
        self._save(credit_note_model)
        self._save_all(credit_note_line_item_models)
        self.flush_session()

    def save_all(self, credit_note_aggregates):
        credit_note_models = []
        credit_note_line_item_models = []
        for credit_note_aggregate in credit_note_aggregates:
            credit_note_model, line_item_models = self.from_aggregate(
                aggregate=credit_note_aggregate
            )
            credit_note_models.append(credit_note_model)
            credit_note_line_item_models.extend(line_item_models)

        self._save_all(credit_note_models)
        self._save_all(credit_note_line_item_models)
        self.flush_session()

    def update(self, credit_note_aggregate):
        credit_note_aggregate.increment_version()
        credit_note_model, credit_note_line_item_models = self.from_aggregate(
            aggregate=credit_note_aggregate
        )

        self._update(credit_note_model)
        self._update_all(credit_note_line_item_models)
        self.flush_session()

    def update_all(self, credit_note_aggregates):
        credit_note_models = []
        credit_note_line_item_models = []
        for credit_note_aggregate in credit_note_aggregates:
            credit_note_aggregate.increment_version()
            credit_note_model, line_item_models = self.from_aggregate(
                aggregate=credit_note_aggregate
            )
            credit_note_models.append(credit_note_model)
            credit_note_line_item_models.extend(line_item_models)

        self._update_all(credit_note_models)
        self._update_all(credit_note_line_item_models)
        self.flush_session()

    def _load(self, credit_note_model):
        credit_note_id = credit_note_model.credit_note_id
        bill_base_currency = self.filter(
            BillModel.base_currency, BillModel.bill_id == credit_note_model.bill_id
        ).one()[0]
        credit_note_line_item_models = self.filter(
            CreditNoteLineItemModel,
            CreditNoteLineItemModel.credit_note_id == credit_note_id,
        )
        return self.to_aggregate(
            credit_note_model=credit_note_model,
            credit_note_line_item_models=credit_note_line_item_models,
            bill_base_currency=bill_base_currency,
        )

    def _create_credit_note_aggregates(
        self, credit_note_models, shallow_response=False
    ):
        if not credit_note_models:
            return []

        credit_note_ids = [
            credit_note_model.credit_note_id for credit_note_model in credit_note_models
        ]
        grouped_credit_note_line_item_models = dict()
        invoice_ids = None
        if not shallow_response:
            q = self.query(CreditNoteLineItemModel)
            if len(credit_note_ids) > 100:
                subq = (
                    self.session()
                    .query(func.unnest(array(credit_note_ids)).label('credit_note_ids'))
                    .subquery()
                )
                q = q.join(
                    subq,
                    CreditNoteLineItemModel.credit_note_id == subq.c.credit_note_ids,
                )
            else:
                q = q.filter(
                    CreditNoteLineItemModel.credit_note_id.in_(credit_note_ids)
                )
            grouped_credit_note_line_item_models = group_list(q, 'credit_note_id')
        else:
            invoice_ids = self.query(CreditNoteLineItemModel.invoice_id).filter(
                CreditNoteLineItemModel.credit_note_id.in_(credit_note_ids)
            )
            invoice_ids = [inv[0] for inv in invoice_ids]

        bill_base_currency = self.filter(
            BillModel.base_currency, BillModel.bill_id == credit_note_models[0].bill_id
        ).one()[0]
        return [
            self.to_aggregate(
                credit_note_model=credit_note_model,
                invoice_ids=invoice_ids,
                credit_note_line_item_models=grouped_credit_note_line_item_models.get(
                    credit_note_model.credit_note_id
                ),
                bill_base_currency=bill_base_currency,
            )
            for credit_note_model in credit_note_models
        ]

    def load_for_bill_id(
        self, bill_id, shallow_response=False, exclude_issued_to_reseller=True
    ):
        credit_note_models = self.filter(
            CreditNoteModel, CreditNoteModel.bill_id == bill_id
        )
        if exclude_issued_to_reseller:
            credit_note_models = credit_note_models.filter(
                CreditNoteModel.issued_to_type != IssuedToType.RESELLER.value
            )
        return self._create_credit_note_aggregates(
            credit_note_models.all(), shallow_response=shallow_response
        )

    def load(self, credit_note_id):
        credit_note_model = self.get(CreditNoteModel, credit_note_id=credit_note_id)
        if not credit_note_model:
            raise AggregateNotFound("CreditNote", credit_note_id)
        return self._load(credit_note_model)

    def load_all(self, credit_note_ids):
        credit_note_models = self.filter(
            CreditNoteModel, CreditNoteModel.credit_note_id.in_(credit_note_ids)
        ).order_by(CreditNoteModel.credit_note_id)
        return self._create_credit_note_aggregates(credit_note_models.all())

    def load_for_update(self, credit_note_id, version=None):
        credit_note_model = self.get_for_update(
            CreditNoteModel, credit_note_id=credit_note_id
        )
        if not credit_note_model:
            raise AggregateNotFound("CreditNote", credit_note_id)
        if version is not None and credit_note_model.version != version:
            raise OutdatedVersion('CreditNote', version, credit_note_model.version)
        return self._load(credit_note_model)

    def load_all_for_update(self, credit_note_ids):
        credit_note_models = self.filter(
            CreditNoteModel,
            CreditNoteModel.credit_note_id.in_(credit_note_ids),
            for_update=True,
        ).order_by(CreditNoteModel.credit_note_id)
        return self._create_credit_note_aggregates(credit_note_models.all())

    def load_for_bill_ids_with_yield_per(self, bill_ids):
        q = self.query(CreditNoteModel).yield_per(1000).enable_eagerloads(False)
        q = q.filter(CreditNoteModel.issued_to_type != IssuedToType.RESELLER.value)
        if len(bill_ids) > 100:
            subq = (
                self.session()
                .query(func.unnest(array(bill_ids)).label('bill_ids'))
                .subquery()
            )
            credit_note_models = q.join(
                subq, CreditNoteModel.bill_id == subq.c.bill_ids
            )
        else:
            credit_note_models = q.filter(CreditNoteModel.bill_id.in_(bill_ids))
        return self._create_credit_note_aggregates(credit_note_models.all())

    def load_by_credit_note_numbers(self, credit_note_numbers, load_line_items=True):
        q = self.query(CreditNoteModel).filter(
            CreditNoteModel.credit_note_number.in_(credit_note_numbers)
        )
        return self._create_credit_note_aggregates(
            q.all(),
            shallow_response=not load_line_items,
        )

    def credit_note_report_query(
        self, start_date, end_date, hotel_ids, issued_by_type=None, bill_ids=None
    ):
        q = self.query(CreditNoteModel).yield_per(1000).enable_eagerloads(False)
        if start_date and end_date:
            q = q.filter(func.Date(CreditNoteModel.created_at) >= start_date).filter(
                func.Date(CreditNoteModel.created_at) <= end_date
            )
        q = q.filter(CreditNoteModel.deleted == False)
        if hotel_ids is not None:
            q = q.filter(CreditNoteModel.vendor_id.in_(hotel_ids))

        if bill_ids:
            q = q.filter(CreditNoteModel.bill_id.in_(bill_ids))

        if issued_by_type:
            q = q.filter(CreditNoteModel.issued_by_type == issued_by_type.value)

        return self._create_credit_note_aggregates(q.all())

    def get_hotel_credit_note_mapping(self, customer_credit_note_ids):
        credit_note_model_sale = aliased(CreditNoteModel)
        credit_note_model_purchase = aliased(CreditNoteModel)
        query = self.query(
            credit_note_model_sale.credit_note_number,
            credit_note_model_purchase.credit_note_number,
        ).join(
            credit_note_model_purchase,
            and_(
                credit_note_model_sale.hotel_credit_note_id
                == credit_note_model_purchase.credit_note_id,
                credit_note_model_sale.credit_note_id.in_(customer_credit_note_ids),
            ),
        )
        return {
            customer_cn_number: hotel_cn_number
            for customer_cn_number, hotel_cn_number in query.all()
        }

    def get_ecredit_notes_without_irn(
        self, credit_note_ids=None, hotel_ids=None, bill_ids=None
    ):
        q = self.query(CreditNoteModel).yield_per(1000).enable_eagerloads(False)
        q = q.filter((CreditNoteModel.irn == '') | (CreditNoteModel.irn.is_(None)))
        q = q.filter(CreditNoteModel.is_einvoice == True)

        if credit_note_ids:
            q = q.filter(CreditNoteModel.credit_note_id.in_(credit_note_ids))

        if hotel_ids:
            q = q.filter(CreditNoteModel.vendor_id.in_(hotel_ids))

        if bill_ids:
            q = q.filter(CreditNoteModel.bill_id.in_(bill_ids))

        models = q.all()
        return self._create_credit_note_aggregates(models)

    def get_total_credit_note_amount_on_given_business_date(
        self, business_date, hotel_id, booking_statuses=None
    ) -> Money:
        q = self.query(func.sum(CreditNoteLineItemModel.posttax_amount))
        q = q.join(
            CreditNoteModel,
            CreditNoteLineItemModel.credit_note_id == CreditNoteModel.credit_note_id,
        )
        q = q.join(
            InvoiceChargeModel,
            and_(
                InvoiceChargeModel.invoice_id == CreditNoteLineItemModel.invoice_id,
                InvoiceChargeModel.charge_id == CreditNoteLineItemModel.charge_id,
            ),
        )
        if booking_statuses:
            q = q.join(BookingModel, CreditNoteModel.bill_id == BookingModel.bill_id)
            q = q.filter(BookingModel.status.in_(booking_statuses))

        q = q.filter(
            CreditNoteModel.vendor_id == hotel_id,
            CreditNoteModel.credit_note_date == business_date,
            InvoiceChargeModel.charge_type == ChargeTypes.CREDIT.value,
            CreditNoteModel.deleted.is_(False),
        )
        total_credit_note_amount = q.first()[0]
        return (
            Money(
                total_credit_note_amount, crs_context.get_hotel_context().base_currency
            )
            if total_credit_note_amount
            else Money(0, crs_context.get_hotel_context().base_currency)
        )

    def get_credit_note_on_given_business_date(self, hotel_id, business_date):
        q = self.query(
            CreditNoteModel.posttax_amount,
            CreditNoteModel.bill_id,
            CreditNoteModel.billed_entity_id,
            FolioModel.folio_number,
        )
        q = q.join(AccountModel, AccountModel.bill_id == CreditNoteModel.bill_id)
        q = q.join(FolioModel, FolioModel.bill_id == CreditNoteModel.bill_id)
        q = q.filter(
            CreditNoteModel.bill_id == AccountModel.bill_id,
            CreditNoteModel.billed_entity_id == AccountModel.billed_entity_id,
            CreditNoteModel.billed_entity_account_number == AccountModel.account_number,
            CreditNoteModel.billed_entity_id == FolioModel.billed_entity_id,
            CreditNoteModel.billed_entity_account_number == FolioModel.account_number,
            AccountModel.account_type == ChargeTypes.CREDIT.value,
            CreditNoteModel.status != InvoiceStatus.CANCELLED.value,
            CreditNoteModel.credit_note_date == business_date,
            CreditNoteModel.vendor_id == hotel_id,
        )
        credit_notes = q.all()
        credit_note_details = [
            ErpInvoiceAndCreditNoteDto(credit_note) for credit_note in credit_notes
        ]
        return credit_note_details

    def get_credit_note_on_given_invoice_date(self, hotel_id, credit_note_date):
        q = self.query(
            CreditNoteModel.bill_id,
            CreditNoteModel.billed_entity_id,
            FolioModel.folio_number,
            CreditNoteModel.billed_entity_account_number,
        )
        q = q.join(FolioModel, FolioModel.bill_id == CreditNoteModel.bill_id)
        q = q.filter(
            CreditNoteModel.billed_entity_id == FolioModel.billed_entity_id,
            CreditNoteModel.billed_entity_account_number == FolioModel.account_number,
            CreditNoteModel.status != InvoiceStatus.CANCELLED.value,
            CreditNoteModel.credit_note_date == credit_note_date,
            CreditNoteModel.vendor_id == hotel_id,
        )
        credit_notes = q.all()
        credit_note_details = [
            ErpInvoiceAndCreditNoteDto(credit_note) for credit_note in credit_notes
        ]
        return credit_note_details
