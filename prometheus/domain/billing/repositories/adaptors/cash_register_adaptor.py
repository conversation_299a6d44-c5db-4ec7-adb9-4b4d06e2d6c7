from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus.domain.billing.entities.cash_register import CashRegister
from prometheus.domain.billing.models import CashRegisterModel
from ths_common.value_objects import CashCounterAmount


class CashRegisterDBAdaptor:
    @staticmethod
    def to_db_model(cash_register):
        # noinspection PyArgumentList
        return CashRegisterModel(
            cash_register_id=cash_register.cash_register_id,
            cash_register_name=cash_register.cash_register_name,
            vendor_id=cash_register.vendor_id,
            opened_by=cash_register.opened_by,
            start_datetime=cash_register.start_datetime,
            end_datetime=cash_register.end_datetime,
            default_opening_balance=cash_register.default_opening_balance.to_json(),
            carry_balance_to_next_session=cash_register.carry_balance_to_next_session,
            base_currency=cash_register.base_currency.value,
            lastest_voucher_sequence_number=cash_register.lastest_voucher_sequence_number,
        )

    @staticmethod
    def to_entity(cashier_register_model):
        return CashRegister(
            cash_register_id=cashier_register_model.cash_register_id,
            cash_register_name=cashier_register_model.cash_register_name,
            start_datetime=dateutils.localize_datetime(
                cashier_register_model.start_datetime
            ),
            end_datetime=dateutils.localize_datetime(
                cashier_register_model.end_datetime
            ),
            vendor_id=cashier_register_model.vendor_id,
            default_opening_balance=CashCounterAmount(
                amounts=[
                    Money(amount)
                    for amount in cashier_register_model.default_opening_balance
                ]
            ),
            opened_by=cashier_register_model.opened_by,
            carry_balance_to_next_session=cashier_register_model.carry_balance_to_next_session,
            base_currency=CurrencyType(cashier_register_model.base_currency),
            lastest_voucher_sequence_number=cashier_register_model.lastest_voucher_sequence_number,
        )
