from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.billing.models import ChargeModel
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
)
from ths_common.value_objects import ChargeItem


class RawChargeDBAdapter(object):
    columns = (
        ChargeModel.charge_id,
        ChargeModel.pretax_amount,
        ChargeModel.tax_amount,
        ChargeModel.posttax_amount,
        ChargeModel.type,
        ChargeModel.bill_to_type,
        ChargeModel.item_id,
        ChargeModel.item_name,
        ChargeModel.sku_category_id,
        ChargeModel.item_detail,
        ChargeModel.status,
        ChargeModel.applicable_date,
        ChargeModel.comment,
        ChargeModel.charge_to,
        ChargeModel.posting_date,
        ChargeModel.split_allowed,
    )
    INDEX_FOR_STATUS = 10

    @staticmethod
    def to_entities(charge_rows, charge_splits_by_charge_id, base_currency):
        base_currency = (
            base_currency
            if isinstance(base_currency, CurrencyType)
            else CurrencyType(base_currency)
        )
        charges = []
        for charge_row in charge_rows:
            charge_item = ChargeItem(
                name=charge_row[7],
                sku_category_id=charge_row[8],
                item_id=charge_row[6],
                details=charge_row[9],
            )
            charge_split_type = None
            charge = Charge(
                charge_id=charge_row[0],
                pretax_amount=Money(charge_row[1], base_currency),
                tax_amount=Money(charge_row[2], base_currency),
                tax_details=None,
                posttax_amount=Money(charge_row[3], base_currency),
                charge_type=ChargeTypes(charge_row[4]) if charge_row[4] else None,
                bill_to_type=ChargeBillToTypes(charge_row[5])
                if charge_row[5]
                else None,
                status=ChargeStatus(charge_row[10]),
                applicable_date=dateutils.localize_datetime(charge_row[11]),
                comment=charge_row[12],
                created_by=None,
                charge_item=charge_item,
                charge_splits=charge_splits_by_charge_id.get(charge_row[0]),
                charge_split_type=charge_split_type,
                deleted=False,
                charge_to=charge_row[13],
                recorded_time=charge_row[14],
                split_allowed=charge_row[15],
                dirty=False,
                new=False,
            )
            charges.append(charge)

        return charges
