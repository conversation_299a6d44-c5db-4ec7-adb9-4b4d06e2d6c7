from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus.domain.billing.entities.cash_counter_amount import CashCounterBalance
from prometheus.domain.billing.entities.cashier_session import CashierSession
from prometheus.domain.billing.models import (
    CashierSessionBalanceModel,
    CashierSessionModel,
)
from ths_common.constants.billing_constants import CashierSessionStatus


class CashierSessionDBAdapter:
    @staticmethod
    def to_db_model(cashier_session):
        # noinspection PyArgumentList
        closing_balance_in_base_currency = (
            cashier_session.closing_balance_in_base_currency.amount
            if cashier_session.closing_balance_in_base_currency != None
            else None
        )
        return CashierSessionModel(
            cashier_session_id=cashier_session.cashier_session_id,
            session_number=cashier_session.session_number,
            start_datetime=cashier_session.start_datetime,
            end_datetime=cashier_session.end_datetime,
            status=cashier_session.status,
            opening_balance_in_base_currency=cashier_session.opening_balance_in_base_currency.amount,
            closing_balance_in_base_currency=closing_balance_in_base_currency,
            opened_by=cashier_session.opened_by,
            closed_by=cashier_session.closed_by,
            deleted=cashier_session.deleted,
            cash_register_id=cashier_session.cash_register_id,
            vendor_id=cashier_session.vendor_id,
        )

    @staticmethod
    def to_entity(cashier_session_model, base_currency):
        closing_balance_in_base_currency = (
            Money(cashier_session_model.closing_balance_in_base_currency, base_currency)
            if cashier_session_model.closing_balance_in_base_currency != None
            else None
        )
        return CashierSession(
            cashier_session_id=cashier_session_model.cashier_session_id,
            session_number=cashier_session_model.session_number,
            start_datetime=dateutils.localize_datetime(
                cashier_session_model.start_datetime
            ),
            end_datetime=dateutils.localize_datetime(
                cashier_session_model.end_datetime
            ),
            status=cashier_session_model.status,
            opening_balance_in_base_currency=Money(
                cashier_session_model.opening_balance_in_base_currency, base_currency
            ),
            closing_balance_in_base_currency=closing_balance_in_base_currency,
            opened_by=cashier_session_model.opened_by,
            closed_by=cashier_session_model.closed_by,
            cash_register_id=cashier_session_model.cash_register_id,
            vendor_id=cashier_session_model.vendor_id,
        )


class CashCounterBalanceDBAdaptor:
    @staticmethod
    def to_db_model(cash_counter_balance, cashier_session_id):
        # noinspection PyArgumentList
        return CashierSessionBalanceModel(
            amount=cash_counter_balance.amount,
            currency=cash_counter_balance.currency.value,
            balance_type=cash_counter_balance.balance_type,
            cashier_session_id=cashier_session_id,
            cash_counter_balance_id=cash_counter_balance.cash_counter_balance_id,
            deleted=cash_counter_balance.deleted,
        )

    @staticmethod
    def to_entity(cash_counter_balance_model):
        return CashCounterBalance(
            amount=cash_counter_balance_model.amount,
            currency=CurrencyType(cash_counter_balance_model.currency),
            balance_type=cash_counter_balance_model.balance_type,
            cash_counter_balance_id=cash_counter_balance_model.cash_counter_balance_id,
            deleted=cash_counter_balance_model.deleted,
        )
