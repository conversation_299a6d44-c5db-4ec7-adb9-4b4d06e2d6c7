from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.billing.entities.allowance import Allowance
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.models import AllowanceModel


class RawAllowanceDBAdaptor(object):
    columns = (
        AllowanceModel.allowance_id,
        AllowanceModel.charge_split_id,
        AllowanceModel.charge_id,
        AllowanceModel.tax_amount,
        AllowanceModel.pretax_amount,
        AllowanceModel.posttax_amount,
        AllowanceModel.invoice_id,
        AllowanceModel.credit_note_id,
        AllowanceModel.billed_entity_id,
        AllowanceModel.billed_entity_account_number,
        AllowanceModel.posting_date,
        AllowanceModel.status,
    )

    @staticmethod
    def to_entities(allowance_rows, base_currency):
        base_currency = (
            base_currency
            if isinstance(base_currency, CurrencyType)
            else CurrencyType(base_currency)
        )
        allowance_map = defaultdict(list)
        for allowance_row in allowance_rows:
            allowance_id = allowance_row[0]
            charge_split_id = allowance_row[1]
            charge_id = allowance_row[2]
            allowance = Allowance(
                allowance_id=allowance_id,
                pretax_amount=Money(allowance_row[4], base_currency),
                tax_amount=Money(allowance_row[3], base_currency),
                posttax_amount=Money(allowance_row[5], base_currency),
                tax_details=None,
                posting_date=allowance_row[10],
                status=allowance_row[11],
                invoice_id=allowance_row[6],
                credit_note_id=allowance_row[7],
                billed_entity_account=BilledEntityAccountVO(
                    billed_entity_id=allowance_row[8], account_number=allowance_row[9]
                ),
                deleted=False,
                dirty=False,
                new=False,
            )
            allowance_map[(charge_id, charge_split_id)].append(allowance)

        return allowance_map
