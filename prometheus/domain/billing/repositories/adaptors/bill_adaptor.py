import json

import jsonpickle
from treebo_commons.utils import dateutils

from prometheus.domain.billing.entities.bill import Bill
from prometheus.domain.billing.models import BillModel
from ths_common.constants.billing_constants import BillStatus
from ths_common.utils.common_utils import DateTimeEncoder
from ths_common.value_objects import GSTDetails, PhoneNumber, VendorDetails


class BillDBAdapter(object):
    @staticmethod
    def to_db_model(bill):
        # noinspection PyArgumentList
        return BillModel(
            bill_id=bill.bill_id,
            vendor_id=bill.vendor_id,
            app_id=bill.app_id,
            parent_reference_number=bill.parent_reference_number,
            vendor_details=bill.vendor_details.to_json()
            if bill.vendor_details
            else bill.vendor_details,
            parent_info=json.loads(json.dumps(bill.parent_info, cls=DateTimeEncoder))
            if bill.parent_info
            else bill.parent_info,
            bill_date=bill.bill_date,
            grace_period=0,
            gstin=None,
            fees=bill.fees,
            version=bill.version,
            deleted=bill.deleted,
            base_currency=bill.base_currency.value,
            status=bill.status.value if bill.status else None,
        )

    @staticmethod
    def to_entity(bill_model):
        vendor_details = bill_model.vendor_details
        parent_info = bill_model.parent_info
        if vendor_details:
            try:
                vendor_details = VendorDetails.from_json(
                    json.loads(vendor_details)
                    if isinstance(vendor_details, str)
                    else vendor_details
                )
            except:
                vendor_details = (
                    jsonpickle.decode(
                        vendor_details, keys=True, classes=[GSTDetails, PhoneNumber]
                    )
                    if vendor_details
                    else None
                )

        if parent_info:
            try:
                parent_info = (
                    json.loads(parent_info)
                    if isinstance(parent_info, str)
                    else parent_info
                )
            except:
                parent_info = jsonpickle.decode(parent_info) if parent_info else None

        return Bill(
            bill_id=bill_model.bill_id,
            bill_date=dateutils.localize_datetime(bill_model.bill_date),
            vendor_id=bill_model.vendor_id,
            app_id=bill_model.app_id,
            parent_reference_number=bill_model.parent_reference_number,
            vendor_details=vendor_details,
            parent_info=parent_info,
            fees=bill_model.fees,
            version=bill_model.version,
            deleted=bill_model.deleted,
            base_currency=bill_model.base_currency,
            status=BillStatus(bill_model.status) if bill_model.status else None,
        )
