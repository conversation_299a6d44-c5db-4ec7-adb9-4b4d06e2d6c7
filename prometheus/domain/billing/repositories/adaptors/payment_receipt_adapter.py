from prometheus.domain.billing.entities.payment_receipt import (
    PaymentReceipt,
    PaymentReceiptReceiverInfo,
)
from prometheus.domain.billing.models import PaymentReceiptModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.billing_constants import PaymentTypes


class PaymentReceiptAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: PaymentReceipt, **kwargs):
        payment_receipt_receiver_info = (
            domain_entity.payment_receipt_receiver_info.to_json()
            if domain_entity.payment_receipt_receiver_info
            else None
        )
        # noinspection PyArgumentList
        return PaymentReceiptModel(
            payment_id=domain_entity.payment_id,
            bill_id=domain_entity.bill_id,
            payment_receipt_date=domain_entity.payment_receipt_date,
            payment_receipt_number=domain_entity.payment_receipt_number,
            payment_type=domain_entity.payment_type.value
            if domain_entity.payment_type
            else None,
            affected_room_nos=domain_entity.affected_room_nos,
            payment_receipt_receiver_info=payment_receipt_receiver_info,
            vendor_id=domain_entity.vendor_id,
            payment_receipt_url=domain_entity.payment_receipt_url,
            signed_url=domain_entity.signed_url,
            signed_url_expiry_time=domain_entity.signed_url_expiry_time,
        )

    def to_domain_entity(self, db_entity: PaymentReceiptModel, **kwargs):
        payment_receipt_receiver_info = (
            PaymentReceiptReceiverInfo.from_json(
                db_entity.payment_receipt_receiver_info
            )
            if db_entity.payment_receipt_receiver_info
            else None
        )
        return PaymentReceipt(
            payment_id=db_entity.payment_id,
            bill_id=db_entity.bill_id,
            payment_receipt_date=db_entity.payment_receipt_date,
            payment_receipt_number=db_entity.payment_receipt_number,
            payment_type=PaymentTypes(db_entity.payment_type),
            payment_receipt_receiver_info=payment_receipt_receiver_info,
            vendor_id=db_entity.vendor_id,
            payment_receipt_url=db_entity.payment_receipt_url,
            affected_room_nos=db_entity.affected_room_nos,
            signed_url=db_entity.signed_url,
            signed_url_expiry_time=db_entity.signed_url_expiry_time,
        )
