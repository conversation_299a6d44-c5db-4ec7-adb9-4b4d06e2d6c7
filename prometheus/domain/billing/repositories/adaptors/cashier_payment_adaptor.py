from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.billing.entities.cash_counter_payment import CashierPayment
from prometheus.domain.billing.models import CashierPaymentModel
from ths_common.constants.billing_constants import (
    CashierPaymentStatus,
    CashierPaymentTypes,
)


class CashierPaymentDBAdapter(object):
    @staticmethod
    def to_db_models(cashier_session_id, cashier_payments):
        cashier_payment_models = []
        for payment in cashier_payments:
            model = CashierPaymentModel(
                payment_id=payment.payment_id,
                amount_in_base_currency=payment.amount.amount,
                cashier_session_id=cashier_session_id,
                date_of_payment=payment.date_of_payment,
                payment_mode=payment.payment_mode,
                payment_type=payment.payment_type.value,
                payment_details=payment.payment_details,
                status=payment.status.value,
                paid_to=payment.paid_to,
                comment=payment.comment,
                added_by=payment.added_by,
                payment_currency=payment.amount_in_payment_currency.currency.value,
                amount_in_payment_currency=payment.amount_in_payment_currency.amount,
                payment_mode_sub_type=payment.payment_mode_sub_type,
                booking_id=payment.booking_id,
                pos_order_id=payment.pos_order_id,
                voucher_url=payment.voucher_url,
                current_session_balance=payment.current_session_balance.amount,
                transaction_id=payment.transaction_id,
                booking_owner_name=payment.booking_owner_name,
                bill_id=payment.bill_id,
                bill_payment_id=payment.bill_payment_id,
                voucher_number=payment.voucher_number,
                booking_reference_number=payment.booking_reference_number,
            )
            cashier_payment_models.append(model)

        return cashier_payment_models

    @staticmethod
    def to_entities(payment_models, base_currency):
        payments = []
        for payment_model in payment_models:
            payment = CashierPayment(
                payment_id=payment_model.payment_id,
                date_of_payment=payment_model.date_of_payment,
                payment_mode=payment_model.payment_mode,
                payment_type=CashierPaymentTypes(payment_model.payment_type),
                payment_details=payment_model.payment_details,
                status=CashierPaymentStatus(payment_model.status),
                added_by=payment_model.added_by,
                paid_to=payment_model.paid_to,
                comment=payment_model.comment,
                amount=Money(
                    payment_model.amount_in_base_currency, CurrencyType(base_currency)
                ),
                amount_in_payment_currency=Money(
                    payment_model.amount_in_payment_currency,
                    CurrencyType(payment_model.payment_currency),
                ),
                payment_mode_sub_type=payment_model.payment_mode_sub_type,
                booking_id=payment_model.booking_id,
                voucher_url=payment_model.voucher_url,
                pos_order_id=payment_model.pos_order_id,
                current_session_balance=Money(
                    payment_model.current_session_balance, CurrencyType(base_currency)
                ),
                transaction_id=payment_model.transaction_id,
                booking_owner_name=payment_model.booking_owner_name,
                bill_id=payment_model.bill_id,
                bill_payment_id=payment_model.bill_payment_id,
                voucher_number=payment_model.voucher_number,
                booking_reference_number=payment_model.booking_reference_number,
            )
            payments.append(payment)
        return payments
