from object_registry import register_instance
from prometheus.domain.billing.aggregates.card_aggregate import CardAggregate
from prometheus.domain.billing.models import CardModel
from prometheus.domain.billing.repositories.adaptors.card_adaptor import CardAdaptor
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.exceptions import ResourceNotFound


@register_instance()
class CardRepository(BaseRepository):
    card_adaptor = CardAdaptor()

    def to_aggregate(self, **kwargs):
        card_model = kwargs.get("card_model")
        card_entity = self.card_adaptor.to_domain_entity(db_entity=card_model)
        return CardAggregate(card_entity=card_entity)

    def from_aggregate(self, aggregate: CardAggregate = None):
        card_model = self.card_adaptor.to_db_entity(domain_entity=aggregate.card)
        return card_model

    def save(self, card_aggregate: CardAggregate):
        self._save(self.from_aggregate(aggregate=card_aggregate))
        self.flush_session()

    def load(self, card_id, bill_id):
        q = self.query(CardModel).filter(
            CardModel.card_id == card_id, CardModel.bill_id == bill_id
        )
        card_model = q.one_or_none()
        if card_model is None:
            raise ResourceNotFound("Card", card_id)

        return self.to_aggregate(card_model=card_model)

    def load_all(self, bill_id):
        card_models = self.query(CardModel).filter(CardModel.bill_id == bill_id).all()
        return [self.to_aggregate(card_model=card_model) for card_model in card_models]
