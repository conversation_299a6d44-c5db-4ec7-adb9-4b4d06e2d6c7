from transitions import Machine


class BillingTransitions:
    def __init__(self, name):
        self.name = name


states = ['created', 'consumed', 'cancelled']


transitions = [
    {'trigger': 'put', 'source': 'created', 'dest': 'cancelled'},
    {'trigger': 'put', 'source': 'created', 'dest': 'consumed'},
    {'trigger': 'put', 'source': 'consumed', 'dest': 'cancelled'},
]

# fix this properly
# pylint: disable=undefined-variable
machine = Machine(lump, states=states, transitions=transitions, initial='created')
