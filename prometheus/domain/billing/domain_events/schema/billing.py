from marshmallow import Schema, fields
from treebo_commons.money.money_field import MoneyField

__all__ = [
    'PaymentEventSchema',
    'BillCreatedEventSchema',
    'BillAmountUpdatedEventSchema',
    'StayChargeItemEventDataSchema',
    'InvoiceGeneratedEventSchema',
    'InvoiceRegeneratedEventSchema',
    'PreviewInvoiceGeneratedEventSchema',
    'InvoiceBillToUpdateEventSchema',
    'ChargeEventDataSchema',
    'ChargeDetailsModifiedDataSchema',
    'ChargeModifiedEventSchema',
    'ChargeCancelledEventSchema',
    'ChargeAddedEventSchema',
    'PaymentModifiedEventSchema',
    'AttributeModifiedDataSchema',
    'AllowanceEventSchema',
    'BilledEntityAccountEventSchema',
    'ChargeTransferredEventSchema',
    'ChargePostedEventSchema',
    'ChargeSplitModifiedEventSchema',
    'PaymentAddedEventSchema',
    'PaymentSplitModifiedEventSchema',
    'AllowancePostedEventSchema',
    'ChargeDeletedEventSchema',
    'ChargeReinstatedEventSchema',
    'ChargePaymentInstructionsModifiedEventSchema',
    'ChargeBilledEntityModifiedEventSchema',
    'ChargeInvoicedEventSchema',
    'BilledEntityUpdatedEventSchema',
    'SpotCreditEventSchema',
    'CreditNoteGeneratedEventSchema',
    'CreditShellEventSchema',
]


class BilledEntityAccountEventSchema(Schema):
    billed_entity_id = fields.Integer(required=True)
    account_number = fields.Integer()


class PaymentSplitEventSchema(Schema):
    payment_split_id = fields.Integer()
    amount = MoneyField()
    billed_entity_account = fields.Nested(BilledEntityAccountEventSchema)
    billed_entity_name = fields.String()


class PaymentEventSchema(Schema):
    payment_id = fields.Integer()
    paid_by = fields.String()
    paid_to = fields.String()
    payment_mode = fields.String()
    payment_mode_sub_type = fields.String()
    amount = MoneyField()
    date_of_payment = fields.LocalDateTime()
    payment_type = fields.String()
    amount_in_payment_currency = MoneyField()
    payer = fields.String()
    payment_reference_id = fields.String()
    remarks = fields.String()
    confirmed = fields.Boolean()
    billed_entity_names = fields.List(fields.String)
    billed_entity_accounts = fields.Nested(BilledEntityAccountEventSchema, many=True)
    payor_billed_entity_name = fields.String()


class SpotCreditEventSchema(Schema):
    source_billed_entity_account = fields.Nested(BilledEntityAccountEventSchema)
    destination_billed_entity_account = fields.Nested(BilledEntityAccountEventSchema)
    unpaid_charges = MoneyField()
    settlement_type = fields.String()
    source_billed_entity_name = fields.String()
    destination_billed_entity_name = fields.String()


class PaymentAddedEventSchema(PaymentEventSchema):
    payment_splits = fields.Nested(PaymentSplitEventSchema, many=True)

    class Meta:
        exclude = ['billed_entity_names', 'billed_entity_accounts']


class PaymentTransferDataSchema(Schema):
    from_account = fields.String()
    to_account = fields.String()
    transferred_amount = MoneyField()


class PaymentRedistributedEventSchema(Schema):
    payment_id = fields.Integer()
    details = fields.Nested(PaymentTransferDataSchema, many=True)


class AttributeModifiedDataSchema(Schema):
    attribute = fields.String()
    old_value = fields.String()
    new_value = fields.String()


class DataModifiedSchema(Schema):
    old_value = fields.String()
    new_value = fields.String()


class RefundAccountAttributeModifiedDataSchema(Schema):
    attribute = fields.String()
    old_value = fields.Nested(BilledEntityAccountEventSchema)
    new_value = fields.Nested(BilledEntityAccountEventSchema)


class PaymentModifiedEventSchema(Schema):
    payment_id = fields.Integer()
    details = fields.Nested(AttributeModifiedDataSchema, many=True)


class PaymentTypeEditedEventSchema(Schema):
    payment_id = fields.Integer()
    payment_mode = fields.String()
    payment_reference_id = fields.String()
    amount = MoneyField()
    billed_entity_names = fields.List(fields.String)
    details = fields.Nested(DataModifiedSchema, many=True)
    payor_billed_entity_name = fields.String()


class PaymentModeEditedEventSchema(Schema):
    payment_id = fields.Integer()
    payment_type = fields.String()
    payment_reference_id = fields.String()
    amount = MoneyField()
    billed_entity_names = fields.List(fields.String)
    details = fields.Nested(DataModifiedSchema)
    payor_billed_entity_name = fields.String()


class PaymentAmountEditedEventSchema(Schema):
    payment_id = fields.Integer()
    payment_mode = fields.String()
    payment_type = fields.String()
    payment_reference_id = fields.String()
    billed_entity_names = fields.List(fields.String)
    details = fields.Nested(DataModifiedSchema)
    payor_billed_entity_name = fields.String()


class PaymentBilledEntityEditedEventSchema(Schema):
    payment_id = fields.Integer()
    payment_mode = fields.String()
    payment_type = fields.String()
    payment_reference_id = fields.String()
    billed_entity_names = fields.List(fields.String)
    details = fields.Nested(AttributeModifiedDataSchema)
    payor_billed_entity_name = fields.String()


class RefundAmountEditedEventSchema(Schema):
    payment_id = fields.Integer()
    payment_mode = fields.String()
    payment_type = fields.String()
    payment_reference_id = fields.String()
    billed_entity_names = fields.List(fields.String)
    details = fields.Nested(DataModifiedSchema)
    payor_billed_entity_name = fields.String()


class RefundAccountEditedEventSchema(Schema):
    payment_id = fields.Integer()
    payment_mode = fields.String()
    payment_type = fields.String()
    amount = MoneyField()
    payment_reference_id = fields.String()
    billed_entity_names = fields.List(fields.String)
    details = fields.Nested(RefundAccountAttributeModifiedDataSchema)
    payor_billed_entity_name = fields.String()


class RefundModeTypeEditedEventSchema(Schema):
    payment_id = fields.Integer()
    amount = MoneyField()
    payment_reference_id = fields.String()
    billed_entity_names = fields.List(fields.String)
    details = fields.Nested(AttributeModifiedDataSchema, many=True)
    payor_billed_entity_name = fields.String()


class BillCreatedEventSchema(Schema):
    bill_amount = MoneyField()
    payments = fields.Nested(PaymentAddedEventSchema, many=True)


class BillAmountUpdatedEventSchema(Schema):
    old_bill_amount = MoneyField()
    new_bill_amount = MoneyField()


class StayChargeItemEventDataSchema(Schema):
    room_type = fields.String()
    room_number = fields.String()


class InvoiceCancelledEventSchema(Schema):
    invoice_id = fields.String()
    invoice_number = fields.String()
    invoice_amount = MoneyField()
    status = fields.String()


class InvoiceGeneratedEventSchema(Schema):
    invoice_id = fields.String()
    invoice_number = fields.String()
    invoice_amount = MoneyField()
    stay_charge_item_details = fields.Nested(StayChargeItemEventDataSchema, many=True)
    status = fields.String()


class InvoiceRegeneratedEventSchema(Schema):
    invoice_id = fields.String()
    invoice_number = fields.String()
    invoice_amount = MoneyField()
    status = fields.String()


class PreviewInvoiceGeneratedEventSchema(Schema):
    invoice_id = fields.String()
    invoice_number = fields.String()
    invoice_amount = MoneyField()
    stay_charge_item_details = fields.Nested(StayChargeItemEventDataSchema, many=True)


class InvoiceBillToUpdateEventSchema(Schema):
    invoice_id = fields.String()
    old_bill_to = fields.String()
    new_bill_to = fields.String()


class ChargeEventDataSchema(Schema):
    charge_id = fields.Integer()
    guest_ids = fields.List(fields.String)
    consuming_guest_names = fields.List(fields.String)
    room_stay_id = fields.String()
    charge_type = fields.String()
    bill_to_type = fields.String()
    posttax_amount = MoneyField()
    applicable_date = fields.LocalDateTime()
    item_name = fields.String()
    room_number = fields.String()
    sku_category = fields.String()
    status = fields.String()


class ChargeDetailsModifiedDataSchema(Schema):
    attribute = fields.String()
    old_value = fields.String()
    new_value = fields.String()


class ChargeModifiedEventSchema(Schema):
    charge_id = fields.Integer()
    details = fields.Nested(ChargeDetailsModifiedDataSchema, many=True)
    applicable_date = fields.LocalDateTime()
    room_number = fields.String()
    item_name = fields.String()
    sku_category = fields.String()
    rate_plan_name = fields.String()


class ChargeBilledEntityModifiedEventSchema(Schema):
    charge_id = fields.Integer()
    room_stay_id = fields.String()
    charge_type = fields.String()
    posttax_amount = MoneyField()
    applicable_date = fields.LocalDateTime()
    item_name = fields.String()
    room_number = fields.String()
    sku_category = fields.String()
    details = fields.Nested(ChargeDetailsModifiedDataSchema, many=True)


class ChargePaymentInstructionsModifiedEventSchema(Schema):
    charge_id = fields.Integer()
    room_stay_id = fields.String()
    charge_type = fields.String()
    posttax_amount = MoneyField()
    applicable_date = fields.LocalDateTime()
    item_name = fields.String()
    room_number = fields.String()
    sku_category = fields.String()
    billed_entity_names = fields.List(fields.String)
    details = fields.Nested(ChargeDetailsModifiedDataSchema, many=True)
    billed_entity_accounts = fields.Nested(BilledEntityAccountEventSchema, many=True)


class ChargeCancelledEventSchema(ChargeEventDataSchema):
    billed_entity_accounts = fields.Nested(BilledEntityAccountEventSchema, many=True)
    billed_entity_names = fields.List(fields.String)
    is_rate_plan_charge = fields.Boolean()


class ChargeDeletedEventSchema(ChargeEventDataSchema):
    billed_entity_accounts = fields.Nested(BilledEntityAccountEventSchema, many=True)
    billed_entity_names = fields.List(fields.String)
    is_rate_plan_charge = fields.Boolean()


class AllowanceEventSchema(Schema):
    charge_id = fields.Integer()
    sku_category = fields.String()
    charge_split_id = fields.Integer()
    allowance_id = fields.Integer()
    item_name = fields.String()
    applicable_date = fields.LocalDateTime()
    room_number = fields.String()
    posttax_amount = MoneyField()
    remarks = fields.String()
    billed_entity_name = fields.String()
    billed_entity_account = fields.Nested(BilledEntityAccountEventSchema)


class AllowancePostedEventSchema(AllowanceEventSchema):
    posting_date = fields.Date()


class AttributeUpdateSchema(Schema):
    attribute = fields.String()
    old_value = fields.String()
    new_value = fields.String()


class BilledEntityUpdatedEventSchema(Schema):
    billed_entity_id = fields.String()
    details = fields.Nested(AttributeUpdateSchema, many=True)


class ChargeTransferredEventSchema(Schema):
    charge_id = fields.Integer()
    sku_category = fields.String()
    item_name = fields.String()
    applicable_date = fields.LocalDateTime()
    room_number = fields.String()
    destination_booking_id = fields.String()
    destination_booker_name = fields.String()
    destination_room_number = fields.String()


class ChargePostedEventSchema(ChargeEventDataSchema):
    billed_entity_accounts = fields.Nested(BilledEntityAccountEventSchema, many=True)
    billed_entity_names = fields.List(fields.String)
    posting_date = fields.Date()


class ChargeSplitEventSchema(Schema):
    charge_split_id = fields.Integer()
    posttax_amount = fields.String()
    percentage = fields.String()
    charge_type = fields.String()
    bill_to_type = fields.String()
    billed_entity_account = fields.Nested(BilledEntityAccountEventSchema)
    billed_entity_name = fields.String()
    invoice_id = fields.String()
    invoiced_date = fields.Date()


class ChargeSplitModifiedEventSchema(Schema):
    charge_id = fields.Integer()
    sku_category = fields.String()
    item_name = fields.String()
    applicable_date = fields.LocalDateTime()
    room_number = fields.String()
    old_splits = fields.Nested(ChargeSplitEventSchema, many=True)
    new_splits = fields.Nested(ChargeSplitEventSchema, many=True)


class ChargeAddedEventSchema(ChargeEventDataSchema):
    charge_splits = fields.Nested(ChargeSplitEventSchema, many=True)


class ChargeInvoicedEventSchema(ChargeEventDataSchema):
    billed_entity_accounts = fields.Nested(BilledEntityAccountEventSchema, many=True)
    charge_splits = fields.Nested(ChargeSplitEventSchema, many=True)


class PaymentSplitModifiedEventSchema(Schema):
    payment_id = fields.Integer()
    old_splits = fields.Nested(PaymentSplitEventSchema, many=True)
    new_splits = fields.Nested(PaymentSplitEventSchema, many=True)


class ChargeReinstatedEventSchema(ChargeEventDataSchema):
    charge_splits = fields.Nested(ChargeSplitEventSchema, many=True)


class CreditNoteGeneratedEventSchema(Schema):
    original_invoice_id = fields.String()
    credit_note_id = fields.String()


class CreditShellEventSchema(Schema):
    bill_id = fields.String()
    booking_id = fields.String()
    credit_shell_id = fields.String()
    amount = MoneyField()
    total_credit = MoneyField()
    remaining_credit = MoneyField()
    used_credit = MoneyField()
    folio_number = fields.String()
    credit_shell_refund_id = fields.String()
    payment_mode = fields.String()
    remarks = fields.String()
    owner_name = fields.String()
