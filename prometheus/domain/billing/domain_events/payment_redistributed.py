from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    PaymentRedistributedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException


class PaymentRedistributedEvent(MergeableDomainEvent):
    def __init__(self, payment_id, from_account, to_account, transferred_amount):
        self.payment_id = payment_id
        self.details = [
            {
                "from_account": from_account,
                "to_account": to_account,
                "transferred_amount": transferred_amount,
            }
        ]

    def serialize(self):
        serialized = PaymentRedistributedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.PAYMENT_REDISTRIBUTED

    def merge(self, mergeable_domain_event):
        if not isinstance(mergeable_domain_event, PaymentRedistributedEvent):
            raise DomainEventMergerException()

        self.details.extend(mergeable_domain_event.details)

    def can_merge(self, mergeable_domain_event):
        return (
            isinstance(mergeable_domain_event, PaymentRedistributedEvent)
            and self.payment_id == mergeable_domain_event.payment_id
        )
