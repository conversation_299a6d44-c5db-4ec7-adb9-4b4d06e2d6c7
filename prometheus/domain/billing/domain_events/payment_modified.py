from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    PaymentModifiedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException


class PaymentModifiedEvent(MergeableDomainEvent):
    def __init__(self, payment_id, attribute, old_value, new_value):
        self.payment_id = payment_id
        self.details = [
            {"attribute": attribute, "old_value": old_value, "new_value": new_value}
        ]

    def serialize(self):
        serialized = PaymentModifiedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.PAYMENT_MODIFIED

    def merge(self, mergeable_domain_event):
        if not isinstance(mergeable_domain_event, PaymentModifiedEvent):
            raise DomainEventMergerException()

        self.details.extend(mergeable_domain_event.details)

    def can_merge(self, mergeable_domain_event):
        return (
            isinstance(mergeable_domain_event, PaymentModifiedEvent)
            and self.payment_id == mergeable_domain_event.payment_id
        )
