from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    InvoiceCancelledEventSchema,
)
from prometheus.domain.billing.dto.invoice_event_data import InvoiceEventData
from ths_common.constants.domain_event_constants import DomainEvent


class InvoiceCancelledEvent(MergeableDomainEvent):
    def __init__(self, invoice_event_data: InvoiceEventData):
        self.invoice_event_data = [invoice_event_data]

    def serialize(self):
        serialized = (
            InvoiceCancelledEventSchema(many=True).dump(self.invoice_event_data).data
        )
        return serialized

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.INVOICE_CANCELLED

    def merge(self, mergeable_domain_event):
        self.invoice_event_data.extend(mergeable_domain_event.invoice_event_data)

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, InvoiceCancelledEvent)
