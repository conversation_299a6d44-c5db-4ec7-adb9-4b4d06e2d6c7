"""
Charge Modified Domain Event
"""

import logging

from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    ChargeModifiedEventSchema,
)
from prometheus.domain.billing.dto.charge_modified_event_data import (
    ChargeModifiedEventData,
)
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException

logger = logging.getLogger(__name__)


class ChargeModifiedEvent(MergeableDomainEvent):
    def __init__(self, charge, attribute, old_value, new_value):
        self.charge = charge
        self.charge_event_data = None
        self.details = [
            {"attribute": attribute, "old_value": old_value, "new_value": new_value}
        ]

    def serialize(self):
        serialized = ChargeModifiedEventSchema().dump(self.charge_event_data).data
        return serialized

    def update_mapping(self, **kwargs):
        customer_map = kwargs.get("customer_map")
        folio_name_map = kwargs.get('folio_name_map')
        sku_category_map = kwargs.get('sku_category_map')

        self.charge_event_data = ChargeModifiedEventData(
            charge_id=self.charge.charge_id,
            applicable_date=self.charge.applicable_date,
            item_name=self.charge.item.name,
            status=self.charge.status,
            sku_category_id=self.charge.item.sku_category_id,
            posttax_amount=self.charge.posttax_amount,
            room_number=self.charge.item.details.get('room_no'),
        )
        for index, detail in enumerate(self.details):
            if detail["attribute"] == "consuming_guests":
                old_value = [
                    customer_map.get(guest_id).name.full_name
                    for guest_id in detail["old_value"]
                ]
                new_value = [
                    customer_map.get(guest_id).name.full_name
                    for guest_id in detail["new_value"]
                ]
                self.charge_event_data.details.append(
                    {
                        "attribute": "consuming_guest_names",
                        "old_value": old_value,
                        "new_value": new_value,
                    }
                )
                break
            if detail["attribute"] == "billed_entity_accounts":
                self.charge_event_data.details.append(
                    {
                        "attribute": "billed_entity_names",
                        "old_value": ' | '.join(
                            [
                                folio_name_map.get(billed_entity_account)
                                for billed_entity_account in detail["old_value"]
                            ]
                        ),
                        "new_value": ' | '.join(
                            [
                                folio_name_map.get(billed_entity_account)
                                for billed_entity_account in detail["new_value"]
                            ]
                        ),
                    }
                )
                break
        self.charge_event_data.details.extend(
            [
                x
                for x in self.details
                if x['attribute'] not in ['consuming_guests', 'billed_entity_accounts']
            ]
        )

        if self.charge.is_rate_plan_charge():
            self.charge_event_data.set_rate_plan_name(
                self.charge.item.details.get('rate_plan_name')
            )
        self.charge_event_data.set_sku_category(sku_category_map)

    def event_type(self):
        return DomainEvent.CHARGE_MODIFIED

    def merge(self, mergeable_domain_event):
        if not isinstance(mergeable_domain_event, ChargeModifiedEvent):
            raise DomainEventMergerException()

        self.details.extend(mergeable_domain_event.details)

    def can_merge(self, mergeable_domain_event):
        return (
            isinstance(mergeable_domain_event, ChargeModifiedEvent)
            and self.charge.charge_id == mergeable_domain_event.charge.charge_id
        )

    @staticmethod
    def _get_billed_entity_name(billed_entity_map, billed_entity_account):
        return billed_entity_map.get(
            billed_entity_account.billed_entity_id
        ).get_name_with_account_number(billed_entity_account.account_number)
