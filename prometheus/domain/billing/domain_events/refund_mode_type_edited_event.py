from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    RefundModeTypeEditedEventSchema,
)
from prometheus.domain.billing.dto.refund_mode_type_edited_event import (
    RefundModeTypeEditedEventData,
)
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException


class RefundModeTypeEditedEvent(MergeableDomainEvent):
    def __init__(self, payment, attribute, old_value, new_value):
        self.payment = payment
        self.refund = None
        self.details = [
            {"attribute": attribute, "old_value": old_value, "new_value": new_value}
        ]

    def serialize(self):
        serialized = RefundModeTypeEditedEventSchema().dump(self.refund).data
        return serialized

    def update_mapping(self, **kwargs):
        folio_name_map = kwargs.get('folio_name_map')
        billed_entity_accounts = [
            ps.billed_entity_account for ps in self.payment.payment_splits
        ]

        self.refund = RefundModeTypeEditedEventData(
            payment_id=self.payment.payment_id,
            payment_reference_id=self.payment.payment_ref_id,
            amount=self.payment.amount,
        )

        self.refund.set_billed_entity_names_using_folio(
            folio_name_map, billed_entity_accounts
        )
        self.refund.set_details(self.details)

    def event_type(self):
        return DomainEvent.REFUND_MODE_TYPE_EDITED

    def merge(self, mergeable_domain_event):
        if not isinstance(mergeable_domain_event, RefundModeTypeEditedEvent):
            raise DomainEventMergerException()

        self.details.extend(mergeable_domain_event.details)

    def can_merge(self, mergeable_domain_event):
        return (
            isinstance(mergeable_domain_event, RefundModeTypeEditedEvent)
            and self.payment.payment_id == mergeable_domain_event.payment.payment_id
        )
