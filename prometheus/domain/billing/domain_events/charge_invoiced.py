"""
Charge Invoiced Domain Event
"""
from typing import <PERSON>ple

from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    ChargeInvoicedEventSchema,
)
from prometheus.domain.billing.dto.charge_event_data import ChargeEventData
from prometheus.domain.billing.dto.charge_split_event_data import ChargeSplitEventData
from ths_common.constants.domain_event_constants import DomainEvent


class ChargeInvoicedEvent(MergeableDomainEvent):
    def __init__(self, charge, charge_split, invoice):
        self.charge_data = [(charge, charge_split, invoice)]
        self.charge_event_data = []

    def serialize(self):
        serialized = (
            ChargeInvoicedEventSchema(many=True).dump(self.charge_event_data).data
        )
        return serialized

    def update_mapping(self, **kwargs):
        customer_map = kwargs.get('customer_map')
        folio_name_map = kwargs.get('folio_name_map')
        sku_category_map = kwargs.get('sku_category_map')
        for charge, charge_split, invoice in self.charge_data:
            self.charge_event_data.append(
                ChargeEventData(
                    posttax_amount=charge.posttax_amount,
                    item_name=charge.item.name,
                    applicable_date=charge.applicable_date,
                    charge_type=charge_split.charge_type,
                    bill_to_type=charge_split.bill_to_type,
                    posting_date=charge.posting_date,
                    status=charge.status,
                    room_stay_id=charge.item.details.get('room_stay_id'),
                    guest_ids=charge.charge_to,
                    charge_id=charge.charge_id,
                    sku_category_id=charge.item.sku_category_id,
                    room_number=charge.item.details.get('room_no'),
                    billed_entity_accounts=[
                        c.billed_entity_account
                        for c in charge.charge_splits
                        if c.billed_entity_account
                    ],
                    charge_splits=[
                        ChargeSplitEventData(
                            charge_split_id=charge_split.charge_split_id,
                            posttax_amount=charge_split.posttax_amount_post_allowance,
                            percentage=charge_split.percentage,
                            charge_type=charge_split.charge_type,
                            bill_to_type=charge_split.bill_to_type,
                            billed_entity_account=charge_split.billed_entity_account,
                            invoice_id=invoice.invoice_id,
                            invoiced_date=invoice.invoice_date,
                        )
                    ],
                )
            )
        for charge in self.charge_event_data:
            charge.set_guest_names(customer_map)
            charge.set_sku_category(sku_category_map)
            charge.set_folio_name_in_charge_splits(folio_name_map=folio_name_map)

    def event_type(self):
        return DomainEvent.CHARGE_INVOICED

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, ChargeInvoicedEvent)

    def merge(self, mergeable_domain_event):
        self.charge_data.extend(mergeable_domain_event.charge_data)
