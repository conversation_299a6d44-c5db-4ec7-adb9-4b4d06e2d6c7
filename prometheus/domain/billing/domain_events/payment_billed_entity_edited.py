from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    PaymentBilledEntityEditedEventSchema,
)
from prometheus.domain.billing.dto.payment_billed_entity_event_data import (
    PaymentBilledEntityEventData,
)
from ths_common.constants.domain_event_constants import DomainEvent


class PaymentBilledEntityEditedEvent(BaseDomainEvent):
    def __init__(self, payment, old_value, new_value):
        self.payment = payment
        self.payment_event_data = None
        self.details = {"old_value": old_value, "new_value": new_value}

    def serialize(self):
        serialized = (
            PaymentBilledEntityEditedEventSchema().dump(self.payment_event_data).data
        )
        return serialized

    def update_mapping(self, **kwargs):
        folio_name_map = kwargs.get('folio_name_map')
        billed_entity_accounts = [
            ps.billed_entity_account for ps in self.payment.payment_splits
        ]

        self.payment_event_data = PaymentBilledEntityEventData(
            payment_id=self.payment.payment_id,
            payment_reference_id=self.payment.payment_ref_id,
            payment_mode=self.payment.payment_mode,
            payment_type=self.payment.payment_type,
        )

        self.details = {
            "old_value": ' | '.join(
                [
                    folio_name_map.get(billed_entity_account)
                    for billed_entity_account in self.details["old_value"]
                ]
            ),
            "new_value": ' | '.join(
                [
                    folio_name_map.get(billed_entity_account)
                    for billed_entity_account in self.details["new_value"]
                ]
            ),
        }

        self.payment_event_data.set_details(self.details)
        self.payment_event_data.set_billed_entity_names_using_folio(
            folio_name_map, billed_entity_accounts
        )

    def event_type(self):
        return DomainEvent.PAYMENT_BILLED_ENTITY_EDITED

    @staticmethod
    def _get_billed_entity_details(billed_entity_map, billed_entity_account):
        return billed_entity_map.get(
            billed_entity_account.billed_entity_id
        ).get_name_with_account_number(billed_entity_account.account_number)
