"""
Charge Deleted Domain Event
"""
from typing import List

from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    ChargeDeletedEventSchema,
)
from prometheus.domain.billing.dto.charge_event_data import ChargeEventData
from ths_common.constants.domain_event_constants import DomainEvent


class ChargeDeletedEvent(MergeableDomainEvent):
    def __init__(self, charges):
        self.charges = charges
        self.charge_event_data = []

    def serialize(self):
        serialized = (
            ChargeDeletedEventSchema(many=True).dump(self.charge_event_data).data
        )
        return serialized

    def update_mapping(self, **kwargs):
        customer_map = kwargs.get('customer_map')
        sku_category_map = kwargs.get('sku_category_map')
        folio_name_map = kwargs.get('folio_name_map')
        for charge in self.charges:
            guest_ids = charge.charge_to
            if not guest_ids:
                guest_ids = [c.charge_to for c in charge.charge_splits if c.charge_to]
            self.charge_event_data.append(
                ChargeEventData(
                    charge_id=charge.charge_id,
                    item_name=charge.item.name,
                    sku_category_id=charge.item.sku_category_id,
                    posttax_amount=charge.posttax_amount,
                    charge_type=charge.type,
                    applicable_date=charge.applicable_date,
                    guest_ids=guest_ids,
                    bill_to_type=charge.bill_to_type,
                    room_stay_id=charge.item.details.get('room_stay_id'),
                    room_number=charge.item.details.get('room_no'),
                    billed_entity_accounts=[
                        cs.billed_entity_account
                        for cs in charge.charge_splits
                        if cs.billed_entity_account
                    ],
                    is_rate_plan_charge=charge.is_rate_plan_charge(),
                )
            )

        for charge in self.charge_event_data:
            charge.set_guest_names(customer_map)
            charge.set_sku_category(sku_category_map)
            charge.set_billed_entity_names(folio_name_map=folio_name_map)

    def event_type(self):
        return DomainEvent.CHARGE_DELETED

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, ChargeDeletedEvent)

    def merge(self, mergeable_domain_event):
        self.charges.extend(mergeable_domain_event.charges)
