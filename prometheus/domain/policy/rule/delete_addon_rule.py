from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.addon_facts import AddonFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.exceptions import PolicyAuthException


class DeleteAddonRule(BaseRule):
    def allow(self, facts: AddonFacts, privileges=None):
        if facts.user_type_fdm_or_aom() and (facts.is_past_dated_addon()):
            raise PolicyAuthException(error=PolicyError.CAN_NOT_DELETE_PAST_DATED_ADDON)
        if (
            facts.user_type_cr() or facts.user_type_fdm_or_aom()
        ) and facts.room_stay_checked_out(facts.addon.room_stay_id):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORISED_FOR_CHECKED_OUT_ROOMS
            )
        if not (
            facts.user_type_super_admin()
            or facts.user_type_cr()
            or facts.user_type_fdm_or_aom()
        ):
            raise PolicyAuthException(error=PolicyError.NOT_AUTHORIZED)
        return True
