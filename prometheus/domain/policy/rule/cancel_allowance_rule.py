from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.allowance_facts import AllowanceFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class CancelAllowanceRule(BaseRule):
    SELF = "Self"
    ANYONE = "Anyone"

    def allow(self, facts: AllowanceFacts, privileges=None):
        if PrivilegeCode.CAN_CANCEL_ALLOWANCE_PASSED_BY not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_CANCEL_ALLOWANCE
            )

        # TODO: Add self and anyone check, once we start storing created_by_auth_id in allowance
