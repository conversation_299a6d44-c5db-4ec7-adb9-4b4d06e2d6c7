from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class ChangeBookingOwnerDetailRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        # todo: privilege yet not defined from product
        if (
            facts.is_first_invoice_generated()
            and PrivilegeCode.CAN_EDIT_BOOKING_OWNER_AFTER_INVOICE_GENERATION
            not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.BOOKING_OWNER_CHANGE_NOT_ALLOWED_AFTER_FIRST_INVOICE_GENERATION
            )

        # if facts.is_gstin_added_or_removed() and facts.is_first_invoice_generated():
        #     raise PolicyAuthException(
        #         error=PolicyError.BOOKING_OWNER_GSTIN_CHANGE_NOT_ALLOWED_AFTER_FIRST_INVOICE_GENERATION)

        if (
            not facts.hotel_live()
            and PrivilegeCode.FULL_ACCESS_IF_HOTEL_NOT_LIVE not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.BOOKING_OWNER_CHANGE_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE
            )

        if PrivilegeCode.EDIT_BOOKING_FOR_CH not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_CHANGE_BOOKING_OWNER_DETAILS
            )

        if (
            facts.get_channel_code()
            not in privileges[PrivilegeCode.EDIT_BOOKING_FOR_CH]
        ):
            raise PolicyAuthException(
                error=PolicyError.BOOKING_OWNER_CHANGE_NOT_ALLOWED_FOR_THIS_CHANNEL
            )

        return True
