from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class ReissueInvoiceWithoutBuySide(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.CAN_REISSUE_INVOICE_WITHOUT_BUYSIDE not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_REISSUE_INVOICE_WITHOUT_BUY_SIDE
            )
        return True
