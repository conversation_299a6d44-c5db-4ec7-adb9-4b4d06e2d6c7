from typing import Union

from prometheus import crs_context
from prometheus.domain.policy.facts import RoomStayFacts
from prometheus.domain.policy.facts.new_booking_facts import NewBookingFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import UserType


class OverBookingRule(BaseRule):
    def allow(self, facts: Union[NewBookingFacts, RoomStayFacts], privileges=None):
        if crs_context.is_pis_tenant():
            return True
        if facts.is_booking_received_from_treebo_pms():
            return False
        if facts.user_type == UserType.CRS_MIGRATION_USER:
            return True
        if facts.is_booking_received_from_su():
            return True
        if facts.is_direct_booking_received():
            return True
        if facts.is_ota_booking_received():
            return True
        if not crs_context.is_treebo_tenant():
            return True
        return False
