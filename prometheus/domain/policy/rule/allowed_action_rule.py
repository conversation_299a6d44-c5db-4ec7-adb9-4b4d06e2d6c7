from prometheus.domain.policy.facts.allowed_action_facts import (
    AllowedBookingActionFacts,
)
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode


class BookingStateTransitionRule(BaseRule):
    def allow(self, facts: AllowedBookingActionFacts, privileges=None):
        if facts.is_walkin_booking():
            allowed_cancellation_time = privileges.get(
                PrivilegeCode.CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME
            )
            if allowed_cancellation_time:
                allowed_cancellation_time = allowed_cancellation_time[0]
                return facts.can_cancel_booking(allowed_cancellation_time)
        return True
