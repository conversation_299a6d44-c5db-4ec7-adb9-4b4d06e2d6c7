from prometheus import crs_context
from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.expense_facts import ExpenseFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class CreateExpenseRule(BaseRule):
    def allow(self, facts: ExpenseFacts, privileges=None):
        if PrivilegeCode.ADD_EXTRA_CHARGE_TO_BOOKING not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_ADD_EXTRA_CHARGE
            )

        attribute_values = privileges[PrivilegeCode.ADD_EXTRA_CHARGE_TO_BOOKING]
        attribute_values = [av.lower() for av in attribute_values]

        if facts.booking_channel_code().lower() not in attribute_values:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_ADD_EXTRA_CHARGE
            )

        if facts.booking_status().lower() not in attribute_values:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_ADD_EXTRA_CHARGE
            )

        if crs_context.is_treebo_tenant():
            if facts.user_type_fdm_or_aom() and not facts.non_credit_charge():
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_ADD_CREDIT_CHARGE
                )

            if facts.is_expense_create_request_type_credit() and not (
                facts.is_corporate_booking() or facts.is_treebo_internal_booking()
            ):
                raise PolicyAuthException(
                    error=PolicyError.CANNOT_ADD_CREDIT_CHARGE_ON_NON_B2B_OR_TA_BOOKING
                )

        if (
            not facts.user_type_cr()
            and not facts.user_type_super_admin()
            and not facts.user_type_fdm_or_aom()
            and facts.added_by_hotel()
        ):
            raise PolicyAuthException(
                error=PolicyError.CANNOT_SELECT_EXPENSE_ADDED_BY_HOTEL
            )

        if (
            not facts.user_type_super_admin()
            and facts.is_booking_complete_noshow_cancel()
        ):
            raise PolicyAuthException(
                error=PolicyError.CANNOT_ADD_NO_SHOW_CANCEL_CHARGE_FOR_COMPLETE_BOOKING_NO_SHOW_CANCEL
            )

        if (
            facts.user_type_fdm_or_aom()
            and facts.is_no_show_cancellation_charge_for_b2b_ta_booking()
        ):
            raise PolicyAuthException(
                error=PolicyError.CANNOT_ADD_NO_SHOW_CANCELLATION_CHARGE_FOR_B2B_TA_BOOKING
            )

        if facts.user_type_fdm_or_aom() and facts.expense_is_in_the_past():
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_ADD_PAST_DATED_CHARGE
            )

        if facts.user_type_fdm_or_aom() and not facts.booking_is_checked_in():
            raise PolicyAuthException(
                error=PolicyError.ADD_CHARGE_ONLY_ALLOWED_AFTER_CHECKIN
            )

        if (
            not facts.user_type_super_admin()
            and facts.expense_is_being_assigned_to_a_checked_out_guest()
        ):
            raise PolicyAuthException(
                error=PolicyError.CANNOT_ADD_CHARGE_ASSIGNED_TO_CHECKED_OUT_GUEST
            )

        if (
            facts.expense_is_being_assigned_to_a_checked_out_guest()
            and PrivilegeCode.ASSIGN_CHARGE_TO_CO_GUEST not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.CANNOT_ADD_CHARGE_ASSIGNED_TO_CHECKED_OUT_GUEST
            )

        if (
            not facts.hotel_live()
            and PrivilegeCode.FULL_ACCESS_IF_HOTEL_NOT_LIVE not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.ADD_CHARGE_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE
            )

        if facts.is_gdc_user():
            raise PolicyAuthException(error=PolicyError.NOT_AUTHORIZED_TO_ADD_CHARGE)

        if (
            facts.non_credit_charge()
            and PrivilegeCode.ADD_NON_CR_CHARGE not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_ADD_NON_CREDIT_CHARGE
            )

        if (
            facts.is_no_show_cancel_charge()
            and PrivilegeCode.ADD_NOSHOW_CANC_CHARGE not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_ADD_NO_SHOW_CANCEL_CHARGE
            )

        return True
