from prometheus import crs_context
from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.addon_facts import AddonFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.exceptions import PolicyAuthException


class CreateAddonRule(BaseRule):
    def allow(self, facts: AddonFacts, privileges=None):
        if (
            facts.user_type_fdm_or_aom()
            and facts.is_past_date_addon_request()
            and not facts.room_stay_reserved(facts.action_payload.get('room_stay_id'))
        ):
            if not facts.linked_addon_added():
                raise PolicyAuthException(
                    error=PolicyError.CAN_NOT_ADD_PAST_DATED_ADDON_FOR_NON_RESERVED_ROOMS
                )

        if (
            facts.user_type_cr() or facts.user_type_fdm_or_aom()
        ) and facts.room_stay_checked_out(facts.action_payload.get('room_stay_id')):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORISED_FOR_CHECKED_OUT_ROOMS
            )

        if crs_context.is_treebo_tenant():
            if (
                facts.user_type_fdm_or_aom()
                and facts.is_charge_type_request()
                and facts.is_credit_addon_request()
            ):
                raise PolicyAuthException(
                    error=PolicyError.CAN_NOT_ADD_ADDONS_OF_CREDIT_TYPE
                )

            if (
                facts.is_charge_type_request()
                and facts.is_credit_addon_request()
                and not (
                    facts.is_corporate_booking() or facts.is_treebo_internal_booking()
                )
            ):
                raise PolicyAuthException(
                    error=PolicyError.CANNOT_ADD_CREDIT_CHARGE_ON_NON_B2B_OR_TA_BOOKING
                )

            if not (
                facts.user_type_super_admin()
                or facts.user_type_cr()
                or facts.user_type_fdm_or_aom()
            ):
                raise PolicyAuthException(error=PolicyError.NOT_AUTHORIZED)

        return True
