from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class InvoicedChargeEditRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.RI_INV_BY_MODIFY_CHARGE_AND_BY_GEN_CN not in privileges:
            raise PolicyAuthException(error=PolicyError.CANNOT_MODIFY_INVOICED_CHARGE)

        return True
