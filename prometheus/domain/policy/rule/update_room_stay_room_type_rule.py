from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.exceptions import PolicyAuthException


class UpdateRoomStayRoomTypeRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if facts.is_gdc_user() and facts.is_room_type_changed_without_price_change():
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_CHANGE_ROOM_TYPE_WITHOUT_PRICE_CHANGE
            )

        return True
