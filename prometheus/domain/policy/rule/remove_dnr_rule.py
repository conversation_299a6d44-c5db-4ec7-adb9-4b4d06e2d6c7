from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts import DNRFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class RemoveDNRRule(BaseRule):
    def allow(self, facts: DNRFacts, privileges=None):
        if PrivilegeCode.DELETE_DNR not in privileges:
            raise PolicyAuthException(error=PolicyError.NOT_AUTHORIZED_TO_DELETE_DNR)

        return True
