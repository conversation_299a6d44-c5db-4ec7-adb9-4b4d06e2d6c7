from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import PolicyAuthException


class UndoCancelNoShowBookingRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if facts.is_first_invoice_generated() and not facts.user_type_super_admin():
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_REVERSE_CANCELLATION_OR_NOSHOW_AFTER_INVOICE_GENERATION
            )

        if facts.hotel_date_is_past_checkin_date():
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_REVERSE_CANCELLATION_OR_NOSHOW_AFTER_CHECKIN_DATE
            )

        if (
            facts.user_type == UserType.CR_TEAM.value
            and facts.current_time_is_past_checkout_datetime()
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_REVERSE_CANCELLATION_OR_NOSHOW_AFTER_CHECKOUT_DATE
            )

        if (
            facts.monthly_settlement_report_generation_is_less_than_24_hours_away_from_now()
            and not facts.is_backend_system_user_type()
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_REVERSE_CANCELLATION_OR_NOSHOW_CLOSE_TO_SETTLEMENT_GENERATION
            )

        if facts.is_gdc_user():
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_REVERSE_CANCELLATION_OR_NOSHOW
            )

        return True
