from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class EditGuaranteeInformationRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if facts.is_booking_checked_out():
            raise PolicyAuthException(
                error=PolicyError.INVALID_STATE_OF_BOOKING_TO_EDIT_GUARANTEE_INFORMATION
            )

        if facts.get_channel_code() not in privileges.get(
            PrivilegeCode.CAN_EDIT_BOOKING_GUARANTEE_INFORMATION, []
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_EDIT_GUARANTEE_INFORMATION
            )

        return True
