from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class UndoCheckoutBookingRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.UNDO_PARTIALLY_OR_FULLY_CO_TILL_CO not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_REVERSE_CHECKOUT
            )

        if (
            facts.monthly_settlement_report_generation_is_less_than_24_hours_away_from_now()
            and not facts.is_backend_system_user_type()
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_REVERSE_CHECKOUT_CLOSE_TO_SETTLEMENT_GENERATION
            )

        if facts.is_credit_note_attached_to_any_invoice():
            raise PolicyAuthException(
                error=PolicyError.CANNOT_REVERSE_CHECKOUT_AFTER_CREDIT_NOTE_GENERATION_ON_INVOICE
            )

        return True
