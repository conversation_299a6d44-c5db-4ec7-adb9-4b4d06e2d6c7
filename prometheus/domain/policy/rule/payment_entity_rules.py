from decimal import Decimal

from prometheus import crs_context
from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.facts.payment_facts import PaymentFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.billing_constants import PaymentReceiverTypes
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.user_constants import PrivilegeCode, UserType
from ths_common.exceptions import PolicyAuthException


class PaymentRules(BaseRule):
    CONFIRMED = "confirmed"
    UNCONFIRMED = "unconfirmed"

    def allow(self, facts: PaymentFacts, privileges=None):
        if PrivilegeCode.CAN_RECORD_PAYMENT not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_RECORD_PAYMENT
            )

        if (
            facts.action_payload.confirmed
            and self.CONFIRMED not in privileges[PrivilegeCode.CAN_RECORD_PAYMENT]
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_RECORD_PAYMENT
            )

        if (
            not facts.action_payload.confirmed
            and self.UNCONFIRMED not in privileges[PrivilegeCode.CAN_RECORD_PAYMENT]
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_RECORD_PAYMENT
            )

        if facts.payment_request_is_for_refund():
            if PrivilegeCode.CAN_ISSUE_REFUND_UPTO not in privileges:
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_ISSUE_REFUND
                )

            max_refund_limit = Decimal(
                privileges[PrivilegeCode.CAN_ISSUE_REFUND_UPTO][0]
            )
            if facts.get_payment_amount_in_base_currency().amount > max_refund_limit:
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_ISSUE_REFUND_FOR_GIVEN_AMOUNT
                )

        if facts.is_new_account_getting_created():
            if PrivilegeCode.CAN_CREATE_NEW_ACCOUNT_IN_BILLED_ENTITY not in privileges:
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_CREATE_NEW_ACCOUNT
                )

        if facts.booking_status() in [
            BookingStatus.RESERVED.value,
            BookingStatus.CONFIRMED.value,
        ]:
            if PrivilegeCode.CAN_RECORD_PAYMENT_BEFORE_CHECKIN in privileges:
                attribute_values = [
                    av.lower()
                    for av in privileges[
                        PrivilegeCode.CAN_RECORD_PAYMENT_BEFORE_CHECKIN
                    ]
                    or []
                ]
                if (
                    attribute_values
                    and facts.get_channel_code() not in attribute_values
                ):
                    raise PolicyAuthException(
                        error=PolicyError.NOT_AUTHORIZED_TO_RECORD_PAYMENT_BEFORE_CHECKIN
                    )
            else:
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_RECORD_PAYMENT_BEFORE_CHECKIN
                )

        # PAYMENT type
        if crs_context.is_treebo_tenant():
            if not facts.payment_request_is_for_refund() and (
                PrivilegeCode.REC_PYMT_FOR_PAID_TO not in privileges
                or facts.action_payload.paid_to
                not in privileges[PrivilegeCode.REC_PYMT_FOR_PAID_TO]
            ):
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_RECORD_PAYMENT_FOR_SELECTED_PAID_TO
                )

            if not facts.payment_request_is_for_refund() and (
                PrivilegeCode.REC_PYMT_FOR_PAID_BY not in privileges
                or facts.action_payload.paid_by
                not in privileges[PrivilegeCode.REC_PYMT_FOR_PAID_BY]
            ):
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_RECORD_PAYMENT_FOR_SELECTED_PAID_BY
                )

            if (
                PrivilegeCode.REC_PYMT_FOR_PYMT_MODE not in privileges
                or PrivilegeCode.REC_PYMT_FOR_PAID_BY not in privileges
                or PrivilegeCode.REC_PYMT_FOR_PAID_TO not in privileges
            ):
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_RECORD_PAYMENT
                )

            if (
                PrivilegeCode.REC_PYMT_FOR_PYMT_MODE not in privileges
                or facts.action_payload.payment_mode
                not in privileges[PrivilegeCode.REC_PYMT_FOR_PYMT_MODE]
            ):
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_ADD_PAYMENT_WITH_SELECTED_PAYMENT_MODES
                )

        # REFUND Type
        if crs_context.is_treebo_tenant() and facts.payment_request_is_for_refund():
            if (
                not PaymentReceiverTypes.GUEST == facts.action_payload.paid_to
                and not facts.payment_request_has_paid_to_guest()
                and (
                    PrivilegeCode.REC_RFD_FOR_PAID_TO not in privileges
                    or PaymentReceiverTypes.GUEST
                    not in privileges[PrivilegeCode.REC_RFD_FOR_PAID_TO]
                )
            ):
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_RECORD_REFUND_FOR_SELECTED_PAID_TO
                )

            if (
                not PaymentReceiverTypes.HOTEL == facts.action_payload.paid_to
                and not facts.payment_request_has_paid_by_hotel()
                and (
                    PrivilegeCode.REC_RFD_FOR_PAID_BY not in privileges
                    or PaymentReceiverTypes.HOTEL
                    not in privileges[PrivilegeCode.REC_RFD_FOR_PAID_BY]
                )
            ):
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_RECORD_REFUND_FOR_SELECTED_PAID_BY
                )

            if (
                PrivilegeCode.REC_RFD_FOR_PAID_TO not in privileges
                or PrivilegeCode.REC_RFD_FOR_PAID_BY not in privileges
                or PrivilegeCode.REC_RFD_FOR_BOOKING_STATUS not in privileges
            ):
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_ISSUE_REFUND
                )

        if crs_context.is_treebo_tenant():
            if facts.is_booking_closed() and not (
                facts.user_type_super_admin()
                or facts.user_type
                in [
                    UserType.THS_BACKEND.value,
                    UserType.CR_TEAM.value,
                    UserType.FDM.value,
                    UserType.BILLING_TEAM.value,
                    UserType.CR_TEAM_LEAD.value,
                    UserType.BILLING_TEAM_MANAGER.value,
                ]
            ):
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_ADD_PAYMENT_TO_CHECKED_OUT_OR_CANCELLED_BOOKING
                )

        if (
            facts.cancel_payment_request_received()
            and facts.payment_request_is_for_refund()
            and PrivilegeCode.CANC_RFD not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_CANCEL_RECORDED_REFUNDS
            )

        if (
            facts.cancel_payment_request_received()
            and PrivilegeCode.CANC_PYMT not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_CANCEL_RECORDED_PAYMENTS
            )

        return True
