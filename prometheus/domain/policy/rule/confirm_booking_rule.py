from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class ConfirmBookingRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.CONF_BOOKING_FROM_RSVD not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_CONFIRM_BOOKING
            )
        return True
