from ths_common.exceptions import CRSError


class PolicyError(CRSError):
    # Use Error code sequence starting "09" and "10" and "11"
    ADD_GUEST_NOT_ALLOWED_FOR_BOOKING_FROM_THIS_CHANNEL = (
        "0901",
        "Addition of guest is not allowed for bookings from this channel",
    )
    PAST_DATED_ROOM_ADD_NOT_ALLOWED = (
        "0902",
        "Addition of room stay is only allowed for current and future checkin dates",
    )
    ADD_ROOM_NOT_ALLOWED_FOR_BOOKING_FROM_THIS_CHANNEL = (
        "0903",
        "Addition of room stay is not allowed for bookings from this channel",
    )

    TEMP_BOOKING_CANCELLATION_NOT_ALLOWED = (
        "0904",
        "Cancellation of Temp Booking is not allowed",
    )
    B2B_TEMP_BOOKING_CANCELLATION_NOT_ALLOWED = (
        "0905",
        "Cancellation of B2B Temp Booking is not allowed",
    )
    BOOKING_CANCELLATION_NOT_ALLOWED_FOR_THIS_CHANNEL = (
        "0906",
        "Cancellation of booking is not allowed for this channel",
    )
    BOOKING_CANCELLATION_ONLY_ALLOWED_TILL_MIDNIGHT_OF_CHECKIN_DATE = (
        "0907",
        "Cancellation of booking is only allowed till midnight of checkin date",
    )
    NOT_AUTHORIZED_TO_CANCEL_CREDIT_NOTE = (
        "0908",
        "You're not allowed to cancel credit note.",
    )
    BOOKING_OWNER_CHANGE_NOT_ALLOWED_FOR_CORP_OR_B2B_TA_BOOKING = (
        "0909",
        "Booking owner change is not allowed for corporate and b2b-ta bookings",
    )

    BOOKING_OWNER_CHANGE_NOT_ALLOWED_FOR_TA_BOOKING = (
        "0910",
        "Booking owner change is not allowed for TA bookings",
    )
    BOOKING_OWNER_CHANGE_NOT_ALLOWED_FOR_B2B_BULK_BOOKING = (
        "0911",
        "Booking owner change is not allowed for B2B bulk booking",
    )
    BOOKING_OWNER_CHANGE_NOT_ALLOWED_AFTER_FIRST_INVOICE_GENERATION = (
        "0912",
        "Booking owner change is not allowed after first invoice generation",
    )

    SETTLEMENT_REPORT_24_HOURS_AWAY_CANNOT_MOVE_ROOM_STAY_TO_PREVIOUS_MONTH = (
        "0913",
        "Cannot move room stay dates to previous month when monthly settlement report generation is 24 hours away",
    )
    SWITCH_OVER_CROSSED_CANNOT_MOVE_ROOM_STAY_TO_PREVIOUS_DAY = (
        "0914",
        "Cannot move room stay dates to previous day after switch over has crossed",
    )
    CANNOT_MOVE_ROOM_STAY_TO_BACK_DATED_CHECKIN = (
        "0915",
        "Cannot change room stay to back-dated checkin",
    )
    CANNOT_MODIFY_ROOM_STAY_OR_CREDIT_CHARGE = (
        "0916",
        "Editing of RoomStay or credit charge is not allowed",
    )
    CANNOT_MODIFY_CHARGE_TYPE = "0917", "Editing of charge type is not allowed"
    CHARGE_CANCELLATION_POST_CHECKOUT_NOT_ALLOWED = (
        "0918",
        "Cancellation of charge post checkout is not allowed",
    )
    CANNOT_MODIFY_INVOICED_CHARGE = "0919", "Editing of invoiced charge is not allowed"
    CANNOT_ADD_CREDIT_CHARGE_ON_NON_B2B_OR_TA_BOOKING = (
        "0920",
        "Creation of credit charge on non corporate/TA booking is not allowed",
    )

    CANNOT_CREATE_NON_B2B_OR_TA_BOOKING_WITH_CREDIT_CHARGE = (
        "0921",
        "Credit charge can only be added to corporate/TA bookings",
    )
    NOT_AUTHORIZED_TO_CREATE_B2B_BOOKING = (
        "0922",
        "You are not authorized to create corporate bookings. Please contact Escalations",
    )
    NOT_AUTHORIZED_TO_CREATE_TA_BOOKING = (
        "0923",
        "You are not authorized to create TA bookings. Please contact Escalations",
    )
    NOT_AUTHORIZED_TO_CREATE_BOOKING_FOR_THIS_CHANNEL = (
        "0924",
        "You are not authorized to create bookings for this channel. Please contact Escalations",
    )
    HOTEL_NOT_LIVE_ON_CRS = (
        "0925",
        "Hotel is not live on Treebo PMS. Please contact Escalations",
    )
    BOOKING_CREATION_NOT_ALLOWED_FOR_DATES_PRIOR_TO_HOTEL_LAUNCH = (
        "0926",
        "Creation of bookings isn't allowed for dates prior to hotel launch",
    )
    PAST_DATED_BOOKING_CREATION_NOT_ALLOWED_AFTER_SETTLEMENT = (
        "0927",
        "Creation of past dated bookings isn't allowed after settlement",
    )
    CANNOT_CREATE_BOOKING_FOR_PAST = (
        "0928",
        "Past dated booking creation is not allowed",
    )
    NOT_AUTHORIZED_TO_CREATE_BOOKING_FOR_PAST_DATES = (
        "0929",
        "You are not authorized to create booking for past dates. Please contact Escalations",
    )
    NOT_AUTHORIZED_TO_CREATE_BOOKING_HAVING_PAYMENT_WITH_SELECTED_PAYMENT_MODES = (
        "0930",
        "Booking cannot have payment with selected payment modes",
    )
    NOT_AUTHORIZED_TO_CREATE_BOOKING_WITH_PAYMENT_NOT_PAID_BY_GUEST = (
        "0931",
        "You're not authorized to add payment that is not paid-by guest",
    )
    NOT_AUTHORIZED_TO_CREATE_BOOKING_WITH_PAYMENT_NOT_PAID_TO_HOTEL = (
        "0932",
        "You're not authorized to add payment that is not paid-to hotel",
    )
    NOT_AUTHORIZED_TO_CREATE_BOOKING_WITH_REFUND_NOT_PAID_BY_HOTEL = (
        "0933",
        "You're not authorized to add refund that is not paid-by hotel",
    )
    NOT_AUTHORIZED_TO_CREATE_BOOKING_WITH_REFUND_NOT_PAID_TO_GUEST = (
        "0934",
        "You're not authorized to add refund that is not paid-to guest",
    )
    NOT_AUTHORIZED_TO_ADD_CREDIT_CHARGE = (
        "0935",
        "You are not authorized to add credit charges. Please contact Escalations",
    )
    NOT_AUTHORIZED_TO_ADD_PAST_DATED_CHARGE = (
        "0936",
        "You are not authorized to add past dated charges. Please contact Escalations",
    )
    ADD_CHARGE_ONLY_ALLOWED_AFTER_CHECKIN = (
        "0937",
        "Addition of charges is only allowed after check-in",
    )
    CANNOT_ADD_CHARGE_ASSIGNED_TO_CHECKED_OUT_GUEST = (
        "0938",
        "Charges can't be assigned to guests who have checked out",
    )
    NOT_AUTHORIZED_TO_MODIFY_PAST_DATED_DNR = (
        "0939",
        "Modification of past dated DNR is not allowed",
    )
    NOT_AUTHORIZED_TO_MOVE_DNR_TO_PAST_DATE = (
        "0940",
        "Change of DNR dates to past dates is not allowed",
    )
    CANNOT_MODIFY_GUEST_DETAILS_AFTER_FIRST_INVOICE_GENERATION = (
        "0941",
        "Modification of guest details is not allowed after first invoice generation for the booking",
    )
    NOT_AUTHORIZED_TO_CREATE_CREDIT_NOTE = (
        "0942",
        "You're not authorized to generate credit note.",
    )
    CAN_ONLY_ISSUE_CREDIT_NOTE_TILL_8_MONTHS_AFTER_FINANCIAL_YEAR = (
        "0943",
        "Credit note can only be issued till 8 months after financial year is over",
    )
    NOT_AUTHORIZED_TO_GENERATE_HOTEL_TO_RESELLER_CREDIT_NOTE = (
        "0944",
        "Hotel to reseller credit note generation is not allowed",
    )
    NOT_AUTHORIZED_TO_GENERATE_HOTEL_TO_RESELLER_INVOICE = (
        "0945",
        "Hotel to reseller invoice generation is not allowed",
    )
    NOT_AUTHORIZED_TO_MARK_PAST_DATED_DNR = (
        "0946",
        "Marking DNR on past dates is not allowed",
    )

    NOT_AUTHORIZED_TO_ADD_PAYMENT_WITH_SELECTED_PAYMENT_MODES = (
        "0947",
        "You are not authorized to add payments with the selected payment mode, please contact escalations.",
    )
    NOT_AUTHORIZED_TO_RECORD_PAYMENT_FOR_SELECTED_PAID_BY = (
        "0948",
        "You are not authorized to record payments which are for selected 'paid by', please contact "
        "escalations.",
    )
    NOT_AUTHORIZED_TO_RECORD_PAYMENT_FOR_SELECTED_PAID_TO = (
        "0949",
        "You are not authorized to record payments which are for selected 'paid to', please contact "
        "escalations.",
    )
    NOT_AUTHORIZED_TO_RECORD_REFUND_FOR_SELECTED_PAID_BY = (
        "0950",
        "You are not authorized to record refunds which are for selected 'paid by', please contact escalations",
    )
    NOT_AUTHORIZED_TO_RECORD_REFUND_FOR_SELECTED_PAID_TO = (
        "0951",
        "You are not authorized to record refunds which are for selected 'paid to', please contact "
        "escalations.",
    )
    NOT_AUTHORIZED_TO_ADD_PAYMENT_TO_CHECKED_OUT_OR_CANCELLED_BOOKING = (
        "0952",
        "You are not allowed to add payments to this booking in its current state.",
    )
    NOT_AUTHORIZED_TO_RESOLVE_DNR = (
        "0953",
        "You are not authorized to resolve DNR. Please contact Escalations",
    )
    NOT_AUTHORIZED_TO_RESOLVE_PAST_DATED_DNR = (
        "0954",
        "You are not authorized to resolve DNR for past dates.",
    )
    CANCELLATION_OF_GUEST_NOT_ALLOWED_FOR_BOOKING_FROM_THIS_CHANNEL = (
        "0955",
        "Removal of guest is not allowed for bookings from this channel",
    )
    CANCELLATION_OF_GUEST_ONLY_ALLOWED_TILL_MIDNIGHT_OF_CHECKIN_DATE = (
        "0956",
        "Removal of guest is allowed only till midnight of check-in date",
    )

    CANCELLATION_OF_ROOM_NOT_ALLOWED_FOR_BOOKING_FROM_THIS_CHANNEL = (
        "0957",
        "Removal of room is not allowed for bookings from this channel",
    )
    CANCELLATION_OF_ROOM_ONLY_ALLOWED_TILL_MIDNIGHT_OF_CHECKIN_DATE = (
        "0958",
        "Removal of room is allowed only till midnight of check-in date",
    )
    NOT_AUTHORIZED_TO_REVERSE_CANCELLATION_OR_NOSHOW_AFTER_CHECKIN_DATE = (
        "0959",
        "You are not allowed to reverse cancellation/no-show after check-in date",
    )
    NOT_AUTHORIZED_TO_REVERSE_CANCELLATION_OR_NOSHOW_AFTER_CHECKOUT_DATE = (
        "0960",
        "You are not allowed to reverse cancellation/no-show after checkout-date",
    )
    NOT_AUTHORIZED_TO_REVERSE_CANCELLATION_OR_NOSHOW_CLOSE_TO_SETTLEMENT_GENERATION = (
        "0961",
        "You are not allowed to reverse cancellation/no-show close to settlement generation",
    )
    NOT_AUTHORIZED_TO_REVERSE_CHECKIN_AFTER_CHECKIN_DATE = (
        "0962",
        "You are not allowed to reverse check-in after check-in date",
    )
    NOT_AUTHORIZED_TO_REVERSE_CHECKIN_AFTER_INVOICE_GENERATION = (
        "0963",
        "You are not allowed to reverse check-in after invoice generation",
    )
    NOT_AUTHORIZED_TO_REVERSE_CHECKIN_CLOSE_TO_SETTLEMENT_GENERATION = (
        "0964",
        "You are not allowed to reverse check-in close to settlement generation",
    )
    NOT_AUTHORIZED_TO_REVERSE_CHECKOUT = (
        "0965",
        "You are not allowed to reverse check-out",
    )
    NOT_AUTHORIZED_TO_REVERSE_CHECKOUT_AFTER_CHECKOUT_DATETIME = (
        "0966",
        "You are not allowed to reverse checkout after checkout datetime",
    )
    NOT_AUTHORIZED_TO_REVERSE_CHECKOUT_CLOSE_TO_SETTLEMENT_GENERATION = (
        "0967",
        "You are not allowed to reverse check-out close to settlement generation",
    )
    STAY_DATES_CHANGE_NOT_ALLOWED_FOR_BOOKING_FROM_THIS_CHANNEL = (
        "0968",
        "Stay dates change is not allowed for bookings from this channel",
    )
    NOT_AUTHORIZED_TO_RESET_HOTEL_OWNERSHIP = (
        "0969",
        "You are not allowed to reset hotel ownership details",
    )
    CHECKIN_CANNOT_BE_PERFORMED_PRIOR_TO_HOTEL_LAUNCH = (
        "0970",
        "You are not allowed to perform checkin operation prior to hotel launch date",
    )
    STAY_DATES_CANNOT_BE_CHANGED_FOR_BOOKING_PRIOR_TO_HOTEL_LAUNCH = (
        "0971",
        "You are not allowed to change stay dates for booking prior to hotel launch",
    )
    BOOKING_CANCELLATION_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE = (
        "0972",
        "You are not allowed to cancel the booking before hotel live date",
    )
    BOOKING_MODIFICATION_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE = (
        "0973",
        "You are not allowed to modify the booking before hotel live date",
    )
    GUEST_CANCELLATION_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE = (
        "0974",
        "You are not allowed to remove guest before hotel live date",
    )
    ROOM_CANCELLATION_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE = (
        "0975",
        "You are not allowed to remove room before hotel live date",
    )
    ADD_CHARGE_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE = (
        "0976",
        "You are not allowed to add charge before hotel live date",
    )
    EDIT_CHARGE_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE = (
        "0977",
        "You are not allowed to edit charge before hotel live date",
    )
    BOOKING_OWNER_CHANGE_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE = (
        "0978",
        "You are not allowed to change booking owner details before hotel live date",
    )
    ADD_ROOM_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE = (
        "0979",
        "You are not allowed to add room before hotel live date",
    )
    ADD_GUEST_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE = (
        "0980",
        "You are not allowed to add guest before hotel live date",
    )
    CANNOT_MODIFY_GUEST_DETAILS_BEFORE_HOTEL_LIVE_DATE = (
        "0981",
        "You are not allowed to edit guest details before hotel live date",
    )
    BOOKING_CREATION_NOT_ALLOWED_FOR_NON_HOTEL_CHANNEL_BEFORE_HOTEL_LAUNCH = (
        "0982",
        "You're not allowed to create non-hotel channel bookings prior to hotel launch",
    )
    CANNOT_MARK_NOSHOW_PRIOR_TO_HOTEL_LAUNCH = (
        "0983",
        "You're not allowed to mark noshow prior to hotel launch",
    )
    CANNOT_CREATE_B2B_OR_TA_BOOKING_WHEN_HOTEL_DOESNT_HAVE_GSTIN_CONFIGURED = (
        "0984",
        "Booking Creation for B2B/TA channel is not allowed. Hotel gstin is not configured",
    )
    NOT_AUTHORIZED_TO_ADD_ROOM = "0985", "You are not authorized to add room"
    NOT_AUTHORIZED_TO_CHANGE_BOOKING_OWNER_DETAILS = (
        "0986",
        "You are not authorized to change booking owner details",
    )
    NOT_AUTHORIZED_TO_MODIFY_CHARGE = "0987", "You are not authorized to modify charge"
    NOT_AUTHORIZED_TO_PERFORM_CHECKIN = (
        "0988",
        "You are not authorized to perform checkin operation",
    )
    NOT_AUTHORIZED_TO_CONFIRM_BOOKING = (
        "0989",
        "You are not authorized to confirm a booking",
    )
    ONLY_AUTHORIZED_TO_CREATE_SOFT_BOOKING = (
        "0990",
        "You are only authorized to create soft block booking",
    )
    NOT_AUTHORIZED_TO_ADD_CHARGE = "0991", "You are not authorized to add charge"
    ONLY_AUTHORIZED_TO_ADD_SPECIAL_INSTRUCTIONS = (
        "0992",
        "You are only authorized to update special instructions in a booking",
    )
    NOT_AUTHORIZED_TO_MODIFY_DNR = "0993", "You are not authorized to modify DNR"
    NOT_AUTHORIZED_TO_MODIFY_GUEST_DETAILS = (
        "0994",
        "You are not authorized to modify guest details",
    )
    NOT_AUTHORIZED_TO_MARK_DNR = "0995", "You are not authorized to mark DNR"
    NOT_AUTHORIZED_TO_MARK_NOSHOW = "0996", "You are not authorized to mark No-Show"
    NOT_AUTHORIZED_TO_RECORD_PAYMENT = (
        "0997",
        "You are not authorized to record payment",
    )
    NOT_AUTHORIZED_TO_REVERSE_CANCELLATION_OR_NOSHOW = (
        "0998",
        "You are not authorized to reverse cancellation or noshow operation",
    )
    NOT_AUTHORIZED_TO_REVERSE_CHECKIN = (
        "0999",
        "You are not authorized to reverse checkin operation",
    )
    NOT_AUTHORIZED_TO_CHANGE_ROOM_TYPE_WITHOUT_PRICE_CHANGE = (
        "1001",
        "You are not authorized to update room type without giving new price",
    )
    BOOKING_MODIFICATION_NOT_ALLOWED_FOR_CANCELLED_OR_NOSHOW_BOOKING = (
        "1002",
        "You are not authorized to update booking details for cancelled or noshow booking",
    )
    NOT_AUTHORIZED_TO_CREATE_SOFT_BOOKING = (
        "1003",
        "You are not authorized to create soft block booking",
    )

    CANNOT_ADD_NO_SHOW_CANCELLATION_CHARGE_FOR_B2B_TA_BOOKING = (
        "1004",
        "No show or cancellation charge addition for B2B/TA channel is not allowed.",
    )
    CANNOT_ADD_NO_SHOW_CANCEL_CHARGE_FOR_COMPLETE_BOOKING_NO_SHOW_CANCEL = (
        "1005",
        "No show or cancellation charge addition for complete noshow/cancellation not allowed",
    )
    CANNOT_CONFIRM_INVOICE = "1006", "Cannot Confirm previewed invoices is not allowed"

    FDM_CANNOT_SELECT_EXPENSE_ADDED_BY_TREEBO = (
        "1007",
        "FDM cannot create expense of added_by treebo type",
    )
    CANNOT_SELECT_EXPENSE_ADDED_BY_HOTEL = (
        "1008",
        "Cannot create expense of added_by hotel type",
    )

    NOT_ALLOWED_TO_MARK_DNR_FOR_INACTIVE_ROOM = (
        "1009",
        "Not allowed to mark dnr for the rooms which are inactive",
    )
    NOT_ALLOWED_TO_RESOLVE_DNR_FOR_INACTIVE_ROOM = (
        "1010",
        "Not allowed to resolve inactive_room type of dnr",
    )
    NOT_AUTHORIZED_TO_SWAP_OVERFLOW = "1011", "You are not authorized to swap overflow"

    NOT_AUTHORISED_FOR_CHECKED_OUT_ROOMS = (
        "1012",
        "You are not authorised to perform this operation on a checked out room",
    )
    CAN_NOT_ADD_PAST_DATED_ADDON_FOR_NON_RESERVED_ROOMS = (
        "1013",
        "You are not authorised to add past dated addons for non reserved rooms",
    )
    CAN_NOT_ADD_ADDONS_OF_CREDIT_TYPE = (
        "1014",
        "You are not Authirised to add addons of charge type credit.",
    )

    CAN_NOT_EDIT_DATE_RANGE_OF_PAST_DATED_ADDON = (
        "1015",
        "You are not authorised to edit addon date range for past dated addon",
    )
    CAN_NOT_EDIT_PRICE_OF_PAST_ADDON = (
        "1016",
        "You are not authorised to edit the price for a past dated addon",
    )

    CAN_NOT_DELETE_PAST_DATED_ADDON = (
        "1017",
        "You are not authorised to delete past dated addons",
    )

    NOT_AUTHORIZED = ("1018", "You are not authorized to perform this operation")

    NOT_AUTHORIZED_TO_EDIT_BOOKING_REFERENCE_NUMBER = (
        "1019",
        "You're not authorized to edit booking reference number",
    )
    BOOKING_OWNER_GSTIN_CHANGE_NOT_ALLOWED_AFTER_FIRST_INVOICE_GENERATION = (
        "1020",
        "Booking owner gstin change is "
        "not allowed "
        "after first invoice generation",
    )
    NOT_AUTHORIZED_TO_ADD_EXPENSE_AFTER_INVOICE_GENERATION = (
        "1021",
        "You're not authorized to add expense after invoice generation",
    )
    NOT_AUTHORIZED_TO_EDIT_CHARGE_POST_CHECKOUT = (
        "1022",
        "You're not authorized to edit charge post checkout",
    )
    NOT_AUTHORIZED_TO_PERFORM_CHECKOUT = (
        "1023",
        "You're not authorized to perform checkout",
    )
    NOT_AUTHORIZED_TO_DELETE_DNR = "1024", "You're not authorized to delete dnr"
    NOT_AUTHORIZED_TO_ACCESS_ENTITY = (
        "1025",
        "You're not authorized to access this entity",
    )
    NOT_AUTHORIZED_TO_DELETE_ATTACHMENT = (
        "1026",
        "You are not authorized to delete this attachment",
    )
    CANNOT_MODIFY_CREDIT_NOTE_CHARGE = (
        "1027",
        "Editing of credit note reversal charge is not allowed",
    )
    NOT_AUTHORIZED_TO_ACCESS_ATTACHMENT = (
        "1028",
        "You are not authorized to access this attachment",
    )
    CANNOT_REVERSE_CHECKIN_AFTER_FIRST_INVOICE_GENERATION = (
        "1029",
        "Reversal of CheckIn is not allowed after first invoice generation",
    )
    CANNOT_REVERSE_CHECKOUT_AFTER_CREDIT_NOTE_GENERATION_ON_INVOICE = (
        "1030",
        "Reversal of CheckOut is not allowed "
        "after generation of credit note "
        "upon invoices",
    )
    NOT_AUTHORIZED_TO_INVOICE_NEW_CHARGES = (
        "1031",
        "You're not authorized to generate invoices for new charges",
    )

    INVALID_BOOKING_STATE_FOR_INVOICE_NEW_CHARGES = (
        "1032",
        "Invalid Booking State for invoicing new charges",
    )
    NOT_AUTHORIZED_TO_REVERSE_CANCELLATION_OR_NOSHOW_AFTER_INVOICE_GENERATION = (
        "1033",
        "You're not authorized to"
        "reverse cancel or noshow"
        "after invoice generation",
    )
    NOT_AUTHORIZED_TO_CREATE_CASHIER_SESSION = (
        "1034",
        "You're not authorized to create new cashier session",
    )
    NOT_AUTHORIZED_TO_CREATE_CASH_REGISTER = (
        "1035",
        "You're not authorized to create new cash register",
    )
    NOT_AUTHORIZED_TO_UPDATE_CASH_REGISTER = (
        "1036",
        "You're not authorized to update cash register",
    )
    NOT_AUTHORIZED_TO_CREATE_CASHIER_PAYMENT = (
        "1037",
        "You're not authorized to create new cashier payment",
    )
    NOT_AUTHORIZED_TO_UPDATE_CASHIER_PAYMENT = (
        "1038",
        "You're not authorized to update cashier payment",
    )
    CANNOT_CREATE_B2B_BOOKING_WITHOUT_GST_DETAILS_PROVIDED = (
        "1039",
        "Booking Creation for B2B channel is not "
        "allowed. Mandatory GST Details are not "
        "provided",
    )
    BOOKING_OWNER_CHANGE_NOT_ALLOWED_FOR_B2B_BOOKING_WITHOUT_GST_DETAILS_PROVIDED = (
        "1040",
        "Booking owner change "
        "is not allowed for b2b "
        "bookings without "
        "mandatory gst details",
    )
    RESERVED_BOOKING_NOT_ALLOWED_FOR_PROVIDED_CHANNEL = (
        "1041",
        "Reserve Booking is not allowed for this channel",
    )
    BOOKING_OWNER_CHANGE_NOT_ALLOWED_FOR_THIS_CHANNEL = (
        "1042",
        "Booking owner change is not allowed forthis channel",
    )
    NOT_AUTHORIZED_TO_ISSUE_REFUND = "1043", "You're not authorized to issue refund"
    NOT_AUTHORIZED_TO_ACCESS_REPORT = "1044", "You are not authorized to access report"
    NOT_AUTHORIZED_TO_MODIFY_ATTACHMENT = (
        "1045",
        "You are not authorized to modify this attachment",
    )
    NOT_AUTHORIZED_TO_MODIFY_SPECIAL_INSTRUCTIONS = (
        "1046",
        "You are not authorized to modify special instruction",
    )
    NOT_AUTHORIZED_TO_ADD_NON_CREDIT_CHARGE = (
        "1047",
        "You are not authorized to add non credit charge",
    )
    NOT_AUTHORIZED_TO_CANCEL_RECORDED_PAYMENTS = (
        "1048",
        "You are not authorized to cancel recorded payments",
    )
    NOT_AUTHORIZED_TO_CANCEL_RECORDED_REFUNDS = (
        "1049",
        "You are not authorized to cancel recorded refunds",
    )
    NOT_AUTHORIZED_TO_ADD_NO_SHOW_CANCEL_CHARGE = (
        "1050",
        "You are nor authoized to add no show cancel charge",
    )
    NOT_AUTHORIZED_TO_ACCESS_CASHIER_MODULE = (
        "1051",
        "You are not authorized to access cashier module",
    )

    CANNOT_MODIFY_B2B_BOOKING_COMPANY_DETAILS_WITHOUT_GST_DETAILS_PROVIDED = (
        "1052",
        "Booking modification for B2B "
        "channel is not allowed. "
        "Mandatory GST Details are not "
        "provided",
    )
    CANNOT_EDIT_BILLING_DETAILS_OF_THIS_CHARGE = (
        "1053",
        "You're not authorized to edit billing details of this charge",
    )
    NOT_AUTHORIZED_TO_TRANSFER_THIS_CHARGE = (
        "1054",
        "You're not authorized to transfer this charge to another booking",
    )
    NOT_AUTHORIZED_TO_CANCEL_ALLOWANCE = (
        "1055",
        "You're not authorized to cancel this allowance",
    )
    NOT_AUTHORIZED_TO_POST_ALLOWANCE = (
        "1056",
        "You're not authorized to post this allowance",
    )
    NOT_AUTHORIZED_TO_CREATE_NEW_ACCOUNT = (
        "1057",
        "You're not authorized to create new account in a billed entity",
    )
    NOT_AUTHORIZED_TO_EDIT_BOOKED_PAYMENT = (
        "1058",
        "You're not authorized to edit this payment",
    )
    NOT_AUTHORIZED_TO_MARK_PAYMENT_AS_CONFIRMED = (
        "1059",
        "You're not authorized to mark payment as confirmed",
    )
    NOT_AUTHORIZED_TO_ADD_EXTRA_CHARGE = (
        "1060",
        "You're not authorized to add extras to this booking",
    )
    NOT_AUTHORIZED_TO_PERFORM_NIGHT_AUDIT_FOR_PAST_DATE = (
        "1061",
        "You're not authorized to perform night audit for past date",
    )
    NOT_AUTHORIZED_TO_PERFORM_NIGHT_AUDIT_FOR_FUTURE_DATE = (
        "1062",
        "You're not authorized to perform night audit for future date",
    )
    NOT_AUTHORIZED_TO_ISSUE_REFUND_FOR_GIVEN_AMOUNT = (
        "1063",
        "You're not authorized to issue refund upto given amount",
    )
    CANNOT_TRANSFER_RATE_PLAN_CHARGE_WITH_INCLUSIONS = (
        "1064",
        "Transfer of rate plan charge with inclusions is not allowed",
    )
    CANNOT_CREATE_TA_BOOKING_WITHOUT_TRAVEL_AGENT_DETAILS = (
        "1065",
        "TA booking is not allowed. Either travel agent "
        "details or booking owner gst details are mandatory",
    )

    CANNOT_CREATE_B2B_BOOKING_WITHOUT_COMPANY_DETAILS = (
        "1066",
        "B2B booking is not allowed. Either company "
        "details or booking owner gst details are mandatory",
    )
    NOT_AUTHORIZED_TO_INVOICE_ACCOUNTS = (
        "1067",
        "You're not authorized to invoice an account outside of checkout flow",
    )

    CANNOT_CREATE_BOOKING_WITH_GIVEN_DEFAULT_BE_CATEGORY = (
        "1068",
        "Cannot create booking with given default billed "
        "entity category, details missing for given category",
    )
    NOT_AUTHORIZED_TO_SETTLE_SPOT_CREDIT = (
        "1069",
        "You are not authorized to settle with spot credit",
    )
    NOT_AUTHORIZED_TO_REISSUE_INVOICE_WITHOUT_BUY_SIDE = (
        "1070",
        "You are not authorized to reissue invoice without buyside",
    )
    NOT_AUTHORIZED_TO_UPDATE_OCCUPANCY_REOCRD = (
        "1071",
        "You are not authorized to update occupancy record",
    )
    NOT_AUTHORIZED_TO_EDIT_REFUND = (
        "1072",
        "You are not authorized to edit refund",
    )
    NOT_AUTHORIZED_TO_ADD_GUEST = (
        "1073",
        "You are not authorized to add guest",
    )
    NOT_AUTHORIZED_TO_EDIT_COMMISSION_IN_BOOKING = (
        "1074",
        "You are not authorized to edit commission in booking",
    )
    INVALID_STATE_OF_BOOKING_TO_EDIT_COMMISSION = (
        "1075",
        "Invalid state of booking to edit commission",
    )
    NOT_AUTHORIZED_TO_USE_THIS_REFUND_MODE = (
        "1076",
        "You are not authorized to use this refund",
    )
    NOT_AUTHORIZED_TO_REDEEM_CREDIT_SHELL = (
        "1077",
        "You are not authorized to redeem credit shell",
    )
    NOT_AUTHORIZED_TO_RECORD_PAYMENT_BEFORE_CHECKIN = (
        "1078",
        "You are not authorized to record payment before checkin",
    )
    INVALID_STATE_OF_BOOKING_TO_EDIT_GUARANTEE_INFORMATION = (
        "1079",
        "Invalid state of booking to edit guarantee information",
    )
    NOT_AUTHORIZED_TO_EDIT_GUARANTEE_INFORMATION = (
        "1080",
        "You are not authorized to edit booking guarantee information",
    )
    NOT_AUTHORIZED_TO_MANUAL_ADD_FUNDING_REQUEST = (
        "1081",
        "You are not authorized to add manual funding request",
    )
    NOT_AUTHORIZED_TO_RELEASE_INVENTORY_BLOCKS = (
        "1082",
        "You are not authorized to release inventory blocks",
    )
