from treebo_commons.utils import dateutils

from prometheus.domain.policy.facts import Facts


class NightAuditFacts(Facts):
    def __init__(
        self,
        user_data=None,
        user_type=None,
        hotel_context=None,
        business_date=None,
        eta=None,
        **aggregates
    ):
        super().__init__(
            user_data=user_data,
            user_type=user_type,
            action_payload=None,
            hotel_context=hotel_context,
            **aggregates
        )
        self.business_date = business_date
        self.eta = eta

    def is_future_day_night_audit(self):
        return dateutils.to_date(self.business_date) >= dateutils.to_date(self.eta)
