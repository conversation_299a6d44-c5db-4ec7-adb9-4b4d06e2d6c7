from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.core.globals import HotelContext
from prometheus.domain.booking.dtos.new_booking_dto import NewBookingDomainDto
from prometheus.domain.policy.context import TenantFactsContext
from prometheus.infrastructure.external_clients.core.constants import CORPORATE_CHANNELS
from ths_common.constants.billing_constants import (
    CashierPaymentTypes,
    ChargeTypes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingChannels, BookingStatus
from ths_common.constants.inventory_constants import DNRType
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import MissingFactsException
from ths_common.utils.dateutils import get_settlement_date


@register_instance(dependencies=[TenantSettings])
class Facts(object):
    """
    Facts used by rules defined for various action.
    If the way of deriving facts for any rule is different from the one implemented in this, create a subclass, and
    override that method. Every rule can take this class or a subclass instance as Fact.

    For E.g.:

    has_credit_charge() -> This fact in create_booking is derived from `action_payload`, but the same fact
        for edit booking would be derived from `bill_aggregate`. So, for that, create a `EditBookingFacts` class, and
        override `has_credit_charge()` fact.
    """

    def __init__(
        self,
        user_data=None,
        user_type=None,
        action_payload=None,
        current_time=None,
        hotel_context: HotelContext = None,
        tenant_facts_context: TenantFactsContext = None,
        **aggregates
    ):
        self.current_time = (
            current_time if current_time else dateutils.current_datetime()
        )
        self.user_data = user_data
        self.user_type = user_type
        self.action_payload = action_payload
        self.hotel_context = hotel_context
        self.aggregates = aggregates
        self.tenant_facts_context = tenant_facts_context

    def get_bill_aggregate(self):
        return self.aggregates.get('bill_aggregate')

    def get_booking_aggregate(self):
        return self.aggregates.get('booking_aggregate')

    def user_type_migration(self):
        return self.user_type == UserType.CRS_MIGRATION_USER.value

    def user_type_fdm(self):
        return self.user_type == UserType.FDM.value

    def user_type_fdm_or_aom(self):
        return self.user_type in (UserType.FDM.value, UserType.AOM.value)

    def user_type_cr(self):
        return self.user_type in (UserType.CR_TEAM.value, UserType.THS_BACKEND.value)

    def user_type_super_admin(self):
        return self.user_type in (
            UserType.SUPER_ADMIN.value,
            UserType.CRS_MIGRATION_USER.value,
        )

    def user_type_cr_or_super_admin(self):
        return self.user_type_cr() or self.user_type_super_admin()

    def is_gdc_user(self):
        return self.user_type == UserType.GDC.value

    def is_backend_system_user_type(self):
        return self.user_type == UserType.BACKEND_SYSTEM.value

    def is_cr_team_lead_user(self):
        return self.user_type == UserType.CR_TEAM_LEAD.value

    def is_aom_user(self):
        return self.user_type == UserType.AOM.value

    def hotel_live(self):
        return self.hotel_context.is_active

    def hotel_has_gstin(self):
        hotel_gstin = self.hotel_context.hotel_gst_details.gstin_num
        return bool(hotel_gstin)

    def is_b2b_booking(self):
        booking_aggregate = self.get_booking_aggregate()
        return (
            booking_aggregate.booking.source.channel_code == BookingChannels.B2B.value
        )

    def is_b2b_bulk_booking(self):
        booking_aggregate = self.get_booking_aggregate()
        return booking_aggregate.booking.source.subchannel_code == "bulk"

    def is_ta_booking(self):
        booking_aggregate = self.get_booking_aggregate()
        return booking_aggregate.booking.source.channel_code == BookingChannels.TA.value

    def get_booking_source(self):
        booking_aggregate = self.get_booking_aggregate()
        return booking_aggregate.booking.source

    def get_booking_application_code(self):
        booking_aggregate = self.get_booking_aggregate()
        return booking_aggregate.booking.source.application_id

    def is_treebo_internal_booking(self):
        booking_aggregate = self.get_booking_aggregate()
        return (
            booking_aggregate.booking.source.channel_code
            == BookingChannels.TREEBO_INTERNAL.value
        )

    def get_subchannel_code(self):
        booking_aggregate = self.get_booking_aggregate()
        return booking_aggregate.booking.source.subchannel_code

    def get_channel_code(self):
        booking_aggregate = self.get_booking_aggregate()
        return booking_aggregate.booking.source.channel_code

    def walk_in_or_hotel_corp_booking(self):
        return self.get_subchannel_code() in ("walk-in", "hotel-corporate")

    def midnight_of_checkin_date(self):
        booking_aggregate = self.get_booking_aggregate()
        checkin_date = booking_aggregate.booking.checkin_date
        return dateutils.datetime_at_midnight(dateutils.add(checkin_date, days=1))

    def is_first_invoice_generated(self):
        generated_invoice_aggregates = self.aggregates.get(
            'generated_invoice_aggregates'
        )
        assert generated_invoice_aggregates is not None, "Required for this fact"
        return len(generated_invoice_aggregates) > 0

    def is_booking_closed(self):
        booking_aggregate = self.get_booking_aggregate()
        if not booking_aggregate:
            raise MissingFactsException(
                description="Fact 'booking_aggregate' is not available to "
                "derive booking closed state."
            )
        return booking_aggregate.is_closed()

    def is_past_date_dnr_received(self):
        return self.action_payload.get('from_date') < self.hotel_context.current_date()

    def is_soft_booking(self):
        booking_aggregate = self.get_booking_aggregate()
        return booking_aggregate.is_temporary()

    def payment_request_has_paid_to_guest(self):
        paid_to = self.action_payload.paid_to
        if paid_to and paid_to != PaymentReceiverTypes.GUEST:
            return False
        return True

    def payment_request_has_paid_by_guest(self):
        paid_by = self.action_payload.paid_by
        if paid_by and paid_by != PaymentReceiverTypes.GUEST:
            return False
        return True

    def payment_request_has_paid_by_hotel(self):
        paid_by = self.action_payload.paid_by
        if paid_by and paid_by != PaymentReceiverTypes.HOTEL:
            return False
        return True

    def payment_request_is_for_refund(self):
        return self.action_payload.payment_type == PaymentTypes.REFUND

    def room_added_with_past_dated_checkin(self):
        room_stay_added = self.action_payload
        return (
            dateutils.to_date(room_stay_added['checkin_date'])
            < self.hotel_context.current_date()
        )

    def hotel_date_is_past_checkin_date(self):
        booking_aggregate = self.get_booking_aggregate()
        checkin_date = self.hotel_context.hotel_checkin_date(
            booking_aggregate.booking.checkin_date
        )
        hotel_date = self.hotel_context.current_date()
        return hotel_date > checkin_date

    def current_time_is_past_checkout_datetime(self):
        booking_aggregate = self.get_booking_aggregate()
        checkout_datetime = booking_aggregate.booking.checkout_date
        return self.current_time > checkout_datetime

    def monthly_settlement_report_generation_is_less_than_24_hours_away_from_now(self):
        booking_aggregate = self.get_booking_aggregate()
        settlement_date = get_settlement_date(
            booking_aggregate.booking.checkout_date, next_month=True
        )
        return (settlement_date - self.current_time).days < 1

    def is_credit_note_generated_after_eight_months_of_financial_year(self):
        raise NotImplementedError()

    def booking_modification_only_has_special_instructions(self):
        booking_aggregate = self.get_booking_aggregate()
        allowed_modifications = ('comments',)
        for field, value in self.action_payload.items():
            if (
                field not in allowed_modifications
                and getattr(booking_aggregate.booking, field) != value
            ):
                return False
        return True

    def booking_modification_has_reference_number(self):
        booking_aggregate = self.get_booking_aggregate()
        disallowed_modifications = ('reference_number',)
        for field, value in self.action_payload.items():
            if (
                field in disallowed_modifications
                and getattr(booking_aggregate.booking, field) != value
            ):
                return True
        return False

    def booking_modification_has_travel_agent_or_company_details(self):
        return self.action_payload.get(
            'travel_agent_details'
        ) or self.action_payload.get('company_details')

    def is_room_type_changed_without_price_change(self):
        return not self.action_payload.get('room_type', dict()).get(
            'price_change_required'
        )

    def is_cancelled_or_noshow_booking(self):
        booking_aggregate = self.get_booking_aggregate()
        return booking_aggregate.is_cancelled() or booking_aggregate.is_noshow()

    def is_room_inactive_dnr(self):
        return self.action_payload.get('type') == DNRType.INACTIVE_ROOM

    def is_btc_booking(self):
        booking_aggregate = self.get_booking_aggregate()
        bill_aggregate = self.get_bill_aggregate()
        room_stays_ids = [
            room_stay.room_stay_id for room_stay in booking_aggregate.room_stays
        ]
        charge_ids = booking_aggregate.get_room_stay_charges(room_stays_ids)
        charges = bill_aggregate.filter_and_get_charges(charge_ids)
        for charge in charges:
            if charge.type == ChargeTypes.CREDIT:
                return True
        return False

    def has_updated_customer_name(self):
        first_name = self.action_payload.get('first_name')
        last_name = self.action_payload.get('last_name')
        customer = self.aggregates.get('customer')

        if customer.first_name:
            if first_name:
                if first_name != customer.first_name:
                    return True

        if customer.last_name:
            if last_name:
                if last_name != customer.last_name:
                    return True

        return False

    def has_customer_name_updated_while_checkin(self):
        guest_id_to_guest_mapping = dict()
        for room_stay in self.action_payload.get('room_stays'):
            for guest_stay in room_stay.get('guest_stays'):
                if 'guest_id' in guest_stay and 'guest' in guest_stay:
                    guest_id_to_guest_mapping[guest_stay['guest_id']] = guest_stay[
                        'guest'
                    ]

        if not guest_id_to_guest_mapping:
            return False

        existing_guest_details = self.get_booking_aggregate().customer_dict
        for guest_id, guest_detail in guest_id_to_guest_mapping.items():
            customer_detail = existing_guest_details.get(guest_id)

            if guest_detail.get('first_name'):
                if customer_detail.first_name:
                    if guest_detail.get('first_name') != customer_detail.first_name:
                        return True

            if guest_detail.get('last_name'):
                if customer_detail.last_name:
                    if guest_detail.get('last_name') != customer_detail.last_name:
                        return True
            return False

    def is_attachment_created_by_same_user(self):
        attachment_aggregate = self.aggregates.get('attachment_aggregate')
        return (
            attachment_aggregate.attachment.uploaded_by
            == attachment_aggregate.user_data.user_auth_id
        )

    def is_credit_note_attached_to_any_invoice(self):
        invoice_aggregates = self.aggregates.get('invoice_aggregates')
        return any(
            invoice_aggregate.credit_note_amount > 0
            for invoice_aggregate in invoice_aggregates
        )

    def is_booking_fully_checked_out(self):
        booking_aggregate = self.get_booking_aggregate()
        return booking_aggregate.is_checked_out()

    def is_booking_checked_out(self):
        booking_aggregate = self.get_booking_aggregate()
        return (
            booking_aggregate.is_checked_out()
            or booking_aggregate.is_part_checked_out()
        )

    def can_booking_hold_ta_commission(self):
        booking_aggregate = self.get_booking_aggregate()
        return (
            booking_aggregate.is_commission_applicable()
            and booking_aggregate.get_travel_agent_details()
        )

    def is_cashier_session_open(self):
        cashier_session_aggregate = self.aggregates.get('cashier_session_aggregate')
        return cashier_session_aggregate.cashier_session.is_open()

    def is_cashier_session_closed(self):
        cashier_session_aggregate = self.aggregates.get('cashier_session_aggregate')
        return cashier_session_aggregate.cashier_session.is_closed()

    def are_mandatory_gst_details_provided_on_booking_owner_edit(self):
        if 'gst_details' in self.action_payload:
            gst_details = self.action_payload.get('gst_details')
            return (
                gst_details
                and gst_details.legal_name
                and gst_details.address
                and gst_details.address.field_1
                and gst_details.address.country
                and gst_details.address.state
                and gst_details.address.city
                and gst_details.address.pincode
            )

    def are_mandatory_gst_details_received(self):
        travel_agent_details = self.action_payload.get('travel_agent_details')
        company_details = self.action_payload.get('company_details')

        if travel_agent_details and company_details:
            company_legal_details = company_details.legal_details
            travel_agent_legal_details = travel_agent_details.legal_details
            return (
                company_legal_details
                and company_legal_details.legal_name
                and company_legal_details.address
                and company_legal_details.address.field_1
                and company_legal_details.address.country
                and company_legal_details.address.state
                and company_legal_details.address.city
                and company_legal_details.address.pincode
                and travel_agent_legal_details
                and travel_agent_legal_details.legal_name
                and travel_agent_legal_details.address
                and travel_agent_legal_details.address.field_1
                and travel_agent_legal_details.address.country
                and travel_agent_legal_details.address.state
                and travel_agent_legal_details.address.city
                and travel_agent_legal_details.address.pincode
            )

        if travel_agent_details:
            travel_agent_legal_details = travel_agent_details.legal_details
            return (
                travel_agent_legal_details
                and travel_agent_legal_details.legal_name
                and travel_agent_legal_details.address
                and travel_agent_legal_details.address.field_1
                and travel_agent_legal_details.address.country
                and travel_agent_legal_details.address.state
                and travel_agent_legal_details.address.city
                and travel_agent_legal_details.address.pincode
            )

        if company_details:
            company_legal_details = company_details.legal_details
            return (
                company_legal_details
                and company_legal_details.legal_name
                and company_legal_details.address
                and company_legal_details.address.field_1
                and company_legal_details.address.country
                and company_legal_details.address.state
                and company_legal_details.address.city
                and company_legal_details.address.pincode
            )

        return False

    def is_indian_hotel(self):
        return self.hotel_context.country_code == 'IN'

    def booking_status(self):
        booking_aggregate = self.get_booking_aggregate()
        return booking_aggregate.booking.status.value

    def attachment_type(self):
        attachment_aggregate = self.aggregates.get('attachment_aggregate')
        return attachment_aggregate.attachment.attachment_group.value

    def record_payment_request_received(self):
        return self.action_payload.status in (
            PaymentStatus.DONE,
            PaymentStatus.MAPPED_TO_INVOICE,
        )

    def cancel_payment_request_received(self):
        return self.action_payload.status == PaymentStatus.CANCELLED

    def is_reserved_booking_received(self):
        return self.action_payload.get('status') == BookingStatus.RESERVED

    def is_cashier_outflow_payment_request_received(self):
        return self.action_payload.payment_type == CashierPaymentTypes.OUTFLOW

    def get_new_booking_source(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            return self.action_payload.source
        else:
            return self.action_payload.get("source")

    def is_corporate_booking(self):
        booking_aggregate = self.aggregates.get('booking_aggregate')
        booking_channel = booking_aggregate.booking.source.channel_code
        corporate_channels = [BookingChannels.B2B.value, BookingChannels.TA.value]
        if self.tenant_facts_context and self.tenant_facts_context.corporate_channels:
            corporate_channels = self.tenant_facts_context.corporate_channels
        return booking_channel in corporate_channels
