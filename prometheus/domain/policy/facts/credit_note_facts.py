import datetime

from dateutil.relativedelta import relativedelta
from treebo_commons.utils import dateutils

from prometheus.domain.policy.facts.facts import Facts


class CreditNoteFacts(Facts):
    def __init__(
        self,
        current_time=None,
        user_type=None,
        hotel_context=None,
        action_payload=None,
        **aggregates
    ):
        if aggregates.get('invoice_aggregates'):
            self.grouped_invoices = {
                invoice_aggregate.invoice.invoice_id: invoice_aggregate
                for invoice_aggregate in aggregates.get('invoice_aggregates')
            }
        super().__init__(
            current_time=current_time,
            user_type=user_type,
            action_payload=action_payload,
            hotel_context=hotel_context,
            **aggregates
        )

    def is_previous_financial_year(self, invoice_date: datetime.date):
        current_month = self.current_time.month
        current_year = self.current_time.year
        if invoice_date.year < current_year:
            return True
        if invoice_date.month < 4 <= current_month:
            return True
        return False

    @staticmethod
    def get_financial_year_end(invoice_date):
        invoice_month = invoice_date.month
        invoice_year = invoice_date.year

        if invoice_month < 4:
            return datetime.date(year=invoice_year, month=3, day=31)
        else:
            return datetime.date(year=invoice_year + 1, month=3, day=31)

    def is_credit_note_generated_after_eight_months_of_financial_year(self):
        line_items = (
            self.action_payload.get('credit_note_line_items')
            if self.action_payload
            else []
        )
        if not self.action_payload:
            invoice_dates = [
                invoice_aggregate.invoice.invoice_date
                for invoice_aggregate in self.grouped_invoices.values()
            ]
        else:
            invoice_dates = []
            for line_item in line_items:
                invoice_id = line_item['invoice_id']
                invoice_aggregate = self.grouped_invoices.get(invoice_id)
                invoice_dates.append(invoice_aggregate.invoice.invoice_date)
        for invoice_date in invoice_dates:
            if self.is_previous_financial_year(invoice_date):
                invoice_financial_year_end = self.get_financial_year_end(invoice_date)
                max_credit_note_generation_date = (
                    invoice_financial_year_end + relativedelta(months=+8)
                )
                if (
                    dateutils.to_date(self.current_time)
                    > max_credit_note_generation_date
                ):
                    return True
        return False
