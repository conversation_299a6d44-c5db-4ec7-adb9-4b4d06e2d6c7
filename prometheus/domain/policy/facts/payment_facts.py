from treebo_commons.money import Money

from prometheus.domain.policy.facts import Facts
from ths_common.constants.billing_constants import PaymentStatus, PaymentTypes


class PaymentFacts(Facts):
    def __init__(
        self, user_type=None, action_payload=None, hotel_context=None, **aggregates
    ):
        super().__init__(
            user_type=user_type,
            action_payload=action_payload,
            hotel_context=hotel_context,
            **aggregates
        )

    def is_new_account_getting_created(self):
        for payment_split in self.action_payload.payment_splits:
            billed_entity = self.get_bill_aggregate().get_billed_entity(
                payment_split.billed_entity_account.billed_entity_id
            )
            if not billed_entity.account_exists(
                payment_split.billed_entity_account.account_number
            ):
                return True
        return False

    def get_payment_amount_in_base_currency(self) -> Money:
        return self.action_payload.amount

    def is_editable_refund(self):
        bill_aggregate = self.aggregates["bill_aggregate"]
        payment = bill_aggregate.get_payment(self.action_payload.payment_id)
        if payment.payment_type.value == PaymentTypes.PAYMENT.value:
            return False
        return True

    def is_cancel_booked_refund(self):
        bill_aggregate = self.aggregates["bill_aggregate"]
        payment = bill_aggregate.get_payment(self.action_payload.payment_id)
        if (
            self.action_payload.status == PaymentStatus.CANCELLED.value
            and payment.status == PaymentStatus.BOOKED
            and payment.payment_type.value == PaymentTypes.REFUND.value
        ):
            return True
        return False
