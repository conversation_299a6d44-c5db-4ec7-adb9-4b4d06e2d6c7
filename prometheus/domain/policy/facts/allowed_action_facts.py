from treebo_commons.utils import dateutils

from prometheus.core.globals import HotelContext
from prometheus.domain.policy.facts import Facts
from ths_common.constants.booking_constants import BookingChannels


class AllowedBookingActionFacts(Facts):
    def __init__(
        self,
        guest_stay=None,
        room_stay=None,
        user_type=None,
        action_payload=None,
        current_time=None,
        hotel_context: HotelContext = None,
        **aggregates
    ):
        super().__init__(
            guest_stay=guest_stay,
            room_stay=room_stay,
            current_time=current_time,
            user_type=user_type,
            action_payload=action_payload,
            hotel_context=hotel_context,
            **aggregates
        )
        self.guest_stay = guest_stay
        self.room_stay = room_stay

    def is_walkin_booking(self):
        booking_aggregate = self.aggregates.get('booking_aggregate')
        return (
            booking_aggregate.booking.source.channel_code == BookingChannels.HOTEL.value
        )

    def can_cancel_booking(self, cancellation_time):
        booking_aggregate = self.aggregates.get('booking_aggregate')
        checkin_date = booking_aggregate.booking.checkin_date.date()

        # Override checkin_date if guest_stay or room_stay is present
        if self.guest_stay:
            checkin_date = self.guest_stay.checkin_date.date()
        elif self.room_stay:
            checkin_date = self.room_stay.checkin_date.date()

        current_datetime = dateutils.current_datetime()

        if checkin_date < current_datetime.date():
            return False
        elif checkin_date > current_datetime.date():
            return True
        return current_datetime.time().hour < int(cancellation_time)
