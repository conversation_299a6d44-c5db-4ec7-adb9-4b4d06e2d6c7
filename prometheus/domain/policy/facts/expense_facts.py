from treebo_commons.utils.dateutils import to_date

from prometheus.domain.policy.facts.facts import Facts
from ths_common.constants.billing_constants import ChargeTypes
from ths_common.constants.booking_constants import ExpenseAddedBy


class ExpenseFacts(Facts):
    def __init__(
        self,
        expense,
        price,
        current_time=None,
        user_type=None,
        action_payload=None,
        hotel_context=None,
        **aggregates
    ):
        self.expense = expense
        self.price = price
        super().__init__(
            current_time=current_time,
            user_type=user_type,
            action_payload=action_payload,
            hotel_context=hotel_context,
            **aggregates
        )

    def booking_channel_code(self):
        booking_aggregate = self.aggregates.get('booking_aggregate')
        return booking_aggregate.booking.source.channel_code

    def is_no_show_cancellation_charge_for_b2b_ta_booking(self):
        return self.is_no_show_cancel_charge() and self.is_b2b_ta_booking()

    def is_booking_complete_noshow_cancel(self):
        expense_item_id = self.expense.get('expense_item_id')
        booking_aggregate = self.aggregates.get('booking_aggregate')
        return (
            booking_aggregate.is_cancelled() or booking_aggregate.is_noshow()
        ) and expense_item_id in ["no_show", "booking_cancellation"]

    def is_b2b_ta_booking(self):
        booking_aggregate = self.aggregates.get('booking_aggregate')
        return booking_aggregate.is_b2b_ta_booking()

    def is_no_show_cancel_charge(self):
        expense_item_id = self.expense.get('expense_item_id')
        return expense_item_id in ["no_show", "booking_cancellation"]

    def non_credit_charge(self):
        return self.price.type != ChargeTypes.CREDIT

    def booking_is_checked_in(self):
        booking_aggregate = self.aggregates.get('booking_aggregate')
        return (
            booking_aggregate.is_checked_in()
            or booking_aggregate.is_part_checked_in()
            or booking_aggregate.is_part_checked_out()
        )

    def is_expense_create_request_type_credit(self):
        return self.price.type == ChargeTypes.CREDIT

    def expense_is_being_assigned_to_a_checked_out_guest(self):
        guest_ids = self.expense['guests']
        booking = self.aggregates.get('booking_aggregate')

        guest_stay_mapping = {gs.guest_id: gs for gs in booking.get_all_guest_stays()}

        for guest_id in guest_ids:
            if (
                guest_id in guest_stay_mapping
                and guest_stay_mapping[guest_id].is_checked_out()
            ):
                return True

        return False

    def expense_is_in_the_past(self):
        return to_date(self.current_time) > to_date(self.expense['applicable_date'])

    def added_by_hotel(self):
        return self.expense.get('added_by') == ExpenseAddedBy.HOTEL

    def added_by_treebo(self):
        return self.expense.get('added_by') == ExpenseAddedBy.TREEBO
