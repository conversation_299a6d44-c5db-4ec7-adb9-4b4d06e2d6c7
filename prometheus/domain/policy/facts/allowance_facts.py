from prometheus.domain.policy.facts import Facts


class AllowanceFacts(Facts):
    def __init__(
        self,
        user_data=None,
        user_type=None,
        action_payload=None,
        hotel_context=None,
        **aggregates
    ):
        super().__init__(
            user_data=user_data,
            user_type=user_type,
            action_payload=action_payload,
            hotel_context=hotel_context,
            **aggregates
        )

    def get_allowance(self):
        charge_split = (
            self.get_bill_aggregate()
            .get_charge(self.action_payload.get('charge_id'))
            .get_split(self.action_payload.get('charge_split_id'))
        )
        return charge_split.get_allowance(self.action_payload.get('allowance_id'))
