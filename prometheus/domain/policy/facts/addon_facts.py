from treebo_commons.utils import dateutils

from prometheus.domain.policy.facts.facts import Facts
from ths_common.constants.billing_constants import ChargeTypes
from ths_common.constants.booking_constants import ExpenseAddedBy


class AddonFacts(Facts):
    def __init__(
        self,
        addon,
        current_time=None,
        user_type=None,
        action_payload=None,
        hotel_context=None,
        **aggregates
    ):
        self.addon = addon
        super().__init__(
            current_time=current_time,
            user_type=user_type,
            action_payload=action_payload,
            hotel_context=hotel_context,
            **aggregates
        )

    def added_by_hotel(self):
        return self.addon.get('added_by') == ExpenseAddedBy.HOTEL

    def room_stay_checked_out(self, room_stay_id):
        room_stay = self.aggregates["booking_aggregate"].get_room_stay(room_stay_id)
        return room_stay.is_checked_out()

    def room_stay_reserved(self, room_stay_id):
        room_stay = self.aggregates["booking_aggregate"].get_room_stay(room_stay_id)
        return room_stay.is_reserved()

    def is_past_date_addon_request(self):
        room_stay = self.aggregates["booking_aggregate"].get_room_stay(
            self.action_payload.get('room_stay_id')
            if self.action_payload.get('room_stay_id')
            else self.addon.room_stay_id
        )
        start_relative = self.action_payload.get('start_relative')
        start_date = self.action_payload.get('start_date')
        if not (start_relative or start_date):
            return False
        if start_relative:
            start_date = start_relative.get_absolute_date(
                room_stay.checkin_date, room_stay.checkout_date
            )
        return dateutils.to_date(start_date) < dateutils.to_date(dateutils.today())

    def is_past_dated_addon(self):
        start_date = self.addon.start_date
        return start_date < dateutils.to_date(dateutils.today())

    def is_past_date_in_addon_request_for_end_date(self):
        room_stay = self.aggregates["booking_aggregate"].get_room_stay(
            self.action_payload.get('room_stay_id')
            if self.action_payload.get('room_stay_id')
            else self.addon.room_stay_id
        )
        end_relative = self.action_payload.get('end_relative')
        end_date = self.action_payload.get('end_date')
        if not (end_relative or end_date):
            return False
        if end_relative:
            end_date = end_relative.get_absolute_date(
                room_stay.checkin_date, room_stay.checkout_date
            )
        return dateutils.to_date(end_date) < dateutils.to_date(dateutils.today())

    def is_past_dated_addon_end_date(self):
        end_date = self.addon.end_date
        return end_date < dateutils.to_date(dateutils.today())

    def is_charge_type_request(self):
        return 'charge_type' in self.action_payload

    def is_credit_addon_request(self):
        addon_charge_type = self.action_payload.get('charge_type')
        return addon_charge_type == ChargeTypes.CREDIT

    def is_price_update_request(self):
        return self.action_payload.get('price')

    def is_addon_date_update_request(self):
        return (
            self.action_payload.get('start_date')
            or self.action_payload.get('end_date')
            or self.action_payload.get('start_relative')
            or self.action_payload.get('end_relative')
        )

    def linked_addon_added(self):
        return bool(self.action_payload.get('linked'))

    def is_linked_addon(self):
        return bool(self.addon.linked)
