from prometheus.domain.report.entities.ar_ledger import ARLedger


class ARLedgerAggregate(object):
    def __init__(self, ar_ledger: ARLedger):
        self.ar_ledger = ar_ledger

    @property
    def opening_balance_in_base_currency(self):
        return (
            self.ar_ledger.opening_balance_in_base_currency if self.ar_ledger else None
        )

    @property
    def closing_balance_in_base_currency(self):
        return (
            self.ar_ledger.closing_balance_in_base_currency if self.ar_ledger else None
        )

    @property
    def total_payments(self):
        return self.ar_ledger.total_payments if self.ar_ledger else None

    @property
    def total_charges(self):
        return self.ar_ledger.total_charges if self.ar_ledger else None

    def check_invariance(self):
        pass
