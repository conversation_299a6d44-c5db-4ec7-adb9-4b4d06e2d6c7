# coding=utf-8
"""
Deposit ledger repository
"""
from sqlalchemy import func

from object_registry import register_instance
from prometheus.domain.report.aggregates.deposit_ledger_aggregate import (
    DepositLedgerAggregate,
)
from prometheus.domain.report.models import DepositLedgerReportModel
from prometheus.domain.report.repositories.adaptors.deposit_ledger_adaptor import (
    DepositLedgerDBAdapter,
)
from prometheus.infrastructure.database.base_repository import BaseRepository


@register_instance()
class DepositLedgerRepository(BaseRepository):
    """
    Deposit Ledger repository
    """

    deposit_ledger_adaptor = DepositLedgerDBAdapter()

    def save(self, deposit_ledger_aggregate):
        """
        Saves the deposit_ledger_aggregate in DB

        :param deposit_ledger_aggregate:
        """
        deposit_ledger_aggregate.check_invariance()
        deposit_ledger_model = DepositLedgerDBAdapter.to_db_model(
            deposit_ledger_aggregate.deposit_ledger
        )

        self._save(deposit_ledger_model)
        self.flush_session()

    def to_aggregate(self, deposit_ledger_model):
        deposit_ledger = (
            self.deposit_ledger_adaptor.to_entity(deposit_ledger_model)
            if deposit_ledger_model
            else None
        )
        deposit_ledger_aggregate = DepositLedgerAggregate(deposit_ledger=deposit_ledger)
        return deposit_ledger_aggregate

    def load_for_date_range(self, start_date, end_date, hotel_id):
        if not start_date and end_date:
            return []
        q = self.query(DepositLedgerReportModel)
        q = q.filter(func.Date(DepositLedgerReportModel.report_date) >= start_date)
        q = q.filter(func.Date(DepositLedgerReportModel.report_date) <= end_date)
        if hotel_id:
            q = q.filter(DepositLedgerReportModel.hotel_id == hotel_id)
        deposit_ledger_models = q.all()
        return [
            self.to_aggregate(deposit_ledger_model=deposit_ledger_model)
            for deposit_ledger_model in deposit_ledger_models
        ]
