from decimal import Decimal

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.report.entities.guest_ledger_v2 import GuestLedgerV2
from prometheus.domain.report.models import GuestLedgerReportModelV2


class GuestLedgerDBAdapterV2(object):
    @staticmethod
    def to_db_model(guest_ledger):
        # noinspection PyArgumentList
        return GuestLedgerReportModelV2(
            business_date=guest_ledger.business_date,
            booking_id=guest_ledger.booking_id,
            opening_balance_in_base_currency=guest_ledger.opening_balance_in_base_currency.amount,
            total_payments=guest_ledger.total_payments.amount,
            total_charges=guest_ledger.total_charges.amount
            if guest_ledger.total_charges
            else Decimal(0),
            closing_balance_in_base_currency=guest_ledger.closing_balance_in_base_currency.amount,
            base_currency=guest_ledger.base_currency.value,
            deposit_transferred_at_checkin=guest_ledger.deposit_transferred_at_checkin.amount,
            payment_components=guest_ledger.payment_components,
            revenue_components=guest_ledger.revenue_components,
            non_revenue_components=guest_ledger.non_revenue_components,
            hotel_id=guest_ledger.hotel_id,
            charges_transferred_to_ar=guest_ledger.charges_transferred_to_ar.amount,
            deleted=guest_ledger.deleted,
        )

    @staticmethod
    def to_entity(guest_ledger_model):
        base_currency = CurrencyType(guest_ledger_model.base_currency)
        return GuestLedgerV2(
            business_date=guest_ledger_model.business_date,
            booking_id=guest_ledger_model.booking_id,
            opening_balance_in_base_currency=Money(
                guest_ledger_model.opening_balance_in_base_currency, base_currency
            ),
            total_payments=Money(guest_ledger_model.total_payments, base_currency),
            total_charges=Money(guest_ledger_model.total_charges, base_currency),
            closing_balance_in_base_currency=Money(
                guest_ledger_model.closing_balance_in_base_currency, base_currency
            ),
            deposit_transferred_at_checkin=Money(
                guest_ledger_model.deposit_transferred_at_checkin, base_currency
            ),
            base_currency=base_currency,
            payment_components=guest_ledger_model.payment_components,
            revenue_components=guest_ledger_model.revenue_components,
            non_revenue_components=guest_ledger_model.non_revenue_components,
            charges_transferred_to_ar=Money(
                0
                if guest_ledger_model.charges_transferred_to_ar is None
                else guest_ledger_model.charges_transferred_to_ar,
                base_currency,
            ),
            hotel_id=guest_ledger_model.hotel_id,
            created_at=guest_ledger_model.created_at,
            deleted=guest_ledger_model.deleted,
        )
