from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.report.entities.deposit_ledger_V2 import DepositLedgerV2
from prometheus.domain.report.models import DepositLedgerReportModelV2


class DepositLedgerDBAdapterV2(object):
    @staticmethod
    def to_db_model(deposit_ledger):
        # noinspection PyArgumentList
        return DepositLedgerReportModelV2(
            business_date=deposit_ledger.business_date,
            hotel_id=deposit_ledger.hotel_id,
            opening_balance_in_base_currency=deposit_ledger.opening_balance_in_base_currency.amount,
            total_payments=deposit_ledger.total_payments.amount
            if deposit_ledger.total_payments
            else 0,
            total_charges=deposit_ledger.total_charges.amount
            if deposit_ledger.total_charges
            else 0,
            closing_balance_in_base_currency=deposit_ledger.closing_balance_in_base_currency.amount,
            deposit_transferred_at_checkin=deposit_ledger.deposit_transferred_at_checkin.amount
            if (deposit_ledger.deposit_transferred_at_checkin)
            else 0,
            payment_components=deposit_ledger.payment_components,
            revenue_components=deposit_ledger.revenue_components,
            non_revenue_components=deposit_ledger.non_revenue_components,
            base_currency=deposit_ledger.base_currency.value,
            deleted=deposit_ledger.deleted,
        )

    @staticmethod
    def to_entity(deposit_ledger_model):
        base_currency = CurrencyType(deposit_ledger_model.base_currency)
        return DepositLedgerV2(
            business_date=deposit_ledger_model.business_date,
            opening_balance_in_base_currency=Money(
                deposit_ledger_model.opening_balance_in_base_currency, base_currency
            ),
            total_payments=Money(deposit_ledger_model.total_payments, base_currency),
            total_charges=Money(deposit_ledger_model.total_charges, base_currency),
            closing_balance_in_base_currency=Money(
                deposit_ledger_model.closing_balance_in_base_currency, base_currency
            ),
            deposit_transferred_at_checkin=Money(
                deposit_ledger_model.deposit_transferred_at_checkin, base_currency
            ),
            hotel_id=deposit_ledger_model.hotel_id,
            base_currency=base_currency,
            payment_components=deposit_ledger_model.payment_components,
            revenue_components=deposit_ledger_model.revenue_components,
            non_revenue_components=deposit_ledger_model.non_revenue_components,
            deleted=deposit_ledger_model.deleted,
        )
