from marshmallow import Schema, fields


class ChargeAndAllowanceDetailSchema(Schema):
    bill_id = fields.String()
    sku_category_id = fields.String()
    pre_tax = fields.String()
    tax_precentage = fields.String()
    cgst = fields.String()
    sgst = fields.String()
    igst = fields.String()
    vat = fields.String()
    charge_type = fields.String()
    billed_entity_id = fields.String()
    folio_number = fields.String()
    invoice_id = fields.String()
    credit_note_id = fields.String()


class PaymentAndRefundDetailSchema(Schema):
    bill_id = fields.String()
    date_of_payment = fields.String()
    amount = fields.String()
    payment_mode = fields.String()
    payment_ref_id = fields.String()
    billed_entity_id = fields.String()
    folio_number = fields.String()
    advance_receipt = fields.String()


class FolioDetailSchema(Schema):
    reference_number = fields.String()
    bill_id = fields.String()
    nature_of_supply = fields.String()
    customer_id = fields.String()
    customer_number = fields.String()
    folio_name = fields.String()
    folio_address = fields.String()
    gstin_num = fields.String()
    billed_entity_id = fields.String()
    customer_state = fields.String()
    folio_pincode = fields.String()
    folio_country = fields.String()


class HotelDetailsSchema(Schema):
    hotel_id = fields.String()
    hotel_state = fields.String()


class SkuCategorySchema(Schema):
    sku_category_id = fields.String()
    hsn_code = fields.String()


class AllowanceInvoiceDetailSchema(Schema):
    invoice_id = fields.String()
    invoice_number = fields.String()
    irn = fields.String()
    irp_ack_number = fields.String()
    irp_ack_date = fields.String()
    folio_number = fields.String()
    bill_id = fields.String()
    billed_entity_id = fields.String()


class InvoiceAndCreditNoteDetailsSchema(Schema):
    amount = fields.String()
    bill_id = fields.String()
    billed_entity_id = fields.String()
    folio_number = fields.String()
    invoice_number = fields.String()
    irn = fields.String()
    irp_ack_number = fields.String()
    irp_ack_date = fields.String()
    billed_entity_account_number = fields.String()
    invoice_id = fields.String()


class ERPEventSchema(Schema):
    charges = fields.List(fields.Nested(ChargeAndAllowanceDetailSchema))
    allowances = fields.List(fields.Nested(ChargeAndAllowanceDetailSchema))
    payments = fields.List(fields.Nested(PaymentAndRefundDetailSchema))
    refunds = fields.List(fields.Nested(PaymentAndRefundDetailSchema))
    folio_details = fields.List(fields.Nested(FolioDetailSchema))
    hotel_details = fields.Nested(HotelDetailsSchema)
    sku_categories = fields.List(fields.Nested(SkuCategorySchema))
    invoice_details = fields.List(fields.Nested(AllowanceInvoiceDetailSchema))
    invoices = fields.List(fields.Nested(InvoiceAndCreditNoteDetailsSchema))
    credit_notes = fields.List(fields.Nested(InvoiceAndCreditNoteDetailsSchema))
