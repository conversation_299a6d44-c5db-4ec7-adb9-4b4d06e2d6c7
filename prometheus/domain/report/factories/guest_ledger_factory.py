# coding=utf-8
"""
Guest Ledger factory
"""
from treebo_commons.utils import dateutils

from prometheus.domain.report.aggregates.guest_ledger_aggregate import (
    GuestLedgerAggregate,
)
from prometheus.domain.report.entities.guest_ledger import GuestLedger


class GuestLedgerFactory:
    @classmethod
    def create_new_guest_ledger(
        cls,
        booking_id,
        opening_balance_in_base_currency,
        closing_balance_in_base_currency,
        total_payments,
        total_charges,
        deposit_transferred_at_checkin,
        payment_components,
        revenue_components,
        non_revenue_components,
        hotel_id,
        base_currency,
        charges_transferred_to_ar,
        report_date=dateutils.current_datetime(),
    ):
        guest_ledger = GuestLedger(
            booking_id=booking_id,
            report_date=report_date,
            opening_balance_in_base_currency=opening_balance_in_base_currency,
            closing_balance_in_base_currency=closing_balance_in_base_currency,
            total_payments=total_payments,
            total_charges=total_charges,
            deposit_transferred_at_checkin=deposit_transferred_at_checkin,
            payment_components=payment_components,
            revenue_components=revenue_components,
            non_revenue_components=non_revenue_components,
            hotel_id=hotel_id,
            base_currency=base_currency,
            charges_transferred_to_ar=charges_transferred_to_ar,
        )
        return GuestLedgerAggregate(guest_ledger=guest_ledger)
