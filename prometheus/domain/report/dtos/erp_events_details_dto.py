from ths_common.constants.booking_constants import NatureOfSupply, ProfileTypes


class ERPEventDto:
    def __init__(self, erp_event_data):
        self.charges = erp_event_data.get('charges')
        self.allowances = erp_event_data.get('allowances')
        self.payments = erp_event_data.get('payments')
        self.refunds = erp_event_data.get('refunds')
        self.folio_details = erp_event_data.get('folio_details')
        self.hotel_details = erp_event_data.get('hotel_details')
        self.sku_categories = erp_event_data.get('sku_categories')
        self.invoice_details = erp_event_data.get('invoice_details')
        self.invoices = erp_event_data.get('invoices')
        self.credit_notes = erp_event_data.get('credit_notes')


class HotelDetailsDto:
    def __init__(self, hotel_id: str, hotel_state: str):
        self.hotel_id = hotel_id
        self.hotel_state = hotel_state


class SkuCategoryDto:
    def __init__(self, sku_category_id: str, hsn_code: str):
        self.sku_category_id = sku_category_id
        self.hsn_code = hsn_code


class ErpFolioDetailsDto:
    def __init__(self, erp_booking_details=None):
        self.reference_number = erp_booking_details.get('reference_number')
        self.bill_id = erp_booking_details.get('bill_id')
        self.nature_of_supply = erp_booking_details.get('nature_of_supply')
        self.customer_id = erp_booking_details.get('external_ref_id')
        self.customer_number = erp_booking_details.get('customer_id')
        self.folio_name = erp_booking_details.get('name')
        self.folio_address = erp_booking_details.get('address')
        self.gstin_num = erp_booking_details.get('gstin_num')
        self.billed_entity_id = erp_booking_details.get('billed_entity_id')
        self.customer_state = erp_booking_details.get('customer_state')
        self.folio_pincode = erp_booking_details.get('pincode')
        self.folio_country = erp_booking_details.get('country')

    @staticmethod
    def fetch_folio_details_from_booking_customer(
        reference_number, bill_id, booking_customer
    ):
        if (
            booking_customer.profile_type
            and booking_customer.profile_type.value == ProfileTypes.CORPORATE.value
        ):
            nature_of_supply = NatureOfSupply.B2B.value
            name = (
                booking_customer.gst_details.legal_name
                if booking_customer.gst_details
                else None
            )
            address = (
                booking_customer.gst_details.address
                if booking_customer.gst_details
                else None
            )
        else:
            nature_of_supply = NatureOfSupply.B2C.value
            name = booking_customer.first_name + (
                booking_customer.last_name if booking_customer.last_name else ''
            )
            address = booking_customer.address

        if address:
            address, pincode, state, country = fetch_address_detail_for_folio(address)
        else:
            address, pincode, state, country = None, None, None, None
        gstin_num = (
            booking_customer.gst_details.gstin_num
            if booking_customer.gst_details
            else None
        )

        return ErpFolioDetailsDto(
            dict(
                reference_number=reference_number,
                bill_id=bill_id,
                nature_of_supply=nature_of_supply,
                external_ref_id=booking_customer.external_ref_id,
                customer_id=booking_customer.customer_id,
                name=name,
                address=address,
                gstin_num=gstin_num,
                billed_entity_id=booking_customer.billed_entity_id,
                customer_state=state,
                pincode=pincode,
                country=country,
            )
        )

    @staticmethod
    def fetch_folio_details_from_company(reference_number, bill_id, booking_company):
        if booking_company.legal_details.address:
            address, pincode, state, country = fetch_address_detail_for_folio(
                booking_company.legal_details.address
            )
        else:
            address, pincode, state, country = None, None, None, None
        return ErpFolioDetailsDto(
            dict(
                reference_number=reference_number,
                bill_id=bill_id,
                nature_of_supply=NatureOfSupply.B2B.value,
                external_ref_id=booking_company.legal_details.external_reference_id,
                customer_id=None,
                name=booking_company.legal_details.legal_name,
                address=address,
                gstin_num=booking_company.legal_details.tin,
                billed_entity_id=booking_company.billed_entity_id,
                customer_state=state,
                pincode=pincode,
                country=country,
            )
        )


def fetch_address_detail_for_folio(address_detail):
    address = ' '.join(
        filter(
            None,
            (
                address_detail.field_1,
                address_detail.field_2,
                address_detail.city,
                address_detail.state,
                address_detail.pincode,
            ),
        )
    )
    pincode = address_detail.pincode
    state = address_detail.state
    country = address_detail.country
    return address, pincode, state, country
