from treebo_commons.money.constants import CurrencyType


class GuestLedger(object):
    """
    Deposit Ledger Entity
    """

    def __init__(
        self,
        report_date,
        opening_balance_in_base_currency,
        total_payments,
        total_charges,
        booking_id,
        closing_balance_in_base_currency,
        deposit_transferred_at_checkin,
        payment_components,
        revenue_components,
        non_revenue_components,
        hotel_id,
        charges_transferred_to_ar,
        base_currency=CurrencyType.INR,
        created_at=None,
        deleted=False,
    ):
        self.report_date = report_date
        self.opening_balance_in_base_currency = opening_balance_in_base_currency
        self.total_payments = total_payments
        self.total_charges = total_charges
        self.booking_id = booking_id
        self.closing_balance_in_base_currency = closing_balance_in_base_currency
        self.deposit_transferred_at_checkin = deposit_transferred_at_checkin
        self.base_currency = base_currency
        self.payment_components = payment_components
        self.revenue_components = revenue_components
        self.non_revenue_components = non_revenue_components
        self.hotel_id = hotel_id
        self.charges_transferred_to_ar = charges_transferred_to_ar
        self.created_at = created_at
        self.deleted = deleted
