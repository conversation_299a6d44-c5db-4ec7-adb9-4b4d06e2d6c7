from treebo_commons.money.constants import CurrencyType


class DepositLedgerV2(object):
    """
    Deposit Ledger Entity
    """

    def __init__(
        self,
        business_date,
        opening_balance_in_base_currency,
        total_payments,
        total_charges,
        deposit_transferred_at_checkin,
        closing_balance_in_base_currency,
        hotel_id,
        payment_components,
        revenue_components,
        non_revenue_components,
        base_currency=CurrencyType.INR,
        deleted=False,
    ):
        self.business_date = business_date
        self.opening_balance_in_base_currency = opening_balance_in_base_currency
        self.total_payments = total_payments
        self.total_charges = total_charges
        self.deposit_transferred_at_checkin = deposit_transferred_at_checkin
        self.closing_balance_in_base_currency = closing_balance_in_base_currency
        self.hotel_id = hotel_id
        self.payment_components = payment_components
        self.revenue_components = revenue_components
        self.non_revenue_components = non_revenue_components
        self.base_currency = base_currency
        self.deleted = deleted
