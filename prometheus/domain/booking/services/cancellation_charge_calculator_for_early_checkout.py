from treebo_commons.money import Money
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import date_range

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.billing.dto.taxable_item import TaxableItem
from prometheus.domain.billing.services import TaxService
from prometheus.domain.booking.repositories import ExpenseItemRepository
from ths_common.constants.booking_constants import CancellationChargeType


@register_instance(dependencies=[ExpenseItemRepository, TaxService])
class CancellationChargeCalculatorForEarlyCheckout(object):
    def __init__(
        self, expense_item_repository: ExpenseItemRepository, tax_service: TaxService
    ):
        self.expense_item_repository = expense_item_repository
        self.tax_service = tax_service

    def calculate_room_stay_cancellation_charge(
        self,
        room_stay_id,
        datewise_charges,
        cancellation_datetime,
        cancellation_start_date,
        booking_aggregate,
        bill_aggregate,
        cancellation_end_date,
        is_rate_manager_enabled,
        **kwargs
    ):
        use_posttax_for_tax_calculation = kwargs.get(
            'use_posttax_for_tax_calculation', False
        )
        cancellation_charge_expense_item = kwargs.get(
            'cancellation_charge_expense_item'
        )
        """Calculate total cancellation charge for a room stay"""
        if is_rate_manager_enabled:
            cancellation_charge = (
                self.calculate_for_room_stay_with_rate_plan_for_early_checkout(
                    booking_aggregate,
                    bill_aggregate,
                    room_stay_id,
                    datewise_charges,
                    cancellation_datetime,
                    cancellation_start_date,
                    cancellation_end_date,
                )
            )
            return self.calculate_posttax_cancellation_charge(
                booking_aggregate,
                pretax_amount=cancellation_charge
                if not use_posttax_for_tax_calculation
                else None,
                posttax_amount=cancellation_charge
                if use_posttax_for_tax_calculation
                else None,
                cancellation_charge_expense_item=cancellation_charge_expense_item,
            )

    @staticmethod
    def calculate_for_room_stay_with_rate_plan_for_early_checkout(
        booking_aggregate,
        bill_aggregate,
        room_stay_id,
        datewise_charges,
        cancellation_datetime,
        cancellation_start_date,
        cancellation_end_date,
    ):
        rate_plan_id_dict = dict()
        room_stay = booking_aggregate.get_room_stay(room_stay_id)

        total_cancellation_charge = Money(0, bill_aggregate.bill.base_currency)
        for cancellation_date in date_range(
            end_date=cancellation_end_date,
            start_date=cancellation_start_date,
            end_inclusive=True,
        ):
            if dateutils.to_date(cancellation_date) == dateutils.to_date(
                room_stay.checkout_date
            ):
                for value in rate_plan_id_dict.values():
                    total_cancellation_charge += value.get("amount")
                return total_cancellation_charge

            rate_plan_id = [
                rp.rate_plan_id
                for rp in room_stay.room_rate_plans
                if rp.stay_date == dateutils.to_date(cancellation_date)
            ][0]
            rate_plan = booking_aggregate.get_rate_plan(rate_plan_id)
            if not rate_plan.policies or not rate_plan.policies.cancellation_policies:
                continue

            (
                cancellation_type,
                cancellation_charge_value,
            ) = rate_plan.get_cancellation_details(
                booking_aggregate.booking.checkin_date, cancellation_datetime
            )

            per_day_charge = datewise_charges.get(
                dateutils.to_date(cancellation_date)
            ) or Money(0, bill_aggregate.bill.base_currency)

            if rate_plan_id not in rate_plan_id_dict.keys():
                if (
                    cancellation_type
                    == CancellationChargeType.PERCENT_BOOKING_VALUE.value
                ):
                    amount = per_day_charge * (int(cancellation_charge_value) / 100)
                else:
                    charge_period_start = crs_context.hotel_context.checkout_time
                    cancellation_date = cancellation_datetime.date()
                    cancellation_time = cancellation_datetime.time()
                    if cancellation_time < charge_period_start:
                        if datewise_charges.get(
                            dateutils.add(dateutils.to_date(cancellation_date), days=-1)
                        ):
                            first_night_charge = datewise_charges.get(
                                dateutils.add(
                                    dateutils.to_date(cancellation_date), days=-1
                                )
                            )
                        else:
                            first_night_charge = datewise_charges.get(
                                dateutils.to_date(cancellation_date)
                            )
                    else:
                        if datewise_charges.get(
                            dateutils.add(dateutils.to_date(cancellation_date), days=0)
                        ):
                            first_night_charge = datewise_charges.get(
                                dateutils.add(
                                    dateutils.to_date(cancellation_date), days=0
                                )
                            )
                        else:
                            first_night_charge = datewise_charges.get(
                                dateutils.add(
                                    dateutils.to_date(cancellation_date), days=1
                                )
                            )
                    first_night_charge = first_night_charge or Money(
                        0, bill_aggregate.bill.base_currency
                    )
                    amount = first_night_charge * (int(cancellation_charge_value) / 100)
                rate_plan_id_dict[rate_plan_id] = {
                    "cancellation_type": cancellation_type,
                    "amount": amount,
                }
            elif (
                rate_plan_id_dict.get(rate_plan_id, {}).get("cancellation_type")
                == CancellationChargeType.PERCENT_BOOKING_VALUE.value
            ):
                rate_plan_id_dict[rate_plan_id]["amount"] += per_day_charge * (
                    int(cancellation_charge_value) / 100
                )

        return total_cancellation_charge

    @staticmethod
    def calculate_for_room_stay_without_rate_plan(
        booking_aggregate, bill_aggregate, room_stay, cancellation_datetime
    ):
        if cancellation_datetime < (
            room_stay.checkin_date - dateutils.timedelta(days=1)
        ):
            return Money(0, crs_context.hotel_context.base_currency)
        else:
            return bill_aggregate.get_charge(
                booking_aggregate.per_night_charge(room_stay.room_stay_id)
            ).get_posttax_amount_post_allowance()

    def calculate_posttax_cancellation_charge(
        self,
        booking_aggregate,
        pretax_amount=None,
        posttax_amount=None,
        cancellation_charge_expense_item=None,
    ):
        hotel_context = crs_context.hotel_context
        expense_item = cancellation_charge_expense_item
        applicable_date = dateutils.datetime_at_given_time(
            hotel_context.current_date(),
            dateutils.to_time(booking_aggregate.booking.checkin_date),
        )
        taxable_item = TaxableItem(
            sku_category_id=expense_item.sku_category_id,
            applicable_date=applicable_date,
            pretax_amount=pretax_amount,
            posttax_amount=posttax_amount,
        )
        booking_owner_gst_details = booking_aggregate.booking_owner_gst_details()
        taxable_items = self.tax_service.calculate_taxes(
            [taxable_item],
            buyer_gst_details=booking_owner_gst_details,
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, crs_context.get_hotel_context()
            ),
            hotel_id=hotel_context.hotel_id,
        )
        return taxable_items[0].posttax_amount
