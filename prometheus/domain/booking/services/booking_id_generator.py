import random


class BookingIdGenerator(object):
    @staticmethod
    def rand_x_digit_num(x=10, leading_zeroes=False):
        """Return an X digit number, leading_zeroes returns a string, otherwise int"""
        if not leading_zeroes:
            # wrap with str() for uniform results
            return random.randint(10 ** (x - 1), 10**x - 1)
        else:
            return '{0:0{x}d}'.format(random.randint(0, 10**x - 1), x=x)

    @staticmethod
    def generate(booking_source):
        booking_id = "{random_num}"
        random_num = BookingIdGenerator.rand_x_digit_num(x=12, leading_zeroes=False)
        booking_id = booking_id.format(random_num=random_num)
        booking_id = '-'.join(
            booking_id[i : i + 4] for i in range(0, len(booking_id), 4)
        )
        return booking_id
