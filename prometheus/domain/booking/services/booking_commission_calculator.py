from typing import List

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.booking.dtos import RoomNightTACommissionDto
from prometheus.domain.booking.dtos.commission_calculation_dtos import CommissionQuery
from prometheus.infrastructure.external_clients.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from ths_common.value_objects import TACommissionDetails, TADetails


@register_instance(dependencies=[CompanyProfileServiceClient])
class BookingCommissionCalculator:
    def __init__(self, company_profile_service_client: CompanyProfileServiceClient):
        self.company_profile_service_client = company_profile_service_client

    def calculate_commission(
        self,
        hotel_id,
        booking_id,
        commission_items: List[RoomNightTACommissionDto],
        travel_agent_details: TADetails,
        booking_creation_date,
    ):
        ta_commission_details = travel_agent_details.ta_commission_details
        booking_commission = (
            dict(
                commission_type=ta_commission_details.commission_type.value,
                commission_value=ta_commission_details.commission_value,
                post_commission_amount=True,
                commission_tax=ta_commission_details.commission_tax.to_json()
                if ta_commission_details and ta_commission_details.has_tax_rule()
                else None,
            )
            if travel_agent_details.has_commission_rule()
            and ta_commission_details.commission_type
            else None
        )
        base_currency = (
            crs_context.hotel_context.base_currency
            if (crs_context.hotel_context and crs_context.hotel_context.base_currency)
            else CurrencyType.INR
        )

        # order can get lost, index will be echo back (similar to tax service)
        request = [
            CommissionQuery(index=index, room_night_price=item.room_night_price)
            for index, item in enumerate(commission_items)
        ]
        commission, response = self.company_profile_service_client.calculate_commission(
            hotel_id=hotel_id,
            booking_id=booking_id,
            superhero_company_code=travel_agent_details.legal_details.external_reference_id
            if travel_agent_details.has_super_hero_profile_tagged()
            else None,
            applicable_date=dateutils.date_to_ymd_str(booking_creation_date),
            booking_commission=booking_commission,
            commission_items=request,
        )
        if commission and (
            not travel_agent_details.has_commission_rule()
            or (
                commission.get('commission_tax')
                and ta_commission_details
                and not ta_commission_details.has_tax_rule()
            )
        ):
            travel_agent_details.ta_commission_details = TACommissionDetails.from_json(
                commission
            )

        for commission_response in response:
            commission_items[commission_response.index].pretax_amount = Money(
                commission_response.commission_amount,
                currency=base_currency,
            )
            commission_items[commission_response.index].posttax_amount = Money(
                commission_response.commission_amount, currency=base_currency
            ) + Money(commission_response.commission_tax_amount, currency=base_currency)

        return commission_items
