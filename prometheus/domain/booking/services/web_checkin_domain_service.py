from typing import List

from object_registry import register_instance
from prometheus.domain.booking.aggregates.web_checkin_aggregate import (
    WebCheckinAggregate,
)
from prometheus.domain.booking.domain_events.web_checkin_performed import (
    WebCheckInModifiedEvent,
    WebCheckInPerformedEvent,
)
from prometheus.domain.domain_events.domain_event_registry import register_event


@register_instance()
class WebCheckInDomainService:
    @staticmethod
    def register_web_checkin_performed_event(
        web_checkin_aggregate: WebCheckinAggregate,
    ):
        web_checkin = web_checkin_aggregate.web_checkin
        register_event(
            WebCheckInPerformedEvent(
                booking_id=web_checkin.booking_id,
                web_checkin_id=web_checkin.web_checkin_id,
                status=web_checkin.status,
                guest_ids=web_checkin.guest_ids,
            )
        )

    @staticmethod
    def register_web_checkin_modified_event(web_checkin_aggregate: WebCheckinAggregate):
        web_checkin = web_checkin_aggregate.web_checkin
        register_event(
            WebCheckInModifiedEvent(
                booking_id=web_checkin.booking_id,
                web_checkin_id=web_checkin.web_checkin_id,
                status=web_checkin.status,
                guest_ids=web_checkin.guest_ids,
            )
        )
