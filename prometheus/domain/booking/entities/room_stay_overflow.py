# coding=utf-8
"""
booking overflow
"""
from ths_common.base_entity import EntityChangeTracker


class RoomStayOverflow(EntityChangeTracker):
    def __init__(
        self,
        hotel_id,
        booking_id,
        room_stay_id,
        room_type_id,
        start_date,
        end_date,
        deleted=False,
        dirty=True,
        new=True,
    ):
        super().__init__(dirty=dirty, new=new)
        self.hotel_id = hotel_id
        self.booking_id = booking_id
        self.room_stay_id = room_stay_id
        self.room_type_id = room_type_id
        self.start_date = start_date
        self.end_date = end_date
        self.deleted = deleted

    def delete(self):
        self.deleted = True
