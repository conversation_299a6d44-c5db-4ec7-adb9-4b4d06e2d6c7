from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.web_checkin_constants import WebCheckinStatus


class WebCheckin(EntityChangeTracker):
    def __init__(self, web_checkin_id, booking_id, status=None, guest_ids=None):
        super().__init__()

        self.booking_id = booking_id
        self.web_checkin_id = web_checkin_id
        self.status = status if status else WebCheckinStatus.PENDING
        self.guest_ids = guest_ids if guest_ids else []
