from treebo_commons.utils import dateutils

from prometheus import crs_context
from ths_common.base_entity import EntityChangeTracker


class GuestAllocation(EntityChangeTracker):
    __slots__ = (
        'created_at',
        'modified_at',
        'guest_allocation_id',
        'guest_id',
        'assigned_by',
        'checkin_date',
        'checkout_date',
        'is_current',
        'deleted',
        'checkin_business_date',
        'checkout_business_date',
    )

    def __init__(
        self,
        guest_allocation_id,
        guest_id,
        assigned_by,
        checkin_date,
        checkout_date,
        is_current,
        checkin_business_date=None,
        checkout_business_date=None,
        deleted=False,
        created_at=None,
        modified_at=None,
        dirty=True,
        new=True,
    ):
        super().__init__(dirty=dirty, new=new)
        self.created_at = created_at if created_at else dateutils.current_datetime()
        self.modified_at = modified_at if modified_at else dateutils.current_datetime()
        self.guest_allocation_id = guest_allocation_id
        self.guest_id = guest_id
        self.assigned_by = assigned_by
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.is_current = is_current
        self.deleted = deleted
        self.checkin_business_date = checkin_business_date
        self.checkout_business_date = checkout_business_date

    @property
    def stay_start(self):
        if self.checkin_date:
            hotel_context = crs_context.get_hotel_context()
            return hotel_context.hotel_checkin_date(self.checkin_date)
        else:
            return None

    @property
    def stay_end(self):
        if self.checkout_date:
            hotel_context = crs_context.get_hotel_context()
            if self.is_current:
                return hotel_context.hotel_checkout_date(self.checkout_date)
            else:
                return hotel_context.hotel_checkin_date(self.checkout_date)
        else:
            return None

    def update_checkout_date(self, checkout_date):
        self.checkout_date = checkout_date
        if checkout_date:
            self.checkout_business_date = crs_context.get_hotel_context().current_date()
        else:
            self.checkout_business_date = None
        self.mark_dirty()

    def update_checkin_date(self, checkin_date):
        self.checkin_date = checkin_date
        if checkin_date:
            self.checkin_business_date = crs_context.get_hotel_context().current_date()
        else:
            self.checkin_business_date = None
        self.mark_dirty()

    def update_guest_id(self, guest_id):
        self.guest_id = guest_id
        self.mark_dirty()

    def mark_deleted(self):
        self.deleted = True
        self.mark_dirty()

    def set_is_current(self, is_current):
        self.is_current = is_current
        self.mark_dirty()
