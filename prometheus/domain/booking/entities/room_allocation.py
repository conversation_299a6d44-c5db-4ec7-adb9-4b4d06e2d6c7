from treebo_commons.utils import dateutils

from prometheus import crs_context
from ths_common.base_entity import EntityChangeTracker


class RoomAllocation(EntityChangeTracker):
    __slots__ = (
        'created_at',
        'modified_at',
        'room_allocation_id',
        'room_stay_id',
        'room_id',
        'room_type_id',
        'assigned_by',
        'checkin_date',
        'checkout_date',
        'room_no',
        'deleted',
        'is_current',
        'room_allotment_id',
        'overridden',
        'checkin_business_date',
        'checkout_business_date',
    )

    def __init__(
        self,
        room_allocation_id,
        room_stay_id,
        room_id,
        room_type_id,
        assigned_by,
        checkin_date,
        checkout_date,
        room_no=None,
        deleted=False,
        is_current=True,
        created_at=None,
        modified_at=None,
        room_allotment_id=None,
        overridden=False,
        checkin_business_date=None,
        checkout_business_date=None,
        dirty=True,
        new=True,
    ):
        super().__init__(dirty=dirty, new=new)
        self.created_at = created_at if created_at else dateutils.current_datetime()
        self.modified_at = modified_at if modified_at else dateutils.current_datetime()
        self.room_allocation_id = room_allocation_id
        self.room_stay_id = room_stay_id
        self.room_id = room_id
        self.room_type_id = room_type_id
        self.assigned_by = assigned_by
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.room_no = room_no
        self.deleted = deleted
        self.is_current = is_current
        self.room_allotment_id = room_allotment_id
        self.overridden = overridden
        self.checkin_business_date = checkin_business_date
        self.checkout_business_date = checkout_business_date

    @property
    def stay_start(self):
        if self.checkin_date:
            hotel_context = crs_context.get_hotel_context()
            return hotel_context.hotel_checkin_date(self.checkin_date)
        else:
            return None

    @property
    def stay_end(self):
        if self.checkout_date:
            hotel_context = crs_context.get_hotel_context()
            if self.is_current:
                return hotel_context.hotel_checkout_date(self.checkout_date)
            else:
                return hotel_context.hotel_checkin_date(self.checkout_date)
        else:
            return None

    def set_allotment_id(self, room_allotment_id):
        self.room_allotment_id = room_allotment_id
        self.mark_dirty()

    def mark_overridden(self):
        self.overridden = True
        self.mark_dirty()

    def is_overridden(self):
        return self.overridden

    def update_checkin_date(self, checkin_date):
        self.checkin_date = checkin_date
        if checkin_date:
            self.checkin_business_date = crs_context.get_hotel_context().current_date()
        else:
            self.checkin_business_date = None
        self.mark_dirty()

    def update_checkout_date(self, checkout_date):
        self.checkout_date = checkout_date
        if checkout_date:
            self.checkout_business_date = crs_context.get_hotel_context().current_date()
        else:
            self.checkout_business_date = None
        self.mark_dirty()

    def mark_deleted(self):
        self.deleted = True
        self.mark_dirty()

    def set_is_current(self, is_current):
        self.is_current = is_current
        self.mark_dirty()
