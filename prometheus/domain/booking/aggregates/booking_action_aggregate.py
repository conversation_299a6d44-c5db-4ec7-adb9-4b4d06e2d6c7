from ths_common.value_objects import BillSideEffect, BookingSideEffect


class BookingActionAggregate(object):
    def __init__(self, booking_action):
        self.booking_action = booking_action

    @property
    def booking_action_type(self):
        return self.booking_action.action_type.value

    def update_side_effect(
        self,
        inventory_side_effect=None,
        booking_side_effect=None,
        bill_side_effect=None,
    ):
        if inventory_side_effect:
            self.booking_action.side_effects.inventory.update(inventory_side_effect)
        if booking_side_effect:
            assert isinstance(
                booking_side_effect, BookingSideEffect
            ), "Wrong type of booking side effect. Please use value object BookingSideEffect"
            self.booking_action.side_effects.booking = booking_side_effect
        if bill_side_effect:
            assert isinstance(
                bill_side_effect, BillSideEffect
            ), "Wrong type of bill side effect. Please use value object BillSideEffect"
            self.booking_action.side_effects.bill = bill_side_effect
