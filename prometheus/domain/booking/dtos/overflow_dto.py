from ths_common.constants.booking_constants import OverflowActions


class OverflowDto:
    def __init__(
        self, booking_id: str, room_stay_id: str, action: OverflowActions, **kwargs
    ):
        self.booking_id = booking_id
        self.booking_aggregate = None
        self.room_stay_id = room_stay_id
        self.action = action

    @classmethod
    def from_dict(cls, expense_dict: dict):
        return cls(**expense_dict)
