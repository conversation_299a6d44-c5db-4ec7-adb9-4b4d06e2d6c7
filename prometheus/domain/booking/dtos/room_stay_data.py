import copy
import datetime
from collections import defaultdict
from datetime import date
from typing import Dict, List

from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import date_range

from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto import ChargeData
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.booking.dtos import ExpenseDto
from prometheus.domain.booking.dtos.guest_stay_data import GuestStayData
from prometheus.domain.booking.dtos.rate_plan_dtos import RatePlanPolicies
from prometheus.domain.booking.dtos.rate_plan_inclusion_dto import (
    RoomRatePlanInclusionDomainDto,
)
from prometheus.domain.booking.dtos.room_night_commission_dto import (
    RoomNightTACommissionDto,
)
from ths_common.constants.booking_constants import (
    AgeGroup,
    BookingStatus,
    ExpenseStatus,
    RoomStayType,
)
from ths_common.exceptions import ValidationException
from ths_common.value_objects import Occupancy, RoomRatePlan


class RoomStayData(object):
    NON_CHECKOUT_ADDON_APPLICABLE_TIME = datetime.time(23, 59)

    def __init__(
        self,
        room_type_id,
        checkin_date,
        checkout_date,
        charges: List[Charge],
        guest_stays: List[GuestStayData],
        type: RoomStayType = None,
        prices=None,
        rate_plan_reference_id=None,
        flexi_rate_plan_details: RatePlanPolicies = None,
        room_rate_plans: [RoomRatePlan] = None,
        stay_date_wise_inclusion_expense_and_charge_dtos: Dict[
            date, List[ExpenseDto]
        ] = None,
        commission_dtos: List[RoomNightTACommissionDto] = None,
        extra_information=None,
    ):
        self.room_type_id = room_type_id
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.type = type
        self.charges = charges
        self.guest_stays = guest_stays
        self.prices = prices
        self.rate_plan_reference_id = rate_plan_reference_id
        self.flexi_rate_plan_details = flexi_rate_plan_details
        self.room_rate_plans = room_rate_plans
        self.stay_date_wise_inclusion_expense_and_charge_dtos = (
            stay_date_wise_inclusion_expense_and_charge_dtos
        )
        self.commission_dtos = commission_dtos if commission_dtos else []
        self.extra_information = extra_information

    def get_date_wise_occupancies(self):
        date_wise_occupancies = dict()
        for d in date_range(
            dateutils.to_date(self.checkin_date), dateutils.to_date(self.checkout_date)
        ):
            active_guest_stays = self._get_active_guest_stays_on_date(d)
            adult = len(
                [gs for gs in active_guest_stays if gs.age_group == AgeGroup.ADULT]
            )
            child = len(
                [gs for gs in active_guest_stays if gs.age_group == AgeGroup.CHILD]
            )
            date_wise_occupancies[d] = Occupancy(adult=adult, child=child)
        return date_wise_occupancies

    def _get_active_guest_stays_on_date(self, date_):
        return [
            gs
            for gs in self.guest_stays
            if gs.status != BookingStatus.CANCELLED
            and date_
            in dateutils.date_range(
                dateutils.to_date(gs.checkin_date), dateutils.to_date(gs.checkout_date)
            )
        ]

    @staticmethod
    def _adult_guest_stays(guest_stay_dtos):
        return [
            gs
            for gs in guest_stay_dtos
            if gs.age_group == AgeGroup.ADULT and gs.status != BookingStatus.CANCELLED
        ]

    def add_charges(self, charges):
        self.charges = charges

    def override_checkin_time(self, override_checkin_time):
        self.checkin_date = dateutils.datetime_at_given_time(
            self.checkin_date, override_checkin_time
        )

    def override_checkout_time(self, override_checkout_time):
        self.checkout_date = dateutils.datetime_at_given_time(
            self.checkout_date, override_checkout_time
        )

    @staticmethod
    def create(
        room_stay: dict,
        override_checkin_time=None,
        override_checkout_time=None,
        expense_items=None,
        grouped_sku_categories=None,
    ):
        checkin_datetime = dateutils.localize_datetime(room_stay['checkin_date'])
        if override_checkin_time:
            checkin_datetime = dateutils.datetime_at_given_time(
                checkin_datetime, override_checkin_time
            )
        checkout_datetime = dateutils.localize_datetime(room_stay['checkout_date'])
        if override_checkout_time:
            checkout_datetime = dateutils.datetime_at_given_time(
                checkout_datetime, override_checkout_time
            )

        guest_stay_dtos = []
        for guest_stay in room_stay['guest_stays']:
            guest_stay_dto = GuestStayData.create(
                guest_stay,
                room_stay_checkin=checkin_datetime,
                room_stay_checkout=checkout_datetime,
                override_checkin_time=override_checkin_time,
                override_checkout_time=override_checkout_time,
            )

            guest_stay_dtos.append(guest_stay_dto)
        for price in room_stay['prices']:
            if not price.rate_plan_reference_id:
                price.rate_plan_reference_id = room_stay.get('rate_plan_reference_id')
                price.flexi_rate_plan_details = room_stay.get('flexi_rate_plan_details')

        stay_date_wise_inclusion_expense_and_charge_dtos = defaultdict(list)

        if room_stay.get('rate_plan_inclusions'):
            rate_plan_inclusions: List[RoomRatePlanInclusionDomainDto] = room_stay.get(
                'rate_plan_inclusions'
            )

            for rate_plan_inclusion in rate_plan_inclusions:
                # TODO: Check if we need to convert datetime to date, before date_range loop
                expense_item = next(
                    (
                        ex
                        for ex in expense_items
                        if ex.sku_id == rate_plan_inclusion.sku_id
                    ),
                    None,
                )
                if not expense_item:
                    raise ValidationException(ApplicationErrors.SKU_NOT_FOUND)

                for date in dateutils.date_range(
                    rate_plan_inclusion.start_date,
                    rate_plan_inclusion.end_date,
                    end_inclusive=True,
                ):
                    applicable_date = dateutils.datetime_at_given_time(
                        date, RoomStayData.NON_CHECKOUT_ADDON_APPLICABLE_TIME
                    )
                    expense_dto = ExpenseDto.from_dict(
                        dict(
                            room_stay_id='NA',
                            status=ExpenseStatus.CREATED,
                            comments='',
                            applicable_date=applicable_date,
                            sku_id=rate_plan_inclusion.sku_id,
                            sku_name=expense_item.name,
                            expense_item_id=expense_item.expense_item_id,
                            sku_category_id=expense_item.sku_category_id,
                            via_rate_plan=True,
                            linked=False,
                            pretax_amount=rate_plan_inclusion.pretax_amount,
                            posttax_amount=rate_plan_inclusion.posttax_amount,
                            via_addon=False,
                        )
                    )
                    charge_dto = ChargeData.create_for_inclusion_expense(
                        expense_dto,
                        None,
                        grouped_sku_categories.get(expense_dto.sku_category_id),
                        pretax_price=expense_dto.pretax_amount,
                        posttax_price=expense_dto.posttax_amount,
                    )
                    expense_dto.charge_dto = charge_dto
                    for _ in range(rate_plan_inclusion.quantity):
                        stay_date_wise_inclusion_expense_and_charge_dtos[date].append(
                            copy.deepcopy(expense_dto)
                        )

        room_stay_data = RoomStayData(
            room_stay['room_type_id'],
            checkin_datetime,
            checkout_datetime,
            charges=None,
            guest_stays=guest_stay_dtos,
            type=room_stay.get('type'),
            prices=room_stay['prices'],
            rate_plan_reference_id=room_stay.get('rate_plan_reference_id'),
            flexi_rate_plan_details=room_stay.get('rate_plan_details'),
            stay_date_wise_inclusion_expense_and_charge_dtos=stay_date_wise_inclusion_expense_and_charge_dtos,
            extra_information=room_stay.get('extra_information'),
        )
        return room_stay_data


class ExistingRoomStayData(RoomStayData):
    def __init__(
        self,
        room_type_id,
        checkin_date,
        checkout_date,
        charges: List[Charge],
        guest_stays: List[GuestStayData],
        type: RoomStayType = None,
        prices=None,
        rate_plan_reference_id=None,
        flexi_rate_plan_details: RatePlanPolicies = None,
        room_rate_plans: [RoomRatePlan] = None,
        stay_date_wise_inclusion_expense_and_charge_dtos: Dict[
            date, List[ExpenseDto]
        ] = None,
        room_stay_id=None,
    ):
        super(ExistingRoomStayData, self).__init__(
            room_type_id,
            checkin_date,
            checkout_date,
            charges,
            guest_stays,
            type,
            prices,
            rate_plan_reference_id,
            flexi_rate_plan_details,
            room_rate_plans,
            stay_date_wise_inclusion_expense_and_charge_dtos,
        )
        self.room_stay_id = room_stay_id

    @staticmethod
    def create(
        room_stay: dict,
        override_checkin_time=None,
        override_checkout_time=None,
        expense_items=None,
        grouped_sku_categories=None,
    ):
        room_stay_data = super(ExistingRoomStayData, ExistingRoomStayData).create(
            room_stay,
            override_checkin_time,
            override_checkout_time,
            expense_items,
            grouped_sku_categories,
        )
        room_stay_data.room_stay_id = room_stay.get('room_stay_id')

        return room_stay_data
