from ths_common.value_objects import NotAssigned


class EditExpenseData(object):
    def __init__(
        self,
        expense_id=None,
        comments=NotAssigned,
        assigned_to=NotAssigned,
        price_data=NotAssigned,
    ):
        if not expense_id:
            raise ValueError("Expense ID is required for editing charge")
        self.expense_id = expense_id
        self.comments = comments
        self.assigned_to = assigned_to

        self.price_data = price_data
        self.pretax_amount = price_data.pretax_amount if price_data else NotAssigned
        self.posttax_amount = price_data.posttax_amount if price_data else NotAssigned
        self.type = price_data.type if price_data else NotAssigned
        self.bill_to_type = price_data.bill_to_type if price_data else NotAssigned
        self.assigned_to = price_data.charge_to if price_data else NotAssigned
        self.billing_instructions = (
            price_data.billing_instructions if price_data else NotAssigned
        )

    @property
    def guests(self):
        return self.assigned_to
