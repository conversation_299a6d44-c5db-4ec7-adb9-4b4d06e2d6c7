from prometheus import crs_context
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.domain_events.booking_created import BookingCreatedEvent
from prometheus.domain.booking.dtos.guest_stay_event_data import GuestStayEventData
from prometheus.domain.booking.dtos.new_booking_dto import NewBookingDomainDto
from prometheus.domain.booking.dtos.room_stay_event_data import RoomStayEventData
from prometheus.domain.booking.entities.booking import Booking
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.exceptions import BookingCreationError
from prometheus.domain.booking.services.booking_id_generator import BookingIdGenerator
from prometheus.domain.domain_events.domain_event_registry import register_event
from ths_common.constants.booking_constants import BookingChannels, BookingTypes
from ths_common.value_objects import BookingSource, UserData


class BookingBuilder:
    def __init__(
        self,
        new_booking_data: NewBookingDomainDto,
        seller_model,
        user_data: UserData = None,
    ):
        data = new_booking_data

        source = BookingSource(
            channel_code=data.source.get('channel_code'),
            subchannel_code=data.source.get('subchannel_code'),
            application_code=data.source.get('application_code'),
        )
        booking_id = BookingIdGenerator.generate(source.channel_code)
        version = 1
        group_name = data.group_name
        company_details = data.company_details
        travel_agent_details = data.travel_agent_details
        account_details = data.account_details

        booking = Booking(
            booking_id=booking_id,
            hotel_id=data.hotel_id,
            type=BookingTypes.ROOM,
            checkin_date=None,
            checkout_date=None,
            status=data.status,
            source=source,
            owner_id=None,
            hold_till=data.hold_till,
            version=version,
            comments=data.comments,
            bill_id=None,
            booking_level_btc_invoice=(
                (not crs_context.is_treebo_tenant())
                and (source.channel_code == BookingChannels.B2B.value)
            ),
            reference_number=data.reference_number,
            extra_information=data.extra_information,
            seller_model=seller_model,
            group_name=group_name,
            company_details=company_details,
            travel_agent_details=travel_agent_details,
            account_details=account_details,
            default_billed_entity_category=data.default_billed_entity_category,
            default_payment_instruction=data.default_payment_instruction,
            default_billed_entity_category_for_extras=data.default_billed_entity_category_for_extras,
            default_payment_instruction_for_extras=data.default_payment_instruction_for_extras,
            booking_business_date=crs_context.current_business_date,
            segments=data.segments,
            guarantee_information=data.guarantee_information,
            discount_details=data.discount_details,
        )
        self.booking_aggregate = BookingAggregate(
            booking, room_stays=list(), customers=list(), user_data=user_data
        )
        booking_owner = self.booking_aggregate.add_customer(data.booking_owner)
        self.booking_aggregate.update_booking_owner(booking_owner.customer_id)

    def enrich_bill_id(self, bill_id: int):
        self.booking_aggregate.set_bill_id(bill_id)
        return self

    def enrich_guest_data(self, guests):
        for guest in guests:
            self.booking_aggregate.add_customer(guest)
        return self

    def enrich_room_stay_level_data(
        self,
        room_stays,
        rate_plans,
        override_checkin_time=None,
        override_checkout_time=None,
    ):
        if not room_stays:
            raise BookingCreationError(error=BookingErrors.EMPTY_ROOM_STAY_ERROR)

        if rate_plans:
            for room_stay in room_stays:
                room_stay.room_rate_plans = self.booking_aggregate.add_rate_plans(
                    room_stay, rate_plans
                )
        self.booking_aggregate.add_room_stays(
            room_stays,
            raise_event=False,
            override_checkin_time=override_checkin_time,
            override_checkout_time=override_checkout_time,
        )
        return self

    def get_partial_build(self):
        return self.booking_aggregate

    def complete_build(self):
        self.booking_aggregate.check_invariance()

        room_stay_event_data = []
        for room_stay in self.booking_aggregate.room_stays:
            guest_stay_event_data = []
            for guest_stay in room_stay.guest_stays:
                guest_id = (
                    guest_stay.guest_allocation.guest_id
                    if guest_stay.guest_allocation
                    else None
                )
                customer = (
                    self.booking_aggregate.get_customer(guest_id) if guest_id else None
                )
                guest_stay_event_data.append(
                    GuestStayEventData(
                        guest_stay_id=guest_stay.guest_stay_id,
                        guest_name=customer.first_name if customer else None,
                        guest_id=guest_id,
                        checkin_date=guest_stay.checkin_date,
                        checkout_date=guest_stay.checkout_date,
                    )
                )

            room_stay_event_data.append(
                RoomStayEventData(
                    room_stay.room_stay_id,
                    room_stay.room_type_id,
                    room_stay.checkin_date,
                    room_stay.checkout_date,
                    guest_stay_event_data,
                )
            )
        register_event(
            BookingCreatedEvent(
                booking_id=self.booking_aggregate.booking.booking_id,
                reference_number=self.booking_aggregate.booking.reference_number,
                room_stays=room_stay_event_data,
            )
        )
        return self.booking_aggregate
