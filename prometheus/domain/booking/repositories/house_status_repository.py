from treebo_commons.money import Money

from object_registry import register_instance
from prometheus.domain.booking.dtos.house_status_statistics_dto import (
    HouseStatusStatisticsDto,
    TotalRoomsAndGuestsDto,
)
from prometheus.domain.booking.models import HouseStatisticsModel
from prometheus.infrastructure.database.base_repository import BaseRepository


@register_instance()
class HouseStatusRepository(BaseRepository):
    def load_house_statistics(self, hotel_id, base_currency, business_date):
        house_statistics_model = (
            self.query(HouseStatisticsModel)
            .filter(
                HouseStatisticsModel.hotel_id == hotel_id,
                HouseStatisticsModel.business_date == business_date,
            )
            .first()
        )
        if not house_statistics_model:
            return None

        return HouseStatusStatisticsDto(
            business_date=house_statistics_model.business_date,
            available_rooms=house_statistics_model.available_rooms,
            occupied_rooms=house_statistics_model.occupied_rooms,
            dnr_rooms=house_statistics_model.dnr_rooms,
            early_departures=house_statistics_model.early_departures,
            day_use_rooms=house_statistics_model.day_use_rooms,
            day_of_arrival_cancellations=house_statistics_model.day_of_arrival_cancellations,
            total_rooms_and_guests_under_group_booking=TotalRoomsAndGuestsDto(
                rooms=house_statistics_model.total_rooms_under_group_booking,
                guests=house_statistics_model.total_guests_under_group_booking,
                vip_guests=house_statistics_model.total_vip_guests_under_group_booking,
            ),
            total_rooms_and_guests_under_individual_booking=TotalRoomsAndGuestsDto(
                rooms=house_statistics_model.total_rooms_under_individual_booking,
                guests=house_statistics_model.total_guests_under_individual_booking,
                vip_guests=house_statistics_model.total_vip_guests_under_individual_booking,
            ),
            total_rooms=house_statistics_model.total_rooms,
            total_eod_projected_dnr_rooms=house_statistics_model.total_eod_projected_dnr_rooms,
            eod_projected_rooms_available_for_sale=house_statistics_model.eod_projected_rooms_available_for_sale,
            occupancy=house_statistics_model.occupancy,
            total_room_revenue=Money(
                house_statistics_model.total_room_revenue, base_currency
            ),
            arr=Money(house_statistics_model.average_room_revenue, base_currency),
        )

    def save_house_statistics(
        self, hotel_id, house_statistics_dto: HouseStatusStatisticsDto
    ):
        house_statistics_model = HouseStatisticsModel(
            hotel_id=hotel_id,
            business_date=house_statistics_dto.business_date,
            available_rooms=house_statistics_dto.available_rooms,
            occupied_rooms=house_statistics_dto.occupied_rooms,
            dnr_rooms=house_statistics_dto.dnr_rooms,
            early_departures=house_statistics_dto.early_departures,
            day_use_rooms=house_statistics_dto.day_use_rooms,
            day_of_arrival_cancellations=house_statistics_dto.day_of_arrival_cancellations,
            total_rooms_under_group_booking=house_statistics_dto.total_rooms_and_guests_under_group_booking.rooms,
            total_guests_under_group_booking=house_statistics_dto.total_rooms_and_guests_under_group_booking.guests,
            total_vip_guests_under_group_booking=house_statistics_dto.total_rooms_and_guests_under_group_booking.vip_guests,
            total_rooms_under_individual_booking=house_statistics_dto.total_rooms_and_guests_under_individual_booking.rooms,
            total_guests_under_individual_booking=house_statistics_dto.total_rooms_and_guests_under_individual_booking.guests,
            total_vip_guests_under_individual_booking=house_statistics_dto.total_rooms_and_guests_under_individual_booking.vip_guests,
            total_rooms=house_statistics_dto.total_rooms,
            total_eod_projected_dnr_rooms=house_statistics_dto.total_eod_projected_dnr_rooms,
            eod_projected_rooms_available_for_sale=house_statistics_dto.eod_projected_rooms_available_for_sale,
            occupancy=house_statistics_dto.occupancy,
            total_room_revenue=house_statistics_dto.total_room_revenue.amount,
            average_room_revenue=house_statistics_dto.arr.amount,
        )
        existing_house_statistics = (
            self.query(HouseStatisticsModel)
            .filter(
                HouseStatisticsModel.hotel_id == hotel_id,
                HouseStatisticsModel.business_date
                == house_statistics_dto.business_date,
            )
            .first()
        )

        if existing_house_statistics:
            house_statistics_model.house_statistics_id = (
                existing_house_statistics.house_statistics_id
            )
            self._update(house_statistics_model)
        else:
            self._save(house_statistics_model)
