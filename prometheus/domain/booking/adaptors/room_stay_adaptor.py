from treebo_commons.utils import dateutils

from prometheus.domain.booking.entities.room_stay import RoomStay
from prometheus.domain.booking.models import RoomStayModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.booking_constants import Booking<PERSON>tatus, RoomStayType
from ths_common.value_objects import Discount, RoomRatePlan, RoomRent


class RoomStayAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity, **kwargs):
        charge_id_map = []
        for k, v in domain_entity.charge_id_map.items():
            charge_id_map.append(dict(charge_id=v, charge_date=k))

        return RoomStayModel(
            booking_id=kwargs['booking_id'],
            room_stay_id=domain_entity.room_stay_id,
            room_type_id=domain_entity.room_type_id,
            type=domain_entity.type.value if domain_entity.type else None,
            status=domain_entity.status.value,
            checkin_date=domain_entity.checkin_date,
            disallow_charge_addition=domain_entity.disallow_charge_addition,
            checkout_date=domain_entity.checkout_date,
            actual_checkin_date=domain_entity.actual_checkin_date,
            actual_checkout_date=domain_entity.actual_checkout_date,
            charge_id_map=charge_id_map,
            deleted=domain_entity.deleted,
            room_rents=[room_rent.to_json() for room_rent in domain_entity.room_rents]
            if domain_entity.room_rents
            else None,
            checkin_business_date=domain_entity.checkin_business_date,
            checkout_business_date=domain_entity.checkout_business_date,
            actual_checkout_business_date=domain_entity.actual_checkout_business_date,
            room_rate_plans=[
                room_rp.to_json() for room_rp in domain_entity.room_rate_plans
            ]
            if domain_entity.room_rate_plans
            else None,
            cancellation_date=domain_entity.cancellation_date,
            cancellation_date_time=domain_entity.cancellation_date_time,
            extra_information=domain_entity.extra_information,
            actual_checkin_calendar_date=domain_entity.actual_checkin_calendar_date,
            actual_checkout_calendar_date=domain_entity.actual_checkout_calendar_date,
            discounts=[discount.to_json() for discount in domain_entity.discounts]
            if domain_entity.discounts
            else [],
        )

    def to_domain_entity(self, db_entity, **kwargs):
        if isinstance(db_entity.charge_id_map, list):
            charge_id_map = dict()
            for x in db_entity.charge_id_map:
                charge_id_map[x['charge_date']] = x['charge_id']
        else:
            charge_id_map = db_entity.charge_id_map
        return RoomStay(
            db_entity.room_stay_id,
            db_entity.room_type_id,
            RoomStayType(db_entity.type) if db_entity.type else None,
            BookingStatus(db_entity.status),
            dateutils.localize_datetime(db_entity.checkin_date),
            dateutils.localize_datetime(db_entity.checkout_date),
            kwargs['guest_stays'],
            charge_id_map,
            disallow_charge_addition=db_entity.disallow_charge_addition
            if db_entity.disallow_charge_addition is not None
            else False,
            actual_checkin_date=dateutils.localize_datetime(
                db_entity.actual_checkin_date
            ),
            actual_checkout_date=dateutils.localize_datetime(
                db_entity.actual_checkout_date
            ),
            room_allocations=kwargs['room_allocations'],
            room_night_ta_commissions=kwargs['room_night_ta_commissions'],
            deleted=db_entity.deleted,
            created_at=dateutils.localize_datetime(db_entity.created_at),
            modified_at=dateutils.localize_datetime(db_entity.modified_at),
            room_rents=[
                RoomRent.from_json(room_rent) for room_rent in db_entity.room_rents
            ]
            if db_entity.room_rents
            else None,
            room_rate_plans=[
                RoomRatePlan.from_json(room_rp) for room_rp in db_entity.room_rate_plans
            ]
            if db_entity.room_rate_plans
            else None,
            checkin_business_date=db_entity.checkin_business_date,
            checkout_business_date=db_entity.checkout_business_date,
            actual_checkout_business_date=db_entity.actual_checkout_business_date,
            cancellation_date=db_entity.cancellation_date,
            cancellation_date_time=db_entity.cancellation_date_time,
            extra_information=db_entity.extra_information,
            discounts=[Discount.from_json(discount) for discount in db_entity.discounts]
            if db_entity.discounts
            else [],
            dirty=False,
            new=False,
            actual_checkin_calendar_date=db_entity.actual_checkin_calendar_date,
            actual_checkout_calendar_date=db_entity.actual_checkout_calendar_date,
        )
