from treebo_commons.utils import dateutils

from prometheus.domain.booking.entities.room_allocation import RoomAllocation
from prometheus.domain.booking.models import RoomAllocationModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class RoomAllocationAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity, **kwargs):
        return RoomAllocationModel(
            room_allocation_id=domain_entity.room_allocation_id,
            room_id=domain_entity.room_id,
            room_no=domain_entity.room_no,
            room_type_id=domain_entity.room_type_id,
            assigned_by=domain_entity.assigned_by,
            checkin_date=domain_entity.checkin_date,
            checkout_date=domain_entity.checkout_date,
            room_stay_id=kwargs.get('room_stay_id'),
            booking_id=kwargs.get('booking_id'),
            deleted=domain_entity.deleted,
            is_current=domain_entity.is_current,
            room_allotment_id=domain_entity.room_allotment_id,
            checkin_business_date=domain_entity.checkin_business_date,
            checkout_business_date=domain_entity.checkout_business_date,
            overridden=domain_entity.overridden,
        )

    def to_domain_entity(self, db_entity):
        return RoomAllocation(
            room_allocation_id=db_entity.room_allocation_id,
            room_stay_id=db_entity.room_stay_id,
            room_id=db_entity.room_id,
            room_type_id=db_entity.room_type_id,
            assigned_by=db_entity.assigned_by,
            checkin_date=dateutils.localize_datetime(db_entity.checkin_date),
            checkout_date=dateutils.localize_datetime(db_entity.checkout_date),
            deleted=db_entity.deleted,
            room_no=db_entity.room_no,
            is_current=db_entity.is_current,
            created_at=dateutils.localize_datetime(db_entity.created_at),
            modified_at=dateutils.localize_datetime(db_entity.modified_at),
            room_allotment_id=db_entity.room_allotment_id,
            checkin_business_date=db_entity.checkin_business_date,
            checkout_business_date=db_entity.checkout_business_date,
            overridden=db_entity.overridden,
            dirty=False,
            new=False,
        )
