from treebo_commons.money.money import Money

from prometheus import crs_context
from prometheus.domain.booking.entities.booking_funding import (
    BookingFundingConfig,
    BookingFundingRequest,
)
from prometheus.domain.booking.models import (
    BookingFundingConfigModel,
    BookingFundingRequestModel,
)
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.funding_constants import FundingStatus, FundingType
from ths_common.value_objects import Guardrail


class BookingFundingConfigAdaptor(BaseAdaptor):
    def to_db_entity(
        self, domain_entity: BookingFundingConfig
    ) -> BookingFundingConfigModel:
        """Converts domain entity to DB model."""
        return BookingFundingConfigModel(
            booking_id=domain_entity.booking_id,
            guardrails=(
                [guardrail.to_json() for guardrail in domain_entity.guardrails]
                if domain_entity.guardrails
                else None
            ),
            extra_information=domain_entity.extra_information,
            deleted=domain_entity.deleted,
        )

    def to_domain_entity(
        self, db_entity: BookingFundingConfigModel
    ) -> BookingFundingConfig:
        """Converts DB model to domain entity."""
        return BookingFundingConfig(
            booking_id=db_entity.booking_id,
            guardrails=(
                [Guardrail.from_json(guardrail) for guardrail in db_entity.guardrails]
                if db_entity.guardrails
                else None
            ),
            extra_information=db_entity.extra_information,
            deleted=db_entity.deleted,
        )


class BookingFundingRequestAdaptor(BaseAdaptor):
    def to_db_entity(
        self, domain_entity: BookingFundingRequest
    ) -> BookingFundingRequestModel:
        """Converts domain entity to DB model."""
        amount = domain_entity.amount.amount if domain_entity.amount else 0
        model_kwargs = {
            "booking_id": domain_entity.booking_id,
            "amount": amount,
            "status": domain_entity.status.value,
            "funding_type": domain_entity.funding_type.value,
            "reason": domain_entity.reason,
            "deleted": domain_entity.deleted,
        }

        if domain_entity.funding_id:
            model_kwargs["funding_id"] = domain_entity.funding_id

        return BookingFundingRequestModel(**model_kwargs)

    def to_domain_entity(
        self, db_entity: BookingFundingRequestModel
    ) -> BookingFundingRequest:
        """Converts DB model to domain entity."""
        base_currency = crs_context.hotel_context.base_currency
        amount = (
            Money(db_entity.amount, base_currency)
            if db_entity.amount
            else Money(0, base_currency)
        )
        return BookingFundingRequest(
            booking_id=db_entity.booking_id,
            funding_id=db_entity.funding_id,
            amount=amount,
            status=FundingStatus(db_entity.status),
            funding_type=FundingType(db_entity.funding_type),
            reason=db_entity.reason,
            deleted=db_entity.deleted,
        )
