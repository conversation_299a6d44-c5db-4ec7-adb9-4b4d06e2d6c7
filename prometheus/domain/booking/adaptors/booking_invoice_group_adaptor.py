import json

import jsonpickle

from prometheus.domain.booking.dtos.guest_checkout_data import (
    ChargeGroupingForInvoiceRequest,
)
from prometheus.domain.booking.entities.booking_invoice_group import BookingInvoiceGroup
from prometheus.domain.booking.models import BookingInvoiceGroupModel
from ths_common.constants.booking_constants import InvoiceGroupStatus


class BookingInvoiceGroupDBAdapter(object):
    @staticmethod
    def to_db_model(invoice_group):
        return BookingInvoiceGroupModel(
            booking_id=invoice_group.booking_id,
            booking_invoice_group_id=invoice_group.booking_invoice_group_id,
            is_last_checkout=invoice_group.is_last_checkout,
            room_wise_invoice_request=[
                request.to_json() for request in invoice_group.room_wise_invoice_request
            ],
            is_advanced=invoice_group.is_advanced,
            request_datetime=invoice_group.request_datetime,
            invoice_ids=invoice_group.invoice_ids,
            generated_by=invoice_group.generated_by,
            generation_channel=invoice_group.generation_channel,
            status=invoice_group.status.value,
            version=invoice_group.version,
            newly_added_charge_ids=invoice_group.newly_added_charge_ids,
            charges_and_allowances_to_be_posted=invoice_group.charges_and_allowances_to_be_posted,
        )

    @staticmethod
    def to_entity(invoice_group_model):
        room_wise_invoice_request = invoice_group_model.room_wise_invoice_request
        if room_wise_invoice_request:
            try:
                room_wise_invoice_request = (
                    json.loads(room_wise_invoice_request)
                    if isinstance(room_wise_invoice_request, str)
                    else room_wise_invoice_request
                )
                room_wise_invoice_request = [
                    ChargeGroupingForInvoiceRequest.from_json(request)
                    for request in room_wise_invoice_request
                ]
            except:
                room_wise_invoice_request = jsonpickle.decode(
                    room_wise_invoice_request, keys=True, classes=[]
                )
        charges_and_allowances_to_be_posted = (
            invoice_group_model.charges_and_allowances_to_be_posted
        )
        if charges_and_allowances_to_be_posted:
            try:
                charges_and_allowances_to_be_posted = (
                    json.loads(charges_and_allowances_to_be_posted)
                    if isinstance(charges_and_allowances_to_be_posted, str)
                    else charges_and_allowances_to_be_posted
                )
            except:
                charges_and_allowances_to_be_posted = jsonpickle.decode(
                    charges_and_allowances_to_be_posted, keys=True, classes=[]
                )

        return BookingInvoiceGroup(
            booking_id=invoice_group_model.booking_id,
            booking_invoice_group_id=invoice_group_model.booking_invoice_group_id,
            room_wise_invoice_request=room_wise_invoice_request,
            is_last_checkout=invoice_group_model.is_last_checkout,
            is_advanced=invoice_group_model.is_advanced,
            request_datetime=invoice_group_model.request_datetime,
            invoice_ids=invoice_group_model.invoice_ids,
            status=InvoiceGroupStatus(invoice_group_model.status),
            generated_by=invoice_group_model.generated_by,
            generation_channel=invoice_group_model.generation_channel,
            version=invoice_group_model.version,
            newly_added_charge_ids=invoice_group_model.newly_added_charge_ids,
            charges_and_allowances_to_be_posted=charges_and_allowances_to_be_posted,
            dirty=False,
            new=False,
        )
