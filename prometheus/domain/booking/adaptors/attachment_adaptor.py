from prometheus.domain.booking.entities.attachment import Attachment
from prometheus.domain.booking.models import AttachmentModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.booking_constants import AttachmentGroup, AttachmentStatus


class AttachmentAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: Attachment, **kwargs):
        # noinspection PyArgumentList
        return AttachmentModel(
            attachment_id=domain_entity.attachment_id,
            booking_id=domain_entity.booking_id,
            url=domain_entity.original_url,
            display_name=domain_entity.display_name,
            file_type=domain_entity.file_type,
            source=domain_entity.source,
            attachment_group=domain_entity.attachment_group.value,
            status=domain_entity.status.value,
            uploaded_by=domain_entity.uploaded_by,
            rejection_reason=domain_entity.rejection_reason,
            deleted=domain_entity.deleted,
        )

    def to_domain_entity(self, db_entity: AttachmentModel, **kwargs):
        status = (
            AttachmentStatus.PENDING_VERIFICATION
            if not db_entity.status
            else AttachmentStatus(db_entity.status)
        )
        return Attachment(
            attachment_id=db_entity.attachment_id,
            booking_id=db_entity.booking_id,
            original_url=db_entity.url,
            display_name=db_entity.display_name,
            file_type=db_entity.file_type,
            source=db_entity.source,
            attachment_group=AttachmentGroup(db_entity.attachment_group),
            uploaded_by=db_entity.uploaded_by,
            status=status,
            rejection_reason=db_entity.rejection_reason,
            deleted=db_entity.deleted,
        )
