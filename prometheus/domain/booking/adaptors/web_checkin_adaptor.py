from prometheus.domain.booking.entities.web_checkin import WebCheckin
from prometheus.domain.booking.models import WebCheckinModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.web_checkin_constants import WebCheckinStatus


class Web<PERSON>heckinAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: WebCheckin, **kwargs):
        # noinspection PyArgumentList
        return WebCheckinModel(
            web_checkin_id=domain_entity.web_checkin_id,
            booking_id=domain_entity.booking_id,
            guest_ids=domain_entity.guest_ids,
            status=domain_entity.status.value,
        )

    def to_domain_entity(self, db_entity: WebCheckinModel, **kwargs):
        return WebCheckin(
            web_checkin_id=db_entity.web_checkin_id,
            booking_id=db_entity.booking_id,
            guest_ids=db_entity.guest_ids,
            status=WebCheckinStatus(db_entity.status),
        )
