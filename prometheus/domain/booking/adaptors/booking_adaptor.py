from treebo_commons.utils import dateutils

from prometheus.domain.booking.entities.booking import Booking
from prometheus.domain.booking.models import BookingModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    PaymentInstruction,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.catalog_constants import SellerType
from ths_common.value_objects import (
    AccountDetails,
    BookingSource,
    CompanyDetails,
    DiscountDetail,
    GuaranteeInformation,
    Segment,
    TADetails,
)


class BookingAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity):
        return BookingModel(
            booking_id=domain_entity.booking_id,
            booking_owner=domain_entity.owner_id,
            checkin_date=domain_entity.checkin_date,
            checkout_date=domain_entity.checkout_date,
            actual_checkin_date=domain_entity.actual_checkin_date,
            actual_checkout_date=domain_entity.actual_checkout_date,
            status=domain_entity.status.value,
            channel_code=domain_entity.source.channel_code,
            subchannel_code=domain_entity.source.subchannel_code,
            application_code=domain_entity.source.application_code,
            hold_till=domain_entity.hold_till,
            hotel_id=domain_entity.hotel_id,
            comments=domain_entity.comments,
            bill_id=domain_entity.bill_id,
            reference_number=domain_entity.reference_number,
            extra_information=domain_entity.extra_information,
            deleted=domain_entity.deleted,
            version=domain_entity.version,
            cancellation_reason=domain_entity.cancellation_reason,
            cancellation_datetime=domain_entity.cancellation_datetime,
            created_at=domain_entity.created_at,
            seller_model=domain_entity.seller_model.value
            if domain_entity.seller_model is not None
            else None,
            booking_level_btc_invoice=domain_entity.booking_level_btc_invoice,
            group_name=domain_entity.group_name,
            company_details=domain_entity.company_details.to_json()
            if domain_entity.company_details
            else None,
            travel_agent_details=domain_entity.travel_agent_details.to_json()
            if domain_entity.travel_agent_details
            else None,
            default_billed_entity_category=domain_entity.default_billed_entity_category.value
            if domain_entity.default_billed_entity_category
            else None,
            default_payment_instruction=domain_entity.default_payment_instruction.value
            if domain_entity.default_payment_instruction
            else None,
            default_billed_entity_category_for_extras=domain_entity.default_billed_entity_category_for_extras.value
            if domain_entity.default_billed_entity_category_for_extras
            else None,
            default_payment_instruction_for_extras=domain_entity.default_payment_instruction_for_extras.value
            if domain_entity.default_payment_instruction_for_extras
            else None,
            checkin_business_date=domain_entity.checkin_business_date,
            checkout_business_date=domain_entity.checkout_business_date,
            actual_checkout_business_date=domain_entity.actual_checkout_business_date,
            actual_checkout_calendar_date=domain_entity.actual_checkout_calendar_date,
            booking_business_date=domain_entity.booking_business_date,
            segments=[segment.to_json() for segment in domain_entity.segments]
            if domain_entity.segments
            else None,
            account_details=domain_entity.account_details.to_json()
            if domain_entity.account_details
            else None,
            guarantee_information=domain_entity.guarantee_information.to_json()
            if domain_entity.guarantee_information
            else None,
            discount_details=[
                discount_detail.to_json()
                for discount_detail in domain_entity.discount_details
            ]
            if domain_entity.discount_details
            else [],
        )

    def to_domain_entity(self, db_entity):
        source = BookingSource(
            channel_code=db_entity.channel_code,
            subchannel_code=db_entity.subchannel_code,
            application_code=db_entity.application_code,
        )
        return Booking(
            db_entity.booking_id,
            db_entity.hotel_id,
            None,
            dateutils.localize_datetime(db_entity.checkin_date),
            dateutils.localize_datetime(db_entity.checkout_date),
            BookingStatus(db_entity.status),
            source,
            db_entity.booking_owner,
            dateutils.localize_datetime(db_entity.hold_till),
            db_entity.version,
            db_entity.comments,
            db_entity.bill_id,
            db_entity.reference_number,
            db_entity.extra_information,
            seller_model=SellerType(db_entity.seller_model)
            if db_entity.seller_model is not None
            else None,
            deleted=db_entity.deleted,
            cancellation_reason=db_entity.cancellation_reason,
            cancellation_datetime=dateutils.localize_datetime(
                db_entity.cancellation_datetime
            ),
            booking_level_btc_invoice=db_entity.booking_level_btc_invoice,
            created_at=dateutils.localize_datetime(db_entity.created_at),
            modified_at=dateutils.localize_datetime(db_entity.modified_at),
            actual_checkin_date=dateutils.localize_datetime(
                db_entity.actual_checkin_date
            ),
            actual_checkout_date=dateutils.localize_datetime(
                db_entity.actual_checkout_date
            ),
            group_name=db_entity.group_name,
            company_details=CompanyDetails.from_json(db_entity.company_details)
            if db_entity.company_details
            else None,
            travel_agent_details=TADetails.from_json(db_entity.travel_agent_details)
            if db_entity.travel_agent_details
            else None,
            account_details=AccountDetails.from_json(db_entity.account_details)
            if db_entity.account_details
            else None,
            default_billed_entity_category=BilledEntityCategory(
                db_entity.default_billed_entity_category
            )
            if db_entity.default_billed_entity_category
            else None,
            default_payment_instruction=PaymentInstruction(
                db_entity.default_payment_instruction
            )
            if db_entity.default_payment_instruction
            else None,
            default_billed_entity_category_for_extras=BilledEntityCategory(
                db_entity.default_billed_entity_category_for_extras
            )
            if db_entity.default_billed_entity_category_for_extras
            else None,
            default_payment_instruction_for_extras=PaymentInstruction(
                db_entity.default_payment_instruction_for_extras
            )
            if db_entity.default_payment_instruction_for_extras
            else None,
            checkin_business_date=db_entity.checkin_business_date,
            checkout_business_date=db_entity.checkout_business_date,
            actual_checkout_business_date=db_entity.actual_checkout_business_date,
            actual_checkout_calendar_date=db_entity.actual_checkout_calendar_date,
            booking_business_date=db_entity.booking_business_date,
            segments=[Segment.from_json(segment) for segment in db_entity.segments]
            if db_entity.segments
            else None,
            guarantee_information=GuaranteeInformation.from_json(
                db_entity.guarantee_information
            )
            if db_entity.guarantee_information
            else None,
            discount_details=[
                DiscountDetail.from_json(discount_detail)
                for discount_detail in db_entity.discount_details
            ]
            if db_entity.discount_details
            else [],
            dirty=False,
            new=False,
        )
