from prometheus.domain.booking.aggregates.audit_trail_aggregate import (
    AuditTrailAggregate,
)
from prometheus.domain.booking.entities.audit_trail import AuditTrail
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.utils import id_generator_utils


class AuditTrailFactory(object):
    @staticmethod
    def create_audit_trail(
        user,
        user_type,
        application,
        timestamp,
        booking_id,
        audit_type: AuditType,
        audit_payload,
        request_id=None,
        action_id=None,
        current_business_date=None,
        auth_id=None,
        application_trace=None,
    ):
        audit_id = id_generator_utils.random_id_generator('AUD')
        audit_trail = AuditTrail(
            audit_id,
            user,
            user_type,
            application,
            timestamp,
            booking_id,
            audit_type,
            audit_payload,
            request_id=request_id,
            action_id=action_id,
            current_business_date=current_business_date,
            auth_id=auth_id,
            application_trace=application_trace,
        )
        return AuditTrailAggregate(audit_trail=audit_trail)
