# coding=utf-8
"""
room stay overflow factory
"""
import logging

from prometheus.domain.booking.aggregates.room_stay_overflow_aggregate import (
    RoomStayOverflowAggregate,
)
from prometheus.domain.booking.dtos.room_stay_overflow_data import RoomStayOverflowData
from prometheus.domain.booking.entities.room_stay_overflow import RoomStayOverflow

logger = logging.getLogger(__name__)


class RoomStayOverflowFactory:
    """Room Stay Overflow factory which takes raw dtos and creates RoomStayOverflow object"""

    @staticmethod
    def create(room_stay_overflow_data: RoomStayOverflowData):
        room_stay_overflow_entity = RoomStayOverflow(
            hotel_id=room_stay_overflow_data.hotel_id,
            booking_id=room_stay_overflow_data.booking_id,
            room_stay_id=room_stay_overflow_data.room_stay_id,
            room_type_id=room_stay_overflow_data.room_type_id,
            start_date=room_stay_overflow_data.start_date,
            end_date=room_stay_overflow_data.end_date,
        )

        return RoomStayOverflowAggregate(room_stay_overflow=room_stay_overflow_entity)
