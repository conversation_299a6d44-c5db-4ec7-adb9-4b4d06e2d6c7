from prometheus.core.base_domain_event import MergeableDomain<PERSON>vent
from prometheus.domain.booking.domain_events.schema.booking import NoShowEventSchema
from prometheus.domain.booking.dtos.noshow_event_data import NoShowEventData
from ths_common.constants.domain_event_constants import DomainEvent


class GuestStayNoShowReversedEvent(MergeableDomainEvent):
    def __init__(self, noshow_event_data: NoShowEventData):
        self.noshow_event_data = [noshow_event_data]

    def serialize(self):
        serialized = NoShowEventSchema(many=True).dump(self.noshow_event_data).data
        return serialized

    def update_mapping(self, **kwargs):
        customer_map = kwargs.get('customer_map')
        room_type_map = kwargs.get('room_type_map')
        for event_data in self.noshow_event_data:
            if event_data.room_type_id is not None:
                event_data.room_type_name = room_type_map.get(
                    event_data.room_type_id
                ).room_type.type

            if event_data.guest_id is not None:
                event_data.guest_name = customer_map.get(
                    event_data.guest_id
                ).name.full_name

    def event_type(self):
        return DomainEvent.GUEST_STAY_NOSHOW_REVERSED

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, GuestStayNoShowReversedEvent)

    def merge(self, mergeable_domain_event):
        self.noshow_event_data.extend(mergeable_domain_event.noshow_event_data)
