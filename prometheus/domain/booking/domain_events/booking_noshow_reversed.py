from prometheus.core.base_domain_event import BaseDomainEvent
from ths_common.constants.domain_event_constants import DomainEvent


class BookingNoShowReversedEvent(BaseDomainEvent):
    def __init__(self, booking_id):
        self.booking_id = booking_id

    def serialize(self):
        return dict()

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.BOOKING_NO_SHOW_REVERSED
