from marshmallow import Schema, fields, post_load
from treebo_commons.money.money_field import MoneyField


class TACommissionAddedEventSchema(Schema):
    booking_id = fields.String()
    reference_number = fields.String()
    commission_amount = MoneyField()

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.domain_events.ta_commission.events import (
            TACommissionAddedEvent,
        )

        return TACommissionAddedEvent(**data)


class TACommissionUpdatedEventSchema(Schema):
    booking_id = fields.String()
    reference_number = fields.String()
    old_commission_amount = MoneyField()
    new_commission_amount = MoneyField()

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.domain_events.ta_commission.events import (
            TACommissionUpdatedEvent,
        )

        return TACommissionUpdatedEvent(**data)
