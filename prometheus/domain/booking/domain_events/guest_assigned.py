from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    GuestAssignedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent


class GuestAssignedEvent(BaseDomainEvent):
    def __init__(self, booking_id, room_stay_id, guest_stay_id, guest_id):
        self.booking_id = booking_id
        self.room_stay_id = room_stay_id
        self.guest_stay_id = guest_stay_id
        self.guest_id = guest_id
        self.guest_name = None

    def serialize(self):
        serialized = GuestAssignedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        customer_map = kwargs.get('customer_map')
        self.guest_name = customer_map.get(self.guest_id).name.full_name

    def event_type(self):
        return DomainEvent.GUEST_ASSIGNED_TO_GUEST_STAY
