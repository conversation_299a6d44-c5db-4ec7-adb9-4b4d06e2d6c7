from typing import List

from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    RoomStayCancelReversedEventSchema,
)
from prometheus.domain.booking.dtos.guest_stay_event_data import GuestStayEventData
from ths_common.constants.domain_event_constants import DomainEvent


class RoomStayCancelReversedEvent(MergeableDomainEvent):
    def __init__(self, guest_stay_event_data: List[GuestStayEventData]):
        self.guest_stay_event_data = guest_stay_event_data

    def serialize(self):
        serialized = (
            RoomStayCancelReversedEventSchema(many=True)
            .dump(self.guest_stay_event_data)
            .data
        )
        return serialized

    def update_mapping(self, **kwargs):
        customer_map = kwargs.get('customer_map')
        for gs_event_data in self.guest_stay_event_data:
            if gs_event_data.guest_id:
                gs_event_data.name = customer_map.get(
                    gs_event_data.guest_id
                ).name.full_name

    def event_type(self):
        return DomainEvent.ROOM_STAY_CANCEL_REVERSED

    def merge(self, mergeable_domain_event):
        self.guest_stay_event_data.extend(mergeable_domain_event.guest_stay_event_data)

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, RoomStayCancelReversedEvent)
