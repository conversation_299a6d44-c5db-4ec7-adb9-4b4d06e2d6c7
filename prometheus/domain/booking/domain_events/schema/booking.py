from marshmallow import Schema, fields
from marshmallow.decorators import post_load
from treebo_commons.money.money_field import <PERSON>Field

from ths_common.value_objects import DateRange

__all__ = [
    'GuestStayEventSchema',
    'RoomStayEventSchema',
    'BookingCreatedEventSchema',
    'BookingReCreatedEventSchema',
    'GuestCheckedOutEventSchema',
    'RoomStayAddedEventSchema',
    'GuestStayAddedEventSchema',
    'GuestStayCancelledEventSchema',
    'OccupancyChangeEventSchema',
    'RoomStayOccupancyChangedEventSchema',
    'RoomStayCancelledEventSchema',
    'NoShowEventSchema',
    'RoomTypeChangedEventSchema',
    'StayDatesSchema',
    'RoomStayDatesChangedEventSchema',
    'AttributeUpdateSchema',
    'CustomerUpdatedEventSchema',
    'BookingDetailsUpdatedEventSchema',
    'GuestStayUpdatedEventSchema',
]


class RoomStayCancelReversedEventSchema(Schema):
    guest_name = fields.String()
    guest_stay_id = fields.Integer()
    room_stay_id = fields.Integer()
    room_type_id = fields.String()
    room_type_name = fields.String()
    room_number = fields.String()


class RoomFreedEventSchema(Schema):
    room_stay_id = fields.Integer()
    room_type_id = fields.String()
    room_number = fields.String()
    room_type_name = fields.String()


class RoomChangedEventSchema(Schema):
    room_stay_id = fields.Integer()
    new_room_type_id = fields.String()
    old_room_type_id = fields.String()
    new_room_number = fields.String()
    old_room_number = fields.String()
    new_room_type_name = fields.String()
    old_room_type_name = fields.String()


class RatePlanChangedEventSchema(Schema):
    room_stay_id = fields.Integer()
    new_rate_plan_id = fields.String()
    old_rate_plan_id = fields.String()
    new_rate_plan_name = fields.String()
    old_rate_plan_name = fields.String()


class GuestStayEventSchema(Schema):
    guest_stay_id = fields.Integer()
    guest_name = fields.String()
    checkin_date = fields.LocalDateTime()
    checkout_date = fields.LocalDateTime()

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.dtos.guest_stay_event_data import (
            GuestStayEventData,
        )

        return GuestStayEventData(**data)


class RoomStayEventSchema(Schema):
    room_stay_id = fields.Integer()
    room_type_id = fields.String()
    room_type_name = fields.String()
    checkin_date = fields.LocalDateTime()
    checkout_date = fields.LocalDateTime()
    guest_stays = fields.Nested(GuestStayEventSchema, many=True)

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.dtos.room_stay_event_data import (
            RoomStayEventData,
        )

        return RoomStayEventData(**data)


class BookingCreatedEventSchema(Schema):
    booking_id = fields.String()
    reference_number = fields.String()
    room_stays = fields.Nested(RoomStayEventSchema, many=True)

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.domain_events.booking_created import (
            BookingCreatedEvent,
        )

        return BookingCreatedEvent(**data)


class GuestAssignedEventSchema(Schema):
    booking_id = fields.String()
    room_stay_id = fields.Integer()
    guest_stay_id = fields.Integer()
    guest_id = fields.String()
    guest_name = fields.String()


class BookingReCreatedEventSchema(Schema):
    booking_id = fields.String()
    reference_number = fields.String()
    room_stays = fields.Nested(RoomStayEventSchema, many=True)

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.domain_events.booking_recreated import (
            BookingReCreatedEvent,
        )

        return BookingReCreatedEvent(**data)


class GuestCheckedOutEventSchema(Schema):
    guest_stay_id = fields.String()
    guest_name = fields.String()
    room_stay_id = fields.Integer()
    room_type_id = fields.String()
    room_number = fields.String()
    room_type_name = fields.String()


class RoomStayAddedEventSchema(Schema):
    room_stays = fields.Nested(RoomStayEventSchema, many=True)

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.domain_events.room_stay_added import (
            RoomStayAddedEvent,
        )

        return RoomStayAddedEvent(**data)


class GuestStayAddedEventSchema(GuestStayEventSchema):
    room_stay_id = fields.Integer()
    room_type_id = fields.String()
    room_type_name = fields.String()
    room_number = fields.String()


GuestStayCancelledEventSchema = GuestStayAddedEventSchema
RoomStayNoShowReversedEventSchema = GuestStayAddedEventSchema


class OccupancyChangeEventSchema(Schema):
    date = fields.Date()
    old_occupancy = fields.Integer()
    new_occupancy = fields.Integer()


class RoomStayOccupancyChangedEventSchema(Schema):
    room_stay_id = fields.Integer()
    occupancy_changes = fields.Nested(OccupancyChangeEventSchema, many=True)


class RoomStayCancelledEventSchema(Schema):
    room_stays = fields.Nested(RoomStayEventSchema, many=True)

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.dtos.room_stay_event_data import (
            RoomStayEventData,
        )

        return RoomStayEventData(**data)


class NoShowEventSchema(Schema):
    room_stay_id = fields.Integer()
    room_number = fields.String()
    room_type_name = fields.String()
    guest_stay_id = fields.Integer()
    guest_id = fields.String()
    guest_name = fields.String()

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.dtos.noshow_event_data import NoShowEventData

        return NoShowEventData(**data)


class RoomTypeChangedEventSchema(Schema):
    room_stay_id = fields.Integer()
    old_room_type_id = fields.String()
    old_room_type_name = fields.String()
    old_room_number = fields.String()
    new_room_type_id = fields.String()
    new_room_type_name = fields.String()
    new_room_number = fields.String()

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.domain_events.room_stay_room_type_changed import (
            RoomStayRoomTypeChangedEvent,
        )

        return RoomStayRoomTypeChangedEvent(**data)


class StayDatesSchema(Schema):
    stay_start = fields.LocalDateTime(attribute="start_date")
    stay_end = fields.LocalDateTime(attribute="end_date")

    @post_load
    def make_object(self, data):
        return DateRange(**data)


class RoomStayDatesChangedEventSchema(Schema):
    room_stay_id = fields.Integer()
    room_number = fields.String()
    room_type_id = fields.String()
    room_type_name = fields.String()
    old_stay_dates = fields.Nested(StayDatesSchema)
    new_stay_dates = fields.Nested(StayDatesSchema)

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.domain_events.room_stay_dates_changed import (
            RoomStayDatesChangedEvent,
        )

        return RoomStayDatesChangedEvent(**data)


class AttributeUpdateSchema(Schema):
    attribute = fields.String()
    old_value = fields.String()
    new_value = fields.String()


class CustomerUpdatedEventSchema(Schema):
    room_stay_id = fields.Integer()
    room_number = fields.String()
    guest_stay_id = fields.Integer()
    room_type_id = fields.String()
    room_type_name = fields.String()
    details = fields.Nested(AttributeUpdateSchema, many=True)

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.domain_events.customer_updated import (
            CustomerUpdatedEvent,
        )

        return CustomerUpdatedEvent(**data)


class BookingDetailsUpdatedEventSchema(Schema):
    details = fields.Nested(AttributeUpdateSchema, many=True)


class GuestStayUpdatedEventSchema(Schema):
    room_stay_id = fields.Integer()
    guest_stay_id = fields.String()
    details = fields.Nested(AttributeUpdateSchema, many=True)

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.domain_events.guest_stay_updated import (
            GuestStayUpdatedEvent,
        )

        return GuestStayUpdatedEvent(**data)


class AddonCreatedEventSchema(Schema):
    booking_id = fields.String()
    addon_id = fields.String()
    name = fields.String()
    expense_category = fields.String()
    applicable_on = fields.String()
    charge_type = fields.String()
    quantity = fields.Integer()
    room_stay_id = fields.Integer()
    room_type_id = fields.String()
    room_type_name = fields.String()
    added_by = fields.String()
    start_date = fields.Date()
    end_date = fields.Date()
    linked = fields.Boolean()
    pretax_price = MoneyField(
        allow_none=True,
        description='Price per addon exclusive of tax',
        missing=None,
        as_string=True,
    )
    posttax_price = MoneyField(
        allow_none=True,
        description='Price per addon inclusive of tax',
        missing=None,
        as_string=True,
    )


class AddonRemovedEventSchema(Schema):
    booking_id = fields.String()
    addon_id = fields.String()
    name = fields.String()
    expense_category = fields.String()
    applicable_on = fields.String()
    charge_type = fields.String()
    quantity = fields.Integer()
    room_stay_id = fields.Integer()
    room_type_id = fields.String()
    room_type_name = fields.String()
    added_by = fields.String()
    start_date = fields.Date()
    end_date = fields.Date()
    linked = fields.Boolean()
    pretax_price = MoneyField(
        allow_none=True,
        description='Price per addon exclusive of tax',
        missing=None,
        as_string=True,
    )
    posttax_price = MoneyField(
        allow_none=True,
        description='Price per addon inclusive of tax',
        missing=None,
        as_string=True,
    )


class AddonModifiedEventSchema(Schema):
    booking_id = fields.String()
    addon_id = fields.String()
    name = fields.String()
    expense_category = fields.String()
    room_stay_id = fields.Integer()
    room_type_id = fields.String()
    room_type_name = fields.String()
    details = fields.Nested(AttributeUpdateSchema, many=True)


class AttachmentAddedEventDetailsSchema(Schema):
    attachment_id = fields.String()
    display_name = fields.String()
    file_type = fields.String()
    attachment_group = fields.String()


class AttachmentAddedEventSchema(Schema):
    booking_id = fields.String()
    source = fields.String()
    uploaded_by = fields.String()
    details = fields.Nested(AttachmentAddedEventDetailsSchema, many=True)


class AttachmentDeletedEventSchema(Schema):
    booking_id = fields.String()
    attachment_id = fields.String()
    display_name = fields.String()
    deleted_by = fields.String()


class AttachmentModifiedEventSchema(Schema):
    booking_id = fields.String()
    attachment_id = fields.String()
    room_number = fields.String()
    guest_number = fields.String()
    guest_name = fields.String()
    original_status = fields.String()
    new_status = fields.String()
    rejection_reason = fields.String()


class WebCheckInPerformedEventSchema(Schema):
    booking_id = fields.String()
    web_checkin_id = fields.String()
    status = fields.String()
    guest_ids = fields.List(fields.String())


class WebCheckInModifiedEventSchema(Schema):
    booking_id = fields.String()
    web_checkin_id = fields.String()
    status = fields.String()
    guest_ids = fields.List(fields.String())


class DisallowChargeAdditionEventSchema(Schema):
    room_id = fields.Integer()
    room_number = fields.String()
    room_type_id = fields.String()
    room_stay_id = fields.String()
    disallow_charge_addition = fields.Boolean()
    room_type = fields.String()

    @post_load
    def make_object(self, data):
        from prometheus.domain.booking.domain_events.disallow_charge_addition import (
            DisallowChargeAdditionChangedEvent,
        )

        return DisallowChargeAdditionChangedEvent(**data)


class FundingRequestCreatedEventSchema(Schema):
    booking_id = fields.String()
    amount = MoneyField()
    status = fields.String()
    funding_type = fields.String()
    reason = fields.String()
    deleted = fields.Boolean()


class FundingRequestModifiedEventSchema(Schema):
    old_value = fields.Dict()
    new_value = fields.Dict()
