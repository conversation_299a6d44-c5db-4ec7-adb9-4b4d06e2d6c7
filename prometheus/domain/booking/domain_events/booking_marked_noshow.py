from prometheus.core.base_domain_event import BaseDomainEvent
from ths_common.constants.domain_event_constants import DomainEvent


class BookingMarkedNoShowEvent(BaseDomainEvent):
    def __init__(self, booking_id=None):
        self.booking_id = booking_id

    def serialize(self):
        return dict()

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.BOOKING_MARKED_NOSHOW
