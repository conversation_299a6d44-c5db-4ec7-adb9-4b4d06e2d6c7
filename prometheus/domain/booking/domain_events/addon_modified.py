from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    AddonModifiedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException


class AddonModifiedEvent(MergeableDomainEvent):
    def __init__(
        self,
        booking_id,
        addon_id,
        room_stay_id,
        addon_name,
        expense_item_id,
        attribute=None,
        old_value=None,
        new_value=None,
        details=None,
    ):
        self.booking_id = booking_id
        self.addon_id = addon_id
        self.room_stay_id = room_stay_id
        self.name = addon_name
        self.expense_item_id = expense_item_id
        self.expense_category = None
        self.room_type_id = None
        self.room_type_name = None
        if not details:
            self.details = [
                {"attribute": attribute, "old_value": old_value, "new_value": new_value}
            ]
        else:
            self.details = details

    def serialize(self):
        serialized = AddonModifiedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        expense_item_map = kwargs.get('expense_item_map')
        room_stay_map = kwargs.get('room_stay_map')
        room_type_map = kwargs.get('room_type_map')
        self.expense_category = expense_item_map[self.expense_item_id].name
        if self.room_stay_id:
            self.room_type_id = room_stay_map.get(self.room_stay_id).room_type_id
            self.room_type_name = room_type_map.get(self.room_type_id).room_type.type

    def merge(self, mergeable_domain_event):
        if not self.can_merge(mergeable_domain_event):
            raise DomainEventMergerException()

        self.details.extend(mergeable_domain_event.details)

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, AddonModifiedEvent)

    def event_type(self):
        return DomainEvent.ADDON_MODIFIED
