from typing import List

from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import NoShowEventSchema
from prometheus.domain.booking.dtos.noshow_event_data import NoShowEventData
from ths_common.constants.domain_event_constants import DomainEvent


class RoomStayMarkedNoShowEvent(MergeableDomainEvent):
    def __init__(self, booking_id, noshow_event_data: List[NoShowEventData]):
        self.booking_id = booking_id
        self.noshow_event_data = noshow_event_data

    def serialize(self):
        serialized = NoShowEventSchema(many=True).dump(self.noshow_event_data).data
        return serialized

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.ROOM_STAY_MARKED_NOSHOW

    def merge(self, mergeable_domain_event):
        self.noshow_event_data.extend(mergeable_domain_event.noshow_event_data)

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, RoomStayMarkedNoShowEvent)
