from prometheus.core.base_domain_event import MergeableDomain<PERSON>vent
from prometheus.domain.booking.domain_events.schema.booking import (
    GuestStayUpdatedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException


class GuestStayUpdatedEvent(MergeableDomainEvent):
    def __init__(
        self,
        room_stay_id,
        guest_stay_id,
        attribute=None,
        old_value=None,
        new_value=None,
        details=None,
    ):
        self.room_stay_id = room_stay_id
        self.guest_stay_id = guest_stay_id
        if not details:
            self.details = [
                {"attribute": attribute, "old_value": old_value, "new_value": new_value}
            ]
        else:
            self.details = details

    def serialize(self):
        serialized = GuestStayUpdatedEventSchema().dump(self).data
        return serialized

    def event_type(self):
        return DomainEvent.GUEST_STAY_UPDATED

    def merge(self, mergeable_domain_event):
        if not self.can_merge(mergeable_domain_event):
            raise DomainEventMergerException()

        self.details.extend(mergeable_domain_event.details)

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, GuestStayUpdatedEvent)

    def update_mapping(self, **kwargs):
        return
