from typing import List

from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    BookingReCreatedEventSchema,
)
from prometheus.domain.booking.dtos.room_stay_event_data import RoomStayEventData
from ths_common.constants.domain_event_constants import DomainEvent


class BookingReCreatedEvent(BaseDomainEvent):
    def __init__(
        self,
        booking_id,
        reference_number,
        room_stay_event_data: List[RoomStayEventData],
    ):
        self.booking_id = booking_id
        self.reference_number = reference_number
        self.room_stays = room_stay_event_data

    def serialize(self):
        serialized = BookingReCreatedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        room_type_map = kwargs.get('room_type_map')
        for room_stay in self.room_stays:
            room_stay.room_type_name = room_type_map.get(
                room_stay.room_type_id
            ).room_type.type

    def event_type(self):
        return DomainEvent.BOOKING_RECREATED
