from treebo_commons.utils import dateutils

from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    AddonRemovedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent


class AddonRemovedEvent(BaseDomainEvent):
    def __init__(
        self,
        booking_id,
        addon_id,
        name,
        expense_item_id,
        charge_type,
        charge_checkin,
        charge_checkout,
        charge_other_days,
        quantity,
        room_stay_id,
        room_type_id,
        added_by,
        pretax_price=None,
        posttax_price=None,
        start_relative=None,
        end_relative=None,
        start_date=None,
        end_date=None,
        linked=None,
    ):
        self.booking_id = booking_id
        self.addon_id = addon_id
        self.name = name
        self.expense_item_id = expense_item_id
        self.charge_checkin = charge_checkin
        self.charge_other_days = charge_other_days
        self.charge_checkout = charge_checkout
        self.charge_type = charge_type
        self.quantity = quantity
        self.room_stay_id = room_stay_id
        self.room_type_id = room_type_id
        self.added_by = added_by
        self.pretax_price = pretax_price
        self.posttax_price = posttax_price
        self.room_type_name = None
        self.expense_category = None
        self.applicable_from = None
        self.applicable_to = None
        self.start_relative = start_relative
        self.end_relative = end_relative
        self.start_date = start_date
        self.end_date = end_date
        self.linked = linked

    def serialize(self):
        serialized = AddonRemovedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        expense_item_map = kwargs.get('expense_item_map')
        room_type_map = kwargs.get('room_type_map')
        self.expense_category = expense_item_map[self.expense_item_id].name
        self.applicable_from = (
            self.start_relative.value
            if self.start_relative
            else dateutils.date_to_ymd_str(self.start_date)
        )
        self.applicable_to = (
            self.end_relative.value
            if self.end_relative
            else dateutils.date_to_ymd_str(self.end_date)
        )
        if self.room_type_id:
            self.room_type_name = room_type_map.get(self.room_type_id).room_type.type

    def event_type(self):
        return DomainEvent.ADDON_REMOVED
