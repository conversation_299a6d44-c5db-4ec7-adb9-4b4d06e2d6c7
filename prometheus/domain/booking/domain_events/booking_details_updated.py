from prometheus.core.base_domain_event import MergeableDomain<PERSON>vent
from prometheus.domain.booking.domain_events.schema.booking import AttributeUpdateSchema
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException


class BookingDetailsUpdated(MergeableDomainEvent):
    def __init__(
        self, booking_id, attribute=None, old_value=None, new_value=None, details=None
    ):
        self.booking_id = booking_id
        if not details:
            self.details = [
                {"attribute": attribute, "old_value": old_value, "new_value": new_value}
            ]
        else:
            self.details = details

    def serialize(self):
        serialized = AttributeUpdateSchema(many=True).dump(self.details).data
        return serialized

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.BOOKING_DETAILS_MODIFIED

    def merge(self, mergeable_domain_event):
        if not isinstance(mergeable_domain_event, BookingDetailsUpdated):
            raise DomainEventMergerException()

        self.details.extend(mergeable_domain_event.details)

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, BookingDetailsUpdated)
