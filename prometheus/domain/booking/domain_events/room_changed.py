# coding=utf-8

from prometheus.core.base_domain_event import BaseDomain<PERSON>vent
from prometheus.domain.booking.domain_events.schema.booking import (
    RoomChangedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent


class RoomChangedEvent(BaseDomainEvent):
    def __init__(
        self,
        room_stay_id,
        new_room_type_id,
        old_room_type_id,
        new_room_number,
        old_room_number,
    ):
        self.room_stay_id = room_stay_id
        self.new_room_type_id = new_room_type_id
        self.old_room_type_id = old_room_type_id
        self.new_room_number = new_room_number
        self.old_room_number = old_room_number
        self.new_room_type_name = None
        self.old_room_type_name = None

    def serialize(self):
        serialized = RoomChangedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        room_type_map = kwargs.get('room_type_map')
        self.new_room_type_name = room_type_map.get(
            self.new_room_type_id
        ).room_type.type
        self.old_room_type_name = (
            room_type_map.get(self.old_room_type_id).room_type.type
            if self.old_room_type_id
            else None
        )

    def event_type(self):
        return DomainEvent.ROOM_CHANGED
