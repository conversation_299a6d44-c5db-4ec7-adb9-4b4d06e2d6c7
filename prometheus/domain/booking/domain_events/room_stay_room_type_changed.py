from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    RoomTypeChangedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent


class RoomStayRoomTypeChangedEvent(BaseDomainEvent):
    def __init__(
        self,
        room_stay_id,
        old_room_number,
        old_room_type_id,
        new_room_number,
        new_room_type_id,
    ):
        self.room_stay_id = room_stay_id
        self.old_room_number = old_room_number
        self.old_room_type_id = old_room_type_id
        self.new_room_number = new_room_number
        self.new_room_type_id = new_room_type_id
        self.old_room_type_name = None
        self.new_room_type_name = None

    def serialize(self):
        serialized = RoomTypeChangedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        room_type_map = kwargs.get('room_type_map')
        self.old_room_type_name = room_type_map.get(
            self.old_room_type_id
        ).room_type.type
        self.new_room_type_name = room_type_map.get(
            self.new_room_type_id
        ).room_type.type

    def event_type(self):
        return DomainEvent.ROOM_TYPE_CHANGED
