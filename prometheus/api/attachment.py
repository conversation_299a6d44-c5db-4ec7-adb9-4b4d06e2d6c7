# coding=utf-8
import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.booking.command_handlers.create_attachment import (
    CreateAttachmentCommandHandler,
)
from prometheus.application.booking.command_handlers.delete_attachment import (
    DeleteAttachmentCommandHandler,
)
from prometheus.application.booking.command_handlers.patch_attachment import (
    PatchAttachmentCommandHandler,
)
from prometheus.application.booking.query_handlers.get_attachment import (
    GetAttachmentQueryHandler,
)
from prometheus.application.booking.query_handlers.get_attachment_by_id import (
    GetAttachmentByIdQueryHandler,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.request_parsers import (
    read_application_from_request_header,
    read_user_data_from_request_header,
)
from prometheus.common.serializers import AttachmentSchema, EditAttachmentSchema
from prometheus.common.serializers.request.attachment import NewAttachmentSchema
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import schema_wrapper_parser
from ths_common.constants.user_constants import UserType

bp = Blueprint('Attachments', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/bookings/<booking_id>/attachments', methods=['GET'])
@inject(query_handler=GetAttachmentQueryHandler)
def get_attachments(query_handler: GetAttachmentQueryHandler, booking_id):
    """Get Booking Attachments
    ---
    parameters:
          - name: booking_id
            in: path
            type: string
            required: true
    operationId: get_attachments
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get attachments for a given booking.
        tags:
            - Attachments
        responses:
            200:
                description:  Attachment object.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/AttachmentSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    attachment_aggregates = query_handler.handle(
        booking_id=booking_id, user_data=user_data
    )
    attachments = [aggregate.attachment for aggregate in attachment_aggregates]
    response = AttachmentSchema().dump(attachments, many=True)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<booking_id>/attachments/<attachment_id>', methods=['GET'])
@inject(query_handler=GetAttachmentByIdQueryHandler)
def get_attachment(
    query_handler: GetAttachmentByIdQueryHandler, booking_id, attachment_id
):
    """Get Booking Attachment
    ---
    parameters:
          - name: booking_id
            in: path
            type: string
            required: true
          - name: attachment_id
            in: path
            type: string
            required: true
    operationId: get_attachment
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get attachment for a given booking.
        tags:
            - Attachments
        responses:
            200:
                description:  Attachment object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/AttachmentSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    attachment_aggregate = query_handler.handle(
        booking_id=booking_id, attachment_id=attachment_id, user_data=user_data
    )
    attachment = attachment_aggregate.attachment
    response = AttachmentSchema().dump(attachment, many=False)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<booking_id>/attachments', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(NewAttachmentSchema, many=True)
@inject(handler=CreateAttachmentCommandHandler)
def add_attachments(
    handler: CreateAttachmentCommandHandler, booking_id, parsed_request
):
    """Add new attachments
    ---
    operationId: add_attachments
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Add new attachments to a given booking.
        tags:
            - Attachments
        parameters:
            - in: body
              name: body
              description: The attachment objects which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        type: array
                        items:
                            $ref: "#/definitions/NewAttachmentSchema"
        responses:
            200:
                description: Attachment object.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/NewAttachmentSchema"
            responses:
                200:
                    description: Attachment object.
                    schema:
                        type: object
                        properties:
                            data:
                                type: array
                                items:
                                    $ref: "#/components/schemas/AttachmentSchema"
                            meta:
                                type: object
                                additionalProperties: {}
                            errors:
                                type: array
                                items:
                                    $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    source = read_application_from_request_header()
    attachment_aggregates = handler.handle(
        booking_id, parsed_request, source, user_data
    )
    attachments = [aggregate.attachment for aggregate in attachment_aggregates]
    response = AttachmentSchema().dump(attachments, many=True)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<booking_id>/attachments/<attachment_id>', methods=['PATCH'])
@authorize_write_op
@schema_wrapper_parser(EditAttachmentSchema)
@inject(handler=PatchAttachmentCommandHandler)
def edit_attachment(
    handler: PatchAttachmentCommandHandler, booking_id, attachment_id, parsed_request
):
    """edit attachment
    ---
    operationId: edit_attachment
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: edit attachments of a given booking.
        tags:
            - Attachments
        parameters:
            - in: body
              name: body
              description: The attachment objects which needs to be edited
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/EditAttachmentSchema"
        responses:
            200:
                description: Attachment object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/AttachmentSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    attachment_aggregate = handler.handle(
        booking_id, attachment_id, parsed_request, user_data
    )
    response = AttachmentSchema().dump(attachment_aggregate.attachment)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<booking_id>/attachments/<attachment_id>', methods=['DELETE'])
@authorize_write_op
@inject(handler=DeleteAttachmentCommandHandler)
def delete_attachment(
    handler: DeleteAttachmentCommandHandler, booking_id, attachment_id
):
    """Delete an attachment
    ---
    operationId: delete_attachment
    parameters:
    - in: path
      name: booking_id
      description: The booking id of the booking which attachment need to be deleted
      required: True
      type: string
    - in: path
      name: attachment_id
      description: The attachment id which needs to be deleted
      required: True
      type: string
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    delete:
        description: Delete an attachment from a given booking.
    tags:
        - Attachments
    parameters:
        - in: body
          name: body
          description: The attachment objects which needs to be edited
          required: True
          schema:
            type: object
            properties:
                data:
                    $ref: "#/definitions/EditAttachmentSchema"
    responses:
        200:
            description: Attachment object.
            schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/AttachmentSchema"
                    meta:
                        type: object
                        additionalProperties: {}
                    errors:
                        type: array
                        items:
                            $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    attachment_aggregate = handler.handle(
        booking_id=booking_id, attachment_id=attachment_id, user_data=user_data
    )
    attachments = attachment_aggregate.attachment
    response = AttachmentSchema().dump(attachments, many=False)
    return ApiResponse.build(data=response.data, status_code=200)
