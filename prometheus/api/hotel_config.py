import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.hotel_settings.hotel_config_app_service import (
    HotelConfigApplicationService,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.serializers import HotelConfigResponseSchema
from prometheus.core.api_docs import swag_route

bp = Blueprint('HotelConfig', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/hotel-configs/<hotel_id>', methods=['GET'])
@inject(hotel_config_app_service=HotelConfigApplicationService)
def get_hotel_config(hotel_config_app_service, hotel_id):
    """Fetch whether the hotel is live on CRS or not
    ---
    operationId: get_hotel_config
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
        description: Fetch whether the hotel is live on CRS or not
        tags:
            - HotelConfig
        responses:
            200:
                description: Fetch whether a hotel is live on CRS or not
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HotelConfigResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    hotel_config_aggregate = hotel_config_app_service.get_hotel_config(hotel_id)
    data = HotelConfigResponseSchema().dump(hotel_config_aggregate.hotel_config).data
    return ApiResponse.build(data=data, status_code=200)


@swag_route
@bp.route('/hotel-configs', methods=['GET'])
@inject(hotel_config_app_service=HotelConfigApplicationService)
def get_hotel_configs(hotel_config_app_service):
    """Fetch hotel configs
    ---
    operationId: get_hotel_config
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Fetch hotel configs
        parameters:
            - name: hotel_config_filter
              in: query
              required: false
              schema:
                $ref: "#/components/schemas/HotelConfigFilterQuery"
        tags:
            - HotelConfig
        responses:
            200:
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/HotelConfigResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    hotel_config_aggregates = hotel_config_app_service.get_hotel_configs()
    data = (
        HotelConfigResponseSchema(many=True)
        .dump(
            [
                hotel_config_aggregate.hotel_config
                for hotel_config_aggregate in hotel_config_aggregates
            ]
        )
        .data
    )
    return ApiResponse.build(data=data, status_code=200)
