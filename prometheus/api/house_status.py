import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.housekeeping.get_hotel_booking import (
    GetHotelBookingQueryHandler,
)
from prometheus.application.housekeeping.house_status_service import HouseStatusService
from prometheus.common.api_response import ApiResponse
from prometheus.common.serializers import (
    GuestSearch<PERSON>ilter,
    HotelBookingDetailsResponseSchema,
    HouseStatisticsResponseSchema,
    HouseStatusArrivalDepartureResponseSchema,
    HouseStatusFilter,
    HouseStatusResponseSchema,
)
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)

bp = Blueprint('HouseStatus', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/hotels/<string:hotel_id>/house-status', methods=['GET'])
@inject(house_status_service=HouseStatusService)
def get_house_status(house_status_service, hotel_id):
    """Get Current House Status
    ---
    parameters:
          - name: hotel_id
            in: path
            type: string
            required: true
    operationId: get_house_status
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get current House Status
        tags:
            - House Status
        responses:
            200:
                description: Current House Status
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HouseStatusResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    house_status_dtos = house_status_service.get_house_status(hotel_id)
    response = HouseStatusResponseSchema(many=True).dump(house_status_dtos)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<string:hotel_id>/house-statistics', methods=['GET'])
@schema_wrapper_parser(HouseStatusFilter, param_type=RequestTypes.ARGS)
@inject(house_status_service=HouseStatusService)
def get_house_statistics(house_status_service, hotel_id, parsed_request):
    """Get House Statistics, on a given business date.
    ---
    operationId: get_house_statistics
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    externalDocs:
        description: Project documents
        url: None
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: house_statistics_filter
              in: query
              required: false
              schema:
                $ref: "#/components/schemas/HouseStatusFilter"
        description: Get House Statistics, on a given business date.
        tags:
            - House Status
        responses:
            200:
                description: HouseStatistics for requested business date
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HouseStatisticsResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    house_statistics = house_status_service.generate_house_statistics(
        hotel_id, parsed_request.get('business_date')
    )
    response = HouseStatisticsResponseSchema().dump(house_statistics)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<string:hotel_id>/house-status-arrival-departure', methods=['GET'])
@schema_wrapper_parser(HouseStatusFilter, param_type=RequestTypes.ARGS)
@inject(house_status_service=HouseStatusService)
def get_house_status_arrival_departure(house_status_service, hotel_id, parsed_request):
    """Get House Arrival & Departure Stats, on a given business date.
    ---
    operationId: get_house_status_arrival_departure
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    externalDocs:
        description: Project documents
        url: None
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: house_statistics_filter
              in: query
              required: false
              schema:
                $ref: "#/components/schemas/HouseStatusFilter"
        description: Get House Arrival & Departure Stats, on a given business date.
        tags:
            - House Status
        responses:
            200:
                description: ArrivalDeparture stats for requested business date
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HouseStatusArrivalDepartureResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    arrival_departure_stats = house_status_service.get_house_status_arrival_departure(
        hotel_id, parsed_request.get('business_date')
    )
    response = HouseStatusArrivalDepartureResponseSchema().dump(arrival_departure_stats)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<string:hotel_id>/refresh-availability-status', methods=['POST'])
@inject(house_status_service=HouseStatusService)
def refresh_availability_status_for_current_business_date(
    house_status_service, hotel_id
):
    """Refreshes entries in RoomInventoryAvailability, from RoomInventory current status and DNRs, for current
    business date
    """
    house_status_service.refresh_availability_status_for_current_business_date(hotel_id)
    return ApiResponse.build(
        status_code=200, data=dict(message="Room Availability status refreshed")
    )


@swag_route
@bp.route('/hotels/<string:hotel_id>/guests', methods=['GET'])
@schema_wrapper_parser(GuestSearchFilter, param_type=RequestTypes.ARGS)
@inject(query_handler=GetHotelBookingQueryHandler)
def get_booking_details_v2(
    query_handler: GetHotelBookingQueryHandler, hotel_id, parsed_request
):
    room_no = parsed_request.get('room_no')
    name = parsed_request.get('name')
    hotel_bookings_data = query_handler.handle(hotel_id, room_no=room_no, name=name)
    response = HotelBookingDetailsResponseSchema(many=True).dump(hotel_bookings_data)
    return ApiResponse.build(status_code=200, data=response.data)
