import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.housekeeping.housekeeping_application_service import (
    HouseKeepingApplicationService,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers import (
    HouseKeeperResponseSchema,
    HouseKeepingRecordResponseSchema,
)
from prometheus.common.serializers.request import (
    CreateHouseKeeperSchema,
    CreateOrUpdateHouseKeepersSchema,
    UpdateHouseKeepingOccupancySchema,
    UpdateHouseKeepingRecordSchema,
)
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)

bp = Blueprint('HouseKeeping', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)

__all__ = [
    'create_new_housekeeper',
    'get_all_housekeepers',
    'update_housekeeping_record',
    'create_or_update_multiple_housekeepers',
]


@swag_route
@bp.route('/hotels/<string:hotel_id>/housekeepers', methods=['POST'])
@schema_wrapper_parser(CreateHouseKeeperSchema, param_type=RequestTypes.JSON)
@inject(housekeeping_application_service=HouseKeepingApplicationService)
def create_new_housekeeper(housekeeping_application_service, hotel_id, parsed_request):
    """Creates a new Housekeeper with given name, under hotel_id
    ---
    operationId: create_new_housekeeper
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    externalDocs:
        description: Project documents
        url: https://docs.google.com/document/d/1VXOb29MK7-8wHeFL9QJ-0SJnoOogDHpi40eI15MpJNc/edit
    post:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - in: body
              name: body
              description: The housekeeper object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/CreateHouseKeeperSchema"
        description: Creates a new Housekeeper with given name, under hotel_id
        tags:
            - HouseKeeping
        responses:
            200:
                description: Newly created housekeeper
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HouseKeeperResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    housekeeper = housekeeping_application_service.create_new_housekeeper(
        hotel_id, parsed_request.get('name')
    )
    housekeeper_response_data = HouseKeeperResponseSchema().dump(housekeeper).data
    return ApiResponse.build(status_code=201, data=housekeeper_response_data)


@swag_route
@bp.route('/hotels/<string:hotel_id>/housekeepers', methods=['PATCH'])
@schema_wrapper_parser(
    CreateOrUpdateHouseKeepersSchema, param_type=RequestTypes.JSON, many=True
)
@inject(housekeeping_application_service=HouseKeepingApplicationService)
def create_or_update_multiple_housekeepers(
    housekeeping_application_service, hotel_id, parsed_request
):
    """Create or Update or Delete housekeepers.
    ---
    operationId: create_or_create_or_update_multiple_housekeepers
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - in: body
              name: body
              description: The data to update housekeeper
              required: True
              schema:
                type: object
                properties:
                    data:
                        type: array
                        items:
                            $ref: "#/components/schemas/CreateOrUpdateHouseKeepersSchema"
        description: Creates a new housekeeper, or update an existing housekeeper, or deletes the housekeeper
        tags:
            - HouseKeeping
        responses:
            200:
                description: List of updated or created housekeepers. Deleted housekeepers won't be returned
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/HouseKeeperResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    housekeepers = (
        housekeeping_application_service.create_or_update_multiple_housekeepers(
            hotel_id, parsed_request
        )
    )
    housekeeper_response_data = (
        HouseKeeperResponseSchema(many=True).dump(housekeepers).data
    )
    return ApiResponse.build(status_code=200, data=housekeeper_response_data)


@swag_route
@bp.route('/hotels/<string:hotel_id>/housekeepers', methods=['GET'])
@inject(housekeeping_application_service=HouseKeepingApplicationService)
def get_all_housekeepers(housekeeping_application_service, hotel_id):
    """Gets all the Housekeepers in the given Hotel
    ---
    operationId: get_all_housekeepers
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    externalDocs:
        description: Project documents
        url: https://docs.google.com/document/d/1VXOb29MK7-8wHeFL9QJ-0SJnoOogDHpi40eI15MpJNc/edit
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
        description: Gets all the housekeepers in the given hotel
        tags:
            - HouseKeeping
        responses:
            200:
                description: List of housekeepers
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/HouseKeeperResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    housekeepers = housekeeping_application_service.get_all_housekeepers(hotel_id)
    housekeeper_response_data = (
        HouseKeeperResponseSchema(many=True).dump(housekeepers).data
    )
    return ApiResponse.build(status_code=200, data=housekeeper_response_data)


@swag_route
@bp.route(
    '/hotels/<string:hotel_id>/rooms/<int:room_id>/housekeeping-record',
    methods=['PATCH'],
)
@schema_wrapper_parser(UpdateHouseKeepingRecordSchema, param_type=RequestTypes.JSON)
@inject(housekeeping_application_service=HouseKeepingApplicationService)
def update_housekeeping_record(
    housekeeping_application_service, hotel_id, room_id, parsed_request
):
    """Updates the housekeeping status, remarks, and also assign a housekeeper to a room
    ---
    operationId: update_housekeeping_record
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    externalDocs:
        description: Project documents
        url: https://docs.google.com/document/d/1VXOb29MK7-8wHeFL9QJ-0SJnoOogDHpi40eI15MpJNc/edit
    patch:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: room_id
              in: path
              type: integer
              required: true
            - in: body
              name: body
              description: The data to update housekeeping status of a room
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/UpdateHouseKeepingRecordSchema"
        description: Updates the housekeeping status, remarks, and also assign a housekeeper to a room
        tags:
            - HouseKeeping
        responses:
            200:
                description: Updated housekeeping status of the room
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HouseKeepingRecordResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    housekeeping_record = housekeeping_application_service.update_housekeeping_record(
        hotel_id,
        room_id,
        parsed_request.get('housekeeping_status'),
        parsed_request.get('housekeeper_id'),
        parsed_request.get('remarks'),
        user_data=user_data,
    )
    housekeeping_record_response_data = (
        HouseKeepingRecordResponseSchema().dump(housekeeping_record).data
    )
    return ApiResponse.build(status_code=200, data=housekeeping_record_response_data)


@swag_route
@bp.route(
    '/hotels/<string:hotel_id>/rooms/<int:room_id>/housekeeping-record/update-hk-occupancy',
    methods=['POST'],
)
@schema_wrapper_parser(UpdateHouseKeepingOccupancySchema, param_type=RequestTypes.JSON)
@inject(housekeeping_application_service=HouseKeepingApplicationService)
def update_housekeeping_occupancy_record(
    housekeeping_application_service, hotel_id, room_id, parsed_request
):
    """Updates the housekeeping occupancy, remarks to a room
    ---
    operationId: update_housekeeping_occupancy_record
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    externalDocs:
        description: Project documents
        url: https://docs.google.com/document/d/1VXOb29MK7-8wHeFL9QJ-0SJnoOogDHpi40eI15MpJNc/edit
    patch:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: room_id
              in: path
              type: integer
              required: true
            - in: body
              name: body
              description: The data to update housekeeping status of a room
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/UpdateHouseKeepingOccupancySchema"
        description: Updates the housekeeping occupancy of a room
        tags:
            - HouseKeeping
        responses:
            200:
                description: Updates the housekeeping occupancy of a room
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HouseKeepingRecordResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    housekeeping_record = (
        housekeeping_application_service.update_housekeeping_occupancy_record(
            hotel_id,
            room_id,
            parsed_request,
            user_data=user_data,
        )
    )
    housekeeping_record_response_data = (
        HouseKeepingRecordResponseSchema().dump(housekeeping_record).data
    )
    return ApiResponse.build(status_code=200, data=housekeeping_record_response_data)


@swag_route
@bp.route('/hotels/<string:hotel_id>/housekeeping-records', methods=['GET'])
@inject(housekeeping_application_service=HouseKeepingApplicationService)
def get_all_housekeeping_records(housekeeping_application_service, hotel_id):
    """Gets the Housekeeping records for all the rooms in the given Hotel
    ---
    operationId: get_all_housekeeping_records
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    externalDocs:
        description: Project documents
        url: https://docs.google.com/document/d/1VXOb29MK7-8wHeFL9QJ-0SJnoOogDHpi40eI15MpJNc/edit
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
        description: Gets the housekeeping status for all rooms in the given hotel
        tags:
            - HouseKeeping
        responses:
            200:
                description: List of housekeepings
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/HouseKeepingRecordResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    housekeeping_records = (
        housekeeping_application_service.get_all_housekeeping_records(hotel_id)
    )
    housekeeping_records_response_data = (
        HouseKeepingRecordResponseSchema(many=True).dump(housekeeping_records).data
    )
    return ApiResponse.build(status_code=200, data=housekeeping_records_response_data)


@swag_route
@bp.route(
    '/hotels/<string:hotel_id>/housekeeping-records/template-url', methods=['GET']
)
@inject(housekeeping_application_service=HouseKeepingApplicationService)
def get_housekeeping_records_template(housekeeping_application_service, hotel_id):
    """Generate Housekeeping records template for all the rooms in a given Hotel
    ---
    operationId: get_all_housekeeping_records
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    externalDocs:
        description: Project documents
        url: None
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
        description: Gets the housekeeping status for all rooms in the given hotel
        tags:
            - HouseKeeping
        responses:
            200:
                description: pdf url for housekeeping records
                schema:
                    type: object
                    properties:
                        data:
                            type: string
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    housekeeping_record_template_link = (
        housekeeping_application_service.generate_housekeeping_record_template_url(
            hotel_id
        )
    )
    return ApiResponse.build(status_code=200, data=housekeeping_record_template_link)
