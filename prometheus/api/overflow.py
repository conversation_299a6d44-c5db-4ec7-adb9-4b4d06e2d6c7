"""
Overflow Swap API
"""
import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers import OverflowResponseSchema
from prometheus.common.serializers.request.booking import SwapOverflowSchema
from prometheus.core.api_docs import swag_route
from prometheus.domain.booking.dtos.overflow_dto import OverflowDto
from shared_kernel.api_helpers.request_parsers import schema_wrapper_parser
from ths_common.constants.booking_constants import OverflowActions
from ths_common.constants.user_constants import UserType

bp = Blueprint('Overflow', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/hotels/<hotel_id>/room-stay-overflows', methods=['PATCH'])
@authorize_write_op
@schema_wrapper_parser(SwapOverflowSchema)
@inject(room_stay_overflow_service=RoomStayOverflowService)
def swap_room_stay_overflows(hotel_id, room_stay_overflow_service, parsed_request):
    """Swap an overflowing room stay
    ---
    operationId: swap_room_stay_overflows
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: Swap an overflowing room stay with a list of confirmed room stays
        tags:
            - Overflow
        parameters:
            - in: path
              name: hotel_id
              description: The hotel id of the room stays to be swapped
              required: True
              type: string
            - in: body
              name: body
              description: List of room stays to mark and unmark as overflow
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/SwapOverflowSchema"
        responses:
            200:
                description: The schema of the edited expense.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/OverflowResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)

    mark_overflow_list = [
        OverflowDto.from_dict(overflow_dict)
        for overflow_dict in parsed_request.get('overflows')
        if overflow_dict.get('action') == OverflowActions.MARK.value
    ]
    unmark_overflow_list = [
        OverflowDto.from_dict(overflow_dict)
        for overflow_dict in parsed_request.get('overflows')
        if overflow_dict.get('action') == OverflowActions.UNMARK.value
    ]

    effected_overflow_aggregates = (
        room_stay_overflow_service.bulk_mark_and_unmark_overflow(
            hotel_id,
            mark_overflow_list,
            unmark_overflow_list,
            user_data,
            user_action="swap_room_stay_overflow",
        )
    )

    effected_overflows = [
        eof.room_stay_overflow for eof in effected_overflow_aggregates
    ]
    response = OverflowResponseSchema(many=True).dump(effected_overflows)
    return ApiResponse.build(data=response.data, status_code=200)
