import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.end_of_day.night_audit_service import NightAuditService
from prometheus.application.hotel_settings.catalog_application_service import (
    CatalogApplicationService,
)
from prometheus.application.hotel_settings.hotel_config_app_service import (
    HotelConfigApplicationService,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.booking_migration_service import (
    BookingMigrationService,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers import HotelConfigResponseSchema
from prometheus.common.serializers.request.booking import BookingMigrationSchema
from prometheus.common.serializers.request.crs_migration import InitMigrationSchema
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)
from ths_common.constants.user_constants import UserType

bp = Blueprint('CRS_Migration', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/crs-migration/hotel-sync/<hotel_id>', methods=['POST'])
@authorize_write_op
@inject(
    catalog_application_service=CatalogApplicationService,
    night_audit_service=NightAuditService,
    tenant_settings=TenantSettings,
)
def sync_hotel(
    catalog_application_service, night_audit_service, tenant_settings, hotel_id
):
    """Sync the hotel, and corresponding room from catalog onto CRS
    ---
    operationId: sync_hotel
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
        description: Sync the hotel, and corresponding room from catalog onto CRS
        tags:
            - CRS Migration
        responses:
            201: None
    """
    catalog_application_service.sync_hotel(hotel_id)
    if catalog_application_service.is_hotel_active(
        hotel_id
    ) and not tenant_settings.is_auto_night_audit_disabled(hotel_id):
        night_audit_service.schedule_night_audit(hotel_id)
    return ApiResponse.build(data=dict(hotel_loaded=hotel_id), status_code=201)


@swag_route
@bp.route('/crs-migration/hotel-seller-sync/<hotel_id>', methods=['POST'])
@authorize_write_op
@inject(catalog_application_service=CatalogApplicationService)
def sync_seller(catalog_application_service, hotel_id):
    """Sync the seller of a hotel  from catalog onto CRS
    ---
    operationId: sync_seller
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
        description: Sync the sellers of a hotel from catalog onto CRS
        tags:
            - CRS Migration
        responses:
            201: None
    """
    catalog_application_service.sync_seller(hotel_id)
    return ApiResponse.build(data=dict(hotel_sellers_loaded=hotel_id), status_code=201)


@swag_route
@bp.route('/crs-migration/init-migration/<hotel_id>', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(InitMigrationSchema, param_type=RequestTypes.ARGS)
@inject(hotel_config_app_service=HotelConfigApplicationService)
def start_migration(hotel_config_app_service, hotel_id, parsed_request):
    """Start Migration for the given hotel id
    ---
    operationId: start_migration
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: dry_run
              in: query
              type: boolean
              required: true
              default: true
        description: Start the migration for the given hotel id
        tags:
            - CRS Migration
        responses:
            201:
                description: The created HotelConfig entity
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HotelConfigResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    hotel_config_aggregate = hotel_config_app_service.initiate_hotel_migration_to_crs(
        hotel_id=hotel_id, user_data=user_data, mock=parsed_request.get('dry_run')
    )
    data = HotelConfigResponseSchema().dump(hotel_config_aggregate.hotel_config).data
    return ApiResponse.build(data=data, status_code=201)


@swag_route
@bp.route('/crs-migration/complete-migration/<hotel_id>', methods=['POST'])
@authorize_write_op
@inject(hotel_config_app_service=HotelConfigApplicationService)
def complete_migration(hotel_config_app_service, hotel_id):
    """Finish the migration for the given hotel id
    ---
    operationId: complete_migration
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
        description: Finish the migration for the given hotel id
        tags:
            - CRS Migration
        responses:
            201:
                description: The updated HotelConfig entity
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HotelConfigResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    hotel_config_aggregate = hotel_config_app_service.complete_hotel_migration(
        hotel_id, user_data=user_data
    )
    data = HotelConfigResponseSchema().dump(hotel_config_aggregate.hotel_config).data
    return ApiResponse.build(data=data, status_code=200)


@swag_route
@bp.route('/crs-migration/rollback-migration/<hotel_id>', methods=['POST'])
@authorize_write_op
@inject(hotel_config_app_service=HotelConfigApplicationService)
def rollback_migration(hotel_config_app_service, hotel_id):
    """Rollbacks the migration of the given hotel_id to HX
    ---
    operationId: complete_migration
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
        description: Rollbacks the migration of the given hotel_id to HX
        tags:
            - CRS Migration
        responses:
            201:
                description: The updated HotelConfig entity
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HotelConfigResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    hotel_config_aggregate = hotel_config_app_service.rollback_hotel_migration(
        hotel_id, user_data=user_data
    )
    data = HotelConfigResponseSchema().dump(hotel_config_aggregate.hotel_config).data
    return ApiResponse.build(data=data, status_code=200)


@swag_route
@bp.route('/crs-migration/booking-migration/', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(BookingMigrationSchema)
@inject(booking_migration_service=BookingMigrationService)
def migrate_booking_using_hotel_ids(booking_migration_service, parsed_request):
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    (
        scheduled_bookings,
        skipped_bookings,
    ) = booking_migration_service.schedule_migrate_bookings(
        parsed_request.get('reference_numbers'),
        parsed_request['source_hotel_id'],
        parsed_request['destination_hotel_id'],
        user_data,
    )
    response = dict(
        scheduled_bookings=scheduled_bookings, skipped_bookings=skipped_bookings
    )
    return ApiResponse.build(data=response, status_code=200)
