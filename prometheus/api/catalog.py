import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.hotel_settings.catalog_application_service import (
    CatalogApplicationService,
)
from prometheus.application.hotel_settings.hotel_ownership_update_service import (
    HotelOwnershipUpdateService,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers import HotelOwnershipResetSchema, SkuCategorySchema
from prometheus.core.api_docs import swag_route
from prometheus.domain.catalog.dtos.sku_category_dto import SkuCategoryDto
from shared_kernel.api_helpers.request_parsers import schema_wrapper_parser
from ths_common.constants.user_constants import UserType

bp = Blueprint('Catalog', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/catalogs/sku-categories', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(SkuCategorySchema)
@inject(catalog_application_service=CatalogApplicationService)
def create_sku_categories(catalog_application_service, parsed_request):
    """Add sku category (FOR INTERNAL PURPOSE ONLY)
    ---
    operationId: create_sku_categories
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: create sku categories cache in crs (db cache).
        tags:
            - Catalog
        parameters:
            - in: body
              name: body
              description: The Sku category objects which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/SkuCategorySchema"
        responses:
            200:
                description: The created sku categories.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/SkuCategorySchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    sku_category_dto = SkuCategoryDto(
        sku_category_id=parsed_request['sku_category_id'],
        item_code=parsed_request.get('item_code'),
        name=parsed_request['name'],
        status=parsed_request['status'],
        has_slab_based_taxation=parsed_request.get('has_slab_based_taxation', False),
    )

    sku_category_aggregate = catalog_application_service.add_sku_category(
        sku_category_dto
    )

    response = SkuCategorySchema().dump(sku_category_aggregate)
    return ApiResponse.build(data=response.data, status_code=201)


@swag_route
@bp.route('/catalogs/sku-categories', methods=['GET'])
@inject(catalog_application_service=CatalogApplicationService)
def get_sku_categories(catalog_application_service):
    """get sku categories (FOR INTERNAL PURPOSE ONLY)
    ---
    operationId: get_sku_categories
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: create sku categories cache in crs (db cache).
        tags:
            - Catalog
        responses:
            200:
                description: The created sku categories.
                schema:
                    type: array
                    properties:
                        data:
                            $ref: "#/components/schemas/SkuCategorySchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    sku_category_aggregates = catalog_application_service.get_sku_categories()

    response = SkuCategorySchema(many=True).dump(sku_category_aggregates)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/hotels/<string:hotel_id>/ownership', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(HotelOwnershipResetSchema)
@inject(hotel_ownership_update_service=HotelOwnershipUpdateService)
def reset_hotel_ownership(hotel_ownership_update_service, hotel_id, parsed_request):
    """reset hotel ownership along with the invoice and credit note sequence number details
    ---
    operationId: reset_hotel_ownership
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: reset the gst details of the hotel and the update the invoice/credit note sequence.
        tags:
            - Catalog
        parameters:
            - in: path
              name: hotel_id
              description: The hotel id on which ownership reset needs to be done
              type: string
              required: True
            - in: body
              name: body
              description: The reset hotel ownership data
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/HotelOwnershipResetSchema"
        responses:
            200:
                description: hotel ownership has been reset
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                message:
                                    type: string
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    hotel_ownership_update_service.reset_hotel_ownership(
        hotel_id,
        parsed_request.get('gstin_num'),
        parsed_request.get('sequence_number_details'),
        user_data,
    )
    response = dict(message="hotel ownership has been reset successfully")
    return ApiResponse.build(data=response, status_code=200)
