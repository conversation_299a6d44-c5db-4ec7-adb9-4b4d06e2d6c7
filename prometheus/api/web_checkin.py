import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.booking.command_handlers.web_checkin.add_web_checkin import (
    AddWebCheckinCommandHandler,
)
from prometheus.application.booking.command_handlers.web_checkin.edit_web_checkin import (
    EditWeb<PERSON><PERSON>ckin<PERSON>ommandHandler,
)
from prometheus.application.booking.query_handlers.web_checkin.get_web_checkin_by_id import (
    GetWebCheckinByIdQueryHandler,
)
from prometheus.application.booking.query_handlers.web_checkin.get_web_checkins import (
    GetWebCheckinsForBookingQueryHandler,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.serializers.request.web_checkin import (
    AttachWebCheckinSchema,
    EditWebCheckinSchema,
    GetWebCheckinSchema,
)
from prometheus.common.serializers.response.web_checkin import WebCheckinSchema
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)

bp = Blueprint('WebCheckin', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route(
    '/bookings/<string:booking_id>/web-checkins/<string:web_checkin_id>',
    methods=['GET'],
)
@inject(query_handler=GetWebCheckinByIdQueryHandler)
def get_web_checkin(
    query_handler: GetWebCheckinByIdQueryHandler, booking_id, web_checkin_id
):
    """Get Web Checkin
    ---
    parameters:
          - name: booking_id
            in: path
            type: string
            required: true
          - name: web_checkin_id
            in: path
            type: string
            required: true
    operationId: get_web_checkin
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get web_checkin data for a given booking.
        tags:
            - WebCheckin
        responses:
            200:
                description:  Web Checkin object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/WebCheckinSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    web_checkin_aggregate = query_handler.handle(booking_id, web_checkin_id)
    response = WebCheckinSchema().dump(web_checkin_aggregate.web_checkin)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<string:booking_id>/web-checkins', methods=['GET'])
@inject(query_handler=GetWebCheckinsForBookingQueryHandler)
def get_web_checkins(query_handler: GetWebCheckinsForBookingQueryHandler, booking_id):
    """Get all Web Checkin for a booking
    ---
    parameters:
          - name: booking_id
            in: path
            type: string
            required: true
    operationId: get_web_checkins
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get all web_checkin data for a given booking.
        tags:
            - WebCheckin
        responses:
            200:
                description:  Web Checkin object.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/WebCheckinSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    web_checkin_aggregates = query_handler.handle([booking_id])
    web_checkins = [
        web_checkin_aggregate.web_checkin
        for web_checkin_aggregate in web_checkin_aggregates
    ]
    response = WebCheckinSchema().dump(web_checkins, many=True)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<string:booking_id>/web-checkins', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(AttachWebCheckinSchema)
@inject(command_handler=AddWebCheckinCommandHandler)
def add_web_checkin_to_booking(
    command_handler: AddWebCheckinCommandHandler, booking_id, parsed_request
):
    """Attach web-checkin entity to a booking
    ---
    operationId: attach_web_checkin
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: adding web-checkin to a booking
        tags:
            - WebCheckin
        parameters:
            - in: body
              name: body
              description: Need to create web checkin entity
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/AttachWebCheckinSchema"
        responses:
            200:
                description: Web Checkin object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/AttachWebCheckinSchema"
            responses:
                200:
                    description: Web Checkin object.
                    schema:
                        type: object
                        properties:
                            data:
                                $ref: "#/components/schemas/WebCheckinSchema"
                            meta:
                                type: object
                                additionalProperties: {}
                            errors:
                                type: array
                                items:
                                    $ref: "#/components/schemas/ApiErrorSchema"
    """

    web_checkin_aggregate = command_handler.handle(booking_id, parsed_request)
    response = WebCheckinSchema().dump(web_checkin_aggregate.web_checkin)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route(
    '/bookings/<string:booking_id>/web-checkins/<string:web_checkin_id>',
    methods=['PATCH'],
)
@authorize_write_op
@schema_wrapper_parser(EditWebCheckinSchema)
@inject(command_handler=EditWebCheckinCommandHandler)
def edit_web_checkin(
    command_handler: EditWebCheckinCommandHandler,
    booking_id,
    web_checkin_id,
    parsed_request,
):
    """Edit web-checkin entity of a booking
    ---
    operationId: edit_web_checkin
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: edit web-checkin of a booking
        tags:
            - WebCheckin
        parameters:
            - in: body
              name: body
              description: Need to edit web checkin entity
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/EditWebCheckinSchema"
        responses:
            200:
                description: Web Checkin object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/WebCheckinSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    web_checkin_aggregate = command_handler.handle(
        booking_id, web_checkin_id, parsed_request
    )
    response = WebCheckinSchema().dump(web_checkin_aggregate.web_checkin)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/web-checkins', methods=['GET'])
@schema_wrapper_parser(GetWebCheckinSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetWebCheckinsForBookingQueryHandler)
def get_web_checkins_by_booking_ids(
    query_handler: GetWebCheckinsForBookingQueryHandler, parsed_request
):
    """Get all Web Checkins for respective booking ids
    ---
    parameters:
          - name: booking_ids
            in: path
            type: list
            required: true
    operationId: get_web_checkins_by_booking_ids
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get all web_checkin data for repective booking ids
        tags:
            - WebCheckin
        responses:
            200:
                description:  Web Checkin object.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/definitions/GetWebCheckinSchema"
                        meta:
                            type: object
                            properties:
                                data:
                                    type: array
                                    items:
                                        $ref: "#/components/schemas/GetWebCheckinSchema"
                                meta:
                                    type: object
                                    additionalProperties: {}
                                errors:
                                    type: array
                                    items:
                                        $ref: "#/components/schemas/ApiErrorSchema"
    """
    booking_ids = parsed_request.get("booking_ids")
    web_checkin_aggregates = query_handler.handle(booking_ids)
    web_checkins = [
        web_checkin_aggregate.web_checkin
        for web_checkin_aggregate in web_checkin_aggregates
    ]
    response = WebCheckinSchema().dump(web_checkins, many=True)
    return ApiResponse.build(data=response.data, status_code=200)
