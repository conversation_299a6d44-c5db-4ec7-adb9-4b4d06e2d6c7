import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.billing.query_handlers.get_bill import GetBillQueryHandler
from prometheus.application.billing.query_handlers.get_bills import GetBillsQueryHandler
from prometheus.application.billing.query_handlers.get_credit_notes_for_bill import (
    GetCreditNotesQueryHandler,
)
from prometheus.application.billing.query_handlers.get_invoices_for_bill_v2 import (
    GetInvoicesForBillV2QueryHandler,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.request_parsers import read_last_version_from_header
from prometheus.common.serializers import BillSchema, CreditNoteSchema
from prometheus.common.serializers.request.billing import (
    BillGetFieldFilterSchema,
    BillsGetSchema,
    CreditNoteGetSchema,
    InvoiceGetSchemaV2,
)
from prometheus.common.serializers.response.billing_v2 import (
    BillSummaryResponseSchemaV2,
    ShallowBillResponseSchemaV2,
    ShallowCreditNoteSchema,
)
from prometheus.common.serializers.response.invoice_v2 import (
    InvoiceSchemaWithLineItems,
    ShallowInvoiceSchema,
)
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)
from ths_common.exceptions import NotModifiedException

bp = Blueprint('BillV2', __name__, url_prefix="/v2")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/bills', methods=['GET'])
@schema_wrapper_parser(BillsGetSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetBillsQueryHandler)
def get_bills_v2(query_handler: GetBillsQueryHandler, parsed_request):
    """Get bills
    ---
    operationId: get_bills
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get bills details.
        parameters:
            - in: query
              name: bill_ids
              description: The bill ids which needs to be fetched
              required: True
              schema:
                $ref: "#/components/schemas/BillsGetSchema"
        tags:
            - Bills
        responses:
            200:
                description: The fetched bills.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/BillSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """

    bill_ids = parsed_request.get("bill_ids")
    summary = parsed_request.get('summary')

    if parsed_request.get('summary'):
        bill_summary_response_dto = query_handler.handle(bill_ids, only_summary=summary)
        bill_response = BillSummaryResponseSchemaV2(many=True).dump(
            bill_summary_response_dto
        )
    else:
        bill_aggregates = query_handler.handle(bill_ids, only_summary=summary)
        bill_response = BillSchema(many=True).dump(bill_aggregates)
    return ApiResponse.build(data=dict(bills=bill_response.data), status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>', methods=['GET'])
@schema_wrapper_parser(BillGetFieldFilterSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetBillQueryHandler)
def get_bill_v2(query_handler: GetBillQueryHandler, bill_id, parsed_request):
    """Get bill
    ---
    operationId: get_bill
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get bill details.
        parameters:
            - in: path
              name: bill_id
              description: The bill id which needs to be fetched
              required: True
              type: string
            - name: field_filter
              in: query
              required: false
              schema:
                $ref: "#/components/schemas/BillGetFieldFilterSchema"
        tags:
            - Bills
        responses:
            200:
                description: The fetched bill.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/ShallowBillResponseSchemaV2"
                        data_non_shallow:
                            $ref: "#/components/schemas/BillSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    last_fetched_version = read_last_version_from_header()

    shallow_response = parsed_request.get("shallow_response")
    use_raw_query = parsed_request.get("use_raw_query")
    exclude_fields = parsed_request.get("skip_fields")
    include_billed_entities = parsed_request.get("include_billed_entities")
    if exclude_fields:
        exclude_fields = {ef.strip() for ef in exclude_fields.split(",")}
    else:
        exclude_fields = set()

    if not include_billed_entities:
        exclude_fields.add('billed_entities')

    try:
        bill_aggregate = query_handler.handle(
            bill_id,
            last_fetched_version=last_fetched_version,
            use_raw_query=use_raw_query,
            exclude_fields=exclude_fields,
            shallow_response=shallow_response,
        )

    except NotModifiedException:
        return ApiResponse.build(
            data=dict(message="Resource Unchanged on Server"), status_code=304
        )

    if shallow_response:
        bill_schema = ShallowBillResponseSchemaV2(exclude=exclude_fields)
    else:
        bill_schema = BillSchema(exclude=exclude_fields)

    response = bill_schema.dump(bill_aggregate)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<bill_id>/invoices', methods=['GET'])
@schema_wrapper_parser(InvoiceGetSchemaV2, param_type=RequestTypes.ARGS)
@inject(query_handler=GetInvoicesForBillV2QueryHandler)
def get_invoices_for_bill_v2(
    query_handler: GetInvoicesForBillV2QueryHandler, bill_id, parsed_request
):
    """Get the invoices for a given bill id
    ---
    operationId: get_invoices_for_bill_v2
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: path
              name: bill_id
              description: The bill id of the bill from which invoices needed to be fetched.
              required: True
              type: string
            - in: query
              name: invoice_filter
              description: filter used to give extra details on invoice
              required: False
              schema:
                $ref: "#/components/schemas/InvoiceGetSchemaV2"
        description: get the invoices for a given bill_id.
        tags:
            - Invoices
        responses:
            200:
                description: The Invoice objects.
                schema:
                    type: object
                    properties:
                        data_shallow:
                            type: array
                            items:
                                $ref: "#/components/schemas/ShallowInvoiceSchema"
                        data_non_shallow:
                            type: array
                            items:
                                $ref: "#/components/schemas/InvoiceSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    invoice_ids = parsed_request.get('invoice_ids')
    shallow_response = parsed_request.get('shallow_response')
    include_preview = parsed_request.get('include_preview')
    only_fields = parsed_request.get('only_fields')

    if invoice_ids:
        invoice_ids = [inv_id.strip() for inv_id in invoice_ids.split(",")]

    invoice_aggregates = query_handler.handle(
        bill_id,
        invoice_ids=invoice_ids,
        shallow_response=shallow_response,
        include_preview=include_preview,
    )
    if only_fields:
        only_fields = [f.strip() for f in only_fields.split(",")]

    if shallow_response:
        invoice_response_schema = ShallowInvoiceSchema(many=True, only=only_fields)
    else:
        invoice_response_schema = InvoiceSchemaWithLineItems(
            many=True, only=only_fields
        )

    response = invoice_response_schema.dump(invoice_aggregates)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<bill_id>/credit-notes', methods=['GET'])
@schema_wrapper_parser(CreditNoteGetSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetCreditNotesQueryHandler)
def get_credit_notes_v2(
    query_handler: GetCreditNotesQueryHandler, bill_id, parsed_request
):
    """Get all credit notes for the given bill_id
    ---
    operationId: get_credit_notes_v2
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: path
              name: bill_id
              description: The bill id of the bill from which invoices needed to be fetched.
              required: True
              type: string
            - in: query
              name: credit_note_filter
              description: filter used to give extra details on invoice
              required: False
              schema:
                $ref: "#/components/schemas/CreditNoteGetSchema"
        description: Get all credit notes for the given bill_id
        tags:
            - Credit Notes
        responses:
            200:
                description: The Credit Note objects.
                schema:
                    type: object
                    properties:
                        data_shallow:
                            type: array
                            items:
                                $ref: "#/components/schemas/ShallowCreditNoteSchema"
                        data_non_shallow:
                            type: array
                            items:
                                $ref: "#/components/schemas/CreditNoteSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    shallow_response = parsed_request.get('shallow_response')
    only_fields = parsed_request.get('only_fields')
    if only_fields:
        only_fields = [f.strip() for f in only_fields.split(",")]

    credit_note_aggregates = query_handler.handle(
        bill_id, shallow_response=shallow_response
    )

    if shallow_response:
        response = ShallowCreditNoteSchema(many=True, only=only_fields).dump(
            credit_note_aggregates
        )
    else:
        response = CreditNoteSchema(many=True, only=only_fields).dump(
            credit_note_aggregates
        )

    return ApiResponse.build(data=response.data, status_code=200)
