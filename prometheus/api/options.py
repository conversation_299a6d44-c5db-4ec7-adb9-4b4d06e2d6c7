# coding=utf-8
"""
Options APIs
"""
import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.services.options_service import OptionsApplicationService
from prometheus.common.api_response import ApiResponse
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers.request import OptionSchema
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)
from ths_common.constants.user_constants import UserType

bp = Blueprint('Options', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/options', methods=['GET'])
@schema_wrapper_parser(OptionSchema, param_type=RequestTypes.ARGS)
@inject(options_app_service=OptionsApplicationService)
def get_option(parsed_request, options_app_service):
    """Get Available Options
    ---
    parameters:
          - name: keys
            in: query
            type: string
            required: false
            default: all
    operationId: get_option
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: true
    get:
        description: Get Options for a given user
        tags:
            - Options
        responses:
            200:
                description: A list of the options with keys and values.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/OptionResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    response = options_app_service.build_options_list(
        user_data, keys=parsed_request.get('keys')
    )

    return ApiResponse.build(data=response, status_code=200)
