import logging

import newrelic
from flask import Blueprint
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import inject
from prometheus.application.booking.command_handlers.create_booking import (
    CreateBookingCommandHandler,
)
from prometheus.application.booking.command_handlers.replace_booking import (
    ReplaceBooking<PERSON>ommandHandler,
)
from prometheus.application.booking.dtos.new_booking_dto import NewBookingDto
from prometheus.application.booking.query_handlers.calculate_cancellation_charge_v2 import (
    CalculateCancellationChargeV2QueryHandler,
)
from prometheus.application.booking.query_handlers.get_booking_v2 import (
    GetBookingV2QueryHandler,
)
from prometheus.application.booking.query_handlers.get_booking_voucher import (
    GetBookingVoucherQueryHandler,
)
from prometheus.application.booking.query_handlers.get_houseview_bookings import (
    GetHouseViewBookingsQueryHandler,
)
from prometheus.application.booking.query_handlers.get_invoices_for_booking import (
    GetInvoicesForBooking<PERSON><PERSON>y<PERSON>and<PERSON>,
)
from prometheus.application.booking.query_handlers.get_pending_web_checkin import (
    GetPendingWebCheckinQueryHandler,
)
from prometheus.application.booking.query_handlers.get_todays_stats import (
    GetTodaysStatsQueryHandler,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.request_parsers import (
    read_last_bill_version_from_header,
    read_last_booking_version_from_header,
    read_user_data_from_request_header,
)
from prometheus.common.serializers import (
    BookingCancellationChargeV2ResponseSchema,
    BookingResponseSchema,
    InvoiceRawSchema,
    InvoiceSchema,
)
from prometheus.common.serializers.request.billing import InvoiceGetSchema
from prometheus.common.serializers.request.booking import (
    BookingCancellationChargeV2Schema,
)
from prometheus.common.serializers.request.booking_v2 import (
    BookingDetailsV2Schema,
    HouseViewBookingQuery,
    NewBookingSchemaV2,
    ReplaceBookingSchemaV2,
)
from prometheus.common.serializers.response.booking_v2 import (
    BookingAllowedActionsResponseSchema,
    HouseViewBookingResponseSchema,
    HouseViewTodaysStatsSchema,
    ShallowBookingResponseSchema,
)
from prometheus.core.api_docs import swag_route
from schema_instances import get_schema_obj
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_and_version_parser,
    schema_wrapper_parser,
)
from shared_kernel.request_parsers import read_user_data_from_request_header
from ths_common.constants.user_constants import UserType

__all__ = ['get_invoices_for_booking_v2', 'create_booking_v2']

from ths_common.exceptions import NotModifiedException

bp = Blueprint('BookingsV2', __name__, url_prefix="/v2")
logger = logging.getLogger(__name__)


def generate_invoice_response(invoice_aggregates, show_raw):
    if show_raw:
        invoice_response_schema = get_schema_obj(InvoiceRawSchema, many=True)
    else:
        invoice_response_schema = get_schema_obj(InvoiceSchema, many=True)
    response = invoice_response_schema.dump(invoice_aggregates)
    return response


@swag_route
@bp.route('/bookings/<booking_id>/invoices', methods=['GET'])
@schema_wrapper_parser(InvoiceGetSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetInvoicesForBookingQueryHandler)
def get_invoices_for_booking_v2(
    query_handler: GetInvoicesForBookingQueryHandler, booking_id, parsed_request
):
    """Get all the invoices for a given booking
    ---
    operationId: get_invoices_for_booking
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: path
              name: invoice_id
              description: The invoice id of the invoice which needs to be fetched.
              required: True
              type: string
            - in: query
              name: invoice_filter
              description: filter used to give extra details on invoice
              required: False
              schema:
                $ref: "#/components/schemas/InvoiceGetSchema"
        description: get the invoices for a given booking_id.
        tags:
            - Invoices
        responses:
            200:
                description: The Invoice objects.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/InvoiceRawSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    newrelic.agent.add_custom_parameter('tenant_id', get_current_tenant_id())
    newrelic.agent.add_custom_parameter('hotel_id', user_data.hotel_id)
    invoice_aggregates = query_handler.handle(
        booking_id,
        include_preview=parsed_request['include_preview'],
        load_charges=parsed_request.get('show_raw'),
    )
    response = generate_invoice_response(
        invoice_aggregates, parsed_request.get('show_raw')
    )
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(NewBookingSchemaV2)
@inject(command_handler=CreateBookingCommandHandler)
def create_booking_v2(command_handler: CreateBookingCommandHandler, parsed_request):
    """Create new booking, along with rate plan inclusions
    ---
    operationId: create_booking_v2
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Create new booking with rate plan inclusions
        tags:
            - Bookings
        parameters:
            - in: body
              name: body
              description: The booking object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/NewBookingSchemaV2"
        responses:
            200:
                description: A shallow detail of the booking object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/BookingResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    new_booking_dto = NewBookingDto.create_new(parsed_request)
    user_data = read_user_data_from_request_header()
    newrelic.agent.add_custom_parameter('tenant_id', get_current_tenant_id())
    newrelic.agent.add_custom_parameter('hotel_id', user_data.hotel_id)
    booking_aggregate = command_handler.handle(new_booking_dto, user_data)

    booking_response_schema = BookingResponseSchema()
    booking_response_schema.context['extra_data'] = dict(
        booking_aggregate=booking_aggregate
    )

    response = booking_response_schema.dump(booking_aggregate.booking)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<booking_id>', methods=['GET'])
@schema_wrapper_parser(BookingDetailsV2Schema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetBookingV2QueryHandler)
def get_booking_details_v2(
    query_handler: GetBookingV2QueryHandler, booking_id, parsed_request
):
    """Get booking details v2 for house-view
    ---
    operationId: get_booking_details_v2
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: path
              name: booking_id
              description: The booking id of the booking.
              required: True
              type: string
            - in: query
              name: booking_detail_filter
              description: filter used to give extra details on booking
              required: False
              schema:
                $ref: "#/components/schemas/BookingDetailsV2Schema"
        description: get booking details for loading loading house-view
        tags:
            - Bookings
        responses:
            200:
                description: Booking details
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/ShallowBookingResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    newrelic.agent.add_custom_parameter('tenant_id', get_current_tenant_id())
    newrelic.agent.add_custom_parameter('hotel_id', user_data.hotel_id)

    last_fetched_bill_version = read_last_bill_version_from_header()
    last_fetched_booking_version = read_last_booking_version_from_header()
    show_bill_summary = parsed_request.get("show_bill_summary")

    try:
        booking_context = query_handler.handle(
            booking_id,
            user_data,
            show_bill_summary=show_bill_summary,
            last_fetched_bill_version=last_fetched_bill_version,
            last_fetched_booking_version=last_fetched_booking_version,
        )
    except NotModifiedException:
        return ApiResponse.build(
            data=dict(message="Resource Unchanged on Server"), status_code=304
        )

    booking_response_schema = ShallowBookingResponseSchema()
    booking_response_schema.context['extra_data'] = dict(
        booking_aggregate=booking_context.booking_aggregate,
        bill_summary=booking_context.bill_summary,
        web_checkin_aggregate=booking_context.web_checkin_aggregate,
    )

    response = booking_response_schema.dump(booking_context.booking_aggregate.booking)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<booking_id>/allowed-actions', methods=['GET'])
@inject(query_handler=GetBookingV2QueryHandler)
def get_booking_allowed_actions(query_handler: GetBookingV2QueryHandler, booking_id):
    """Get booking allowed actions, based on user type
    ---
    operationId: get_booking_allowed_actions
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: path
              name: booking_id
              description: The booking id of the booking.
              required: True
              type: string
        description: Get booking allowed actions, based on user type
        tags:
            - Bookings
        responses:
            200:
                description: Booking details
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/BookingAllowedActionsResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    booking_context = query_handler.handle(booking_id, user_data)
    booking_aggregate = booking_context.booking_aggregate

    booking_response_schema = BookingAllowedActionsResponseSchema()
    booking_response_schema.context['extra_data'] = dict(
        booking_aggregate=booking_aggregate
    )

    response = booking_response_schema.dump(booking_aggregate.booking)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/hotels/<hotel_id>/houseview/bookings', methods=['GET'])
@schema_wrapper_parser(HouseViewBookingQuery, param_type=RequestTypes.ARGS)
@inject(query_handler=GetHouseViewBookingsQueryHandler)
def get_bookings_for_houseview(
    query_handler: GetHouseViewBookingsQueryHandler, hotel_id, parsed_request
):
    """Get Bookings for HouseView Display. This API is a compressed version of search booking API
    ---
    operationId: get_bookings_for_houseview
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: true
              schema:
                $ref: "#/components/schemas/HouseViewBookingQuery"
        description: Get list of bookings which match the search criteria.
        tags:
            - Bookings
        responses:
            200:
                description: A shallow list of detail of the booking objects.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HouseViewQueryResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    newrelic.agent.add_custom_parameter('tenant_id', get_current_tenant_id())
    newrelic.agent.add_custom_parameter('hotel_id', hotel_id)
    from_date = parsed_request.get('from_date')
    to_date = parsed_request.get('to_date')
    limit, offset = parsed_request.get('limit'), parsed_request.get('offset')
    total_count_required = parsed_request.get('total_count_required')
    with_balance_payable = parsed_request.get('with_balance_payable')
    booking_id = parsed_request.get('booking_id')

    (
        house_view_booking_dtos,
        total_count,
    ) = query_handler.handle(
        hotel_id,
        from_date,
        to_date,
        limit,
        offset,
        total_count_required=total_count_required,
        with_balance_payable=with_balance_payable,
        booking_id=booking_id,
    )

    exclude_customers = parsed_request.get('exclude_customers')
    if exclude_customers:
        schema = HouseViewBookingResponseSchema(many=True, exclude=('customers',))
    else:
        schema = HouseViewBookingResponseSchema(many=True)

    booking_serialized = schema.dump(house_view_booking_dtos).data

    search_response = {
        'bookings': booking_serialized,
        'offset': parsed_request['offset'],
        'limit': parsed_request['limit'],
        'total_count': total_count,
    }

    return ApiResponse.build(status_code=200, data=search_response)


@swag_route
@bp.route('/hotels/<hotel_id>/houseview/todays-stats', methods=['GET'])
@inject(query_handler=GetTodaysStatsQueryHandler)
def get_todays_stats(query_handler: GetTodaysStatsQueryHandler, hotel_id):
    """Get todays count for expected checkins, checkouts & active DNRs
    ---
    operationId: get_todays_stats
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              required: true
              description: Hotel for which todays-stats need to be queried.
        tags:
            - Bookings
        responses:
            200:
                description: Todays count for expected checkins, checkouts & active DNRs
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/HouseViewTodaysStatsSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    house_view_todays_stats_dtos = query_handler.handle(hotel_id)
    schema = HouseViewTodaysStatsSchema()
    todays_stats = schema.dump(house_view_todays_stats_dtos)
    return ApiResponse.build(status_code=200, data=todays_stats.data)


@swag_route
@bp.route('/hotels/<hotel_id>/pending-web-checkins', methods=['GET'])
@inject(query_handler=GetPendingWebCheckinQueryHandler)
def get_pending_web_checkins(query_handler: GetPendingWebCheckinQueryHandler, hotel_id):
    """Get Booking pending Web-Checkin.
    ---
    operationId: get_pending_web_checkins
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              required: true
              description: Hotel for which pending web-checkin needs to be fetched.
        tags:
            - Bookings
        responses:
            200:
                description: List of booking_ids for which web-checkin is pending.
                schema:
                    type: object
                    properties:
                        data:
                            pending_web_checkin_booking_ids: List of booking ids pending web-checkin.
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    pending_web_checkins = query_handler.handle(hotel_id)
    return ApiResponse.build(
        status_code=200, data={'pending_web_checkin_booking_ids': pending_web_checkins}
    )


@swag_route
@bp.route('/bookings/<string:booking_id>', methods=['PUT'])
@authorize_write_op
@schema_wrapper_and_version_parser(ReplaceBookingSchemaV2)
@inject(command_handler=ReplaceBookingCommandHandler)
def edit_booking_v2(
    command_handler: ReplaceBookingCommandHandler,
    booking_id,
    resource_version,
    parsed_request,
):
    """Completely replaces the booking having the given booking_id with the new booking as in request.

    This API will not generate a new booking_id.
    ---
    operationId: edit_booking
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    put:
        description: Replace the booking resource for the given booking_id, with new booking
        tags:
            - Bookings
        parameters:
            - in: path
              name: booking_id
              description: The booking id of the booking that needs to be replaced
              required: True
              type: string
            - in: body
              name: body
              description: The booking object data
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/definitions/ReplaceBookingSchemaV2"
        responses:
            200:
                description: A shallow detail of the booking object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/BookingResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"

    """
    new_booking_dto = NewBookingDto.create_new(parsed_request)
    user_data = read_user_data_from_request_header()
    newrelic.agent.add_custom_parameter('tenant_id', get_current_tenant_id())
    newrelic.agent.add_custom_parameter('hotel_id', user_data.hotel_id)
    booking_aggregate = command_handler.handle(
        booking_id, resource_version, new_booking_dto, user_data
    )

    booking_response_schema = BookingResponseSchema()
    booking_response_schema.context['extra_data'] = dict(
        booking_aggregate=booking_aggregate
    )

    response = booking_response_schema.dump(booking_aggregate.booking)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<string:booking_id>/confirmation-voucher', methods=['GET'])
@authorize_write_op
@inject(command_handler=GetBookingVoucherQueryHandler)
def booking_confirmation_voucher(
    command_handler: GetBookingVoucherQueryHandler,
    booking_id,
):
    template_url = command_handler.handle(booking_id)
    return ApiResponse.build(data={"template_url": template_url}, status_code=200)


@swag_route
@bp.route('/bookings/<booking_id>/calculate-cancellation-charges', methods=['POST'])
@schema_wrapper_parser(BookingCancellationChargeV2Schema)
@inject(query_handler=CalculateCancellationChargeV2QueryHandler)
def booking_cancellation_charges(
    query_handler: CalculateCancellationChargeV2QueryHandler, parsed_request, booking_id
):
    """Get cancellation charges for a booking
    ---
    operationId: get_cancellation_charges_for_a_booking
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
             - in: path
               name: booking_id
               description: The booking id for which cancellation charges needs to be calculated
               required: True
               type: string
             - in: body
               name: body
               description: Booking cancellation details
               required: True
               schema:
                  type: object
                  properties:
                     resource_version:
                        type: integer
                     data:
                        $ref: "#/components/schemas/BookingCancellationChargeV2Schema"
        description: Get list of cancellation charges for given booking_id and room_stay_id
        tags:
            - Bookings
        responses:
            200:
                description: Cancellation charges
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/BookingCancellationChargeResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    cancellation_charges = query_handler.handle(
        booking_id=booking_id,
        cancellation_request_payload=parsed_request,
    )
    response = BookingCancellationChargeV2ResponseSchema().dump(cancellation_charges)
    return ApiResponse.build(status_code=200, data=response.data)
