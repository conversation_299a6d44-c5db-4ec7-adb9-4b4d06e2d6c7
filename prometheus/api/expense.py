"""
booking API
"""
import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.booking.command_handlers.add_expense import (
    AddExpenseCommandHandler,
)
from prometheus.application.booking.command_handlers.update_expense import (
    UpdateExpenseCommandHandler,
)
from prometheus.application.booking.query_handlers.get_expense import (
    GetExpenseQueryHandler,
)
from prometheus.application.booking.query_handlers.get_expense_by_id import (
    GetExpenseByIdQueryHandler,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers import ExpenseSchema
from prometheus.common.serializers.request.expense import (
    EditExpenseSchema,
    ExpensePaginationSchema,
    NewExpenseSchema,
)
from prometheus.core.api_docs import swag_route
from prometheus.domain.booking.dtos.edit_expense_data import EditExpenseData
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_and_version_parser,
    schema_wrapper_parser,
)
from ths_common.value_objects import NotAssigned, PriceData

bp = Blueprint('Expenses', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/bookings/<booking_id>/expenses', methods=['POST'])
@authorize_write_op
@schema_wrapper_and_version_parser(NewExpenseSchema)
@inject(command_handler=AddExpenseCommandHandler)
def create_booking_expense(
    command_handler: AddExpenseCommandHandler,
    booking_id,
    resource_version,
    parsed_request,
):
    """Create an Expense on a given booking
    ---
    operationId: create_booking_expense
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: create an expense on a booking
        tags:
            - Expenses
        parameters:
            - in: path
              name: booking_id
              description: The booking id of the booking which action need to be created
              required: True
              type: string
            - in: body
              name: body
              description: The action object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/NewExpenseSchema"
        responses:
            200:
                description: The schema of the booking expense created.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/ExpenseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """

    user_data = read_user_data_from_request_header()
    expense, resource_version = command_handler.handle(
        booking_id, resource_version, parsed_request, parsed_request['price'], user_data
    )

    expense_response_schema = ExpenseSchema()
    response = expense_response_schema.dump(expense)

    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=resource_version
    )


@swag_route
@bp.route('/bookings/<booking_id>/expenses', methods=['GET'])
@schema_wrapper_parser(ExpensePaginationSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetExpenseQueryHandler)
def get_booking_expenses(
    query_handler: GetExpenseQueryHandler, booking_id, parsed_request
):
    """Get all the expenses on a given booking
    ---
    operationId: get_booking_expenses
    parameters:
        - in: path
          name: booking_id
          description: The booking id of the booking for which expenses need to be fetched
          required: True
          type: string
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: get all the expenses for a given booking
        parameters:
            - in: path
              name: booking_id
              description: The booking id of the booking for which expenses need to be fetched
              required: True
              type: string
            - name: pagination_filter
              in: query
              required: false
              schema: ExpensePaginationSchema
        tags:
            - Expenses
        responses:
            200:
                description: The list of booking Expenses.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/ExpenseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    expense_id_gt, limit = None, None
    if parsed_request:
        expense_id_gt = parsed_request.get("expense_id_gt")
        limit = parsed_request.get("limit")

        expense_id_gt = expense_id_gt if expense_id_gt is not None else None
        limit = limit if limit is not None else None

    expenses, booking_version, max_expense_id = query_handler.handle(
        booking_id, expense_id_gt=expense_id_gt, limit=limit
    )
    expense_response_schema = ExpenseSchema()
    response = expense_response_schema.dump(expenses, many=True)

    return ApiResponse.build(
        data=response.data,
        status_code=200,
        resource_version=booking_version,
        meta=dict(max_expense_id=max_expense_id) if max_expense_id else None,
    )


@swag_route
@bp.route('/bookings/<booking_id>/expenses/<int:expense_id>', methods=['GET'])
@inject(handler=GetExpenseByIdQueryHandler)
def get_booking_expense(handler: GetExpenseByIdQueryHandler, booking_id, expense_id):
    """Get the specific action on a given booking
    ---
    operationId: get_booking_expense
    parameters:
        - in: path
          name: booking_id
          description: The booking id of the booking of which the expense is part of.
          required: True
          type: string
        - in: path
          name: expense_id
          description: The expense id whose details need to be fetched
          required: True
          type: integer
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: get the action on a given booking
        tags:
            - Expenses
        responses:
            200:
                description: The specific expense.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/ExpenseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    expense, booking_version = handler.handle(booking_id, expense_id)
    expense_response_schema = ExpenseSchema()
    response = expense_response_schema.dump(expense)

    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=booking_version
    )


@swag_route
@bp.route('/bookings/<booking_id>/expenses/<int:expense_id>', methods=['PATCH'])
@authorize_write_op
@schema_wrapper_and_version_parser(EditExpenseSchema)
@inject(command_handler=UpdateExpenseCommandHandler)
def edit_booking_expense(
    command_handler: UpdateExpenseCommandHandler,
    booking_id,
    expense_id,
    resource_version,
    parsed_request,
):
    """Update an Expense on a given booking
    ---
    operationId: edit_booking_expense
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    patch:
        description: update an expense on a booking
        tags:
            - Expenses
        parameters:
            - in: path
              name: booking_id
              description: The booking id of the booking which expense need to be edited
              required: True
              type: string
            - in: path
              name: expense_id
              description: The expense id whose details need to be edited
              required: True
              type: string
            - in: body
              name: body
              description: The expense object which needs to be edited
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/EditExpenseSchema"
        responses:
            200:
                description: The schema of the edited expense.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/ExpenseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    data = parsed_request
    price_data = PriceData(
        pretax_amount=data.get('pretax_amount', NotAssigned),
        posttax_amount=data.get('posttax_amount', NotAssigned),
        bill_to_type=data.get('bill_to_type', NotAssigned),
        type=data.get('type', NotAssigned),
        charge_to=data.get('assigned_to', NotAssigned),
        billing_instructions=data.get('billing_instructions', NotAssigned),
    )
    edit_expense_data = EditExpenseData(
        expense_id=expense_id,
        comments=parsed_request.get('comments', NotAssigned),
        price_data=price_data,
    )
    expense, booking_version = command_handler.handle(
        booking_id, expense_id, edit_expense_data, resource_version, user_data
    )
    expense_response_schema = ExpenseSchema()
    response = expense_response_schema.dump(expense)

    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=booking_version
    )
