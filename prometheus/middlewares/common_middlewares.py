# coding=utf-8
"""
Middlewares
"""
import logging
import re

import flask
import sentry_sdk
from flask import g
from marshmallow import ValidationError
from treebo_commons.exceptions import NaiveDatetimeUnsupportedException
from treebo_commons.request_tracing.context import (
    get_current_request_id,
    get_current_tenant_id,
)
from werkzeug.exceptions import NotFound

from prometheus.common.api_response import ApiResponse
from prometheus.core.domainbus.bus import get_domain_bus
from prometheus.core.globals import crs_context
from prometheus.domain.booking.exceptions import (
    BookingInvarianceCheckError,
    InvalidActionError,
    InvalidStayDatesError,
    RoomStayAdditionError,
    UnsupportedOccupancyError,
)
from prometheus.domain.domain_events.domain_event_registry import clear_all_events
from prometheus.domain.inventory.exceptions import InventoryException
from ths_common.exceptions import (
    AggregateNotFound,
    ApiValidationException,
    AuthorizationError,
    BookingIdCollision,
    BookingReferenceIdCollision,
    CRSException,
    DatabaseError,
    DatabaseLockError,
    DomainEventMergerException,
    DownstreamSystemFailure,
    InternalServerException,
    InvalidOperationError,
    JobNotRegistered,
    MissingFactsException,
    OutdatedVersion,
    PolicyAuthException,
    PrimaryKeyCollision,
    ResourceNotFound,
    UniqueIdCollision,
    UserTypeHeaderInformationMissing,
    ValidationException,
)

logger = logging.getLogger(__name__)


def before_request(request_headers):
    sentry_sdk.set_tag("tenant_id", request_headers.get("X-Tenant-Id"))
    sentry_sdk.set_tag("hotel_id", request_headers.get("X-Hotel-Id"))


def after_request(response):
    clear_thread_local()
    return response


def clear_thread_local():
    from prometheus import crs_context

    crs_context.clear()
    clear_all_events()


def get_http_status_code_from_exception(exception: CRSException) -> int:
    if isinstance(exception, AuthorizationError) or isinstance(
        exception, PolicyAuthException
    ):
        return 403

    resource_missing_exceptions = [AggregateNotFound, ResourceNotFound]
    for error_cls in resource_missing_exceptions:
        if isinstance(exception, error_cls):
            return 404

    bad_request_exceptions = [
        ApiValidationException,
        ValidationException,
        InvalidOperationError,
        InvalidActionError,
        BookingInvarianceCheckError,
        UnsupportedOccupancyError,
        InvalidStayDatesError,
        RoomStayAdditionError,
        InventoryException,
        NaiveDatetimeUnsupportedException,
    ]

    for error_cls in bad_request_exceptions:
        if isinstance(exception, error_cls):
            return 400

    resource_conflict_exceptions = [
        OutdatedVersion,
        DatabaseLockError,
        BookingReferenceIdCollision,
    ]

    for error_cls in resource_conflict_exceptions:
        if isinstance(exception, error_cls):
            return 409

    internal_server_errors = [
        InternalServerException,
        DatabaseError,
        JobNotRegistered,
        MissingFactsException,
        DownstreamSystemFailure,
        PrimaryKeyCollision,
        BookingIdCollision,
        DomainEventMergerException,
        UniqueIdCollision,
    ]

    for error_cls in internal_server_errors:
        if isinstance(exception, error_cls):
            return 500

    if isinstance(exception, UserTypeHeaderInformationMissing):
        return 401

    if isinstance(exception, CRSException):
        return 400

    return 500


def need_error_logging(error):
    if isinstance(error, NotFound):
        return False
    return True


def exception_handler(error, from_consumer=False):
    """
    Exception handler
    :param error:
    :param from_consumer:
    :return:
    """

    if isinstance(error, ApiValidationException):
        status_code = 400
        error_messages = error.error_messages
        error_code = error.code(app_name="crs")
        errors = []
        for error_message in error_messages:
            error = dict(
                code=error_code,
                message=error_message["error"],
                extra_payload=dict(field=error_message["field"]),
                developer_message=None,
                request_id=get_current_request_id() if not from_consumer else None,
            )
            errors.append(error)
        response = ApiResponse.build(errors=errors, status_code=status_code)

    elif isinstance(error, CRSException):
        status_code = get_http_status_code_from_exception(error)
        error_code = error.code(app_name="crs")
        error = dict(
            code=error_code,
            message=error.message,
            developer_message=error.description,
            extra_payload=error.extra_payload,
            request_id=get_current_request_id() if not from_consumer else None,
        )
        response = ApiResponse.build(errors=[error], status_code=status_code)
    else:
        # populate status code
        status_code = 500
        if getattr(error, "status_code", None):
            status_code = error.status_code
        if getattr(error, "code", None):
            status_code = error.code

        if isinstance(error, ValidationError):
            status_code = 400

        if not re.search(r'^[1-5]\d{2}$', str(status_code)):
            status_code = 500

        error_code = None
        # populate error dict
        error_dict = dict(code=status_code)
        # TODO:: causing JSON serializer error for unknown types. Need to find a cleaner solution for this.
        # error_dict['extra_payload'] = error.args if hasattr(error, 'args') else None
        error_dict['extra_payload'] = dict()
        error_dict['message'] = (
            error.message if hasattr(error, 'message') else 'Exception occurred.'
        )
        error_dict['developer_message'] = (
            error.description if hasattr(error, 'description') else str(error)
        )
        error_dict['request_id'] = (
            get_current_request_id() if not from_consumer else None
        )
        error_dict['tenant_id'] = get_current_tenant_id()
        response = ApiResponse.build(errors=[error_dict], status_code=status_code)

    if need_error_logging(error):
        if isinstance(error, Exception) and status_code >= 500:
            sentry_sdk.capture_exception(error)

        if not from_consumer:
            request = flask.request
            request_url = request.url
            request_headers = dict(request.headers)

            if request.is_json:
                request_data = (
                    request.json
                    if request.get_json(silent=True)
                    else request.get_data(as_text=True)
                )
            else:
                request_data = request.get_data(as_text=True)

            hotel_context = crs_context.get_hotel_context()
            hotel_id = hotel_context.hotel_id if hotel_context else None
            tenant_id = get_current_tenant_id()

            logger.exception(
                "Exception in api: %s. Request Payload: %s",
                error,
                request_data,
                extra=dict(
                    error_code=error_code,
                    status_code=status_code,
                    request_url=request_url,
                    request_headers=request_headers,
                    request_data=request_data,
                    request_method=request.method,
                    hotel_id=hotel_id,
                    tenant_id=tenant_id,
                ),
            )
        else:
            tenant_id = get_current_tenant_id()
            logger.exception(
                "Exception in consumer: %s",
                error,
                extra=dict(error_code=error_code, tenant_id=tenant_id),
            )

    return response


def filter_sentry_events(event, hint):
    if 'exc_info' in hint:
        exc_type, error, tb = hint['exc_info']

        status_code = 500
        if isinstance(error, ApiValidationException):
            status_code = 400

        elif isinstance(error, CRSException):
            status_code = get_http_status_code_from_exception(error)
        else:
            # populate status code
            if getattr(error, "status_code", None):
                status_code = error.status_code
            if getattr(error, "code", None):
                status_code = error.code
            if isinstance(error, ValidationError):
                status_code = 400
            if not re.search(r'^[1-5]\d{2}$', str(status_code)):
                status_code = 500

        if isinstance(error, Exception) and status_code >= 500:
            return event

    # Discard sentry event
    return None


def setup_domain_bus():
    from prometheus.application.booking.domain_event_handlers.booking_handlers import (
        register_domain_bus_event_handlers,
    )

    register_domain_bus_event_handlers(get_domain_bus())


def cleanup_domain_bus(response):
    g.pop('domain_bus', None)
    return response
