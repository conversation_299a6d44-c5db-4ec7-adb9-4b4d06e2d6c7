import json

from prometheus.integration_tests.config.common_config import EARLY_CHECKOUT
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import del_none, increment_date, sanitize_blank, return_date


class CalculateCancellationCharges:
    def __init__(self, sheet_name, test_case_id, invoice_group_id):
        self.sheet_name = sheet_name
        self.test_case_id = test_case_id
        self.invoice_group_id = invoice_group_id

    def calculate_cancellation_charges_request(self):
        test_case_data = excel_utils.get_test_case_data(self.sheet_name, self.test_case_id)[0]
        if self.test_case_id == 'CancellationCharge_11':
            cancellation_datetime = str(return_date(sanitize_blank(test_case_data['cancellation_datetime'])))
        else:
            cancellation_datetime = None if sanitize_blank(test_case_data['cancellation_datetime']) is None \
                else str(increment_date(sanitize_blank(test_case_data['cancellation_datetime'])))
        action = str(test_case_data['action'])
        data = {'cancellation_datetime': cancellation_datetime, 'action': action}
        if action == EARLY_CHECKOUT:
            data['action_payload'] = {'invoice_group_id': self.invoice_group_id}
        return json.dumps(del_none({'data': data}))
