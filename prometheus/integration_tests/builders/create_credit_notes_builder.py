import json
from prometheus.integration_tests.utilities.common_utils import sanitize_blank
from prometheus.integration_tests.config import common_config, sheet_names
from prometheus.integration_tests.utilities import excel_utils


class CreateCreditNotes:
    def __init__(self, sheet_name, test_case_id, invoicepreviews):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = Data(test_data[0], test_case_id, invoicepreviews).__dict__


class Data(object):
    def __init__(self, test_data, test_case_id, invoicepreviews):
        if sanitize_blank(test_data['credit_note_line_items']):
            credit_note_line_item_data = excel_utils.get_test_case_data(sheet_names.credit_note_line_item_sheet_name,
                                                                        test_data['credit_note_line_items'])
            self.credit_note_line_items = []
            for line_item_data in credit_note_line_item_data:
                item_data = CreditNoteLineItem(line_item_data, invoicepreviews).__dict__
                self.credit_note_line_items.append(item_data)
        self.generate_hotel_credit_note = sanitize_blank(test_data['generate_hotel_credit_note'])
        if sanitize_blank(test_data['invoice_versions']):
            self.invoice_versions = []
            # self.invoice_versions = json.loads(test_data['invoice_versions'])
            # for invoice_number in self.invoice_versions:
            #     invoice_number['invoice_id'] = invoicepreviews[0]['invoice_id']
            #     invoice_number['version'] = invoicepreviews[0]['version']+1
            print("3"*1000)
            print(len(invoicepreviews))
            for invoice_number in invoicepreviews:
                invoice_data = InvoiceVersion(invoice_number, test_case_id).__dict__
                self.invoice_versions.append(invoice_data)
                print(invoice_number['invoice_id'])
                print(invoice_number['version'])
            #     self.invoice_versions[i]['invoice_id'] = invoice_number['invoice_id']
            #     self.invoice_versions[i]['version'] = invoice_number['version']+1
            #


class InvoiceVersion(object):
    def __init__(self, invoice_data, test_case_id):
        if 'CreditNote_09' in test_case_id:
            self.invoice_id = 'INV-***********-000'
        else:
            self.invoice_id = invoice_data['invoice_id']
        self.version = invoice_data['version']+1


class CreditNoteLineItem(object):
    def __init__(self, line_item_data, invoicepreviews):
        self.invoice_charge_id = sanitize_blank(line_item_data['invoice_charge_id'])
        self.invoice_id = invoicepreviews[0]['invoice_id']
            # sanitize_blank(line_item_data['invoice_id'])
        self.posttax_amount = sanitize_blank(line_item_data['posttax_amount'])
