import json
from prometheus.integration_tests.builders.common_request_builder import get_resource_version
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data, del_none


class EditRatePlanCharge(object):
    def __init__(self, sheet_name, test_case_id, booking_id):
        self.sheet_name = sheet_name
        self.test_case_id = test_case_id
        self.booking_id = booking_id

    def edit_rate_plan_charge_request(self):
        data = []
        edit_rate_plan_charge_data_from_sheet = excel_utils.get_test_case_data(self.sheet_name, self.test_case_id)
        for charge_data in edit_rate_plan_charge_data_from_sheet:
            if sanitize_test_data(charge_data['billing_instructions']) and \
                    charge_data['billing_instructions'] != 'NULL':
                billing_instructions = json.loads(charge_data['billing_instructions'])
            else:
                billing_instructions = sanitize_test_data(charge_data['billing_instructions'])
            charge_id = sanitize_test_data(charge_data['charge_id'])
            if sanitize_test_data(charge_data['charge_to']) and charge_data['charge_to'] != 'NULL':
                charge_to = json.loads(charge_data['charge_to'])
            else:
                charge_to = sanitize_test_data(charge_data['charge_to'])
            if sanitize_test_data(charge_data['inclusion_charges']) and charge_data['inclusion_charges'] != 'NULL':
                inclusion_charges = json.loads(charge_data['inclusion_charges'])
            else:
                inclusion_charges = sanitize_test_data(charge_data['inclusion_charges'])
            posttax_amount = sanitize_test_data(charge_data['posttax_amount'])
            pretax_amount = sanitize_test_data(charge_data['pretax_amount'])
            data.append({'billing_instructions': billing_instructions, 'charge_id': charge_id, 'charge_to': charge_to,
                         'inclusion_charges': inclusion_charges, 'posttax_amount': posttax_amount,
                         'pretax_amount': pretax_amount})
        edit_rate_plan_charge_request = {'data': data, 'resource_version': get_resource_version(self.booking_id)}
        return json.dumps(del_none(edit_rate_plan_charge_request))
