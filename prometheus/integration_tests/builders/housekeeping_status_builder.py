from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_blank


class EditHousekeepingStatus(object):
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = Data(test_data[0]).__dict__


class Data(object):
    def __init__(self, test_data):
        self.housekeeper_id = sanitize_blank(test_data['housekeeper_id'])
        self.housekeeping_status = sanitize_blank(test_data['housekeeping_status'])
        self.remarks = sanitize_blank(test_data['remarks'])
