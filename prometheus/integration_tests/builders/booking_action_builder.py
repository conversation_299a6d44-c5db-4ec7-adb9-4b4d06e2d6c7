import json
from prometheus.integration_tests.builders import common_request_builder
from prometheus.integration_tests.builders.common_request_builder import booking_repo
from prometheus.integration_tests.config import common_config, sheet_names
from prometheus.integration_tests.requests.inventory_requests import InventoryRequests
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import increment_date, sanitize_blank, get_room_type_id, \
    sanitize_test_data


class BookingActionBuilder(object):
    def __init__(self, sheet_name, test_case_id, client, booking_id=None, invoice_group_id=None,
                 inventory_request_required=False, hotel_id=None, room_id=None):

        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        if test_case_id == "checkinPost_28":
            self.resource_version = 1000
        elif test_case_id == "checkinPost_29":
            self.resource_version = None
        else:
            self.resource_version = booking_repo().load(booking_id).booking.version
        self.data = BookingActionBuilder.Data(test_data, client, test_case_id, booking_id, invoice_group_id,
                                              inventory_request_required, hotel_id, room_id).__dict__

    class Data(object):
        def __init__(self, test_data, client, test_case_id, booking_id, invoice_group_id=None,
                     inventory_request_required=False, hotel_id=None, room_id=None):
            self.action_type = sanitize_blank(test_data[0]['action_type'])
            self.payload = BookingActionBuilder.Data.Payload(test_data, client, test_case_id, booking_id,
                                                             invoice_group_id, inventory_request_required,
                                                             hotel_id, room_id).__dict__

        class Payload(object):
            def __init__(self, booking_action_data, client, test_case_id, booking_id, invoice_group_id=None,
                         inventory_request_required=False, hotel_id=None, room_id=None):
                if booking_action_data[0]['action_type'] == 'checkin':
                    self.checkin = BookingActionBuilder.Data.Payload.CheckIn(booking_action_data, client, test_case_id,
                                                                             booking_id, hotel_id, room_id).__dict__
                elif booking_action_data[0]['action_type'] == 'checkout':
                    self.checkout = BookingActionBuilder.Data.Payload.CheckOut(booking_action_data[0],
                                                                               invoice_group_id).__dict__
                elif booking_action_data[0]['action_type'] == 'cancel':
                    self.cancel = BookingActionBuilder.Data.Payload.Cancel(booking_action_data[0]).__dict__
                elif booking_action_data[0]['action_type'] == 'noshow':
                    self.noshow = BookingActionBuilder.Data.Payload.NoShow(booking_action_data[0]).__dict__

            class CheckIn(object):
                def __init__(self, checkin_data, client, test_case_id, booking_id, hotel_id, room_id=None):
                    if test_case_id == "checkinPost_25":
                        self.room_stays = None
                    else:
                        room_stay_data = excel_utils.get_test_case_data(sheet_names.add_room_stay_sheet_name,
                                                                        checkin_data[0]['room_stays'])
                        self.room_stays = []
                        for room_data in room_stay_data:
                            self.room_stays.append(
                                BookingActionBuilder.Data.Payload.CheckIn.RoomStay(room_data, client, test_case_id,
                                                                                   booking_id, hotel_id,
                                                                                   room_id).__dict__)

                class RoomStay(object):
                    def __init__(self, room_stay_data, client, test_case_id, booking_id, hotel_id, room_id=None):
                        self.room_stay_id = room_stay_data['room_stay_id_forAction']
                        if test_case_id == "checkinPost_23":
                            self.guest_stays = None
                        else:
                            guest_stay_data = excel_utils.get_test_case_data(sheet_names.add_guest_stay_sheet_name,
                                                                             room_stay_data['GuestStay'])
                            self.guest_stays = []
                            for guest_data in guest_stay_data:
                                self.guest_stays.append(
                                    BookingActionBuilder.Data.Payload.CheckIn.RoomStay.GuestStay(guest_data,
                                                                                                 test_case_id).__dict__)
                        if room_stay_data['room_type_id'] == "null":
                            self.room_allocation = None
                        else:
                            self.room_allocation = BookingActionBuilder.Data.Payload.CheckIn.RoomStay.RoomAllocation(
                                room_stay_data, client, test_case_id, booking_id, hotel_id, room_id).__dict__

                    class GuestStay(object):
                        def __init__(self, guest_stay_data, test_case_id):
                            self.checkin_date = increment_date(int(guest_stay_data['checkin_date']),
                                                               common_config.CHECK_IN_TIME_ZONE) if guest_stay_data[
                                'checkin_date'] else None
                            if test_case_id == "checkinPost_31":
                                self.guest_id = "100000000"
                            else:
                                self.guest_id = sanitize_blank(guest_stay_data['guest_id'])
                            self.guest_stay_id = sanitize_blank(guest_stay_data['guest_stay_id'])
                            if guest_stay_data['guest_details'] == "existing":
                                self.guest = None
                            elif sanitize_blank(guest_stay_data['guest_details']) != "wrong":
                                guest_data = excel_utils.get_test_case_data(sheet_names.customer_data_sheet_name,
                                                                            guest_stay_data['guest_details'])
                                self.guest = common_request_builder.Customer(guest_data[0]).__dict__

                    class RoomAllocation(object):

                        def __init__(self, room_allocation_data, client, test_case_id, booking_id, hotel_id,
                                     room_id=None):
                            self.checkin_date = increment_date(int(room_allocation_data['checkin_date']),
                                                               common_config.CHECK_IN_TIME_ZONE)
                            if test_case_id == "checkinPost_44":
                                # room_response = BookingRequests().get_booking_actions(client, booking_id)
                                index = int(room_allocation_data['room_stay_id_forAction'])
                                # self.room_id = \
                                #     room_response['data'][0]['payload']['checkin']['room_stays'][index - 1][
                                #         'room_allocation']['room_id']
                            elif test_case_id == "checkinPost_15":
                                self.room_id = 1000
                            elif room_id:
                                self.room_id = room_id
                            else:
                                available_room_ids = InventoryRequests().get_available_room_ids(
                                    client, room_allocation_data['checkin_date'], room_allocation_data['checkout_date'],
                                    get_room_type_id(room_allocation_data['room_type_id']), 200, True, hotel_id)
                                self.room_id = available_room_ids[int(room_allocation_data['room_number']) - 1]

            class CheckOut(object):
                def __init__(self, checkout_data, invoice_group_id):
                    self.invoice_group_id = invoice_group_id
                    self.should_generate_invoice = bool(checkout_data['should_generate_invoice'])

            class Cancel(object):
                def __init__(self, cancel_data):
                    self.cancellation_reason = sanitize_blank(cancel_data['cancellation_reason'])
                    if sanitize_blank(cancel_data['room_stays']):
                        self.room_stays = []
                        guest_stay_id_with_room = cancel_data['guest_stay_ids'].split('#')
                        room_stays_data = excel_utils.get_test_case_data(sheet_names.add_room_stay_sheet_name,
                                                                         cancel_data['room_stays'])
                        for guest_stay_id, room_stay_data in zip(guest_stay_id_with_room, room_stays_data):
                            guest_stay_ids = (guest_stay_id.split(':')[1]).split(',') if (
                                guest_stay_id.split(':')[1]) else []
                            room_stay_id = int(room_stay_data['room_stay_id_forAction'])
                            price_test_data = excel_utils.get_test_case_data(sheet_names.add_prices_sheet_name,
                                                                             room_stay_data['Prices'])
                            prices = []
                            for price_data in price_test_data:
                                price = BookingActionBuilder.Data.Payload.Cancel.Prices(price_data).__dict__
                                prices.append(price)
                            self.room_stays.append(
                                {'room_stay_id': room_stay_id, 'prices': prices, 'guest_stay_ids': guest_stay_ids})

                class Prices(object):
                    def __init__(self, prices_data):
                        self.applicable_date = increment_date(int(prices_data['applicable_date']),
                                                              common_config.CHECK_IN_TIME_ZONE)
                        self.bill_to_type = sanitize_blank(prices_data['bill_to_type'])
                        self.type = sanitize_blank(prices_data['type'])
                        self.pretax_amount = sanitize_blank(prices_data['pretax_amount'])

            class NoShow(object):
                def __init__(self, no_show_data):
                    if sanitize_blank(no_show_data['noshow_reason']):
                        self.noshow_reason = sanitize_blank(no_show_data['noshow_reason'])
                    if sanitize_blank(no_show_data['room_stays']):
                        self.room_stays = []
                        if sanitize_test_data(no_show_data['crs_guest_stay_ids']):
                            room_stay_ids = json.loads(no_show_data['crs_guest_stay_ids'])
                            for room_stay_id_data in room_stay_ids:
                                self.room_stays.append(BookingActionBuilder.Data.Payload.NoShow.RoomStay
                                                       (room_stay_id_data).__dict__)

                class RoomStay:
                    def __init__(self, room_stay_id_data):
                        self.room_stay_id = int(room_stay_id_data['room_stay_id'])
                        self.guest_stay_ids = []
                        if sanitize_test_data(room_stay_id_data['guest_stay_ids']):
                            guest_ids = room_stay_id_data['guest_stay_ids'].split(',')
                            for guest_id in guest_ids:
                                self.guest_stay_ids.append(int(guest_id))

