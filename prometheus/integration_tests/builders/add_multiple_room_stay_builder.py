from prometheus.integration_tests.builders.common_request_builder import *
from prometheus.integration_tests.builders.common_request_builder import booking_repo
from prometheus.integration_tests.config.sheet_names import *
from prometheus.integration_tests.utilities import excel_utils


class AddMultipleRoomStay:
    def __init__(self, test_case_id, booking_id, enable_rate_plan, is_inclusion_added):
        self.data = Data(test_case_id, enable_rate_plan, is_inclusion_added).__dict__
        self.resource_version = booking_repo().load(booking_id).booking.version


class Data(object):
    def __init__(self, test_case_id, enable_rate_plan, is_inclusion_added):
        if test_case_id == 'AddMultipleRoom_60':
            self.room_stays = 'NULL_KEY'
        elif test_case_id == 'AddMultipleRoom_61':
            self.room_stays = ''
        else:
            self.room_stays = []
            multiple_room_data = excel_utils.get_test_case_data(add_multiple_room_stay_sheet_name, test_case_id)
            for room_data in multiple_room_data:
                room_count = RoomStay(room_data, enable_rate_plan, is_inclusion_added).__dict__
                self.room_stays.append(room_count)
