import json

from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import del_none, sanitize_test_data


class GetRefundModeRequest:
    def __init__(self, sheet_name, test_case_id):
        self.test_case_id = test_case_id
        self.sheet_name = sheet_name

    def get_refund_mode_request(self):
        get_refund_mode_data_from_sheet = excel_utils.get_test_case_data(self.sheet_name, self.test_case_id)[0]
        data = json.loads(get_refund_mode_data_from_sheet['refund_mode_api_data'])
        return json.dumps(del_none({'data': data}))
