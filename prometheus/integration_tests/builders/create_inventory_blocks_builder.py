import json
from dateutil.parser import parse
from prometheus.integration_tests.config import common_config
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data, increment_date


class CreateInventoryBlockBuilder:
    def __init__(self, sheet_name, test_case_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = {}

        if sanitize_test_data(test_data.get('inventory_blocks')):
            inventory_block_request = json.loads(test_data['inventory_blocks'])

            inventory_blocks = []
            for block in inventory_block_request:
                inventory_blocks.append({
                    "start_date": str(parse(increment_date(int(block["start_date"]),
                                                           common_config.CHECK_IN_TIME_ZONE)).date()),
                    "end_date": str(parse(increment_date(int(block["end_date"]),
                                                         common_config.CHECKOUT_TIME_ZONE)).date()),
                    "room_type_id": block.get("room_type_id")
                })

            self.data = {
                "action": "block",
                "block_ids": ["Block Inventory"],
                "booking_id": booking_id,
                "inventory_blocks": inventory_blocks
            }
