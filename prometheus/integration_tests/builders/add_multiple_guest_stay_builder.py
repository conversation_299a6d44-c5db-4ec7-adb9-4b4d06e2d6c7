from prometheus.integration_tests.builders.common_request_builder import *
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data


class AddMultipleGuestStay:
    def __init__(self, sheet_name, test_case_id, booking_id, enable_rate_plan, is_inclusion_added):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = Data(test_data, test_case_id, enable_rate_plan, is_inclusion_added).__dict__
        self.resource_version = booking_repo().load(booking_id).booking.version


class Data(object):
    def __init__(self, test_data, test_case_id, enable_rate_plan, is_inclusion_added):
        multiple_guest_data = excel_utils.get_test_case_data(sheet_names.add_guest_stay_sheet_name_api, test_case_id)
        self.guest_stays = [GuestStay(guest_data).__dict__ for guest_data in multiple_guest_data]

        if sanitize_test_data(test_data['prices']) and test_data['prices'] != 'NULL':
            price_test_data = excel_utils.get_test_case_data(sheet_names.add_prices_sheet_name, test_data['prices'])
            self.new_room_stay_prices = [Prices(price_data, enable_rate_plan).__dict__ for price_data in
                                         price_test_data]
        else:
            self.new_room_stay_prices = sanitize_test_data(test_data['prices'])

        if sanitize_test_data(test_data['rate_plan_inclusions']) and is_inclusion_added:
            rate_plan_inclusions_data = excel_utils.get_test_case_data(sheet_names.
                                                                       rate_plan_inclusions_v2_booking_sheet_name,
                                                                       test_data['rate_plan_inclusions'])
            self.rate_plan_inclusions = [RatePlanInclusion(rate_plan_inclusion_data).__dict__ for
                                         rate_plan_inclusion_data in rate_plan_inclusions_data]
