from prometheus.integration_tests.builders.common_request_builder import bill_repo
from prometheus.integration_tests.utilities.common_utils import sanitize_blank


class TransferChargeBuilder(object):
    def __init__(self, destination_booking_id, source_bill_id, room_stay_id=None, is_bulk_transfer=False,
                 charge_ids=None, test_case_id=None):
        self.data = Data(destination_booking_id, room_stay_id, is_bulk_transfer, charge_ids).__dict__
        self.resource_version = bill_repo().load(source_bill_id).bill.version


class Data(object):
    def __init__(self, booking_id, room_stay_id, is_bulk_transfer, charge_ids):
        if booking_id:
            self.booking_id = sanitize_blank(booking_id)
        if is_bulk_transfer:
            self.charge_ids = charge_ids
        if room_stay_id:
            self.room_stay_id = sanitize_blank(room_stay_id)
