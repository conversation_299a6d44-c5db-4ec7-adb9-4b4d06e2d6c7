from prometheus.integration_tests.builders.common_request_builder import addon_repo
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_blank, return_date
from prometheus.integration_tests.builders.common_request_builder import set_hotel_context


class AddOnV2(object):
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = Data(test_data[0]).__dict__


class EditOnV2(object):
    def __init__(self, sheet_name, test_case_id, addon_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = Data(test_data[0]).__dict__
        set_hotel_context(booking_id)
        self.resource_version = addon_repo().load(addon_id).addon.version


class Data(object):
    def __init__(self, test_data):
        self.added_by = sanitize_blank(test_data['added_by'])
        self.bill_to_type = sanitize_blank(test_data['bill_to_type'])
        self.charge_type = sanitize_blank(test_data['charge_type'])
        if sanitize_blank(test_data['end_date']) is not None:
            self.end_date = str(return_date(int(test_data['end_date'])))
        self.end_relative = sanitize_blank(test_data['end_relative'])
        self.expense_item_id = sanitize_blank(test_data['expense_item_id'])
        self.name = sanitize_blank(test_data['name'])
        self.posttax_price = sanitize_blank(test_data['posttax_price'])
        self.pretax_price = sanitize_blank(test_data['pretax_price'])
        self.quantity = sanitize_blank(test_data['quantity'])
        self.room_stay_id = sanitize_blank(test_data['room_stay_id'])
        if test_data['sku_id']:
            self.sku_id = sanitize_blank(test_data['sku_id'])
        if sanitize_blank(test_data['start_date']) is not None:
            self.start_date = str(return_date(int(test_data['start_date'])))
        self.start_relative = sanitize_blank(test_data['start_relative'])
        if sanitize_blank(test_data['is_rate_plan_addon']):
            self.is_rate_plan_addon = bool(test_data['is_rate_plan_addon'])
