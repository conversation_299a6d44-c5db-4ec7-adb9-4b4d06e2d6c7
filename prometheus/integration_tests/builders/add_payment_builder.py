from prometheus.integration_tests.builders import common_request_builder
from prometheus.integration_tests.builders.common_request_builder import bill_repo
from prometheus.integration_tests.utilities import excel_utils


class AddPayment(object):
    def __init__(self, sheet_name, test_case_id, bill_id, credit_shell_id, invoice_group_id,
                 payment_matrix=False, payor_billed_entity_id=None):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        if test_data['invoice_group_id']:
            self.data = common_request_builder.Payment(test_data, credit_shell_id, invoice_group_id,
                                                       payment_matrix=payment_matrix,
                                                       payor_billed_entity_id=payor_billed_entity_id).__dict__
        else:
            self.data = common_request_builder.Payment(test_data, credit_shell_id, payment_matrix=payment_matrix,
                                                       payor_billed_entity_id=payor_billed_entity_id).__dict__
        if 'AddPayment_21' in test_case_id:
            self.resource_version = 10000
        elif 'AddPayment_22' in test_case_id:
            self.resource_version = 'NULL_KEY'
        else:
            self.resource_version = bill_repo().load(bill_id).bill.version
