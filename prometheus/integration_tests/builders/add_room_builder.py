from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.builders.common_request_builder import booking_repo
from prometheus.integration_tests.builders.common_request_builder import Prices, GuestStay
from prometheus.integration_tests.utilities.common_utils import *
from prometheus.integration_tests.config import sheet_names


class AddRoom:
    def __init__(self, sheet_name, test_case_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = Data(test_data[0], test_case_id).__dict__
        self.resource_version = booking_repo().load(booking_id).booking.version


class Data:
    def __init__(self, test_data, test_case_id):
        if "AddRoom_16" not in test_case_id:
            self.checkin_date = increment_date(int(test_data['checkin_date']), common_config.CHECK_IN_TIME_ZONE)
            self.checkout_date = increment_date(int(test_data['checkout_date']), common_config.CHECKOUT_TIME_ZONE)

        self.room_type_id = get_room_type_id(test_data['room_type_id'])
        self.type = test_data['type']
        if sanitize_test_data(test_data['GuestStay']):
            guest_test_data = excel_utils.get_test_case_data(sheet_names.add_guest_stay_sheet_name,
                                                             test_data['GuestStay'])
            self.guest_stays = []
            for guest_data in guest_test_data:
                guest_stay = GuestStay(guest_data).__dict__
                self.guest_stays.append(guest_stay)
        if sanitize_test_data(test_data['Prices']):
            price_test_data = excel_utils.get_test_case_data(sheet_names.add_prices_sheet_name, test_data['Prices'])
            self.prices = []
            for price_data in price_test_data:
                price = Prices(price_data).__dict__
                self.prices.append(price)






