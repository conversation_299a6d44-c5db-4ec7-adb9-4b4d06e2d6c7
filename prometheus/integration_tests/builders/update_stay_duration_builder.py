from prometheus.integration_tests.builders.common_request_builder import *
from prometheus.integration_tests.config.sheet_names import add_prices_sheet_name
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import increment_date, sanitize_test_data


class UpdateStayDuration(object):
    def __init__(self, sheet_name, test_case_id, booking_id, enable_rate_plan, is_inclusion_added):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = Data(test_data, enable_rate_plan, is_inclusion_added).__dict__
        self.resource_version = booking_repo().load(booking_id).booking.version


class Data(object):
    def __init__(self, test_data, enable_rate_plan, is_inclusion_added):
        if sanitize_test_data(test_data['checkin_date']) and test_data['checkin_date'] not in ('NULL', 'INVALID'):
            self.checkin_date = increment_date(int(test_data['checkin_date']))
        else:
            self.checkin_date = sanitize_test_data(test_data['checkin_date'])

        if sanitize_test_data(test_data['checkout_date']) and test_data['checkout_date'] not in ('NULL', 'INVALID'):
            self.checkout_date = increment_date(int(test_data['checkout_date']))
        else:
            self.checkout_date = sanitize_test_data(test_data['checkout_date'])

        if sanitize_test_data(test_data['prices']) and test_data['prices'] != 'NULL':
            self.prices = []
            price_data = excel_utils.get_test_case_data(add_prices_sheet_name, test_data['prices'])
            for price in price_data:
                self.prices.append(Prices(price, enable_rate_plan).__dict__)
        else:
            self.prices = sanitize_test_data(test_data['prices'])

        if sanitize_test_data(test_data['rate_plan']) and enable_rate_plan:
            rate_plan_data = excel_utils.get_test_case_data(sheet_names.rate_plan_details_sheet,
                                                            test_data['rate_plan'])[0]
            self.rate_plan = RatePlanData(rate_plan_data).__dict__

        if sanitize_test_data(test_data['rate_plan_inclusions']) and is_inclusion_added:
            self.rate_plan_inclusions = []
            rate_plan_inclusions_data = excel_utils.get_test_case_data(sheet_names.
                                                                       rate_plan_inclusions_v2_booking_sheet_name,
                                                                       test_data['rate_plan_inclusions'])
            for rate_plan_inclusion_data in rate_plan_inclusions_data:
                self.rate_plan_inclusions.append(RatePlanInclusion(rate_plan_inclusion_data).__dict__)


class RatePlanData(object):
    def __init__(self, rate_plan_data):
        self.rate_plan_reference_id = sanitize_test_data(rate_plan_data['rate_plan_reference_id'])
        self.rate_plan_id = sanitize_test_data(rate_plan_data['rate_plan_id'])
        if rate_plan_data['restrictions']:
            self.restrictions = json.loads(rate_plan_data['restrictions'])
        if rate_plan_data['is_flexi']:
            policies_data = excel_utils.get_test_case_data(sheet_names.rate_plan_policies_details_sheet,
                                                           rate_plan_data['policy_data_id'])[0]
            self.flexi_rate_plan_details = FlexiRatePlanDetail(policies_data).__dict__
