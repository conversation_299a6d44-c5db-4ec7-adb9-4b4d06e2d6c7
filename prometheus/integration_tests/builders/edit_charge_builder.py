from prometheus.integration_tests.builders.common_request_builder import bill_repo
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data, del_none
from prometheus.integration_tests.builders import common_request_builder
from prometheus.integration_tests.config import common_config, sheet_names
import json


class EditCharge(object):
    def __init__(self, sheet_name, test_case_id, bill_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = EditCharge.Data(test_data[0]).__dict__
        self.resource_version = bill_repo().load(bill_id).bill.version

    class Data(object):
        def __init__(self, test_data):
            self.bill_to_type = sanitize_test_data(test_data['bill_to_type'])
            self.type = sanitize_test_data(test_data['type'])
            self.status = sanitize_test_data(test_data['status'])
            self.posttax_amount = sanitize_test_data(test_data['posttax_amount'])
            self.pretax_amount = sanitize_test_data(test_data['pretax_amount'])
            if test_data['billed_entity_account'] or test_data['payment_instruction']:
                self.billing_instructions = []
                charge_data = excel_utils.get_test_case_data(sheet_names.add_edit_charge_sheet_name, test_data['TC_ID'])
                for data in charge_data:
                    bill_instruction = self.BillingInstructionData(data).__dict__
                    self.billing_instructions.append(bill_instruction)

        class BillingInstructionData(object):
            def __init__(self, charge_data):
                if sanitize_test_data(charge_data['billed_entity_account']):
                    self.billed_entity_account = json.loads(sanitize_test_data(charge_data['billed_entity_account']))
                self.payment_instruction = sanitize_test_data(charge_data['payment_instruction'])
                if sanitize_test_data(charge_data['split_percentage']):
                    self.split_percentage = int(sanitize_test_data(charge_data['split_percentage']))


class AddAllowance(object):
    def __init__(self, bill_id, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = AddAllowance.Data(test_data[0]).__dict__
        self.resource_version = bill_repo().load(bill_id).bill.version

    class Data(object):
        def __init__(self, test_data):
            if test_data['pretax_amount']:
                self.pretax_amount = sanitize_test_data(str(test_data['pretax_amount']))
            else:
                self.posttax_amount = sanitize_test_data(str(test_data['posttax_amount']))
            self.remarks = sanitize_test_data(test_data['remarks'])


class AddBulkAllowance(object):
    def __init__(self, bill_id, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = []
        for data in test_data:
            allowance_data = AddBulkAllowance.Data(data).__dict__
            self.data.append(allowance_data)
        self.resource_version = bill_repo().load(bill_id).bill.version

    class Data(object):
        def __init__(self, test_data):
            if sanitize_test_data(test_data['charge_id']) and test_data['charge_id'] != 'NULL':
                self.charge_id = sanitize_test_data(int(test_data['charge_id']))
            else:
                self.charge_id = sanitize_test_data(test_data['charge_id'])
            if sanitize_test_data(test_data['charge_split_id']) and test_data['charge_split_id'] != 'NULL':
                self.charge_split_id = sanitize_test_data(int(test_data['charge_split_id']))
            else:
                self.charge_split_id = sanitize_test_data(test_data['charge_split_id'])
            self.pretax_amount = sanitize_test_data(str(test_data['pretax_amount']))
            self.posttax_amount = sanitize_test_data(str(test_data['posttax_amount']))
            self.remarks = sanitize_test_data(test_data['remarks'])


class EditChargeV2(object):
    def __init__(self, sheet_name, test_case_id, bill_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = common_request_builder.Charge(test_data[0]).__dict__
        self.resource_version = bill_repo().load(bill_id).bill.version


class EditAllowanceV2(object):
    def __init__(self, bill_id, status):
        self.resource_version = bill_repo().load(bill_id).bill.version
        self.data = EditAllowanceV2.Data(status).__dict__

    class Data(object):
        def __init__(self, status):
            self.status = status
