import json

from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data


class RedistributePaymentsBuilder(object):
    def __init__(self, sheet_name, test_case_id, invoice_group_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = Data(test_data, invoice_group_id).__dict__


class Data(object):
    def __init__(self, test_data, invoice_group_id):
        if sanitize_test_data(test_data['booking_invoice_group_id']) and \
                test_data['booking_invoice_group_id'] not in ['NULL', 'INVALID']:
            self.booking_invoice_group_id = invoice_group_id
        else:
            self.booking_invoice_group_id = sanitize_test_data(test_data['booking_invoice_group_id'])
