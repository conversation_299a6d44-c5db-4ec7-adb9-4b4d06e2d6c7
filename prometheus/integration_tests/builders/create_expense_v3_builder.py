import ast
import json

from prometheus.integration_tests.config import common_config
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data, increment_date


class CreateExpenseV3(object):
    def __init__(self, sheet_name, test_case_id, block_id=None):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = []
        for expense_data in test_data:
            expense = CreateExpenseData(expense_data, test_case_id, block_id).__dict__
            self.data.append(expense)


class CreateExpenseData(object):
    def __init__(self, expense_data, test_case_id, block_id=None):
        self.assigned_to = sanitize_test_data(expense_data['assigned_to']) if test_case_id == 'Multiple_Expense_40' \
            else json.loads(sanitize_test_data(expense_data['assigned_to']))
        self.added_by = sanitize_test_data(expense_data['added_by'])

        if sanitize_test_data(expense_data['billing_instructions']) and expense_data['billing_instructions'] != 'NULL':
            self.billing_instructions = json.loads(expense_data['billing_instructions'])
        else:
            self.billing_instructions = sanitize_test_data(expense_data['billing_instructions'])

        if sanitize_test_data(expense_data['room_stay_id']) and expense_data['room_stay_id'] not in ('NULL', 'INVALID'):
            self.room_stay_id = int(expense_data['room_stay_id'])
        else:
            self.room_stay_id = sanitize_test_data(expense_data['room_stay_id'])

        if sanitize_test_data(expense_data['skus']) and expense_data['skus'] != 'NULL':
            sku_list = json.loads(expense_data['skus'])
            for sku in sku_list:
                for date_wise_price in sku['date_wise_prices']:
                    if date_wise_price['applicable_date'] != 'NULL':
                        date_wise_price['applicable_date'] = increment_date(int(date_wise_price['applicable_date']),
                                                                            common_config.CHECK_IN_TIME_ZONE)
                    else:
                        date_wise_price['applicable_date'] = sanitize_test_data(date_wise_price['applicable_date'])
            self.skus = sku_list
        else:
            self.skus = sanitize_test_data(expense_data['skus'])

        if sanitize_test_data(expense_data['service_context']) and expense_data['service_context'] != 'NULL':
            service_context = json.loads(expense_data['service_context'])
            if block_id:
                service_context.setdefault('service_details', {})
                service_context['service_details']['hours'] = "6"
                service_context['service_details']['inventory_block_id'] = block_id[0][0]
            self.service_context = service_context
