from prometheus.integration_tests.config import request_uris, common_config
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.utilities.common_utils import query_execute, return_date


def sync_inventory(client_, start_inventory_date, end_inventory_date, hotel_id):
    BaseRequest().request_processor(client_, 'POST',
                                    request_uris.sync_inventory.format(hotel_id, return_date(start_inventory_date),
                                                                       return_date(end_inventory_date)), 201)
    query_execute(db_queries.UPDATE_INVENTORY_COUNT.format(500, hotel_id))


def change_inventory(client_, start_inventory_date=-3, end_inventory_date=10, hotel_id=None):
    query = "Delete from room_type_inventory_availability"
    query_execute(query)
    if hotel_id:
        sync_inventory(client_, start_inventory_date, end_inventory_date, hotel_id)
    else:
        for hotel_id in common_config.HOTEL_ID:
            sync_inventory(client_, start_inventory_date, end_inventory_date, hotel_id)
