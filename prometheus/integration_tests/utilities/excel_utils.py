import sys

import xlrd


# Method for storing excel data in dict
def extract_excel_data(file_name):
    wb = xlrd.open_workbook(file_name) # Opening the sheet and saving in wb.
    excel_data= {}
    for sheetName in wb.sheet_names():
        sheet_data= wb.sheet_by_name(sheetName)
        current_sheet_data={}
        for i in range(1,sheet_data.nrows):
            data_per_row={} # Data per row is stored in this
            for j in range(sheet_data.ncols): #
                data_per_row.update({sheet_data.cell_value(0, j):sheet_data.cell_value(i, j)})
                current_sheet_data.update({i:data_per_row})
        excel_data.update({sheetName:current_sheet_data})
        set_excel_data(excel_data)
    """for keys in excelData.keys():
        print(keys +"-->"+str(excelData[keys]))"""
    print("Excel data extracted")
    return excel_data

def set_excel_data(excel_data):
    global excelData1
    excelData1= excel_data

def get_excel_data():
    return excelData1

# Function for getting the Test case specific data
def get_test_case_data(sheet_name, test_case_id):
    test_case_data = []
    try:
        excel_data= get_excel_data()
        for row in excel_data[sheet_name]:
            if excel_data[sheet_name][row]['TC_ID'] == test_case_id: # Verifying if the TestCase ID passed is matching and returning the data
                test_case_data.append(excel_data[sheet_name][row])
                #print(excelData[SheetName][row])
        if len(test_case_data) == 0:
            raise ValueError('No data found in Excel sheet for TestCaseID: {}'.format(test_case_id))
            exit(1)

    except FileNotFoundError:
        print("Oops!", sys.exc_info()[0], "occurred.")
        print("Sheet Not found: " + sheet_name)
        exit(1)
    return test_case_data


def get_test_case_data_by_other_column(sheet_name, test_case_id, column_name):
    test_case_data=[]
    try:
        excel_data= get_excel_data()
        for row in excel_data[sheet_name]:
            if excel_data[sheet_name][row][column_name]==test_case_id: # Verifying if the TestCase ID passed is matching and returning the data
                test_case_data.append(excel_data[sheet_name][row])
                #print(excelData[SheetName][row])
        if len(test_case_data)==0:
            raise ValueError('No data found in Excel sheet for TestCaseID: {}'.format(test_case_id))
            exit(1)

    except FileNotFoundError:
        print("Oops!", sys.exc_info()[0], "occurred.")
        print("Sheet Not found: " + sheet_name)
        exit(1)
    return test_case_data