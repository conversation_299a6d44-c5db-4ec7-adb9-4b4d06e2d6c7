INSERT INTO public.room_type (deleted, room_type_id, type, created_at, modified_at) VALUES
(false, 'RT02', 'OAK', now(), now()),
(false, 'RT03', 'MAPLE', now(), now()),
(false, 'RT04', 'MAHOGANY', now(), now()),
(false, 'RT01', 'ACACIA', now(), now());

INSERT INTO public.expense_item (deleted, id, name, description, short_name, sku_category_id, expense_item_id, created_at, modified_at, linked, addon_code, sku_id) VALUES
(false, 1, 'Food: Lunch', 'This is lunch', 'LNCH', 'food', 'lunch', now(), now(),False, 'LUNCH', '378'),
(false, 2, 'Food: Dinner', 'This is dinner', 'DNNR', 'food', 'dinner', now(), now(),False, 'DINNER', '1'),
(false, 3, 'Food: Snacks', 'Tasty snacks', 'SNCKS', 'food', 'snacks', now(), now(),False, NULL, '2'),
(false, 4, 'Beverages', 'Tasty bevereges', 'BVRGS', 'food', 'beverages', now(), now(),False, NULL, '3'),
(false, 5, 'Laundry', 'Wash your clothes', 'LNDRY', 'laundry', 'laundry', now(), now(),False, NULL, '4'),
(false, 6, 'Conference/Banquet', 'Meetings', 'BNQT', 'banquet', 'banquet', now(), now(),False, NULL, '5'),
(false, 7, 'Stay: Extra Guest', 'Additional Guest', 'Extra guest', 'stay', 'extra_guest', now(), now(),True, NULL, '377'),
(false, 8, 'Stay: Early Check-in', 'Early check-in charges', 'Early checkin', 'stay', 'early_checkin', now(), now(),True, NULL, 'early_checkin'),
(false, 9, 'Stay: Late Check-out', 'Late check-out charges', 'Late Check-out', 'stay', 'late_checkout', now(), now(),True, NULL, 'late_checkout'),
(false, 10, 'Food: Breakfast', 'Add breakfast to your booking', 'Breakfast', 'food', 'breakfast', now(), now(),False, 'BREAKFAST', '8'),
(false, 11, '3655', 'Cold Coffee', ' Cold Coffee', 'food', '3655', now(), now(), False, NULL, '3655'),
(false, 13, 'Charges: Booking Cancellation', 'Cancellation charges', 'Cancellation', 'stay', 'booking_cancellation', now(), now(),False, NULL, '9'),
(false, 14, 'Charges: No Show', 'No show charges', 'No show', 'stay', 'no_show', now(), now(),False, NULL, '10'),
(false, 15, 'Charges: Booking modification ', 'Booking modification charges', 'Modification', 'stay', 'booking_modification', now(), now(),False, NULL, '11'),
(false, 16, 'Taxi', 'This is cab', 'CB', 'taxi', 'taxi', now(), now(),False, NULL, '12'),
(false, 17, 'Food: Major Meal', 'Add Major to your booking', 'Major Meal', 'food', 'major_meal', now(), now(), False, 'MAJORMEAL', '13'),
(false, 18, 'Accommodation', 'Accommodation Charge', 'Accommodation Charge', 'stay', 'accommodation_charge', now(), now(),False, NULL,NULL),
(false, 19, 'Food: Brunch', 'This is dinner but made as linked charge', 'BRNCH', 'food', 'Brunch', now(), now(), True, 'BRUNCH', '379'),
(false, 20, 'Stay: Early Check-out', 'Early check-out charges', 'Early checkout', 'stay', 'early_checkout', now(), now(),True, NULL, '16'),
(false, 21, 'Extra: 9', 'Extra charge at 9%', 'Extra charge', 'extra9', 'extra9', now(), now(), False, NULL, '17'),
(false, 22, 'Taxi', 'This is linked cab', 'CB', 'taxi', 'linked_taxi', now(), now(), True, 'linked_taxi', '18');

INSERT INTO public.sku_category (created_at, modified_at, deleted, item_code, name, sku_category_id, status, has_slab_based_taxation) VALUES
(now(), now(), false, 'HSN:996331', 'food', 'food', 'ACTIVE', false),
(now(), now(), false, 'HSN:-1', 'hotel-fees', 'hotel-fees', 'ACTIVE', false),
(now(), now(), false, 'HSN:9997', 'laundry', 'laundry', 'ACTIVE', false),
(now(), now(), false, 'HSN:996311', 'stay', 'stay', 'ACTIVE', false),
(now(), now(), false, 'HSN:996331', 'beverages', 'beverages', 'ACTIVE', false),
(now(), now(), false, 'HSN:996601', 'taxi', 'taxi', 'ACTIVE', false),
(now(), now(), false, 'HSN:996334', 'banquet', 'banquet', 'ACTIVE', false),
(now(), now(), false, 'HSN:001', 'extra9', 'extra9', 'ACTIVE', false);

INSERT INTO public.reseller_gst (created_at, modified_at, deleted, id, state_id, state_name, gstin_num, date_of_registration, address) VALUES
(now(), now(), false, '1', 1, 'Karnataka', '29AAHCR3137R1ZN', '2017-07-01', '1, 1, AMR TECH PARK, IT INDUSTRY, NAGARATHPURA, HOSUR ROAD, BENGALURU,(BANGALORE) URBAN, KARNATAKA,560068'),
(now(), now(), false, '2', 7, 'MAHARASHTRA', '27AAHCR3137R1ZR', '2017-09-04', '355, Linking Road, Khar West, Mumbai, Maharashtra - 400052'),
(now(), now(), false, '4',11, 'DELHI', '07AAHCR3137R2ZS', '21-Sep-17', '1, B-117, LAJPAT NAGAR 1, NEW DELHI, NEW DELHI, DELHI,110024'),
(now(), now(), false, '5',9, 'KERALA', '32AAHCR3137R1Z0', '30-Dec-17', '40/5340, Market Road, Near Saritha Theatre, Marine Drive, Ernakulam, Kerala - 682035'),
(now(), now(), false, '6',6, 'MADHYA PRADESH', '23AAHCR3137R1ZZ', '30-Dec-17', '4, BLOCK 1, PLOT NO 4 AND 5, SHEKHAR CENTRAL, A. B.ROAD, MANORAMAGANJ, INDORE, MADHYA PRADESH, 452001'),
(now(), now(), false, '7',16, 'RAJASTHAN', '08AAHCR3137R1ZR', '30-Dec-17', 'Near Sanghi Petrol Pump, Old Olympic Cinema, Station Road, Ratanada, Jodhpur, Rajasthan - 342001'),
(now(), now(), false, '8',14, 'TAMIL NADU','33AAHCR3137R1ZY', '30-Dec-17', 'Plot No 4 & 5, 4th Cross St, Kalaimagal Nagar, Ekkatuthangal, Chennai, Tamil Nadu - 600032'),
(now(), now(), false, '9',4, 'TELANGANA','36AAHCR3137R1ZS', '03-Jan-18', '5-9-88/A1, Beside Haj House, Opposite Public Gardens, Nampally, Hyderabad, Telangana - 500001'),
(now(), now(), false, '10',18, 'WEST BENGAL','19AAHCR3137R1ZO', '15-Jan-18', '4TH-FR 290, MAIN ROAD LP-416/3, RAJ DANGA, KOLKATA,WEST BENGAL, 700107'),
(now(), now(), false, '11',22, 'CHHATTISGARH','22AAHCR3137R1Z1', '01-Feb-18', 'JEEVAN VIHAR RING ROAD NO 1,TELIABANDHA, RAIPUR, CHHATTISGARH, 492006'),
(now(), now(), false, '12',17, 'GOA','30AAHCR3137R1Z4', '12-Feb-18', '129/1, Francisco Pereira Vaddo, Near Kenilworth Hotel, Utorda, Goa - 403713'),
(now(), now(), false, '13',15, 'PUDUCHERRY','34AAHCR3137R1ZW', '14-Mar-18', '92-94, OPPOSITE TO RAILWAY STATION, SUBBAIAH SALAI, PONDICHERRY, PUDUCHERRY'),
(now(), now(), false, '14',8, 'UTTARAKHAND','05AAHCR3137R1ZX', '04-Feb-18', 'BADRINATH ROAD, TAPOVAN,LAXMANJHULLA, TEHRI GARHWAL, UTTARAKHAND, 249192'),
(now(), now(), false, '15',12, 'UTTAR PRADESH','09AAHCR3137R1ZP', '24-Apr-18', 'KHASRA NO 331SA, KAMTA, FAIZABAD ROAD, CHINHAT,LUCKNOW, UTTAR PRADESH, 226010'),
(now(), now(), false, '16',3, 'PUNJAB','03AAHCR3137R1Z1', '25-Apr-18', 'GROUND FLOOR, 5, WADHWA NAGAR,ZIRAKPUR KALKA HIGHWAY, ZIRAKPUR, SAS NAGAR, PUNJAB, 140603'),
(now(), now(), false, '17', 13, 'ANDHRA PRADESH', '37AAHCR3137R1ZQ', '08-May-18', '26-6-4, O/P TALUK OFFICE, PACHA PAPAIH STREET,GANDHI NAGAR, KRISHNA, ANDHRA PRADESH, 520003'),
(now(), now(), false, '18',10, 'HIMACHAL PRADESH','02AAHCR3137R1Z3', '17-May-18', 'NEAR JUDICIARY COURT, SANDAL,CHAKKAR, SHIMLA, HIMACHAL PRADESH, 171005'),
(now(), now(), false, '19',5, 'HARYANA','06AAHCR3137R1ZV', '09-Jun-18', '520A, Block A, Sushant Lok Phase I, Sector 28, Gurgaon, Haryana - 122002'),
(now(), now(), false, '20',20, 'ASSAM','18AAHCR3137R1ZQ', '03-Jul-18', 'BOJHRAJHAR, ROWTA, ASSAM, DARRANG, ASSAM, 784508'),
(now(), now(), false, '21',25, 'MEGHALAYA','17AAHCR3137R1ZS', '24-Dec-18', '0, 7th Mile Upper Shillong, Mawaniaglah Village, Upper Gate,Shillong, East Khasi Hills, Meghalaya, 793009'),
(now(), now(), false, '22',19, 'Jharkhand','20AAHCR3137R1Z5', '13-Dec-2018', '11, Gomate Apartments, Bank More, Dhanbad, Jharkhand, 826001'),
(now(), now(), false, '23',23, 'Sikkim','11AAHCR3137R1Z4', '27-Dec-2018', 'National Highway, NH 31, Arithang, Gangtok, Sikkim, 737101'),
(now(), now(), false, '24',2, 'Gujarat','24AAHCR3137R1ZX', '06-Dec-2018', '10, Vasant Bagh Society, Gulbai Tekra, Ahemdabad, Ahmedabad,Gujarat, 380006'),
(now(), now(), false, '25',26, 'Chandigarh','04AAHCR3137R1ZZ', '9-Feb-2019', '1, Cabin No. 2, Plot No 3136, Industrial Area, Phase 2, Chandigarh, 160002'),
(now(), now(), false, '26',27, 'Bihar','10AAHCR3137R1Z6', '5-Feb-2019', 'HS, Station Road 2, Nawada, Bihar, 805103');

INSERT INTO public.hotel_room_type_config (created_at,modified_at,deleted,hotel_id,room_type_id,count,base_pax,max_pax,max_child,max_adult_plus_children,status) VALUES
(now(), now(), false,'0016932','RT01',NULL,1,3,1,3,'active'),
(now(), now(), false,'0016932','RT02',NULL,1,3,1,3,'active'),
(now(), now(), false,'0016932','RT03',NULL,1,3,1,3,'active'),
(now(), now(), false,'0016932','RT04',NULL,1,3,1,3,'active');

INSERT INTO public.hotel_room_type_config (created_at,modified_at,deleted,hotel_id,room_type_id,count,base_pax,max_pax,max_child,max_adult_plus_children,status) VALUES
(now(), now(), false,'0016932','RT01',NULL,1,3,1,3,'active'),
(now(), now(), false,'0016932','RT02',NULL,1,3,1,3,'active'),
(now(), now(), false,'0016932','RT03',NULL,1,3,1,3,'active'),
(now(), now(), false,'0016932','RT04',NULL,1,3,1,3,'active');


INSERT INTO public.hotel_room_type_config (created_at,modified_at,deleted,hotel_id,room_type_id,count,base_pax,max_pax,max_child,max_adult_plus_children,status) VALUES
(now(), now(), false,'0037935','RT01',NULL,1,3,1,3,'active'),
(now(), now(), false,'0037935','RT02',NULL,1,3,1,3,'active'),
(now(), now(), false,'0037935','RT03',NULL,1,3,1,3,'active'),
(now(), now(), false,'0037935','RT04',NULL,1,3,1,3,'active');

INSERT INTO public.room (created_at,modified_at,deleted,hotel_id,room_type_id,room_number,status) VALUES
(now(),now(),false,'0016932','RT01','101','active'),
(now(),now(),false,'0016932','RT01','102','active'),
(now(),now(),false,'0016932','RT01','103','active'),
(now(),now(),false,'0016932','RT01','104','active'),
(now(),now(),false,'0016932','RT02','105','active'),
(now(),now(),false,'0016932','RT02','106','active'),
(now(),now(),false,'0016932','RT02','107','active'),
(now(),now(),false,'0016932','RT02','108','active'),
(now(),now(),false,'0016932','RT03','109','active'),
(now(),now(),false,'0016932','RT03','110','active'),
(now(),now(),false,'0016932','RT03','111','active'),
(now(),now(),false,'0016932','RT03','112','active'),
(now(),now(),false,'0016932','RT04','113','active'),
(now(),now(),false,'0016932','RT04','114','active'),
(now(),now(),false,'0016932','RT04','115','active'),
(now(),now(),false,'0016932','RT04','116','active'),
(now(),now(),false,'0016932','RT01','117','active'),
(now(),now(),false,'0016932','RT01','118','active'),
(now(),now(),false,'0016932','RT01','119','active'),
(now(),now(),false,'0016932','RT01','120','active'),
(now(),now(),false,'0016932','RT01','121','active'),
(now(),now(),false,'0016932','RT01','122','active'),
(now(),now(),false,'0016932','RT01','123','active'),
(now(),now(),false,'0016932','RT01','124','active'),
(now(),now(),false,'0016932','RT01','125','active'),
(now(),now(),false,'0016932','RT01','126','active'),
(now(),now(),false,'0016932','RT01','127','active'),
(now(),now(),false,'0016932','RT01','128','active'),
(now(),now(),false,'0016932','RT02','129','active'),
(now(),now(),false,'0016932','RT02','130','active'),
(now(),now(),false,'0016932','RT02','131','active'),
(now(),now(),false,'0016932','RT02','132','active'),
(now(),now(),false,'0016932','RT03','133','active'),
(now(),now(),false,'0016932','RT03','134','active'),
(now(),now(),false,'0016932','RT03','135','active'),
(now(),now(),false,'0016932','RT03','136','active'),
(now(),now(),false,'0016932','RT01','137','active'),
(now(),now(),false,'0016932','RT01','138','active'),
(now(),now(),false,'0016932','RT01','139','active'),
(now(),now(),false,'0016932','RT02','140','active'),
(now(),now(),false,'0016932','RT02','141','active'),
(now(),now(),false,'0016932','RT02','142','active'),
(now(),now(),false,'0016932','RT02','143','active'),
(now(),now(),false,'0016932','RT01','144','active'),
(now(),now(),false,'0016932','RT01','145','active'),
(now(),now(),false,'0016932','RT01','146','active');

INSERT INTO public.room (created_at,modified_at,deleted,hotel_id,room_type_id,room_number,status) VALUES
(now(),now(),false,'0037935','RT01','101','active'),
(now(),now(),false,'0037935','RT01','102','active'),
(now(),now(),false,'0037935','RT01','103','active'),
(now(),now(),false,'0037935','RT01','104','active'),
(now(),now(),false,'0037935','RT01','201','active'),
(now(),now(),false,'0037935','RT01','202','active'),
(now(),now(),false,'0037935','RT01','301','active'),
(now(),now(),false,'0037935','RT01','302','active'),
(now(),now(),false,'0037935','RT01','303','active'),
(now(),now(),false,'0037935','RT01','304','active'),
(now(),now(),false,'0037935','RT01','305','active'),
(now(),now(),false,'0037935','RT01','306','active'),
(now(),now(),false,'0037935','RT01','307','active'),
(now(),now(),false,'0037935','RT01','308','active'),
(now(),now(),false,'0037935','RT02','105','active'),
(now(),now(),false,'0037935','RT02','106','active'),
(now(),now(),false,'0037935','RT02','107','active'),
(now(),now(),false,'0037935','RT02','108','active'),
(now(),now(),false,'0037935','RT02','203','active'),
(now(),now(),false,'0037935','RT02','204','active'),
(now(),now(),false,'0037935','RT03','109','active'),
(now(),now(),false,'0037935','RT03','110','active'),
(now(),now(),false,'0037935','RT03','111','active'),
(now(),now(),false,'0037935','RT03','205','active'),
(now(),now(),false,'0037935','RT03','206','active'),
(now(),now(),false,'0037935','RT03','112','active'),
(now(),now(),false,'0037935','RT04','113','active'),
(now(),now(),false,'0037935','RT04','114','active'),
(now(),now(),false,'0037935','RT04','115','active'),
(now(),now(),false,'0037935','RT04','116','active');

INSERT INTO public.hotel (created_at, modified_at, deleted, hotel_id,name, area,status, legal_name, legal_address, gstin_num, switch_over_time, checkin_grace_time, checkout_grace_time,
checkin_time, checkout_time, free_late_checkout_time, launched_date, state_id, state_name, city_id, city_name, pincode,country, legal_signature, legal_city_id, legal_city_name, legal_state_id, legal_state_name, legal_pincode, housekeeping_enabled,base_currency, timezone, current_business_date,
is_test) VALUES
(now(), now(),false,'0016932','Treebo CRS','CRS Bay','active','Treebo CRS','AMR Tech park','IN111','00:00:00',540,360,'12:00:00','11:00:00','15:00:00' ,NULL,9,
'Karnataka',1,'Bangalore',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,true,'INR',NULL, CURRENT_DATE, False),
(now(), now(),false,'0037935','Treebo CRS 2','CRS Bay','active','Treebo CRS 2','AMR Tech park','IN111','00:00:00',540,360,'12:00:00','11:00:00','15:00:00',NULL,9,
'Karnataka',1,'Bangalore',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,true,'EUR',NULL, CURRENT_DATE, False);

INSERT INTO public.hotel_config (deleted,hotel_id,migration_start_date,migration_end_date,live_date,managed_by,created_at,modified_at) VALUES (
false,'0016932',now(),now(),now(),'crs',now(),now()),
(false,'0037935',now(),now(),now(),'crs',now(),now());