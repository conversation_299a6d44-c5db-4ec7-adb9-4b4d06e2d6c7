import pytest

from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.cashier_module.validations.validation_cash_register_session import \
    ValidationCashRegisterSession
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import get_cashiering_config


class TestCashRegisterSession(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id,tc_description, multicurrency_enabled, cashiering_enabled, x_hotel_id, hotel, previous_actions, status_code, user_type, user,"
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [
            ("CashierSession_01", "Create a Session for a given cash register with opening balance in INR only and x_hotel_id is same as hotel",
             False, True, None, None,
             [{'id': 'CreateRegister_01', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_01",
             "Create a Session for a given cash register with opening balance in INR only and x_hotel_id is different from hotel",
             False, True, "0037935", None,
             [{'id': 'CreateRegister_06', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             403, SUPER_ADMIN, AUTOMATION, "04011025", "", "", '{"entity_type": "cash_register"}', True, ""),
            ("CashierSession_01", "Create a Session for a given cash register with opening balance in INR only and x_hotel_id is same as hotel and user_type is FDM",
             False, True, None, None, [{'id': 'CreateRegister_02', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, FDM, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_01", "Create a Session for a given cash register with opening balance in INR only and x_hotel_id is same as hotel and user_type is CR Team",
             False, True, None, None,
             [{'id': 'CreateRegister_01', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             403, CR_TEAM , AUTOMATION, "04011034", "", "", "", "", ""),
            ("CashierSession_02", "Create a Session for a given cash register with opening balance as INR 0 and x_hotel_id is same as hotel and user_type is Super-Admin",
             False, True, None, None, [{'id': 'CreateRegister_01', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, SUPER_ADMIN, AUTOMATION, "", "", "", "", True, "Need to clear this usecase"),
            ("CashierSession_03", "Create a Session for a given cash register with opening Balance in INR,EUR and x_hotel_id is same as hotel and user_type is Super-Admin",
             False, True, None, None,
             [{'id': 'CreateRegister_01', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_04", "Create Multiple Open Session For a Register and x_hotel_id is same as hotel and user_type is Super-Admin",
             False, True, None, None, CashierSession_04, 400, SUPER_ADMIN, AUTOMATION, "04015936", "", "", "", False, ""),
            ("CashierSession_05",
             "Create a Session for a given cash register after closing previous one and x_hotel_id is same as hotel and user_type is Super-Admin",
             False, True, None, None, CashierSession_05, 200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_06",
             "Create a Session for a given cash register without opening balance and x_hotel_id is same as hotel",
             False, True, None, None,
             [{'id': 'CreateRegister_06', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             400, SUPER_ADMIN, AUTOMATION, "04010006", "", "", {"field": "opening_balance"}, True, "Need to clear this usecase"),
            ("CashierSession_07",
             "Create a Session for a given cash register with Opening Balance as Null and x_hotel_id is same as hotel",
             False, True, None, None,
             [{'id': 'CreateRegister_06', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             400, SUPER_ADMIN, AUTOMATION, "04010006", "", "", {"field": "opening_balance"}, True, "Skipping this test cases as api is throwing 500"),
            ("CashierSession_01",
             "(MultiCurrency)Create a Session for a given cash register with opening balance in INR only and x_hotel_id is same as hotel",
             True, True, None, None,
             [{'id': 'CreateRegister_01', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_01",
             "(MultiCurrency)Create a Session for a given cash register with opening balance in INR only and x_hotel_id is different from hotel",
             True, True, "0037935", None,
             [{'id': 'CreateRegister_06', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_01",
             "(MultiCurrency)Create a Session for a given cash register with opening balance in INR only and x_hotel_id is same as hotel and user_type is FDM",
             True, True, None, None,
             [{'id': 'CreateRegister_02', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, FDM, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_02",
             "(MultiCurrency)Create a Session for a given cash register with opening balance as INR 0 and x_hotel_id is same as hotel and user_type is Super-Admin",
             True, True, None, None,
             [{'id': 'CreateRegister_01', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, SUPER_ADMIN, AUTOMATION, "", "", "", "", True, "Need to clear this usecase"),
            ("CashierSession_03",
             "(MultiCurrency)Create a Session for a given cash register with opening Balance in INR,EUR and x_hotel_id is same as hotel and user_type is Super-Admin",
             True, True, None, None,
             [{'id': 'CreateRegister_01', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_08",
             "Create a Session for a given cash register with opening balance in INR only and x_hotel_id is same as hotel",
             True, True, None, None,
             [{'id': 'CreateRegister_10', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_09",
             "Create a Session for a given cash register with opening balance in EUR and GBP and x_hotel_id is same as hotel",
             True, True, None, None,
             [{'id': 'CreateRegister_10', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN}],
             200, FDM, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_10", "Create a Multiple Cashier Session", False, True, None, None,
             [{'id': 'CreateRegister_01', 'type': 'create_register', 'user': AUTOMATION, 'user_type': SUPER_ADMIN},
              {'id': 'CashierSession_01', 'type': 'create_register_session', 'user': AUTOMATION,
               'user_type': SUPER_ADMIN}], 400, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("CashierSession_11", "Create a cashier session after one session is closed", False, True, None, None,
             CashierSession_05, 200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            # ("CashierSession_12", "Create a multiple cashier session after many session is closed", False, True, None,
            #  None, CashierSession_12, 200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_cash_register_session(self, client_, test_case_id, tc_description,
                                   multicurrency_enabled, cashiering_enabled, x_hotel_id, hotel, previous_actions,
                                   status_code, user_type,
                                   user, error_code, error_message, dev_message, error_payload, skip_case,
                                   skip_message):
        if skip_case:
            pytest.skip(skip_message)
        hotel_id = hotel if hotel else HOTEL_ID[1] if multicurrency_enabled else HOTEL_ID[0]
        x_hotel_id = hotel_id if x_hotel_id is None else x_hotel_id
        hotel_level_catalog_config = [get_cashiering_config() if cashiering_enabled else None]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, hotel_level_catalog_config, x_hotel_id)

        response = self.cashier_request.create_cash_register_session_request(client_, test_case_id,
                                                                             status_code, user_type, hotel_id, user,
                                                                             x_hotel_id, hotel_level_catalog_config)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, test_case_id, response, hotel_id, self.cashier_request.cash_register_id, user,
                            self.cashier_request, user_type, x_hotel_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, test_case_id, response, hotel_id, cash_register_id, user, cashier_request, user_type, x_hotel_id):
        validation = ValidationCashRegisterSession(client_, response, test_case_id, hotel_id, user, cashier_request, x_hotel_id)
        validation.validate_create_register_session_response(cash_register_id)
        validation.validate_get_session_response(cash_register_id, user_type)
