import pytest

from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.cashier_module.validations.validation_create_register import \
    ValidationCreateRegister
from prometheus.integration_tests.config.common_config import FDM, SUPER_ADMIN, HOTEL_ID, ERROR_CODES, SUCCESS_CODES, \
    PMS, CR_TEAM, \
    TESTING_CRS, AUTOMATION
from prometheus.integration_tests.utilities.common_utils import get_cashiering_config


class TestCreateRegister(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id,tc_description, multicurrency_enabled, cashiering_enabled, x_hotel_id, hotel, previous_actions, status_code, user_type, user,"
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [
            ("CreateRegister_01", "Create a Register for a hotel with opening Balance in INR only and x_hotel is same as hotel_id and user_type is FDM",
             False, True, None, None, None, 403, FDM, TESTING_CRS, "04011035", "", "", "", "", ""),
            ("CreateRegister_01", "Create a Register for a hotel with opening Balance in INR only and x_hotel is same as hotel_id and user_type is Super-Admin", False, True, None,
             None, None, 200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("CreateRegister_01","Create a Register for a hotel with opening Balance in INR only and x_hotel is same as hotel_id and user_type is CR Team",
             False, True, None, None, None, 403, CR_TEAM, TESTING_CRS, "04011035", "", "", "", "", ""),
            ("CreateRegister_02", "Create a Register for a hotel with opening Balance in EUR and INR and x_hotel is same as hotel_id and user_type is Super-Admin Team", False,
             True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", False, ""),
            ("CreateRegister_03", "Create a Register for a hotel with opening Balance in EUR,USD and  INR and x_hotel is same as hotel_id and user_type is Super-Admin Team",
             False, True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", False, ""),
            ("CreateRegister_06", "Create a Register for a hotel with opening Balance as INR 0 and x_hotel is same as hotel_id and user_type is Super-Admin Team",
             False, True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", False, ""),
            ("CreateRegister_07",
             "Create a Register for a hotel with opening Balance without Currency  and x_hotel is same as hotel_id and user_type is Super-Admin Team",
             False, True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", True, "Skipping this test case as api is giving 500"),
            ("CreateRegister_08",
             "Create a register for a hotel without Opening Balance and x_hotel is same as hotel_id and user_type is Super-Admin",
             False, True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", False, ""),
            ("CreateRegister_09",
             "Create a register with Opening Balance as Null and x_hotel is same as hotel_id and user_type is Super-Admin",
             False, True, None, None, None, 400, SUPER_ADMIN, TESTING_CRS, "04010006", "", "", {'field': 'default_opening_balance'}, False, ""),
            ("CreateRegister_01",
             "(MultiCurrency)Create a Register for a hotel with opening Balance in INR only and x_hotel is same as hotel_id and user_type is Super-Admin",
             True, True, None, None, None, 200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("CreateRegister_01",
             "(MultiCurrency)Create a Register for a hotel with opening Balance in INR only and x_hotel is same as hotel_id and user_type is FDM",
             True, True, None, None, None, 403, FDM, TESTING_CRS, "04011035", "", "", "", "", ""),
            ("CreateRegister_01",
             "(MultiCurrency)Create a Register for a hotel with opening Balance in INR only and x_hotel is same as hotel_id and user_type is CR Team",
             True, True, None, None, None, 403, CR_TEAM, TESTING_CRS, "04011035", "", "", "", "", ""),
            ("CreateRegister_02",
             "(MultiCurrency)Create a Register for a hotel with opening Balance in EUR and INR and x_hotel is same as hotel_id and user_type is Super-Admin Team",
             True, True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", False, ""),
            ("CreateRegister_03",
             "(MultiCurrency)Create a Register for a hotel with opening Balance in EUR,USD and  INR and x_hotel is same as hotel_id and user_type is Super-Admin Team",
             True, True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", False, ""),
            ("CreateRegister_06",
             "(MultiCurrency)Create a Register for a hotel with opening Balance as INR 0 and x_hotel is same as hotel_id and user_type is Super-Admin Team",
             True, True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", False, ""),
            ("CreateRegister_07",
             "(MultiCurrency)Create a Register for a hotel with opening Balance without Currency  and x_hotel is same as hotel_id and user_type is Super-Admin Team",
             True, True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", True, "Skipping this test case as api is giving 500"),
            ("CreateRegister_08",
             "(MultiCurrency)Create a register for a hotel without Opening Balance and x_hotel is same as hotel_id and user_type is Super-Admin",
             True, True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", False, ""),
            ("CreateRegister_10",
             "(MultiCurrency)Create a Register for a hotel with opening Balance in EUR only and x_hotel is same as hotel_id and user_type is Super-Admin",
             True, True, None, None, None, 200, SUPER_ADMIN, TESTING_CRS, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_create_register(self, client_, test_case_id, tc_description,
                                multicurrency_enabled, cashiering_enabled, x_hotel_id, hotel,
                                previous_actions, status_code, user_type, user,
                                error_code, error_message, dev_message, error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if hotel:
            hotel_id = hotel
        else:
            if multicurrency_enabled:
                hotel_id = HOTEL_ID[1]
            else:
                hotel_id = HOTEL_ID[0]

        if x_hotel_id is None:
            x_hotel_id = hotel_id
        hotel_level_catalog_config = []
        if cashiering_enabled:
            hotel_level_catalog_config.append(get_cashiering_config())

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.cashier_request.create_register_request(client_, test_case_id,
                                                                status_code, user_type, hotel_id, x_hotel_id, user,
                                                                hotel_level_catalog_config)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, test_case_id, response, hotel_id, user, x_hotel_id, self.cashier_request,
                            user_type)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, test_case_id, response, hotel_id, user, x_hotel_id, cashier_request, user_type):
        validation = ValidationCreateRegister(client_, response, test_case_id, hotel_id, user, x_hotel_id,
                                              cashier_request, user_type)
        validation.validate_create_register_response()
        validation.validate_get_cash_register_response()
