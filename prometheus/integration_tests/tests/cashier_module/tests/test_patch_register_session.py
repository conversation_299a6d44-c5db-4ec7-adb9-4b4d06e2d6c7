import pytest

from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.cashier_module.validations.validation_patch_register_session import \
    ValidationPatchRegisterSession
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import get_cashiering_config


class TestEditRegisterSession(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id,tc_description, multicurrency_enabled, cashiering_enabled, x_hotel_id, hotel, previous_actions, status_code, user_type, user,"
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [
            ("PatchCashierSession_01", "Close a Session, x_hotel_id is same as hotel and user_type is Super-Admin",
             False, True, None, None, PatchCashierSession_01, 200, SUPER_ADMIN, AUTOMATION, "", "", "", "", False, ""),
            ("Patch<PERSON>ashierSession_01", "Close a Session, x_hotel_id is same as hotel and user_type is FDM",
             False, True, None, None, PatchCashierSession_01, 200, FDM, AUTOMATION, "", "", "", "", False, ""),
            ("PatchCashierSession_01", "Close a Session, x_hotel_id is same as hotel and user_type is CR-Team",
             False, True, None, None, PatchCashierSession_01, 403, CR_TEAM, AUTOMATION, "04011034", "", "", "", "",
             ""),
            ("PatchCashierSession_02", "Close already closed Session, x_hotel_id is same as hotel and user_type is FDM",
             False, True, None, None, PatchCashierSession_03, 200, FDM, AUTOMATION, "", "", "", "", True, ""),
            ("PatchCashierSession_03",
             "Close a Session for a Register which does not exist, x_hotel_id is same as hotel and user_type is FDM",
             False, True, None, None, PatchCashierSession_01, 404, FDM, AUTOMATION, "04010007", "", "", "", False, ""),
            ("PatchCashierSession_04",
             "Close a Session which doen not exist, x_hotel_id is same as hotel and user_type is FDM",
             False, True, None, None, PatchCashierSession_01, 404, FDM, AUTOMATION, "04010007", "", "", "", False, ""),
            ("PatchCashierSession_05", "Open a Closed Session, x_hotel_id is same as hotel and user_type is FDM",
             False, True, None, None, PatchCashierSession_03, 200, FDM, AUTOMATION, "", "", "", "", False, ""),
            ("PatchCashierSession_06",
             "Pass Status other than close and open, x_hotel_id is same as hotel and user_type is FDM",
             False, True, None, None, PatchCashierSession_01, 200, FDM, AUTOMATION, "", "", "", "", True,
             "Skipping this test case as api is throwing 200 right now"),
            ("PatchCashierSession_07",
             "Open a Session for a Register when there is already an open Session for the Same Register, x_hotel_id is same as hotel and user_type is FDM",
             False, True, None, None, PatchCashierSession_03, 200, FDM, AUTOMATION, "", "", "", "", True,
             "Skipping this test case as right now api is giving 200"),
        ])
    @pytest.mark.regression
    def test_patch_register_session(self, client_, test_case_id, tc_description,
                                    multicurrency_enabled, cashiering_enabled, x_hotel_id, hotel, previous_actions,
                                    status_code, user_type, user, error_code, error_message, dev_message, error_payload,
                                    skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if hotel:
            hotel_id = hotel
        else:
            if multicurrency_enabled:
                hotel_id = HOTEL_ID[1]
            else:
                hotel_id = HOTEL_ID[0]

        if x_hotel_id is None:
            x_hotel_id = hotel_id

        hotel_level_catalog_config = []
        if cashiering_enabled:
            hotel_level_catalog_config.append(get_cashiering_config())

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, hotel_level_catalog_config, x_hotel_id)

        if test_case_id == 'PatchCashierSession_07':
            closed_session_id = self.cashier_request.cash_register_session_id
            self.cashier_request.create_cash_register_session_request(client_, "CashierSession_03", 200, user_type,
                                                                      hotel_id, user, x_hotel_id, hotel_id)
            self.cashier_request.cash_register_session_id = closed_session_id

        response = self.cashier_request.edit_register_session_request(client_, test_case_id, status_code, user_type,
                                                                      user, x_hotel_id)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, test_case_id, response, hotel_id, self.cashier_request.cash_register_id, user,
                            self.cashier_request, user_type, x_hotel_id, self.cashier_request.cash_register_session_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, test_case_id, response, hotel_id, cash_register_id, user, cashier_request, user_type,
                   x_hotel_id, register_session_id):
        validation = ValidationPatchRegisterSession(client_, response, test_case_id, hotel_id, user, cashier_request,
                                                    x_hotel_id)
        validation.validate_create_register_session_response(cash_register_id, register_session_id)
        validation.validate_get_session_response(cash_register_id, user_type)
