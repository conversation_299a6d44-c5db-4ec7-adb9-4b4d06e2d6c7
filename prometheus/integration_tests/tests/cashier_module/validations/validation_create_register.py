from prometheus.integration_tests.config import sheet_names, common_config
from prometheus.integration_tests.tests.cashier_module.validations.common_register_validations import ValidationRegister
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data



class ValidationCreateRegister(ValidationRegister):

    def __init__(self, client, response, test_case_id, hotel_id, user, x_hotel_id, cashier_request, user_type):
        self.hotel_id = hotel_id
        self.response = response
        self.test_data = get_test_case_data(sheet_names.cash_register_sheet_name, test_case_id)[0]
        self.client = client
        self.user = user
        self.x_hotel_id = x_hotel_id
        self.cashier_request = cashier_request
        self.user_type = user_type

    def validate_create_register_response(self):
        super(ValidationCreateRegister, self).validate_create_register_response(self.response, self.test_data,
                                                                                self.hotel_id)
        assert self.response['data']['start_datetime'] is not None
        if self.user is None:
            assert_(self.response['data']['opened_by'], common_config.AUTOMATION)
        else:
            assert_(self.response['data']['opened_by'], self.user)

    def validate_get_cash_register_response(self):
        get_cashier_session_response = self.cashier_request.get_cash_register_request(self.client, 200, self.user_type,
                                                                                      self.user, self.response['data'][
                                                                                          'cash_register_id'],
                                                                                      self.x_hotel_id)
        super(ValidationCreateRegister, self).validate_get_cash_register_response(self.response, self.test_data,
                                                                                  self.hotel_id,
                                                                                  get_cashier_session_response)
        assert get_cashier_session_response['data']['start_datetime'] is not None
        if self.user is None:
            assert_(get_cashier_session_response['data']['opened_by'], common_config.AUTOMATION)
        else:
            assert_(get_cashier_session_response['data']['opened_by'], self.user)
