import ast
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data
from prometheus.integration_tests.config import common_config


class ValidationPatchRegisterSession:

    def __init__(self, client, response, test_case_id, hotel_id, user, cashier_request, x_hotel_id):
        self.hotel_id = hotel_id
        self.response = response
        self.test_data = get_test_case_data(sheet_names.cash_register_sheet_name, test_case_id)[0]
        self.client = client
        self.user = user
        self.cashier_request = cashier_request
        self.x_hotel_id = x_hotel_id

    def validate_create_register_session_response(self, cash_register_id, register_session_id):
        response_data = self.response['data']
        assert_(response_data['cash_register_id'], cash_register_id)
        assert_(response_data['cashier_session_id'], register_session_id)
        assert_(response_data['opening_balance'], ast.literal_eval(self.test_data['expected_default_opening_balance']))
        assert_(response_data['session_number'], self.test_data['expected_session_number'])
        assert_(response_data['status'], self.test_data['status'])
        assert_(response_data['vendor_id'], self.hotel_id)
        if self.user is None:
            assert_(response_data['opened_by'], common_config.AUTOMATION)
        else:
            assert_(response_data['opened_by'], self.user)
        assert_(response_data['closing_balance'], ast.literal_eval(self.test_data['expected_closing_balance']))
        if response_data['status'] == 'closed':
            assert_(response_data['closing_balance_in_base_currency'],
                    sanitize_test_data(self.test_data['expected_closing_balance_in_base_currency']))
            assert response_data['end_datetime'] is not None

    def validate_get_session_response(self, cash_register_id, user_type):
        get_cashier_session_response = self.cashier_request.get_cashier_session_request(self.client, 200, user_type,
                                                                                        self.user, cash_register_id,
                                                                                        self.response['data'][
                                                                                            'cashier_session_id'],
                                                                                        self.x_hotel_id)
        assert_(get_cashier_session_response['data']['cash_register_id'], cash_register_id)
        assert_(get_cashier_session_response['data']['cashier_session_id'], self.response['data']['cashier_session_id'])
        if self.user is None:
            assert_(get_cashier_session_response['data']['opened_by'], common_config.AUTOMATION)
        else:
            assert_(get_cashier_session_response['data']['opened_by'], self.user)
        assert_(get_cashier_session_response['data']['opening_balance'],
                ast.literal_eval(self.test_data['expected_default_opening_balance']))
        assert_(get_cashier_session_response['data']['status'], self.test_data['status'])
        assert_(get_cashier_session_response['data']['vendor_id'], self.hotel_id)
        assert_(get_cashier_session_response['data']['session_number'], self.test_data['expected_session_number'])
        if get_cashier_session_response['data']['status'] == 'closed':
            assert get_cashier_session_response['data']['end_datetime'] is not None
            assert_(get_cashier_session_response['data']['closing_balance_in_base_currency'],
                    self.test_data['expected_closing_balance_in_base_currency'])
