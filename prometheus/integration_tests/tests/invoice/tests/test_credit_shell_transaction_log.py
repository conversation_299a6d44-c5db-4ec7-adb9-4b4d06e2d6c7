import pytest

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_crs_report import ValidationCrsReport
from prometheus.integration_tests.tests.invoice.validations.validation_credit_shell_transaction_log import \
    ValidationCreditShellTransactionLog
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.integration_tests.utilities.csv_utils import read_invoice_details_from_csv, delete_invoice_csv


class TestCreditShellTransactionLog(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, ,error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, is_credit_shell_id_provided, "
        "actions_after_credit_shell_generated", [
            ("CreditShellTransactionLog_01", 'Provide invalid credit shell id', "", 404, None, "04010007",
             "Aggregate: CreditNote with id: INVALID missing.", "", "", "", False, True, ""),
            ("CreditShellTransactionLog_02", 'Provide credit shell id as NULL', "", 404, None, "04010007",
             "Aggregate: CreditNote with id: None missing.", "", "", "", False, True, ""),
            ("CreditShellTransactionLog_03", 'Provide empty credit shell id', "", 404, None, 404, "Exception occurred.",
             "", "", "", False, True, ""),
            ("CreditShellTransactionLog_04", 'Check Transaction Log after reissue invoice', Credit_Shell_01, 200, None,
             "", "", "", "", "", False, False, ""),
            ("CreditShellTransactionLog_05",
             'Check Transaction Log after reissue invoice and record payment using credit shell', Credit_Shell_01, 200,
             None, "", "", "", "", "", False, False, Record_Payment_With_Credit_Shell),
            ("CreditShellTransactionLog_06",
             'Check Transaction Log after reissue invoice and record payment multiple times using credit shell',
             Credit_Shell_01, 200, None, "", "", "", "", "", False, False, Record_Multiple_Payment_With_Credit_Shell),
            ("CreditShellTransactionLog_07",
             'Check Transaction Log after reissue invoice and record payment on two different booking', Credit_Shell_01,
             200, None, "", "", "", "", "", False, False, Record_Payment_With_Credit_Shell_On_Two_Different_booking),
            ("CreditShellTransactionLog_08",
             'Check Transaction Log after reissue invoice and record payment greater than payment in credit shell',
             Credit_Shell_01, 200, None, "", "", "", "", True,
             "Works Fine before skipping because previous action api fail which is needed case", False,
             Record_Payment_Greater_Than_Credit_Shell),
            ("CreditShellTransactionLog_09",
             'Check Transaction Log after reissue invoice and record payment using credit shell and then change '
             'payment mode', Credit_Shell_01, 200, None, "", "", "", "", "", False, False,
             Record_Payment_With_Credit_Shell_Edit_Payment_Mode),
            ("CreditShellTransactionLog_10",
             'Check Transaction Log after reissue invoice and record payment using credit shell and then increase the '
             'amount', Credit_Shell_01, 200, None, "", "", "", "", "", False, False,
             Record_Payment_With_Credit_Shell_Increase_Amount_Paid),
            ("CreditShellTransactionLog_11",
             'Check Transaction Log after reissue invoice and record payment using credit shell and the decrease the '
             'amount', Credit_Shell_01, 200, None, "", "", "", "", "", False, False,
             Record_Payment_With_Credit_Shell_Decrease_Amount_Paid),
            ("CreditShellTransactionLog_12",
             'Check Transaction Log after reissue invoice and record payment using cash and then change payment_mode '
             'to credit shell', Credit_Shell_01, 200, None, "", "", "", "", "", False, False,
             Record_Payment_With_Cash_Edit_Payment_Mode_To_CreditShell),
            ("CreditShellTransactionLog_13",
             'Check Transaction Log after reissue invoice and record payment using credit shell and then change '
             'payment mode and amount', Credit_Shell_01, 200, None, "", "", "", "", "", False,
             False, Record_Payment_With_Credit_Shell_Edit_Payment_Mode_And_Amount),
            ("CreditShellTransactionLog_14",
             'Check Transaction Log after reissue invoice and record payment using cash and then change payment_mode '
             'to credit shell and amount', Credit_Shell_01, 200, None, "", "", "", "", "", False,
             False, Record_Payment_With_Cash_Edit_Payment_Mode_CreditShell_AND_Amount),
            ("CreditShellTransactionLog_15",
             'Check Transaction Log after reissue invoice and record payment using credit shell and then cancel the '
             'payment', Credit_Shell_01, 200, None, "", "", "", "", "", False, False,
             Record_Payment_With_Credit_Shell_Cancel_That_Payment),
            ("CreditShellTransactionLog_16",
             'Check Transaction Log after reissue invoice and transfer money to new invoice account with credit shell',
             Credit_Shell_02, 200, None, "", "", "", "", "", False, False, ""),
            ("CreditShellTransactionLog_17",
             'Check Transaction Log after reissue invoice, transfer money to new invoice account with credit shell '
             'and record payment using credit shell', Credit_Shell_02, 200, None, "", "", "", "", "", False, False,
             Record_Payment_With_Credit_Shell),
            ("CreditShellTransactionLog_18", 'Check Transaction Log after reissue invoice with payment paid to Treebo',
             Credit_Shell_06, 200, None, "", "", "", "", "", False, False, ""),
        ])
    @pytest.mark.regression
    def test_credit_shell_transaction_log(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                          user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                          skip_message, is_credit_shell_id_provided,
                                          actions_after_credit_shell_generated):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        credit_shell_id = None
        if not is_credit_shell_id_provided:
            credit_shell_id = self.credit_shell_request.get_credit_shells_request(
                client_, 200, self.booking_request.booking_id)['data'][0]['credit_shell_id']

        if actions_after_credit_shell_generated:
            self.common_request_caller(client_, actions_after_credit_shell_generated, hotel_id)

        response = self.credit_shell_request.get_credit_shell_transaction_log_request(client_, status_code,
                                                                                      test_case_id, credit_shell_id)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            query_execute(db_queries.UPDATE_FREE_LATE_CHECKOUT_TIME)
            self.report_requests.crs_report(client_, status_code, hotel_id)
            self.validation(test_case_id, client_, response, credit_shell_id, self.billing_request,
                            self.booking_request.bill_id, self.booking_request.booking_ids,
                            self.booking_request.reference_numbers, hotel_id)
            delete_invoice_csv()
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(test_case_id, client, response, credit_shell_id, billing_request, bill_id, booking_ids,
                   reference_numbers, hotel_id):
        validation = ValidationCreditShellTransactionLog(test_case_id, client, response, credit_shell_id)
        report_validation = ValidationCrsReport(test_case_id, read_invoice_details_from_csv(), booking_ids,
                                                reference_numbers, hotel_id, sheet_names.credit_shells_sheet_name)
        validation.validate_response()
        validation.validate_payments(billing_request, bill_id)
        report_validation.validate_data()
