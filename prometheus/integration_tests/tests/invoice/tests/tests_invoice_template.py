import pytest
from datetime import date, timedelta

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest, query_execute
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.invoice.validations.validation_invoice_template import (
    ValidationInvoiceTemplate,
)


class TestInvoiceTemplate(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, extras, actions_after_night_audit, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message, should_upload",
        [
            ("InvoiceTemplate_01", 'Invoice Template for single day walk-in booking', BEFORE_CHECKOUT_ACTION_01,
             [False, ''], [{'id': "CheckoutV2_01", 'type': 'checkout_v2'}],
             200, None, "", "", "", "", False, "", False),
            ("InvoiceTemplate_02", 'Generate Invoice template where all pending charges to cancel and all pending'
                                   ' allowance to post on 1st day, where booked charges and allowances present',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_71', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_29', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_95', 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_95", 'type': 'checkout_v2'}], [False, ''], "", 200, None, "", "",
             "", "", False, "", False),
            ("InvoiceTemplate_03", 'Generate Invoice template where all pending allowances to cancel all pending'
                                   ' charges to post on 1st day, where booked charges and allowances are present',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_72', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_29', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_96', 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_96", 'type': 'checkout_v2'}], [False, ''], "", 200, None, "", "",
             "", "", False, "", False),
            ("InvoiceTemplate_04", 'Generate Invoice template all pending charges and allowances to cancel on 1st day',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_73', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_29', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_97', 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_97", 'type': 'checkout_v2'}], [False, ''], "", 200, None, "", "",
             "", "", False, "", False),
            ("InvoiceTemplate_05", 'Generate Invoice template which has Cancel allowance, posted allowance, booked'
                                   ' allowance, booked charge',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE_BOOKED_CHARGE +
             [{'id': 'invoicePreview_136', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_29', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_115', 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_115", 'type': 'checkout_v2'}], [False, ''], "", 200, None, "", "",
             "", "", False, "", False),
            ("InvoiceTemplate_06", 'Generate Invoice template for a single guest when "is_advance" is true"',
             SINGLE_BOOKING_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                           {'id': 'invoicePreview_01', 'type': 'preview_invoice'}],
             [False, ''], "", 200, None, "", "", "", "", "", "", False),
            ("InvoiceTemplate_07", 'Generate Invoice template for a single guest when "is_advance" is false',
             SINGLE_BOOKING_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                           {'id': 'invoicePreview_02', 'type': 'preview_invoice'}],
             [False, ''], "", 200, None, "", "", "", "", "", "", False),
            ("InvoiceTemplate_08",
             'Generate Invoice template for all guest of a single room booking where all dues are paid',
             PAST_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                 {'id': 'invoicePreview_08', 'type': 'preview_invoice'}],
             [False, ''], "", 200, None, "", "", "", "", "", "", False),
            ("InvoiceTemplate_09",
             'Generate Invoice template for a booking for 3 days and provide all pending allowances to cancel on 1st'
             ' day, where only booked allowances are present', BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_70', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_10",
             'Generate Invoice template for a booking for 3 days and provide all pending charges to cancel and all '
             'pending allowance to post on 1st day, where booked charges and allowances present',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_71', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_11",
             'Generate Invoice template for a booking for 3 days and provide all pending allowances to cancel all '
             'pending charges to post on 1st day, where booked charges and allowances are present',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_72', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_12",
             'Generate Invoice template for a booking for 3 days and provide all pending charges and allowances to '
             'cancel on 1st day',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_73', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_13",
             'Generate Invoice template for a booking for 3 days and provide all pending charges to post on 1st day,'
             ' where only booked charges present',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'invoicePreview_74', 'type': 'preview_invoice'}],
             [False, ''], "", 200, None, "", "", "", "", "", "", False),
            ("InvoiceTemplate_14",
             'Generate Invoice template for a booking for 3 days and provide all pending allowances to post on 1st day,'
             ' where only booked allowances are present', BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_75', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_15",
             'Generate Invoice template for a booking for 3 days and provide all pending charges and allowances to post'
             'on 1st day', BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_76', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_16",
             'Generate Invoice template for a booking for 3 days and provide charges to post when generating invoice on'
             '2nd day',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}],
             [True, [{'id': 'invoicePreview_80', 'type': 'preview_invoice'}]], "", 200, None, "", "", "", "", "", ""
             , False),
            ("InvoiceTemplate_17",
             'Generate Invoice template for a booking for 3 days and provide allowances to post when generating invoice'
             ' on 2nd day',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}],
             [True, [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                     {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [2, 1]},
                     {'id': 'invoicePreview_81', 'type': 'preview_invoice'}]], "", 200, None, "", "",
             "", "", "", "", False),
            ("InvoiceTemplate_18",
             'Generate Invoice template for a booking for 3 days and and provide charges and allowances to post when'
             ' generating invoice on 2nd day',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}],
             [True, [{'id': 'Create_Expense_01', 'type': 'expense'},
                     {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                     {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [2, 1]},
                     {'id': 'invoicePreview_82', 'type': 'preview_invoice'}]], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_19",
             'Cancel room stay charge and then create expense and post the charge, then generate Invoice template',
             [{'id': "booking_01_invoice_preview_83", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '1'},
              {'id': 'invoicePreview_83', 'type': 'preview_invoice'}], [False, ""], "", 200, None, "", "", "", "",
             "", "", False),
            ("InvoiceTemplate_20", 'Generate Invoice template for a booking which on checkout date has no booked '
                                   'charge and no booked allowance',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}],
             [True, [{'id': 'invoicePreview_117', 'type': 'preview_invoice'}]], "", 200, None, "", "", "", "", "", "",
             False),
            ("InvoiceTemplate_21",
             'Generate Invoice template for a booking which on checkout date has booked charge and no booked allowance,'
             ' and post the charge',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'}],
             [True, [{'id': 'invoicePreview_121', 'type': 'preview_invoice'}]], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_22", 'Generate Invoice template for a booking which on checkout date has no booked charge'
                                   ' and booked allowance, and post the allowance',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}],
             [True, [{'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
                     {'id': 'invoicePreview_122', 'type': 'preview_invoice'}]], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_23",
             'Generate Invoice template for a booking which has Cancel charge, posted charge, booked charge, and post them',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE +
             [{'id': 'invoicePreview_129', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_24",
             'Generate Invoice template for a booking which has Cancel allowance, posted allowance, booked allowance '
             'and post them',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_130', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_25", 'Generate Invoice template for a booking which has Cancel extra charge, booked '
                                   'charge and post them',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'},
              {'id': 'invoicePreview_131', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_26",
             'Generate Invoice template for a booking which has Cancel charge, booked charge, booked allowance'
             ' and post them',
             BOOKING_WITH_CANCEL_BOOKED_CHARGE_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_132', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_27", 'Generate Invoice template for a booking which has Cancel allowance, booked'
                                   ' allowance and post them', BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_133', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_28",
             'Generate Invoice template for a booking which has Cancel allowance, booked charge, booked allowance'
             ' and post them', BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE_BOOKED_CHARGE +
             [{'id': 'invoicePreview_134', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_29",
             'Generate Invoice template for a booking which has Cancel charge, posted charge, booked charge, booked'
             ' allowance and post them', BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_135', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_30",
             'Generate Invoice template for a booking which has Cancel allowance, posted allowance, booked allowance,'
             ' booked charge and post them', BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE_BOOKED_CHARGE +
             [{'id': 'invoicePreview_136', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", False),
            ("InvoiceTemplate_31", 'Generate Invoice template where all pending charges to cancel and all pending'
                                   ' allowance to post on 1st day, where booked charges and allowances present'
                                   ' with should_upload as true',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_71', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_29', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_95', 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_95", 'type': 'checkout_v2'}], [False, ''], "", 200, None, "", "",
             "", "", False, "", True),
            ("InvoiceTemplate_32", 'Generate Invoice template where all pending allowances to cancel all pending'
                                   ' charges to post on 1st day, where booked charges and allowances are present'
                                   ' with should_upload as true',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_72', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_29', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_96', 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_96", 'type': 'checkout_v2'}], [False, ''], "", 200, None, "", "",
             "", "", False, "", True),
            ("InvoiceTemplate_33", 'Generate Invoice template all pending charges and allowances to cancel on 1st day'
                                   ' with should_upload as true',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_73', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_29', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_97', 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_97", 'type': 'checkout_v2'}], [False, ''], "", 200, None, "", "",
             "", "", False, "", True),
            ("InvoiceTemplate_34", 'Generate Invoice template which has Cancel allowance, posted allowance, booked'
                                   ' allowance, booked charge with should_upload as true',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE_BOOKED_CHARGE +
             [{'id': 'invoicePreview_136', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_29', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_115', 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_115", 'type': 'checkout_v2'}], [False, ''], "", 200, None, "", "",
             "", "", False, "", True),
            ("InvoiceTemplate_35", 'Generate Invoice template for a single guest when "is_advance" is true'
                                   ' with should_upload as true',
             SINGLE_BOOKING_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                           {'id': 'invoicePreview_01', 'type': 'preview_invoice'}],
             [False, ''], "", 200, None, "", "", "", "", "", "", True),
            ("InvoiceTemplate_36", 'Generate Invoice template for a single guest when "is_advance" is false'
                                   ' with should_upload as true',
             SINGLE_BOOKING_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                           {'id': 'invoicePreview_02', 'type': 'preview_invoice'}],
             [False, ''], "", 200, None, "", "", "", "", "", "", True),
            ("InvoiceTemplate_37",
             'Generate Invoice template for all guest of a single room booking where all dues are paid'
             ' with should_upload as true',
             PAST_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                 {'id': 'invoicePreview_08', 'type': 'preview_invoice'}],
             [False, ''], "", 200, None, "", "", "", "", "", "", True),
            ("InvoiceTemplate_38",
             'Generate Invoice template for a booking for 3 days and provide all pending allowances to cancel on 1st'
             ' with should_upload as true'
             ' day, where only booked allowances are present', BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_70', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_39",
             'Generate Invoice template for a booking for 3 days and provide all pending charges to cancel and all '
             'pending allownace to post on 1st day, where booked charges and allowances present'
             ' with should_upload as true',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_71', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_40",
             'Generate Invoice template for a booking for 3 days and provide all pending allowances to cancel all '
             'pending charges to post on 1st day, where booked charges and allowances are present'
             ' with should_upload as true',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_72', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_41",
             'Generate Invoice template for a booking for 3 days and provide all pending charges and allowances to '
             'cancel on 1st day with should_upload as true',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_73', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_42",
             'Generate Invoice template for a booking for 3 days and provide all pending charges to post on 1st day,'
             ' where only booked charges present with should_upload as true',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'invoicePreview_74', 'type': 'preview_invoice'}],
             [False, ''], "", 200, None, "", "", "", "", "", "", True),
            ("InvoiceTemplate_43",
             'Generate Invoice template for a booking for 3 days and provide all pending allowances to post on 1st day,'
             ' where only booked allowances are present with should_upload as true',
             BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE + [{'id': 'invoicePreview_75', 'type': 'preview_invoice'}],
             [False, ''], "", 200, None, "", "", "", "", "", "", True),
            ("InvoiceTemplate_44",
             'Generate Invoice template for a booking for 3 days and provide all pending charges and allowances to post'
             'on 1st day  with should_upload as true', BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_76', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_45",
             'Generate Invoice template for a booking for 3 days and provide charges to post when generating invoice on'
             '2nd day with should_upload as true',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}],
             [True, [{'id': 'invoicePreview_80', 'type': 'preview_invoice'}]], "", 200, None, "", "", "", "", "", "",
             True),
            ("InvoiceTemplate_46",
             'Generate Invoice template for a booking for 3 days and provide allowances to post when generating invoice'
             ' on 2nd day with should_upload as true',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}],
             [True, [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                     {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [2, 1]},
                     {'id': 'invoicePreview_81', 'type': 'preview_invoice'}]], "", 200, None, "", "",
             "", "", "", "", True),
            ("InvoiceTemplate_47",
             'Generate Invoice template for a booking for 3 days and and provide charges and allowances to post when'
             ' generating invoice on 2nd day with should_upload as true',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}],
             [True, [{'id': 'Create_Expense_01', 'type': 'expense'},
                     {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                     {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [2, 1]},
                     {'id': 'invoicePreview_82', 'type': 'preview_invoice'}]], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_48",
             'Cancel room stay charge and then create expense and post the charge, then generate Invoice template'
             ' with should_upload as true',
             [{'id': "booking_01_invoice_preview_83", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '1'},
              {'id': 'invoicePreview_83', 'type': 'preview_invoice'}], [False, ""], "", 200, None, "", "", "", "", "",
             "", True),
            ("InvoiceTemplate_49",
             'Generate Invoice template for a booking which on checkout date has no booked charge and no booked'
             ' allowance with should_upload as true',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}],
             [True, [{'id': 'invoicePreview_117', 'type': 'preview_invoice'}]], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_50",
             'Generate Invoice template for a booking which on checkout date has booked charge and no booked allowance,'
             ' and post the charge with should_upload as true',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'}],
             [True, [{'id': 'invoicePreview_121', 'type': 'preview_invoice'}]], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_51", 'Generate Invoice template for a booking which on checkout date has no booked charge'
                                   ' and booked allowance, and post the allowance with should_upload as true',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}],
             [True, [{'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
                     {'id': 'invoicePreview_122', 'type': 'preview_invoice'}]], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_52",
             'Generate Invoice template for a booking which has Cancel charge, posted charge, booked charge, and post'
             ' them with should_upload as true', BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE +
             [{'id': 'invoicePreview_129', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_53",
             'Generate Invoice template for a booking which has Cancel allowance, posted allowance, booked allowance '
             'and post them  with should_upload as true',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_130', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_54", 'Generate Invoice template for a booking which has Cancel extra charge, booked '
                                   'charge and post them with should_upload as true',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'},
              {'id': 'invoicePreview_131', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_55",
             'Generate Invoice template for a booking which has Cancel charge, booked charge, booked allowance'
             ' and post them with should_upload as true',
             BOOKING_WITH_CANCEL_BOOKED_CHARGE_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_132', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_56", 'Generate Invoice template for a booking which has Cancel allowance, booked'
                                   ' allowance and post them with should_upload as true',
             BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE + [{'id': 'invoicePreview_133', 'type': 'preview_invoice'}],
             [False, ''], "", 200, None, "", "", "", "", "", "", True),
            ("InvoiceTemplate_57",
             'Generate Invoice template for a booking which has Cancel allowance, booked charge, booked allowance'
             ' and post them  with should_upload as true', BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE_BOOKED_CHARGE +
             [{'id': 'invoicePreview_134', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_58",
             'Generate Invoice template for a booking which has Cancel charge, posted charge, booked charge, booked'
             ' allowance and post them  with should_upload as true',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_135', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_59",
             'Generate Invoice template for a booking which has Cancel allowance, posted allowance, booked allowance,'
             ' booked charge and post them with should_upload as true',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE_BOOKED_CHARGE +
             [{'id': 'invoicePreview_136', 'type': 'preview_invoice'}], [False, ''], "", 200, None,
             "", "", "", "", "", "", True),
            ("InvoiceTemplate_60", 'Invoice Template for a room having different checkout dates',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'},
              {'id': "checkin_preview_invoice_137", 'type': 'checkin_v2'},
              {'id': "invoicePreview_checkout_130_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentV2_checkout_125", 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_01", 'type': 'checkout_v2'}], [False, ''], None, 200, None, "", "", "", "", False,
             "", False),
        ])
    @pytest.mark.regression
    def test_invoice_template(self, client_, test_case_id, tc_description, previous_actions, extras,
                              actions_after_night_audit, status_code, user_type, error_code, error_message,
                              dev_message, error_payload, skip_case, skip_message, should_upload):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]
        perform_night_audit, action_after_night_audit = extras[0], extras[1]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            self.common_request_caller(client_, previous_actions, hotel_id)
            self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
            if action_after_night_audit:
                self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        self.booking_request.get_booking_preview_invoices(client_, self.booking_request.booking_id, 200)

        response = self.invoice_template_request.invoice_template(client_, status_code, self.booking_request.bill_id,
                                                                  self.booking_request.invoice_id[-1], test_case_id,
                                                                  should_upload)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, message=error_message,
                                                    dev_message=dev_message, extra_payload=error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, hotel_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, hotel_id):
        validation = ValidationInvoiceTemplate(response, test_case_id, hotel_id)
        validation.validate_response()
        validation.validate_charge()
        validation.validate_payment()


class TestInvoiceTemplateWithDifferentTaxConfig(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, extras, actions_after_night_audit, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message, should_upload, "
        "has_slab_based_taxation, is_tax_clubbed, show_allowance_as_separate_line_item",
        [
            ("InvoiceTemplateWithDifferentTaxConfig_01", 'Generate Invoice template without clubbed taxation and'
                                                         ' allowance', BOOKING_WITH_EXTRA_CHARGE, [False, ''], "", 200,
             None, "", "", "", "", False, "", False, False, [], []),
            ("InvoiceTemplateWithDifferentTaxConfig_02", 'Generate Invoice template without clubbed taxation and'
                                                         ' contains allowance', BOOKING_WITH_EXTRA_CHARGE_ALLOWANCE,
             [False, ''], "", 200, None, "", "", "", "", False, "", False, False, [],
             NOT_SHOW_ALLOWANCE_AS_SEPARATE_LINE_ITEM),
            ("InvoiceTemplateWithDifferentTaxConfig_03", 'Generate Invoice template with clubbed taxation without'
                                                         ' allowance', BOOKING_WITH_EXTRA_CHARGE_CLUBBED_TAX,
             [False, ''], "", 200, None, "", "", "", "", False, "", False, False, CLUBBED_TAX_CONFIG, []),
            ("InvoiceTemplateWithDifferentTaxConfig_04", 'Generate Invoice template with clubbed taxation having '
                                                         'allowance', BOOKING_WITH_EXTRA_CHARGE_ALLOWANCE_CLUBBED_TAX,
             [False, ''], "", 200, None, "", "", "", "", False, "", False, False, CLUBBED_TAX_CONFIG,
             NOT_SHOW_ALLOWANCE_AS_SEPARATE_LINE_ITEM),
            ("InvoiceTemplateWithDifferentTaxConfig_05", 'Generate Invoice template with clubbed taxation and allowance'
                                                         ' as separate line item',
             BOOKING_WITH_EXTRA_CHARGE_ALLOWANCE_CLUBBED_TAX, [False, ''], "", 200, None, "", "", "", "", False, "",
             False, False, CLUBBED_TAX_CONFIG, []),
            ("InvoiceTemplateWithDifferentTaxConfig_06", 'Generate Invoice template without clubbed taxation and slab'
                                                         ' based taxation',
             BOOKING_WITH_EXTRA_CHARGE_SLAB_BASED_TAX, [False, ''], "", 200, None, "", "", "", "", False, "",
             False, True, [], NOT_SHOW_ALLOWANCE_AS_SEPARATE_LINE_ITEM),
            ("InvoiceTemplateWithDifferentTaxConfig_07", 'Generate Invoice template without clubbed taxation and slab'
                                                         ' based taxation and allowance as separate line item',
             BOOKING_WITH_EXTRA_CHARGE_SLAB_BASED_TAX, [False, ''], "", 200, None, "", "", "", "", True, "Need to fix",
             False, True, [], []),
            ("InvoiceTemplateWithDifferentTaxConfig_08", 'Generate Invoice template with clubbed taxation, allowance on '
                                                         'room stay charge',
             BOOKING_WITH_EXTRA_CHARGE_AND_ALLOWANCE_ON_ROOM, [False, ''], "", 200, None, "", "", "", "", False, "",
             False, False, CLUBBED_TAX_CONFIG, NOT_SHOW_ALLOWANCE_AS_SEPARATE_LINE_ITEM),
            ("InvoiceTemplateWithDifferentTaxConfig_09", 'Generate Invoice template with clubbed taxation, allowance on '
                                                         'room stay charge as separate line item',
             BOOKING_WITH_EXTRA_CHARGE_AND_ALLOWANCE_ON_ROOM, [False, ''], "", 200, None, "", "", "", "", False, "",
             False, False, CLUBBED_TAX_CONFIG, []),
        ])
    @pytest.mark.regression
    def test_invoice_template(self, client_, test_case_id, tc_description, previous_actions, extras,
                              actions_after_night_audit, status_code, user_type, error_code, error_message,
                              dev_message, error_payload, skip_case, skip_message, should_upload,
                              has_slab_based_taxation, is_tax_clubbed, show_allowance_as_separate_line_item):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        self.common_request_caller(client_, previous_actions, hotel_id)
        self.booking_request.get_booking_preview_invoices(client_, self.booking_request.booking_id, 200)

        response = self.invoice_template_request.invoice_template(client_, status_code, self.booking_request.bill_id,
                                                                  self.booking_request.invoice_id[-1], test_case_id,
                                                                  should_upload, has_slab_based_taxation,
                                                                  is_tax_clubbed, show_allowance_as_separate_line_item)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, message=error_message,
                                                    dev_message=dev_message, extra_payload=error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, hotel_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, hotel_id):
        validation = ValidationInvoiceTemplate(response, test_case_id, hotel_id)
        validation.validate_response()
        validation.validate_charge()
        validation.validate_payment()
