import pytest
from pos.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.invoice.validations.validation_reverse_checkout import ValidationReverseCheckout
from prometheus.integration_tests.config.error_messages import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.integration_tests.config.common_config import *


class TestReverseCheckout(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions,reissue_invoice_test_case_id, tc_description, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, skip_message",
        [
            ("ReverseCheckout_01", REVERSE_CHECKOUT_01, "",
             "Reverse the booking checkout with single invoices", 200, 'super-admin', "",
             "", "", "", ""),
            ("ReverseCheckout_03", REVERSE_CHECKOUT_03, "",
             "Reverse the early checked-out room and validate inventory", 200, 'super-admin', "", "", "", "",
             "Please send prices only for the room stay dates where occupancy or room type has changed, and existing"
             " charges hasn't been consumed"),
            ("ReverseCheckout_04", REVERSE_CHECKOUT_03, "",
             "CREATE_ALLOTMENT Reverse of checkout for a early checked_out booking where same room allotment is not"
             " available", 200, 'super-admin', "", "", "", "",
             "Please send prices only for the room stay dates where occupancy or room type has changed, and "
             "existing charges hasn't been consumed"),
            ("ReverseCheckout_08", REVERSE_CHECKOUT_08, "",
             "Reverse the checked-out guest in multi-guest room where booking is in partially checked-out state",
             200, 'super-admin', "", "", "", "",
             "Please send prices only for the room stay dates where occupancy or room type has changed, and "
             "existing charges hasn't been consumed"),
            ("ReverseCheckout_10", REVERSE_CHECKOUT_08, "",
             "Reverse the early checked-out guest in partially checkout room", 200, 'super-admin', "", "", "", "",
             "Please send prices only for the room stay dates where occupancy or room type has changed, and existing"
             " charges hasn't been consumed"),
            ("ReverseCheckout_11", REVERSE_CHECKOUT_08, "",
             "Reverse the checked-out guest where stay-dates have been changed in part-checkout booking", 200,
             'super-admin', "", "", "", "", "Please send prices only for the room stay dates where occupancy or room"
                                            " type has changed, and existing charges hasn't been consumed"),
            ("ReverseCheckout_15", REVERSE_CHECKOUT_08, "",
             "Reverse the action which is not the latest(if booking is going from part-checkout to checkout)", 200,
             'super-admin', "", "", "", "", "Please send prices only for the room stay dates where occupancy or room"
                                            " type has changed, and existing charges hasn't been consumed"),
            ("ReverseCheckout_16", REVERSE_CHECKOUT_16, "", "Reverse the checked-out for fdm", 200, FDM, "", "", "",
             "", ""),
            ("ReverseCheckout_17", REVERSE_CHECKOUT_08, "", "Reverse the part-checkout for fdm", 200, FDM, "", "", "",
             "", "Please send prices only for the room stay dates where occupancy or room type has changed, and"
                 " existing charges hasn't been consumed"),
            ("ReverseCheckout_18", REVERSE_CHECKOUT_08, "", "Reverse the part-checkout for cr-team", 200, CR_TEAM, "",
             "", "", "", "Please send prices only for the room stay dates where occupancy or room type has changed,"
                         " and existing charges hasn't been consumed"),
            ("ReverseCheckout_19", REVERSE_CHECKOUT_03, "", "Reverse the early checkout for cr-team", 200, CR_TEAM, "",
             "", "", "", "Please send prices only for the room stay dates where occupancy or room type has changed,"
                         " and existing charges hasn't been consumed"),
            ("ReverseCheckout_20", REVERSE_CHECKOUT_16, "", "Reverse the checked-out for cr-team after checkout time",
             200, CR_TEAM, "", "", "", "", ""),
            ("ReverseCheckout_22", REVERSE_CHECKOUT_22, "",
             "Reverse the checkout for a single BTC booking without reissue invoices ", 200, 'super-admin', "", "", "",
             "", ""),
            ("ReverseCheckout_23", REISSUE_INVOICE_01, "",
             "(Reverse CO and Reverse CI) Perform reverse checkin and reverse Checkout on the same booking", 403,
             'super-admin', "04011029", "Reversal of CheckIn is not allowed after first invoice generation", "", "",
             ""),
            ("ReverseCheckout_24", REISSUE_INVOICE_01, "Reissue_Invoice_01",
             "(ReissueInvoice)Perform Reverse Checkout after reissuing invoice", 403, 'super-admin', "04011030",
             "Reversal of CheckOut is not allowed after generation of credit note upon invoices", "", "", "Gives 500"),
            ("ReverseCheckout_25", ReverseCheckout_25, "",
             "Reverse the checkout for a single 2 charges BTC booking", 200, 'super-admin', "", "", "", "", ""),
            ("ReverseCheckout_26", ReverseCheckout_26, "",
             "Reverse a part checkout booking.Booking type is 1 day 2 room booking, one is in checked_in state and "
             "another one is in checked_out state ",
             200, 'super-admin', "", "", "", "", "Needs to fix the inventory logic"),
        ])
    @pytest.mark.regression
    def test_reverse_checkout(self, client_, test_case_id, previous_actions, reissue_invoice_test_case_id,
                              tc_description, status_code, user_type,
                              error_code, error_message, dev_message, error_payload, skip_message):
        if skip_message:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        if status_code in SUCCESS_CODES:
            before_reverse_checkout_booking_response = self.booking_request.booking_response
            before_reverse_checkout_billing_response = self.billing_request.billing_response
            before_reverse_checkout_charges = self.billing_request.charges
            before_reverse_checkout_booking_invoices = self.booking_request.booking_invoices
            before_reverse_checkout_inventory = self.inventory_request.get_room_wise_inventory(client_, 200,
                                                                                               before_reverse_checkout_booking_response)
        if "CREATE_ALLOTMENT" in tc_description:
            self.create_allotment(before_reverse_checkout_booking_response)
        if "Reverse CI" in tc_description:
            self.booking_request.delete_booking_action(client_, 200)
            self.booking_request.set_action_id(client_, 'checkin')
            response = self.booking_request.delete_booking_action(client_, status_code)
        elif "ReissueInvoice" in tc_description:
            invoice_id_bill_to_type_mapping = self.booking_request.get_invoice_id_bill_to_type_mapping(client_,
                                                                                                       user_type,
                                                                                                       hotel_id)
            self.booking_request.reissue_invoice(client_, test_case_id=reissue_invoice_test_case_id,
                                                 status_code=201, user_type=user_type,
                                                 invoice_id_map=invoice_id_bill_to_type_mapping)
            response = self.booking_request.delete_booking_action(client_, status_code,hotel_id=hotel_id)
        else:
            response = self.booking_request.delete_booking_action(client_, status_code, hotel_id=hotel_id)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, message=error_message,
                                                    dev_message=dev_message, extra_payload=error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, user_type, self.booking_request.booking_id,
                            before_reverse_checkout_charges,
                            before_reverse_checkout_booking_response,
                            before_reverse_checkout_billing_response, self.booking_request, self.billing_request,
                            before_reverse_checkout_booking_invoices, before_reverse_checkout_inventory,
                            self.inventory_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, user_type, booking_id, before_reverse_checkout_charges,
                   before_checkout_booking_response, before_reverse_checkout_billing_response,
                   booking_request, billing_request, before_reverse_checkout_booking_invoices,
                   before_reverse_checkout_inventory, inventory_request, action_type='checkout'):
        validation = ValidationReverseCheckout(client_, response=response, test_case_id=test_case_id,
                                               user_type=user_type, booking_id=booking_id,
                                               booking_request=booking_request,
                                               before_checkout_booking_response=before_checkout_booking_response)
        validation.validate_response(booking_request=booking_request, action_type=action_type)
        validation.validate_bill(billing_request, before_reverse_checkout_billing_response,
                                 before_reverse_checkout_charges, before_reverse_checkout_booking_invoices)
        validation.validate_inventory(before_reverse_checkout_inventory, inventory_request)
        validation.validate_booking_response()

    @staticmethod
    def create_allotment(before_checkout_booking_response):
        start_time = before_checkout_booking_response['room_stays'][0]['actual_checkin_date']
        expected_end_time = before_checkout_booking_response['room_stays'][0]['checkout_date']
        room_id = str(before_checkout_booking_response['room_stays'][0]['room_allocation']['room_id'])
        allotment_id = 'RIA-120219-999999-999-9999'
        query = db_queries.INSERT_ROOM_ALLOTMENT.format(room_id=room_id, start_time=start_time,
                                                        expected_end_time=expected_end_time, allotment_id=allotment_id)
        query_execute(query)
