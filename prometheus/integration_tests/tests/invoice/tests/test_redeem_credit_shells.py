import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.invoice.validations.validation_redeem_credit_shell import \
    ValidationRedeemCreditShell


class TestRedeemCreditShells(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, "
        "error_message, dev_message, error_payload, skip_message",
        [
            ("RedeemCreditShells_01", SINGLE_WALK_BOOKING_V2_01, "Provide Invalid Credit shell id", 404, "super-admin",
             "04010007", "Aggregate: CreditNote with id: Invalid missing.", "", "", ""),
            ("RedeemCreditShells_02", SINGLE_WALK_BOOKING_V2_01, "Provide credit shell id as null", 400, "super-admin",
             "04010006", "[Credit Shell Id] -> Field may not be null.", "", {'field': 'credit_shell_id'}, ""),
            ("RedeemCreditShells_03", Credit_Shell_01, "Provide Invalid payment mode", 400, "super-admin", "04010007",
             "", "", "", "Need to check"),
            ("RedeemCreditShells_04", Credit_Shell_01, "Provide payment mode as null", 400, "super-admin", "04010006",
             "[Payment Mode] -> Field may not be null.", "", {'field': 'payment_mode'}, ""),
            ("RedeemCreditShells_05", Credit_Shell_01, "Provide amount as null", 400, "super-admin", "04010006",
             "[Amount] -> Field may not be null.", "", {'field': 'amount'}, ""),
            ("RedeemCreditShells_06", Credit_Shell_01, "Provide amount more than present in credit shell", 400,
             "super-admin", "04010419", "Amount cannot be larger than credit shell balance", "", "", ""),
            ("RedeemCreditShells_07", Credit_Shell_01, "Provide amount without currency", 400, "super-admin",
             "04010421", "Credit Shell Currency is not matching with current bill", "", "", ""),
            ("RedeemCreditShells_08", Credit_Shell_01, "Does not provide remarks in payload", 200, "super-admin", "",
             "", "", "", ""),
            ("RedeemCreditShells_09", Credit_Shell_01, "Redeem credit shell, with correct credit shell id", 200, 
             "super-admin", "", "", "", "", ""),
        ])
    @pytest.mark.regression
    def test_redeem_credit_shells(self, client_, test_case_id, previous_actions, tc_description, status_code,
                                  user_type, error_code, error_message, dev_message, error_payload, skip_message):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        self.credit_shell_request.credit_shell_id = None

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        if self.booking_request.booking_id:
            self.credit_shell_request.get_credit_shells_request(client_, 200, self.booking_request.booking_id)

        response = self.credit_shell_request.redeem_credit_shells_request(client_, status_code, test_case_id,
                                                                          self.credit_shell_request.credit_shell_id)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, self.credit_shell_request.credit_shell_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, credit_shell_id):
        validation = ValidationRedeemCreditShell(response, test_case_id, credit_shell_id)
        validation.validate_response()
