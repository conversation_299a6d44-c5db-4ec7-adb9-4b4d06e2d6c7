import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import assert_, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationInvoiceTemplate:
    def __init__(self, response, test_case_id, hotel_id):
        self.test_data = get_test_case_data(sheet_names.invoice_template_sheet_name, test_case_id)[0]
        self.response = response
        self.hotel_id = hotel_id

    def validate_response(self):
        actual_bill_summary = self.response['data']['template']['bill_summary']['summary']
        expected_bill_summary = json.loads(self.test_data['expected_bill_summary'])
        BaseValidations().validate_bill_summary(actual_bill_summary, expected_bill_summary, self.hotel_id)
        actual_booking_details = self.response['data']['template']['booking']
        expected_booking_details = json.loads(self.test_data['expected_booking_details'])
        assert_(actual_booking_details['checkin_date'][0:10], str(return_date(expected_booking_details['checkin_date'])))
        assert_(actual_booking_details['checkout_date'][0:10], str(return_date(expected_booking_details['checkout_date'])))

    def validate_charge(self):
        expected_charge = json.loads(self.test_data['expected_charge_details'])
        expected_charge = sorted(expected_charge, key=lambda i: i['guests'][0]['customer_id'])
        for ci_index, charge_items in enumerate(sorted(self.response['data']['template']['charge_items'],
                                                       key=lambda i: i['guests'][0]['customer_id'])):
            for cd_index, charge_details in enumerate(sorted(charge_items['charges'],
                                                             key=lambda i: (i['posttax_amount']))):
                expected_charge_details = sorted(expected_charge[ci_index]['charges'], key=lambda
                    i: (i['posttax_amount']))[cd_index]
                assert_(charge_details['applicable_date'], str(return_date(expected_charge_details['applicable_date'])))
                if charge_details['posting_date']:
                    assert_(charge_details['posting_date'], str(return_date(expected_charge_details['posting_date'])))
                else:
                    assert_(charge_details['posting_date'], expected_charge_details['posting_date'])
                assert_(charge_details['charge_category'], expected_charge_details['charge_category'])
                assert_(charge_details['description'], expected_charge_details['description'])
                assert_(charge_details['posttax_amount'], expected_charge_details['posttax_amount'])
                assert_(charge_details['pretax_amount'], expected_charge_details['pretax_amount'])
                assert_(charge_details['tax'], expected_charge_details['tax'])
                assert_(charge_details['tax_breakup'], expected_charge_details['tax_breakup'])

    def validate_payment(self):
        for pd_index, payment_details in enumerate(self.response['data']['template']['payments']):
            expected_payment_details = json.loads(self.test_data['expected_payment_details'])[pd_index]
            assert_(payment_details['amount'], expected_payment_details['amount'])
            assert_(payment_details['comment'], expected_payment_details['comment'])
            assert_(payment_details['date_of_payment'], str(return_date(expected_payment_details['date_of_payment'])))
            assert_(payment_details['payment_mode'], expected_payment_details['payment_mode'])
            assert_(payment_details['payment_type'], expected_payment_details['payment_type'])
