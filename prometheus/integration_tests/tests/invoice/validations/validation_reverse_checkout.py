import json
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.resources.db_queries import GET_ALL_ACCOUNT, GET_SPECIFIC_ACCOUNT, GET_INVOICE, \
    GET_INVOICE_CHARGES
from prometheus.integration_tests.utilities.common_utils import assert_, return_date, sanitize_blank, query_execute, \
    convert_2d_list_to_1d_list,get_current_year_short
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from unittest.case import TestCase


class ValidationReverseCheckout:

    def __init__(self, client_, test_case_id, response, user_type, booking_id, booking_request,
                 before_checkout_booking_response):
        self.test_data = get_test_case_data(sheet_names.correction_mode_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.user_type = user_type
        self.booking_id = booking_id
        self.booking_request = booking_request
        self.before_checkout_booking_response = before_checkout_booking_response

    def validate_response(self, booking_request, action_type):
        assert_(self.response['data']['action_id'], booking_request.action_id)
        assert_(self.response['data']['action_type'], action_type)
        assert_(self.response['data']['reversal'], 'irreversible')
        assert_(self.response['data']['status'], 'reversed')

    def validate_bill(self, billing_request, before_reverse_checkout_billing_response,
                      before_reverse_checkout_charges, before_reverse_checkout_booking_invoices):
        bill_credit_notes = billing_request.get_credit_notes(self.client,
                                                             before_reverse_checkout_billing_response['bill_id'],
                                                             200, self.user_type)
        after_reverse_checkout_booking_invoices = \
            self.booking_request.get_booking_invoices(self.client, self.booking_id, 200,
                                                      user_type=self.user_type)['data']
        self.validate_invoices(before_reverse_checkout_booking_invoices, after_reverse_checkout_booking_invoices,
                               bill_credit_notes)
        after_reverse_checkout_billing_response = \
            billing_request.get_bill_request(self.client, self.booking_request.bill_id, 200, self.user_type)['data']
        if "AddChargesAfterCheckout" not in self.test_data['TC_Description']:
            assert_(before_reverse_checkout_billing_response['total_posttax_amount'],
                    after_reverse_checkout_billing_response['total_posttax_amount'])
            # assert_(before_reverse_checkout_billing_response['total_pretax_amount'],after_reverse_checkout_billing_response['total_pretax_amount'])
            # assert_(before_reverse_checkout_billing_response['total_tax_amount'],after_reverse_checkout_billing_response['total_tax_amount'])
            assert_(before_reverse_checkout_billing_response['net_paid_amount'],
                    after_reverse_checkout_billing_response['net_paid_amount'])
            assert_(before_reverse_checkout_billing_response['net_payable'],
                    after_reverse_checkout_billing_response['net_payable'])
            # assert_(before_reverse_checkout_billing_response['paid_amount'],
            #         after_reverse_checkout_billing_response['paid_amount'])
            assert_(before_reverse_checkout_billing_response['parent_reference_number'],
                    after_reverse_checkout_billing_response['parent_reference_number'])
            # assert [i for i in before_reverse_checkout_billing_response['payments'] if
            #         i not in after_reverse_checkout_billing_response['payments']] == []
            if sanitize_blank(self.test_data['expected_bill_charges']):
                fetched_bill_charges = []
                for bill_charge in after_reverse_checkout_billing_response['charges']:
                    charge = {
                        'posttax_amount': bill_charge['posttax_amount'],
                        'status': bill_charge['status'],
                        'item_id': bill_charge['item']['item_id'],
                        'name': bill_charge['item']['name']
                    }
                    fetched_bill_charges.append(charge)
                # assert [i for i in fetched_bill_charges if
                #         i not in sanitize_blank(json.loads(self.test_data['expected_bill_charges']))] == []

    def validate_invoices(self, before_reverse_checkout_booking_invoices, after_reverse_checkout_booking_invoices,
                          bill_credit_notes):
        print(before_reverse_checkout_booking_invoices)
        print(after_reverse_checkout_booking_invoices)
        for before_reverse_checkout_booking_invoice in before_reverse_checkout_booking_invoices['data']:
            invoice_id = before_reverse_checkout_booking_invoice['invoice_id']
            for after_reverse_checkout_booking_invoice in after_reverse_checkout_booking_invoices:
                if after_reverse_checkout_booking_invoice['invoice_id'] == invoice_id:
                    assert_(before_reverse_checkout_booking_invoice['allowed_charge_types'],
                            after_reverse_checkout_booking_invoice['allowed_charge_types'])
                    assert_(before_reverse_checkout_booking_invoice['bill_id'],
                            after_reverse_checkout_booking_invoice['bill_id'])
                    assert_(before_reverse_checkout_booking_invoice['bill_to'],
                            after_reverse_checkout_booking_invoice['bill_to'])
                    assert_(before_reverse_checkout_booking_invoice['bill_to_type'],
                            after_reverse_checkout_booking_invoice['bill_to_type'])
                    assert_(after_reverse_checkout_booking_invoice['credit_note_amount'],
                            before_reverse_checkout_booking_invoice['posttax_amount'])
                    assert_(before_reverse_checkout_booking_invoice['credit_payable'],
                            after_reverse_checkout_booking_invoice['credit_payable'])
                    assert_(before_reverse_checkout_booking_invoice['hotel_invoice_id'],
                            after_reverse_checkout_booking_invoice['hotel_invoice_id'])
                    # assert_(before_reverse_checkout_booking_invoice['invoice_date'],
                    #         after_reverse_checkout_booking_invoice['invoice_date'])
                    assert after_reverse_checkout_booking_invoice['invoice_number'] is not None
                    assert_(before_reverse_checkout_booking_invoice['issued_by'],
                            after_reverse_checkout_booking_invoice['issued_by'])
                    assert_(before_reverse_checkout_booking_invoice['issued_by_type'],
                            after_reverse_checkout_booking_invoice['issued_by_type'])
                    assert_(before_reverse_checkout_booking_invoice['issued_to_type'],
                            after_reverse_checkout_booking_invoice['issued_to_type'])
                    assert_(before_reverse_checkout_booking_invoice['posttax_amount'],
                            after_reverse_checkout_booking_invoice['posttax_amount'])
                    assert_(before_reverse_checkout_booking_invoice['pretax_amount'],
                            after_reverse_checkout_booking_invoice['pretax_amount'])
                    assert_(before_reverse_checkout_booking_invoice['tax_amount'],
                            after_reverse_checkout_booking_invoice['tax_amount'])
                    if before_reverse_checkout_booking_invoice['allowed_charge_tos']:
                        assert_(len(before_reverse_checkout_booking_invoice['allowed_charge_tos']),
                                len(after_reverse_checkout_booking_invoice['allowed_charge_tos']))
                        assert [i for i in before_reverse_checkout_booking_invoice['allowed_charge_tos'] if
                                i not in after_reverse_checkout_booking_invoice['allowed_charge_tos']] == []
                    else:
                        assert after_reverse_checkout_booking_invoice['allowed_charge_tos'] is None
                    assert_(len(before_reverse_checkout_booking_invoice['invoice_charges']),
                            len(after_reverse_checkout_booking_invoice['invoice_charges']))
                    for invoice_charge in before_reverse_checkout_booking_invoice['invoice_charges']:
                        invoice_charge_id = invoice_charge['charge_id']
                        for after_reverse_checkout_invoice_charge in after_reverse_checkout_booking_invoice[
                            'invoice_charges']:
                            if invoice_charge['charge_id'] == after_reverse_checkout_invoice_charge['charge_id']:
                                if invoice_charge['bill_to_type'] == 'guest' and invoice_charge[
                                    'charge_split_ids'] is not None and invoice_charge['charge_split_ids'] == \
                                        after_reverse_checkout_invoice_charge['charge_split_ids']:
                                    assert_(invoice_charge['charge_to_ids'],
                                            after_reverse_checkout_invoice_charge['charge_to_ids'])
                                    assert_(invoice_charge['charge_type'],
                                            after_reverse_checkout_invoice_charge['charge_type'])
                                    assert_(invoice_charge['posttax_amount'],
                                            after_reverse_checkout_invoice_charge['credit_note_generated_amount'])
                                    assert_(invoice_charge['posttax_amount'],
                                            after_reverse_checkout_invoice_charge['posttax_amount'])
                                    assert_(invoice_charge['pretax_amount'],
                                            after_reverse_checkout_invoice_charge['pretax_amount'])
                                    assert_(invoice_charge['tax_amount'],
                                            after_reverse_checkout_invoice_charge['tax_amount'])
                                else:
                                    assert_(after_reverse_checkout_invoice_charge['bill_to_type'], 'company')
                                    assert_(invoice_charge['charge_to_ids'],
                                            after_reverse_checkout_invoice_charge['charge_to_ids'])
                                    assert_(invoice_charge['charge_type'],
                                            after_reverse_checkout_invoice_charge['charge_type'])
                                    assert_(invoice_charge['posttax_amount'],
                                            after_reverse_checkout_invoice_charge['credit_note_generated_amount'])
                                    assert_(invoice_charge['posttax_amount'],
                                            after_reverse_checkout_invoice_charge['posttax_amount'])
                                    assert_(invoice_charge['pretax_amount'],
                                            after_reverse_checkout_invoice_charge['pretax_amount'])
                                    assert_(invoice_charge['tax_amount'],
                                            after_reverse_checkout_invoice_charge['tax_amount'])

    def validate_inventory(self, before_reverse_checkout_inventory, inventory_request):
        after_reverse_checkout_inventory = inventory_request.get_room_wise_inventory(self.client, 200,
                                                                                     self.before_checkout_booking_response)
        if sanitize_blank(self.test_data['room_wise_inventory_changes_new']):
            for room_wise_inventory in json.loads(self.test_data['room_wise_inventory_changes_new']):
                if sanitize_blank(room_wise_inventory['inventory_change']) is None:
                    room_stay_id = int(sanitize_blank(room_wise_inventory['room_stay_id']))
                    assert_(len(after_reverse_checkout_inventory[room_stay_id]),
                            len(before_reverse_checkout_inventory[room_stay_id]))
                else:
                    room_stay_id = int(sanitize_blank(room_wise_inventory['room_stay_id']))
                    change_in_dates = sanitize_blank(room_wise_inventory['date']).split(',')
                    inventory_changes = sanitize_blank(room_wise_inventory['inventory_change']).split(',')
                    for change_in_date, inventory_change in zip(change_in_dates, inventory_changes):
                        date = str(return_date(int(change_in_date)))
                        room_stay_inventory_after_reverse_checkout = after_reverse_checkout_inventory[room_stay_id]
                        room_stay_inventory_before_reverse_checkout = before_reverse_checkout_inventory[room_stay_id]
                        for inventory in room_stay_inventory_after_reverse_checkout:
                            if inventory['date'] == date:
                                inventory['actual_count'] += int(inventory_change)
                        assert_(len(room_stay_inventory_after_reverse_checkout),
                                len(room_stay_inventory_before_reverse_checkout))
                        assert [i for i in room_stay_inventory_before_reverse_checkout if
                                i not in room_stay_inventory_after_reverse_checkout] == []
                        TestCase.assertCountEqual(self, room_stay_inventory_before_reverse_checkout,
                                                  room_stay_inventory_after_reverse_checkout)

    def validate_booking_response(self):
        after_reverse_checkout_booking_response = self.booking_request.get_booking_request(self.client, self.booking_id,
                                                                                           200, self.user_type)['data']
        assert_(self.before_checkout_booking_response['actual_checkin_date'],
                after_reverse_checkout_booking_response['actual_checkin_date'])
        assert_(self.before_checkout_booking_response['actual_checkout_date'],
                after_reverse_checkout_booking_response['actual_checkout_date'])
        assert_(self.before_checkout_booking_response['allowed_actions'],
                after_reverse_checkout_booking_response['allowed_actions'])
        assert_(self.before_checkout_booking_response['bill_id'], after_reverse_checkout_booking_response['bill_id'])
        assert_(self.before_checkout_booking_response['booking_id'],
                after_reverse_checkout_booking_response['booking_id'])
        assert_(self.before_checkout_booking_response['booking_owner_id'],
                after_reverse_checkout_booking_response['booking_owner_id'])
        assert_(self.before_checkout_booking_response['cancellation_datetime'],
                after_reverse_checkout_booking_response['cancellation_datetime'])
        assert_(self.before_checkout_booking_response['cancellation_reason'],
                after_reverse_checkout_booking_response['cancellation_reason'])
        assert_(self.before_checkout_booking_response['checkin_date'],
                after_reverse_checkout_booking_response['checkin_date'])
        assert_(self.before_checkout_booking_response['checkout_date'],
                after_reverse_checkout_booking_response['checkout_date'])
        assert_(len(self.before_checkout_booking_response['customers']),
                len(after_reverse_checkout_booking_response['customers']))
        assert [i for i in self.before_checkout_booking_response['customers'] if
                i not in after_reverse_checkout_booking_response['customers']] == []
        TestCase.assertCountEqual(self, self.before_checkout_booking_response['customers'],
                                  after_reverse_checkout_booking_response['customers'])
        assert_(self.before_checkout_booking_response['extra_information'],
                after_reverse_checkout_booking_response['extra_information'])
        assert_(self.before_checkout_booking_response['hotel_id'], after_reverse_checkout_booking_response['hotel_id'])
        assert_(self.before_checkout_booking_response['reference_number'],
                after_reverse_checkout_booking_response['reference_number'])
        assert_(self.before_checkout_booking_response['status'], after_reverse_checkout_booking_response['status'])
        assert_(self.before_checkout_booking_response['stay_start'],
                after_reverse_checkout_booking_response['stay_start'])
        assert_(self.before_checkout_booking_response['stay_end'], after_reverse_checkout_booking_response['stay_end'])
        for room_stay in after_reverse_checkout_booking_response['room_stays']:
            room_stay_id = room_stay['room_stay_id']
            for before_checkout_room_stay in self.before_checkout_booking_response['room_stays']:
                if before_checkout_room_stay['room_stay_id'] == room_stay_id:
                    assert_(room_stay['actual_checkin_date'], before_checkout_room_stay['actual_checkin_date'])
                    if len(room_stay['allowed_actions']) == len(before_checkout_room_stay['allowed_actions']):
                        assert_(room_stay['allowed_actions'], before_checkout_room_stay['allowed_actions'])
                    assert_(room_stay['checkin_date'], before_checkout_room_stay['checkin_date'])
                    assert_(room_stay['checkout_date'], before_checkout_room_stay['checkout_date'])
                    assert_(room_stay['room_type_id'], before_checkout_room_stay['room_type_id'])
                    assert_(room_stay['status'], before_checkout_room_stay['status'])
                    assert_(room_stay['stay_end'], before_checkout_room_stay['stay_end'])
                    assert_(room_stay['stay_start'], before_checkout_room_stay['stay_start'])
                    assert_(room_stay['type'], before_checkout_room_stay['type'])
                    assert [i for i in room_stay['date_wise_charge_ids'] if
                            i not in before_checkout_room_stay['date_wise_charge_ids']] == []
                    assert_(len(room_stay['date_wise_charge_ids']),
                            len(before_checkout_room_stay['date_wise_charge_ids']))
                    # assert_(room_stay['guest_stays'], before_checkout_room_stay['guest_stays'])

    def validate_account_and_invoice(self, bill_id, hotel_id, locked_accounts):
        invoice_seq_number = hotel_id + get_current_year_short() + "-00000{invoice_number}"
        sequence_number = 0
        for billed_entity_id, account_number in locked_accounts.items():
            sequence_number += 1
            invoice_number = invoice_seq_number.format(invoice_number=sequence_number)
            # validation for account
            account_data_from_db = query_execute(
                GET_SPECIFIC_ACCOUNT.format(bill_id=bill_id, billed_entity_id=billed_entity_id,
                                            account_number=account_number), False).fetchall()
            account_data = convert_2d_list_to_1d_list(account_data_from_db)
            assert_(account_data[0], False)  # assert of deleted
            assert_(account_data[1], False)  # assert of invoiced
            assert_(account_data[2], False)  # assert of locked
            assert_(account_data[3], False)  # assert of allowance account
            assert (account_data[4]['invoice_date'], return_date(0))
            # assert_(account_data[4]['invoice_numbers'][0], invoice_number)

            # validation for invoice
            invoice_data_from_db = query_execute(
                GET_INVOICE.format(invoice_number=account_data[4]['invoice_numbers'][0],
                                   billed_entity_id=billed_entity_id,
                                   billed_entity_account_number=account_number, bill_id=bill_id), False).fetchall()
            invoice_data = convert_2d_list_to_1d_list(invoice_data_from_db)
            assert_(invoice_data[0], False)  # assert of deleted
            assert (invoice_data[2], return_date(0))
            assert_(invoice_data[3], 'cancelled')  # assert of invoice status

            # validation for invoice_charges
            invoice_charge_data_from_db = query_execute(
                GET_INVOICE_CHARGES.format(invoice_id=invoice_data[1]), False).fetchall()
            invoice_charge_data = convert_2d_list_to_1d_list(invoice_charge_data_from_db)
            assert_(len(set(invoice_charge_data)), 1)  # assert of all invoice_charges to be deleted=True
