from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from ths_common.utils.dateutils import get_current_fiscal_year


class UploadCreditNotesValidation:
    def __init__(self, response, test_case_id):
        self.response = response
        self.expected_data = get_test_case_data(sheet_names.credit_note_template_sheet_name, test_case_id)[0]

    def validate_response(self, client, booking_request, billing_request, hotel_id):
        credit_note_response = self.response['data']['template']['credit_note']
        actual_booking_details = self.response['data']['template']['booking']
        assert_(credit_note_response['issued_to_type'], self.expected_data['issued_to_type'])
        assert_(credit_note_response['issued_by_type'], self.expected_data['issued_by_type'])
        assert_(credit_note_response['credit_note_number'].split("-")[0], self.expected_data['Prefix_1'])
        assert_(credit_note_response['credit_note_number'].split("-")[1], self.expected_data['Prefix_2'] +
                str(get_current_fiscal_year(short_format=True)))
        assert_(actual_booking_details['checkin_date'][0:10], str(return_date(self.expected_data['expected_booking_CI_date'])))
        assert_(actual_booking_details['checkout_date'][0:10], str(return_date(self.expected_data['expected_booking_CO_date'])))
