import json

from prometheus.integration_tests.config.common_config import HOTEL_ID, HOTEL_CURRENCY_MAP
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, get_currency_and_amount
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationCreditNotes:
    def __init__(self, response, test_case_id):
        self.response = response
        self.expected_data = get_test_case_data(sheet_names.credit_note_template_sheet_name, test_case_id)[0]

    def validate_response(self, client, booking_request, billing_request, hotel_id):
        credit_note_response = self.response['data']['credit_note']
        billing_response = billing_request.get_bill_request(client, booking_request.bill_id, 200)
        booking_response = booking_request.get_booking_request(client, booking_request.booking_id, 200)
        invoice_response = billing_request.get_bill_invoices(client, booking_request.bill_id, 200)
        assert_(credit_note_response['issued_to_type'], self.expected_data['issued_to_type'])
        assert_(credit_note_response['issued_by_type'], self.expected_data['issued_by_type'])
        expected_tax_data = json.loads(self.expected_data['credit_note_tax_deails'])
        expected_tax_breakup = json.loads(self.expected_data['tax_breakup'])

        for tax_data in expected_tax_data:
            if hotel_id != HOTEL_ID[0]:
                assert_(get_currency_and_amount(credit_note_response['tax_details']['cgst']['amount'])[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(get_currency_and_amount(credit_note_response['tax_details']['sgst']['amount'])[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(get_currency_and_amount(credit_note_response['tax_amount'])[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(get_currency_and_amount(credit_note_response['tax_details']['cgst']['amount'])[1],
                        tax_data['cgst']['amount'])
                assert_(get_currency_and_amount(credit_note_response['tax_details']['sgst']['amount'])[1],
                        tax_data['sgst']['amount'])
                assert_(get_currency_and_amount(credit_note_response['tax_amount'])[1],
                        tax_data['tax_amount']['amount'])
            else:
                assert_(credit_note_response['tax_details']['cgst']['amount'], tax_data['cgst']['amount'])
                assert_(credit_note_response['tax_details']['sgst']['amount'], tax_data['sgst']['amount'])
                assert_(credit_note_response['tax_amount'], tax_data['tax_amount']['amount'])

            assert_(credit_note_response['tax_details']['cgst']['percentage'], tax_data['cgst']['percentage'])
            assert_(credit_note_response['tax_details']['sgst']['percentage'], tax_data['sgst']['percentage'])

        for actual_tax_breakup_data, expected_tax_breakup_data in zip(credit_note_response['tax_breakup'],
                                                                      expected_tax_breakup):
            if hotel_id != HOTEL_ID[0]:
                assert_(get_currency_and_amount(actual_tax_breakup_data['amount'])[0], HOTEL_CURRENCY_MAP[hotel_id])
                assert_(get_currency_and_amount(actual_tax_breakup_data['amount'])[1],
                        expected_tax_breakup_data['amount'])
            else:
                assert_(actual_tax_breakup_data['amount'], expected_tax_breakup_data['amount'])
            assert_(actual_tax_breakup_data['tax_type'], expected_tax_breakup_data['tax_type'])

        expected_item_code = json.loads(self.expected_data['expected_item_code'])
        expected_item_name = json.loads(self.expected_data['expected_item_name'])
        for credit_note_line_item, item_code, item_name in zip(credit_note_response['credit_note_line_items'],
                                                               expected_item_code, expected_item_name):
            assert_(credit_note_line_item['item_code']['code_type'], item_code['code_type'])
            assert_(credit_note_line_item['item_code']['value'], item_code['value'])
            assert_(credit_note_line_item['item_name'], item_name['item_name'])
            if hotel_id != HOTEL_ID[0]:
                assert_(get_currency_and_amount(credit_note_line_item['pretax_amount'])[0], HOTEL_CURRENCY_MAP[hotel_id])
                assert_(get_currency_and_amount(credit_note_line_item['posttax_amount'])[0], HOTEL_CURRENCY_MAP[hotel_id])
                assert_(get_currency_and_amount(credit_note_line_item['tax_amount'])[0], HOTEL_CURRENCY_MAP[hotel_id])
                assert_(float(get_currency_and_amount(credit_note_line_item['pretax_amount'])[1]),
                        abs(float(get_currency_and_amount(billing_response['data']['charges'][(credit_note_line_item
                        ['charge_id']) - 1]['pretax_amount'])[1])))
                assert_(float(get_currency_and_amount(credit_note_line_item['posttax_amount'])[1]),
                        abs(float(get_currency_and_amount(billing_response['data']['charges'][(credit_note_line_item
                        ['charge_id']) - 1]['posttax_amount'])[1])))
                assert_(float(get_currency_and_amount(credit_note_line_item['tax_amount'])[1]),
                        abs(float(get_currency_and_amount(billing_response['data']['charges'][(credit_note_line_item
                        ['charge_id']) - 1]['tax_amount'])[1])))
            else:
                assert_(float(credit_note_line_item['pretax_amount']),
                        abs(float(billing_response['data']['charges'][(credit_note_line_item['charge_id']) - 1]
                                  ['pretax_amount'])))
                assert_(float(credit_note_line_item['posttax_amount']),
                        abs(float(billing_response['data']['charges'][(credit_note_line_item['charge_id']) - 1][
                            'posttax_amount'])))
                assert_(float(credit_note_line_item['tax_amount']),
                        abs(float(billing_response['data']['charges'][(credit_note_line_item['charge_id']) - 1]
                                  ['tax_amount'])))

        expected_credit_note_line_items_tax_details = json.loads(self.expected_data['credit_note_lineItems_tax_details'])
        expected_credit_note_line_items_tax_breakup_details = json.loads(self.expected_data
                                                                         ['credit_note_line_items_tax_breakup'])
        for actual_credit_note_line_items_tax, expected_credit_note_line_items_tax, \
            expected_credit_note_line_items_tax_breakup in zip(credit_note_response['credit_note_line_items'],
                                                               expected_credit_note_line_items_tax_details,
                                                               expected_credit_note_line_items_tax_breakup_details):
            if hotel_id != HOTEL_ID[0]:
                assert_(get_currency_and_amount(actual_credit_note_line_items_tax['tax_details']['cgst']['amount'])[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(get_currency_and_amount(actual_credit_note_line_items_tax['tax_details']['sgst']['amount'])[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(get_currency_and_amount(actual_credit_note_line_items_tax['tax_details']['cgst']['amount'])[1],
                        expected_credit_note_line_items_tax['cgst']['amount'])
                assert_(get_currency_and_amount(actual_credit_note_line_items_tax['tax_details']['sgst']['amount'])[1],
                        expected_credit_note_line_items_tax['sgst']['amount'])
                assert_(get_currency_and_amount(actual_credit_note_line_items_tax['tax_breakup'][0]['amount'])[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(get_currency_and_amount(actual_credit_note_line_items_tax['tax_breakup'][1]['amount'])[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(get_currency_and_amount(actual_credit_note_line_items_tax['tax_breakup'][0]['amount'])[1],
                        expected_credit_note_line_items_tax_breakup[0]['amount'])
                assert_(get_currency_and_amount(actual_credit_note_line_items_tax['tax_breakup'][1]['amount'])[1],
                        expected_credit_note_line_items_tax_breakup[1]['amount'])
            else:
                assert_(actual_credit_note_line_items_tax['tax_details']['cgst']['amount'],
                        expected_credit_note_line_items_tax['cgst']['amount'])
                assert_(actual_credit_note_line_items_tax['tax_details']['sgst']['amount'],
                        expected_credit_note_line_items_tax['sgst']['amount'])
                assert_(actual_credit_note_line_items_tax['tax_breakup'][0]['amount'],
                        expected_credit_note_line_items_tax_breakup[0]['amount'])
                assert_(actual_credit_note_line_items_tax['tax_breakup'][1]['amount'],
                        expected_credit_note_line_items_tax_breakup[1]['amount'])

            assert_(actual_credit_note_line_items_tax['tax_details']['cgst']['percentage'],
                    expected_credit_note_line_items_tax['cgst']['percentage'])
            assert_(actual_credit_note_line_items_tax['tax_details']['sgst']['percentage'],
                    expected_credit_note_line_items_tax['sgst']['percentage'])
            assert_(actual_credit_note_line_items_tax['tax_breakup'][0]['tax_type'],
                    expected_credit_note_line_items_tax_breakup[0]['tax_type'])
            assert_(actual_credit_note_line_items_tax['tax_breakup'][0]['percentage'],
                    expected_credit_note_line_items_tax_breakup[0]['percentage'])
            assert_(actual_credit_note_line_items_tax['tax_breakup'][1]['tax_type'],
                    expected_credit_note_line_items_tax_breakup[1]['tax_type'])
            assert_(actual_credit_note_line_items_tax['tax_breakup'][1]['percentage'],
                    expected_credit_note_line_items_tax_breakup[1]['percentage'])

        assert_(self.response['data']['booking']['booking_id'], booking_response['data']['booking_id'])
        assert_(self.response['data']['booking']['checkin_date'], booking_response['data']['checkin_date'])
        assert_(self.response['data']['booking']['checkout_date'], booking_response['data']['checkout_date'])
        assert_(self.response['data']['booking']['reference_number'], booking_response['data']['reference_number'])
        assert_(self.response['data']['booking']['creation_date'], booking_response['data']['created_at'])

        for actual_invoice_data, expected_invoice_data in zip(self.response['data']['invoice_details'],
                                                              invoice_response['data']):
            assert_(actual_invoice_data['invoice_date'], expected_invoice_data['invoice_date'])
            assert_(actual_invoice_data['invoice_number'], expected_invoice_data['invoice_number'])

        assert_(self.response['data']['vendor_context']['currency'], HOTEL_CURRENCY_MAP[hotel_id])
        assert_(self.response['data']['vendor_context']['time_zone'], self.expected_data['time_zone'])

        vendor_details_response = credit_note_response['vendor_details']
        expected_vendor_details = billing_response['data']['vendor_details']
        assert_(vendor_details_response['email'], expected_vendor_details['email'])
        assert_(vendor_details_response['hotel_id'], expected_vendor_details['hotel_id'])
        assert_(vendor_details_response['hotel_name'], expected_vendor_details['hotel_name'])
        assert_(vendor_details_response['url'], expected_vendor_details['url'])
        assert_(vendor_details_response['vendor_id'], expected_vendor_details['vendor_id'])
        assert_(vendor_details_response['vendor_name'], expected_vendor_details['vendor_name'])
        assert_(vendor_details_response['address']['country'], expected_vendor_details['address']['country'])
        assert_(vendor_details_response['address']['field_1'], expected_vendor_details['address']['field_1'])
        assert_(vendor_details_response['address']['field_2'], expected_vendor_details['address']['field_2'])
        assert_(vendor_details_response['address']['pincode'], expected_vendor_details['address']['pincode'])
        assert_(vendor_details_response['address']['state'], expected_vendor_details['address']['state'])
        assert_(vendor_details_response['phone']['country_code'], expected_vendor_details['phone']['country_code'])
        assert_(vendor_details_response['phone']['number'], expected_vendor_details['phone']['number'])






