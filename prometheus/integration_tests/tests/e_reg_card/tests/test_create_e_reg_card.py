import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.e_reg_card.validations.validation_checkin_status import ValidationCheckinStatus
from prometheus.integration_tests.utilities.common_utils import get_e_reg_card_config


class TestCreateERegCard(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, existing_test_case_id, tc_description, previous_actions, e_reg_card_config_enabled, e_reg_card_config_required, e_reg_card_config_level,"
        "status_code, user_type, error_code, error_message, dev_message, error_payload, skip_case",

        [
            # eregcard generation is not handled hence keeping eregcard non-mandatory
            ("Create_ERegCard_01", "checkinPost_69",
             "(Room Level)Checkin two guests in a Single booking when E Reg Config is set to Room Level and E Reg Status is Complete For all the Guest",
             [{'id': "Booking_130", 'type': 'booking'}], "true", "false", "room", 200, None, "", "", "", "", False),
            # eregcard generation is not handled hence keeping eregcard non-mandatory
            ("Create_ERegCard_02", "checkinPost_70",
             "Checkin two guests in a Single booking when E Reg Config is set to Room Level and E Reg Status is Complete only for the Primary Guest",
             [{'id': "Booking_131", 'type': 'booking'}], "true", "false", "room", 200, None, "", "", "", "", False),
            ("Create_ERegCard_03", "checkinPost_71",
             "Checkin two guests in a Single booking when E Reg Config is set to Room Level and E Reg Status is Incomplete for the Primary Guest",
             [{'id': "Booking_89", 'type': 'booking'}], "true", "true", "room", 400, "super-admin", "04010193",
             "ERegCard is not completed", "Mandatory ERegCard is not completed", "", False),
            ("Create_ERegCard_04", "checkinPost_70",
             "Checkin two guests in a Single booking when E Reg Config is set to Room Level and E Reg Status is Complete only for the Primary Guest and Required is set to False",
             [{'id': "Booking_130", 'type': 'booking'}], "true", "false", "room", 200, None, "", "", "", "", False),
            # ("Create_ERegCard_05", "checkinPost_66",
            #  "Checkin two guests in a Single booking when E Reg Config is set to Room Level and no reg card is filled",
            #  [{'id': "Booking_710", 'type': 'booking'}], "true", "false", "room", 200, None, "", "", "", "", False),
            # ("Create_ERegCard_06", "checkinPost_72",
            #  "Checkin 2 room with 1 guest in each room when E Reg Config is set to Room Level and E Reg Status is Complete For all the Guest",
            #  [{'id': "Booking_91", 'type': 'booking'}], "true", "true", "room", 200, None, "", "", "", "", False),
            # ("Create_ERegCard_07", "checkinPost_73",
            #  "Checkin 1 room out of 2 Room Booking when E Reg Config is set to Room Level and E Reg Status is Complete For all the Guest",
            #  [{'id': "Booking_91", 'type': 'booking'}], "true", "true", "room", 200, None, "", "", "", "", False),
            # ("Create_ERegCard_08", "checkinPost_69",
            #  "(Guest Level)Checkin two guests in a Single booking when E Reg Config is set to Guest Level and E Reg Status is Complete For all the Guest",
            #  [{'id': "Booking_87", 'type': 'booking'}], "true", "true", "guest", 200, None, "", "", "", "", False),
            ("Create_ERegCard_09", "checkinPost_69",
             "Checkin two guests in a Single booking when E Reg Config is set to Guest Level and E Reg Status is Complete only for the Primary Guest",
             [{'id': "Booking_90", 'type': 'booking'}], "true", "true", "guest", 400, "super-admin", "04010193",
             "ERegCard is not completed", "Mandatory ERegCard is not completed", "", False),
            ("Create_ERegCard_10", "checkinPost_71",
             "Checkin two guests in a Single booking when E Reg Config is set to Guest Level and E Reg Status is Incomplete for the Primary Guest",
             [{'id': "Booking_89", 'type': 'booking'}], "true", "true", "guest", 400, "super-admin", "04010193",
             "ERegCard is not completed", "Mandatory ERegCard is not completed", "", False),
            ("Create_ERegCard_11", "checkinPost_70",
             "Checkin two guests in a Single booking when E Reg Config is set to Guest Level and E Reg Status is Complete only for the Primary Guest and Required is set to False",
             [{'id': "Booking_130", 'type': 'booking'}], "true", "false", "guest", 200, None, "", "", "", "", False),
        ])
    @pytest.mark.regression
    def test_create_e_reg_card(self, client_, test_case_id, existing_test_case_id, tc_description, previous_actions,
                               e_reg_card_config_enabled, e_reg_card_config_required, e_reg_card_config_level,
                               status_code, user_type, error_code,
                               error_message, dev_message, error_payload, skip_case):

        if skip_case:
            pytest.skip()

        hotel_level_catalog_config = []
        e_reg_config = None

        if e_reg_card_config_enabled:
            e_reg_config = get_e_reg_card_config(e_reg_card_config_enabled, e_reg_card_config_required,
                                                 e_reg_card_config_level)
            hotel_level_catalog_config.append(e_reg_config)

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_level_config=hotel_level_catalog_config)

        if existing_test_case_id:
            test_case_id = existing_test_case_id
        response = self.booking_request.check_in_request(client_, self.booking_request.booking_id,
                                                         test_case_id, status_code, user_type,
                                                         hotel_level_config=hotel_level_catalog_config if hotel_level_catalog_config else None)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request):
        validation = ValidationCheckinStatus(client, response, test_case_id, booking_request)
        validation.validate_checkin_status()
