import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.tests.base_validations import BaseValidations


class ValidationGetFolioSummary(BaseValidations):
    def __init__(self, response, test_case_id, hotel_id):
        self.test_data = get_test_case_data(sheet_names.folio_summary_sheet_name, test_case_id)[0]
        self.response = response
        self.hotel_id = hotel_id

    def validate_response(self):
        actual_bill_summary = self.response['data']['bill_summary']
        expected_bill_summary = json.loads(self.test_data['expected_bill_summary'])
        self.validate_bill_summary(actual_bill_summary, expected_bill_summary, self.hotel_id)
        assert_(self.response['data']['is_invoiced'], sanitize_test_data(self.test_data['is_invoiced']))

        if sanitize_test_data(self.test_data['expected_charges']):
            actual_charges = self.response['data']['charges']
            expected_charges = json.loads(self.test_data['expected_charges'])
            actual_charges_sorted = sorted(actual_charges, key=lambda i: i['charge_id'])
            expected_charges_sorted = sorted(expected_charges, key=lambda i: i['charge_id'])
            self.validate_flat_charges(actual_charges_sorted, expected_charges_sorted)

        if sanitize_test_data(self.test_data['expected_payments']):
            actual_payments = self.response['data']['payments']
            expected_payments = json.loads(self.test_data['expected_payments'])
            actual_payments_sorted = sorted(actual_payments, key=lambda i: i['payment_id'])
            expected_payments_sorted = sorted(expected_payments, key=lambda i: i['payment_id'])
            self.validate_flat_payments(actual_payments_sorted, expected_payments_sorted)

    @staticmethod
    def validate_flat_charges(actual_charges, expected_charges):
        for actual_charge, expected_charge in zip(actual_charges, expected_charges):
            if expected_charge['allowances']:
                for actual_allowance, expected_allowance in zip(actual_charge['allowances'],
                                                                expected_charge['allowances']):
                    assert_(actual_allowance['allowance_id'], expected_allowance['allowance_id'])
                    assert_(actual_allowance['billed_entity_account']['account_number'],
                            expected_allowance['billed_entity_account']['account_number'])
                    assert_(actual_allowance['billed_entity_account']['billed_entity_id'],
                            expected_allowance['billed_entity_account']['billed_entity_id'])
                    assert_(actual_allowance['credit_note_id'], expected_allowance['credit_note_id'])
                    assert_(actual_allowance['invoice_id'], expected_allowance['invoice_id'])
                    assert_(actual_allowance['is_active'], expected_allowance['is_active'])
                    assert_(actual_allowance['posting_date'], str(return_date(expected_allowance['posting_date'])))
                    assert_(actual_allowance['posttax_amount'], expected_allowance['posttax_amount'])
                    assert_(actual_allowance['pretax_amount'], expected_allowance['pretax_amount'])
                    assert_(actual_allowance['remarks'], expected_allowance['remarks'])
                    assert_(actual_allowance['status'], expected_allowance['status'])
                    assert_(actual_allowance['tax_amount'], expected_allowance['tax_amount'])
            assert_(actual_charge['charge_id'], expected_charge['charge_id'])
            assert_(actual_charge['charge_split_id'], expected_charge['charge_split_id'])
            assert_(actual_charge['applicable_date'].split('T')[0],
                    str(return_date(expected_charge['applicable_date'])))
            assert_(actual_charge['billed_entity_account']['account_number'],
                    expected_charge['billed_entity_account']['account_number'])
            assert_(actual_charge['billed_entity_account']['billed_entity_id'],
                    expected_charge['billed_entity_account']['billed_entity_id'])
            assert_(actual_charge['charge_to'], expected_charge['charge_to'])
            assert_(actual_charge['charge_type'], expected_charge['charge_type'])
            assert_(actual_charge['inclusion_charge_ids'], expected_charge['inclusion_charge_ids'])
            assert_(actual_charge['item_name'], expected_charge['item_name'])
            assert_(actual_charge['posttax_amount'], expected_charge['posttax_amount'])
            assert_(actual_charge['pretax_amount'], expected_charge['pretax_amount'])
            assert_(actual_charge['room_no'], expected_charge['room_no'])
            assert_(actual_charge['room_stay_id'], expected_charge['room_stay_id'])
            assert_(actual_charge['room_type'], expected_charge['room_type'])
            assert_(actual_charge['sku_category_id'], expected_charge['sku_category_id'])
            assert_(actual_charge['status'], expected_charge['status'])
            assert_(actual_charge['tax_amount'], expected_charge['tax_amount'])
            assert_(actual_charge['tax_details'], expected_charge['tax_details'])

    @staticmethod
    def validate_flat_payments(actual_payments, expected_payments):
        for actual_payment, expected_payment in zip(actual_payments, expected_payments):
            assert_(actual_payment['payment_id'], expected_payment['payment_id'])
            assert_(actual_payment['payment_split_id'], expected_payment['payment_split_id'])
            assert_(actual_payment['billed_entity_account']['account_number'],
                    expected_payment['billed_entity_account']['account_number'])
            assert_(actual_payment['billed_entity_account']['billed_entity_id'],
                    expected_payment['billed_entity_account']['billed_entity_id'])
            assert_(actual_payment['date_of_payment'].split('T')[0],
                    str(return_date(expected_payment['date_of_payment'])))
            assert_(actual_payment['amount'], expected_payment['amount'])
            assert_(actual_payment['paid_by'], expected_payment['paid_by'])
            assert_(actual_payment['paid_to'], expected_payment['paid_to'])
            assert_(actual_payment['payment_channel'], expected_payment['payment_channel'])
            assert_(actual_payment['payment_mode'], expected_payment['payment_mode'])
            assert_(actual_payment['payment_mode_sub_type'], expected_payment['payment_mode_sub_type'])
            assert_(actual_payment['payment_ref_id'], expected_payment['payment_ref_id'])
            assert_(actual_payment['payment_type'], expected_payment['payment_type'])
            assert_(actual_payment['status'], expected_payment['status'])
