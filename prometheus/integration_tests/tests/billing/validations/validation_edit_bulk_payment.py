from flask import json
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_blank
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationEditBulkPayment(BaseValidations):
    def __init__(self, client_, test_case_id, response, bill_id):
        self.test_data = get_test_case_data(sheet_names.payment_v2_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.bill_id = bill_id

    def validate_response(self):
        if sanitize_blank(self.test_data["comment"]):
            assert(self.response["data"][0]["comment"], self.test_data["comment"])

        if sanitize_blank(self.test_data["status"]) and self.response["data"][0]["status"] != 'posted':
            assert_(self.response["data"][0]["status"], self.test_data['status'])

        if sanitize_blank(self.test_data["payment_ref_id"]):
            assert_(self.response["data"][0]["payment_ref_id"], self.test_data['payment_ref_id'])

    def validate_billing(self, billing_request):
        expected_billing_details = json.loads(self.test_data['expected_bill_summary'])
        actual_billing_details = billing_request.get_bill_request(self.client, self.bill_id, 200)
        assert_(actual_billing_details['data']['total_pretax_amount'], expected_billing_details['total_pretax_amount'])
        assert_(actual_billing_details['data']['total_posttax_amount'],
                expected_billing_details['total_posttax_amount'])
        assert_(actual_billing_details['data']['total_tax_amount'], expected_billing_details['total_tax_amount'])
        assert_(actual_billing_details['data']['refund_amount'], expected_billing_details['refund_amount'])
        assert_(actual_billing_details['data']['paid_amount'], expected_billing_details['paid_amount'])
        assert_(actual_billing_details['data']['net_paid_amount'], expected_billing_details['net_paid_amount'])
        assert_(actual_billing_details['data']['net_payable'], expected_billing_details['net_payable'])
        assert_(actual_billing_details['data']['total_credit_posttax_amount'],
                expected_billing_details['total_credit_posttax_amount'])
        assert_(actual_billing_details['data']['total_non_credit_invoiced_amount'],
                expected_billing_details['total_non_credit_invoiced_amount'])

    def validate_all_payment_response(self, billing_request):
        self.validate_all_payments_response(self.test_data, self.bill_id, self.client, billing_request)
