import json

from prometheus.integration_tests.utilities.common_utils import return_date, sanitize_test_data
from prometheus.integration_tests.config.sheet_names import *
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationAddBulkAllowance(BaseValidations):
    def __init__(self, client, test_case_id, billing_request, bill_id, hotel_id):
        self.test_data = get_test_case_data(charges_v2_sheet_name, test_case_id)[0]
        self.billing_request = billing_request
        self.client = client
        self.bill_id = bill_id
        self.hotel_id = hotel_id

    def validate_response(self):
        actual_charge_details = self.billing_request.get_bill_charges(self.client, self.bill_id, 200)['data']
        expected_charge_details = json.loads(self.test_data['expected_charge_details'])
        sorted_actual_charge_details = sorted(actual_charge_details, key=lambda i: i['charge_id'])
        sorted_expected_charge_details = sorted(expected_charge_details, key=lambda i: i['charge_id'])
        for actual_charge_detail, expected_charge_detail in zip(sorted_actual_charge_details,
                                                                sorted_expected_charge_details):
            actual_charge_detail['applicable_date'] = actual_charge_detail['applicable_date'].split('T')[0]
            expected_charge_detail['applicable_date'] = str(return_date(expected_charge_detail['applicable_date']))
            self.validate_charge(actual_charge_detail, expected_charge_detail)

    def validate_bill_summary_response(self):
        actual_bill_details = self.billing_request.get_bill_request_v2(self.client, self.bill_id, 200)['data']
        actual_billed_summary_response = actual_bill_details['summary']
        expected_billed_summary_response = json.loads(self.test_data['expected_billed_summary_response'])
        self.validate_bill_summary(actual_billed_summary_response, expected_billed_summary_response, self.hotel_id)

    def validate_billed_entity_response(self):
        actual_billed_entity_response = self.billing_request.get_bill_entity_request(self.client, self.bill_id, 200)
        expected_billed_entity_response = json.loads(self.test_data['expected_billed_entity_response'])
        actual_billed_entity_response_sorted = sorted(actual_billed_entity_response['data'],
                                                      key=lambda i: i['billed_entity_id'])
        expected_billed_entity_response_sorted = sorted(expected_billed_entity_response,
                                                        key=lambda i: i['billed_entity_id'])
        self.validate_billed_entity(actual_billed_entity_response_sorted, expected_billed_entity_response_sorted)

    def validate_commissions(self):
        if sanitize_test_data(self.test_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(self.test_data['expected_commissions']))
        else:
            self.validate_ta_commissions()
