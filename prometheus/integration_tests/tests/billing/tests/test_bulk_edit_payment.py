import pytest

from prometheus.integration_tests.resources.db_queries import UPDATE_PAYMENT_TO_POSTED
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.billing.validations.validation_edit_bulk_payment import \
    ValidationEditBulkPayment
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestBulkEditPayment(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message, is_mock_rule_req", [
            ("EditBulkPayment_01", 'Update a multi payment booking with same recorded payment',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_02", 'Update a single payment booking with same recorded payment',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_03", 'Update few fields, others remain same(credit pending-->refund cancelled)',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "", "",
             "", "", False, "", True),
            ("EditBulkPayment_04", 'Update field all to null',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 400, None, "", "",
             "", "", False, "", True),
            ("EditBulkPayment_05", 'No fields provided',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "", "",
             "", "", False, "", True),
            ("EditBulkPayment_06", 'Wrong enum values',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 400, None, "", "",
             "", "", False, "", True),
            ("EditBulkPayment_07", 'Wrong_Payment_ID',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 404, None, "04010004",
             "Payment not found. Please contact escalations.", "", "", False, "", True),
            ("EditBulkPayment_08", 'Update an payment with comment',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "", "",
             "", "", False, "", True),
            ("EditBulkPayment_09", 'Update a multi payment booking with different different payment mode',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "", "",
             "", "", False, "", True),

            # --------------------Test cases related to amount ------------------------------------#

            ("EditBulkPayment_10", 'Update a multi payment booking with amount as NULL',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "", "",
             "", "", False, "", True),
            ("EditBulkPayment_11", 'Update a multi payment booking with amount having special characters',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 400, None, "", "",
             "", "", False, "", True),
            ("EditBulkPayment_12", 'Update a multi payment booking with amount as Blank',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 400, None, "", "",
             "", "", False, "", True),
            ("EditBulkPayment_13", 'Update a multi payment booking with amount as zero',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "", "",
             "", "", False, "", True),
            ("EditBulkPayment_14", 'Update a multi payment booking with amount having negative value',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 400, None, "", "",
             "", "", False, "", True),

            # --------------------Test cases related to comment ------------------------------------#

            ("EditBulkPayment_15", 'Update a multi payment booking with comment having special characters',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "", "",
             "", "", False, "", True),
            ("EditBulkPayment_16", 'Update a multi payment booking with comment as NULL',
             [{'id': 'Booking_02', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'AddPayment_02', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "", "",
             "", "", False, "", True),

            # --------------------Test cases related to payment_id ------------------------------------#

            ("EditBulkPayment_17", 'Update a multi payment booking with payment_id having special characters',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 400, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_18", 'Update a multi payment booking with payment_id as Blank',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 400, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_19", 'Update a multi payment booking with payment_id as NULL',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 400, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_20", 'Update a multi payment booking with payment_id as Negative value',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 404, None, "",
             "", "", "", False, "", True),

            # --------------------Test cases related to payment_id ------------------------------------#

            ("EditBulkPayment_21", 'Update a multi payment booking with status as done',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_22", 'Update a multi payment booking with status as cancelled',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_23", 'Update a multi payment booking with status as pending',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_24", 'Update a multi payment booking with status as mapped_to_invoice',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_25", 'Update a multi payment booking with status as unconfirmed',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_26", 'Update a multi payment booking with status as posted',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 400, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_27", 'Update a multi payment booking with status as Null',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_28", 'Update a multi payment booking with status as Blank',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 200, None, "",
             "", "", "", False, "", True),
            ("EditBulkPayment_29", 'Update a multi payment booking with status having special characters',
             [{'id': 'Booking_01', 'type': 'booking'},
              {'id': 'AddPayment_01', 'type': 'add_payment', 'is_mock_rule_req': True}], 400, None, "",
             "", "", "", False, "", True),
        ])
    @pytest.mark.regression
    def test_bulk_edit_payment(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                               error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                               is_mock_rule_req):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.billing_request.edit_bulk_payment_request(client_, test_case_id, self.booking_request.bill_id,
                                                                  status_code, user_type, is_mock_rule_req)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.billing_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, billing_request, bill_id):
        validation = ValidationEditBulkPayment(client_, test_case_id, response, bill_id)
        validation.validate_response()
        validation.validate_billing(billing_request)


class TestBulkCancelPayments(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, payments_already_posted, actions_after_posting_payment", [
            ("EditBulkPayment_35", 'Cancel payment with done status', ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "",
             "", False, "", False, None),
            ("EditBulkPayment_36", 'Cancel payment with posted status', ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "",
             "", False, "", True, None),
            ("EditBulkPayment_37", 'Cancel payment with cancelled status', ADD_CONFIRMED_PAYMENT_50, 400, None, "", "",
             "", "", True, "Can have validation on cancelled payment", False, None),
            ("EditBulkPayment_38", 'Cancel payment with posted status where folio is already invoiced',
             FULL_CHECKOUT_01_V2, 400, None, "********",
             "Cannot cancel a payment billed to an account which is invoiced", "", "", False, "", False, None),
            ("EditBulkPayment_39", 'Cancel payment with posted status twice', ADD_CONFIRMED_PAYMENT_51, 400, None,
             "********", "Refund is already passed on the payment", "", "", False, "", False, None),
            ("EditBulkPayment_40", 'Cancel refund with done status', ADD_CONFIRMED_PAYMENT_03, 200, None,
             "********", "Refund is already passed on the payment", "", "", False, "", False, None),
            ("EditBulkPayment_41", 'Cancel refund with posted status', ADD_CONFIRMED_PAYMENT_03, 400, None,
             "********", "Posted refund cannot be cancelled", "", "", False, "", True, None),
            ("EditBulkPayment_42", 'Cancel payment with posted status, which already have some refund',
             ADD_CONFIRMED_PAYMENT_03, 400, None, "", "", "", "", True, "Will fix it later", True, None),
            ("EditBulkPayment_43", 'Cancel multiple payment with done and posted status', ADD_CONFIRMED_PAYMENT_17, 200,
             None, "", "", "", "", False, "", True,
             [{'id': 'AddPaymentV2_41', 'type': 'add_payment_v2', "payor_billed_entity_id": 1, "payment_matrix": True}]),
            ("EditBulkPayment_44", 'Cancel multiple payment with posted status', ADD_CONFIRMED_PAYMENT_52, 200, None,
             "", "", "", "", False, "", True, None),
            ("EditBulkPayment_45", 'Cancel multiple payment with posted status, where one payment already refunded',
             ADD_CONFIRMED_PAYMENT_53, 400, None, "********", "Refund is already passed on the payment", "", "", False,
             "", True, None),
        ])
    @pytest.mark.regression
    def test_bulk_cancel_payments(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                                  error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                                  payments_already_posted, actions_after_posting_payment):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        if payments_already_posted:
            query_execute(UPDATE_PAYMENT_TO_POSTED.format(bill_id=self.booking_request.bill_id))

        if actions_after_posting_payment:
            self.common_request_caller(client_, actions_after_posting_payment, hotel_id)

        response = self.billing_request.edit_bulk_payment_request(client_, test_case_id, self.booking_request.bill_id,
                                                                  status_code, user_type)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.billing_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, billing_request, bill_id):
        validation = ValidationEditBulkPayment(client_, test_case_id, response, bill_id)
        validation.validate_response()
        validation.validate_all_payment_response(billing_request)
