from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.common_config import HOTEL_ID
from prometheus.integration_tests.utilities.common_utils import assert_
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationCreateMultipleDnr(BaseTest):

    def __init__(self, client_, response, test_case_id, total_inventory_count):
        self.test_data = get_test_case_data(sheet_names.create_dnr_sheet_name, test_case_id)[0]
        self.response = response
        self.client_ = client_
        self.total_inventory_count = total_inventory_count
        self.test_case_id = test_case_id

    def validate_response(self):
        test_data = self.test_data
        for create_multiple_dnr_index in range(len(self.response['data']['dnrs'])):
            assert_(self.response['data']['dnrs'][create_multiple_dnr_index]['hotel_id'], HOTEL_ID[0])
            assert_(self.response['data']['dnrs'][create_multiple_dnr_index]['source'], test_data['source'])
            assert_(self.response['data']['dnrs'][create_multiple_dnr_index]['subtype'], test_data['subtype'])
            assert_(self.response['data']['dnrs'][create_multiple_dnr_index]['assigned_by'], test_data['assigned_by'])
            audit_type_from_response = self.dnr_request.get_audit_trail(self.client_, self.response['data']['dnrs'][
                create_multiple_dnr_index]['dnr_id'], 200)
            assert_(audit_type_from_response, test_data['expected_audit_type'])
        inventory_count_after_dnr_created = self.dnr_request.get_inventory_count(self.client_, 200, self.test_case_id)
        assert_(self.total_inventory_count - len(self.response['data']['dnrs']) * (int(self.test_data['to_date']) - int(
            self.test_data['from_date'])), inventory_count_after_dnr_created)
