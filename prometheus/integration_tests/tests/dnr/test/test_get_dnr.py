import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.dnr.validations.validation_get_dnr import ValidationGetDnr


class TestGetDNR(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_message",
        [
            ("GetDNR_01", "", "Get DNRs with wrong hotelId in request params", 404, 'super-admin', "04010007",
             "Aggregate: HotelAggregate with id: 000000 missing.", "", "", ""),
            ("GetDNR_02", "", "Get DNRs with empty hotelId in request params", 404, 'super-admin', "",
             "Exception occurred.", "", "", ""),
            ("GetDNR_03", "", "Get DNRs with null hotelId in request params", 404, 'super-admin', "04010007",
             "Aggregate: HotelAggregate with id: null missing.", "", "", ""),
            ("GetDNR_05", [CREATE_DNR_01], "Get Valid Single DNR", 200, 'super-admin', "", "", "", "", ""),
            ("GetDNR_06", RESOLVE_DNR, "Get multiple inactive DNR", 200, 'super-admin', "", "", "", "", ""),
            ("GetDNR_07", "", "Get DNRs with wrong dnrId in request params", 400, 'super-admin', "04010605",
             "DNR with given id cannot be located. Please contact escalations team.", "", "", ""),
            ("GetDNR_08", "", "Get DNRs with empty dnrId in request params", 404, 'super-admin', "",
             "Exception occurred.", "", "", ""),
            ("GetDNR_09", "", "Get DNRs with null dnrId in request params", 400, 'super-admin', "04010605",
             "DNR with given id cannot be located. Please contact escalations team.", "", "", ""),
            ("GetDNR_10", "", "Get DNRs with different dnrId and hotelId in request params", 400, "super-admin",
             "04010605", "DNR with given id cannot be located. Please contact escalations team.", "", "", ""),
        ])
    def test_get_dnr(self, client_, test_case_id, previous_actions, tc_description, status_code, user_type,
                     error_code, error_message, dev_message, error_payload, skip_message):

        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]
        status = 'active'

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        if test_case_id == 'GetDNR_01':
            hotel_id = '000000'
        elif test_case_id == 'GetDNR_02':
            hotel_id = ''
        elif test_case_id == 'GetDNR_03':
            hotel_id = 'null'
        else:
            hotel_id = hotel_id

        if test_case_id == 'GetDNR_07':
            dnr_id = '000000'
        elif test_case_id == 'GetDNR_08':
            dnr_id = ''
        elif test_case_id == 'GetDNR_09':
            dnr_id = 'null'
        else:
            dnr_id = self.dnr_request.dnr_id

        if test_case_id == 'GetDNR_06':
            self.dnr_request.dnr_id = self.dnr_request.inactive_dnr_id
            status = 'inactive'

        response = self.dnr_request.get_dnr_request(client_, hotel_id, status_code, dnr_id)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, None, None)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, status)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, status):
        validation_success_code = ValidationGetDnr(client_, response, test_case_id)
        validation_success_code.validate_get_dnr_response(status)
