import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.dnr.validations.validation_get_dnr import ValidationGetDnr
from prometheus.integration_tests.utilities.common_utils import return_date


class TestGetMultipleDNRs(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_message",
        [("GetMultipleDNR_01", "", "Get DNRs with wrong hotelId in request params", 404, 'super-admin', "04010007",
          "Aggregate: HotelAggregate with id: 000000 missing.", "", "", ""),
         ("GetMultipleDNR_02", "", "Get DNRs with empty hotelId in request params", 404, 'super-admin', "",
          "Exception occurred.", "", "", ""),
         ("GetMultipleDNR_03", "", "Get DNRs with null hotelId in request params", 404, 'super-admin', "04010007",
          "Aggregate: HotelAggregate with id: null missing.", "", "", ""),
         ("GetMultipleDNR_04", CREATE_MULTIPLE_DAY_DNR_01, "Get Valid DNRs", 200, 'super-admin', "", "", "", "", ""),
         ("GetMultipleDNR_05", RESOLVE_MULTIPLE_DNRs, "Get multiple inactive DNR", 200, 'super-admin', "", "", "", "",
          ""),
         ])
    def test_get_multiple_dnr(self, client_, test_case_id, previous_actions, tc_description, status_code, user_type,
                              error_code, error_message, dev_message, error_payload, skip_message):

        if skip_message:
            pytest.skip(skip_message)

        if test_case_id == 'GetMultipleDNR_01':
            hotel_id = '000000'
        elif test_case_id == 'GetMultipleDNR_02':
            hotel_id = ''
        elif test_case_id == 'GetMultipleDNR_03':
            hotel_id = 'null'
        else:
            hotel_id = HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        if test_case_id == 'GetMultipleDNR_05':
            self.dnr_request.dnr_ids = self.dnr_request.inactive_dnr_ids

        if test_case_id == 'GetMultipleDNR_06':
            response = self.dnr_request.get_multiple_dnr_by_date_request(client_, hotel_id, status_code, return_date(0),
                                                                         return_date(2))
        else:
            response = self.dnr_request.get_multiple_dnr_request(client_, hotel_id, status_code)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, None, None)

        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id)

    @staticmethod
    def validation(client_, response, test_case_id):
        validation_success_code = ValidationGetDnr(client_, response, test_case_id)
        validation_success_code.assert_dnrs_in_getdnrs()
        validation_success_code.validate_multiple_dnr_response()
