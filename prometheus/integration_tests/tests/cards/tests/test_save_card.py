import pytest
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.cards.validations.validation_save_card import ValidationSaveCard
from prometheus.integration_tests.config.common_config import *

class TestSaveCard(BaseTest):

    @pytest.mark.parametrize("test_case_id, previous_actions, tc_description,status_code, ""user_type, error_code, dev_message, error_payload, skip_case, skip_message", [
        ("SaveCard_01",[{'id': 'Booking_01', 'type': 'booking'}], "Save card details with card type as credit card",201, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_02", [{'id': 'Booking_01', 'type': 'booking'}], "Save card details with card type as debit card",201, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_03", [{'id': 'Booking_01', 'type': 'booking'}], "Save  mandatory details of a credit card", 201,
        SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_04", [{'id': 'Booking_01', 'type': 'booking'}], "Save card details with bill entity id not as integer",400, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_05", [{'id': 'Booking_01', 'type': 'booking'}], "Save card details with last digits as not a 4 digit number",400, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_06", [{'id': 'Booking_01', 'type': 'booking'}],"Save card details with expiry being a past date", 400, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_07", [{'id': 'Booking_01', 'type': 'booking'}],"Save card details with expiry not in the form mm/yy",400, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_08", [{'id': 'Booking_01', 'type': 'booking'}],"Save card details with pre auth code not as a string", 400, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_09", [{'id': 'Booking_01', 'type': 'booking'}], "Save card details with pre auth amount as negative value ",
         400, SUPER_ADMIN, "", "", "", True, ""),
        ("SaveCard_10", [{'id': 'Booking_01', 'type': 'booking'}], "Save card details without including expiry date",
         400, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_11", [{'id': 'Booking_01', 'type': 'booking'}], "Save card details without including last digits",
         400, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_12", [{'id': 'Booking_01', 'type': 'booking'}], "Save card details without including holder name ",
         400, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_13", [{'id': 'Booking_01', 'type': 'booking'}], "Save card details without including card_type ",
         400, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_14", [{'id': 'Booking_01', 'type': 'booking'}], "Save card details without currency ",
         400, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_15", [{'id': "Booking_01", 'type': 'booking'},{'id': "checkinPost_01", 'type': 'check_in'}],"Save card details with checked in booking ",
         201, SUPER_ADMIN, "", "", "", "", ""),
        ("SaveCard_16", [{'id': 'Booking_01', 'type': 'booking'}], "Save card details for same bill id ",
         201, SUPER_ADMIN, "", "", "", "", ""),
    ])

    @pytest.mark.regression
    def test_save_card(self, client_, test_case_id,  previous_actions, tc_description,
                                  status_code, user_type, error_code, dev_message, error_payload, skip_case,
                                  skip_message):
        if skip_case:
            pytest.skip(skip_message)

        test_case_id_for_errors = test_case_id
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        extra_data = None
        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.card_request.save_card(client_, test_case_id, status_code,self.booking_request.bill_id,user_type=user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id_for_errors)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request.bill_id, self.card_request ,response["data"]["card_id"],status_code, extra_data, hotel_id)
        else:
            assert False, "Response status code is not matching"

    def validation(self, client_, response, test_case_id, bill_id, card_request, card_id, status_code, extra_data, hotel_id, validate_billed_entity=False):
        validation = ValidationSaveCard(client_, response, test_case_id, bill_id, card_request, card_id, extra_data, hotel_id)
        validation.validate_response()
        validation.validate_response_with_get_card_response()