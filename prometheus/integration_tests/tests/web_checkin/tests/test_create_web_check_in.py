import pytest

from prometheus.integration_tests.resources.db_queries import DELETE_ATTACHMENT
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.integration_tests.tests.web_checkin.validations.validation_create_web_checkin import \
    ValidationCreateWebCheckIn
from prometheus.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *


class TestCreateWebCheckIn(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case",
        [

            ("WebCheckin_01", "Single booking web-checkin", Create_Web_check_in_01, 200, 'super_admin', "", "", "", "",
             ""),
            ("WebCheckin_02", "Multiple guest and room web-checkin", Create_Web_check_in_02, 200,
             'super_admin', "", "", "", "", ""),
            ("WebCheckin_04", "web-checkin for a reserved booking", Create_Web_check_in_03, 200,
             'super_admin', "", "", "", "", ""),
            ("WebCheckin_05", "web-checkin for a booking when attachment is rejected", Create_Web_check_in_04, 200,
             'super_admin', "", "", "", "", ""),
            ("WebCheckin_06", "web-checkin for a booking when no room allotment is done", Create_Web_check_in_05, 200,
             'super_admin', "", "", "", "", ""),
        ])
    @pytest.mark.regression
    def test_create_web_check_in(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                                 error_code, error_message, dev_message, error_payload, skip_case):

        query_execute(DELETE_ATTACHMENT)
        if skip_case:
            pytest.skip()

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        response = self.web_checkin_request.create_web_checkin_request(client_, test_case_id, status_code,
                                                                       self.booking_request.booking_id)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request):
        validation = ValidationCreateWebCheckIn(client, response, test_case_id)
        validation.validate_response(booking_request)
