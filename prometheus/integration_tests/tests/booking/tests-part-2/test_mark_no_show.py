from datetime import date, timed<PERSON>ta

import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_mark_no_show import (
    ValidationMarkNoShow,
)
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestMarkNoShow(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, skip_message, perform_night_audit", [
            ("MarkNoShow_01", "Mark single day and 1 room booking as no show.", NO_SHOW_SINGLE_DAY_BOOKING, 200, None,
             "", "", "", "", False, "", True),
            ("MarkNoShow_02", "Mark multiple day and 1 room booking as no show.", NO_SHOW_MULTIPLE_DAY_BOOKING, 200,
             None, "", "", "", "", False, "", True),
            ("MarkNoShow_03", "Mark current day booking as no show. Should throw error",
             SINGLE_BOOKING_01, 400, None, "04010115",
             "Cannot mark booking noshow before midnight of checkin date. Please cancel if necessary", "", "", False,
             "", True),
            ("MarkNoShow_04", "Mark future day booking as no show. Should throw error",
             Future_booking_v2_with_rate_plan_and_inclusion, 400, None, "04010115",
             "Cannot mark booking noshow before midnight of checkin date. Please cancel if necessary", "", "", False,
             "", True),
            ("MarkNoShow_05", "Mark single day booking with multiple rooms as no show",
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_06", "Mark multiple day booking with multiple rooms as no show",
             NO_SHOW_MULTIPLE_DAY_MULTIPLE_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_07", "Mark multiple day booking with multiple rooms  and multiple guests as no show",
             NO_SHOW_MULTIPLE_DAY_MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING, 200, None, "",
             "", "", "", False, "", True),
            ("MarkNoShow_08", "Mark partial check-in booking as no show. Should throw error",
             NO_SHOW_PARTIAL_CHECK_IN_BOOKING, 400, None, "04010104", "Invalid Action Error",
             "RoomStay: Can't trigger event noshow from state checked_in!", "", False, "", True),
            ("MarkNoShow_09", "Mark check-in booking as no show. Should throw error",
             NO_SHOW_CHECK_IN_BOOKING, 400, None, "04010104", "Invalid Action Error",
             "RoomStay: Can't trigger event noshow from state checked_in!", "", False, "", True),
            ("MarkNoShow_12", "Mark already marked as no show booking as no show. Should throw error",
             NO_SHOW_CANCELLED_BOOKING, 400, None, "04010104", "Invalid Action Error",
             "Booking: Can't trigger event noshow from state cancelled!", "", False, "", True),
            ("MarkNoShow_13", "Mark already marked as no show booking as no show. Should throw error",
             NO_SHOW_MARKED_BOOKING, 400, None, "04010113", "Booking is already marked noshow", "", "", False, "",
             True),
            ("MarkNoShow_15", "Mark only guest present in the room as no show. Should throw error.",
             NO_SHOW_SINGLE_DAY_BOOKING, 400, None, "04010144",
             "You can't mark last guest as no-show. If required, please mark room as no-show.",
             "", "", False, "", True),
            ("MarkNoShow_16", "Mark all guest present in the room as no show. Should throw error.",
             NO_SHOW_SINGLE_DAY_SINGLE_ROOM_MULTIPLE_GUEST_BOOKING, 400, None, "04010144",
             "You can't mark last guest as no-show. If required, please mark room as no-show.", "", "", False, "",
             True),
            ("MarkNoShow_17", "Mark only one guest in the room as no show.",
             NO_SHOW_SINGLE_DAY_SINGLE_ROOM_MULTIPLE_GUEST_BOOKING, 200, None, "", "", "", "", True, "Folio Issue",
             True),
            ("MarkNoShow_18", "Mark multiple guests but not all in the room as no show.",
             NO_SHOW_SINGLE_DAY_SINGLE_ROOM_THREE_GUEST_BOOKING, 200, None, "", "", "", "", True, "Folio Issue", True),
            ("MarkNoShow_19", "Mark already marked no show guest as no show. Should throw error.",
             NO_SHOW_MARKED_GUEST, 400, None, "04010145", "Guest stay has already been marked no-show.",
             "", {'guest_stay_id': 1}, False, "", True),
            ("MarkNoShow_21", "Mark check-in guest as no show. Should throw error",
             NO_SHOW_CHECK_IN_ALL_GUEST_IN_ROOM, 400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event noshow from state checked_in!", "", False, "", True),
            ("MarkNoShow_23", "Mark only room present in booking as no show. Should throw error.",
             NO_SHOW_SINGLE_DAY_BOOKING, 400, None, "04010132",
             "You can't mark last room as no-show. If required, please mark booking as no-show.",
             "", {'room_stay_id': 1}, False, "", True),
            ("MarkNoShow_24", "Mark all room present in booking as no show. Should throw error",
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING, 400, None, "04010132",
             "You can't mark last room as no-show. If required, please mark booking as no-show.",
             "", {'room_stay_id': 2}, False, "", True),
            ("MarkNoShow_25", "Mark only single room out of multiple rooms present in booking as no show.",
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_26", "Mark two rooms out of multiple rooms present in booking as no show.",
             NO_SHOW_SINGLE_DAY_THREE_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_27", "Mark already marked no show room as no show. Should throw error.",
             NO_SHOW_MARKED_ROOM, 400, None, "04010133", "Room stay has already been marked no-show.",
             "", {'room_stay_id': 1}, False, "", True),
            ("MarkNoShow_28", "Mark already marked as no show booking as no show. Should throw error",
             NO_SHOW_CANCELLED_ROOM, 400, None, "04010117", "Room stay is inactive",
             "RoomStay is cancelled or marked noshow", "", False, "", True),
            ("MarkNoShow_29", "Mark room with future check-in date as no show. Should throw error",
             NO_SHOW_DIFFERENT_ROOM_STAY_DATE_BOOKING, 400, None, "04010115",
             "Cannot mark booking noshow before midnight of checkin date. Please cancel if necessary",
             "", {'entity': 'RoomStay', 'room_stay_id': 2}, False, "", True),
            ("MarkNoShow_30", "Mark partial check-in room as no show. Should throw error",
             NO_SHOW_PARTIAL_CHECK_IN_ROOM, 400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event noshow from state checked_in!", "", False, "", True),
            ("MarkNoShow_31", "Mark check-in room as no show. Should throw error",
             NO_SHOW_PARTIAL_CHECK_IN_BOOKING, 400, None, "04010104", "Invalid Action Error",
             "RoomStay: Can't trigger event noshow from state checked_in!", "", False, "", True),
            ("MarkNoShow_34", "Mark no show with null/empty guest ids.",
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Guest Stay Ids] -> Field may not be null.",
             "", {'field': 'room_stays.0.guest_stay_ids'}, False, "", False),
            ("MarkNoShow_35", "Mark no show with null/empty room ids.",
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Room Stay Id] -> Room stay id may not be null.",
             "", {'field': 'room_stays.0.room_stay_id'}, False, "", False),
            ("MarkNoShow_36", "Mark no show by giving guest id but without room id",
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Room Stay Id] -> Please provide room stay id.",
             "", {'field': 'room_stays.0.room_stay_id'}, False, "", False),
            ("MarkNoShow_37", "Mark no show with invalid guest ids.", SINGLE_WALK_BOOKING_V2_01, 404, None, "04010004",
             "GuestStay not found. Please contact escalations.",
             "RoomStay:GuestStay not found in 1:20", "", False, "", False),
            ("MarkNoShow_38", "Mark no show with invalid room ids.", SINGLE_WALK_BOOKING_V2_01, 404, None, "04010004",
             "RoomStay not found. Please contact escalations.", "", "", False, "", False),
            ("MarkNoShow_39", "Mark no show without resource version.", SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Resource Version] -> This is a required field", "", {'field': 'resource_version'}, False, "",
             False),
            ("MarkNoShow_40", "Mark no show without noshow reason.", NO_SHOW_SINGLE_DAY_BOOKING, 200, None, "", "", "",
             "", False, "", True),
            ("MarkNoShow_41", "Mark no show with empty/null noshow reason.", NO_SHOW_SINGLE_DAY_BOOKING, 200, None, "",
             "", "", "", False, "", True),
            ("MarkNoShow_43", "Mark no show after reverse cancel contains cancellation charge",
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING + [{'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
                                                         {'id': "CancelAction_15", 'type': 'cancel'},
                                                         {'type': 'delete_booking_action'}], 200, None, "", "", "", "",
             False, "", True),
            ("MarkNoShow_44", "Mark no show after reverse cancel contains expense and cancellation charge",
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING
             + [{'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
                {'id': 'Create_Expense_checkout_10', 'type': 'expense'},
                {'id': "CancelAction_15", 'type': 'cancel'}, {'type': 'delete_booking_action'}], 200, None, "", "", "",
             "", False, "", True),
        ])
    @pytest.mark.regression
    def test_mark_no_show(self, client_, test_case_id, tc_description, previous_actions, status_code,
                          user_type, error_code, error_message, dev_message, error_payload, skip_case,
                          skip_message, perform_night_audit):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_no_show(client_, test_case_id, status_code,
                                                     self.booking_request.booking_id, user_type)
        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.expense_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, billing_request, expense_request,
                   bill_id):
        validation = ValidationMarkNoShow(client_, test_case_id, response, booking_request, booking_id)
        validation.validate_response()
        validation.validate_billed_entity_response(billing_request, bill_id)
        validation.validate_proforma_invoice(client_, booking_request, billing_request, bill_id,
                                             booking_request.get_booking_request_v2(client_, booking_id, 200),
                                             extra_data=None)
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id)


class TestMarkNoShowWithCommission(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, skip_message, perform_night_audit", [
            ("MarkNoShow_45", "Mark single day and 1 room ota booking as no show",
             [{'id': 'booking_01_no_show_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_46", "Mark multiple days and 1 room ota booking as no show",
             [{'id': 'booking_02_no_show_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_47", "Mark single day booking with multiple rooms as no show",
             [{'id': 'booking_03_no_show_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_48", "Mark multiple day booking with multiple rooms as no show",
             [{'id': 'booking_04_no_show_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_49", "Mark single day and 1 room ota booking having updated rate plan as no show",
             [{'id': 'booking_01_no_show_ota', 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_99_no_show', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_50", "Mark single day and 1 room ota booking having updated commission as no show",
             [{'id': 'booking_226_no_show', 'type': 'booking_v2'},
              {'id': 'put_booking_105_no_show', 'type': 'put_booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_51", "Mark single day and 1 room ota booking having updated stay duration as no show",
             [{'id': 'booking_01_no_show_ota', 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_129_no_show', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_52", "Mark single day and 1 room ota booking having updated room charge as no show",
             [{'id': 'booking_01_no_show_ota', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1}], 200, None, "", "", "", "",
             False, "", True),
            ("MarkNoShow_53", "Mark single day and 1 room hotel walkin booking as no show",
             [{'id': 'booking_01_no_show', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_54", "Mark single day and 1 room ota booking having extra expense as no show",
             [{'id': 'booking_01_no_show_ota', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_104_no_show', 'type': 'create_expense_V3'},
              {'id': 'AddPaymentCheckoutV2_10', 'type': 'add_payment_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_55", "Mark single day and 1 room ota booking where a room is added",
             [{'id': 'booking_01_no_show_ota', 'type': 'booking_v2'},
              {'id': 'AddMultipleRoom_155_no_show', 'type': 'add_multiple_room'}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_56", "Mark single day and 1 room ota booking where a room is removed",
             [{'id': 'booking_03_no_show_ota', 'type': 'booking_v2'},
              {'id': 'MarkCancelled_115', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': False}, {'id': 'AddPaymentCheckoutV2_09', 'type': 'add_payment_v2'}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_57", "Mark single day and 1 room ota booking where a guest is added",
             [{'id': 'booking_01_no_show_ota', 'type': 'booking_v2'},
              {'id': 'AddMultipleGuest_99_no_show', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_58", "Mark single day and 1 room ota booking where a guest is removed",
             [{'id': 'booking_05_no_show_ota', 'type': 'booking_v2'},
              {'id': 'MarkCancelled_127_no_show', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_59", "Mark single day and 1 room ota reverse cancelled booking as no show",
             [{'id': 'booking_01_no_show_ota', 'type': 'booking_v2'},
              {'id': "MarkCancelled_95", 'type': 'mark_cancelled'},
              {'id': 'Delete_Cancel_01', 'type': 'reverse_cancel_action'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_60", "Mark single day and 1 room ota reverse no show booking as no show",
             [{'id': 'booking_01_no_show_ota', 'type': 'booking_v2'},
              {'id': "MarkNoShow_45", 'type': 'mark_no_show'},
              {'id': 'Delete_NoShow_01', 'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_61", "Mark one guest in a single room single day ota booking as no show",
             [{'id': 'booking_05_no_show_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_62", "Mark multiple guests in a single room single day ota booking as no show",
             [{'id': 'booking_07_no_show_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_63", "Mark one guest in a multiple rooms single day ota booking as no show",
             [{'id': 'booking_03_no_show_ota_multiple_guest', 'type': 'booking_v2'}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_64", "Mark multiple guests in a multiple rooms single day ota booking as no show",
             [{'id': 'booking_03_no_show_ota_multiple_guest', 'type': 'booking_v2'}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_65", "Mark one guest in a single room multiple days ota booking as no show",
             [{'id': 'booking_02_no_show_ota_multiple_guest', 'type': 'booking_v2'}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_66", "Mark multiple guests in a single room multiple days ota booking as no show",
             [{'id': 'booking_02_no_show_ota_multiple_guest', 'type': 'booking_v2'}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_67", "Mark one guest in a multiple rooms multiple days ota booking as no show",
             [{'id': 'booking_04_no_show_ota_02', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_68", "Mark multiple guests in a multiple rooms multiple days ota booking as no show",
             [{'id': 'booking_04_no_show_ota_02', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_69", "Mark one guest in a single room single day ota booking as no show",
             [{'id': 'booking_05_no_show', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_70", "Mark a guest in single room single day ota booking having updated rate plan as no show",
             [{'id': 'booking_05_no_show_ota', 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_99_no_show', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_71",
             "Mark a guest in single room single day ota booking having updated stay duration as no show",
             [{'id': 'booking_05_no_show_ota', 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_129_no_show', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_72",
             "Mark a guest in single room single day ota booking having updated room stay charge as no show",
             [{'id': 'booking_05_no_show_ota', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_73", "Mark one guest in a single room single day ota booking having extra expense as no show",
             [{'id': 'booking_05_no_show_ota', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_94_no_show', 'type': 'create_expense_V3'}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_74", "Mark one guest in a single room single day ota reverse cancelled booking as no show",
             [{'id': 'booking_05_no_show_ota', 'type': 'booking_v2'},
              {'id': "MarkCancelled_95", 'type': 'mark_cancelled'},
              {'id': 'Delete_Cancel_01', 'type': 'reverse_cancel_action'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_75", "Mark one guest in a single room single day ota reverse no show booking as no show",
             [{'id': 'booking_05_no_show_ota', 'type': 'booking_v2'},
              {'id': "MarkNoShow_45", 'type': 'mark_no_show'},
              {'id': 'Delete_NoShow_01', 'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_76", "Mark one room in a multiple rooms single day ota booking as no show",
             [{'id': 'booking_03_no_show_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_77", "Mark one room in a multiple rooms multiple days ota booking as no show",
             [{'id': 'booking_04_no_show_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_78", "Mark multiple rooms in a multiple rooms multiple days ota booking as no show",
             [{'id': 'booking_08_no_show_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_79", "Mark one room in a multiple rooms single day hotel walkin booking as no show",
             [{'id': 'booking_03_no_show', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_80",
             "Mark one room in a multiple rooms single day ota booking having updated rate plan as no show",
             [{'id': 'booking_03_no_show_ota', 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_103_no_show', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_81",
             "Mark one room in a multiple rooms single day ota booking having updated stay duration as no show",
             [{'id': 'booking_03_no_show_ota', 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_131_no_show', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': False},
              {'id': 'UpdateStayDuration_131_no_show_diff_room', 'type': 'update_stay_duration',
               'enable_rate_manager': True, 'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_82",
             "Mark one room in a multiple rooms single day ota booking having updated room stay charge as no show",
             [{'id': 'booking_03_no_show_ota', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1}], 200, None, "", "",
             "", "", False, "", True),
            ("MarkNoShow_83",
             "Mark one room in a multiple rooms single day ota booking having extra expense as no show",
             [{'id': 'booking_03_no_show_ota', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_104_no_show', 'type': 'create_expense_V3'}], 200, None,
             "", "", "", "", False, "", True),
            ("MarkNoShow_84",
             "Mark one room in a multiple rooms single day ota booking having multiple guests as no show",
             [{'id': 'booking_03_no_show_ota_multiple_guest', 'type': 'booking_v2'}], 200, None, "", "", "",
             "", False, "", True),
            ("MarkNoShow_85", "Mark one room in a multiple rooms single day ota reverse cancelled booking as no show",
             [{'id': 'booking_03_no_show_ota', 'type': 'booking_v2'},
              {'id': "MarkCancelled_95", 'type': 'mark_cancelled'},
              {'id': 'Delete_Cancel_01', 'type': 'reverse_cancel_action'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_86", "Mark one room in a multiple rooms single day ota reverse no show booking as no show",
             [{'id': 'booking_03_no_show_ota', 'type': 'booking_v2'},
              {'id': "MarkNoShow_45", 'type': 'mark_no_show'},
              {'id': 'Delete_NoShow_01', 'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_132", "Edit commission then mark ota booking as no show",
             [{'id': 'booking_01_no_show_ota', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_133", "Edit commission then mark one guest in a ota booking as no show",
             [{'id': 'booking_05_no_show_ota', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_134", "Edit commission then mark one room in a ota booking as no show",
             [{'id': 'booking_03_no_show_ota', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", True),
        ])
    @pytest.mark.regression
    def test_mark_no_show_with_commission(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                          user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                          skip_message, perform_night_audit):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_no_show(client_, test_case_id, status_code,
                                                     self.booking_request.booking_id, user_type)

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.expense_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, billing_request, expense_request,
                   bill_id):
        validation = ValidationMarkNoShow(client_, test_case_id, response, booking_request, booking_id)
        validation.validate_response()
        validation.validate_billed_entity_response(billing_request, bill_id)
        validation.validate_proforma_invoice(client_, booking_request, billing_request, bill_id,
                                             booking_request.get_booking_request_v2(client_, booking_id, 200),
                                             extra_data=None)
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id)
        validation.validate_commissions()


class TestMarkNoShowWithNewTaxCalc(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, skip_message, perform_night_audit", [
            ("MarkNoShow_87", "Mark no show a booking where sez and lut is false for company",
             [{'id': 'booking_with_lut_04_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_88", "Mark no show a booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_89", "Mark no show a booking where sez is false and lut is true for company",
             [{'id': 'booking_with_lut_03_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_90", "Mark no show a booking where sez and lut is true for company",
             [{'id': 'booking_with_lut_01_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_91", "Mark no show a booking where sez and lut is false for travel agent",
             [{'id': 'booking_with_lut_04_ta_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_92", "Mark no show a booking where sez is true and lut is false for travel agent",
             [{'id': 'booking_with_lut_02_ta_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_93", "Mark no show a booking where sez is false and lut is true for travel agent",
             [{'id': 'booking_with_lut_03_ta_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_94", "Mark no show a booking where sez and lut is true for travel agent",
             [{'id': 'booking_with_lut_01_ta_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_95", "Mark no show a booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_96", "Mark no show a multiple days booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_days_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_97", "Mark no show a multiple days booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_multiple_days_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_98", "Mark no show room in booking where sez and lut is false for company",
             [{'id': 'booking_with_lut_04_multiple_rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_99", "Mark no show room in booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_100", "Mark no show room in booking where sez is false and lut is true for company",
             [{'id': 'booking_with_lut_03_multiple_rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_101", "Mark no show room in booking where sez and lut is true for company",
             [{'id': 'booking_with_lut_01_multiple_rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_102", "Mark no show room in booking where sez and lut is false for travel agent",
             [{'id': 'booking_with_lut_04_ta_multiple_rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_103", "Mark no show room in booking where sez is true and lut is false for travel agent",
             [{'id': 'booking_with_lut_02_ta_multiple_rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_104", "Mark no show room in booking where sez is false and lut is true for travel agent",
             [{'id': 'booking_with_lut_03_ta_multiple_rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_105", "Mark no show room in booking where sez and lut is true for travel agent",
             [{'id': 'booking_with_lut_01_ta_multiple_rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_106", "Mark no show room in booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_107",
             "Mark no show room in booking with company and travel agent where charges are billed to company",
             [{'id': 'booking_with_lut_02_company_ta_multiple_rooms_no_show', 'type': 'booking_v2',
               'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_108",
             "Mark no show room in booking with company and travel agent where charges are billed to travel agent",
             [{'id': 'booking_with_lut_02_ta_company_multiple_rooms_no_show', 'type': 'booking_v2',
               'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_109", "Mark no show multiple rooms in booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_3rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_110", "Mark no show multiple rooms in booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_3rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_111", "Mark no show multiple room in booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_3rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_112",
             "Mark no show room in multiple days booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_days_rooms_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_113",
             "Mark no show room in multiple days booking where sez is true and lut is false for travel agent",
             [{'id': 'booking_with_lut_02_ta_multiple_days_rooms_no_show', 'type': 'booking_v2',
               'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_114", "Mark no show room in multiple days booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_days_rooms_no_show', 'type': 'booking_v2',
               'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_115", "Mark no show guest in booking where sez and lut is false for company",
             [{'id': 'booking_with_lut_04_multiple_guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_116", "Mark no show guest in booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_117", "Mark no show guest in booking where sez is false and lut is true for company",
             [{'id': 'booking_with_lut_03_multiple_guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_118", "Mark no show guest in booking where sez and lut is true for company",
             [{'id': 'booking_with_lut_01_multiple_guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_119", "Mark no show guest in booking where sez and lut is false for travel agent",
             [{'id': 'booking_with_lut_04_ta_multiple_guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_120", "Mark no show guest in booking where sez is true and lut is false for travel agent",
             [{'id': 'booking_with_lut_02_ta_multiple_guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_121", "Mark no show guest in booking where sez is false and lut is true for travel agent",
             [{'id': 'booking_with_lut_03_ta_multiple_guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_122", "Mark no show guest in booking where sez and lut is true for travel agent",
             [{'id': 'booking_with_lut_01_ta_multiple_guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_123", "Mark no show guest in booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_124",
             "Mark no show guest in booking with company and travel agent where charges are billed to company",
             [{'id': 'booking_with_lut_02_company_ta_multiple_guests_no_show', 'type': 'booking_v2',
               'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_125",
             "Mark no show guest in booking with company and travel agent where charges are billed to travel agent",
             [{'id': 'booking_with_lut_02_ta_company_multiple_guests_no_show', 'type': 'booking_v2',
               'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_126", "Mark no show multiple guests in booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_3guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_127", "Mark no show multiple guests in booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_3guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_128", "Mark no show multiple guests in booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_3guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_129",
             "Mark no show guest in multiple days booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_days_guests_no_show', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_130",
             "Mark no show guest in multiple days booking where sez is true and lut is false for travel agent",
             [{'id': 'booking_with_lut_02_ta_multiple_days_guests_no_show', 'type': 'booking_v2',
               'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_131", "Mark no show guest in multiple days booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_days_guests_no_show', 'type': 'booking_v2',
               'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True),
        ])
    @pytest.mark.regression
    def test_mark_no_show_with_new_tax_calc(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                            user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                            skip_message, perform_night_audit):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_no_show(client_, test_case_id, status_code,
                                                     self.booking_request.booking_id, user_type)

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.expense_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, billing_request, expense_request,
                   bill_id):
        validation = ValidationMarkNoShow(client_, test_case_id, response, booking_request, booking_id)
        validation.validate_response()
        validation.validate_billed_entity_response(billing_request, bill_id)
        validation.validate_proforma_invoice(client_, booking_request, billing_request, bill_id,
                                             booking_request.get_booking_request_v2(client_, booking_id, 200),
                                             extra_data=None)
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id)
        validation.validate_commissions()
        validation.validate_charges(billing_request)

class TestMarkNoShowLastRoom(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, skip_message, perform_night_audit", [
            # ---------------- Mark NoShow last room cases ------------
            ("MarkNoShow_135", "Mark last room in a 2 rooms single day hotel booking as no-show, "
                               "while another room is checked-out",
             CHECKOUT_ONE_ROOM_IN_TWO_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_136", "Mark last room in a multiple rooms single day hotel booking as no-show, "
                               "while remaining rooms are checked-out",
             CHECKOUT_ALL_EXCEPT_ONE_IN_MULTIPLE_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_137", "Mark last room in a multiple rooms single day hotel booking as no-show "
                               "while 1 room is checked out and 1 room is cancelled",
             CHECKOUT_AND_CANCEL_IN_MULTIPLE_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_138", "Mark the last room no-show on checkout date after reverse no-show and "
                               "a night audit is performed, while another room is checked-out.",
             CHECKOUT_AND_NOSHOW_REVERSE_NOSHOW_BOOKING, 200, None, "", "", "", "", True, "", True),
            ("MarkNoShow_139", "Verify correct NoShow charge distribution for Multi-Room(odd rooms) "
                                       "booking with payment – Resolving 0.01 Rounding Error",
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING_V2, 200, None, "", "", "", "", False, "", True),
            ("MarkNoShow_140", "Verify correct NoShow charge distribution for Multi-Room(even rooms) "
                                       "booking with payment – Resolving 0.01 Rounding Error",
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING_V3, 200, None, "", "", "", "", False, "", True),

        ])
    @pytest.mark.regression
    def test_mark_no_show_last_room(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                            user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                            skip_message, perform_night_audit):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_no_show(client_, test_case_id, status_code,
                                                     self.booking_request.booking_id, user_type)

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.expense_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, billing_request, expense_request,
                   bill_id):
        validation = ValidationMarkNoShow(client_, test_case_id, response, booking_request, booking_id)
        validation.validate_response()
        validation.validate_billed_entity_response(billing_request, bill_id)
        validation.validate_proforma_invoice(client_, booking_request, billing_request, bill_id,
                                             get_booking_response=None, extra_data=None)
        validation.validate_charges(billing_request)
