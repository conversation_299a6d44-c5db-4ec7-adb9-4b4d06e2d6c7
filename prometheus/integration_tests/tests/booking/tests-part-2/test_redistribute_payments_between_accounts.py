import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.booking.validations.validation_redistribute_payments_between_accounts import \
    ValidationRedistributePayments


class TestRedistributePayment(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            # --------------------------------- Field Related Negative Cases -----------------------------------#
            ("Redistribute_Payments_01", 'Provide Invoice group id as NULL',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, None, "********",
             "[Booking Invoice Group Id] -> Field may not be null.", "", {"field": "booking_invoice_group_id"},
             False, ""),
            ("Redistribute_Payments_02", 'Provide Invoice group id as empty',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, None, "********",
             "[Booking Invoice Group Id] -> Please provide a valid booking_invoice_group_id", "",
             {"field": "booking_invoice_group_id"}, False, ""),
            ("Redistribute_Payments_03", 'Provide Invoice group id as invalid',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, None, "********",
             "Aggregate: BookingInvoiceGroupAggregate with id: INVALID missing.", "", "", False, ""),
            # ------------------------------------------ Positive Cases --------------------------------------------#
            ("Redistribute_Payments_04", 'Provide billed_entity id having one account with charges',
             [{'id': "booking_01", 'type': 'booking_v2'}], 201, None, "", "", "", "", False, ""),
            ("Redistribute_Payments_05", 'Provide billed_entity id having two account with charges',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': 'Create_Expense_12', 'type': 'expense'}], 201, None,
             "", "", "", "", False, ""),
            ("Redistribute_Payments_06", 'Provide billed entity with one account having surplus',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'AddPaymentV2_Redistribute_08', 'type': 'add_payment_v2'}], 201, None, "", "", "", "",
             False, ""),
            ("Redistribute_Payments_07", 'Provide billed entity with two account having surplus in both',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'AddPaymentV2_Redistribute_09', 'type': 'add_payment_v2'}], 201, None, "", "", "", "",
             False, ""),
            ("Redistribute_Payments_08", 'Provide billed entity with two account one have surplus while other have '
                                         'charges such that net balance required payment',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'AddPaymentV2_26', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_09", 'Provide billed entity with two account one have surplus while other have '
                                         'charges such that net balance has surplus',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'AddPaymentV2_Redistribute_10', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_10", 'Provide billed entity with multiple account which have surplus, while one '
                                         'account having charges which alone can nullify all the surplus',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'AddPaymentV2_Redistribute_15_1', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_Redistribute_15_2', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_Redistribute_15_3', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "",
             False, ""),
            ("Redistribute_Payments_11", 'Provide billed entity with multiple account which have surplus, while one '
                                         'account having charges which able to nullify one of the surplus account',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'AddPaymentV2_Redistribute_10', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_Redistribute_15_2', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_Redistribute_12', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_12",
             'Provide billed entity with multiple account which have surplus, and multiple account having charges '
             'which gives multiple account having net balance to record payment',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': 'Create_Expense_12', 'type': 'expense'},
              {'id': 'Create_Expense_Redistribute_18', 'type': 'expense'},
              {'id': 'AddPaymentV2_Redistribute_15_3', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_Redistribute_19', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_13",
             'Provide two billed entity in which one has two accounts(one with charge, other with surplus net balance'
             ' requires refund or surplus), while other billed entity also have two account with surplus(one with'
             ' charge, other with surplus net balance requires payment)',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'AddPaymentV2_Redistribute_10', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'},
              {'id': 'Create_Expense_Redistribute_10', 'type': 'expense'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_14", 'Redistribute payment if given folio is locked',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': 'checkin_01', 'type': 'checkin_v2'},
              {'id': 'invoicePreview_redistribute_payment_29', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_Redistribute_22', 'type': 'add_payment_v2'},
              {'id': 'CheckoutV2_01', 'type': 'checkout_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_15", 'Redistribute payment if given folio is locked, but new folio get created '
                                         'after checkout due to surplus',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': 'checkin_01', 'type': 'checkin_v2'},
              {'id': 'invoicePreview_redistribute_payment_29', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_Redistribute_08', 'type': 'add_payment_v2'},
              {'id': 'CheckoutV2_01', 'type': 'checkout_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_16", 'Create a booking with 15 accounts having payment, charges in it',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': 'Create_Expense_12', 'type': 'expense'},
              {'id': 'Create_Expense_Redistribute_18', 'type': 'expense'},
              {'id': 'Create_Expense_Redistribute_25_A04', 'type': 'expense'},
              {'id': 'AddPaymentV2_Redistribute_19', 'type': 'add_payment_v2'},
              {'id': 'Create_Expense_Redistribute_25_A07', 'type': 'expense'},
              {'id': 'AddPaymentV2_Redistribute_25', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_Redistribute_25_B2', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "",
             False, ""),
            ("Redistribute_Payments_17", 'Provide billed_entity id having payment and refund both',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'AddPaymentV2_Redistribute_08', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_Redistribute_17', 'type': 'add_payment_v2'}], 201, None, "", "", "", "", False, ""),
            ("Redistribute_Payments_18", 'Provide billed_entity id having payment and refund both in multiple account',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'AddPaymentV2_Redistribute_08', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_Redistribute_17', 'type': 'add_payment_v2'},
              {'id': 'Create_Expense_Redistribute_10', 'type': 'expense'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_19", 'Provide Invoice group id in request',
             [{'id': 'booking_153_invoice_preview_66', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'},
              {'id': 'invoicePreview_redistribute_payment_29', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_26', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_20",
             'Redistribute payment having payment on different billed entity and provide invoice group id in request',
             [{'id': 'booking_153_invoice_preview_66', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'},
              {'id': 'invoicePreview_redistribute_payment_29', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_21", 'Redistribute payment having extra payment on different billed entity and '
                                         'provide invoice group id in request',
             [{'id': 'booking_153_invoice_preview_66', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'},
              {'id': 'invoicePreview_redistribute_payment_29', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_001', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_22", 'Redistribute payment having multiple payment on different and same billed '
                                         'entity and provide invoice group id in request',
             [{'id': 'booking_153_invoice_preview_66', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'},
              {'id': 'invoicePreview_redistribute_payment_29', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_01', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_23", 'Redistribute payment having multiple and extra payment on different and same '
                                         'billed entity and provide invoice group id in request',
             [{'id': 'booking_153_invoice_preview_66', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'},
              {'id': 'invoicePreview_redistribute_payment_29', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_01', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_001', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_24", 'Redistribute payment having multiple and extra payment on different billed '
                                         'entity and provide invoice group id in request',
             [{'id': 'booking_153_invoice_preview_66', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'},
              {'id': 'invoicePreview_redistribute_payment_29', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_001', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_25", 'Redistribute payment having less payment on different and same billed entity'
                                         ' and provide invoice group id in request having complete checkout of two room',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_217', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_checkout_125', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_checkout_104', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_26", 'Redistribute payment having extra payment on different and same billed entity'
                                         ' and provide invoice group id in request having complete checkout of two room',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_217', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_checkout_125', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_checkout_104', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_spot_credit_16', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_27", 'Redistribute payment having less payment on different and same billed entity'
                                         ' and provide invoice group id in request having partial checkout of room',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_216', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_checkout_125', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_checkout_104', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
            ("Redistribute_Payments_28", 'Redistribute payment having extra payment on different and same billed entity'
                                         ' and provide invoice group id in request having partial checkout of room',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_216', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_checkout_125', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_checkout_104', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_spot_credit_16', 'type': 'add_payment_v2'}], 201,  None, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_redistribute_payment(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                                  error_code, error_message, dev_message, error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.redistribute_payments_request(client_, self.booking_request.bill_id,
                                                                      test_case_id, status_code, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request, self.booking_request.booking_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request, booking_id):
        validation = ValidationRedistributePayments(client, test_case_id, response, booking_request, booking_id)
        validation.validate_response()
        validation.validate_redistribute_audit_trail()
