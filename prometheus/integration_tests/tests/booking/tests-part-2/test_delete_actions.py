import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest, date, timedelta
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_delete_actions import (
    ValidationBeforeDeleteActions,
    ValidationDeleteActions,
)
from prometheus.integration_tests.utilities.common_utils import query_execute, get_rate_manager_enabled
from prometheus.tests.mockers import mock_treebo_tenant_boolean


class TestReverseNoShow(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, perform_night_audit, action_after_night_audit", [
            # --------------------------------- Positive Cases -----------------------------------#
            ("Delete_NoShow_01", 'Single room booking no-show and reverse no show.',
             NO_SHOW_SINGLE_DAY_BOOKING, 200, None, "", "", "", "", False, "", True,
             [{'id': "MarkNoShow_01", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_02", 'Multiple day, Single room booking no-show and reverse no show.',
             NO_SHOW_MULTIPLE_DAY_BOOKING, 200, None, "", "", "", "", False, "", True,
             [{'id': "MarkNoShow_01", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_03", 'Multiple room booking complete no-show and reverse no-show.',
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True,
             [{'id': "MarkNoShow_01", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_04", 'Multiple room booking with partial no-show and reverse no-show.',
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True,
             [{'id': "MarkNoShow_25", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_05", 'Single room booking with expense in it no-show and reverse no-show.',
             NO_SHOW_SINGLE_DAY_BOOKING, 200, None, "", "", "", "", False, "", True,
             [{'id': "Create_Expense_01", 'type': "expense"}, {'id': "MarkNoShow_01", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_06", 'Multiple room, single day booking with expense in it no-show and reverse no-show.',
             NO_SHOW_MULTIPLE_DAY_BOOKING, 200, None, "", "", "", "", False, "", True,
             [{'id': "Create_Expense_01", 'type': "expense"}, {'id': "MarkNoShow_01", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_07", 'Multiple room booking with expense complete no-show and reverse no-show.',
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True,
             [{'id': "Create_Expense_01", 'type': "expense"}, {'id': "MarkNoShow_01", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_08", 'Multiple room booking with expense ,partial no-show and reverse no-show.',
             NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING, 200, None, "", "", "", "", False, "", True,
             [{'id': "Create_Expense_01", 'type': "expense"}, {'id': "MarkNoShow_25", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_09", 'Single room booking no-show and reverse no show of OTA Booking',
             [{'id': "booking_01_no_show_ota", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True,
             [{'id': "MarkNoShow_01", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_10", 'Multiple day, Single room booking no-show and reverse no show of OTA Booking',
             [{'id': "booking_02_no_show_ota", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True,
             [{'id': "MarkNoShow_01", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_11", 'Multiple room booking complete no-show and reverse no-show of OTA Booking',
             [{'id': "booking_03_no_show_ota", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True,
             [{'id': "MarkNoShow_01", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_12", 'Multiple room booking with partial no-show and reverse no-show of OTA Booking',
             [{'id': "booking_03_no_show_ota", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True,
             [{'id': "MarkNoShow_25", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_13", 'Multiple guest booking with partial no-show and reverse no-show of OTA Booking',
             [{'id': "booking_05_no_show_ota", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True,
             [{'id': "MarkNoShow_17", 'type': 'mark_no_show'}]),
            ("Delete_NoShow_14", 'Edit commission then single room booking no-show and reverse no show of OTA Booking',
             [{'id': "booking_01_no_show_ota", 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", True, [{'id': "MarkNoShow_01", 'type': 'mark_no_show'}]),
        ])
    @pytest.mark.regression
    def test_reverse_no_show(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                             error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                             perform_night_audit, action_after_night_audit):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)

        expected_bill_response = self.billing_request.get_bill_request_v2(client_, self.booking_request.bill_id, 200)

        if action_after_night_audit:
            self.common_request_caller(client_, action_after_night_audit, hotel_id)

        ValidationBeforeDeleteActions(test_case_id).validate_data()

        response = self.booking_request.delete_booking_action(client_, status_code,
                                                              action_id=self.booking_request.action_id)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.booking_request.bill_id, expected_bill_response,
                            self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request, booking_id, billing_request, bill_id,
                   expected_bill_response, expense_request):
        validation = ValidationDeleteActions(client, response, test_case_id, booking_request, booking_id,
                                             billing_request, bill_id, expected_bill_response)
        validation.validate_response()
        validation.validate_charge_and_expense_status(expense_request)
        validation.validate_commissions()


class TestReverseCancel(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, perform_night_audit, action_after_night_audit", [
            # --------------------------------- Positive Cases -----------------------------------#
            ("Delete_Cancel_01", 'Single room booking cancel and reverse cancel.',
             SINGLE_BOOKING_01_USING_V2, 200, None, "", "", "", "", False, "", False,
             [{'id': "MarkCancelled_95", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_02", 'Multiple day, Single room booking cancel and reverse cancel.',
             MULTIPLE_DAYS_WALKIN_BOOKING_V2, 200, None, "", "", "", "", False, "", False,
             [{'id': "MarkCancelled_95", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_03", 'Multiple room booking complete cancel and reverse cancel.',
             MULTIPLE_ROOM_BOOKING_01_USING_V2, 200, None, "", "", "", "", False, "", False,
             [{'id': "MarkCancelled_95", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_04", 'Multiple room booking with partial cancel and reverse cancel.',
             MULTIPLE_ROOM_BOOKING_01_USING_V2, 400, None, "", "The actions provided cannot be reversed.", "", "",
             False, "", False, [{'id': "MarkCancelled_04", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_05", 'Single room booking with expense in it cancel and reverse cancel.',
             SINGLE_BOOKING_01_USING_V2, 200, None, "", "", "", "", False, "", False,
             [{'id': "Create_Expense_01", 'type': "expense"}, {'id': "MarkCancelled_95", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_06", 'Multiple room booking with expense complete cancel and reverse cancel',
             MULTIPLE_DAYS_WALKIN_BOOKING_V2, 200, None, "", "", "", "", False, "", False,
             [{'id': "Create_Expense_01", 'type': "expense"}, {'id': "MarkCancelled_95", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_07", ' Multiple room booking with expense ,partial cancel and reverse cancel',
             MULTIPLE_ROOM_BOOKING_01_USING_V2, 200, None, "", "", "", "", False, "", False,
             [{'id': "Create_Expense_01", 'type': "expense"}, {'id': "MarkCancelled_95", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_08", 'Single room booking cancel and reverse cancel.',
             SINGLE_ROOM_OTA_BOOKING_V2_01, 200, None, "", "", "", "", False, "", False,
             [{'id': "MarkCancelled_95", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_09", 'Multiple day, Single room booking cancel and reverse cancel.',
             MULTIPLE_DAY_OTA_BOOKING_V2_01, 200, None, "", "", "", "", False, "", False,
             [{'id': "MarkCancelled_95", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_10", 'Multiple room booking complete cancel and reverse cancel.',
             MULTIPLE_ROOM_OTA_BOOKING_V2_01, 200, None, "", "", "", "", False, "", False,
             [{'id': "MarkCancelled_95", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_11", 'Multiple room booking with partial cancel and reverse cancel.',
             MULTIPLE_ROOM_OTA_BOOKING_V2_01, 400, None, "04015408", "The actions provided cannot be reversed.", "", "",
             False, "", False, [{'id': "MarkCancelled_04", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_12", 'Multiple guest booking with partial cancel and reverse cancel of OTA Booking',
             [{'id': "ota_booking_multiple_guest", 'type': 'booking_v2'}], 400, None, "04015408",
             "The actions provided cannot be reversed.", "", "", False, "", False,
             [{'id': "MarkCancelled_02", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_13", 'Edit Commission then single room booking cancel and reverse cancel of OTA Booking',
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", False, [{'id': "MarkCancelled_95", 'type': 'mark_cancelled'}]),
            ("Delete_Cancel_14", 'Single room booking cancel and reverse cancel.',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", False,
             [{'id': "cancel_booking_56", 'type': 'mark_cancel',
               'tenant_config': get_rate_manager_enabled() + USE_CANCELLATION_POLICY}]),
        ])
    @pytest.mark.regression
    def test_reverse_cancel(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                            error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                            perform_night_audit, action_after_night_audit):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        expected_bill_response = self.billing_request.get_bill_request_v2(client_, self.booking_request.bill_id, 200)

        if action_after_night_audit:
            self.common_request_caller(client_, action_after_night_audit, hotel_id)

        ValidationBeforeDeleteActions(test_case_id).validate_data()

        response = self.booking_request.delete_booking_action(client_, status_code,
                                                              action_id=self.booking_request.action_id)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.booking_request.bill_id, expected_bill_response,
                            self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request, booking_id, billing_request, bill_id,
                   expected_bill_response, expense_request):
        validation = ValidationDeleteActions(client, response, test_case_id, booking_request, booking_id,
                                             billing_request, bill_id, expected_bill_response)
        validation.validate_response()
        validation.validate_charge_and_expense_status(expense_request)
        validation.validate_commissions()


class TestReverseCheckout(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, perform_night_audit, action_after_night_audit,"
        " lock_invoice, clubbed_taxation",
        [
            # --------------------------------- Positive Cases -----------------------------------#
            ("Delete_Checkout_01", 'Checkout complete booking and reverse checkout.',
             SINGLE_ROOM_CHECKOUT_BOOKING_01, 200, None, "", "", "", "", False, "", {'run_night_audit_before': 1},
             [{'id': "CheckoutV2_01", 'type': 'checkout_v2'}], "", False),
            ("Delete_Checkout_02", 'Early checkout booking and reverse early checkout.',
             EARLY_CHECKOUT_1ST_DAY_3_DAY_BOOKING, 200, None, "", "", "", "", False, "", False,
             [{'id': "CheckoutV2_91", 'type': 'checkout_v2'}], "", False),
            ("Delete_Checkout_03", 'Early checkout booking and reverse early checkout.',
             EARLY_CHECKOUT_1ST_DAY_3_DAY_BOOKING, 200, None, "", "", "", "", False, "", False,
             [{'id': "Create_Expense_checkout_23", 'type': "expense"}, {'id': "CheckoutV2_91", 'type': 'checkout_v2'}],
             "", False),
            ("Delete_Checkout_04", 'Early checkout booking and reverse early checkout.',
             EARLY_CHECKOUT_1ST_DAY_3_DAY_BOOKING, 200, None, "", "", "", "", False, "", False,
             [{'id': "CheckoutV2_91", 'type': 'checkout_v2'}], True, False),
            ("Delete_Checkout_05", 'Reverse checkout a multi room booking',
             [{'id': "booking_04", 'type': 'booking_v2'},
              {'id': "checkin_02", 'type': 'checkin_v2'}], 200, None, "", "", "", "", False, "",
             {'run_night_audit_before': 1}, [{'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
                                             {'id': "AddPaymentCheckoutV2_02", 'type': 'add_payment_v2'},
                                             {'id': "CheckoutV2_03", 'type': 'checkout_v2'}], True, False),
            ("Delete_Checkout_06", 'Reverse checkout a multiple day booking on first day checkout',
             [{'id': "booking_05", 'type': 'booking_v2'},
              {'id': "checkin_05", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_05", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'}], 200, None, "", "", "", "", False, "",
             {'run_night_audit_before': 1}, [{'id': "CheckoutV2_11", 'type': 'checkout_v2'}], True, False),
            ("Delete_Checkout_07", 'Reverse checkout a multiple day booking on second day checkout',
             [{'id': "booking_05", 'type': 'booking_v2'},
              {'id': "checkin_05", 'type': 'checkin_v2'}], 200, None, "", "", "", "", False, "",
             {'run_night_audit_before': 2}, [{'id': "invoicePreviewCheckoutV2_05", 'type': 'preview_invoice'},
                                             {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'},
                                             {'id': "CheckoutV2_11", 'type': 'checkout_v2'}], True, False),
            ("Delete_Checkout_08", 'Early checkout booking and reverse early checkout of OTA booking',
             EARLY_CHECKOUT_1ST_DAY_3_DAY_OTA_BOOKING, 200, None, "", "", "", "", False, "", False,
             [{'id': "CheckoutV2_141", 'type': 'checkout_v2'}], "", False),
            ("Delete_Checkout_09",
             'Edit commission then early checkout booking and reverse early checkout of OTA booking',
             [{'id': "ota_booking_3_days", 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_141', 'type': 'add_payment_v2'}],
             200, None, "", "", "", "", False, "", False, [{'id': "CheckoutV2_141", 'type': 'checkout_v2'}], "", False),
            ("Delete_Checkout_10", 'Early checkout booking and reverse early checkout.',
             [{'id': "booking_153_checkout_138", 'type': 'booking_v2',
               'extras': {'has_slab_based_taxation': True, 'hotel_level_config': CLUBBED_TAX_CONFIG}},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_edit_charge_61', 'type': 'add_payment_v2'}], 200, None, "", "", "", "", False, "",
             False, [{'id': "CheckoutV2_01", 'type': 'checkout_v2'}], True, True),
            ("Delete_Checkout_11", 'Checkout complete booking and reverse checkout of two room having early checkout '
                                   'charges on both room where invoice is not locked',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'}],
             200, None, "", "", "", "", False, "", {'run_night_audit_before': 0},
             [{'id': 'invoicePreview_217', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_checkout_116', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_001', 'type': 'add_payment_v2'},
              {'id': 'Redistribute_Payments_Checkout_116', 'type': 'redistribute_payment'},
              {'id': "CheckoutV2_116", 'type': 'checkout_v2'}], False, False),
            ("Delete_Checkout_12", 'Checkout complete booking and reverse checkout of two room having early checkout '
                                   'charges on both room where invoice is locked',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'}], 200,
             None, "", "", "", "", False, "", {'run_night_audit_before': 0},
             [{'id': 'invoicePreview_217', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_checkout_116', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_001', 'type': 'add_payment_v2'},
              {'id': 'Redistribute_Payments_Checkout_116', 'type': 'redistribute_payment'},
              {'id': "CheckoutV2_116", 'type': 'checkout_v2'}], True, False),
        ])
    @pytest.mark.regression
    def test_reverse_checkout(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                              error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                              perform_night_audit, action_after_night_audit, lock_invoice, clubbed_taxation):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if test_case_id == 'Delete_Checkout_01':
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        if perform_night_audit:
            for no_of_times_run_night_audit in range(perform_night_audit['run_night_audit_before']):
                with mock_treebo_tenant_boolean():
                    self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)

        if action_after_night_audit:
            self.common_request_caller(client_, action_after_night_audit, hotel_id)

        if lock_invoice:
            query_execute(db_queries.UPDATE_INVOICE_STATUS_TO_LOCKED)

        expected_bill_response = self.billing_request.get_bill_request_v2(client_, self.booking_request.bill_id, 200)

        response = self.booking_request.delete_booking_action(client_, status_code,
                                                              action_id=self.booking_request.action_id,
                                                              clubbed_taxation=clubbed_taxation)
        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.booking_request.bill_id, expected_bill_response,
                            self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request, booking_id, billing_request, bill_id,
                   expected_bill_response, expense_request):
        validation = ValidationDeleteActions(client, response, test_case_id, booking_request, booking_id,
                                             billing_request, bill_id, expected_bill_response)
        validation.validate_checkout_response()
        validation.validate_charge_and_expense_status(expense_request)
        validation.validate_commissions()
