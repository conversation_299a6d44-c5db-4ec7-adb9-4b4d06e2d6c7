from datetime import date, timedelta

import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_preview_invoice import (
    ValidationPreviewInvoice,
)
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestPreviewInvoice(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, extra_data, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message, extras", [
            ("invoicePreview_01", 'invoice preview creation for a single guest when "is_advance" is true"',
             SINGLE_BOOKING_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_02", 'invoice preview creation for a single guest when "is_advance" is false',
             SINGLE_BOOKING_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_03", 'invoice preview creation for multiple guests when "is_advance" is true',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_04", 'invoice preview creation for multiple guests when "is_advance" is false',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_05", 'invoice preview creation for only checked_in guests  of multiple rooms',
             PART_BOOKING_CHECK_IN_01, {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "",
             [False, '']),
            ("invoicePreview_08",
             'invoice preview creation for all guest of a single room booking where all dues are paid',
             PAST_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_09", 'invoice preview creation for all guest of a multi room booking where all dues are '
                                  'paid, only consumed charges for first day as preview date not checkout date',
             [{'id': "Booking_42_FullPayment_multipleRooms", 'type': 'booking'},
              {'id': "checkinBookingStatus_38", 'type': 'check_in'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", True, "Please send prices only for the room stay dates where occupancy or room type has changed,"
                           " and existing charges hasn't been consumed", [False, '']),
            ("invoicePreview_10", 'invoice preview creation for all guest of one room in multi room booking where all '
                                  'dues are paid',
             [{'id': "Booking_42_FullPayment_multipleRooms", 'type': 'booking'},
              {'id': "checkinBookingStatus_38", 'type': 'check_in'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", True, "Please send prices only for the room stay dates where occupancy or room type has changed, "
                           "and existing charges hasn't been consumed", [False, '']),
            ("invoicePreview_11", 'invoice preview with preview date as checkout date to get all charges consumed',
             [{'id': "Booking_42_FullPayment_multipleRooms", 'type': 'booking'},
              {'id': "checkinBookingStatus_38", 'type': 'check_in'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", True, "Please send prices only for the room stay dates where occupancy or room type has changed,"
                           " and existing charges hasn't been consumed", [False, '']),
            ("invoicePreview_12", 'invocie preview for a guest not in checked_in state but present in room',
             PART_BOOKING_CHECK_IN_01, {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "",
             [False, '']),
            ("invoicePreview_13", 'invocie preview for a guest not belonging to that room',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'}],
             {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_14", "invoice preview by making 'guest_ids' field as blank", SINGLE_BOOKING_CHECK_IN_01,
             {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_15", "removing mandatory fields for invoice preview", SINGLE_BOOKING_CHECK_IN_01,
             {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_16", "Invoice preview for preview date before the charge was consumed/pastDate",
             SINGLE_BOOKING_CHECK_IN_01, {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "",
             [False, '']),
            ("invoicePreview_17", "invoice preview for more room_stay_id not present in booking",
             SINGLE_BOOKING_CHECK_IN_01, {"include_kerala_cess": False}, 404, None, "", "", "", "", False, "",
             [False, '']),
            ("invoicePreview_18", "invoice preview for guest with type as credit,non-credit and bill_to as "
                                  "company,guest,is_advance as true",
             [{'id': "Booking_Credit_NonCredit", 'type': 'booking'},
              {'id': "checkinBookingStatus_40", 'type': 'check_in'},
             {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
             {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 3}], {"include_kerala_cess": False}, 200,
            None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_19", "invoice preview for guest with type as credit,non-credit and bill_to as "
                                  "company,guest,is_advance as false",
             [{'id': "Booking_Credit_NonCredit", 'type': 'booking'},
              {'id': "checkinBookingStatus_40", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 3}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_20",
             "invoice preview with default vlalue of is_advance by leaving it blank (default-false)",
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_23", "invoice preview after adding an expense of non-credit, paid_to_guest type",
             [{'id': "Booking_11", 'type': 'booking'}, {'id': "checkinPost_01", 'type': 'check_in'},
              {'id': "AddExpense_01", 'type': 'add_expense'}], {"include_kerala_cess": False}, 200, None, "", "", "",
             "", True, "", [False, '']),
            ("invoicePreview_24", "invoice preview after adding an credit_billToCompany add-on for b2b booking",
             [{'id': "Booking_11", 'type': 'booking'}, {'id': "AddOns_02", 'type': 'add_addon_v1'},
              {'id': "checkinPost_01", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 3}, ], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_25", "invoice preview after adding an expense of credit type",
             [{'id': "Booking_11", 'type': 'booking'}, {'id': "checkinPost_01", 'type': 'check_in'},
              {'id': "AddExpense_06_invoiceRegeneration", 'type': 'add_expense', 'is_mock_rule_req': True},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_26", "invoice preview after adding an add-on of non-credit type for b2b booking",
             [{'id': "Booking_11", 'type': 'booking'}, {'id': "AddOns_01", 'type': 'add_addon_v1'},
              {'id': "checkinPost_01", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_27", "invoice preview for a reserved guest of a part_checked_in booking",
             [{'id': "Booking_noshowAction_02", 'type': 'booking'},
              {'id': "checkinBookingStatus_38", 'type': 'check_in'}], {"include_kerala_cess": False}, 400, None, "", "",
             "", "", True, "Please send prices only for the room stay dates where occupancy or room type has changed, "
                           "and existing charges hasn't been consumed", [False, '']),
            ("invoicePreview_28", "invoice preview for a reserved room in a part_checked_in booking",
             [{'id': "Booking_noshowAction_02", 'type': 'booking'},
              {'id': "checkinBookingStatus_38", 'type': 'check_in'}], {"include_kerala_cess": False}, 400, None, "", "",
             "", "", True, "Please send prices only for the room stay dates where occupancy or room type has changed, "
                           "and existing charges hasn't been consumed", [False, '']),
            ("invoicePreview_29", "invoice preview for a cancelled guest of a checkin booking",
             [{'id': "Booking_cancelAction_01", 'type': 'booking'}, {'id': "CancelAction_01", 'type': 'cancel'}],
             {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_30", "invoice preview for a cancelled room in a checkin booking",
             [{'id': "Booking_cancelAction_02", 'type': 'booking'}, {'id': "CancelAction_06", 'type': 'cancel'}],
             {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_31", "invoice preview for a noshow guest of a checkin booking",
             [{'id': "Booking_noshowAction_01", 'type': 'booking'}, {'id': "noshowAction_01", 'type': 'noshow'}],
             {"include_kerala_cess": False}, 400, None, "", "", "", "", True,
             "Cannot mark booking noshow before midnight of checkin date. Please cancel if necessary", [False, '']),
            ("invoicePreview_32", "invoice preview for a noshow room in a checkin booking",
             [{'id': "Booking_noshowAction_02", 'type': 'booking'}, {'id': "noshowAction_03", 'type': 'noshow'}],
             {"include_kerala_cess": False}, 400, None, "", "", "", "", True,
             "Please send prices only for the room stay dates where occupancy or room type has changed, and existing "
             "charges hasn't been consumed", [False, '']),
            ("invoicePreview_33", "invoice preview for the booking in confirmed state", SINGLE_BOOKING_01,
             {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_34", "invoice preview for the booking in reserved state", SINGLE_BOOKING_02,
             {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_35", "invoice preview for the booking in cancelled state",
             [{'id': "Booking_cancelAction_01", 'type': 'booking'}, {'id': "CancelAction_14", 'type': 'cancel'}],
             {"include_kerala_cess": False}, 400, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_36", "invoice preview for the booking in noshow state",
             [{'id': "Booking_noshowAction_01", 'type': 'booking'}, {'id': "noshowAction_12", 'type': 'noshow'}],
             {"include_kerala_cess": False}, 400, None, "", "", "", "", True,
             "Cannot mark booking noshow before midnight of checkin date. Please cancel if necessary", [False, '']),
            ("invoicePreview_37", "invoice preview creation for few guests out of many checked_in guests",
             PAST_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_38", "invoice preview for guest in checkin state where all other guests are not in "
                                  "checkin state for single room booking",
             [{'id': "Booking_41_FullPayment_singleRoom", 'type': 'booking'},
              {'id': 'checkinBookingStatus_35', 'type': 'check_in'}], {"include_kerala_cess": False}, 400, None, "", "",
             "", "", False, "", [False, '']),
            ("invoicePreview_39_NoInvoice", "invoice preview for guest with no charge mapping after all charges are "
                                            "cancelled before preview",
             [{'id': "Booking_01", 'type': 'booking'}, {'id': "checkinPost_01", 'type': 'check_in'},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': 1}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_41", 'invoice preview creation for a single guest when "is_advance" is true',
             SINGLE_BOOKING_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_42", 'Invoice preview on 2nd day and 2nd guest',
             SINGLE_BOOKING_CHECK_IN_01 + [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_43", 'Invoice preview on 2nd day and for all guests',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_44", 'invoice preview on checkout date for a single guest when "is_advance" is true ',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_46", 'invoice preview for a reserved room in a part_checked_in booking',
             [{'id': "Booking_noshowAction_02", 'type': 'booking'}, {'id': "noshowAction_03", 'type': 'noshow'}],
             {"include_kerala_cess": False}, 400, None, "", "", "", "", True,
             "Please send prices only for the room stay dates where occupancy or room type has changed, "
             "and existing charges hasn't been consumed", [False, '']),
            ("invoicePreview_55", 'invoice preview creation for a single guest for a B2B booking and bill to comapany',
             [{'id': "Booking_77", 'type': 'booking'}, {'id': "checkinPost_61", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_59", 'invoice preview for guest with type as credit,non-credit and bill_to as '
                                  'company,guest,is_advance as true',
             [{'id': "Booking_Credit_NonCredit", 'type': 'booking'},
              {'id': "checkinBookingStatus_40", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_60", 'invoice preview for guest with type as credit,non-credit and bill_to as '
                                  'company,guest,is_advance as true',
             [{'id': "Booking_Credit_NonCredit", 'type': 'booking'},
              {'id': "checkinBookingStatus_40", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 3}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_61", 'invoice preview creation for multiple guests when "is_advance" is true',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_62", 'invoice preview creation for multiple guests when "is_advance" is true',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_63", 'invoice preview creation for multiple guests when "is_advance" is true',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_64", 'invoice preview creation for multiple guests when "is_advance" is true',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_66",
             'Create a booking for 3 days and generate invoice with booked charges only pending on 1st day',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_67",
             'Create a booking for 3 days and generate invoice with booked allowances only pending on 1st day',
             BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "", "", False,
             "", [False, '']),
            ("invoicePreview_68",
             'Create a booking for 3 days and generate invoice with booked charges and allowances pending on 1st day',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_69",
             'Create a booking for 3 days and provide all pending charges to cancel on 1st day, where only booked '
             'charges present',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'invoicePreview_66', 'type': 'preview_invoice'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_70",
             'Create a booking for 3 days and provide all pending allowances to cancel on 1st day, where only booked'
             ' allowances are present', BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_71",
             'Create a booking for 3 days and provide all pending charges to cancel and all pending allownace to post'
             ' on 1st day, where booked charges and allowances present',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_72",
             'Create a booking for 3 days and provide all pending allowances to cancel all pending charges to post on'
             ' 1st day, where booked charges and allowances are present',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_73",
             'Create a booking for 3 days and provide all pending charges and allowances to cancel on 1st day',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_74",
             'Create a booking for 3 days and provide all pending charges to post on 1st day, where only booked '
             'charges present',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_75",
             'Create a booking for 3 days and provide all pending allowances to post on 1st day, where only booked '
             'allowances are present', BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_76",
             'Create a booking for 3 days and provide all pending charges and allowances to post on 1st day',
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_77",
             'Create a booking for 3 days and generate invoice with booked charges only pending on 2nd day',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", False, "", [True, ""]),
            ("invoicePreview_78",
             'Create a booking for 3 days and generate invoice with booked allowances only pending on 2nd day',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", False, "", [True, [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                        {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [2, 1]}]]),
            ("invoicePreview_79",
             'Create a booking for 3 days and generate invoice with booked charges and allowances only pending on '
             '2nd day',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", False, "", [True, [{'id': 'Create_Expense_01', 'type': 'expense'},
                                        {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                        {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [2, 1]}]]),
            ("invoicePreview_80",
             'Create a booking for 3 days and provide charges to post when generating invoice on 2nd day',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", False, "", [True, ""]),
            ("invoicePreview_81",
             'Create a booking for 3 days and provide allowances to post when generating invoice on 2nd day',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", False, "", [True, [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                        {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [2, 1]}]]),
            ("invoicePreview_82",
             'Create a booking for 3 days and and provide charges and allowances to post when generating invoice on '
             '2nd day',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", False, "", [True, [{'id': 'Create_Expense_01', 'type': 'expense'},
                                        {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                        {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [2, 1]}]]),
            ("invoicePreview_83",
             'Cancel room stay charge and then create expense and post the charge, then generate invoice',
             [{'id': "booking_01_invoice_preview_83", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '1'}], {"include_kerala_cess": False},
             200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_84", 'Provide booked_charges_to_post as NULL',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04010006",
             "[Booked Charges To Post] -> Field may not be null.", "", {"field": "booked_charges_to_post"}, False, "",
             [False, '']),
            ("invoicePreview_85", 'Provide booked_charges_to_post as empty',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04010006", "[Booked Charges To Post] -> Not a valid list.", "",
             {"field": "booked_charges_to_post"}, False, "", [False, '']),
            ("invoicePreview_86", 'Provide Invalid charge IDs in booked_charges_to_post',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04010203",
             "Not all charges provided in the decision are booked charges.", "", "", False, "", [False, '']),
            ("invoicePreview_87", 'Provide booked_allowances_to_post as NULL',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04010006",
             "[Booked Allowances To Post] -> Field may not be null.", "", {"field": "booked_allowances_to_post"},
             False, "", [False, '']),
            ("invoicePreview_88", 'Provide booked_allowances_to_post as Empty',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04010006", "[Booked Allowances To Post] -> Invalid type.", "",
             {"field": "booked_allowances_to_post"}, False, "", [False, '']),
            ("invoicePreview_89", 'Provide charge_id in booked_allowances_to_post as NULL',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Id] -> Field may not be null.", "", {"field": "booked_allowances_to_post.0.charge_id"},
             False, "", [False, '']),
            ("invoicePreview_90", 'Provide charge_id in booked_allowances_to_post as Empty',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Id] -> Not a valid integer.", "", {"field": "booked_allowances_to_post.0.charge_id"}, False, "",
             [False, '']),
            ("invoicePreview_91", 'Provide charge_id in booked_allowances_to_post as Invalid',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Id] -> Not a valid integer.", "", {"field": "booked_allowances_to_post.0.charge_id"}, False, "",
             [False, '']),
            ("invoicePreview_92", 'Provide charge_split_id in booked_allowances_to_post as NULL',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Split Id] -> Field may not be null.", "",
             {"field": "booked_allowances_to_post.0.charge_split_id"}, False, "", [False, '']),
            ("invoicePreview_93", 'Provide charge_split_id in booked_allowances_to_post as Empty',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Split Id] -> Not a valid integer.", "", {"field": "booked_allowances_to_post.0.charge_split_id"},
             False, "", [False, '']),
            ("invoicePreview_94", 'Provide charge_split_id in booked_allowances_to_post as Invalid',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Split Id] -> Not a valid integer.", "", {"field": "booked_allowances_to_post.0.charge_split_id"},
             False, "", [False, '']),
            ("invoicePreview_95", 'Provide allowance_id in booked_allowances_to_post as NULL',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Allowance Id] -> Field may not be null.", "", {"field": "booked_allowances_to_post.0.allowance_id"},
             False, "", [False, '']),
            ("invoicePreview_96", 'Provide charge_split_id in booked_allowances_to_post as Empty',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Allowance Id] -> Not a valid integer.", "", {"field": "booked_allowances_to_post.0.allowance_id"},
             False, "", [False, '']),
            ("invoicePreview_97", 'Provide allowance_id in booked_allowances_to_post as Invalid',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Allowance Id] -> Not a valid integer.", "", {"field": "booked_allowances_to_post.0.allowance_id"},
             False, "", [False, '']),
            ("invoicePreview_98",
             'Provide allowance_id different with respect to charge_id, where allowance_id is wrong',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_99",
             'Provide allowance_id different with respect to charge_id, where charge_id is wrong',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_100",
             'Provide charge_split_id different with respect to charge_id, where charge_split_id is wrong',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_101",
             'Provide charge_split_id different with respect to charge_id, where charge_id is wrong',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_102", 'Provide allowance_id, charge_split_id different with respect to charge_id',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_103", 'Provide only charge_id in booked_allowances_to_post',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_104", 'Provide only allowance_id in booked_allowances_to_post',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_105", 'Provide only charge_split_id in booked_allowances_to_post',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_106", 'Provide only charge_id, charge_split_id in booked_allowances_to_post',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_107", 'Provide only allowance_id, charge_split_id in booked_allowances_to_post',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_108", 'Provide only allowance_id, charge_id in booked_allowances_to_post',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_109", 'Create a booking which has Cancel charge, posted charge, booked charge',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE, {"include_kerala_cess": False}, 200, None, "", "", "", "", False,
             "", [False, '']),
            ("invoicePreview_110", 'Create a booking which has Cancel allowance, posted allowance, booked allowance',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "", "",
             False, "", [False, '']),
            ("invoicePreview_111", 'Create a booking which has Cancel extra charge, booked charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'}], {"include_kerala_cess": False},
             200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_112", 'Create a booking which has Cancel charge, booked charge, booked allowance',
             BOOKING_WITH_CANCEL_BOOKED_CHARGE_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_113", 'Create a booking which has Cancel allowance, booked allowance',
             BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "",
             [False, '']),
            ("invoicePreview_114", 'Create a booking which has Cancel allowance, booked charge, booked allowance',
             BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE_BOOKED_CHARGE, {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_115",
             'Create a booking which has Cancel charge, posted charge, booked charge, booked allowance',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "",
             "", "", "", False, "", [False, '']),
            ("invoicePreview_116",
             'Create a booking which has Cancel allowance, posted allowance, booked allowance, booked charge',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE_BOOKED_CHARGE, {"include_kerala_cess": False}, 200, None, "",
             "", "", "", False, "", [False, '']),
            ("invoicePreview_117",
             'Create a booking which on checkout date has no booked charge and no booked allowance',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False},
             200, None, "", "", "", "", False, "", [True, '']),
            ("invoicePreview_118", 'Create a booking which on checkout date has booked charge and no booked allowance',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [True, '']),
            ("invoicePreview_119", 'Create a booking which on checkout date has no booked charge and booked allowance',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", False, "", [True, [{'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]}]]),
            ("invoicePreview_120", 'Create a booking which on checkout date has booked charge and booked allowance',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "",
             [True, [{'id': 'Create_Expense_01', 'type': 'expense'},
                     {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]}]]),
            ("invoicePreview_121",
             'Create a booking which on checkout date has booked charge and no booked allowance, and post the charge',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [True, '']),
            ("invoicePreview_122", 'Create a booking which on checkout date has no booked charge and booked allowance,'
                                   ' and post the allowance',
             [{'id': "booking_01_invoice_preview_117", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", False, "", [True, [{'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]}]]),
            ("invoicePreview_123", 'Provide charge to post which is already cancelled',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'}],
             {"include_kerala_cess": False}, 400, None, "04010203",
             "Not all charges provided in the decision are booked charges.", "", "", False, "", [False, '']),
            ("invoicePreview_124", 'Provide allowance to post which is already cancelled',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
              {'id': "AddAllowance_21_expense_01", 'type': 'add_allowance_v2', 'extras': [6, 1]},
              {'id': "UpdateAllowance_02", 'type': 'update_allowance_v2', 'extras': [6, 1, 1, 'cancelled']}],
             {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", False, "", [False, '']),
            ("invoicePreview_125",
             'Create a booking for 1 day with two guest and generate invoice for one guest from it which do not have'
             ' charges mapped to it',
             [{'id': "booking_23", 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'type': 'get_booking'}, {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'Create_Expense_01_invoice_preview_125', 'type': 'expense'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_126", 'Create a booking for 1 day with two guest and generate invoice for one guest from'
                                   ' it which have booked charge mapped to it',
             [{'id': "booking_23", 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'Create_Expense_01_invoice_preview_125', 'type': 'expense'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_127", 'Create a booking for 1 day with two room and checkout one room from it which do not'
                                   ' have charges mapped to it',
             [{'id': "booking_04", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_128", 'Create a booking for 1 day with room and checkout one room from it which have'
                                   ' billed entity in which booked charge mapped to it',
             [{'id': "booking_04", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_invoice_preview_128', 'type': 'expense'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_129",
             'Create a booking which has Cancel charge, posted charge, booked charge, and post them',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE, {"include_kerala_cess": False}, 200, None, "", "", "", "", False,
             "", [False, '']),
            ("invoicePreview_130",
             'Create a booking which has Cancel allowance, posted allowance, booked allowance and post them',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "", "",
             False, "", [False, '']),
            ("invoicePreview_131", 'Create a booking which has Cancel extra charge, booked charge and post them',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'}], {"include_kerala_cess": False},
             200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_132",
             'Create a booking which has Cancel charge, booked charge, booked allowance and post them',
             BOOKING_WITH_CANCEL_BOOKED_CHARGE_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_133", 'Create a booking which has Cancel allowance, booked allowance and post them',
             BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "",
             [False, '']),
            ("invoicePreview_134",
             'Create a booking which has Cancel allowance, booked charge, booked allowance and post them',
             BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE_BOOKED_CHARGE, {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_135",
             'Create a booking which has Cancel charge, posted charge, booked charge, booked allowance and post them',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 200, None, "",
             "", "", "", False, "", [False, '']),
            ("invoicePreview_136",
             'Create a booking which has Cancel allowance, posted allowance, booked allowance, booked charge and post'
             ' them', BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE_BOOKED_CHARGE, {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_137", 'Create a booking for 2 day with two room and checkout both room on 1st day, and'
                                   ' post charge of both room',
             [{'id': "booking_08", 'type': 'booking_v2'}, {'id': "checkin_preview_invoice_137", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_138", 'Create a booking for 2 day with two room and checkout both room on 1st day, and'
                                   ' check pending charge',
             [{'id': "booking_08", 'type': 'booking_v2'}, {'id': "checkin_preview_invoice_137", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_139", 'Create a booking for 2 day with two room and checkout both room on 1st day, and'
                                   ' cancel charge of one room',
             [{'id': "booking_08", 'type': 'booking_v2'}, {'id': "checkin_preview_invoice_137", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_140", 'Create a booking for 2 day with two room and checkout both room on 1st day, and'
                                   ' cancel charge of both room',
             [{'id': "booking_08", 'type': 'booking_v2'}, {'id': "checkin_preview_invoice_137", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_141", 'Create a booking having multiple allowance on same charge',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
              {'id': "AddAllowance_21_expense_01", 'type': 'add_allowance_v2', 'extras': [1, 1]}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_142", 'Create a booking having multiple allowance on same charge and cancel 1st allowance',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
              {'id': "AddAllowance_21_expense_01", 'type': 'add_allowance_v2', 'extras': [1, 1]},
              {'id': "UpdateAllowance_02", 'type': 'update_allowance_v2', 'extras': [1, 1, 1, 'cancelled']}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_143", 'Create a booking having multiple allowance on same charge and post both allowance',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
              {'id': "AddAllowance_21_expense_01", 'type': 'add_allowance_v2', 'extras': [1, 1]}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_144", 'Create a booking having multiple allowance on same charge and post 2nd allowance',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
              {'id': "AddAllowance_21_expense_01", 'type': 'add_allowance_v2', 'extras': [1, 1]}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_145", 'Create a booking having account with only payment in it',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_01', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200, None, "", "",
             "", "", False, "", [False, '']),
            ("invoicePreview_146", 'Provide inclusion id in booked charge to post',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 400, None,
             "********", "Only Inclusion charges cannot be invoiced. Please send the parent charge ids only.", "", "",
             False, "", [False, '']),
            ("invoicePreview_147", 'Provide future charge id in booked charge to post',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 400, None, "********",
             "Future charges cannot be invoiced.", "", "", False, "", [False, '']),
            ("invoicePreview_148", 'Provide inclusion id in booked charge to cancel',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 400, None,
             "********", "Only Inclusion charges cannot be invoiced. Please send the parent charge ids only.", "", "",
             True, "Need to fix", [False, '']),
            ("invoicePreview_149", 'Provide future charge id in booked charge to cancel',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 400, None, "********",
             "Future charges cannot be invoiced.", "", "", True, "Need to fix", [False, '']),
            ("invoicePreview_150", 'Provide booked_charges_to_cancel as NULL',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04010006",
             "[Booked Charges To Cancel] -> Field may not be null.", "", {"field": "booked_charges_to_cancel"}, False, "",
             [False, '']),
            ("invoicePreview_151", 'Provide booked_charges_to_cancel as empty',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04010006", "[Booked Charges To Cancel] -> Not a valid list.",
             "", {"field": "booked_charges_to_cancel"}, False, "", [False, '']),
            ("invoicePreview_152", 'Provide Invalid charge IDs in booked_charges_to_cancel',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04010203",
             "Not all charges provided in the decision are booked charges.", "", "", True, "Need to fix", [False, '']),
            ("invoicePreview_153", 'Provide booked_allowances_to_cancel as NULL',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04010006",
             "[Booked Allowances To Cancel] -> Field may not be null.", "", {"field": "booked_allowances_to_cancel"},
             False, "", [False, '']),
            ("invoicePreview_154", 'Provide booked_allowances_to_cancel as Empty',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04010006", "[Booked Allowances To Cancel] -> Invalid type.",
             "", {"field": "booked_allowances_to_cancel"}, False, "", [False, '']),
            ("invoicePreview_155", 'Provide charge_id in booked_allowances_to_cancel as NULL',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Id] -> Field may not be null.", "", {"field": "booked_allowances_to_cancel.0.charge_id"},
             False, "", [False, '']),
            ("invoicePreview_156", 'Provide charge_id in booked_allowances_to_cancel as Empty',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Id] -> Not a valid integer.", "", {"field": "booked_allowances_to_cancel.0.charge_id"}, False, "",
             [False, '']),
            ("invoicePreview_157", 'Provide charge_id in booked_allowances_to_cancel as Invalid',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Id] -> Not a valid integer.", "", {"field": "booked_allowances_to_cancel.0.charge_id"}, False, "",
             [False, '']),
            ("invoicePreview_158", 'Provide charge_split_id in booked_allowances_to_cancel as NULL',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Split Id] -> Field may not be null.", "",
             {"field": "booked_allowances_to_cancel.0.charge_split_id"}, False, "", [False, '']),
            ("invoicePreview_159", 'Provide charge_split_id in booked_allowances_to_cancel as Empty',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Split Id] -> Not a valid integer.", "",
             {"field": "booked_allowances_to_cancel.0.charge_split_id"}, False, "", [False, '']),
            ("invoicePreview_160", 'Provide charge_split_id in booked_allowances_to_cancel as Invalid',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Charge Split Id] -> Not a valid integer.", "",
             {"field": "booked_allowances_to_cancel.0.charge_split_id"}, False, "", [False, '']),
            ("invoicePreview_161", 'Provide allowance_id in booked_allowances_to_cancel as NULL',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Allowance Id] -> Field may not be null.", "", {"field": "booked_allowances_to_cancel.0.allowance_id"},
             False, "", [False, '']),
            ("invoicePreview_162", 'Provide charge_split_id in booked_allowances_to_cancel as Empty',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Allowance Id] -> Not a valid integer.", "", {"field": "booked_allowances_to_cancel.0.allowance_id"},
             False, "", [False, '']),
            ("invoicePreview_163", 'Provide allowance_id in booked_allowances_to_cancel as Invalid',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010006",
             "[Allowance Id] -> Not a valid integer.", "", {"field": "booked_allowances_to_cancel.0.allowance_id"},
             False, "", [False, '']),
            ("invoicePreview_164",
             'Provide allowance_id different with respect to charge_id, where allowance_id is wrong',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_165",
             'Provide allowance_id different with respect to charge_id, where charge_id is wrong',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_166",
             'Provide charge_split_id different with respect to charge_id, where charge_split_id is wrong',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_167",
             'Provide charge_split_id different with respect to charge_id, where charge_id is wrong',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_168", 'Provide allowance_id, charge_split_id different with respect to charge_id',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_169", 'Provide only charge_id in booked_allowances_to_cancel',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_170", 'Provide only allowance_id in booked_allowances_to_cancel',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_171", 'Provide only charge_split_id in booked_allowances_to_cancel',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_172", 'Provide only charge_id, charge_split_id in booked_allowances_to_cancel',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_173", 'Provide only allowance_id, charge_split_id in booked_allowances_to_cancel',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_174", 'Provide only allowance_id, charge_id in booked_allowances_to_cancel',
             BOOKING_V2_WITH_BOOKED_ALLOWANCE, {"include_kerala_cess": False}, 400, None, "04010204",
             "Not all allowances provided in the decision are booked allowances.", "", "", True, "Need to fix",
             [False, '']),
            ("invoicePreview_175", 'Provide charge to cancel which is already posted',
             [{'id': "booking_154_invoice_preview_77", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 400, None, "04010006",
             "[Allowance Id] -> Not a valid integer.", "", "", True, "Need to check", [True, '']),
            ("invoicePreview_176",
             'Provide charge when booking have company and travel agent details and charges mapped to TA',
             [{'id': "booking_48", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_177", 'Provide charge when booking have company details',
             [{'id': "booking_14", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_178", 'Provide split charge',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "Create_Expense_split", 'type': 'expense'}], {"include_kerala_cess": False}, 400, None, "04010208",
             "Invalid decision provided for charge, Decision for a charge can't be both POST and CANCEL.", "", "",
             False, "", [False, '']),
            ("invoicePreview_179",
             'Provide charge when booking have company and travel agent details and charges mapped to Company',
             [{'id': "booking_48_update_billing_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_180", 'Create a booking for 3 days and generate invoice after charge split',
             [{'id': "booking_153_checkout_138", 'type': 'booking_v2', 'extras': {"has_slab_based_taxation": True}},
              {'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1, 'has_slab_based_taxation': True},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "", "", "",
             False, "", [False, '']),
            ("invoicePreview_181", 'Provide Invalid cancellation policy',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 400, None, "04010006",
             "[Cancellation Policy] -> 'Invalid' is not a valid choice for cancellation policy", "",
             {"field": "cancellation_policy"}, False, "", [False, '']),
            ("invoicePreview_182",
             'Provide cancellation policy as retain_complete_booking_amount, when payment is not present',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_183", 'Provide cancellation policy as retain_complete_payment, when payment is not present',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_184",
             'Provide cancellation policy as complete_cancellation_fee_waiver, when payment is not present',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_185",
             'Provide cancellation policy as cancellation_fee_as_per_rate_plan, when payment is not present',
             [{'id': "booking_153_invoice_preview_189", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "", "",
             "", False, "", [False, '']),
            ("invoicePreview_186", 'Provide cancellation policy as retain_complete_booking_amount, when payment is less'
                                   ' than cancellation charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_17.3', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_187", 'Provide cancellation policy as retain_complete_payment, when payment is less'
                                   ' than cancellation charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_17.3', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_188", 'Provide cancellation policy as complete_cancellation_fee_waiver, when payment is '
                                   'less than cancellation charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_17.3', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_189", 'Provide cancellation policy as cancellation_fee_as_per_rate_plan, when payment is '
                                   'less than cancellation charge',
             [{'id': "booking_153_invoice_preview_189", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_17.3', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_190", 'Provide cancellation policy as retain_complete_booking_amount, when payment equal '
                                   'to cancellation charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_preview_invoice_190', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_191", 'Provide cancellation policy as retain_complete_payment, when payment equal '
                                   'to cancellation charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_preview_invoice_190', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_192", 'Provide cancellation policy as complete_cancellation_fee_waiver, when payment equal'
                                   ' to cancellation charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_preview_invoice_190', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_193", 'Provide cancellation policy as cancellation_fee_as_per_rate_plan, when payment '
                                   'equal to cancellation charge',
             [{'id': "booking_153_invoice_preview_189", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_preview_invoice_190', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_194", 'Provide cancellation policy as retain_complete_booking_amount, when payment more '
                                   'than cancellation charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_16', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_195", 'Provide cancellation policy as retain_complete_payment, when payment more '
                                   'than cancellation charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_16', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_196", 'Provide cancellation policy as complete_cancellation_fee_waiver, when payment more '
                                   'than cancellation charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_16', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_197", 'Provide cancellation policy as cancellation_fee_as_per_rate_plan, when payment more '
                                   'than cancellation charge',
             [{'id': "booking_153_invoice_preview_189", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_16', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_198", 'Provide cancellation policy as retain_complete_booking_amount, when payment more '
                                   'than cancellation charge and booking has both room stay and expense charge',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'Multiple_Expense_04', 'type': 'create_expense_V3'},
              {'id': 'AddPaymentV2_spot_credit_16', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_199",
             'Create a booking for 3 days and provide all pending charges to cancel on 1st day, where only booked '
             'charges present, with cancellation policy as retain complete booking amount',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_16', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "Need to fix", [False, '']),
            ("invoicePreview_200", 'Provide cancellation policy as cancellation_fee_as_per_rate_plan, when payment more '
                                   'than cancellation charge with 10 min before checkout time',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_16', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", True, "Skipping, running on local having issue on running in pipeline", [False, '', -10]),
            ("invoicePreview_201", 'Provide cancellation policy as cancellation_fee_as_per_rate_plan, when payment more '
                                   'than cancellation charge with 10 min after checkout time',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_spot_credit_16', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '', 10]),
            ("invoicePreview_202", 'Perform early checkout, while cancelling first day charge itself with cancellation '
                                   'policy according to rate plan',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_001', 'type': 'add_payment_v2'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_203", 'Create a booking which has Cancel charge, posted charge, booked charge, and post '
                                   'them with cancellation policy as retain booking amount',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE, {"include_kerala_cess": False}, 200, None, "", "", "", "", False,
             "", [False, '']),
            ("invoicePreview_204",
             'Create a booking for 1 day with two room and checkout one room from it which do not have charges mapped '
             'to it and post the charge with cancellation policy as retain booking amount',
             [{'id': "booking_04", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_205",
             'Create a booking for 1 day with room and checkout one room from it which have billed entity in which '
             'booked charge mapped to it and post the charge with cancellation policy as retain booking amount',
             [{'id': "booking_04", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_invoice_preview_128', 'type': 'expense'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_206",
             'Create a booking for 1 day with two room and checkout one room from it which do not have charges mapped '
             'to it and cancel the charge with cancellation policy as retain booking amount',
             [{'id': "booking_04", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_207",
             'Create a booking for 1 day with room and checkout one room from it which have billed entity in which '
             'booked charge mapped to it and cancel the charge with cancellation policy as retain booking amount',
             [{'id': "booking_04", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_invoice_preview_128', 'type': 'expense'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_208",
             'Create a booking for 1 day with two guest and generate invoice for one guest from it which do not have'
             ' charges mapped to it',
             [{'id': "booking_23", 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'type': 'get_booking'}, {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'Create_Expense_01_invoice_preview_125', 'type': 'expense'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_209", 'Create a booking for 1 day with two guest and generate invoice for one guest from'
                                   ' it which have booked charge mapped to it',
             [{'id': "booking_23", 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'Create_Expense_01_invoice_preview_125', 'type': 'expense'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_210", 'Create a booking for 1 day with two guest and generate invoice for one guest from'
                                   ' it which have booked charge mapped to it',
             [{'id': "booking_23", 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'Create_Expense_01_invoice_preview_125', 'type': 'expense'}], {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_211", 'Provide cancellation policy as custom pricing without providing cancellation amount',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 400, None, "04015991",
             "Cancellation charge required for custom cancellation fee", "", "", False, "", [False, '']),
            ("invoicePreview_212", 'Provide cancellation policy as custom pricing while doing complete early checkout '
                                   'and cancellation amount less than booking amount',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "",
             "", "", "", False, "", [False, '']),
            ("invoicePreview_213", 'Provide cancellation policy as custom pricing while doing complete early checkout '
                                   'and cancellation amount equal to booking amount',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "",
             "", "", "", False, "", [False, '']),
            ("invoicePreview_214", 'Provide cancellation policy as custom pricing while doing complete early checkout '
                                   'and cancellation amount more than booking amount',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 400, None, "04015992",
             "Cancellation charge greater than booking amount", "", "", False, "", [False, '']),
            ("invoicePreview_215", 'Provide cancellation policy as custom pricing while doing complete early checkout '
                                   'and cancellation amount less than booking amount where charge passed to cancel',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], {"include_kerala_cess": False}, 200, None, "", "", "", "",
             False, "", [False, '']),
            ("invoicePreview_216",
             'Create a booking for 1 day with two room and checkout one room from it which do not have charges mapped '
             'to it and post the charge with cancellation policy as retain booking amount',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_217",
             'Create a booking for 1 day with two room and checkout one room from it which do not have charges mapped '
             'to it and cancel the charge with cancellation policy as retain booking amount',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_218",
             'Create a booking for 1 day with two room and checkout both room while posting charge of one room and '
             'cancelling charge of other cancel the charge with cancellation policy as retain booking amount',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_219", 'Create a booking for 1 day with two room and checkout one room from it which do not'
                                   ' have charges mapped to it and post the charge with cancellation policy as '
                                   'custom pricing and cancellation charge more than booking amount',
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'}],
             {"include_kerala_cess": False}, 400, None, "04015992", "Cancellation charge greater than booking amount",
             "", "", False, "", [False, '']),
            ("invoicePreview_220",
             'Create a booking for 3 day and checkout one room from it which do not have charges mapped to it and post '
             'the allowance with cancellation policy as custom pricing', BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE,
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
            ("invoicePreview_221",
             'Create a booking for 3 day and checkout one room from it which do not have charges mapped to it and '
             'cancel the allowance with cancellation policy as custom pricing', BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE,
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, "", [False, '']),
        ])
    @pytest.mark.regression
    def test_preview_invoice(self, client_, test_case_id, tc_description, previous_actions, extra_data, status_code,
                             user_type, error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                             extras):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]
        perform_night_audit, action_after_night_audit = extras[0], extras[1]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                if test_case_id != 'invoicePreview_66':
                    self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        if len(extras) == 3:
            query_execute(db_queries.UPDATE_CHECKOUT_TIME_WITH_INTERVAL.format(interval_minutes=extras[2]))

        response = self.booking_request.preview_invoice_request(client_, self.booking_request.booking_id, test_case_id,
                                                                status_code, user_type,
                                                                include_kerala_cess=extra_data['include_kerala_cess'],
                                                                hotel_id=hotel_id)

        if len(extras) == 3:
            query_execute(db_queries.UPDATE_CHECKOUT_TIME.format(checkout_time='11:00:00'))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.billing_request,
                            self.booking_request.booking_id, self.booking_request.invoice_group_id,
                            self.booking_request.bill_id, hotel_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, billing_request, booking_id, invoice_group_id,
                   bill_id, hotel_id, expense_request):
        validation = ValidationPreviewInvoice(client_, test_case_id, response, booking_id, invoice_group_id, bill_id,
                                              hotel_id)
        validation.validate_response(booking_request, billing_request)
        validation.validate_charge_and_expense_status(expense_request, billing_request)
