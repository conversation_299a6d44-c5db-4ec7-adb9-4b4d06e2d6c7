from datetime import date, timedelta

import pytest

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_checkout_v2 import (
    ValidationCheckoutV2,
)
from prometheus.integration_tests.tests.booking.validations.validation_crs_report import ValidationCrsReport
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.integration_tests.utilities.csv_utils import read_invoice_details_from_csv, delete_invoice_csv
from prometheus.tests.mockers import mock_treebo_tenant_boolean


class TestCheckoutV2(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, "
        "dev_message, error_payload, skip_message, extras, skip_case",
        [
            ("CheckoutV2_01", "Checkout 1 guest from 1 room walk in booking having rate plan",
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_02", "early Checkout 1 guest from 1 room walkin booking having rate plan",
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_03", "Checkout 1 guest from 2 room single guest walkin booking having rate plan",
             [{'id': "booking_04", 'type': 'booking_v2'},
              {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_02", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_04", "Checkout 1 guest from 2 room single guest walkin booking having rate plan from 1st "
                              "room only without payment", [{'id': "booking_04", 'type': 'booking_v2'},
                                                            {'id': "checkin_02", 'type': 'checkin_v2'},
                                                            {'id': "PostCharge_01", 'type': 'update_expense',
                                                             'charge_id': 1},
                                                            {'id': "invoicePreviewCheckoutV2_01",
                                                             'type': 'preview_invoice'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_05", "early Checkout 1 guest from 2 room single guest walkin booking having rate plan from 1st"
                              "room only without paymnet", [{'id': "booking_04", 'type': 'booking_v2'},
                                                            {'id': "checkin_02", 'type': 'checkin_v2'},
                                                            {'id': "PostCharge_01", 'type': 'update_expense',
                                                             'charge_id': 1},
                                                            {'id': "invoicePreviewCheckoutV2_01",
                                                             'type': 'preview_invoice'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_06", "early Checkout 1 guest from 2 room single guest walkin booking having rate plan",
             [{'id': "booking_04", 'type': 'booking_v2'},
              {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_02", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_07", "checkout 2 guests from a booking of 1 room with 2 guest walkin booking having rate plan",
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_04", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_08", "checkout 1 guests from a booking of 1 room with 2 guest walkin booking having rate plan"
                              " without payment",
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_09", "early checkout 1 guests from a booking of 1 room with 2 guest walkin booking having rate"
                              " plan without payment",
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_10", "early checkout 2 guests from a booking of 1 room with 2 guest walkin booking having rate"
                              "plan",
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_04", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_11", "checkout 1 guest from booking of 2 days having one room with one guest",
             [{'id': "booking_05", 'type': 'booking_v2'},
              {'id': "checkin_05", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'type': 'get_booking_v2'},
              {'id': "invoicePreviewCheckoutV2_05", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'},
              {'type': 'get_booking_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 4}, ""),
            ("CheckoutV2_12", "early checkout by one day for 1 guest from booking of 2 days having one room with one"
                              " guest",
             [{'id': "booking_05", 'type': 'booking_v2'}, {'id': "checkin_05", 'type': 'checkin_v2'}],
             200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                           {'id': "invoicePreviewCheckoutV2_06", 'type': 'preview_invoice'},
                                           {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'}]}, ""),
            ("CheckoutV2_13", "early checkout by two day for 1 guest from booking of 2 days having one room with one"
                              " guest",
             [{'id': "booking_05", 'type': 'booking_v2'},
              {'id': "checkin_05", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_14", "checkout one room which was checked in out of two rooms in a walkin booking",
             [{'id': "booking_135_checkin_06", 'type': 'booking_v2'},
              {'id': "checkin_06", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_15", "checkout 2 guests, one child and one adult of a walkin booking",
             [{'id': "booking_19_01_checkin_07", 'type': 'booking_v2'},
              {'id': "checkin_07", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_16_checkin_49", "checkout one guest from each room after Checkin 1 room out of 2 rooms where"
                                         " non checked rooms checkin is of future date",
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'},
              {'id': "checkin_11", 'type': 'checkin_v2'},
              {'id': "checkin_49", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_09", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_16", "checkout room after Checkin 1 day 1 room single guest booking having rate plan billed "
                              "entity is primary_guest",
             [{'id': "booking_128_checkin_15", 'type': 'booking_v2'},
              {'id': "checkin_15", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_17", "Early checkout room after Checkin 1 day 1 room single guest booking having rate plan "
                              "billed entity is primary_guest",
             [{'id': "booking_128_checkin_15", 'type': 'booking_v2'},
              {'id': "checkin_15", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_18", "checkout one guest from each room after Checkin 1 day 2 room 1 guest booking having rate"
                              " plan Direct bill entity is primary_guest",
             [{'id': "booking_129_checkin_16", 'type': 'booking_v2'},
              {'id': "checkin_16", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'id': "AddPaymentCheckoutV2_06", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_19", "checkout one guest from 1st room after Checkin 1 day 2 room 1 guest booking having rate"
                              " plan Direct bill entity is primary_guest",
             [{'id': "booking_129_checkin_16", 'type': 'booking_v2'},
              {'id': "checkin_16", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_20",
             "Early checkout one guest from 1st room after Checkin 1 day 2 room 1 guest booking having rate"
             " plan Direct bill entity is primary_guest",
             [{'id': "booking_129_checkin_16", 'type': 'booking_v2'},
              {'id': "checkin_16", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_21", "checkout one guest from each room after Checkin 1 day 2 room 1 guest booking having rate"
                              " plan Direct bill entity is primary_guest",
             [{'id': "booking_129_checkin_16", 'type': 'booking_v2'},
              {'id': "checkin_16", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_07", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'id': "AddPaymentCheckoutV2_06", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_22", "checkout one guest from each room after Checkin 1 day 2 diffrent room type 1 guest"
                              " booking having rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_130_checkin_17", 'type': 'booking_v2'},
              {'id': "checkin_17", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'id': "AddPaymentCheckoutV2_06", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_23", "checkout one guest from 1st room after Checkin 1 day 2 diffrent room type 1 guest"
                              " booking having rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_130_checkin_17", 'type': 'booking_v2'},
              {'id': "checkin_17", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_24",
             "Early checkout one guest from 1st room after Checkin 1 day 2 diffrent room type 1 guest booking having"
             " rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_130_checkin_17", 'type': 'booking_v2'},
              {'id': "checkin_17", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_25", "Early checkout one guest from each room after Checkin 1 day 2 diffrent room type 1 guest"
                              " booking having rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_130_checkin_17", 'type': 'booking_v2'},
              {'id': "checkin_17", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_07", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'id': "AddPaymentCheckoutV2_06", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_26", "checkout both guest after Checkin 1 day 1 room booking having 2 adult guest and having"
                              " rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_131_checkin_18", 'type': 'booking_v2'},
              {'id': "checkin_18", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_04", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_27", "checkout 1 primary_guest where charges are mapped after Checkin 1 day 1 room booking"
                              " having 2 adult guest and having rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_131_checkin_18", 'type': 'booking_v2'},
              {'id': "checkin_18", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_28",
             "Early checkout 1 primary_guest where charges are mapped after Checkin 1 day 1 room booking"
             " having 2 adult guest and having rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_131_checkin_18", 'type': 'booking_v2'},
              {'id': "checkin_18", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_29",
             "Early checkout both guest after Checkin 1 day 1 room booking having 2 adult guest and having"
             " rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_131_checkin_18", 'type': 'booking_v2'},
              {'id': "checkin_18", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_04", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_30",
             "checkout 1 primary_guest where charges are mapped without payment after Checkin 1 day 1 room booking"
             " having 2 adult guest and having rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_131_checkin_18", 'type': 'booking_v2'},
              {'id': "checkin_18", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_31",
             "checkout 1 non_primary_guest where charges are mapped without payment after Checkin 1 day 1 room booking"
             " having 2 adult guest and having rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_131_checkin_18", 'type': 'booking_v2'},
              {'id': "checkin_18", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_08", 'type': 'preview_invoice'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_32", "checkout 1 guest after Checkin 2 day 1 room booking having 1 guest with rate plan Direct"
                              " and billed entity is primary_guest",
             [{'id': "booking_132_checkin_19", 'type': 'booking_v2'},
              {'id': "checkin_19", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_05", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'},
              {'id': "AddPaymentCheckoutV2_02", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 2}, ""),
            ("CheckoutV2_33", "early checkout by one day for 1 guest Checkin 2 day 1 room booking having 1 guest with"
                              " rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_132_checkin_19", 'type': 'booking_v2'}, {'id': "checkin_19", 'type': 'checkin_v2'}],
             200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                           {'id': "invoicePreviewCheckoutV2_06", 'type': 'preview_invoice'},
                                           {'id': "AddPaymentCheckoutV2_02", 'type': 'add_payment_v2'}]}, ""),
            ("CheckoutV2_34", "early checkout by two day for 1 guest Checkin 2 day 1 room booking having 1 guest with"
                              " rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_132_checkin_19", 'type': 'booking_v2'},
              {'id': "checkin_19", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_35", "checkout one room after Checkin 1 room out of 2 room having 1 guest in each room"
                              " Direct and billed entity is primary_guest",
             [{'id': "booking_133_checkin_20", 'type': 'booking_v2'},
              {'id': "checkin_20", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_36", "checkout 1 adult and 1 child after Checkin 1 day 1 room booking having 1 adult and 1"
                              " child walkin rate plan booking Direct and billed entity is primary_guest",
             [{'id': "booking_134_checkin_21", 'type': 'booking_v2'},
              {'id': "checkin_21", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_04", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_05", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_37", "Checkout 1 guest after Checkin 1 day 1 room 1 guest booking having billed entity as"
                              " company and charge type is non credit",
             [{'id': "booking_29", 'type': 'booking_v2'},
              {'id': "checkin_22", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_38", "early Checkout 1 guest after Checkin 1 day 1 room 1 guest booking having billed entity"
                              " as company and charge type is non credit",
             [{'id': "booking_29", 'type': 'booking_v2'},
              {'id': "checkin_22", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_39", "Checkout 1 guest after Checkin 1 day 1 room 1 guest booking having billed entity as"
                              " company and charge type is credit",
             [{'id': "booking_27", 'type': 'booking_v2'},
              {'id': "checkin_23", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_40", "early Checkout 1 guest after Checkin 1 day 1 room 1 guest booking having billed entity"
                              " as company and charge type is credit",
             [{'id': "booking_27", 'type': 'booking_v2'},
              {'id': "checkin_23", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_41", "checkout one guest from each room after Checkin 1 day 2 room and each room contains 1"
                              " guest and billed entity as company and charge type is non credit",
             [{'id': "booking_30", 'type': 'booking_v2'},
              {'id': "checkin_24", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_42", "checkout one guest from 1st room afterCheckin 1 day 2 room and each room contains 1 "
                              "guest and billed entity as company and charge type is non credit",
             [{'id': "booking_30", 'type': 'booking_v2'},
              {'id': "checkin_24", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_43",
             "Early checkout one guest from 1st room after Checkin 1 day 2 room and each room contains 1 guest and"
             " billed entity as company and charge type is non credit",
             [{'id': "booking_30", 'type': 'booking_v2'},
              {'id': "checkin_24", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_44", "Early checkout one guest from each room after Checkin 1 day 2 room and each room"
                              " contains 1 guest and billed entity as company and charge type is non credit",
             [{'id': "booking_30", 'type': 'booking_v2'},
              {'id': "checkin_24", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_07", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_45", "checkout one guest from each room after Checkin 1 day 2 room and each room contains 1"
                              " guest and billed entity as company and charge type is credit",
             [{'id': "booking_30_checkin_25", 'type': 'booking_v2'},
              {'id': "checkin_25", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_46", "checkout one guest from 1st room afterCheckin 1 day 2 room and each room contains 1 "
                              "guest and billed entity as company and charge type is credit",
             [{'id': "booking_30_checkin_25", 'type': 'booking_v2'},
              {'id': "checkin_25", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_47",
             "Early checkout one guest from 1st room after Checkin 1 day 2 room and each room contains 1 guest and"
             " billed entity as company and charge type is credit",
             [{'id': "booking_30_checkin_25", 'type': 'booking_v2'},
              {'id': "checkin_25", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_48", "Early checkout one guest from each room after Checkin 1 day 2 room and each room"
                              " contains 1 guest and billed entity as company and charge type is credit",
             [{'id': "booking_30_checkin_25", 'type': 'booking_v2'},
              {'id': "checkin_25", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_07", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_49", "checkout 1 guest after Checkin 2 day 1 room booking having 1 guest with rate plan Direct"
                              " and billed entity is primary_guest",
             [{'id': "booking_32", 'type': 'booking_v2'},
              {'id': "checkin_27", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_05", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 2}, ""),
            ("CheckoutV2_50", "early checkout by one day for 1 guest Checkin 2 day 1 room booking having 1 guest with"
                              " rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_32", 'type': 'booking_v2'}, {'id': "checkin_27", 'type': 'checkin_v2'}],
             200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                           {'id': "invoicePreviewCheckoutV2_06", 'type': 'preview_invoice'}]}, ""),
            ("CheckoutV2_51", "early checkout by two day for 1 guest Checkin 2 day 1 room booking having 1 guest with"
                              " rate plan Direct and billed entity is primary_guest",
             [{'id': "booking_32", 'type': 'booking_v2'},
              {'id': "checkin_27", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_52", "Checkout 2 guests after Checkin one after another guest in a 2 day 1 room  multiple day"
                              " booking, billed entity as company and charge type is non credit",
             [{'id': "checkin_28_booking", 'type': 'booking_v2'},
              {'id': "checkin_28", 'type': 'checkin_v2'},
              {'id': "checkin_32", 'type': 'checkin_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                           {'id': "invoicePreviewCheckoutV2_09", 'type': 'preview_invoice'},
                                           {'id': "AddPaymentCheckoutV2_08", 'type': 'add_payment_v2'}]}, ""),
            ("CheckoutV2_53",
             "Checkout 2 guests after Checkin one after another guest in a 2 day 2 room  multiple day booking,"
             " billed entity as company and charge type is non credit",
             [{'id': "checkin_33_booking", 'type': 'booking_v2'},
              {'id': "checkin_33_01", 'type': 'checkin_v2'},
              {'id': "checkin_33", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 3},
              {'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 2}, ""),
            ("CheckoutV2_54", "checkout room after Checkin 1 day 1 room 1 guest booking having billed entity as"
                              " travel agent and charge type is credit",
             [{'id': "booking_156", 'type': 'booking_v2'},
              {'id': "checkin_36", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_55",
             "Early checkout room after Checkin 1 day 1 room 1 guest booking having billed entity as travel agent"
             " and charge type is credit",
             [{'id': "booking_156", 'type': 'booking_v2'},
              {'id': "checkin_36", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_56",
             "checkout one guest from each room after Checkin 1 day 2 room and each room contains 1 guest and "
             "billed entity as travel agent and charge type is non credit",
             [{'id': "booking_159", 'type': 'booking_v2'},
              {'id': "checkin_37", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_57",
             "checkout one guest from 1st room after Checkin 1 day 2 room and each room contains 1 guest"
             " and billed entity as travel agent and charge type is non credit",
             [{'id': "booking_159", 'type': 'booking_v2'},
              {'id': "checkin_37", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_58",
             "Early checkout one guest from 1st room after Checkin 1 day 2 room and each room contains 1"
             " guest and billed entity as travel agent and charge type is non credit",
             [{'id': "booking_159", 'type': 'booking_v2'},
              {'id': "checkin_37", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_59",
             "Early checkout one guest from each room after Checkin 1 day 2 room and each room contains"
             " 1 guest and billed entity as travel agent and charge type is non credit",
             [{'id': "booking_159", 'type': 'booking_v2'},
              {'id': "checkin_37", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_07", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_04", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_60",
             "checkout one guest from each room after Checkin 1 day 2 room and each room contains 1 guest and "
             "billed entity as travel agent and charge type is credit",
             [{'id': "booking_157", 'type': 'booking_v2'},
              {'id': "checkin_38", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_61",
             "checkout one guest from 1st room after Checkin 1 day 2 room and each room contains 1 guest"
             " and billed entity as travel agent and charge type is credit",
             [{'id': "booking_157", 'type': 'booking_v2'},
              {'id': "checkin_38", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_62",
             "Early checkout one guest from 1st room after Checkin 1 day 2 room and each room contains 1"
             " guest and billed entity as travel agent and charge type is credit",
             [{'id': "booking_157", 'type': 'booking_v2'},
              {'id': "checkin_38", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_63",
             "Early checkout one guest from each room after Checkin 1 day 2 room and each room contains"
             " 1 guest and billed entity as travel agent and charge type is credit",
             [{'id': "booking_157", 'type': 'booking_v2'},
              {'id': "checkin_38", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_07", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_66",
             "checkout room after Checkin 1 day 1 room single guest Walkin Booking without rateplan",
             [{'id': "booking_78", 'type': 'booking_v2'},
              {'id': "checkin_51", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_09", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1, "non_rate_plan_booking": True}, ""),
            ("CheckoutV2_67",
             "checkout room after Checkin 1 day 1 room single guest Walkin Booking without rateplan",
             [{'id': "booking_78", 'type': 'booking_v2'},
              {'id': "checkin_51", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_09", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0, "non_rate_plan_booking": True}, ""),
            ("CheckoutV2_68", "checkout one guest from each room after Checkin 1 day 2 room 1 guest booking having"
                              "  walkin without rateplan",
             [{'id': "booking_80", 'type': 'booking_v2'},
              {'id': "checkin_52", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_03", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_10", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1, "non_rate_plan_booking": True}, ""),
            ("CheckoutV2_69", "checkout one guest from 1st room after Checkin 1 day 2 room 1 guest booking having"
                              "  walkin without rateplan",
             [{'id': "booking_80", 'type': 'booking_v2'},
              {'id': "checkin_52", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_10", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1, "non_rate_plan_booking": True}, ""),
            ("CheckoutV2_70",
             "Early checkout one guest from 1st room after Checkin 1 day 2 room 1 guest booking having  walkin without"
             " rateplan",
             [{'id': "booking_80", 'type': 'booking_v2'},
              {'id': "checkin_52", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_02", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_10", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0, "non_rate_plan_booking": True}, ""),
            ("CheckoutV2_71", "Early checkout one guest from each room after Checkin 1 day 2 room 1 guest booking"
                              " having  walkin without rateplan",
             [{'id': "booking_80", 'type': 'booking_v2'},
              {'id': "checkin_52", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
              {'id': "invoicePreviewCheckoutV2_07", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_10", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0, "non_rate_plan_booking": True}, ""),
            ("CheckoutV2_72", "checkout two guest one child and one adult  after Checkin 1 day 1 room having 1 adult"
                              " and 1 child Walkin Booking without rateplan",
             [{'id': "checkin_53_booking", 'type': 'booking_v2'},
              {'id': "checkin_53", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_09", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_11", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1, "non_rate_plan_booking": True}, ""),
            ("CheckoutV2_73", "checkout one room after Checkin 1 room out of 2 room from a multiple Day Walking"
                              " Booking without rate plan",
             [{'id': "booking_80", 'type': 'booking_v2'},
              {'id': "checkin_54", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_10", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1, "non_rate_plan_booking": True}, True),
            ("CheckoutV2_74", "Checkout without payment 1 guest from 1 room walkin booking having rate plan",
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_75", "Checkout without payment 1 guest from 1 room walkin booking having rate plan",
             [{'id': "booking_128_checkin_15", 'type': 'booking_v2'},
              {'id': "checkin_15", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_76", "Checkout 1 guest after Checkin 1 day 1 room 1 guest booking having billed entity as"
                              " company and charge type is non credit",
             [{'id': "booking_29", 'type': 'booking_v2'},
              {'id': "checkin_22", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_77",
             "checkout without payment after Checkin 1 day 1 room single guest Walkin "
             "Booking without rateplan",
             [{'id': "booking_78", 'type': 'booking_v2'},
              {'id': "checkin_51", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'type': 'get_bill_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1, "non_rate_plan_booking": True}, ""),
            ("CheckoutV2_78",
             "Checkout with payment on wrong billed_entity after checkin 1 guest from 1 room walkin booking having rate plan",
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_79",
             "Checkout with payment on wrong billed_entity after checkin 1 guest from 1 room walkin booking having rate plan",
             [{'id': "booking_128_checkin_15", 'type': 'booking_v2'},
              {'id': "checkin_15", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_80",
             "Checkout with payment on wrong billed_entity after 1 guest after Checkin 1 day 1 room 1 guest booking having billed entity as"
             " company and charge type is non credit",
             [{'id': "booking_29", 'type': 'booking_v2'},
              {'id': "checkin_22", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_81",
             "checkout with payment on wrong billed_entity after Checkin 1 day 1 room single guest Walkin "
             "Booking without rateplan",
             [{'id': "booking_78", 'type': 'booking_v2'},
              {'id': "checkin_51", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1, "non_rate_plan_booking": True}, ""),
            ("CheckoutV2_82",
             "Checkout with partial payment after checkin 1 guest from 1 room walkin booking having rate plan",
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_12", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_83",
             "Checkout with partial payment after checkin 1 guest from 1 room walkin booking having rate plan",
             [{'id': "booking_128_checkin_15", 'type': 'booking_v2'},
              {'id': "checkin_15", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_09", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_84",
             "Checkout with partial payment after 1 guest after Checkin 1 day 1 room 1 guest booking having billed entity as"
             " company and charge type is non credit",
             [{'id': "booking_29", 'type': 'booking_v2'},
              {'id': "checkin_22", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_12", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_85",
             "checkout with partial payment after Checkin 1 day 1 room single guest Walkin "
             "Booking without rateplan",
             [{'id': "booking_78", 'type': 'booking_v2'},
              {'id': "checkin_51", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_13", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1, "non_rate_plan_booking": True}, ""),
            ("CheckoutV2_86", "Checkout with wrong resource_version from 1 room walkin booking having rate plan",
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'}],
             409, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_87", "checkout with wrong resource_version after Checkin 1 day 1 room single guest booking"
                              " having rate plan billed entity is primary_guest",
             [{'id': "booking_128_checkin_15", 'type': 'booking_v2'},
              {'id': "checkin_15", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             409, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_88", "Checkout with wrong resource_version after Checkin 1 day 1 room 1 guest booking having"
                              " billed entity as company and charge type is non credit",
             [{'id': "booking_128_checkin_15", 'type': 'booking_v2'},
              {'id': "checkin_15", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'},
              {'type': 'get_bill_v2'}],
             409, '', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_89", "Checkout with wrong resource_version from 1 room walkin booking having rate plan",
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, True),
            ("CheckoutV2_90", "Checkout with wrong resource_version from 1 room walkin booking having rate plan",
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'}],
             400, '', "", "", "", "", {'run_night_audit_before': 1}, True),
            ("CheckoutV2_91",
             "Checkout 1 guest from 1 room walkin booking having rate plan, with all charges posted of first day",
             EARLY_CHECKOUT_1ST_DAY_3_DAY_BOOKING, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_92", "Checkout 1 guest from 1 room walkin booking having rate plan, with all charges posted of"
                              " first day with excess payment",
             EARLY_CHECKOUT_1ST_DAY_3_DAY_BOOKING_EXCESS_PAYMENT, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_93", "Checkout 1 guest from 1 room walkin booking having rate plan, with all charges to "
                              "cancel on 1st day, where only booked charges present",
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'invoicePreview_69', 'type': 'preview_invoice'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_94", "Checkout 1 guest from 1 room walkin booking having rate plan, with all allowances to"
                              " cancel on 1st day, where only booked allowances are present",
             BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_70', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_97', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_95", "Checkout a booking for 3 days and provide all pending charges to cancel and all pending"
                              " allowance to post on 1st day, where booked charges and allowances present",
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_71', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_95', 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_96", "Checkout a booking for 3 days and provide all pending allowances to cancel all pending"
                              " charges to post on 1st day, where booked charges and allowances are present",
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_72', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_96', 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_97", "Checkout a booking for 3 days and provide all pending charges and allowances to cancel"
                              " on 1st day",
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_73', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_97', 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_98", "Checkout a booking for 3 days and provide all pending allowances to post on 1st day, "
                              "where only booked allowances are present",
             BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_75', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_95', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_99",
             "Checkout a booking for 3 days and provide all pending charges and allowances to post on 1st day",
             BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE +
             [{'id': 'invoicePreview_76', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_99', 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_100",
             "Checkout a booking for 3 days and provide charges to post when generating invoice on 2nd day",
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': 'invoicePreview_80', 'type': 'preview_invoice'},
                                           {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                           {'id': 'AddPaymentV2_checkout_100', 'type': 'add_payment_v2'}]}, ""),
            ("CheckoutV2_101",
             "Checkout a booking for 3 days and provide allowances to post when generating invoice on 2nd day",
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                           {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [2, 1]},
                                           {'id': 'invoicePreview_81', 'type': 'preview_invoice'},
                                           {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                           {'id': 'AddPaymentV2_Redistribute_22', 'type': 'add_payment_v2'}]}, ""),
            ("CheckoutV2_102", "Checkout a booking for 3 days and and provide charges and allowances to post when "
                               "generating invoice on 2nd day",
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': 'Create_Expense_01', 'type': 'expense'},
                                           {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                           {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [2, 1]},
                                           {'id': 'invoicePreview_82', 'type': 'preview_invoice'},
                                           {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                           {'id': 'AddPaymentV2_checkout_102', 'type': 'add_payment_v2'}]}, ""),
            ("CheckoutV2_103",
             "Checkout a booking have Cancel all the charges and then create expense and post the charge",
             EARLY_CHECKOUT_BOOKING_HAVING_EXPENSE_CHARGE_ONLY, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_104",
             "Checkout a booking have Cancel all the charges and then create expense and post the charge",
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_checkout_104', 'type': 'expense'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': 'invoicePreview_checkout_104', 'type': 'preview_invoice'},
                                           {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                           {'id': 'AddPaymentV2_checkout_102', 'type': 'add_payment_v2'}]}, ""),
            ("CheckoutV2_105",
             "Checkout a booking have Cancel all the charges and then create expense and cancel the charge",
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_checkout_104', 'type': 'expense'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': 'invoicePreview_checkout_105', 'type': 'preview_invoice'},
                                           {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                           {'id': 'AddPaymentV2_Redistribute_22', 'type': 'add_payment_v2'}]},
             ""),
            ("CheckoutV2_106",
             "Checkout a booking for which has no booked charge and booked allowance, and post the allowance",
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], 200,
             'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
                                           {'id': 'invoicePreview_122', 'type': 'preview_invoice'},
                                           {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                           {'id': 'AddPaymentV2_checkout_106', 'type': 'add_payment_v2'}]}, ""),
            ("CheckoutV2_107",
             "Checkout a booking for which has no booked charge and booked allowance, and cancel the allowance",
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], 200,
             'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
                                           {'id': 'invoicePreview_checkout_107', 'type': 'preview_invoice'},
                                           {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                           {'id': 'AddPaymentV2_Redistribute_22', 'type': 'add_payment_v2'}]},
             ""),
            ("CheckoutV2_108", "Checkout a booking which has Cancel charge, posted charge, booked charge",
             BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE +
             [{'id': 'invoicePreview_129', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_96', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_109", "Checkout a booking which has Cancel allowance, posted allowance, booked allowance",
             BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_130', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_109', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_110", "Checkout a booking which has has Cancel extra charge, booked charge",
             EARLY_CHECKOUT_BOOKING_HAVING_CANCELLED_EXPENSE, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_111", "Checkout a booking which has Cancel charge, booked charge, booked allowance",
             BOOKING_WITH_CANCEL_BOOKED_CHARGE_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_132', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_99', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_112", "Checkout a booking which has Cancel allowance, booked allowance",
             BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_133', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_112', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_113", "Checkout a booking which has Cancel allowance, booked charge, booked allowance",
             BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE_BOOKED_CHARGE +
             [{'id': 'invoicePreview_134', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_113', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_114", "Checkout a booking which has Cancel charge, posted charge, booked charge, "
                               "booked allowance",
             BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE_BOOKED_ALLOWANCE +
             [{'id': 'invoicePreview_135', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_113', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_115", "Checkout a booking which has Cancel allowance, posted allowance, booked allowance,"
                               " booked charge",
             BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE_BOOKED_CHARGE +
             [{'id': 'invoicePreview_136', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_115', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_116", "Checkout a booking for 2 day with two room and checkout both room on 1st day, and"
                               " post charge of both room",
             EARLY_CHECKOUT_BOOKING_WITH_TWO_ROOM, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_117", "Checkout a booking for 2 day with two room and checkout both room on 1st day, and"
                               " cancel charge of one room",
             EARLY_CHECKOUT_BOOKING_WITH_TWO_ROOM_CANCEL_ONE_ROOM_CHARGE, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_118", "Checkout a booking for 2 day with two room and checkout both room on 1st day, and"
                               " cancel charge of both room",
             EARLY_CHECKOUT_BOOKING_WITH_TWO_ROOM_CANCEL_BOTH_ROOM_CHARGE, 200, 'super-admin', "", "",
             "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_119", "Checkout a booking having multiple allowance on same charge and post both allowance",
             EARLY_CHECKOUT_BOOKING_WITH_MULTIPLE_ALLOWANCE, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_120", "Checkout a booking having multiple allowance on same charge and post 2nd allowance",
             EARLY_CHECKOUT_BOOKING_WITH_CANCEL_AND_BOOKED_ALLOWANCE, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_121", "Checkout a booking having account with only payment in it",
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'AddPaymentV2_Redistribute_22', 'type': 'add_payment_v2'},
              {'id': 'invoicePreview_145', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_122", "checkout one guests from a booking of 1 room with 2 guest walkin booking having "
                               "rate plan charges mapped to booker",
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "invoicePreview_66", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_03", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_123", "checkout one guests from a booking of 1 room with 2 guest walkin booking having "
                               "rate plan charges mapped to checking out guest",
             [{'id': "booking_19_one_primary_guest_07", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "invoicePreview_66", 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_Checkout_122', 'type': 'redistribute_payment'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, False),
            ("CheckoutV2_124", "checkout one guests from a booking of 2 room with 2 guest walk in booking on second day"
                               " of booking",
             [{'id': "booking_two_room_two_guest_three_day_booking", 'type': 'booking_v2'},
              {'id': "checkin_two_room_two_guest_three_day_booking", 'type': 'checkin_v2'},
              {'id': "Create_Expense_checkout_01", 'type': 'expense'},
              {'id': "Create_Expense_checkout_02", 'type': 'expense'},
              {'id': "Create_Expense_checkout_03", 'type': 'expense'},
              {'id': "Create_Expense_checkout_04", 'type': 'expense'},
              {'id': "Create_Expense_checkout_05", 'type': 'expense'},
              {'id': "Create_Expense_checkout_06", 'type': 'expense'},
              {'id': "Create_Expense_checkout_07", 'type': 'expense'},
              {'id': "Create_Expense_checkout_08", 'type': 'expense'},
              {'id': "Create_Expense_checkout_09", 'type': 'expense'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': 'invoicePreview_checkout_109', 'type': 'preview_invoice'},
                                           {'id': 'AddPaymentV2_checkout_124', 'type': 'add_payment_v2'},
                                           {'id': 'Redistribute_Payments_Checkout_122',
                                            'type': 'redistribute_payment'}]}, False),
            ("CheckoutV2_125", "checkout guests one by one from a booking of 1 room with 2 guest walkin booking having "
                               "rate plan charges mapped to checking out guest",
             [{'id': "booking_19_one_primary_guest_07", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "invoicePreview_66", 'type': 'preview_invoice'},
              {'id': "CheckoutV2_122", 'type': 'checkout_v2'},
              {'id': "invoicePreview_checkout_110", 'type': 'preview_invoice'},
              {'id': "AddPaymentV2_checkout_125", 'type': 'add_payment_v2'},
              {'id': 'Redistribute_Payments_Checkout_122', 'type': 'redistribute_payment'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, False),
            ("CheckoutV2_126", "checkout one guests from a booking of 1 room with 2 guest walkin booking having "
                               "rate plan and extra charges consumed and billed to checking out guest",
             [{'id': "booking_19_one_primary_guest_07", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "Create_Expense_checkout_10", 'type': 'expense'},
              {'id': "Create_Expense_checkout_11", 'type': 'expense'},
              {'id': "invoicePreview_checkout_126", 'type': 'preview_invoice'},
              {'id': "AddPaymentV2_checkout_126", 'type': 'add_payment_v2'},
              {'id': 'Redistribute_Payments_Checkout_122', 'type': 'redistribute_payment'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, False),
            ("CheckoutV2_127", "checkout one guests from a booking of 1 room with 2 guest walkin booking having "
                               "rate plan and extra charges billed to checking out guest",
             [{'id': "booking_19_one_primary_guest_07", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "Create_Expense_checkout_14", 'type': 'expense'},
              {'id': "Create_Expense_checkout_15", 'type': 'expense'},
              {'id': "invoicePreview_checkout_126", 'type': 'preview_invoice'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, False),
            ("CheckoutV2_128", "checkout one guests from a booking of 1 room with 2 guest walkin booking having "
                               "rate plan and extra charges consumed by checking out guest",
             [{'id': "booking_19_one_primary_guest_07", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "Create_Expense_checkout_12", 'type': 'expense'},
              {'id': "Create_Expense_checkout_13", 'type': 'expense'},
              {'id': "invoicePreview_checkout_126", 'type': 'preview_invoice'},
              {'id': "AddPaymentV2_checkout_126", 'type': 'add_payment_v2'},
              {'id': 'Redistribute_Payments_Checkout_122', 'type': 'redistribute_payment'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, False),
            ("CheckoutV2_129", "Checkout a booking with 2 room and charge mapped to booker and checkout first room "
                               "completely.",
             [{'id': "booking_two_room_two_guest_three_day_booking", 'type': 'booking_v2'},
              {'id': "checkin_two_room_two_guest_three_day_booking", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_checkout_129', 'type': 'preview_invoice'}], 200, 'super-admin', "", "", "",
             "", {'run_night_audit_before': 0}, False),
            ("CheckoutV2_130", "Checkout a booking with 2 room, 2 guest and charge mapped to booker and checkout"
                               " first room completely by one by one guest where charges mapped to booker.",
             [{'id': "booking_two_room_two_guest_three_day_booking", 'type': 'booking_v2'},
              {'id': "checkin_two_room_two_guest_three_day_booking", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_checkout_133_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_124', 'type': 'checkout_v2'},
              {'id': 'invoicePreview_checkout_133_02', 'type': 'preview_invoice'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, False),
            ("CheckoutV2_131", "Checkout a booking with 2 room, 2 guest and charge mapped to booker and checkout"
                               " first room completely by one by one guest where charges mapped to primary guest.",
             [{'id': "booking_two_room_two_guest_three_day_booking_pg", 'type': 'booking_v2'},
              {'id': "checkin_two_room_two_guest_three_day_booking", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_checkout_130_02', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_124', 'type': 'checkout_v2'},
              {'id': 'invoicePreview_checkout_130_01', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_Checkout_122', 'type': 'redistribute_payment'},
              {'id': "AddPaymentV2_checkout_131", 'type': 'add_payment_v2'}], 200, 'super-admin', "",
             "", "", "", {'run_night_audit_before': 0}, False),
            ("CheckoutV2_132", "Checkout two guest out of 3 present in room which have some charges as consuming guest",
             [{'id': "booking_three_guest_two_day_booking", 'type': 'booking_v2'},
              {'id': "checkin_three_guest_two_day_booking", 'type': 'checkin_v2'},
              {'id': "Create_Expense_checkout_16", 'type': 'expense'},
              {'id': "Create_Expense_checkout_17", 'type': 'expense'},
              {'id': "Create_Expense_checkout_18", 'type': 'expense'},
              {'id': "Create_Expense_checkout_19", 'type': 'expense'},
              {'id': "Create_Expense_checkout_20", 'type': 'expense'},
              {'id': "Create_Expense_checkout_21", 'type': 'expense'},
              {'id': "Create_Expense_checkout_22", 'type': 'expense'},
              {'id': "Create_Expense_checkout_23", 'type': 'expense'},
              {'id': "Create_Expense_checkout_24", 'type': 'expense'},
              {'id': "Create_Expense_checkout_25", 'type': 'expense'},
              {'id': "Create_Expense_checkout_26", 'type': 'expense'},
              {'id': "Create_Expense_checkout_27", 'type': 'expense'},
              {'id': 'invoicePreview_checkout_132', 'type': 'preview_invoice'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, False),
            ("CheckoutV2_133", "Checkout a booking with 2 room, 2 guest and charge mapped to booker and checkout"
                               " first room completely by one by one guest where charges mapped to primary guest, "
                               "and first guest checking out is primary",
             [{'id': "booking_two_room_two_guest_three_day_booking_pg", 'type': 'booking_v2'},
              {'id': "checkin_two_room_two_guest_three_day_booking", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_checkout_133_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_124', 'type': 'checkout_v2'},
              {'id': 'invoicePreview_checkout_133_02', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_Checkout_122', 'type': 'redistribute_payment'},
              {'id': "AddPaymentV2_checkout_131", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "",
             "", {'run_night_audit_before': 0}, False),
            ("CheckoutV2_134", "Checkout a booking with 2 room, 2 guest and charge mapped to booker and checkout"
                               " first room completely by one by one guest where charges mapped to primary guest, "
                               "doesn't record payment",
             [{'id': "booking_two_room_two_guest_three_day_booking_pg", 'type': 'booking_v2'},
              {'id': "checkin_two_room_two_guest_three_day_booking", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_checkout_130_02', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_124', 'type': 'checkout_v2'},
              {'id': 'invoicePreview_checkout_130_01', 'type': 'preview_invoice'}], 400, 'super-admin', "",
             "", "", "", {'run_night_audit_before': 0}, False),
            ("CheckoutV2_135", "Checkout a booking with 2 room, 2 guest and checkout first room completely by one by"
                               " one guest where room charges mapped to primary guest and expense charges mapped to"
                               " secondary guest, and first guest checking out is primary",
             [{'id': "booking_two_room_two_guest_three_day_booking_pg", 'type': 'booking_v2'},
              {'id': "checkin_two_room_two_guest_three_day_booking", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_checkout_28', 'type': 'expense'},
              {'id': 'invoicePreview_checkout_135_01', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_checkout_132', 'type': 'add_payment_v2'},
              {'id': 'CheckoutV2_124', 'type': 'checkout_v2'},
              {'id': 'invoicePreview_checkout_135_02', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_Checkout_122', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_131', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "",
             "", {'run_night_audit_before': 0}, False),
            ("CheckoutV2_136", "Checkout a booking after edit booking",
             CHECKOUT_AFTER_EDIT_BOOKING +
             [{'id': 'invoicePreview_01', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_Checkout_122', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_200', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_checkout_200_second', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0, 'is_put_booking_v2': True}, ""),
            ("CheckoutV2_137", "Checkout a booking after edit booking and cancel charge",
             CHECKOUT_AFTER_CANCEL_CHARGE_EDIT_BOOKING +
             [{'id': 'invoicePreview_01', 'type': 'preview_invoice'},
              {'id': 'Redistribute_Payments_Checkout_122', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_checkout_200', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_checkout_200_second', 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0, 'is_put_booking_v2': True}, ""),
            ("CheckoutV2_138", "Checkout after room stay charge split",
             EARLY_CHECKOUT_1ST_DAY_3_DAY_BOOKING_CHARGE_SPLIT, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_139", "Checkout after room stay charge split having slab based taxation",
             EARLY_CHECKOUT_CHARGE_SPLIT_SLAB_BASED, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0, 'has_slab_based_taxation': True}, ""),
            ("CheckoutV2_140", "Checkout after room stay charge split having slab based taxation and clubbed charge",
             EARLY_CHECKOUT_CHARGE_SPLIT_SLAB_BASED_CLUBBED_CHARGE, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0, 'has_slab_based_taxation': True}, ""),
            ("CheckoutV2_141",
             "Checkout 1 guest from 1 room walkin booking having rate plan, with all charges posted of first day",
             EARLY_CHECKOUT_1ST_DAY_3_DAY_OTA_BOOKING, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_142", "Checkout 1 guest from 1 room walkin booking having rate plan, with all charges to "
                               "cancel on 1st day, where only booked charges present",
             [{'id': "ota_booking_3_days", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_69', 'type': 'preview_invoice'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_143",
             "Checkout a booking for 3 days and provide charges to post when generating invoice on 2nd day",
             [{'id': "ota_booking_3_days", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], 200,
             'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': 'invoicePreview_80', 'type': 'preview_invoice'},
                                           {'id': 'AddPaymentV2_checkout_143', 'type': 'add_payment_v2'}]}, ""),
            ("CheckoutV2_144", "Checkout 1 guest from 1 room walk in booking having rate plan just before night audit, "
                               "post extra charge of checkout date",
             [{'id': "booking_01_no_show", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Multiple_Expense_29', 'type': 'create_expense_V3'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreview_121", 'type': 'preview_invoice'},
              {'id': "AddPaymentV2_checkout_96", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_145", "Checkout 1 guest from 1 room walk in booking having rate plan just before night audit, "
                               "cancel extra charge of checkout date",
             [{'id': "booking_01_no_show", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Multiple_Expense_29', 'type': 'create_expense_V3'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreview_checkout_145", 'type': 'preview_invoice'},
              {'id': "AddPaymentV2_checkout_97", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_146",
             "Checkout a booking for 2 day with two room and checkout one room on 1st day such that early checkout "
             "charges gets added",
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': 'invoicePreview_217', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_checkout_116', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_001', 'type': 'add_payment_v2'},
              {'id': 'Redistribute_Payments_Checkout_116', 'type': 'redistribute_payment'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_147",
             "Checkout a booking for 2 day with two room and checkout one room on 1st day, and post allowance on it "
             "such that early checkout charges gets added",
             [{'id': "booking_checkout_146", 'type': 'booking_v2'}, {'id': "checkin_02", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 3},
              {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [3, 1]},
              {'id': 'invoicePreview_checkout_147', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_checkout_116', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_001', 'type': 'add_payment_v2'},
              {'id': 'Redistribute_Payments_Checkout_116', 'type': 'redistribute_payment'}], 200, 'super-admin', "", "",
             "", "", {'run_night_audit_before': 0}, ""),
            ("CheckoutV2_148",
             "Checkout one guests from a booking of 2 room with 2 guest walk in booking on second day of booking "
             "posting the charge, cancelling the allowance and early checkout charge will get added",
             [{'id': "booking_two_room_two_guest_three_day_booking", 'type': 'booking_v2'},
              {'id': "checkin_two_room_two_guest_three_day_booking", 'type': 'checkin_v2'},
              {'id': "Create_Expense_checkout_01", 'type': 'expense'},
              {'id': "Create_Expense_checkout_02", 'type': 'expense'},
              {'id': "Create_Expense_checkout_03", 'type': 'expense'},
              {'id': "Create_Expense_checkout_04", 'type': 'expense'},
              {'id': "Create_Expense_checkout_05", 'type': 'expense'},
              {'id': "Create_Expense_checkout_06", 'type': 'expense'},
              {'id': "Create_Expense_checkout_07", 'type': 'expense'},
              {'id': "Create_Expense_checkout_08", 'type': 'expense'},
              {'id': "Create_Expense_checkout_09", 'type': 'expense'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': "AddAllowance_17", 'type': 'add_allowance_v2', 'extras': [15, 1]},
                                           {'id': 'invoicePreview_checkout_109', 'type': 'preview_invoice'},
                                           {'id': 'AddPaymentV2_checkout_124', 'type': 'add_payment_v2'},
                                           {'id': 'Redistribute_Payments_Checkout_122',
                                            'type': 'redistribute_payment'}]}, False),
            ("CheckoutV2_149",
             "Checkout one guests from a booking of 2 room with 2 guest walk in booking on second day of booking while"
             " posting the charge and allowance both and early checkout charge will get added",
             [{'id': "booking_two_room_two_guest_three_day_booking", 'type': 'booking_v2'},
              {'id': "checkin_two_room_two_guest_three_day_booking", 'type': 'checkin_v2'},
              {'id': "Create_Expense_checkout_01", 'type': 'expense'},
              {'id': "Create_Expense_checkout_02", 'type': 'expense'},
              {'id': "Create_Expense_checkout_03", 'type': 'expense'},
              {'id': "Create_Expense_checkout_04", 'type': 'expense'},
              {'id': "Create_Expense_checkout_05", 'type': 'expense'},
              {'id': "Create_Expense_checkout_06", 'type': 'expense'},
              {'id': "Create_Expense_checkout_07", 'type': 'expense'},
              {'id': "Create_Expense_checkout_08", 'type': 'expense'},
              {'id': "Create_Expense_checkout_09", 'type': 'expense'}], 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1,
              'action_after_night_audit': [{'id': "AddAllowance_17", 'type': 'add_allowance_v2', 'extras': [15, 1]},
                                           {'id': 'invoicePreview_checkout_149', 'type': 'preview_invoice'},
                                           {'id': 'AddPaymentV2_checkout_124', 'type': 'add_payment_v2'},
                                           {'id': 'Redistribute_Payments_Checkout_122',
                                            'type': 'redistribute_payment'}]}, False),
            ("CheckoutV2_150", "Checkout 1 guest from 1 room 1 day booking having an addon",
             CHECKOUT_BOOKING_HAVING_ADDONS, 200, 'super-admin', "", "", "", "", {'run_night_audit_before': 1}, ""),
            ("CheckoutV2_151", "Checkout 1 guest from 1 room 1 day booking after edit/recreate the booking",
             CHECKOUT_RECREATED_OR_EDITED_BOOKING, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1, 'is_put_booking_v2': 1}, ""),
            ("CheckoutV2_152", "Checkout 1 guest from 1 room 2 day booking having an add-ons after edit/recreate "
                               "the booking",
            CHECKOUT_RECREATED_BOOKING_HAVING_ADDONS, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1, 'is_put_booking_v2': 1}, ""),
            ("CheckoutV2_153", "Checkout 1 guest from 1 room 2 day reverse checked-out booking having an add-ons "
                               "after edit/recreate the booking and then reverse checked-out",
             CHECKOUT_REVERSE_CHECKEDOUT_AND_RECREATED_BOOKING_HAVING_ADDONS, 200, 'super-admin', "", "", "", "",
             {'run_night_audit_before': 1, 'is_put_booking_v2': 1}, ""),
        ])
    @pytest.mark.regression
    def test_checkout(self, client_, test_case_id, previous_actions, tc_description, status_code, user_type,
                      error_code, dev_message, error_payload, skip_message, extras, skip_case):

        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]
        has_slab_based_taxation = extras.get('has_slab_based_taxation') if isinstance(extras, dict) else False

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=True))

        if test_case_id in ('CheckoutV2_144', 'CheckoutV2_145'):
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today()-timedelta(days=1), hotel_id))
            query_execute(db_queries.UPDATE_SWITCH_OVER_TIME.format(switch_over_time='11:00:00'))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, extra=extras)

        if 'run_night_audit_before' in extras:
            for no_of_times_run_night_audit in range(extras['run_night_audit_before']):
                with mock_treebo_tenant_boolean():
                    self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)

        if 'action_after_night_audit' in extras:
            self.common_request_caller(client_, extras['action_after_night_audit'], hotel_id, extra=extras)

        response = self.booking_request.checkout_request_v2(client_, test_case_id, self.booking_request.booking_id,
                                                            status_code, hotel_id, user_type)

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=False))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)

        elif status_code in SUCCESS_CODES:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))
            query_execute(db_queries.UPDATE_FREE_LATE_CHECKOUT_TIME)
            query_execute(db_queries.UPDATE_SWITCH_OVER_TIME.format(switch_over_time='00:00:00'))
            self.report_requests.crs_report(client_, status_code, hotel_id)
            self.validation(client_, response, test_case_id, self.booking_request, self.billing_request,
                            self.booking_request.booking_id, self.booking_request.bill_id,
                            self.booking_request.action_id, hotel_id, user_type, extras,
                            self.booking_request.room_stay_ids_and_room_ids, self.expense_request,
                            self.booking_request.reference_number)
            delete_invoice_csv()
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request, billing_request, booking_id, bill_id,
                   action_id, hotel_id, user_type, extra_data, room_stay_ids_and_room_ids, expense_request,
                   reference_number):
        checkout_validation = ValidationCheckoutV2(client, response, test_case_id, booking_request, billing_request,
                                                   booking_id, bill_id, action_id, hotel_id, user_type, extra_data,
                                                   room_stay_ids_and_room_ids)
        report_validation = ValidationCrsReport(test_case_id, read_invoice_details_from_csv(), booking_id,
                                                reference_number, hotel_id, sheet_names.checkout_v2_sheet_name)
        checkout_validation.validation_response()
        checkout_validation.validate_get_booking()
        checkout_validation.validate_charge()
        checkout_validation.validate_invoice()
        checkout_validation.validate_charge_and_expense_status(expense_request)
        checkout_validation.validate_commissions()
        if test_case_id not in ('CheckoutV2_144', 'CheckoutV2_145'):
            report_validation.validate_data()
