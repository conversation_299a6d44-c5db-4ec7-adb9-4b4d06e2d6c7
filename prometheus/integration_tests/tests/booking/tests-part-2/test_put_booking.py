import pytest

from prometheus.integration_tests.config.common_config import SUPER_ADMIN, HOTEL_ID, ERROR_CODES, SUCCESS_CODES
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import SINGLE_BOOKING_01
from prometheus.integration_tests.tests.booking.validations.validation_create_booking_v2 import ValidationBooking


class TestPutBookingV2(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, dev_message, error_payload, "
        "skip_case, skip_message, extras",
        [
            ("booking_77", "Edit/put a booking with channel as hotel, subchannel as walkin, single room , pay at"
                           " checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_78", "Edit/put a booking with channel as hotel, subchannel as walkin, company details, "
                           "single room  pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_79", "Edit/put a booking with channel as hotel, subchannel as walkin, company details, single"
                           " room  , pay after checkout and without rate plan", 403, "", "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_80", "Edit/put a booking with channel as hotel, subchannel as walkin, company details, multiple "
                           "room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_81", "Edit/put a booking with channel as hotel, subchannel as walkin, single days, multiple "
                           "room ,  pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_82", "Edit/put a booking with channel as hotel, subchannel as walkin, multiple days, company "
                           "details single room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "",
             "", "", {"non_rate_plan_booking": True}),
            ("booking_83", "Edit/put a booking with channel as hotel, subchannel as walkin, multiple days, company "
                           "details, single room , pay after checkout and without rate plan", 403, "", "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_84", "Edit/put a booking with channel as hotel, subchannel as walkin, multiple days,"
                           " company details multiple room , pay at checkout and without rate plan", 200, SUPER_ADMIN,
             "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_85", "Edit/put a future bookings with channel as hotel, subchannel as walkin, single days, "
                           "multiple room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_86", "Edit/put a future bookings with channel as hotel, subchannel as walkin, single days, "
                           "company details, single room , pay at checkout and without rate plan", 200, SUPER_ADMIN,
             "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_87", "Edit/put a future bookings with channel as hotel, subchannel as walkin, single days,"
                           " company details, single room , pay after checkout and without rate plan", 403, "", "",
             "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_88", "Edit/put a future bookings with channel as hotel, subchannel as walkin, company details,"
                           " multiple room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_89",
             "Edit/put a future bookings with channel as hotel, subchannel as walkin, single days, multiple"
             " room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "",
             "", "", {"non_rate_plan_booking": True}),
            ("booking_91", "Edit/put a future bookings with channel as hotel, subchannel as walkin, multiple days, "
                           "company details, single room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "",
             "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_92", "Edit/put a future bookings with channel as hotel, subchannel as walkin, multiple days,"
                           "company details, single room , pay after checkout and without rate plan", 403,
             "", "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_93", "Edit/put a future bookings with channel as hotel, subchannel as walkin, multiple days, "
                           "company details, multiple room , pay at checkout and without rate plan", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_94",
             "Edit/put a booking with channel as hotel, subchannel as walkin, single days, company details,"
             " single room , pay at checkout and without rate plan with default billed entity as booker_company",
             200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_95",
             "Edit/put a booking with channel as hotel, subchannel as walkin, single days, company details,"
             " multiple room, multiple guest , pay at checkout and without rate plan  ", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_96", "Edit/put a booking with channel as hotel, subchannel as walkin, mutliple days, company"
                           " details, multiple room, multiple guest , pay at checkout and without rate plan  ", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_97",
             "Edit/put to future bookings with channel as hotel, subchannel as walkin, single days, company"
             " details, multiple room, multiple guest , pay at checkout and without rate plan ", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_98",
             "Edit/put to future bookings with channel as hotel, subchannel as walkin, mutliple days, company"
             " details, multiple room, multiple guest , pay at checkout and without rate plan ", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_99",
             "Edit/put a booking with channel as hotel, subchannel as walkin, single days, company details,"
             " single room, multiple guest , pay at checkout and without rate plan ", 200, SUPER_ADMIN,
             "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_100", "Edit/put a booking with channel as hotel, subchannel as walkin, multiple days, company"
                            "details, single room, multiple guest , pay at checkout and without rate plan ", 200,
             SUPER_ADMIN, "", "",
             "", "", "", {"non_rate_plan_booking": True}),
            ("booking_101",
             "Edit/put to future bookings with channel as hotel, subchannel as walkin, single days, company"
             "details, single room, multiple guest , pay at checkout and without rate plan ", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_102",
             "Edit/put to future bookings with channel as hotel, subchannel as walkin, multiple days, company"
             " details, single room, multiple guest , pay at checkout and without rate plan ", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            (
                    "booking_103",
                    "Edit/put a future bookings with channel as hotel and subchannel as walkin for multiple days"
                    " with single rooms, multiple guests, pay at checkout", 200, SUPER_ADMIN, "", "", "", "",
                    "", {"non_rate_plan_booking": True}),
            (
                    "booking_104",
                    "Edit/put a booking with channel as b2b and subchannel as corporate without company details",
                    200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_105",
             "Edit/put a booking with channel as b2b and subchannel as corporate with company details and "
             "pay after checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_106",
             "Edit/put a booking with channel as b2b and subchannel as corporate with company details and "
             "pay at checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_107",
             "Edit/put a booking with channel as b2b and subchannel as corporate with company details and "
             "pay after checkout and multiple room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_108",
             "Edit/put a booking with channel as b2b and subchannel as corporate for multiple days without "
             "company details and pay after checkout and single room", 200, SUPER_ADMIN, "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_109", "Edit/put a booking with channel as b2b and subchannel as corporate for multiple days with "
                            "company details and pay after checkout and single room", 200, SUPER_ADMIN, "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_110", "Edit/put a booking with channel as b2b and subchannel as corporate for multiple days with "
                            "company details and pay at checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_111", "Edit/put a booking with channel as b2b and subchannel as corporate for multiple days with "
                            "company details and pay at checkout and multiple room", 200, SUPER_ADMIN, "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_112", "Edit/put a future bookings with channel as b2b and subchannel as corporate "
             , 200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_113", "Edit/put a future bookings with channel as b2b and subchannel as corporate with company"
                            " details and pay after checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_114", "Edit/put a future bookings with channel as b2b and subchannel as corporate with company "
                            "details and pay at checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_115", "Edit/put a future bookings with channel as b2b and subchannel as corporate with company"
                            " details and pay after checkout and multiple room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_116", "Edit/put a future bookings with channel as b2b and subchannel as corporate for multiple"
                            " dates", 200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_117", "Edit/put a future bookings with channel as b2b and subchannel as corporate for multiple "
                            "dates with company details and pay after checkout and single room", 200, SUPER_ADMIN, "",
             "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_118", "Edit/put afuture bookings with channel as b2b and subchannel as corporate for "
                            "multiple dates with company details and pay at checkout and single room", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_119", "Edit/put a future bookings with channel as b2b and subchannel as corporate for multiple "
                            "dates with company details and pay after checkout and multiple room", 200, SUPER_ADMIN, "",
             "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_120", "Edit/put a booking with channel as b2b and subchannel as corporate with multiple room and "
                            "multiple guests and pay after checkout and single room", 200, SUPER_ADMIN, "", "",
             "", "", "", {"non_rate_plan_booking": True}),
            (
                    "booking_121",
                    "Edit/put a booking with channel as b2b and subchannel as corporate fro multiple dates with "
                    "multiple room and multiple guests and pay after checkout and single room", 200,
                    SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_122", "Edit/put a future bookings with channel as b2b and subchannel as corporate with multiple "
                            "room and multiple guests and pay after checkout and single room", 200, SUPER_ADMIN, "", "",
             "", "", "", {"non_rate_plan_booking": True}),
            ("booking_123", "Edit/put a future bookings with channel as b2b and subchannel as corporate from multiple"
                            " dates  with multiple room and multiple guests and pay after checkout and single room",
             200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_124",
             "Edit/put a booking with channel as b2b and subchannel as corporate with billed to type as guest",
             200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_125",
             "Edit/put a booking with channel as b2b and subchannel as corporate with billed to type astravel agent",
             200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_126",
             "Edit/put a booking with channel as b2b and subchannel as corporate with billed to type astravel"
             " agent with pay after checkout", 400, "", "", "", "", "", "", {"non_rate_plan_booking": True}),

            ############################## Default Billing Instruction Test Cases ##########################
            ("booking_173", "Update default_billed_entity_category_for_extras as Invalid enum", 400, SUPER_ADMIN,
             "04010006", "", {"field": "default_billed_entity_category_for_extras"}, "", "",
             {"non_rate_plan_booking": True}),
            ("booking_174", "Update default_payment_instruction as Invalid enum", 400, SUPER_ADMIN, "04010006", "",
             {"field": "default_payment_instruction"}, "", "", {"non_rate_plan_booking": True}),
            ("booking_175", "Update default_payment_instruction_for_extras as Invalid enum", 400, SUPER_ADMIN,
             "04010006", "", {"field": "default_payment_instruction_for_extras"}, "", "",
             {"non_rate_plan_booking": True}),
            ("booking_176", "Update default_billed_entity_category as Invalid enum", 400, SUPER_ADMIN, "04010006",
             "", {"field": "default_billed_entity_category"}, "", "", {"non_rate_plan_booking": True}),
            ("booking_177", "Update default_billed_entity_category_for_extras as EMPTY", 400, SUPER_ADMIN, "04010006",
             "", {"field": "default_billed_entity_category_for_extras"}, False, "", ""),
            ("booking_178", "Update default_payment_instruction as EMPTY", 400, SUPER_ADMIN, "04010006", "",
             {"field": "default_payment_instruction"}, False, "", ""),
            ("booking_179", "Update default_payment_instruction_for_extras as EMPTY", 400, SUPER_ADMIN, "04010006", "",
             {"field": "default_payment_instruction_for_extras"}, "", "", ""),
            ("booking_180", "Update default_billed_entity_category as EMPTY", 400, SUPER_ADMIN, "04010006", "",
             {"field": "default_billed_entity_category"}, "", "", ""),
            ("booking_181_put_booking", "Update default_billed_entity_category_for_extras as NULL", 200, SUPER_ADMIN,
             "", "", "", False, "", ""),
            ("booking_182_put_booking", "Update default_payment_instruction as NULL", 200, SUPER_ADMIN, "", "", "",
             False, "", ""),
            ("booking_183_put_booking", "Update default_payment_instruction_for_extras as NULL", 200, SUPER_ADMIN, "",
             "", "", False, "", ""),
            ("booking_184_put_booking", "Update default_billed_entity_category as NULL", 200, SUPER_ADMIN, "", "", "",
             False, "", ""),
            ("booking_166_put_booking", "Update default billing instruction from booker to primary_guest", 200,
             SUPER_ADMIN, "", "", "", False, "",
             [{"previous_action": [{'id': "booking_77_put_booking", 'type': 'booking_v2'}]},
              {"non_rate_plan_booking": True}]),
            ("booking_167_put_booking", "Update default billing instruction from booker to company", 200, SUPER_ADMIN,
             "", "", "", False, "", [{"previous_action": [{'id': "booking_77_put_booking", 'type': 'booking_v2'}]},
                                     {"non_rate_plan_booking": True}]),
            ("booking_168_put_booking", "Update default billing instruction from booker to TA", 200, SUPER_ADMIN, "",
             "", "", False, "", [{"previous_action": [{'id': "booking_77_put_booking", 'type': 'booking_v2'}]},
                                 {"non_rate_plan_booking": True}]),
            ("booking_165_put_booking", "Update default billing instruction from primary_guest to booker", 200,
             SUPER_ADMIN, "", "", "", False, "",
             [{"previous_action": [{'id': "booking_77_put_booking_02", 'type': 'booking_v2'}]},
              {"non_rate_plan_booking": True}]),
            ("booking_167_put_booking", "Update default billing instruction from primary_guest to company", 200,
             SUPER_ADMIN, "", "", "", False, "",
             [{"previous_action": [{'id': "booking_77_put_booking_02", 'type': 'booking_v2'}]},
              {"non_rate_plan_booking": True}]),
            ("booking_168_put_booking", "Update default billing instruction from primary_guest to TA", 200, SUPER_ADMIN,
             "", "", "", False, "", [{"previous_action": [{'id': "booking_77_put_booking_02", 'type': 'booking_v2'}]},
                                     {"non_rate_plan_booking": True}]),
            ("booking_165_put_booking", "Update default billing instruction from company to booker", 200, SUPER_ADMIN,
             "", "", "", False, "", [{"previous_action": [{'id': "booking_78_put_booking", 'type': 'booking_v2'}]},
                                     {"non_rate_plan_booking": True}]),
            ("booking_166_put_booking", "Update default billing instruction from company to primary_guest", 200,
             SUPER_ADMIN, "", "", "", False, "",
             [{"previous_action": [{'id': "booking_78_put_booking", 'type': 'booking_v2'}]},
              {"non_rate_plan_booking": True}]),
            ("booking_168_put_booking", "Update default billing instruction from company to TA", 200, SUPER_ADMIN,
             "", "", "", False, "", [{"previous_action": [{'id': "booking_78_put_booking", 'type': 'booking_v2'}]},
                                     {"non_rate_plan_booking": True}]),
            ("booking_165_put_booking", "Update default billing instruction from TA to booker", 200, SUPER_ADMIN,
             "", "", "", False, "", [{"previous_action": [{'id': "booking_125_put_booking", 'type': 'booking_v2'}]},
                                     {"non_rate_plan_booking": True}]),
            ("booking_166_put_booking", "Update default billing instruction from TA to primary_guest", 200,
             SUPER_ADMIN, "", "", "", False, "",
             [{"previous_action": [{'id': "booking_125_put_booking", 'type': 'booking_v2'}]},
              {"non_rate_plan_booking": True}]),
            ("booking_167_put_booking", "Update default billing instruction from TA to company", 200, SUPER_ADMIN,
             "", "", "", False, "", [{"previous_action": [{'id': "booking_125_put_booking", 'type': 'booking_v2'}]},
                                     {"non_rate_plan_booking": True}]),
            ("booking_167_put_booking", "Update Payment instruction from pay after checkout to pay at checkout", 200,
             SUPER_ADMIN, "", "", "", False, "",
             [{"previous_action": [{'id': "booking_78_put_booking_02", 'type': 'booking_v2'}]},
              {"non_rate_plan_booking": True}]),
            ("booking_171_put_booking", "Update Payment instruction from pay at checkout to pay after checkout", 200,
             SUPER_ADMIN, "", "", "", False, "",
             [{"previous_action": [{'id': "booking_78_put_booking", 'type': 'booking_v2'}]},
              {"non_rate_plan_booking": True}]),
        ])
    @pytest.mark.regression
    def test_put_booking(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                         dev_message, error_payload, skip_case, skip_message, extras):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]

        if isinstance(extras, list) and 'previous_action' in extras[0]:
            self.common_request_caller(client_, extras[0].get('previous_action'), hotel_id, extra=extras[1])
        else:
            self.common_request_caller(client_, SINGLE_BOOKING_01, hotel_id)

        response = self.booking_request.put_booking_request(client_, test_case_id, status_code, user_type, extras)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            booking_id = self.booking_request.booking_id
            bill_id = self.booking_request.bill_id
            self.validation(client_, response, test_case_id, self.booking_request, self.billing_request, booking_id,
                            bill_id, hotel_id, user_type, extras)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, booking_request, billing_request, booking_id, bill_id,
                   hotel_id, user_type, extra_data):
        validation = ValidationBooking(client_, response, test_case_id, booking_request, billing_request, booking_id,
                                       bill_id, hotel_id, user_type, extra_data)
        validation.validate_response(True)
        validation.validate_get_booking(True)
        validation.validate_get_bill(True)
        validation.compare_get_booking_and_bill_summary()
        validation.validate_billed_entities(True)
