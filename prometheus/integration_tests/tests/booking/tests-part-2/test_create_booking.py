import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.tests.booking.validations.validation_create_booking import (
    ValidationCreateBooking,
)
from prometheus.integration_tests.tests.booking.validations.validation_create_booking_v2 import (
    ValidationBooking,
)
from prometheus.integration_tests.utilities.common_utils import set_inventory_count
from prometheus.integration_tests.utilities.inventory_modifications import *


class TestCreateBooking(BaseTest):

    @pytest.mark.parametrize("test_case_id, existing_test_case_id, tc_description, inventory_config, status_code, "
                             "user_type, error_code, dev_message, error_payload, skip_case, skip_message", [
                                 ("Booking_02", "Booking_02", "Single booking with multiple days", "", 200, SUPER_ADMIN,
                                  "", "", "", "", ""),
                                 ("Booking_06", "Booking_06", "Group booking with diff room types", "", 200,
                                  SUPER_ADMIN, "", "", "", "", ""),
                                 ("Booking_12", "Booking_12", "Group Coorporate booking", "", 200, SUPER_ADMIN, "", "",
                                  "", "", ""),
                                 ("Booking_43", "Booking_43",
                                  "CHANGE_INVENTORY Create a booking after 24 months where Inventory is present",
                                  {"start_inventory_date": 739, "end_inventory_date": 742}, 400, SUPER_ADMIN,
                                  "04010109", "", "", True, ""),
                                 ("Booking_44", "Booking_44", "Past date booking", "", 403, SUPER_ADMIN, "", "", "", "",
                                  ""),
                                 ("Booking_Policy_03", "Booking_11", "Create a non walk-in and non-hotel_corporate", "",
                                  403, FDM,
                                  "04010922", "", "", "", "")])
    @pytest.mark.regression
    def test_create_booking_smoke(self, client_, test_case_id, existing_test_case_id, tc_description, inventory_config,
                                  status_code, user_type, error_code, dev_message, error_payload, skip_case,
                                  skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if "CHANGE_INVENTORY" in tc_description:
            change_inventory(client_, inventory_config['start_inventory_date'], inventory_config['end_inventory_date'],
                             hotel_id=hotel_id)
        test_case_id_for_errors = test_case_id
        if existing_test_case_id:
            test_case_id = existing_test_case_id
        extra_data = None
        response = self.booking_request.new_booking_request(client_, test_case_id, status_code, user_type=user_type)

        if "CHANGE_INVENTORY" in tc_description:
            change_inventory(client_)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id_for_errors)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request.bill_id, extra_data, hotel_id)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize("test_case_id, tc_description, status_code, user_type, "
                             "error_code, dev_message, error_payload, skip_case, skip_message", [
                                 ("Booking_01", "Single booking with 1 guest", 200, "", "", "", "", "", ""),
                                 ("Booking_03", "Single booking with adult and Child", 200, "", "", "", "", "", ""),
                                 ("Booking_04", "Group booking with same room type", 200, "", "", "", "", "", ""),
                                 ("Booking_05", "Group booking for multiple days with Adult and child", 200, "", "", "",
                                  "", "", ""),
                                 ("Booking_07", "Booking with Gap in room dates", 200, "", "", "", "", "", ""),
                                 ("Booking_08", "Remove booking owner name KEY", 400, "", "04010006", "", "", "", ""),
                                 ("Booking_11", "Single Coorporate booking", 200, "", "", "", "", "", ""),
                                 ("Booking_107", "Single booking with travel agent details", 200, "", "", "", "", True,
                                  ""),
                                 ("Booking_108", "Travel Agent Details with Optional Booker Details", 200, "", "", "",
                                  "", True, ""),
                                 ("Booking_109", "Corporate Details with Optional Booker Details", 200, "", "", "", "",
                                  "", ""),
                                 ("Booking_110", "Single booking with travel agent details without legal name", 400, "",
                                  "04010006", "", "", "", ""),
                                 (
                                         "Booking_111",
                                         "Single booking with travel agent details with legal name as null", 400,
                                         "", "04010006", "", "", "", ""),
                                 ("Booking_13", "Wrong Profile type of owner", 400, "", "04010006", "", "", "", ""),
                                 ("Booking_16", "Wrong Hotel ID format", 404, "", "04010007", "", "", "", ""),
                                 ("Booking_17", "Booking with hotel ID as null", 400, "", "04010006", "", "", "", ""),
                                 ("Booking_19", "Create a booking with reserved status and no hold_till value", 200, "",
                                  "", "", "", "", ""),
                                 ("Booking_20", "Booking with adults more than occupancy", 400, "", "", "", "", "", ""),
                                 ("Booking_22", "BOOKING_WITH_NOINVENTORY", 400, "", "04010602", "", "", "", ""),
                                 ("Booking_23", "Booking with Checkout less than checkIn date", 400, "", "04010006", "",
                                  "", "", ""),
                                 ("Booking_25", "Booking 2 days price only 1day", 400, "", "04010101", "", "", "", ""),
                                 ("Booking_26", "Booking with Booking SOURCE as null", 400, "", "04010006", "", "", "",
                                  ""),
                                 ("Booking_27", "Booking with future dates", 200, "", "", "", "", "", ""),
                                 ("Booking_28", "Booking without a RoomStay", 400, "", "04010006", "", "", "", ""),
                                 ("Booking_29", "CONFIRMED booking with null payment", 200, "", "", "", "", "", ""),
                                 ("Booking_30", "Booking with minimun payload", 200, "", "", "", "", "", ""),
                                 ("Booking_31", "Pass wrong timezone in booking dates", 400, "", "04010006", "", "", "",
                                  ""),
                                 ("Booking_34_hold_till_cancel",
                                  "Verify the confirmed booking is auto cancelled after the hold_till time for "
                                  "future booking", 200, "", "", "", "", "", ""),
                                 ("Booking_35_hold_till", "Provide hold_till and booking status as confirmed", 400, "",
                                  "", "", "", "", ""),
                                 ("Booking_36_hold_till", "provide hold_till more than checkout date", 400, "",
                                  "04010102", "", "", "", ""),
                                 ("Booking_37", "HOLD_TILL_LESS_CURRENT_TIME", 400, "", "04010102", "", "", "", ""),
                                 (
                                         "Booking_39_timezone",
                                         "Verify if time is send in UTC its returned in IST format", 200,
                                         "", "", "", "", "", ""),
                                 ("Booking_45_NULL_CHECK", "Booking with null object GST Details", 200, "", "", "", "",
                                  "", ""),
                                 ("Booking_46_NULL_CHECK", "Booking with null object on mandatory Fields", 400, "",
                                  "04010006", "", "", True, ""),
                                 ("Booking_emptyStrings",
                                  "New booking with mandatory and non mandatory fields as emptyString", 400, "",
                                  "04010006", "", "", True, ""),
                                 ("Booking_whitespaces",
                                  "New booking with mandatory and non mandatory fields value as whitespaces", 400, "",
                                  "04010006", "", "", True, ""),
                                 ("Booking_invalidGSTN", "New booking with invalid GSTN for booking owner and guest",
                                  200, "", "", "", "", "", ""),
                                 ("Booking_randomCICOtime", "Single booking with CI/CO dates different then hotel", 200,
                                  "", "", "", "", "", ""),
                                 ("Booking_randomCICOTimeZone",
                                  "Single booking with CI/CO timezone different then hotel", 200, "", "", "", "",
                                  True, ""),
                                 ("Booking_54_hold_till", "Provide hold_till and booking status as temporary", 200, "",
                                  "", "", "", "", ""),
                                 ("Booking_55", "Create booking with Ref number as null", 400, "", "", "", "", "", ""),
                                 ("Booking_56", "Create booking with Ref number as duplicate", 409, "", "", "",
                                  "", True, ""),
                                 ("Booking_57", "Create a booking for past month after settlement date", 200, "",
                                  "", "", "", True, ""),
                                 ("Booking_66", "create booking with has_lut and is_sez as true", 200, "", "", "", "",
                                  "", ""),
                                 ("Booking_67", "create booking with has_lut as false and is_sez as true", 200, "", "",
                                  "", "", "", ""),
                                 ("Booking_68", "Create Booking with profile type = 'sme' and valid gstin number", 200,
                                  "", "", "", "", "", ""),
                                 ("Booking_69", "Create booking with profile type = 'sme' and no gstin number", 200, "",
                                  "", "", "", "", "")
                             ])
    @pytest.mark.regression
    def test_create_booking(self, client_, test_case_id, tc_description, status_code,
                            user_type, error_code, dev_message, error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]

        extra_data = None
        response = self.booking_request.new_booking_request(client_, test_case_id, status_code)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request.bill_id, extra_data, hotel_id)
        else:
            assert False, "Response status code is not matching"

    def validation(self, client_, response, test_case_id, bill_id, extra_data, hotel_id, validate_billed_entity=False):
        validation = ValidationCreateBooking(client_, test_case_id, response, bill_id, self.billing_request, extra_data,
                                             hotel_id)
        if validate_billed_entity:
            validation.validate_billing_entity()
            billed_entity_validation = BaseValidations()
            billed_entity_validation.validate_proforma_invoice(client_, self.booking_request, self.billing_request,
                                                               bill_id, self.booking_request.
                                                               get_booking_request_v2(client_,
                                                                                      self.booking_request.booking_id,
                                                                                      200), extra_data)
        else:
            validation.validate_response(self.billing_request, self.customer_request)
            validation.validate_roomstay_details(self.customer_request)
            validation.validate_payment(self.billing_request)

    @pytest.mark.parametrize(
        "test_case_id, existing_test_case_id, tc_description, inventory_config, status_code, user_type, error_code, dev_message, error_payload, skip_case, skip_message",
        [
            ("Booking_Policy_04", "Booking_01", "Create a  subChannel walk-in for FDM", "", 200, FDM, "", "", "", "",
             ""),
            ("Booking_Policy_05", "Booking_02", "Create a  subChannel hotel-corporate for FDM", "", 200, FDM, "",
             "", "", "", ""),
            ("Booking_Policy_06", "Booking_24", "Past dated booking for CR_Team with channel OTA/TA/B2B/Direct",
             "", 200, CR_TEAM, "", "", "", True, ""),
            ("Booking_Policy_07", "Booking_11",
             "Single Corporate booking for CR_team for B2B_PMS-teebo application_code",
             "", 403, CR_TEAM, "", "", "", "", ""),
            ("Booking_Policy_08", "Booking_14", "Create a non-b2b booking with credit charge", "", 403, FDM, "04010921",
             "", "", "", ""),
            ("Booking_Policy_09", "Booking_04", "OVERBOOKING_BY_OTA", "", 200, CR_TEAM, "", "", "", "", ""),
            ("Booking_Policy_10", "Booking_Direct_overbooking", "OVERBOOKING_BY_DIRECT", "", 200, CR_TEAM, "", "", "",
             "", ""),
            ("Booking_Policy_11", "Booking_12", "OVERBOOKING_BY_B2B", "", 400, CR_TEAM, "", "", "", "", ""),
            ("Booking_Policy_12", "Booking_12", "Single Corporate booking for CR_team", "", 200, CR_TEAM, "", "",
             "", "", ""),
            ("Booking_Policy_13", "Booking_04", "Create booking by gdc", "", 403, GDC, "", "", "", "", ""),
            ("Booking_Policy_14", "Booking_54_hold_till", "Create temp booking by gdc", "", 200, GDC, "", "", "", "",
             ""),
            ("Booking_Policy_15", "Booking_57",
             "CHANGE_INVENTORY Create a booking for past month after settlement date "
             "by FDM", {"start_inventory_date": -36, "end_inventory_date": -30}, 403,
             FDM, "", "", "", "", ""),
            (
                    "Booking_Policy_16", "Booking_57",
                    "CHANGE_INVENTORY Create a booking for past month after settlement date "
                    "by CR_Team", {"start_inventory_date": -36, "end_inventory_date": -30},
                    403, FDM, "", "", "", "", "")
        ])
    @pytest.mark.regression
    def test_create_booking_policy(self, client_, test_case_id, existing_test_case_id, tc_description, inventory_config,
                                   status_code, user_type, error_code, dev_message, error_payload, skip_case,
                                   skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if "OVERBOOKING" in tc_description:
            set_inventory_count(0, hotel_id)
        if "CHANGE_INVENTORY" in tc_description:
            change_inventory(client_, inventory_config['start_inventory_date'], inventory_config['end_inventory_date'],
                             hotel_id=hotel_id)
        test_case_id_for_errors = test_case_id
        if existing_test_case_id:
            test_case_id = existing_test_case_id
        extra_data = None
        response = self.booking_request.new_booking_request(client_, test_case_id, status_code, user_type=user_type)

        if "OVERBOOKING" in tc_description:
            set_inventory_count(500, hotel_id)
        if "CHANGE_INVENTORY" in tc_description:
            change_inventory(client_)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id_for_errors)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request.bill_id, extra_data, hotel_id)
        else:
            assert False, "Response status code is not matching"

    # Todo: Need to figure out a way to validate tax call reequest based on gstin_details availability
    @pytest.mark.parametrize("test_case_id, existing_test_case_id,tc_description, extra_data, status_code, user_type, "
                             "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [
                                 ("CreateBookingKeralaCess_01", "Booking_01",
                                  "Create Booking with GSTIN details for Walkin booking",
                                  {"include_kerala_cess": False}, 200, None, "", "", "", "", False, ""),
                                 ("CreateBookingKeralaCess_02", "Booking_03",
                                  "Create Booking with GSTIN details for Direct booking",
                                  {"include_kerala_cess": False}, 200, None, "", "", "", "", False, ""),
                                 ("CreateBookingKeralaCess_03", "Booking_04",
                                  "Create Booking with GSTIN details for Ota booking",
                                  {"include_kerala_cess": False}, 200, None, "", "", "", "", False, ""),
                                 ("CreateBookingKeralaCess_04", "Booking_59",
                                  "Create Booking without GSTIN details for Walkin booking",
                                  {"include_kerala_cess": True}, 200, None, "", "", "", "", False, ""),
                                 ("CreateBookingKeralaCess_05", "Booking_60",
                                  "Create Booking without GSTIN details for Direct booking",
                                  {"include_kerala_cess": True}, 200, None, "", "", "", "", False, ""),
                                 ("CreateBookingKeralaCess_05", "Booking_61",
                                  "Create Booking without GSTIN details for Ota booking",
                                  {"include_kerala_cess": True}, 200, None, "", "", "", "", False, ""),
                             ])
    @pytest.mark.regression
    def test_create_booking_kerala_cess(self, client_, test_case_id, existing_test_case_id, tc_description, extra_data,
                                        status_code, user_type, error_code, error_message, dev_message, error_payload,
                                        skip_case, skip_message):

        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if existing_test_case_id:
            test_case_id = existing_test_case_id
        response = self.booking_request.new_booking_request(client_, test_case_id, status_code, user_type,
                                                            include_kerala_cess=extra_data['include_kerala_cess']
                                                            if extra_data is not None and
                                                               'include_kerala_cess' in extra_data else None,
                                                            hotel_id=hotel_id)

        if status_code in (400, 403):
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in (200, 201):
            self.validation(client_, response, test_case_id, self.booking_request.bill_id, extra_data, hotel_id)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize("test_case_id, existing_test_case_id,tc_description, extra_data, status_code, user_type, "
                             "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [
                                 (
                                         "CreateBookingRatePlan_01", "Booking_01",
                                         "Create Booking with rate plan but not flexi"
                                         , None, 200, None, "", "", "", "", False, ""),
                                 ("CreateBookingRatePlan_02", "Booking_02", "Create Booking with flexi rate plan"
                                  , "flexi", 200, None, "", "", "", "", False, ""),
                                 ("CreateBookingRatePlan_03", "Booking_04", "Create booking of two rooms one of them "
                                                                            "with flexi rate plan", "flexi", 200, None,
                                  "",
                                  "", "", "", False, ""),
                                 ("CreateBookingRatePlan_04", "Booking_96", "Create booking with flexi rate plan with"
                                                                            " cancellation_policy 100% non-refundable",
                                  "flexi", 200, None, "", "", "", "", False, ""),
                                 ("CreateBookingRatePlan_05", "Booking_97", "Single booking with flexi rate plan and "
                                                                            "layered cancellation policy ", "flexi",
                                  200,
                                  None, "", "", "", "", False, ""),
                                 ("CreateBookingRatePlan_06", "Booking_98", "Single booking with flexi rate plan and "
                                                                            "only payment policy (percent policy) ",
                                  "flexi", 200, None, "", "", "", "", False, ""),
                                 ("CreateBookingRatePlan_07", "Booking_105", "Single booking with flexi rate plan and "
                                                                             "only payment policy (X days before checkin policy) ",
                                  "flexi", 200, None, "", "", "",
                                  "", False, ""),
                                 ("CreateBookingRatePlan_08", "Booking_106", "Single Booking with cancellation policy "
                                                                             "as non-refundable 100% and payment policy (percent policy)",
                                  "flexi", 200, None,
                                  "", "", "", "", False, ""),
                                 ("CreateBookingRatePlan_09", "Booking_102", "Single Booking with cancellation policy "
                                                                             "as non-refundable 100% and payment policy (X days before checkin policy)",
                                  "flexi",
                                  200, None, "", "", "", "", False, ""),
                                 ("CreateBookingRatePlan_10", "Booking_103", "Single booking with flexi rate plan with"
                                                                             " cancellation policy as layered cancellation policy and payment policy"
                                                                             " (percent policy)", "flexi", 200, None,
                                  "",
                                  "", "", "", False, ""),
                                 ("CreateBookingRatePlan_11", "Booking_104", "Single booking with flexi rate plan with"
                                                                             " cancellation policy as layered cancellation policy and payment policy"
                                                                             " (X days before checkin policy)", "flexi",
                                  200, None, "", "", "", "", False, "")
                             ])
    @pytest.mark.regression
    def test_create_booking_with_rate_plan(self, client_, test_case_id, existing_test_case_id, tc_description,
                                           extra_data,
                                           status_code, user_type, error_code, error_message, dev_message,
                                           error_payload,
                                           skip_case, skip_message):

        if skip_case:
            pytest.skip(skip_message)
        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]
        if existing_test_case_id:
            test_case_id = existing_test_case_id
        response = self.booking_request.new_booking_request(client_, test_case_id, status_code, user_type,
                                                            hotel_id=hotel_id, enable_rate_plan=True)

        if status_code in (400, 403):
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in (200, 201):
            self.validation(client_, response, test_case_id, self.booking_request.bill_id, extra_data, hotel_id)
        else:
            assert False, "Response status code is not matching"

        # Todo: Need to figure out a way to validate tax call reequest based on gstin_details availability

    @pytest.mark.parametrize(
        "test_case_id, existing_test_case_id,tc_description, extra_data, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [
            ("CreateBookingV2_01", "Booking_01", "Create Booking with GSTIN details for Walkin booking",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_02", "Booking_03", "Single booking with adult and Child",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_03", "Booking_04", "Group booking with same room type",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_04", "Booking_05", "Group booking for multiple days with Adult and child",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_05", "Booking_07", "Booking with Gap in room dates",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_06", "Booking_08", "Remove booking owner name KEY",
             {"is_booking_v2": True}, 400, None, "04010006", "", "", "", False, ""),
            ("CreateBookingV2_07", "Booking_11", "Single Coorporate booking",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_08", "Booking_13", "Wrong Profile type of owner",
             {"is_booking_v2": True}, 400, None, "04010006", "", "", "", False, ""),
            ("CreateBookingV2_09", "Booking_16", "Wrong Hotel ID format",
             {"is_booking_v2": True}, 404, None, "04010007", "", "", "", False, ""),
            ("CreateBookingV2_10", "Booking_17", "Booking with hotel ID as null",
             {"is_booking_v2": True}, 400, None, "04010006", "", "", "", False, ""),
            ("CreateBookingV2_11", "Booking_19", "Create a booking with reserved status and no hold_till value",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_12", "Booking_20", "Booking with adults more than occupancy",
             {"is_booking_v2": True}, 400, None, "04010122", "", "", "", False, ""),
            ("CreateBookingV2_13", "Booking_22", "BOOKING_WITH_NOINVENTORY",
             {"is_booking_v2": True}, 400, None, "04010602", "", "", "", False, ""),
            ("CreateBookingV2_14", "Booking_23", "Booking with Checkout less than checkIn date",
             {"is_booking_v2": True}, 400, None, "04010006", "", "", "", False, ""),
            ("CreateBookingV2_15", "Booking_25", "Booking 2 days price only 1day",
             {"is_booking_v2": True}, 400, None, "04010101", "", "", "", False, ""),
            ("CreateBookingV2_16", "Booking_26", "Booking with Booking SOURCE as null",
             {"is_booking_v2": True}, 400, None, "04010006", "", "", "", False, ""),
            ("CreateBookingV2_17", "Booking_27", "Booking with future dates",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_18", "Booking_28", "Booking without a RoomStay",
             {"is_booking_v2": True}, 400, None, "04010006", "", "", "", False, ""),
            ("CreateBookingV2_19", "Booking_29", "CONFIRMED booking with null payment",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_20", "Booking_30", "Booking with minimun payload",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_21", "Booking_31", "Pass wrong timezone in booking dates",
             {"is_booking_v2": True}, 400, None, "04010006", "", "", "", False, ""),
            ("CreateBookingV2_22", "Booking_34_hold_till_cancel",
             "Verify the confirmed booking is auto cancelled after the hold_till time for "
             "future booking", {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_23", "Booking_35_hold_till", "Provide hold_till and booking status as confirmed",
             {"is_booking_v2": True}, 400, None, "", "", "", "", False, ""),
            ("CreateBookingV2_24", "Booking_36_hold_till", "provide hold_till more than checkout date",
             {"is_booking_v2": True}, 400, None, "04010102", "", "", "", False, ""),
            ("CreateBookingV2_25", "Booking_37", "HOLD_TILL_LESS_CURRENT_TIME",
             {"is_booking_v2": True}, 400, None, "04010102", "", "", "", False, ""),
            ("CreateBookingV2_26", "Booking_39_timezone", "Verify if time is send in UTC its returned in IST format",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_27", "Booking_45_NULL_CHECK", "Booking with null object GST Details",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_28", "Booking_invalidGSTN", "New booking with invalid GSTN for booking owner and guest",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_29", "Booking_randomCICOtime", "Single booking with CI/CO dates different then hotel",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_30", "Booking_54_hold_till", "Provide hold_till and booking status as temporary",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_31", "Booking_55", "Create booking with Ref number as null",
             {"is_booking_v2": True}, 400, None, "", "", "", "", False, ""),
            ("CreateBookingV2_32", "Booking_66", "create booking with has_lut and is_sez as true",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_33", "Booking_67", "create booking with has_lut as false and is_sez as true",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_34", "Booking_68", "Create Booking with profile type = 'sme' and valid gstin number",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_35", "Booking_69", "Create booking with profile type = 'sme' and no gstin number",
             {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingRatePlan_01", "Booking_01", "Create Booking with rate plan but not flexi"
             , {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingRatePlan_02", "Booking_02", "Create Booking with flexi rate plan"
             , {"is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingRatePlan_03", "Booking_04", "Create booking of two rooms one of them "
                                                       "with flexi rate plan",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingRatePlan_04", "Booking_96", "Create booking with flexi rate plan with"
                                                       " cancellation_policy 100% non-refundable",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingRatePlan_05", "Booking_97", "Single booking with flexi rate plan and "
                                                       "layered cancellation policy ",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingRatePlan_06", "Booking_98", "Single booking with flexi rate plan and "
                                                       "only payment policy (percent policy) ",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingRatePlan_07", "Booking_105", "Single booking with flexi rate plan and "
                                                        "only payment policy (X days before checkin policy) ",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingRatePlan_08", "Booking_106", "Single Booking with cancellation policy "
                                                        "as non-refundable 100% and payment policy (percent policy)",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingRatePlan_09", "Booking_102", "Single Booking with cancellation policy "
                                                        "as non-refundable 100% and payment policy (X days before checkin policy)",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "",
             "", False, ""),
            ("CreateBookingRatePlan_10", "Booking_103", "Single booking with flexi rate plan with"
                                                        " cancellation policy as layered cancellation policy and payment policy (percent policy)",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200,
             None, "", "", "", "", False, ""),
            ("CreateBookingRatePlan_11", "Booking_104", "Single booking with flexi rate plan with"
                                                        " cancellation policy as layered cancellation policy and payment policy (X days before checkin policy) "
                                                        "and quantity negative for inclusions",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}
             , 400, None, "", "", "", "", False, ""),
            ("CreateBookingKeralaCess_01", "Booking_01", "Create Booking with GSTIN details for Walkin booking",
             {"include_kerala_cess": False, "is_booking_v2": True, "enable_rate_plan": False}, 200, None, "", "", "",
             "", False, ""),
            ("CreateBookingKeralaCess_02", "Booking_03", "Create Booking with GSTIN details for Direct booking",
             {"include_kerala_cess": False, "is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingKeralaCess_03", "Booking_04", "Create Booking with GSTIN details for Ota booking",
             {"include_kerala_cess": False, "is_booking_v2": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingKeralaCess_04", "Booking_59", "Create Booking without GSTIN details for Walkin booking",
             {"include_kerala_cess": True, "is_booking_v2": True}, 200, None, "", "", "", "", True, ""),
            ("CreateBookingKeralaCess_05", "Booking_60", "Create Booking without GSTIN details for Direct booking",
             {"include_kerala_cess": True, "is_booking_v2": True}, 200, None, "", "", "", "", True, ""),
            ("CreateBookingKeralaCess_05", "Booking_61", "Create Booking without GSTIN details for Ota booking",
             {"include_kerala_cess": True, "is_booking_v2": True}, 200, None, "", "", "", "", True, ""),
            ("CreateBookingV2_01", "Booking_01", "Create Booking with GSTIN details for Walkin booking",
             {"is_booking_v2": True, "enable_rate_plan": True, "include_kerala_cess": False,
              "is_inclusion_added": True}, 200, None, "", "", "", "", False, ""),
            ("CreateBookingV2_36", "Booking_96", "Create Booking with GSTIN details for Walkin booking",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingV2_37", "Booking_97", "Create Booking with GSTIN details for Walkin booking",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingV2_38", "Booking_98", "Create Booking with GSTIN details for Walkin booking",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingV2_39", "Booking_102", "Create Booking with GSTIN details for Walkin booking",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingV2_40", "Booking_103", "Create Booking with GSTIN details for Walkin booking",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 200, None, "", "", "", "",
             False, ""),
            ("CreateBookingV2_41", "Booking_104", "Create Booking with GSTIN details for Walkin booking",
             {"is_booking_v2": True, "enable_rate_plan": True, "is_inclusion_added": True}, 400, None, "", "", "", "",
             False, ""),
            ("CreateBookingV2_42", "Booking_127", "Create Booking with wrong GSTIN details for Walkin booking",
             {"is_booking_v2": True, "enable_rate_plan": False, "is_inclusion_added": False}, 200, None, "", "", "", "",
             False, ""),
        ])
    @pytest.mark.regression
    def test_create_v2_booking(self, client_, test_case_id, existing_test_case_id, tc_description, extra_data,
                               status_code, user_type, error_code, error_message, dev_message, error_payload,
                               skip_case, skip_message):

        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if existing_test_case_id:
            test_case_id = existing_test_case_id
        response = self.booking_request.new_booking_request(client_, test_case_id, status_code, user_type,
                                                            hotel_id=hotel_id,
                                                            enable_rate_plan=extra_data['enable_rate_plan']
                                                            if extra_data is not None and 'enable_rate_plan' in extra_data
                                                            else None, is_booking_v2=extra_data['is_booking_v2']
            if extra_data is not None and 'is_booking_v2' in extra_data
            else None, is_inclusion_added=extra_data['is_inclusion_added']
            if extra_data is not None and 'is_inclusion_added' in extra_data else None)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in (200, 201):
            self.validation(client_, response, test_case_id, self.booking_request.bill_id, extra_data, hotel_id)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, dev_message, error_payload, skip_case, skip_message",
        [
            ("Booking_114", "Multiple room booking with charges mixup", 200, SUPER_ADMIN, "", "", "", "", ""),
            ("Booking_115", "Multiple room booking with charges as credit", 200, SUPER_ADMIN, "", "", "", "", ""),
            ("Booking_116", "Multiple room booking with charges as non-credit", 200, SUPER_ADMIN, "", "", "", "", "")
        ])
    @pytest.mark.regression
    def test_create_mix_up_smoke(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                                 dev_message, error_payload, skip_case,
                                 skip_message):
        if skip_case:
            pytest.skip(skip_message)

        response = self.booking_request.new_booking_request(client_, test_case_id, status_code, user_type=user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request.bill_id, extra_data=None,
                            hotel_id=None, validate_billed_entity=True)
        else:
            assert False, "Response status code is not matching"


class TestCreateBookingV2(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, dev_message, error_payload, "
        "skip_case, skip_message, extras",
        [
            ("booking_01", "Create bookings with channel as hotel, subchannel as walkin, single room and pay at chec"
                           "kout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_02", "Create bookings with channel as hotel, subchannel as walkin, company details, single room"
                           " and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, }),
            ("booking_03", "Create bookings with channel as hotel, subchannel as walkin, company details, single room "
                           "and pay after checkout", 403, "", "", "", "", "", "", ""),
            ("booking_04", "Create bookings with channel as hotel, subchannel as walkin, company details, multiple "
                           "room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_05", "Create bookings with channel as hotel, subchannel as walkin, single days, multiple room "
                           "and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_06", "Create bookings with channel as hotel, subchannel as walkin, multiple days, company details"
                           " single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_07", "Create bookings with channel as hotel, subchannel as walkin, multiple days, company details"
                           " single room and pay after checkout", 403, "", "", "", "", "", "", ""),
            ("booking_08", "Create bookings with channel as hotel, subchannel as walkin, multiple days, company details"
                           " multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", True, "", ""),
            ("booking_09", "Create a future bookings with channel as hotel, subchannel as walkin, single days, multiple"
                           " room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_10", "Create a future bookings with channel as hotel, subchannel as walkin, single days, company "
                           "details, single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_11", "Create a future bookings with channel as hotel, subchannel as walkin, single days, company"
                           " details, single room and pay after checkout", 403, "", "", "", "", "", "", ""),
            ("booking_12", "Create a future bookings with channel as hotel, subchannel as walkin, company details,"
                           " multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_13", "Create a future bookings with channel as hotel, subchannel as walkin, single days,"
                           " multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_14", "Create a future bookings with channel as b2b, subchannel as corporates, single days, "
                           "company details, single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_15", "Create a future bookings with channel as hotel, subchannel as walkin, multiple days, "
                           "company details, single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_16", "Create a future bookings with channel as hotel, subchannel as walkin, multiple days, "
                           "company details, single room and pay after checkout ", 403, "", "", "", "", "", "", ""),
            ("booking_17", "Create a future bookings with channel as hotel, subchannel as walkin, multiple days, "
                           "company details, multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_18", "Create bookings with channel as hotel, subchannel as walkin, single days, company details,"
                           " single room and pay at checkout with default billed entity as booker_company", 200,
             SUPER_ADMIN, "", "",
             "", "", "", ""),
            ("booking_19", "Create bookings with channel as hotel, subchannel as walkin, single days, company details,"
                           " multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_20", "Create bookings with channel as hotel, subchannel as walkin, mutliple days, company"
                           " details, multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_21", "Create future bookings with channel as hotel, subchannel as walkin, single days, company"
                           " details, multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_22", "Create future bookings with channel as hotel, subchannel as walkin, mutliple days, company"
                           " details, multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_23", "Create bookings with channel as hotel, subchannel as walkin, single days, company details,"
                           " single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_24", "Create bookings with channel as hotel, subchannel as walkin, multiple days, company"
                           " details, single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_25", "Create future bookings with channel as hotel, subchannel as walkin, single days, company"
                           " details, single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_26", "Create future bookings with channel as hotel, subchannel as walkin, multiple days, company"
                           " details, single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_27", "Create bookings with channel as b2b, subchannel as corporate, single room and pay at "
                           "checkout", 400, "", "", "", "", True, "", ""),
            ("booking_28", "Create bookings with channel as hotel, subchannel as corporate, company details, single"
                           " room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_29", "Create bookings with channel as hotel, subchannel as corporate, company details, single "
                           "room and pay after checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_30", "Create bookings with channel as hotel, subchannel as corporate, company details, multiple"
                           " room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_31", "Create bookings with channel as b2b, subchannel as corporate, multiple days, single "
                           "room and pay at checkout", 400, "", "", "", "", True, "", ""),
            ("booking_32", "Create bookings with channel as hotel, subchannel as corporate, company details, multiple"
                           " days, single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_33", "Create bookings with channel as hotel, subchannel as corporate, company details, multiple"
                           " days, single room and pay after checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_34", "Create bookings with channel as hotel, subchannel as corporate, company details, multiple"
                           " days, multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_35", "Create future bookings with channel as b2b, subchannel as corporate, single room and pay"
                           " at checkout ", 400, "", "", "", "", True, "", ""),
            ("booking_36", "Create future bookings with channel as hotel, subchannel as corporate, company details,"
                           " single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_37", "Create future bookings with channel as hotel, subchannel as corporate, company details,"
                           " single room and pay after checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_38", "Create future bookings with channel as hotel, subchannel as corporate, company details,"
                           " multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_39", "Create future bookings with channel as b2b, subchannel as corporate, multiple days, single"
                           " room and pay at checkout", 400, "", "", "", "", True, "", ""),
            ("booking_40", "Create  future bookings with channel as hotel, subchannel as corporate, company details, "
                           "multiple days, single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_41", "Create future bookings with channel as hotel, subchannel as corporate, company details, "
                           "multiple days, single room and pay after checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_42", "Create future bookings with channel as hotel, subchannel as corporate, company details, "
                           "multiple days, multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_43", "Create bookings with channel as b2b, subchannel as corporate, single days, company details,"
                           " single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_44", "Create bookings with channel as b2b, subchannel as corporate, multiple days, company "
                           "details, single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_45", "Create future bookings with channel as b2b, subchannel as corporate, single days, company"
                           " details, single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_46", "Create future bookings with channel as b2b, subchannel as corporate, multiple days, company"
                           " details, single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_47", "Create bookings with channel as b2b and subchannel as corporate with billed to type as "
                           "guest", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_48", "Create bookings with channel as b2b and subchannel as corporate with billed to type as "
                           "travel agent with pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_49", "Create bookings with channel as b2b and subchannel as corporate with billed to type as "
                           "travel agent with pay after checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_50", "Create bookings with channel as b2b and subchannel as corporate with billed to type as "
                           "booker", 400, "", "", "", "", "", "", ""),
            ("booking_51", "Create bookings with channel as b2b, subchannel as bulk, single room and pay at checkout",
             403, "", "", "", "", "", "", ""),
            ("booking_52", "Create bookings with channel as b2b, subchannel as bulk, company details, single room and"
                           " pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_53", "Create bookings with channel as b2b, subchannel as bulk, company details, single room and"
                           " pay after checkout", 400, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_54", "Create bookings with channel as b2b, subchannel as bulk, company details, multiple room and"
                           " pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_55", "Create bookings with channel as b2b, subchannel as bulk, single days, multiple room and"
                           " pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_56", "Create bookings with channel as b2b, subchannel as bulk, multiple days, company details,"
                           " single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_57", "Create bookings with channel as b2b, subchannel as bulk, multiple days, company details,"
                           " single room and pay after checkout", 400, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_58", "Create bookings with channel as b2b, subchannel as bulk, multiple days, company details,"
                           " multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_59", "Create a future bookings with channel as b2b, subchannel as bulk, single days, multiple "
                           "room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_60", "Create a future bookings with channel as b2b, subchannel as bulk, single days, company"
                           " details, single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_61", "Create a future bookings with channel as b2b, subchannel as bulk, single days, company"
                           " details, single room and pay after checkout", 400, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_62", "Create a future bookings with channel as b2b, subchannel as bulk, company details, "
                           "multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_63", "Create a future bookings with channel as b2b, subchannel as bulk, single days, multiple"
                           " room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_65", "Create a future bookings with channel as b2b, subchannel as bulk, multiple days, company"
                           " details, single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_66", "Create a future bookings with channel as b2b, subchannel as bulk, multiple days, company"
                           " details, single room and pay after checkout ", 400, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_67", "Create a future bookings with channel as b2b, subchannel as bulk, multiple days, company"
                           " details, multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_68", "Create bookings with channel as b2b, subchannel as bulk, single days, company details, "
                           "single room and pay at checkout with default billed entity as booker_company", 200,
             SUPER_ADMIN, "", "",
             "", "", "", ""),
            ("booking_69", "Create bookings with channel as b2b, subchannel as bulk, single days, company details, "
                           "multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_70", "Create bookings with channel as b2b, subchannel as bulk, mutliple days, company details,"
                           " multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "",
             "", ""),
            ("booking_71", "Create future bookings with channel as b2b, subchannel as bulk, single days, company"
                           " details, multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_72", "Create future bookings with channel as b2b, subchannel as bulk, mutliple days, company "
                           "details, multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_73", "Create bookings with channel as b2b, subchannel as bulk, single days, company details, "
                           "single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_74", "Create bookings with channel as b2b, subchannel as bulk, multiple days, company details, "
                           "single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_75", "Create future bookings with channel as b2b, subchannel as bulk, single days, company "
                           "details, single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_76", "Create future bookings with channel as b2b, subchannel as bulk, multiple days, company"
                           " details, single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", ""),
            ("booking_77", "Create bookings with channel as hotel, subchannel as walkin, single room , pay at checkout "
                           "and without rate plan", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_78", "Create bookings with channel as hotel, subchannel as walkin, company details, single room "
                           ", pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking":
                  True}),
            ("booking_79", "Create bookings with channel as hotel, subchannel as walkin, company details, single room"
                           " , pay after checkout and without rate plan", 403, "", "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_80", "Create bookings with channel as hotel, subchannel as walkin, company details, multiple "
                           "room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_81", "Create bookings with channel as hotel, subchannel as walkin, single days, multiple room , "
                           "pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_82", "Create bookings with channel as hotel, subchannel as walkin, multiple days, company details"
                           " single room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_83", "Create bookings with channel as hotel, subchannel as walkin, multiple days, company "
                           "details, single room , pay after checkout and without rate plan", 403, "", "", "", "", "",
             "",
             {"non_rate_plan_booking": True}),
            ("booking_84", "Create bookings with channel as hotel, subchannel as walkin, multiple days, company details"
                           " multiple room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "",
             "",
             {"non_rate_plan_booking": True}),
            ("booking_85", "Create a future bookings with channel as hotel, subchannel as walkin, single days, "
                           "multiple room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "",
             "",
             {"non_rate_plan_booking": True}),
            ("booking_86", "Create a future bookings with channel as hotel, subchannel as walkin, single days, company"
                           " details, single room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "",
             "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_87", "Create a future bookings with channel as hotel, subchannel as walkin, single days, company"
                           " details, single room , pay after checkout and without rate plan", 403, "", "", "", "", "",
             "",
             {"non_rate_plan_booking": True}),
            ("booking_88", "Create a future bookings with channel as hotel, subchannel as walkin, company details,"
                           " multiple room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "", "",
             "",
             {"non_rate_plan_booking": True}),
            ("booking_89", "Create a future bookings with channel as hotel, subchannel as walkin, single days, multiple"
                           " room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "", "", "",
             "", "", {"non_rate_plan_booking": True}),
            ("booking_91", "Create a future bookings with channel as hotel, subchannel as walkin, multiple days, "
                           "company details, single room , pay at checkout and without rate plan", 200, SUPER_ADMIN, "",
             "", "",
             "", "", {"non_rate_plan_booking": True}),
            ("booking_92", "Create a future bookings with channel as hotel, subchannel as walkin, multiple days,"
                           "company details, single room , pay after checkout and without rate plan", 403,
             "", "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_93", "Create a future bookings with channel as hotel, subchannel as walkin, multiple days, "
                           "company details, multiple room , pay at checkout and without rate plan", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_94", "Create bookings with channel as hotel, subchannel as walkin, single days, company details,"
                           " single room , pay at checkout and without rate plan with default billed entity as booker_company",
             200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_95", "Create bookings with channel as hotel, subchannel as walkin, single days, company details,"
                           " multiple room, multiple guest , pay at checkout and without rate plan  ", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_96", "Create bookings with channel as hotel, subchannel as walkin, mutliple days, company"
                           " details, multiple room, multiple guest , pay at checkout and without rate plan  ", 200,
             SUPER_ADMIN,
             "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_97", "Create future bookings with channel as hotel, subchannel as walkin, single days, company"
                           " details, multiple room, multiple guest , pay at checkout and without rate plan ", 200,
             SUPER_ADMIN, "",
             "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_98", "Create future bookings with channel as hotel, subchannel as walkin, mutliple days, company"
                           " details, multiple room, multiple guest , pay at checkout and without rate plan ", 200,
             SUPER_ADMIN, "",
             "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_99", "Create bookings with channel as hotel, subchannel as walkin, single days, company details,"
                           " single room, multiple guest , pay at checkout and without rate plan ", 200, SUPER_ADMIN,
             "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_100", "Create bookings with channel as hotel, subchannel as walkin, multiple days, company"
                            "details, single room, multiple guest , pay at checkout and without rate plan ", 200,
             SUPER_ADMIN, "", "",
             "", "", "", {"non_rate_plan_booking": True}),
            ("booking_101", "Create future bookings with channel as hotel, subchannel as walkin, single days, company"
                            "details, single room, multiple guest , pay at checkout and without rate plan ", 200,
             SUPER_ADMIN, "", "",
             "", "", "", {"non_rate_plan_booking": True}),
            ("booking_102", "Create future bookings with channel as hotel, subchannel as walkin, multiple days, company"
                            " details, single room, multiple guest , pay at checkout and without rate plan ", 200,
             SUPER_ADMIN, "", "",
             "", "", "", {"non_rate_plan_booking": True}),
            ("booking_103", "Create a future bookings with channel as hotel and subchannel as walkin for multiple days"
                            " with single rooms, multiple guests, pay at checkout", 200, SUPER_ADMIN, "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_104", "Create bookings with channel as b2b and subchannel as corporate without company details",
             200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_105", "Create bookings with channel as b2b and subchannel as corporate with company details and "
                            "pay after checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_106", "Create bookings with channel as b2b and subchannel as corporate with company details and "
                            "pay at checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_107", "Create bookings with channel as b2b and subchannel as corporate with company details and "
                            "pay after checkout and multiple room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_108", "Create bookings with channel as b2b and subchannel as corporate for multiple days without "
                            "company details and pay after checkout and single room", 200, SUPER_ADMIN, "", "", "",
             "", "", {"non_rate_plan_booking": True}),
            ("booking_109", "Create bookings with channel as b2b and subchannel as corporate for multiple days with "
                            "company details and pay after checkout and single room", 200, SUPER_ADMIN, "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_110", "Create bookings with channel as b2b and subchannel as corporate for multiple days with "
                            "company details and pay at checkout and single room", 200, SUPER_ADMIN, "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_111", "Create bookings with channel as b2b and subchannel as corporate for multiple days with "
                            "company details and pay at checkout and multiple room", 200, SUPER_ADMIN, "", "", "",
             "", "", {"non_rate_plan_booking": True}),
            ("booking_112", "Create a future bookings with channel as b2b and subchannel as corporate "
             , 200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_113", "Create a future bookings with channel as b2b and subchannel as corporate with company"
                            " details and pay after checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_114", "Create afuture bookings with channel as b2b and subchannel as corporate with company "
                            "details and pay at checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_115", "Create a future bookings with channel as b2b and subchannel as corporate with company"
                            " details and pay after checkout and multiple room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_116",
             "Create a future bookings with channel as b2b and subchannel as corporate for multiple dates", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_117",
             "Create a future bookings with channel as b2b and subchannel as corporate for multiple dates with company"
             " details and pay after checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_118",
             "Create a future bookings with channel as b2b and subchannel as corporate for multiple dates with "
             "company details and pay at checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_119",
             "Create a future bookings with channel as b2b and subchannel as corporate for multiple dates with "
             "company details and pay after checkout and multiple room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_120", "Create bookings with channel as b2b and subchannel as corporate with multiple room and "
                            "multiple guests and pay after checkout and single room", 200, SUPER_ADMIN, "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_121", "Create bookings with channel as b2b and subchannel as corporate fro multiple dates with "
                            "multiple room and multiple guests and pay after checkout and single room", 200,
             SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_122",
             "Create a future bookings with channel as b2b and subchannel as corporate with multiple room and "
             "multiple guests and pay after checkout and single room", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_123",
             "Create a future bookings with channel as b2b and subchannel as corporate fro multiple dates with "
             "multiple room and multiple guests and pay after checkout and single room", 200, SUPER_ADMIN,
             "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_124",
             "Create bookings with channel as b2b and subchannel as corporate with billed to type as guest",
             200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_125",
             "Create bookings with channel as b2b and subchannel as corporate with billed to type astravel agent",
             200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_126",
             "Create bookings with channel as b2b and subchannel as corporate with billed to type as travel agent"
             " with pay after checkout", 400, "", "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_127", "Create bookings with channel as b2b, subchannel as bulk, single room and pay at checkout",
             403, "", "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_128", "Create bookings with channel as b2b, subchannel as bulk, company details, single room and"
                            " pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_129", "Create bookings with channel as b2b, subchannel as bulk, company details, single room and"
                            " pay after checkout", 400, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_130", "Create bookings with channel as b2b, subchannel as bulk, company details, multiple room "
                            "and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_131", "Create bookings with channel as b2b, subchannel as bulk, single days, multiple room and"
                            " pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"non_rate_plan_booking": True}),
            ("booking_132", "Create bookings with channel as b2b, subchannel as bulk, multiple days, company details,"
                            " single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_133", "Create bookings with channel as b2b, subchannel as bulk, multiple days, company details,"
                            " single room and pay after checkout", 400, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_134", "Create bookings with channel as b2b, subchannel as bulk, multiple days, company details,"
                            " multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_135", "Create a future bookings with channel as b2b, subchannel as bulk, single days, multiple "
                            "room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_136", "Create a future bookings with channel as b2b, subchannel as bulk, single days, company"
                            " details, single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_137", "Create a future bookings with channel as b2b, subchannel as bulk, single days, company"
                            " details, single room and pay after checkout", 400, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_138", "Create a future bookings with channel as b2b, subchannel as bulk, company details, "
                            "multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_139", "Create a future bookings with channel as b2b, subchannel as bulk, single days, multiple"
                            " room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_140", "Create a future bookings with channel as b2b, subchannel as bulk, multiple days, company"
                            " details, single room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_141", "Create a future bookings with channel as b2b, subchannel as bulk, multiple days, company"
                            " details, single room and pay after checkout ", 400, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_142", "Create a future bookings with channel as b2b, subchannel as bulk, multiple days, company"
                            " details, multiple room and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_143", "Create bookings with channel as b2b, subchannel as bulk, single days, company details, "
                            "single room and pay at checkout with default billed entity as booker_company", 200,
             SUPER_ADMIN, "", "",
             "", "", "", {"non_rate_plan_booking": True}),
            ("booking_144", "Create bookings with channel as b2b, subchannel as bulk, single days, company details, "
                            "multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_145", "Create bookings with channel as b2b, subchannel as bulk, mutliple days, company details,"
                            " multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "",
             "", {"non_rate_plan_booking": True}),
            ("booking_146", "Create future bookings with channel as b2b, subchannel as bulk, single days, company"
                            " details, multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "",
             "",
             "", "", {"non_rate_plan_booking": True}),
            ("booking_147", "Create future bookings with channel as b2b, subchannel as bulk, mutliple days, company "
                            "details, multiple room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", {"non_rate_plan_booking": True}),
            ("booking_148", "Create bookings with channel as b2b, subchannel as bulk, single days, company details, "
                            "single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_149", "Create bookings with channel as b2b, subchannel as bulk, multiple days, company details, "
                            "single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("booking_150", "Create future bookings with channel as b2b, subchannel as bulk, single days, company "
                            "details, single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", {"non_rate_plan_booking": True}),
            ("booking_151", "Create future bookings with channel as b2b, subchannel as bulk, multiple days, company"
                            " details, single room, multiple guest and pay at checkout ", 200, SUPER_ADMIN, "", "", "",
             "", "", {"non_rate_plan_booking": True}),
            ("booking_152", "Create a booking with channel as b2b and subcahnnel as corporates and with multiple guests"
                            "and multiple days where charge type is non credit", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_153", "Create bookings with channel as b2b and subchannel as corporates for multiple days with"
                            " multiple rooms, multiple guests, pay after checkout", 200, SUPER_ADMIN, "", "", "", "",
             "", ""),
            ("booking_154", "Create bookings with channel as b2b and subchannel as corporates for multiple days with"
                            " multiple rooms, multiple guests, pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_155", "Create bookings with channel as b2b and subchannel as corporates for multiple days with"
                            " multiple rooms, multiple guests, pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            #################### Setting Default Billing Instruction for room and extras TestCase #####################
            ("booking_165", "Create bookings with channel as hotel, subchannel as walkin, single room with extras and "
                            "room charges mapped to booker with pay at checkout", 200, SUPER_ADMIN, "", "", "", "",
             "", ""),
            ("booking_166", "Create bookings with channel as b2b and subchannel as corporate with billed to type as"
                            " guest for room and extras with pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_167", "Create bookings with channel as b2b, subchannel as corporate, single room with room and"
                            " extras mapped to company with pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_168", "Create bookings with channel as b2b and subchannel as corporate with room and extras"
                            " mapped to travel agent with pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_169", "Create bookings with channel as hotel, subchannel as walkin, single room with extras and "
                            "room charges mapped to booker with pay after checkout", 200, SUPER_ADMIN, "", "", "", "",
             "", ""),
            ("booking_170", "Create bookings with channel as b2b and subchannel as corporate with billed to type as"
                            " guest for room and extras with pay after checkout", 400, SUPER_ADMIN, "04010201",
             "The payment instruction can't be pay_after_checkout for given default billed entity category", "", "", "",
             ""),
            ("booking_171", "Create bookings with channel as b2b, subchannel as corporate, single room with room and"
                            " extras mapped to company with pay after checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_172", "Create bookings with channel as b2b and subchannel as corporate with room and extras"
                            " mapped to travel agent with pay after checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             ""),
            ("booking_173", "Provide default_billed_entity_category_for_extras as Invalid enum", 400, SUPER_ADMIN,
             "04010006", "", {"field": "default_billed_entity_category_for_extras"}, False, "", ""),
            ("booking_174", "Provide default_payment_instruction as Invalid enum", 400, SUPER_ADMIN, "04010006",
             "", {"field": "default_payment_instruction"}, False, "", ""),
            ("booking_175", "Provide default_payment_instruction_for_extras as Invalid enum", 400, SUPER_ADMIN,
             "04010006", "", {"field": "default_payment_instruction_for_extras"}, "", "", ""),
            ("booking_176", "Provide default_billed_entity_category as Invalid enum", 400, SUPER_ADMIN, "04010006", "",
             {"field": "default_billed_entity_category"}, "", "", ""),
            ("booking_177", "Provide default_billed_entity_category_for_extras as EMPTY", 400, SUPER_ADMIN, "04010006",
             "", {"field": "default_billed_entity_category_for_extras"}, False, "", ""),
            ("booking_178", "Provide default_payment_instruction as EMPTY", 400, SUPER_ADMIN, "04010006", "",
             {"field": "default_payment_instruction"}, False, "", ""),
            ("booking_179", "Provide default_payment_instruction_for_extras as EMPTY", 400, SUPER_ADMIN, "04010006", "",
             {"field": "default_payment_instruction_for_extras"}, "", "", ""),
            ("booking_180", "Provide default_billed_entity_category as EMPTY", 400, SUPER_ADMIN, "04010006", "",
             {"field": "default_billed_entity_category"}, "", "", ""),
            ("booking_181", "Provide default_billed_entity_category_for_extras as NULL", 200, SUPER_ADMIN, "", "", "",
             False, "", ""),
            ("booking_182", "Provide default_payment_instruction as NULL", 200, SUPER_ADMIN, "", "", "", False, "", ""),
            ("booking_183", "Provide default_payment_instruction_for_extras as NULL", 200, SUPER_ADMIN, "", "", "", "",
             "", ""),
            ("booking_184", "Provide default_billed_entity_category as NULL", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_185", "Provide billing instruction of room and extras different with same payment instruction of"
                            " room and extras", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_186", "Provide billing instruction of room and extras different with different payment"
                            " instruction of room and extras", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_187", "Provide billing instruction of room and extras same with different payment instruction"
                            " of room and extras", 200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_188", "Provide billing instruction of room and extras such that billed entity does not exist",
             400, SUPER_ADMIN, "04010201",
             "For b2b - corporates bookings booker can't be default_billed_entity_category", "", "", "", ""),
            ("booking_189", "Provide same name as of booking owner in walkin booking so that there will be one billed"
                            " entity of booker", 200, SUPER_ADMIN, "", "", True, "Need to check", "", ""),
            ("booking_190", "Create bookings with channel as hotel, subchannel as walkin, single room with extras and"
                            " room charges mapped to booker_company without having company details",
             403, SUPER_ADMIN, "04011068", "", "", "", "", ""),
            ("booking_204", "Create Booking with rate plan which has SKU id not mapped in catalog as well as crs",
             400, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_203", "Create Booking with rate plan which has SKU id mapped in catalog but not in crs",
             200, SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_205", "Create bookings which has clubbed and slab based taxation for room stay", 200,
             SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "has_slab_based_taxation": True,
              "hotel_level_config": [{"config_name": "inclusion_config.club_with_room_rate_for_taxation",
                                      "config_value": "true", "value_type": "boolean"}]}),
            ("booking_206", "Create bookings which has clubbed taxation for room stay", 200, SUPER_ADMIN, "", "", "",
             "", "", {"include_kerala_cess": False, "hotel_level_config": [
                {"config_name": "inclusion_config.club_with_room_rate_for_taxation",
                 "config_value": "true", "value_type": "boolean"}]}),
            ("booking_207", "Create bookings which has slab based taxation for room stay, where room stay charge "
                            "appear in higher tax bracket", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "has_slab_based_taxation": True}),
            ("booking_208", "Create bookings which has slab based taxation for room stay, where room stay charge "
                            "appear in lower tax bracket", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "has_slab_based_taxation": True}),
            ("over_booking_01", "Over Booking where application code is SU for single day booking pay at checkout", 200,
             SUPER_ADMIN, "", "", "", "", "", ""),
            ("over_booking_02", "Don't allow overbooking where application code is B2B", 400, SUPER_ADMIN, "04010602",
             "", "", "", "", ""),
            ("booking_212", "Create bookings with channel as ota, subchannel as mmt, single room and pay at checkout",
             200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_213", "Create bookings with channel as ota, subchannel as corporates, single room and pay at "
                            "checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_214", "Create bookings with channel as ota, subchannel as tmc-makemytrip, single room and pay at "
                            "checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_215", "Create bookings with channel as ota, subchannel as tmc-expedia, single room and pay at "
                            "checkout such that commission is not defined", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, }),
            # ("booking_216", "Create bookings with channel as ota, subchannel as agoda, single room and pay at checkout "
            #                 "with pre commission", 200, SUPER_ADMIN, "", "", "", "", "",
            #  {"include_kerala_cess": False, }),
            ("booking_217", "Create bookings with channel as ta, subchannel as ta-bulk, single room and pay at "
                            "checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_218", "Create bookings with channel as ta, subchannel as local-ta, single room and pay at "
                            "checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_219", "Create bookings with channel as ta, subchannel as b2b-ta, single room and pay at checkout",
             200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_220", "Create bookings with channel as ta, subchannel as ta-commisionable, single room and pay at"
                            " checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_221", "Create bookings with channel as ta, subchannel as tmc, single room and pay at checkout",
             200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_222", "Create bookings with channel as ta, subchannel as tmc-makemytrip, single room and pay at "
                            "checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_223", "Create bookings with channel as ta, subchannel as corporates, single room and pay at "
                            "checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_224", "Create bookings with channel as b2b, subchannel as tmc, single room and pay at checkout "
                            "without company details", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, }),
            ("booking_225", "Create bookings with channel as b2b, subchannel as tmc, single room and pay at checkout "
                            "without travel agent details", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, }),
            ("booking_226", "Create bookings with channel as b2b, subchannel as tmc, single room and pay at checkout",
             200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_227", "Create bookings with channel as b2b, subchannel as tmc-makemytrip, single room and pay at "
                            "checkout without company details", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, }),
            ("booking_228", "Create bookings with channel as b2b, subchannel as tmc-makemytrip, single room and pay at "
                            "checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_229", "Create bookings with channel as b2b, subchannel as corporates, single room and pay at "
                            "checkout without travel agent details", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, }),
            ("booking_230", "Create bookings with channel as b2b, subchannel as corporates, single room and pay at "
                            "checkout without company details", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, }),
            ("booking_231", "Create bookings with channel as b2b, subchannel as corporates, single room and pay at "
                            "checkout with company and travel agent details", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, }),
            ("booking_232", "Create bookings with channel as ota, subchannel as mmt, multiple room and pay at checkout",
             200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_233", "Create bookings with channel as ota, subchannel as mmt, single room, multiple days and "
                            "pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_234", "Create bookings with channel as ota, subchannel as mmt, multiple room, multiple days and "
                            "pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_235", "Create bookings with channel as ota, subchannel as mmt, multiple room, multiple days and "
                            "pay at checkout with diff rate plan price", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, }),
            ("booking_236", "Create future bookings with channel as ota, subchannel as mmt, single room and pay at "
                            "checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_237", "Create future bookings with channel as ota, subchannel as mmt, multiple room and pay at "
                            "checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_238", "Create future bookings with channel as ota, subchannel as mmt, multiple room, multiple "
                            "days and pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, }),
            ("booking_239", "Create bookings with channel as ota, subchannel as mmt, single room, multiple days and "
                            "pay at checkout", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_240", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as cash", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_241", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with full payments as cash", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_242", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with extra payments as cash", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_243", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as treebo_corporate_rewards", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_244", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as paid_by_treebo", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_245", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as hotel_collectible", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_246", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as UPI", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_247", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as razorpay_api", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_248", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as amazon_pay", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_249", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as bank_transfer_hotel", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_250", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as bank_transfer_treebo", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_251", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as debit_card", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_252", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as credit_card", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_253", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as paid_at_ota", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_254", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as phone_pe", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_255", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as other", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_256", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as air_pay", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_257", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as payment_service", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_258", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as payment_link", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_259", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as razorpay_payment_gateway", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_260", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with multiple part payments", 200, SUPER_ADMIN, "", "", "", "", "",
             {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_261", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and record payment with invalid payment method", 400, SUPER_ADMIN, "04010006", "", "", "",
             "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_262", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and record payment with payor entity as primary guest and payment method as cash", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_263", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and record payment with payor entity as company and payment method as cash", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_264", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and record payment with payor entity as travel agent and payment method as cash", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_265", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and record payment with payor entity as primary guest and payment method as amazon_pay",
             200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_266", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and record payment with payor entity as company and payment method as amazon_pay", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_267", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and record payment with payor entity as travel agent and payment method as amazon_pay",
             200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_268", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout,"
                            " future booking and record payment",
             200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_269", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and record payment without payor billed entity id and default BE as Booker", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_270", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and record payment without payor billed entity id and default BE as Company",
             200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_271", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout,"
                            " and record payment without payor billed entity id and default BE as TA",
             200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_293", "Create bookings with channel as b2b and subchannel as corporate with billed to type as "
                            "travel agent and with account details of travel agent with pay at checkout", 200,
             SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_294", "Create bookings with channel as b2b and subchannel as corporate with billed to type as "
                            "booker-company and with account details of company  with pay at checkout", 200,
             SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_295", "Create bookings with channel as b2b and subchannel as corporate with billed to type as "
                            "booker and with account details of travel agent with pay at checkout", 200,
             SUPER_ADMIN, "", "", "", "", "", ""),
            ("booking_301", "Create bookings with channel as hotel, subchannel as walkin, single room, pay at checkout "
                            "and with part payments as razorpay_api and send status as done", 200, SUPER_ADMIN, "", "",
             "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ########################### Rate Plan Restricitons Related Test Case #####################################
            ("restriction_01", "Create bookings with channel as hotel, subchannel as walkin, single room and pay at"
                               "checkout and rate plan having restrictions without abw end", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("restriction_02",
             "Create Booking having rate plan max_abw restrictions as 2 and booking checkin is today+1", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("restriction_03",
             "Create Booking having checkin is today+3 and rate plan max_abw restrictions as 2", 400,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ########################### Rate Plan print/suppress rate Related Test Case ###############################
            ("suppress_rate_booking_01",
             "Create Booking having printRate as True and suppressRate as False", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("suppress_rate_booking_02",
             "Create Booking having printRate as True and suppressRate as True", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("suppress_rate_booking_03",
             "Create Booking having printRate as False and suppressRate as False", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("suppress_rate_booking_04",
             "Create Booking having printRate as False and suppressRate as True", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("suppress_rate_booking_05",
             "Create Multiple room Booking one room having printRate as True suppressRate as False and another room "
             "having printRate as False and suppressRate as True ",
             200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("suppress_rate_booking_06",
             "Create Multiple room Booking one room having printRate as True suppressRate as True and another room "
             "having printRate as False and suppressRate as False",
             200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_01_discount_details", "Create booking with discount details", 200, SUPER_ADMIN, "", "",
             "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
            ("booking_01_empty_discount_details", "Create booking with [] as discount details", 200, SUPER_ADMIN, "",
             "", "", "", "", {"include_kerala_cess": False, "payment_matrix": True}),
        ])
    @pytest.mark.regression
    def test_create_booking(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                            dev_message, error_payload, skip_case, skip_message, extras):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]
        if test_case_id in ('booking_204', 'booking_203'):
            query_execute(db_queries.UPDATE_EXPENSE_ITEM_SEQ)

        if "over_booking" in test_case_id:
            set_inventory_count(0, hotel_id)

        response = self.booking_request.new_booking_request_v2(client_, test_case_id, status_code, user_type, extras)

        if test_case_id in ('booking_204', 'booking_203'):
            query_execute(db_queries.DELETE_EXPENSE_ITEM)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            booking_id = self.booking_request.booking_id
            bill_id = self.booking_request.bill_id
            self.validation(client_, response, test_case_id, self.booking_request, self.billing_request, booking_id,
                            bill_id, hotel_id, user_type, extras)
        else:
            assert False, "Response status code is not matching"

        if "over_booking" in test_case_id:
            set_inventory_count(500, hotel_id)

    @staticmethod
    def validation(client_, response, test_case_id, booking_request, billing_request, booking_id, bill_id,
                   hotel_id, user_type, extra_data):
        validation = ValidationBooking(client_, response, test_case_id, booking_request, billing_request, booking_id,
                                       bill_id, hotel_id, user_type, extra_data)
        validation.validate_response()
        get_booking_response = validation.validate_get_booking()
        validation.validate_get_bill()
        validation.validate_billed_entities()
        validation.compare_get_booking_and_bill_summary()
        validation.validate_proforma_invoice(client_, booking_request, billing_request, bill_id, get_booking_response,
                                             extra_data)
        validation.validate_commissions()


class TestCreateBookingV2ForPG(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, dev_message, error_payload, "
        "skip_case, skip_message, extras",
        [
            ("booking_191", "credit booking single room ,guest with guest details and is_primary as true", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_192", "credit booking single room ,guest with guest details and is_primary as false", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_193", "credit booking single room ,guest without guest details and is_primary as false", 400,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_194", "credit booking single room ,guest without guest details and is_primary as true", 400,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_195", "credit booking single room ,guest with guest details and is_primary as true for b2b "
                            "channel", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_196", "credit booking single room ,guest with guest details and is_primary as false for b2b "
                            "channel", 200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_197", "credit booking single room ,guest with only guest details and is_primary as empty", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_198", "create booking with multiple guest multiple room with both guest as primary", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_199", "create booking with multiple guest single room with guest as primary as true, false", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_200", "create booking with multiple guest single room with guest as primary as false, true", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
            ("booking_201", "create booking with multiple guest single room with guest as primary as false, false", 200,
             SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
        ])
    @pytest.mark.regression
    def test_create_booking_for_pg(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                                   dev_message, error_payload, skip_case, skip_message, extras):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]

        response = self.booking_request.new_booking_request_v2(client_, test_case_id, status_code, user_type, extras)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            booking_id = self.booking_request.booking_id
            bill_id = self.booking_request.bill_id
            self.validation(self, client_, response, test_case_id, self.booking_request, self.billing_request,
                            booking_id,
                            bill_id, hotel_id, user_type, extras)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(self, client_, response, test_case_id, booking_request, billing_request, booking_id, bill_id,
                   hotel_id, user_type, extra_data):
        validation = ValidationBooking(client_, response, test_case_id, booking_request, billing_request, booking_id,
                                       bill_id, hotel_id, user_type, extra_data)
        validation.validate_primary_guest_in_response()
        validation.validate_proforma_invoice(client_, booking_request, billing_request, bill_id,
                                             booking_request.get_booking_request_v2(client_, booking_request.booking_id,
                                                                                    200), extra_data)

    class TestCreateBookingV2ForSegment(BaseTest):
        @pytest.mark.parametrize(
            "test_case_id, tc_description, status_code, user_type, error_code, dev_message, error_payload, "
            "skip_case, skip_message, extras",
            [
                ("segment_01", "Walkin Booking having single room and single day having segment data", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_02", "Single Day Single Room Walkin Booking Having 2 Segment Json", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_03", "Single Day Single Room Walkin Booking Having Segment Data as blank Array", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_04", "Single Day Single Room Walkin booking having segment as null", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_05", "Single Day Single Room Walkin Booking without segment Key", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_06", "Single Day Single Room Walkin Booking without group_name key in Segment", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_07", "Single Day Single Room Walkin Booking with group_name key as Blank in Segment", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_08", "Single Day Single Room Walkin Booking with group_name as null in Segment", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_09", "Single Day Single Room Walkin Booking without name key in Segment", 400,
                 SUPER_ADMIN, "04010006", "", {"field": "segments.0.name"}, "", "", {"include_kerala_cess": False, }),
                ("segment_10", "Single Day Single Room Walkin Booking with name key as Blank in Segment", 400,
                 SUPER_ADMIN, "04010006", "", {"field": "segments.0.name"}, "", "", {"include_kerala_cess": False, }),
                ("segment_11", "Single Day Single Room Walkin Booking with name key as Null", 400,
                 SUPER_ADMIN, "04010006", "", {"field": "segments.0.name"}, "", "", {"include_kerala_cess": False, }),
                ("segment_12", "Single Day Single Room Walkin Booking without value key ", 400,
                 SUPER_ADMIN, "04010006", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_13", "Single Day Single Room Walkin Booking with value as blank json", 400,
                 SUPER_ADMIN, "04010006", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_14", "Single Day Single Room Walkin Booking with value as Null", 400,
                 SUPER_ADMIN, "04010006", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_15", "Single Day Single Room B2B Booking having segments as Null", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_16", "Single Day Single Room B2B Booking having segments data", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_17", "Single Day Single Room TA Booking having segments data ", 200,
                 SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, }),
                ("segment_18", "Single Day Single Room without code inside value key in segment", 400,
                 SUPER_ADMIN, "04010006", "", {"field": "segments.0.value.code"}, "", "",
                 {"include_kerala_cess": False, }),
                ("segment_19", "Single Day Single Room without name key inside the value key in segments", 400,
                 SUPER_ADMIN, "04010006", "", {"field": "segments.0.value.name"}, "", "",
                 {"include_kerala_cess": False, }),
                ("segment_20", "Single Day Single Room Walkin Booking having code as null inside value key in segment",
                 400, SUPER_ADMIN, "04010006", "", {"field": "segments.0.value.code"}, "", "",
                 {"include_kerala_cess": False}),
                ("segment_21", "Single Day Single Room Walking Booking having name as null inside value key in segment",
                 400, SUPER_ADMIN, "04010006", "", {"field": "segments.0.value.name"}, "", "",
                 {"include_kerala_cess": False, }),

            ])
        @pytest.mark.regression
        def test_create_booking_for_pg(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                                       dev_message, error_payload, skip_case, skip_message, extras):
            if skip_case:
                pytest.skip(skip_message)

            hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]

            response = self.booking_request.new_booking_request_v2(client_, test_case_id, status_code, user_type,
                                                                   extras)

            if status_code in ERROR_CODES:
                self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                       test_case_id)
            elif status_code in SUCCESS_CODES:
                booking_id = self.booking_request.booking_id
                bill_id = self.booking_request.bill_id
                self.validation(self, client_, response, test_case_id, self.booking_request, self.billing_request,
                                booking_id,
                                bill_id, hotel_id, user_type, extras)
            else:
                assert False, "Response status code is not matching"

        @staticmethod
        def validation(self, client_, response, test_case_id, booking_request, billing_request, booking_id, bill_id,
                       hotel_id, user_type, extra_data):
            validation = ValidationBooking(client_, response, test_case_id, booking_request, billing_request,
                                           booking_id,
                                           bill_id, hotel_id, user_type, extra_data)
            validation.validate_response()
            get_booking_response = validation.validate_get_booking()
            validation.validate_get_bill()
            validation.validate_billed_entities()
            validation.compare_get_booking_and_bill_summary()
            validation.validate_proforma_invoice(client_, booking_request, billing_request, bill_id,
                                                 get_booking_response, extra_data)

    class TestCreateBookingV2ForDE(BaseTest):
        @pytest.mark.parametrize(
            "test_case_id, tc_description, status_code, user_type, error_code, dev_message, error_payload, "
            "skip_case, skip_message, extras",
            [("booking_01_default_entity",
              "Default as booker_company with is_sez and has_lut as true and with booker and "
              "no TA", 200, SUPER_ADMIN, "", "", "", "", "", [True, True]),
             ("booking_02_default_entity",
              "Default as booker_company with is_sez and has_lut as false and with booker(F)"
              "no TA", 200, SUPER_ADMIN, "", "", "", "", "", [False, False]),
             ("booking_03_default_entity",
              "Default as booker_company with is_sez and has_lut as false and with booker(T)"
              "no TA", 200, SUPER_ADMIN, "", "", "", "", "", [False, False]),
             ("booking_04_default_entity", "Default as booker_company with is_sez and has_lut as true and with "
                                           "booker(T) and TA with is_sez and has_lut as true", 200, SUPER_ADMIN, "", "",
              "", "", "", [True, True]),
             ("booking_05_default_entity", "Default as travel_agent with is_sez and has_lut as false and with booker(T)"
                                           "and booker company with  with is_sez and has_lut as true", 200, SUPER_ADMIN,
              "", "", "", "", "", [False, False]),
             ("booking_06_default_entity", "Default as travel_agent with is_sez and has_lut as true and with booker(T)"
                                           "and booker company with  with is_sez and has_lut as true", 200, SUPER_ADMIN,
              "", "", "", "", "", [True, True]),
             ("booking_07_default_entity", "default as travel agent without travel agent details"
                                           "", 400, SUPER_ADMIN, "", "", "", "", "", ""),
             ("booking_08_default_entity", "default as booker but with company_details as true", 200, SUPER_ADMIN, "",
              "", "", "", "", [True, True]),
             ("booking_09_default_entity", "default as booker but with company_details as true", 200, SUPER_ADMIN, "",
              "", "", "", "", [False, False]),
             ])
        @pytest.mark.regression
        def test_create_booking(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                                dev_message, error_payload, skip_case, skip_message, extras):

            if skip_case:
                pytest.skip(skip_message)

            response = self.booking_request.new_booking_request_v2(client_, test_case_id, status_code, user_type,
                                                                   extras)

            if status_code in ERROR_CODES:
                self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                       test_case_id)
            elif status_code in SUCCESS_CODES:
                booking_id = self.booking_request.booking_id
                bill_id = self.booking_request.bill_id
                self.validation(self, client_, response, test_case_id, self.booking_request, self.billing_request,
                                booking_id, bill_id, user_type, extras)
            else:
                assert False, "Response status code is not matching"

        @staticmethod
        def validation(self, client_, response, test_case_id, booking_request, billing_request,
                       booking_id, bill_id, user_type, extra_data):
            validation = ValidationBooking(client_, response, test_case_id, booking_request, billing_request,
                                           booking_id, bill_id, user_type, extra_data)
            validation.validate_default_billed_category(extra_data)

    class TestCreateBookingTaxOnBasisOfBE(BaseTest):
        @pytest.mark.parametrize(
            "test_case_id, tc_description, status_code, user_type, error_code, dev_message, error_payload, skip_case, "
            "skip_message, extras",
            [("booking_273", "Create normal walk in booking", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_274", "Create bookings with sez and lut as false", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_275", "Create bookings with sez as true and lut as false", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_276", "Create bookings with sez and lut as true", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_277", "Create bookings with sez as false and lut as true", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_278", "Create walk in bookings with company details having sez as true and lut as false with "
                             "default BE as guest", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_279", "Create b2b bookings with company details having sez as true and lut as false with default"
                             " BE as guest", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_280", "Create b2b bookings with company details having sez as true and lut as false and TA "
                             "details with sez and lut as false default BE as company", 200, SUPER_ADMIN, "", "", "",
              "", "", {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_281", "Create b2b bookings with company details having sez as true and lut as false and TA "
                             "details with sez and lut as true default BE as TA", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_282", "Create b2b bookings with company details having sez as true and lut as false and TA "
                             "details with sez and lut as false default BE as guest", 200, SUPER_ADMIN, "", "", "", "",
              "", {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_283", "Create b2b bookings with TA details having sez as true and lut as false default BE as TA",
              200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_284", "Create b2b bookings with TA details having sez as false and lut as true default BE as TA",
              200, SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_285", "Create ta booking with TA details having sez and lut as true default BE as TA", 200,
              SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_286", "Create ota booking with TA details having sez and lut as false default BE as TA", 200,
              SUPER_ADMIN, "", "", "", "", "", {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_287", "Create b2b bookings with TA details having sez as true and lut as false default BE as "
                             "guest", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             # ("booking_288", "Create ota booking with TA details having sez as true and lut as false default BE as "
             #                 "booker", 200, SUPER_ADMIN, "", "", "", "", "",
             #  {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_289", "Create b2b multiple day bookings with company details having sez as true and lut as false"
                             " default BE as guest", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_290", "Create b2b multiple room bookings with company details having sez as true and lut as "
                             "false default BE as guest", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_291", "Create b2b multiple guest booking with company details having sez as true and lut as "
                             "false default BE as guest", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ("booking_292", "Create b2b future booking with company details having sez as true and lut as false "
                             "default BE as guest", 200, SUPER_ADMIN, "", "", "", "", "",
              {"include_kerala_cess": False, "new_tax_mocker": True}),
             ])
        @pytest.mark.regression
        def test_create_booking_tax_on_basis_of_be(self, client_, test_case_id, tc_description, status_code, user_type,
                                                   error_code, dev_message, error_payload, skip_case, skip_message,
                                                   extras):
            if skip_case:
                pytest.skip(skip_message)

            hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]

            response = self.booking_request.new_booking_request_v2(client_, test_case_id, status_code, user_type,
                                                                   extras)

            if status_code in ERROR_CODES:
                self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                       test_case_id)
            elif status_code in SUCCESS_CODES:
                booking_id = self.booking_request.booking_id
                bill_id = self.booking_request.bill_id
                self.validation(client_, response, test_case_id, self.booking_request, self.billing_request, booking_id,
                                bill_id, hotel_id, user_type, extras)
            else:
                assert False, "Response status code is not matching"

        @staticmethod
        def validation(client_, response, test_case_id, booking_request, billing_request, booking_id, bill_id,
                       hotel_id, user_type, extra_data):
            validation = ValidationBooking(client_, response, test_case_id, booking_request, billing_request,
                                           booking_id,
                                           bill_id, hotel_id, user_type, extra_data)
            validation.validate_response()
            get_booking_response = validation.validate_get_booking()
            validation.validate_charges()
            validation.validate_billed_entities()
            validation.compare_get_booking_and_bill_summary()
            validation.validate_proforma_invoice(client_, booking_request, billing_request, bill_id,
                                                 get_booking_response,
                                                 extra_data)
            validation.validate_commissions()
