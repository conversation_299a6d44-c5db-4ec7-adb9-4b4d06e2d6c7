import pytest

from prometheus.integration_tests.tests.before_test_actions import Modify_Locked_Invoice_01
from prometheus.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES
from prometheus.integration_tests.config.common_config import HOTEL_ID
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.booking.validations.validation_invoice_account import ValidationInvoiceAccount


class TestInvoiceAccount(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, "
        "error_message, dev_message, error_payload, skip_message",
        [
            ("InvoiceAccount_01", [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': 'Create_Expense_14', 'type': 'expense'}],
             "Invoice an account before checkin and without payment", 400, 'super-admin', "", "", "", "", ""),
            ("InvoiceAccount_02", [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': 'checkinPost_01', 'type': 'check_in'},
                                   {'id': 'Create_Expense_14', 'type': 'expense'}],
             "Invoice an account after checkin without payment", 400, 'super-admin', "", "", "", "", ""),
            ("InvoiceAccount_03", [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': 'checkinPost_01', 'type': 'check_in'},
                                   {'id': 'Create_Expense_14', 'type': 'expense'}],
             "Invoice a wrong account", 400, 'super-admin', "", "", "", "", ""),
            ("InvoiceAccount_04", [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': 'AddPayment_35', 'type': 'add_payment', 'is_mock_rule_req': True},
                                   {'id': 'Create_Expense_14', 'type': 'expense'}],
             "Invoice an account before checkin and with payment", 400, 'super-admin', "********",
             "Cannot post this charge as no guest has checked-in in this room yet.", "", "", ""),
            ("InvoiceAccount_05", [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': 'checkinPost_01', 'type': 'check_in'},
                                   {'id': 'AddPayment_35', 'type': 'add_payment', 'is_mock_rule_req': True},
                                   {'id': 'Create_Expense_14', 'type': 'expense'}],
             "Invoice an account before checkin and with payment", 201, 'super-admin', "", "", "", "", ""),
            ("InvoiceAccount_06", [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': 'checkinPost_01', 'type': 'check_in'},
                                   {'id': 'AddPayment_35', 'type': 'add_payment', 'is_mock_rule_req': True},
                                   {'id': 'Create_Expense_14', 'type': 'expense'},
                                   {'id': "Edit_Charge_06", 'type': 'edit_charge', 'charge_id': 2,
                                    'is_mock_rule_req': True}],
             "Invoice an account before checkin and with payment", 201, 'super-admin', "", "", "", "", ""),
            ("InvoiceAccount_07", [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': 'checkinPost_01', 'type': 'check_in'},
                                   {'id': 'AddPayment_35', 'type': 'add_payment', 'is_mock_rule_req': True},
                                   {'id': 'Create_Expense_14', 'type': 'expense'}],
             "Invoice an account of wrong billed_entity_id", 400, 'super-admin', "", "", "", "", ""),
            ("InvoiceAccount_08", [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': 'checkinPost_01', 'type': 'check_in'},
                                   {'id': 'AddPayment_36', 'type': 'add_payment', 'is_mock_rule_req': True},
                                   {'id': 'Create_Expense_14', 'type': 'expense'},
                                   {'id': 'Create_Expense_15', 'type': 'expense'}],
             "Invoice an account of wrong billed_entity_id", 400, 'super-admin', "", "", "", "", ""),
            ("InvoiceAccount_09", [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': 'checkinPost_01', 'type': 'check_in'},
                                   {'id': 'AddPayment_37', 'type': 'add_payment', 'is_mock_rule_req': True}],
             "Invoice an account before checkin and with payment", 400, 'super-admin', "", "", "", "", ""),
        ])
    @pytest.mark.regression
    def test_invoice_account(self, client_, test_case_id, previous_actions, tc_description, status_code,
                             user_type, error_code, error_message, dev_message, error_payload, skip_message):
        if skip_message:
            pytest.skip(skip_message)
        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]
        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.invoice_account_request.invoice_account(client_, test_case_id, status_code,
                                                                self.booking_request.bill_id, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.invoice_account_request, self.booking_request,
                            self.billing_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, invoice_account_request, booking_request, billing_request):
        validation = ValidationInvoiceAccount(client, response, test_case_id, invoice_account_request, booking_request,
                                              billing_request)
        validation.validate_response()
        if test_case_id == 'InvoiceAccount_10':
            validation.validate_external_reference_id()