import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.booking.validations.validation_abort_checkout import ValidationAbortCheckout
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestAbortCheckout(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, extras", [
            # --------------------------------- Field Related Negative Cases -----------------------------------#
            ("Abort_Checkout_01", 'Provide Invoice group id as NULL',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, None, "04010006",
             "[Booking Invoice Group Id] -> Field may not be null.", "", {"field": "booking_invoice_group_id"},
             False, "", [False, ""]),
            ("Abort_Checkout_02", 'Provide Invoice group id as empty',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, None, "04010007",
             "Aggregate: BookingInvoiceGroupAggregate with id:  missing.", "", "", False, "", [False, ""]),
            ("Abort_Checkout_03", 'Provide Invoice group id as invalid',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, None, "04010007",
             "Aggregate: BookingInvoiceGroupAggregate with id: INVALID missing.", "", "", False, "", [False, ""]),
            ("Abort_Checkout_04", 'Delete Invoice group id key',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, None, "04010007",
             "Aggregate: BookingInvoiceGroupAggregate with id: None missing.", "", "", False, "", [False, ""]),
            ("Abort_Checkout_05", 'Abort after generating invoice',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'invoicePreview_abort_05', 'type': 'preview_invoice'}],
             404, None, "04010007", "Aggregate: BookingInvoiceGroupAggregate with id: None missing.", "", "", False,
             "", [False, ""]),
            # ------------------------------------------ Positive Cases --------------------------------------------#
            ("Abort_Checkout_06", 'Abort after generating invoice with booked charges to post',
             [{'id': "booking_abort_06", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
              {'id': 'Create_Expense_01_early_checkout', 'type': 'expense'}], 200, None, "", "", "", "", False, "",
             [True, {6}]),
            ("Abort_Checkout_07", 'Abort after generating invoice and redistribute payment',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_Redistribute_15_1', 'type': 'add_payment_v2'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'Create_Expense_01_early_checkout', 'type': 'expense'}], 200, None, "", "", "", "", False, "",
             [True, {6}]),
            ("Abort_Checkout_08", 'Abort after generating invoice and redistribute payment',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_Redistribute_15_1', 'type': 'add_payment_v2'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_abort_07', 'type': 'add_payment_v2'},
              {'id': 'Create_Expense_01_early_checkout', 'type': 'expense'}], 200, None, "", "", "", "", False, "",
             [True, {6}]),
            ("Abort_Checkout_09", 'Abort after generating invoice, redistribute payment and refund surplus',
             [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'}, {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
              {'id': 'AddPaymentV2_Redistribute_10', 'type': 'add_payment_v2'},
              {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
              {'id': 'AddPaymentV2_abort_08', 'type': 'add_payment_v2'},
              {'id': 'Create_Expense_01_early_checkout', 'type': 'expense'}], 200, None, "", "", "", "", False, "",
             [True, {6}]),
        ])
    @pytest.mark.regression
    def test_abort_checkout(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                            error_code, error_message, dev_message, error_payload, skip_case, skip_message, extras):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]
        self.booking_request.invoice_group_id = None

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        early_checkout_charge, checkout_charge_id = extras[0], extras[1]
        if early_checkout_charge:
            query_execute(db_queries.ADD_CHARGE_IN_BOOKING_INVOICE_GROUP.format(checkout_charge_id=checkout_charge_id))

        response = self.booking_request.abort_checkout_request(client_, self.booking_request.booking_id, test_case_id,
                                                               status_code, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request, booking_id, billing_request, bill_id):
        validation = ValidationAbortCheckout(client, response, test_case_id, booking_request, booking_id,
                                             billing_request, bill_id)
        validation.validate_response()
