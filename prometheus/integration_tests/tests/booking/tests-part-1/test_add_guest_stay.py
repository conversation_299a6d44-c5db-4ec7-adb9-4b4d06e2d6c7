import pytest

from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_add_guest_stay import ValidationAddGuestStay
from prometheus.integration_tests.config.common_config import *


class TestAddGuestStay(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [
            ("AddGuest_01",
             'Add a single guest to a room with guest details with same checkin/checkout dates as booking dates',
             SINGLE_BOOKING_01, 201, None, "", "", "", "", False, ""),
            ("AddGuest_02", 'Guest with different checkin date as booking', SINGLE_BOOKING_02, 400, None, "", "",
             "", "", False, ""),
            ("AddGuest_03", 'Guest with different checkout date as booking', SINGLE_BOOKING_02, 201, None, "", "", "",
             "", False, ""),
            ("AddGuest_06", 'Guest without checkout date key', SINGLE_BOOKING_01, 201, None, "", "", "", "", False, ""),
            ("AddGuest_07", 'Guest without CI CO dates_2 days booking', SINGLE_BOOKING_02, 400, None, "", "", "", "",
             False, ""),
            ("AddGuest_09", 'Guest with unaccepted age group', SINGLE_BOOKING_01, 400, None, "", "", "", "", False, ""),
            ("AddGuest_10", 'Guest with different checkin date as booking_outside booking', SINGLE_BOOKING_01,
             400, None, "", "", "", "", False, ""),
            ("AddGuest_11", 'Guest with different checkout date as booking_outside booking', SINGLE_BOOKING_01, 400,
             None, "", "", "", "", False, ""),
            ("AddGuest_12", 'Guest with incorrect checkin/checkout date format', SINGLE_BOOKING_01, 400, None, "", "",
             "", "", False, ""),
            ("AddGuest_14", 'Guest with charges for fewer stay dates', SINGLE_BOOKING_02, 400, None, "", "", "", "",
             False, ""),
            ("AddGuest_15", 'Guest with no charges_Zero prices', SINGLE_BOOKING_01, 201, None, "", "", "", "", True,
             "Gives 500"),
            ("AddGuest_16", 'Guest with no charges_ Null values', SINGLE_BOOKING_01, 400, None, "", "", "", "", False,
             ""),
            ("AddGuest_17", 'Guest in booking with charges outside booking', SINGLE_BOOKING_01, 201, None, "", "", "",
             "", False, ""),
            ("AddGuest_18", 'Add guest in a future booking', [{'id': "Booking_27", 'type': 'booking'}], 201, None, "",
             "", "", "", False, ""),
            ("AddGuest_19", 'Add guest to a booking having max guest already',
             [{'id': "Booking_cancelAction_01", 'type': 'booking'}], 400, None, "", "", "", "", False, ""),
            ("AddGuest_20", 'Guest without age group key', SINGLE_BOOKING_02, 400, None, "", "", "", "", False, ""),
            ("AddGuest_21", 'Add guest stay for a guest when booking & room in checkin state',
             SINGLE_BOOKING_CHECK_IN_01, 201, None, "", "", "", "", False, ""),
            ("AddGuest_22", 'Add guest for a part_checked_in room of a part_checked_in single room booking',
             [{'id': "Booking_32", 'type': 'booking'}, {'id': "checkinPost_02", 'type': 'check_in'}], 201, None, "", "",
             "", "", False, ""),
            ("AddGuest_23", 'Add guest for a checked_in room of a part_checked_in single room booking',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_07", 'type': 'check_in'}], 201, None, "", "",
             "", "", False, ""),
            ("AddGuest_24", 'Add guestStay for a cancelled room',
             [{'id': "Booking_cancelAction_02", 'type': 'booking'}, {'id': "CancelAction_06", 'type': 'cancel'}], 400,
             None, "", "", "", "", False, ""),
            ("AddGuest_25", 'Add guestStay for a cancelled booking',
             [{'id': "Booking_cancelAction_01", 'type': 'booking'}, {'id': "CancelAction_14", 'type': 'cancel'}], 400,
             None, "", "", "", "", False, ""),
            ("AddGuest_26", 'Add guestStay for a noshow room',
             [{'id': "Booking_noshowAction_02", 'type': 'booking'}, {'id': "noshowAction_03", 'type': 'noshow'}], 400,
             None, "", "", "", "", True, "Please send prices only for the room stay dates where occupancy or room type "
                                         "has changed, and existing charges hasn't been consumed"),
            ("AddGuest_27", 'Add guestStay for a noshow booking',
             [{'id': "Booking_noshowAction_01", 'type': 'booking'}, {'id': "noshowAction_12", 'type': 'noshow'}], 400,
             None, "", "", "", "", True,
             "Cannot mark booking noshow before midnight of checkin date. Please cancel if necessary"),
            ("AddGuest_29", 'Add GuestStay when the room is in checkout state and booking in part_checked_out',
             [{'id': "Booking_01", 'type': 'booking'}, {'id': "checkinPost_01", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreview_01", 'type': 'preview_invoice'},
              {'id': "AddPayment_03", 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': "checkoutAction_01", 'type': 'checkout'}], 400, None,
             "", "", "", "", False, ""),
            ("AddGuest_30", 'Add a guest to checkedout state booking',
             [{'id': "Booking_41_FullPayment_singleRoom", 'type': 'booking'},
              {'id': "checkinBookingStatus_37", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreview_08", 'type': 'preview_invoice'},
              {'id': "checkoutAction_08", 'type': 'checkout'}], 400, None, "", "", "", "", False, ""),
            ("AddGuest_31", 'Add a guest to a room having max occupency',
             [{'id': "Booking_cancelAction_01", 'type': 'booking'}], 400, None, "", "", "", "", False, ""),
            ("AddGuest_32",
             'Add a Guest to a room where one or more guests has checkedout and occupency goes below max occupency',
             [{'id': "Booking_42_FullPayment_multipleRooms", 'type': 'booking'},
              {'id': "checkinBookingStatus_38", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreview_08_CO", 'type': 'preview_invoice'},
              {'id': "checkoutAction_13", 'type': 'checkout'}], 400, None, "", "", "", "", True,
             "Please send prices only for the room stay dates where occupancy or room type has changed, and "
             "existing charges hasn't been consumed"),
            ("AddGuest_33",
             'Add a Guest to a room where one or more guests has cancelled and occupency goes below max occupency',
             [{'id': "Booking_cancelAction_01", 'type': 'booking'}, {'id': "CancelAction_01", 'type': 'cancel'}], 201,
             None, "", "", "", "", False, ""),
            ("AddGuest_34",
             'Add a Guest to a room where one or more guests has marked noshow and occupency goes below max occupency',
             [{'id': "Booking_noshowAction_02", 'type': 'booking'}, {'id': "noshowAction_03", 'type': 'noshow'}], 400,
             None, "", "", "", "", True,
             "Please send prices only for the room stay dates where occupancy or room type has"
             " changed, and existing charges hasn't been consumed"),
            ("AddGuest_emptyString", 'Add guest with key values as empty string', SINGLE_BOOKING_01, 400, None, "", "",
             "", "", False, ""),
            ("AddGuest_whitespaces", 'Add guest with key values as whitespaces', SINGLE_BOOKING_01, 400, None, "", "",
             "", "", False, ""),
            ("AddGuest_addChild", 'Add a child in the room with same checkin/checkout dates as booking dates',
             SINGLE_BOOKING_01, 201, None, "", "", "", "", False, ""),
            ("AddGuest_randomCICOtime", 'Adding a guest by passing random CI/CO and applicable price date',
             SINGLE_BOOKING_01, 201, None, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_add_guest_stay(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                            error_code, error_message, dev_message, error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.booking_request.add_guest_stay(client_, test_case_id,
                                                       status_code, self.booking_request.booking_id, user_type)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request.booking_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_id):
        validation = ValidationAddGuestStay(client_, test_case_id, response, booking_id)
        validation.validate_response()

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("AddGuest_36", "Adding a guest as booker", SINGLE_BOOKING_01, 201, None, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_add_guest_as_booker(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                 user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                 skip_message):
        if skip_case:
            pytest.skip(skip_message)
        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.booking_request.add_guest_stay(client_, test_case_id,
                                                       status_code, self.booking_request.booking_id, user_type,
                                                       is_booker=True)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request.booking_id)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("AddGuest_01_FDM", "Adding a guest by FDM for a b2b future booking",
             [{'id': 'Booking_11', 'type': 'booking'}], 403, FDM, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_add_guest_stay_policy(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                   user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                   skip_message):
        if skip_case:
            pytest.skip(skip_message)
        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.booking_request.add_guest_stay(client_, test_case_id,
                                                       status_code, self.booking_request.booking_id, user_type)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request.booking_id)
        else:
            assert False, "Response status code is not matching"
