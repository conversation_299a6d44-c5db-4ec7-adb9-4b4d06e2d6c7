import pytest

from pos.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_update_room_stay import ValidationUpdateRoomStay
from prometheus.integration_tests.config.error_messages import *
from prometheus.integration_tests.config.common_config import HOTEL_ID
from prometheus.integration_tests.utilities.common_utils import set_inventory_count


class TestAssignRoom(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, "
        "error_message, dev_message, error_payload, skip_message",
        [
            ("UpdateRoomStay_01_FutureSingleRoomBooking", ASSIGN_ROOM_01,
             "Assign Room To Future Single Room Booking", 200, 'fdm', "", "", "", "", ""),
            ("UpdateRoomStay_02_FutureMultipleRoomBooking", ASSIGN_ROOM_02,
             "Assign One Room To Future Multiple Room Booking", 200, 'fdm', "", "", "", "", ""),
            ("UpdateRoomStay_03_FutureReservedBooking", ASSING_ROOM_03,
             "Assign Room To Future Reserved Booking", 200, 'fdm', "", "", "", "", ""),
            ("UpdateRoomStay_04_AssignAlreadyBookedRoom", ASSING_ROOM_04,
             "Assign Already Assigned Room To Future Confirmed Booking", 200, 'fdm', "", "", "", "", ""),
            ("UpdateRoomStay_06_AssignRoomAfterAvailable", ASSING_ROOM_05,
             "Assign Room After room got available(after booking got checkout then assign same room to some other booking)",
             200, 'fdm', "", "", "", "", "Skipping this test case as its not cleared from dev prospective"),
            ("UpdateRoomStay_07_AssignDnrRoom", ASSIGN_ROOM_07, "Assign a DNR marked room to future confirmed booking)"
             , 400, 'fdm', "04010603", DNR_MARKED_ROOM_ASSIGN, "", "", "Able to assign DNR room"),
            ("UpdateRoomStay_08_AssignCheckinRoomToFuture", ASSIGN_ROOM_08,
             "CheckIn a Room and Allot same room to future booking", 200, 'fdm', "", "", "", "", ""),
            ("UpdateRoomStay_11_DisallowChargeAddition", SINGLE_BOOKING_01,
             "disallow charge addition for a booking with a single room stay", 200, 'fdm', "", "", "", "",
             ""),
            ("UpdateRoomStay_12_DisallowChargeAddition", DISALLOW_CHARGE_ADDITION_01,
             "allow charge addition for a booking with a single room stay", 200, 'fdm', "",
             "", "", "", ""),
        ])
    @pytest.mark.regression
    def test_assign_room(self, client_, test_case_id, previous_actions, tc_description, status_code, user_type,
                         error_code, error_message, dev_message, error_payload, skip_message):
        if skip_message:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        set_inventory_count(500, hotel_id)
        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.booking_request.update_room_stay(client_, test_case_id=test_case_id, status_code=status_code,
                                                         user_type=user_type,
                                                         booking_id=self.booking_request.booking_id)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, message=error_message,
                                                    dev_message=dev_message, extra_payload=error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request.booking_id, self.booking_request,
                            hotel_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, booking_id, booking_request, hotel_id):
        validation = ValidationUpdateRoomStay(client_, response=response, test_case_id=test_case_id, hotel_id=hotel_id)
        validation.validate_response(booking_request=booking_request, booking_id=booking_id)

    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, "
        "error_message, dev_message, error_payload, skip_message",
        [
            ("UpdateRoomStay_01", [{'id': 'Booking_27', 'type': 'booking'}],
             "increase roomStay by reducing the current checkin_date and provide only checkin date", 200, None, "",
             "", "", "", ""),
            ("UpdateRoomStay_02", [{'id': 'Booking_27', 'type': 'booking'}],
             "increase roomStay by increasing the current checkout date and provide only checkout date", 200, None, "",
             "", "", "", ""),
            ("UpdateRoomStay_04", [{'id': 'Booking_27', 'type': 'booking'}],
             "reduce roomStay by decreasing the current checkout date", 200, None, "", "", "", "", ""),
            ("UpdateRoomStay_05", [{'id': 'Booking_27', 'type': 'booking'}],
             "increase roomStay by decreasing checkin date and increasing checkout date", 200, None, "", "", "", "",
             ""),
            ("UpdateRoomStay_07", [{'id': 'Booking_guestsDiff_CI_CO', 'type': 'booking'}],
             "Update roomStay for booking where guest stay have diff checkin checkout dates for both sides", 400, None,
             "04010125", "Room stay dates and guest stay dates don't match. Please change guest stay dates one by one.",
             "Some guest stays have checkin date different from room stay checkin date. Cannot change room stay checkin"
             " date in such scenario.", "", ""),
            ("UpdateRoomStay_12", SINGLE_BOOKING_02, "upgrade room_type_id as well as reduce booking duration", 400,
             None, "04010101", "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices only for the room stay dates where occupancy or room type has changed, and existing "
             "charges hasn't been consumed", "", ""),
            ("UpdateRoomStay_13", [{'id': 'Booking_27', 'type': 'booking'}],
             "Update the room_stay by giving changing only checkin date, and dont give prices. Shall take checkout "
             "automatically", 400, None, "04010101", "Something went wrong. Please contact Escalations team with the "
                                                     "booking id.",
             "Please send prices for all the room stay dates where occupancy or room type has changed", "", ""),
            ("UpdateRoomStay_14", SINGLE_BOOKING_01, "Update RoomStay for 2 days but pass prices for only 1", 400,
             None, "04010101", "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices for all the room stay dates where occupancy or room type has changed", "", ""),
            ("UpdateRoomStay_16", SINGLE_BOOKING_01, "Update roomStay for same checkin checkout dates", 400, None,
             "04010006", "[Checkout Date] -> Checkout date should be greater than checkin date", "", "", ""),
            ("UpdateRoomStay_17", SINGLE_BOOKING_01, "Update roomStay for lower checkout date then checkin", 400, None,
             "04010006", "[Checkout Date] -> Checkout date should be greater than checkin date", "", "", ""),
            ("UpdateRoomStay_18", SINGLE_BOOKING_01, "Update RoomStay dates outside booking dates_before checkin", 200,
             None, "", "", "", "", ""),
            ("UpdateRoomStay_19", SINGLE_BOOKING_01, "Update RoomStay dates after 1 year from now", 400, None,
             "04010101", "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices for all the room stay dates where occupancy or room type has changed", "", ""),
            ("UpdateRoomStay_20", [{'id': 'Booking_04', 'type': 'booking'}],
             "Update RoomStay to dates where inventory not there", 400, None, "04010602",
             "Room type is not available for selected dates in this hotel", "", "", ""),
            ("UpdateRoomStay_22", SINGLE_BOOKING_01,
             "Update the roomStay by just passing checking date greater then checkout date", 400, None, "04010002",
             "Checkout date should be greater than checkin date", "", "", ""),
            ("UpdateRoomStay_23", SINGLE_BOOKING_01,
             "Update the roomStay by just passing checkout date lesser then checkin date", 400, None, "04010002",
             "Checkout date should be greater than checkin date", "", "", ""),
            ("UpdateRoomStay_32", [{'id': 'Booking_04', 'type': 'booking'}],
             "update roomStay dates for one room outside booking duration for multi-room booking", 200, None, "", "",
             "", "", ""),
            ("UpdateRoomStay_BookingStatus_43", SINGLE_BOOKING_CHECK_IN_01,
             "update room stay duration after checkin by passing charges for new dates", 200, None, "", "", "", "", ""),
            ("UpdateRoomStay_BookingStatus_47", SINGLE_BOOKING_CHECK_IN_01, "Update the checkin_date after checkin",
             403, None, "04010915", "Cannot change room stay to back-dated checkin", "", "", ""),
            ("UpdateRoomStay_BookingStatus_48", SINGLE_BOOKING_CHECK_IN_01,
             "Update roomstay for past invalid checkout date after checking-in the room", 400, None, "04010002",
             "Checkout date should be greater than checkin date", "", "", ""),
            ("UpdateRoomStay_BookingStatus_48", SINGLE_BOOKING_CHECK_IN_01,
             "Update roomstay for past invalid checkout date after checking-in the room", 400, None, "04010002",
             "Checkout date should be greater than checkin date", "", "", ""),
            ("UpdateRoomStay_BookingStatus_60_addEditAddon",
             [{'id': 'Booking_02', 'type': 'booking'}, {'id': 'CreateAddOnV2_01', 'type': 'create_addon'}],
             "Add addon and then extend the booking", 200, None, "", "", "", "", ""),
            ("UpdateRoomStay_BookingStatus_61_addEditAddon",
             [{'id': 'Booking_02', 'type': 'booking'}, {'id': 'CreateAddOnV2_01', 'type': 'create_addon'}],
             "Add addon and then reduce the booking duration", 200, None, "", "", "", "", ""),
            ("UpdateRoomStay_62", SINGLE_BOOKING_01, "Update the room to last month before the report generation", 403,
             None, "04010915", "Cannot change room stay to back-dated checkin", "", "", ""),
            ("UpdateRoomStay_64", [{'id': 'Booking_33', 'type': 'booking'}],
             "NO_INVENTORY_Update the stay dates when inventory is not present for next days", 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices only for the room stay dates where occupancy or room type has changed, and "
             "existing charges hasn't been consumed", "", ""),
            ("UpdateRoomStay_65", [{'id': 'Booking_32', 'type': 'booking'}],
             "Extend the checkout dates for the room where checkin date is diff for guests", 200, None, "", "", "",
             "", ""),
            ("UpdateRoomStay_66", [{'id': 'Booking_32', 'type': 'booking'}],
             "Reduce the checkin dates for the room where checkin date is diff for guests", 400, None, "04010125",
             "Room stay dates and guest stay dates don't match. Please change guest stay dates one by one.",
             "Some guest stays have checkin date different from room stay checkin date. Cannot change room stay "
             "checkin date in such scenario.", "", ""),
            ("UpdateRoomStay_67", [{'id': 'Booking_48', 'type': 'booking'}],
             "Reduce the checkin dates for the room where checkout date is diff for guests", 403, None, "04010915",
             "Cannot change room stay to back-dated checkin", "", "", ""),
            ("UpdateRoomStay_68", [{'id': 'Booking_48', 'type': 'booking'}],
             "Extend the checkout dates for the room where checkout date is diff for guests", 400, None, "04010125",
             "Room stay dates and guest stay dates don't match. Please change guest stay dates one by one.",
             "Some guest stays have checkout date different from room stay checkout date. Cannot change room stay "
             "checkout date in such scenario.", "", ""),
            ("UpdateRoomStay_76", [{'id': 'Booking_04', 'type': 'booking'}],
             "Update the room stay and extend for 2 days for 1 day booking", 200, None, "", "", "", "", ""),
            ("UpdateRoomStay_78", [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': 'checkinPost_01', 'type': 'check_in'},
                                   {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
                                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                   {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                                   {'id': 'checkoutAction_04', 'type': 'checkout'}],
             "Change the stay date for checked out booking", 400, None, "04010127",
             "Checkout date cannot be changed for already checked-out room",
             "RoomStay has already been checked out. Cannot change room stay checkout date", "", ""),
            ("UpdateRoomStay_01_FDM", [{'id': 'BookingFuture_02_Credit_B2B', 'type': 'booking'}],
             "change stay dates by FDM for b2b booking", 403, 'fdm', "04010968",
             "Stay dates change is not allowed for bookings from this channel", "", "", ""),
            ("UpdateRoomStay_01_CRTeam", [{'id': 'BookingFuture_02_Credit_B2B', 'type': 'booking'}],
             "change stay dates by CR_team for b2b booking", 200, 'cr-team', "", "", "", "", ""),
        ])
    @pytest.mark.regression
    def test_change_stay_date(self, client_, test_case_id, previous_actions, tc_description, status_code, user_type,
                              error_code, error_message, dev_message, error_payload, skip_message):
        if skip_message:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.booking_request.update_room_stay(client_, test_case_id=test_case_id, status_code=status_code,
                                                         user_type=user_type,
                                                         booking_id=self.booking_request.booking_id)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, message=error_message,
                                                    dev_message=dev_message, extra_payload=error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request.booking_id, self.booking_request,
                            hotel_id)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, "
        "error_message, dev_message, error_payload, skip_message",
        [
            ("UpdateRoomStay_DCA_01", SINGLE_BOOKING_01_USING_V2,
             "Update DCA to true for single room booking", 200, None, "", "", "", "", ""),
            ("UpdateRoomStay_DCA_02",
             SINGLE_BOOKING_01_USING_V2 + [{'id': 'UpdateRoomStay_DCA_01', 'type': 'room_update'}],
             "Update DCA to false from true for single room booking", 200, None, "", "", "", "", ""),
            ("UpdateRoomStay_DCA_03", MULTIPLE_ROOM_BOOKING_01_USING_V2,
             "Update DCA to true for multiple room booking", 200, None, "", "", "", "", ""),
        ])
    @pytest.mark.regression
    def test_disallow_charge_addition(self, client_, test_case_id, previous_actions, tc_description, status_code,
                                      user_type,
                                      error_code, error_message, dev_message, error_payload, skip_message):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.update_room_stay(client_, test_case_id=test_case_id, status_code=status_code,
                                                         user_type=user_type,
                                                         booking_id=self.booking_request.booking_id)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, message=error_message,
                                                    dev_message=dev_message, extra_payload=error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation_disallow_charge_addition(client_, response, test_case_id, self.booking_request.booking_id,
                                                     self.booking_request, hotel_id)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, "
        "error_message, dev_message, error_payload, skip_message",
        [
            ("UpdateBulkRoomStay_DCA_01", SINGLE_BOOKING_01_USING_V2,
             "Update DCA to true for single room booking", 200, None, "", "", "", "", ""),
            ("UpdateBulkRoomStay_DCA_02",
             SINGLE_BOOKING_01_USING_V2 + [{'id': 'UpdateBulkRoomStay_DCA_01', 'type': 'bulk_room_update'}],
             "Update DCA to false from true for single room booking", 200, None, "", "", "", "", ""),
            ("UpdateBulkRoomStay_DCA_03", MULTIPLE_ROOM_BOOKING_01_USING_V2,
             "Update DCA to true for multiple room booking", 200, None, "", "", "", "", ""),
            ("UpdateBulkRoomStay_DCA_04", MULTIPLE_ROOM_BOOKING_01_USING_V2,
             "Update DCA to true, false for multiple room booking", 200, None, "", "", "", "", ""),
        ])
    @pytest.mark.regression
    def test_bulk_disallow_charge_addition(self, client_, test_case_id, previous_actions, tc_description, status_code,
                                           user_type,
                                           error_code, error_message, dev_message, error_payload, skip_message):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.bulk_update_room_stay(client_, test_case_id=test_case_id,
                                                              status_code=status_code,
                                                              user_type=user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, message=error_message,
                                                    dev_message=dev_message, extra_payload=error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation_disallow_charge_addition(client_, response, test_case_id, self.booking_request.booking_id,
                                                     self.booking_request, hotel_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation_disallow_charge_addition(client_, response, test_case_id, booking_id, booking_request, hotel_id):
        validation = ValidationUpdateRoomStay(client_, response=response, test_case_id=test_case_id, hotel_id=hotel_id)
        validation.validate_disallow_charge_response(booking_request=booking_request, booking_id=booking_id)
