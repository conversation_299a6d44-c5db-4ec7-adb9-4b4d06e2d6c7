import pytest

from prometheus.integration_tests.config import error_messages
from prometheus.integration_tests.config.common_config import FDM, CR_TEAM
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_remove_addon_v2 import RemoveAddOnValidations
from prometheus.tests.mockers import mock_tax_calculator_service
from prometheus.integration_tests.config.common_config import HOTEL_ID, HOTEL_CURRENCY_MAP
from treebo_commons.money.constants import CurrencyType


class TestRemoveAddOnV2(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [

            ("RemoveAddOnV2_01", "Remove a addon on single booking", SINGLE_BOOKING_01 + CREATE_ADDON_01, 200,
             None, "", "", "", "", False, ""),
            ("RemoveAddOnV2_02", "Remove a linked addon and verify charge components single booking",
             SINGLE_BOOKING_CHECK_IN_01 +
             CREATE_ADDON_04, 400,
             None, "", "Addon delete API for Room Linked Addon is not supported", "", "", False, ""),
            ("RemoveAddOnV2_03", "Remove a addon on checked-in booking", SINGLE_BOOKING_CHECK_IN_01 + CREATE_ADDON_03,
             200,
             None, "", "", "", "", False, ""),
            # ("RemoveAddOnV2_04", "Remove a addon on part-checked-out booking", PART_CHECKOUT_01 + CREATE_ADDON_02, 200,
            #  None, "", "", "", "", False, ""),
            # ("RemoveAddOnV2_05", "Remove a addon on checked-out booking", FULL_CHECKOUT_01 + CREATE_ADDON_03, 200,
            #  None, "", "", "", "", False, ""),
            # # ("RemoveAddOnV2_01", "Remove a already deleted addon", SINGLE_BOOKING_01 + CREATE_ADDON_01, 200,
            # #      None, "", "", "", "", False, ""),
            ("RemoveAddOnV2_01", "(MultiCurrency)Remove a addon on single booking", SINGLE_BOOKING_01 + CREATE_ADDON_01,
             200,
             None, "", "", "", "", False, ""),
            ("RemoveAddOnV2_02", "(MultiCurrency)Remove a linked addon and verify charge components single booking",
             SINGLE_BOOKING_CHECK_IN_01 +
             CREATE_ADDON_04, 400,
             None, "", "Addon delete API for Room Linked Addon is not supported", "", "", False, ""),
            ("RemoveAddOnV2_03", "(MultiCurrency)Remove a addon on checked-in booking",
             SINGLE_BOOKING_CHECK_IN_01 + CREATE_ADDON_03,
             200,
             None, "", "", "", "", False, ""),
            # ("RemoveAddOnV2_04", "(MultiCurrency)Remove a addon on part-checked-out booking",
            #  PART_CHECKOUT_01 + CREATE_ADDON_02, 200,
            #  None, "", "", "", "", False, ""),
            ("RemoveAddOnV2_05", "(MultiCurrency)Remove a addon on checked-out booking",
             FULL_CHECKOUT_03 + CREATE_ADDON_03, 200,
             None, "", "", "", "", True, ""),

        ])
    @pytest.mark.regression
    def test_remove_add_on_v2(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                              error_code, error_message, dev_message, error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        with mock_tax_calculator_service(currency=CurrencyType(currency)):
            if previous_actions:  # Running the pre-requisite for the actual test.
                self.common_request_caller(client_, previous_actions, hotel_id)
                # Running the actual test
            response = self.booking_request.remove_add_on(client_, self.booking_request.booking_id,
                                                          self.booking_request.addon_id, status_code,
                                                          user_type)
        self.billing_request.get_bill_request(client_, self.booking_request.bill_id, 200)
        # Vaildations
        if status_code in (400, 403):
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in (200, 201):
            self.validation(test_case_id, client_)
        else:
            assert False, "Response status code is not matching"

    def validation(self, test_case_id, client_):
        validation = RemoveAddOnValidations(test_case_id)
        validation.validate_deleted_addons_expenses(self.booking_request.addon_id,
                                                    self.booking_request.booking_id)
        validation.validate_charge_components_linked_addon(client_, self.billing_request,
                                                           self.booking_request.bill_id)

    ################################################## POLICY ##################################################
    @pytest.mark.parametrize("test_case_id, tc_super_admin ,tc_description, previous_actions, status_code, user_type, "
                             "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [

                                 # ("RemoveAddOnV2Policy_01", "RemoveAddOnV2_02",
                                 #  "Remove the addon by fdm for checked-in booking",
                                 #  GROUP_BOOKING_CHECK_IN_01 + CREATE_ADDON_04, 200, FDM, "", "", "", "", False, ""),
                                 # ("RemoveAddOnV2Policy_02", "RemoveAddOnV2_04",
                                 #  "Remove the addon by fdm for part-checked-out booking",
                                 #  PART_CHECKOUT_01 + CREATE_ADDON_02, 403, FDM, "",
                                 #  error_messages.DELETE_PAST_DATED_ADDON, "", "", False, ""),
                                 # ("RemoveAddOnV2Policy_03", "RemoveAddOnV2_04",
                                 #  "Remove the addon by cr-team for part-checked-out booking",
                                 #  PART_CHECKOUT_01 + CREATE_ADDON_02, 200, CR_TEAM, "", "", "", "", False, ""),
                                 # ("RemoveAddOnV2Policy_04", "RemoveAddOnV2_05",
                                 #  "Remove the addon by fdm for checked-out booking",
                                 #  FULL_CHECKOUT_01 + CREATE_ADDON_03, 403, FDM, "", error_messages.ADDON_CHECKOUT_ERROR,
                                 #  "", "", False, ""),
                                 # ("RemoveAddOnV2Policy_05", "RemoveAddOnV2_05",
                                 #  "Remove the addon by cr-team for checked-out booking",
                                 #  FULL_CHECKOUT_01 + CREATE_ADDON_03, 403, CR_TEAM, "",
                                 #  error_messages.ADDON_CHECKOUT_ERROR, "", "", False, ""),
                                 # ("RemoveAddOnV2Policy_01", "RemoveAddOnV2_02",
                                 #  "(MultiCurrency)Remove the addon by fdm for checked-in booking",
                                 #  GROUP_BOOKING_CHECK_IN_01 + CREATE_ADDON_04, 200, FDM, "", "", "", "", False, ""),
                                 # ("RemoveAddOnV2Policy_02", "RemoveAddOnV2_04",
                                 #  "(MultiCurrency)Remove the addon by fdm for part-checked-out booking",
                                 #  PART_CHECKOUT_01 + CREATE_ADDON_02, 403, FDM, "",
                                 #  error_messages.DELETE_PAST_DATED_ADDON, "", "", False, ""),
                                 # ("RemoveAddOnV2Policy_03", "RemoveAddOnV2_04",
                                 #  "(MultiCurrency)Remove the addon by cr-team for part-checked-out booking",
                                 #  PART_CHECKOUT_01 + CREATE_ADDON_02, 200, CR_TEAM, "", "", "", "", False, ""),
                                 # ("RemoveAddOnV2Policy_04", "RemoveAddOnV2_05",
                                 #  "(MultiCurrency)Remove the addon by fdm for checked-out booking",
                                 #  FULL_CHECKOUT_01 + CREATE_ADDON_03, 403, FDM, "", error_messages.ADDON_CHECKOUT_ERROR,
                                 #  "", "", False, ""),
                                 # ("RemoveAddOnV2Policy_05", "RemoveAddOnV2_05",
                                 #  "(MultiCurrency)Remove the addon by cr-team for checked-out booking",
                                 #  FULL_CHECKOUT_01 + CREATE_ADDON_03, 403, CR_TEAM, "",
                                 #  error_messages.ADDON_CHECKOUT_ERROR, "", "", False, ""),
                             ])
    @pytest.mark.regression
    def test_remove_add_on_policy(self, client_, test_case_id, tc_super_admin, tc_description, previous_actions,
                                  status_code, user_type, error_code, error_message, dev_message, error_payload,
                                  skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if tc_super_admin is None:
            tc_super_admin = test_case_id
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        with mock_tax_calculator_service(currency=CurrencyType(currency)):
            if previous_actions:  # Running the pre-requisite for the actual test.
                self.common_request_caller(client_, previous_actions, hotel_id)
                # Running the actual test
                response = self.booking_request.remove_add_on(client_, self.booking_request.booking_id,
                                                              self.booking_request.addon_id, status_code,
                                                              user_type)
        # Vaildations
        if status_code in (400, 403):
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in (200, 201):
            self.validation(tc_super_admin, client_)
        else:
            assert False, "Response status code is not matching"
