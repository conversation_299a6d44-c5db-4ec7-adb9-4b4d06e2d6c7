import pytest

from prometheus.integration_tests.config.common_config import (
    ERROR_CODES,
    HOTEL_ID,
    SUCCESS_CODES,
)
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests import before_test_actions
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.booking.validations.validation_create_booking_v2 import (
    ValidationBooking,
)
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestPatchBooking(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, previous_actions",
        [
            ("patch_booking_01", "Mark booking level btc to true", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_BOOKING_01_USING_V2),
            ("patch_booking_02", "Add comment ", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_BOOKING_01_USING_V2),
            ("patch_booking_03", "Add extra information", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_BOOKING_01_USING_V2),
            ("patch_booking_04", "Add group name", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_BOOKING_01_USING_V2),
            ("patch_booking_02", "Add comment for b2b booking ", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_03", "Add extra information for b2b booking", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_04", "Add group name for b2b booking", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_05", "Add Travel Agent legal Name", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_06", "change Travel Agent legal Name", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_07", "change Travel Agent client_internal_code", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_08", "change Travel Agent email", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_09", "change Travel Agent external reference_id", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_10", "change Travel Agent phone number", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_11", "change Travel Agent tin", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_12", "change Travel Agent address -> country", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_13", "change Travel Agent address -> state", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_14", "change Travel Agent address -> city", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_15", "change Travel Agent address -> pincode", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_16", "change Travel Agent address -> line1", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_17", "change Travel Agent address -> line2", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2),
            ("patch_booking_18", "Add company_details Details", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_19", "change company_details legal Name", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_20", "change company_details client_internal_code", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_21", "change company_details email", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_22", "change company_details external reference_id", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_23", "change company_details phone number", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_24", "change company_details tin", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_25", "change company_details address -> country", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_26", "change company_details address -> state", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_27", "change company_details address -> city", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_28", "change company_details address -> pincode", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_29", "change company_details address -> line1", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_30", "change company_details address -> line2", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_31", "change reference_number", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_BOOKING_01_USING_V2),
            ("patch_booking_31", "change reference_number for b2b booking", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_32", "change reference_number of checkout booking", 200, "", "", "", "", "", "",
             before_test_actions.FULL_CHECKOUT_03_V2),
            ("patch_booking_33", "[SEZ_LUT]Create a booking with sez and lut set to true and update lut to false", 200,
             "", "", "", "", "", "", before_test_actions.FULL_CHECKOUT_03_V2),
            ("patch_booking_34", "[SEZ_LUT]Create a booking with sez and lut set to true and update sez to false", 200,
             "", "", "", "", "", "", before_test_actions.FULL_CHECKOUT_03_V2),
            ("patch_booking_35", "[SEZ_LUT]Create a booking with sez and lut set to true and update lut and sez to"
                                 " false", 200, "", "", "", "", "", "", before_test_actions.FULL_CHECKOUT_03_V2),
            ("patch_booking_36", "[SEZ_LUT]Create a booking with sez set to true and lut set to false and update lut "
                                 "to true", 200, "", "", "", "", "", "", before_test_actions.FULL_CHECKOUT_04_V2),
            ("patch_booking_35", "[SEZ_LUT]Create a booking with sez set to true and lut set to false and update sez "
                                 "to false", 200, "", "", "", "", "", "", before_test_actions.FULL_CHECKOUT_04_V2),
            ("patch_booking_34", "[SEZ_LUT]Create a booking with sez set to true and lut set to false and update lut "
                                 "to true and sez to false", 200, "", "", "", "", "", "",
             before_test_actions.FULL_CHECKOUT_04_V2),
            ("patch_booking_36", "[SEZ_LUT]Create a booking with sez set to false and lut set to true and update sez "
                                 "to true", 200, "", "", "", "", "", "", before_test_actions.FULL_CHECKOUT_05_V2),
            ("patch_booking_35", "[SEZ_LUT]Create a booking with sez set to false and lut set to true and update lut "
                                 "to false", 200, "", "", "", "", "", "", before_test_actions.FULL_CHECKOUT_05_V2),
            ("patch_booking_33", "[SEZ_LUT]Create a booking with sez set to false and lut set to true and update lut"
                                 " to false and sez to true", 200, "", "", "", "", "", "",
             before_test_actions.FULL_CHECKOUT_05_V2),
            ("patch_booking_33", "[SEZ_LUT]Create a booking with sez and lut set to false and update sez to true", 200,
             "", "", "", "", "", "", before_test_actions.FULL_CHECKOUT_06_V2),
            ("patch_booking_34", "[SEZ_LUT]Create a booking with sez and lut set to false and update lut to true", 200,
             "", "", "", "", "", "", before_test_actions.FULL_CHECKOUT_06_V2),
            ("patch_booking_36", "[SEZ_LUT]Create a booking with sez and lut set to false and update lut and sez to "
                                 "true", 200, "", "", "", "", "", "", before_test_actions.FULL_CHECKOUT_06_V2),
            ("patch_booking_37", "[SEZ_LUT]Update sez and lut of travel agent details", 200, "", "", "", "", "", "",
             before_test_actions.FULL_CHECKOUT_07_V2),
            ("patch_booking_38", "change Travel Agent external reference_id to hello", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_39", "change Travel Agent external reference_id to hello world", 200, "", "", "", "", "",
             "", before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2 + [{'id': "patch_booking_38", 'type': 'patch_booking'}]),
            ("patch_booking_40", "change company_details external_ref_id to hello", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2),
            ("patch_booking_41", "change company_details external_ref_id to hello world", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2 + [
                 {'id': "patch_booking_40", 'type': 'patch_booking'}]),
            ("patch_booking_42", "Update GST number of checked out booking", 200, "", "", "", "", "", "",
             before_test_actions.FULL_CHECKOUT_06_V2),
            ("patch_booking_43", "Update Legal Name of checked out booking", 200, "", "", "", "", "", "",
             before_test_actions.FULL_CHECKOUT_06_V2),
            ("patch_booking_44", "[SEZ_LUT]Create a booking with sez and lut set to true and update lut to false", 400,
             "", "04015980", "", "charge is invoiced or consumed or allowance passed", "", "",
             [{'id': "booking_with_lut_04", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}]),
            ("patch_booking_45", "[SEZ_LUT]Create a booking with sez and lut set to true and update sez to false", 400,
             "", "04015980", "", "charge is invoiced or consumed or allowance passed", "", "",
             [{'id': "booking_with_lut_04", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}]),
            ("patch_booking_46", "Update GST number of checked in booking", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_04", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}]),
            ("patch_booking_47", "Update Legal Name of checked in booking", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_04", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}]),
            ("patch_booking_48", "[SEZ_LUT, SLAB BASED]Create a booking with sez and lut set to true and update lut to"
                                 " false", 400, "", "04010436", "", "", "", "",
             before_test_actions.BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_04),
            ("patch_booking_49", "[SEZ_LUT, SLAB BASED]Create a booking with sez and lut set to true and update sez to"
                                 " false", 400, "", "04010436", "", "", "", "",
             before_test_actions.BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_04),
            ("patch_booking_50", "[SEZ_LUT, SLAB BASED]Create a booking with sez and lut set to true and update lut to"
                                 " false", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_WALK_BOOKING_V2_01_SLAB_BASED),
            ("patch_booking_51", "[SEZ_LUT, SLAB BASED]Create a booking with sez and lut set to true and update sez to"
                                 " false", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_WALK_BOOKING_V2_01_SLAB_BASED),
            ("patch_booking_52", "[SEZ_LUT]Create a booking with sez and lut set to true and update lut to false", 200,
             "", "", "", "", "", "", before_test_actions.BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_04),
            ("patch_booking_53", "[SEZ_LUT]Create a booking with sez and lut set to true and update sez to false", 200,
             "", "", "", "", "", "", before_test_actions.BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_04),
            ("patch_booking_54", "[SEZ_LUT]Create a booking with sez and lut set to true and update lut to false", 200,
             "", "", "", "", "", "", [{'id': "booking_with_lut_01", 'type': 'booking_v2'}]),
            ("patch_booking_55", "[SEZ_LUT]Create a booking with sez and lut set to true and update sez to false", 200,
             "", "", "", "", "", "", [{'id': "booking_with_lut_01", 'type': 'booking_v2'}]),
            ("patch_booking_56", "[SEZ_LUT]Create a booking with sez and lut set to true and update lut and sez to"
                                 " false", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_01", 'type': 'booking_v2'}]),
            ("patch_booking_57", "[SEZ_LUT]Create a booking with sez set to true and lut set to false and update lut "
                                 "to true", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_02", 'type': 'booking_v2'}]),
            ("patch_booking_56", "[SEZ_LUT]Create a booking with sez set to true and lut set to false and update sez "
                                 "to false", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_02", 'type': 'booking_v2'}]),
            ("patch_booking_55", "[SEZ_LUT]Create a booking with sez set to true and lut set to false and update lut "
                                 "to true and sez to false", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_02", 'type': 'booking_v2'}]),
            ("patch_booking_57", "[SEZ_LUT]Create a booking with sez set to false and lut set to true and update sez "
                                 "to true", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_03", 'type': 'booking_v2'}]),
            ("patch_booking_56", "[SEZ_LUT]Create a booking with sez set to false and lut set to true and update lut "
                                 "to false", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_03", 'type': 'booking_v2'}]),
            ("patch_booking_54", "[SEZ_LUT]Create a booking with sez set to false and lut set to true and update lut"
                                 " to false and sez to true", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_03", 'type': 'booking_v2'}]),
            ("patch_booking_54", "[SEZ_LUT]Create a booking with sez and lut set to false and update sez to true", 200,
             "", "", "", "", "", "", [{'id': "booking_with_lut_04", 'type': 'booking_v2'}]),
            ("patch_booking_55", "[SEZ_LUT]Create a booking with sez and lut set to false and update lut to true", 200,
             "", "", "", "", "", "", [{'id': "booking_with_lut_04", 'type': 'booking_v2'}]),
            ("patch_booking_57", "[SEZ_LUT]Create a booking with sez and lut set to false and update lut and sez to "
                                 "true", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_04", 'type': 'booking_v2'}]),
            ("patch_booking_58", "[SEZ_LUT]Update sez and lut of travel agent details", 200, "", "", "", "", "", "",
             [{'id': "booking_with_lut_05", 'type': 'booking_v2'}]),
            ("patch_booking_59", "[SEZ_LUT] Create a checked in booking and add company detail with sez and lut as "
                                 "false", 200, "", "", "", "", "", "",
             before_test_actions.WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2),
            ("patch_booking_60",
             "[SEZ_LUT] Create a checked in booking and add company detail with sez as true and lut as false", 400, "",
             "04015980", "", "charge is invoiced or consumed or allowance passed", "", "",
             before_test_actions.WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2 +
             [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}]),
            ("patch_booking_61",
             "[SEZ_LUT] Create a checked in booking and add company detail with sez as false and lut as true", 400, "",
             "04015980", "", "charge is invoiced or consumed or allowance passed", "", "",
             before_test_actions.WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2 +
             [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}]),
            ("patch_booking_62", "[SEZ_LUT] Create a checked in booking and add company detail with sez and lut as "
                                 "true", 400, "",  "04015980", "", "charge is invoiced or consumed or allowance passed",
             "", "", before_test_actions.WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2 +
             [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}]),
            ("patch_booking_63", "Update Travel Agent Details of OTA booking", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_ROOM_OTA_BOOKING_V2_01),
            ("patch_booking_64", "Update Travel Agent Details of OTA booking whose commission is not defined", 200, "",
             "", "", "", "", "", before_test_actions.SINGLE_ROOM_OTA_BOOKING_V2_01),
            ("patch_booking_65", "Add Travel Agent Details in B2B booking", 200, "", "", "", "", "", "",
             [{'id': "booking_225", 'type': 'booking_v2'}]),
            ("patch_booking_66", "Add Travel Agent Details in B2B booking whose commission is not defined", 200, "", "",
             "", "", "", "", [{'id': "booking_225", 'type': 'booking_v2'}]),
            ("patch_booking_67", "Update Travel Agent Details in B2B booking", 200, "", "", "", "", "", "",
             [{'id': "booking_226", 'type': 'booking_v2'}]),
            ("patch_booking_68", "Update Travel Agent Details in B2B booking whose commission is not defined", 200, "",
             "", "", "", "", "", [{'id': "booking_226", 'type': 'booking_v2'}]),
            ("patch_booking_69", "Update Travel Agent Details of OTA booking after add room", 200, "", "", "", "", "",
             "", [{'id': "booking_212", 'type': 'booking_v2'},
                  {'id': "AddMultipleRoom_01", 'type': 'add_multiple_room'}]),
            ("patch_booking_70", "Update Travel Agent Details of OTA booking after add guest", 200, "", "", "", "", "",
             "", [{'id': "booking_212", 'type': 'booking_v2'},
                  {'id': 'AddGuest_01', 'type': 'add_multiple_guest_stay', 'enable_rate_plan': False,
                   'is_inclusion_added': False}]),
            ("patch_booking_71", "Update Travel Agent Details of OTA booking after remove room", 200, "", "", "", "",
             "", "", [{'id': "booking_232", 'type': 'booking_v2'}, {'id': "cancel_room_04", 'type': 'mark_cancel'}]),
            ("patch_booking_72", "Update Travel Agent Details of OTA booking after remove guest", 200, "", "", "", "",
             "", "", [{'id': "ota_booking_multiple_guest", 'type': 'booking_v2'},
                      {'id': 'MarkCancelled_02', 'type': 'mark_cancelled', 'enable_rate_manager': False,
                       'is_inclusion_added': False}]),
            ("patch_booking_73", "Update Travel Agent Details of OTA booking after change stay date", 200, "", "", "",
             "", "", "", [{'id': "booking_212", 'type': 'booking_v2'},
                          {'id': 'UpdateStayDuration_129', 'type': 'update_stay_duration', 'enable_rate_manager': True,
                           'is_inclusion_added': False}]),
            ("patch_booking_74", "Update Travel Agent Details of OTA booking after update rate plan", 200, "", "", "",
             "", "", "", [{'id': "booking_212", 'type': 'booking_v2'},
                          {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan',
                           'enable_rate_manager': True, 'is_inclusion_added': True}]),
            ("patch_booking_75", "Update Travel Agent Details of OTA booking after add extra charge", 200, "", "", "",
             "", "", "", [{'id': "booking_212", 'type': 'booking_v2'},
                          {'id': 'Multiple_Expense_97', 'type': 'create_expense_V3'}]),
            ("patch_booking_76", "Update Travel Agent Details of OTA booking after room stay price updation", 200, "",
             "", "", "", "", "", [{'id': "booking_212", 'type': 'booking_v2'},
                                  {'id': 'EditCharge_66', 'type': 'update_expense', 'charge_id': 1}]),
            ("patch_booking_77", "Update Travel Agent Details of OTA booking after put booking", 200, "", "", "",
             "", "", True, [{'id': "booking_212", 'type': 'booking_v2'},
                            {'id': "put_booking_108", 'type': 'edit_booking_v2'}]),
            ("patch_booking_78", "Update Travel Agent Details of OTA booking after put booking", 200, "", "", "",
             "", "", "", [{'id': "booking_212", 'type': 'booking_v2'},
                          {'id': "put_booking_109", 'type': 'edit_booking_v2'}]),
            ("patch_booking_79", "Update Travel Agent Details of OTA booking after passing allowance", 200, "", "", "",
             "", "", "", [{'id': "booking_212", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                          {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                          {'id': 'AddAllowance_02', 'type': 'add_allowance_v2', 'extras': [1, 1]}]),
            ("patch_booking_80", "Update Travel Agent Details of OTA booking after passing bulk allowance", 200, "", "",
             "", "", "", "", [{'id': "booking_212", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                              {'id': 'AddBulkAllowance_01', 'type': 'add_bulk_allowance'}]),
            ("patch_booking_81", "Update Travel Agent Details in B2B booking after checkout", 200, "", "", "", "", "",
             "", before_test_actions.FULL_CHECKOUT_11_V2),
            ("patch_booking_82", "Update Travel Agent Details of OTA booking after edit commission",
             200, "", "", "", "", "", "", before_test_actions.SINGLE_ROOM_OTA_BOOKING_V2_01 +
             [{'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}]),
            ("patch_booking_83", "Update Travel Agent Details of B2B booking after edit commission",
             200, "", "", "", "", "", "", [{'id': "booking_226", 'type': 'booking_v2'},
             {'id': 'update_ta_commission_02', 'type': 'update_ta_commission', 'commission_value': 10.0}]),
            ("patch_booking_84",
             "Update Travel Agent Details of B2B booking whose commission is not defined after edit commission",
             200, "", "", "", "", "", "", [{'id': "booking_226", 'type': 'booking_v2'},
                                           {'id': 'update_ta_commission_02', 'type': 'update_ta_commission',
                                            'commission_value': 10.0}]),
            ("patch_booking_85", "Update Travel Agent Details in B2B booking after edit commission and checkout",
             200, "", "", "", "", "", "",
             [{'id': "booking_226", 'type': 'booking_v2'},
              {'id': 'update_ta_commission_02', 'type': 'update_ta_commission', 'commission_value': 10.0},
              {'id': "checkin_reissue_27", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]),
            ("patch_booking_86", "Add Travel Agent Details in B2B booking having account details", 200, "", "", "", "",
             "", "",
             [{'id': "booking_225", 'type': 'booking_v2'}]),
            ("patch_booking_87", "Add company_details Details having account details", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01_USING_V2)
        ])
    @pytest.mark.regression
    def test_patch_booking(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                           error_message, dev_message, error_payload, skip_case, previous_actions):
        if skip_case:
            pytest.skip("skipped")

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]
        is_sez_lut_update = True if "SEZ_LUT" in tc_description else False

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        if "SLAB BASED" in tc_description:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=True))

        response = self.booking_request.patch_booking_request(client_, test_case_id, status_code, user_type)

        if "SLAB BASED" in tc_description:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=False))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            booking_id = self.booking_request.booking_id
            bill_id = self.booking_request.bill_id
            self.validation(client_, response, test_case_id, self.booking_request, self.billing_request, booking_id,
                            bill_id, hotel_id, is_sez_lut_update, user_type)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, booking_request, billing_request, booking_id, bill_id,
                   hotel_id, is_sez_lut_update, user_type):
        validation = ValidationBooking(client_, response, test_case_id, booking_request, billing_request,
                                       booking_id, bill_id, hotel_id, user_type)
        validation.validate_patch_booking_response(is_sez_lut_update)
        validation.validate_commissions()


class TestPatchGSTBooking(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, previous_actions, extra",
        [
            ("patch_default_entity_booking_01", "Update TA has_lut and is_sez as false", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DEFAULT_TA_USING_V2, [False, False]),
            ("patch_default_entity_booking_02", "Update Company has_lut and is_sez as false", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DEFAULT_TA_USING_V2, [True, True]),
            ("patch_default_entity_booking_03", "Update Company has_lut and is_sez as true", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DEFAULT_TA_USING_V2, [True, True]),
            ("patch_default_entity_booking_04", "Update company has_lut and is_sez as false", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DEFAULT_COMPANY_BOOKING_USING_V2, [False, False]),
            ("patch_default_entity_booking_05", "Update TA has_lut and is_sez as false", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DEFAULT_COMPANY_BOOKING_USING_V2, [True, True]),
            ("patch_default_entity_booking_06", "Update TA has_lut and is_sez as true", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DEFAULT_COMPANY_BOOKING_USING_V2, [True, True]),
        ]
    )
    @ pytest.mark.regression
    def test_patch_booking(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                           error_message, dev_message, error_payload, skip_case, previous_actions,extra):
        if skip_case:
            pytest.skip("skipped")

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]
        is_sez_lut_update = True if "SEZ_LUT" in tc_description else False

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.patch_booking_request(client_, test_case_id, status_code, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            booking_id = self.booking_request.booking_id
            bill_id = self.booking_request.bill_id
            self.validation(client_, response, test_case_id, self.booking_request, self.billing_request, booking_id,
                            bill_id, hotel_id, is_sez_lut_update, user_type, extra)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, booking_request, billing_request, booking_id, bill_id,
                   hotel_id, is_sez_lut_update, user_type, extra):
        validation = ValidationBooking(client_, response, test_case_id, booking_request, billing_request,
                                       booking_id, bill_id, hotel_id, user_type)
        validation.validate_default_billed_category(extra)
