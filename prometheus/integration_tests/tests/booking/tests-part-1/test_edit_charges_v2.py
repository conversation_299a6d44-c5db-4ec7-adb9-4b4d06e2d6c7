from datetime import date, timedelta

import pytest

from prometheus.integration_tests.config.common_config import HOTEL_ID, ERROR_CODES, SUCCESS_CODES, CLUBBED_TAX_CONFIG
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests import before_test_actions
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.booking.validations.validation_charge_v2 import ValidationEditChargesV2
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestEditChargesV2(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, previous_actions, charge_id",
        [
            ("EditCharge_01", "Edit pretax amount of room charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_BOOKING_01, "1"),
            ("EditCharge_02", "Edit pretax amount of expense non-credit non-linked charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE, "2"),
            ("EditCharge_03", "Edit pretax amount of expense credit non-linked charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE, "2"),
            ("EditCharge_04", "Edit pretax amount of expense non-credit linked charge", 200, "", "",
             "", "", "", "Bug", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE, "2"),
            ("EditCharge_05", "Edit pretax amount of expense credit linked charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE, "2"),
            ("EditCharge_06", "Edit posttax amount of room charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_BOOKING_01, "1"),
            ("EditCharge_07", "Edit posttax amount of expense non-credit non-linked charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE, "2"),
            ("EditCharge_08", "Edit posttax amount of expense credit non-linked charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE, "2"),
            ("EditCharge_09", "Edit posttax amount of expense non-credit linked charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE, "2"),
            ("EditCharge_10", "Edit posttax amount of expense credit linked charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE, "2"),
            ("EditCharge_11", "Edit pretax amount of posted non-credit charge", 400, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'PostCharge_06', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_12", "Edit pretax amount of posted linked credit charge", 400, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE + [
                 {'id': 'PostCharge_09', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_13", "Edit posttax amount of posted non-credit charge", 400, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'PostCharge_06', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_14", "Edit posttax amount of posted linked credit charge", 400, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE + [
                 {'id': 'PostCharge_09', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_15", "Edit pretax amount of cancel non-credit charge", 400, "", "", "", "", "", "Bug",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'CancelCharge_08', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_16", "Edit pretax amount of cancel linked credit charge", 400, "", "", "", "", "", "Bug",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE + [
                 {'id': 'CancelCharge_08', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_17", "Edit posttax amount of cancel non-credit charge", 400, "", "", "", "", "", "Bug",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'CancelCharge_08', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_18", "Edit posttax amount of cancel linked credit charge", 400, "", "", "", "", "", "Bug",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE + [
                 {'id': 'CancelCharge_08', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_19", "Change pay_at_checkout to pay_after_checkout for b2c booking", 400, "", "",
             "", "", "", "", before_test_actions.SINGLE_BOOKING_01, "1"),
            ("EditCharge_20", "Change pay_at_checkout to pay_after_checkout for roomstay b2b booking", 200, "", "",
             "", "", "", "", before_test_actions.MULTIPLE_ROOM_BOOKING_02, "2"),
            ("EditCharge_21", "Change account of roomstay for b2b booking ", 200, "", "", "", "", "", "",
             before_test_actions.MULTIPLE_ROOM_BOOKING_02, "1"),
            ("EditCharge_22", "Change pay_at_checkout to pay_after_checkout for non-linked non-credit charge",
             200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE, "2"),
            ("EditCharge_23", "Change account of non-linked credit charge for b2b booking ",
             200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE, "2"),
            ("EditCharge_24", "Change pay_at_checkout to pay_after_checkout for linked non-credit charge in b2b "
                              "booking", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE, "2"),
            ("EditCharge_25", "Change account of inked credit charge for b2b booking",
             200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE, "2"),
            ("EditCharge_26", "change pay_after_checkout to pay at checkoutof credit roomstay charge", 200, "",
             "", "", "", "", "", before_test_actions.MULTIPLE_ROOM_BOOKING_02, "1"),
            ("EditCharge_27", "change pay_after_checkout of credit roomstay charge of booker", 200, "",
             "", "", "", "", "", before_test_actions.MULTIPLE_ROOM_BOOKING_02, "1"),
            ("EditCharge_28", "change account of non-credit charge", 200, "",
             "", "", "", "", "", before_test_actions.MULTIPLE_ROOM_BOOKING_02, "1"),
            ("EditCharge_29", "change p-after-c to p-at-c of credit linked charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE, "2"),
            ("EditCharge_30", "change p-after-c to p-at-c of credit non-linked charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE, "2"),
            ("EditCharge_31", "change account of non-credit linked charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE, "2"),
            ("EditCharge_32", "change account of non-credit non-linked charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE, "2"),
            ("EditCharge_33", "change pay_at_checkout to pay_after_checkout of posted charge", 200, "", "", "", "", "",
             "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'PostCharge_06', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_34", "change pay_at_checkout to pay_after_checkout of posted charge", 200, "", "", "", "", "",
             "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE + [
                 {'id': 'PostCharge_09', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_36", "change account of posted account non-credit charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'PostCharge_06', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_35", "change account of posted account credit charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE + [
                 {'id': 'PostCharge_09', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_37", "change account of posted account non-credit charge", 400, "", "", "", "", "", "BUG",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE + [
                 {'id': 'PostCharge_09', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_38", "change account of posted account credit charge", 400, "", "", "", "", "", "BUG",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'CancelCharge_08', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_39", "split the credit room-stay charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_01, "1"),
            ("EditCharge_40", "split the non-credit room-stay charge", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_BOOKING_01, "1"),
            ("EditCharge_41", "split the posted credit room-stay charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_01 + [
                 {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}], "1"),
            ("EditCharge_42", "split the posted non-credit room-stay charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_BOOKING_CHECK_IN_01 + [
                 {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}], "1"),
            ("EditCharge_43", "split the credit linked charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE, "2"),
            ("EditCharge_44", "split the non-credit linked charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE, "2"),
            ("EditCharge_45", "split the posted credit room-stay charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE + [
                 {'id': 'PostCharge_09', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_46", "split the posted non-credit room-stay charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'PostCharge_08', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_47", "split the credit non-linked charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE, "2"),
            ("EditCharge_48", "split the non-credit non-linked  charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE, "2"),
            ("EditCharge_49", "split the posted credit non-linked  charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE + [
                 {'id': 'PostCharge_06', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_50", "split the posted non-credit non-linked  charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'PostCharge_07', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("EditCharge_51", "percentage less than 100", 400, "", "", "", "", "", "BUG",
             before_test_actions.SINGLE_BOOKING_01, "1"),
            ("EditCharge_52", "percentage more than 100", 400, "", "", "", "", "", "BUG",
             before_test_actions.SINGLE_BOOKING_01, "1"),
            ("EditCharge_53", "change account of non-credit charge with allowance", 200, "",
             "", "", "", "", "", before_test_actions.MULTIPLE_ROOM_BOOKING_02_WITH_ALLOWANCE, "1"),
            ("EditCharge_54", "change account of non-credit charge with multiple allowance", 200, "",
             "", "", "", "", "", before_test_actions.ADD_MULTIPLE_ALLOWANCE_NON_CREDIT_CHARGE, "1"),
            ("EditCharge_55", "change account of non-credit charge with allowance on 1 split", 200, "",
             "", "", "", "", "Skip", before_test_actions.SINGLE_ALLOWANCE_ON_NON_CREDIT_SPLIT_CHARGE, "1"),
            ("EditCharge_75", "Edit room stay prices in rate plan of OTA booking", 200, "", "", "", "", "", "Skip",
             before_test_actions.SINGLE_ROOM_OTA_BOOKING_V2_01, "1"),
            ("EditCharge_76", "Edit inclusion prices in rate plan of OTA booking", 200, "", "", "", "", "", "Skip",
             before_test_actions.SINGLE_ROOM_OTA_BOOKING_V2_01, "2"),
            ("EditCharge_77", "Split room stay prices in rate plan of OTA booking", 200, "", "", "", "", "", "Skip",
             before_test_actions.SINGLE_ROOM_OTA_BOOKING_V2_01, "1"),
            ("EditCharge_78", "Edit room stay prices in rate plan of OTA booking after add guest", 200, "", "", "", "",
             "", "Skip", [{'id': "booking_212", 'type': 'booking_v2'},
                          {'id': 'AddGuest_01', 'type': 'add_multiple_guest_stay', 'enable_rate_plan': False,
                           'is_inclusion_added': False}], "1"),
            ("EditCharge_79", "Edit room stay prices in rate plan of OTA booking after remove guest", 200, "", "", "",
             "", "", "Skip", [{'id': "ota_booking_multiple_guest", 'type': 'booking_v2'},
                              {'id': 'MarkCancelled_02', 'type': 'mark_cancelled', 'enable_rate_manager': False,
                               'is_inclusion_added': False}], "1"),
            ("EditCharge_80", "Edit room stay prices in rate plan of OTA booking after updating stay duration", 200, "",
             "", "", "", "", "Skip", [{'id': "booking_212", 'type': 'booking_v2'},
                                      {'id': 'UpdateStayDuration_129', 'type': 'update_stay_duration',
                                       'enable_rate_manager': True, 'is_inclusion_added': False}], "1"),
            ("EditCharge_81", "Edit room stay prices in rate plan of OTA booking after updating rate plan", 200, "", "",
             "", "", "", "Skip", [{'id': "booking_212", 'type': 'booking_v2'},
                                  {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan', 'enable_rate_manager': True,
                                   'is_inclusion_added': True}], "1"),
            ("EditCharge_82", "Edit room stay prices in rate plan of OTA booking after updating room stay price", 200,
             "", "", "", "", "", "Skip", [{'id': "booking_212", 'type': 'booking_v2'},
                                          {'id': 'EditCharge_66', 'type': 'update_expense', 'charge_id': 1}], "1"),
            ("EditCharge_83", "Edit room stay prices in rate plan of OTA booking after put booking", 200, "", "", "",
             "", "", "Skip", [{'id': "booking_212", 'type': 'booking_v2'},
                              {'id': "put_booking_109", 'type': 'edit_booking_v2'}], "1"),
            ("EditCharge_115", "Edit commission then edit room stay prices in rate plan of OTA booking",
             200, "", "", "", "", "", False, [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}], "1"),
        ])
    @pytest.mark.regression
    def test_edit_charges_v2(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                             error_message, dev_message, error_payload, skip_case, previous_actions, charge_id):
        if skip_case:
            pytest.skip("skipped")

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        charge_id = charge_id if charge_id else self.expense_request.charge_id

        response = self.billing_request.edit_charge_v2_request(client_, test_case_id,
                                                               self.booking_request.bill_id, charge_id,
                                                               status_code, user_type)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, charge_id,
                       self.booking_request.booking_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"


class TestPostChargesV2(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, previous_actions, charge_id",
        [
            ("PostCharge_01", "Post a current date non-credit charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_BOOKING_CHECK_IN_01, "1"),
            ("PostCharge_02", "Post a future date non-credit charge", 200, "", "", "", "", "", "",
             before_test_actions.MULTIPLE_DAY_B2C_CHECKIN_BOOKING_01, "1"),
            ("PostCharge_03", "Post a current date credit charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_01, "1"),
            ("PostCharge_04", "Post a charge for non-checked in booking", 400, "", "", "", "", "", "",
             before_test_actions.SINGLE_BOOKING_01, "1"),
            ("PostCharge_05", "Post an invalid charge", 404, "", "", "", "", "", "",
             before_test_actions.SINGLE_BOOKING_01, "10"),
            ("PostCharge_06", "Post an non-credit non-linked expense after checkin", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE, "2"),
            ("PostCharge_07", "Post an credit non-linked expense after checkin", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE, "2"),
            ("PostCharge_08", "Post an non-credit linked expense after checkin", 200, "", "", "", "", "",
             "Bug-expense get created as credit",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE, "2"),
            ("PostCharge_09", "Post an credit linked expense after checkin", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE, "2"),
            ("PostCharge_10", "Post an future non-credit non-linked expense after checkin", 200, "", "", "", "", "", "",
             before_test_actions.MULTIPLE_DAY_B2B_BOOKING_WITH_FUTURE_NON_CREDIT_NON_LINKED_EXPENSE, "5"),
            ("PostCharge_11", "Post an future credit non-linked expense after checkin", 200, "", "", "", "", "", "",
             before_test_actions.MULTIPLE_DAY_B2B_BOOKING_WITH_FUTURE_CREDIT_NON_LINKED_EXPENSE, "5"),
            ("PostCharge_12", "Post an future non-credit linked expense after checkin", 200, "", "", "", "", "", "",
             before_test_actions.MULTIPLE_DAY_B2B_BOOKING_WITH_FUTURE_NON_CREDIT_LINKED_EXPENSE, "5"),
            ("PostCharge_13", "Post an future credit linked expense after checkin", 200, "", "", "", "", "", "",
             before_test_actions.MULTIPLE_DAY_B2B_BOOKING_WITH_FUTURE_CREDIT_LINKED_EXPENSE, "5"),
            ("PostCharge_14", "Post a charge after reversal", 400, "", "", "", "", "", "",
             before_test_actions.REVERSE_BOOKING_01, "1"),
            ("PostCharge_15", "Post a charge for a canceled booking", 400, "", "", "", "", "", "True",
             before_test_actions.CANCELLED_BOOKING_V2, "1"),
            ("PostCharge_16", "Post a charge for a partially checked in booking", 400, "", "", "", "", "", "",
             before_test_actions.PARTIAL_BOOKING_CHECK_IN_02, "2"),
            ("PostCharge_17", "Post a current date credit charge for a b2b booking", 400, "", "", "", "", "", "",
             before_test_actions.SINGLE_B2B_BOOKING_01, "1"),
            ("PostCharge_18", "Post a future non-credit non-linked expense before checkin for a b2b booking", 400, "",
             "", "", "", "", "",
             before_test_actions.MULTIPLE_DAY_NON_CHECKED_IN_BOOKING_WITH_FUTURE_NON_CREDIT_NON_LINKED_EXPENSE, "5"),
            ("PostCharge_19", "Post a future credit linked expense before checkin", 400, "", "", "", "", "", "",
             before_test_actions.MULTIPLE_DAY_NON_CHECKED_IN_BOOKING_WITH_FUTURE_CREDIT_LINKED_EXPENSE, "5"),
            ("PostCharge_20", "Post a future non-credit non-linked expense before checkin", 400, "", "", "", "", "", "",
             before_test_actions.MULTIPLE_DAY_NON_CHECKED_IN_BOOKING_WITH_FUTURE_NON_CREDIT_NON_LINKED_EXPENSE, "5"),
            ("PostCharge_21", "Post a future credit non-linked expense before checkin", 400, "", "", "", "", "", "",
             before_test_actions.MULTIPLE_DAY_NON_CHECKED_IN_BOOKING_WITH_FUTURE_CREDIT_NON_LINKED_EXPENSE, "5"),
            ("PostCharge_22", "Post a future non-credit linked expense before checkin", 400, "", "", "", "", "", "",
             before_test_actions.MULTIPLE_DAY_NON_CHECKED_IN_WITH_FUTURE_NON_CREDIT_LINKED_EXPENSE, "5"),
            ("PostCharge_23", "Post a non-credit non-linked expense before checkin", 400, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_NON_CHECKED_IN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE, "2"),
            ("PostCharge_24", "Post a credit non-linked expense before checkin", 400, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_NON_CHECKED_IN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE, "2"),
            ("PostCharge_25", "Post a non-credit linked expense before checkin", 400, "", "", "", "", "",
             "", before_test_actions.SINGLE_DAY_NON_CHECKED_IN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE, "2"),
            ("PostCharge_26", "Post a credit linked expense before checkin", 400, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_NON_CHECKED_IN_BOOKING_WITH_LINKED_CREDIT_CHARGE, "2"),
            ("PostCharge_29", "Post a charge when hotel business date is equal to calendar date", 200, "", "", "",
             "", "", "", before_test_actions.SINGLE_BOOKING_CHECK_IN_01, "1"),
            ("PostCharge_30", "Post a charge when hotel business date is less than calendar date", 200, "", "", "",
             "", "", "", before_test_actions.SINGLE_BOOKING_CHECK_IN_01, "1"),
            ("PostCharge_31", "Post a charge when hotel business date is greater than calendar date", 200, "", "", "",
             "", "", "", before_test_actions.SINGLE_BOOKING_CHECK_IN_01, "1"),
            ("PostCharge_32", "Post a current date non-credit charge of OTA booking", 200, "", "", "", "", "", "",
             [{'id': "booking_212", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], "1"),
            ("PostCharge_33", "Edit commission then post a current date non-credit charge of OTA booking",
             200, "", "", "", "", "", "",
             [{'id': "booking_212", 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0},
              {'id': "checkin_01", 'type': 'checkin_v2'}], "1"),
        ])
    @pytest.mark.regression
    def test_post_charges_v2(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                             error_message, dev_message, error_payload, skip_case, previous_actions, charge_id):
        if skip_case:
            pytest.skip("skipped")

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        charge_id = charge_id if charge_id else self.expense_request.charge_id

        if test_case_id == 'PostCharge_30':
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))

        if test_case_id == 'PostCharge_31':
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() + timedelta(days=1), hotel_id))

        response = self.billing_request.edit_charge_v2_request(client_, test_case_id,
                                                               self.booking_request.bill_id, charge_id,
                                                               status_code, user_type)

        if test_case_id in ('PostCharge_30', 'PostCharge_31'):
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, charge_id,
                       self.booking_request.booking_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"


class TestCancelChargesV2(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, previous_actions, charge_id",
        [
            ("CancelCharge_01", "Cancel a non-posted room-stay non-credit charge before checkin", 200, "", "", "", "",
             "", "", [{'id': "Booking_01", 'type': 'booking'}], "1"),
            ("CancelCharge_02", "Cancel a non-posted room-stay credit charge before checkin", 200, "", "", "", "",
             "", "", [{'id': "Booking_11", 'type': 'booking'}], "1"),
            ("CancelCharge_03", "Cancel a non-posted room-stay non-credit charge", 200, "", "", "", "",
             "", "", before_test_actions.SINGLE_BOOKING_CHECK_IN_01, "1"),
            ("CancelCharge_04", "Cancel a non-posted room-stay credit charge", 200, "", "", "", "",
             "", "", before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_01, "1"),
            ("CancelCharge_05", "Cancel a posted non-credit charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'PostCharge_06', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("CancelCharge_06", "Cancel a posted non-credit charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE + [
                 {'id': 'PostCharge_07', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("CancelCharge_07", "Cancel a posted linked non-credit charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE + [
                 {'id': 'PostCharge_08', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("CancelCharge_08", "Cancel a posted linked credit charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE + [
                 {'id': 'PostCharge_09', 'type': 'update_expense', 'charge_id': '2'}], "2"),
            ("CancelCharge_09", "Cancel a non-posted linked non-credit charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE, "2"),
            ("CancelCharge_10", "Cancel a non-posted linked credit charge", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE, "2"),
            ("CancelCharge_11", "Cancel a non-posted non-credit expense", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE, "2"),
            ("CancelCharge_12", "Cancel a non-posted credit expense", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE, "2"),
            ("CancelCharge_15", "Cancel a non-posted room-stay non-credit charge of OTA Booking", 200, "", "", "", "",
             "", "", before_test_actions.SINGLE_ROOM_OTA_BOOKING_V2_01, "1"),
            ("CancelCharge_16", "Cancel a non-posted inclusion non-credit charge of OTA Booking", 200, "", "", "", "",
             "", "Need to fix", before_test_actions.SINGLE_ROOM_OTA_BOOKING_V2_01, "2"),
            ("CancelCharge_17", "Edit commission then cancel a non-posted room-stay non-credit charge of OTA Booking",
             200, "", "", "", "", "", "", [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}], "1"),
        ])
    @pytest.mark.regression
    def test_cancel_charges_v2(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                               error_message, dev_message, error_payload, skip_case, previous_actions, charge_id):
        if skip_case:
            pytest.skip("skipped")

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        charge_id = charge_id if charge_id else self.expense_request.charge_id

        response = self.billing_request.edit_charge_v2_request(client_, test_case_id,
                                                               self.booking_request.bill_id, charge_id,
                                                               status_code, user_type)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, charge_id,
                       self.booking_request.booking_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"


class TestEditChargesV2SlabBased(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message, dev_message, error_payload, "
        "skip_case, previous_actions, charge_id, has_slab_based_taxation, is_tax_clubbed",
        [
            ("EditCharge_56", "Split the non-credit room-stay charge where slab based taxation is true", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_WALK_BOOKING_V2_01_SLAB_BASED, "1", True, []),
            ("EditCharge_57", "Split the credit room-stay charge where slab based taxation is true", 200, "", "",
             "", "", "", "", [{'id': "booking_209", 'type': 'booking_v2', 'extras': {"has_slab_based_taxation": True}}],
             "1", True, []),
            ("EditCharge_58", "Split the clubbed charge where slab based taxation is true", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_WALK_BOOKING_V2_01_CLUBBED_AND_SLAB_BASED, "1", True, CLUBBED_TAX_CONFIG),
            ("EditCharge_59", "Split the consumed room stay charge where slab based taxation is true", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_BOOKING_CHECK_IN_V2_SLAB_BASED_02 +
             [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}], "1", True, []),
            ("EditCharge_60", "Split the cancelled room stay charge where slab based taxation is true", 200, "", "",
             "", "", "", "Need to fix", before_test_actions.SINGLE_WALK_BOOKING_V2_01_SLAB_BASED +
             [{'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '1'}], "1", True, []),
            ("EditCharge_61", "Split the room stay charge where slab based taxation is true after checkout", 400, "",
             "04010385", "Editing of invoiced charge is not allowed", "", "", "",
             before_test_actions.FULL_CHECKOUT_V2_SLAB_BASED, "1", True, []),
            ("EditCharge_62", "Split the room stay charge having allowance where slab based taxation is true", 400, "",
             "04010402", "Edit billing instructions not allowed if any charge split already has an allowance", "", "",
             "", before_test_actions.SINGLE_BOOKING_CHECK_IN_V2_SLAB_BASED_02 +
             [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'AddAllowance_02', 'type': 'add_allowance_v2', 'extras': [1, 1]}], "1", True, []),
            ("EditCharge_63", "Split the inclusion charge where slab based taxation is true", 200, "", "",
             "", "", "", "Need to fix", before_test_actions.SINGLE_WALK_BOOKING_V2_01_SLAB_BASED, "2", True, []),
            ("EditCharge_64", "Split the extra charge where slab based taxation of different category is true", 200, "",
             "", "", "", "", "", before_test_actions.SINGLE_WALK_BOOKING_V2_01_SLAB_BASED +
             [{'id': 'Multiple_Expense_01', 'type': 'create_expense_V3'}], "6", True, []),
            ("EditCharge_65", "Split the linked charge where slab based taxation is true", 200, "", "", "", "", "", "",
             before_test_actions.SINGLE_WALK_BOOKING_V2_01_SLAB_BASED +
             [{'id': "Create_Expense_34", 'type': 'expense'}], "6", True, []),
            ("EditCharge_66", "Edit Charge which have slab based taxation but not contain split", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_WALK_BOOKING_V2_01_SLAB_BASED, "1", True, []),
            ("EditCharge_67", "Edit Charge which have slab based taxation and clubbed charge but not contain split",
             200, "", "", "", "", "", "", before_test_actions.SINGLE_WALK_BOOKING_V2_01_CLUBBED_AND_SLAB_BASED, "1",
             True, CLUBBED_TAX_CONFIG),
            ("EditCharge_68", "Edit Charge which have clubbed charge but not contain split", 200, "", "", "", "", "",
             "", before_test_actions.SINGLE_WALK_BOOKING_V2_01_CLUBBED_CHARGE, "1", False, CLUBBED_TAX_CONFIG),
            ("EditCharge_69", "Edit Charge which have clubbed charge and contain split", 200, "", "", "", "", "", "",
             [{'id': "booking_206", 'type': 'booking_v2', 'extras': {"hotel_level_config": CLUBBED_TAX_CONFIG}},
              {'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1, 'is_tax_clubbed': CLUBBED_TAX_CONFIG}],
             "1", False, CLUBBED_TAX_CONFIG),
            ("EditCharge_70", "Edit Charge after charge splits which has slab based taxation", 400, "", "04010436",
             "Cannot edit this charge as this has splits and slabbed taxation.", "", "", "",
             before_test_actions.BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_01, "1", True, []),
            ("EditCharge_71", "Edit Billing Details change billing instruction with slab based taxation", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_WALK_BOOKING_V2_01_SLAB_BASED, "1", True, []),
            ("EditCharge_72",
             "Edit Billing Details change billing instruction after charge splits with slab based taxation", 200, "",
             "", "", "", "", "", before_test_actions.BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_01, "1", True, []),
            ("EditCharge_73",
             "Edit Billing Details change payment instruction after charge splits with slab based taxation", 200, "",
             "", "", "", "", "", before_test_actions.BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_05, "1", True, []),
            ("EditCharge_74", "Edit Billing Details change billing instruction and payment instruction after charge "
                              "splits with slab based taxation", 200, "", "", "", "", "", "",
             before_test_actions.BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_05, "1", True, []),
        ])
    @pytest.mark.regression
    def test_edit_charges_v2_slab_based(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                                        error_message, dev_message, error_payload, skip_case, previous_actions,
                                        charge_id, has_slab_based_taxation, is_tax_clubbed):
        if skip_case:
            pytest.skip("skipped")

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=True))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        charge_id = charge_id if charge_id else self.expense_request.charge_id

        response = self.billing_request.edit_charge_v2_request(client_, test_case_id,
                                                               self.booking_request.bill_id, charge_id, status_code,
                                                               user_type, has_slab_based_taxation, is_tax_clubbed)
        self.billing_request.get_bill_entity_request(client_, self.booking_request.bill_id, 200)

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=False))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, charge_id,
                       self.booking_request.booking_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"


class TestPostChargeV2SlabBased(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, previous_actions, charge_id, has_slab_based_taxation",
        [
            ("PostCharge_27", "Post the room-stay charge with split where slab based taxation is true", 200, "", "", "",
             "", "", "", before_test_actions.BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_04 +
             [{'id': "checkin_01", 'type': 'checkin_v2'}], "1", True),
            ("PostCharge_28", "Post the room-stay charge with split where slab based taxation is true", 200, "", "", "",
             "", "", "", before_test_actions.BOOKING_WITH_CHARGE_SPLITS_CLUBBED_AND_SLAB_BASED_02 +
             [{'id': "checkin_01", 'type': 'checkin_v2'}], "1", True),
        ])
    @pytest.mark.regression
    def test_post_charge_v2_slab_based(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                                       error_message, dev_message, error_payload, skip_case, previous_actions,
                                       charge_id, has_slab_based_taxation):
        if skip_case:
            pytest.skip("skipped")

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=True))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        charge_id = charge_id if charge_id else self.expense_request.charge_id

        response = self.billing_request.edit_charge_v2_request(client_, test_case_id,
                                                               self.booking_request.bill_id, charge_id,
                                                               status_code, user_type)

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=False))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, charge_id,
                       self.booking_request.booking_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"


class TestCancelChargeV2SlabBased(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, previous_actions, charge_id, has_slab_based_taxation",
        [
            ("CancelCharge_13", "Cancel the room-stay charge with split where slab based taxation is true", 200, "", "",
             "", "", "", "", before_test_actions.BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_04, "1", True),
            ("CancelCharge_14", "Cancel the room-stay clubbed charge with split where slab based taxation is true", 200,
             "", "", "", "", "", "", before_test_actions.BOOKING_WITH_CHARGE_SPLITS_CLUBBED_AND_SLAB_BASED_02, "1",
             True),
        ])
    @pytest.mark.regression
    def test_cancel_charge_v2_slab_based(self, client_, test_case_id, tc_description, status_code, user_type,
                                         error_code, error_message, dev_message, error_payload, skip_case,
                                         previous_actions, charge_id, has_slab_based_taxation):
        if skip_case:
            pytest.skip("skipped")

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=True))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        charge_id = charge_id if charge_id else self.expense_request.charge_id

        response = self.billing_request.edit_charge_v2_request(client_, test_case_id,
                                                               self.booking_request.bill_id, charge_id,
                                                               status_code, user_type)

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=False))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, charge_id,
                       self.booking_request.booking_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"


class TestEditChargesV2TaxOnBasisOfBE(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, previous_actions, charge_id",
        [
            ("EditCharge_84", "Edit pretax amount of room charge where BE is booker", 200, "", "",
             "", "", "", "", [{'id': "booking_273", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_85", "Edit pretax amount of room charge where BE is booker and slab get change", 200, "", "",
             "", "", "", "", [{'id': "booking_273", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_86", "Edit pretax amount of room charge where BE is TA, sez is true and lut is false", 200, "",
             "", "", "", "", "", [{'id': "booking_283", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_87", "Edit pretax amount of room charge where BE is company, sez is true and lut is false",
             200, "", "", "", "", "", "", [{'id': "booking_275", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_88", "Edit pretax amount of room charge where BE is guest", 200, "", "",
             "", "", "", "", [{'id': "booking_287", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_89", "Edit pretax amount of room charge where BE is company", 200, "", "",
             "", "", "", "", [{'id': "booking_280", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_90", "Edit pretax amount of room charge where BE is TA", 200, "", "",
             "", "", "", "", [{'id': "booking_281", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_91", "Edit billing instruction from TA to company where tax config is change", 200, "", "",
             "", "", "", "", [{'id': "booking_281", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_92", "Edit billing instruction from company to TA where tax config is change", 200, "", "",
             "", "", "", "", [{'id': "booking_280", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_93", "Edit billing instruction from company to guest where tax config is change", 200, "", "",
             "", "", "", "", [{'id': "booking_280", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_94", "Edit billing instruction from ta to guest where tax config is change", 200, "", "",
             "", "", "", "", [{'id': "booking_281", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_95", "Edit billing instruction from guest to ta where tax config is change", 200, "", "",
             "", "", "", "", [{'id': "booking_282", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_96", "Edit billing instruction from guest to company where tax config is change", 200, "", "",
             "", "", "", "", [{'id': "booking_282", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_97", "Edit billing instruction from guest to company where tax config doesn't change", 200, "",
             "", "", "", "", "", [{'id': "booking_edit_charge_03", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_98", "Edit billing instruction from guest to ta where tax config doesn't change", 200, "",
             "", "", "", "", "", [{'id': "booking_edit_charge_03", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_99", "Edit billing instruction from company to ta where tax config doesn't change", 200, "",
             "", "", "", "", "", [{'id': "booking_edit_charge_01", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_100", "Edit billing instruction from ta to company where tax config doesn't change", 200, "",
             "", "", "", "", "", [{'id': "booking_edit_charge_02", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_101", "Edit billing instruction from ta to guest where tax config doesn't change", 200, "",
             "", "", "", "", "", [{'id': "booking_edit_charge_02", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_102", "Edit billing instruction from company to guest where tax config doesn't change", 200, "",
             "", "", "", "", "", [{'id': "booking_edit_charge_01", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_103", "Split the charge between ta and company where tax config is same", 200, "", "", "", "",
             "", "", [{'id': "booking_edit_charge_01", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_104", "Split the charge between ta and guest where tax config is same", 200, "", "", "", "", "",
             "", [{'id': "booking_edit_charge_01", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_105", "Split the charge between company and guest where tax config is same", 200, "", "", "",
             "", "", "", [{'id': "booking_edit_charge_01", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_106", "Split the charge between ta and company where tax config is different", 400, "",
             "04015986", "Charge cannot be split to billed entities having different tax configs", "", "", "",
             [{'id': "booking_282", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_107", "Split the charge between ta and guest where tax config is different", 400, "",
             "04015986", "Charge cannot be split to billed entities having different tax configs", "", "", "",
             [{'id': "booking_282", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_108", "Split the charge between company and guest where tax config is different", 400, "",
             "04015986", "Charge cannot be split to billed entities having different tax configs", "", "", "",
             [{'id': "booking_282", 'type': 'booking_v2', 'new_tax_mocker': True}], "1"),
            ("EditCharge_109", "Edit billing instruction from ta to company for posted charge where tax config is "
                               "different", 400, "", "04015980",
             "Cannot update tax determiners as booking has non updatable charges",
             "charge is invoiced or consumed or allowance passed", "", "",
             [{'id': "booking_281", 'type': 'booking_v2', 'new_tax_mocker': True},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}], "1"),
            ("EditCharge_110", "Edit billing instruction from ta to company for posted charge where tax config is same",
             200, "", "", "", "", "", "",
             [{'id': "booking_edit_charge_02", 'type': 'booking_v2', 'new_tax_mocker': True},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}], "1"),
            ("EditCharge_111", "Edit billing instruction from guest to ta for extra charge where tax config is change",
             200, "", "", "", "", "", "",
             [{'id': "booking_282", 'type': 'booking_v2', 'new_tax_mocker': True},
              {'id': 'Multiple_Expense_118', 'type': 'create_expense_V3', 'new_tax_mocker': True}], "6"),
            ("EditCharge_112", "Split the charge between company and guest where tax config is different", 400, "",
             "04015986", "Charge cannot be split to billed entities having different tax configs", "", "", "",
             [{'id': "booking_282", 'type': 'booking_v2', 'new_tax_mocker': True},
              {'id': 'Multiple_Expense_118', 'type': 'create_expense_V3', 'new_tax_mocker': True}], "6"),
            ("EditCharge_113", "Edit billing instruction from ta to company for posted extra charge where tax config is "
                               "different", 400, "", "04015980",
             "Cannot update tax determiners as booking has non updatable charges",
             "charge is invoiced or consumed or allowance passed", "", "",
             [{'id': "booking_281", 'type': 'booking_v2', 'new_tax_mocker': True},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Multiple_Expense_117', 'type': 'create_expense_V3', 'new_tax_mocker': True},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6}], "6"),
            ("EditCharge_114", "Edit billing instruction from ta to company for posted extra charge where tax config is "
                               "same", 200, "", "", "", "", "", "",
             [{'id': "booking_edit_charge_02", 'type': 'booking_v2', 'new_tax_mocker': True},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Multiple_Expense_116', 'type': 'create_expense_V3', 'new_tax_mocker': True},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6}], "6"),
        ])
    @pytest.mark.regression
    def test_edit_charges_v2_tax_on_basis_of_be(self, client_, test_case_id, tc_description, status_code, user_type,
                                                error_code, error_message, dev_message, error_payload, skip_case,
                                                previous_actions, charge_id):
        if skip_case:
            pytest.skip("skipped")

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        charge_id = charge_id if charge_id else self.expense_request.charge_id

        response = self.billing_request.edit_charge_v2_request(client_, test_case_id,
                                                               self.booking_request.bill_id, charge_id,
                                                               status_code, user_type, new_tax_mocker=True)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, charge_id,
                       self.booking_request.booking_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"


def validation(billing_request, test_case_id, client_, bill_id, charge_id, booking_id, expense_request):
    response_validation = ValidationEditChargesV2(client_, test_case_id, bill_id, billing_request)
    response_validation.validate_response(charge_id)
    response_validation.validate_billed_entity_response()
    response_validation.validate_charge_and_expense_status(expense_request, booking_id)
    response_validation.validate_commissions()
