import pytest

from prometheus.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES
from prometheus.integration_tests.tests.booking.validations.validation_bulk_addon import BulkAddOnValidations
from ths_common.constants.catalog_constants import SellerType
from prometheus.integration_tests.tests import before_test_actions
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.tests.mockers import mock_tax_calculator_service, mock_catalog_client


class TestCreateBulkAddOnV2(BaseTest):
    @pytest.mark.parametrize("test_case_id, tc_description, previous_actions, status_code, user_type, error_code,"
                             " dev_message, error_payload, skip_case, skip_message",
                             [
                                 # we are no longer creating addons on checkout, hence skiping will add cases for checkout-1 for all scenarios
                                 ("Bulk_add_ons_01",
                                  "Create bulk add-ons on a single booking created state with absolute dates",
                                  before_test_actions.SINGLE_BOOKING_01, 200, None, "", "", "", True, ""),
                                 ("Bulk_add_ons_02",
                                  "Create bulk add-ons on a single two days booking with relative dates",
                                  before_test_actions.SINGLE_BOOKING_02, 200, None, "", "", "", True, ""),
                                 ("Bulk_add_ons_03", "Create bulk add-ons for a multiple guests booking with two rooms",
                                  [{'id': "Booking_04", 'type': 'booking'}], 200, None, "", "", "", True, ""),
                                 ("Bulk_add_ons_04", "Create a single add-on on a single two days booking",
                                  before_test_actions.SINGLE_BOOKING_02, 200, None, "", "", "", True, ""),
                                 ("Bulk_add_ons_05",
                                  "Create a single add-on on a single booking with wrong room_stay_id",
                                  before_test_actions.SINGLE_BOOKING_02, 404, None, "04010004", "", "", False, ""),
                                 ("Bulk_add_ons_06",
                                  "Create bulk add-ons on a single multiple days booking created state with both absolute and "
                                  "relative dates", [{'id': "Booking_84", 'type': 'booking'}], 200, None, "", "", "",
                                  True, ""),
                                 ("Bulk_add_ons_07",
                                  "Create bulk add-ons on a single booking as rate plan addons without sku_id",
                                  [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True}], 200, None, "",
                                  "", "", True, ""),
                                 ("Bulk_add_ons_08",
                                  "Create bulk add-ons on a single booking as rate plan addons without expense_item_id",
                                  [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True}], 400, None, "",
                                  "", "", False, ""),
                                 ("Bulk_add_ons_09",
                                  "Create bulk add-ons on a single booking as rate plan addons with expense_item_id and sku_id",
                                  [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True}], 200, None, "",
                                  "", "", True, ""),
                                 ("Bulk_add_ons_10",
                                  "Create bulk add-ons on a single booking as rate plan addons with quantity as 0 and without "
                                  "sku_id", [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True}], 400,
                                  None, "", "", "", False, ""),
                                 ("Bulk_add_ons_11",
                                  "Create bulk add-ons on a single booking as rate plan addons with quantity as -1 and"
                                  " without sku_id",
                                  [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True}], 400,
                                  None, "", "", "", False, ""),
                                 ("Bulk_add_ons_12",
                                  "Create bulk add-ons on a single booking as rate plan addons with quantity as 1 for"
                                  " credit to company",
                                  [{'id': "Booking_11", 'type': 'booking', 'enable_rate_manager': True}],
                                  200, None, "", "", "", True, ""),
                                 ("Bulk_add_ons_13",
                                  "Create bulk add-ons on a single booking as rate plan addons without expense_item_id"
                                  " for credit to company",
                                  [{'id': "Booking_11", 'type': 'booking', 'enable_rate_manager': True}], 400, None, "",
                                  "", "",
                                  False, ""),
                                 ("Bulk_add_ons_14",
                                  "Create bulk add-ons on a single booking as rate plan addons with expense_item_id and"
                                  " sku_id for credit to company",
                                  [{'id': "Booking_11", 'type': 'booking', 'enable_rate_manager': True}], 200, None, "",
                                  "", "", True, ""),
                                 ("Bulk_add_ons_15",
                                  "Create bulk add-ons on a single booking as rate plan addons with quantity as 0 "
                                  "and without sku_id for credit to company",
                                  [{'id': "Booking_11", 'type': 'booking', 'enable_rate_manager': True}], 400,
                                  None, "", "", "", False, ""),
                                 ("Bulk_add_ons_16",
                                  "Create bulk add-ons on a single booking as rate plan addons with quantity as -1 and "
                                  "without sku_id for credit to company",
                                  [{'id': "Booking_11", 'type': 'booking', 'enable_rate_manager': True}], 400,
                                  None, "", "", "", False, ""),
                                 ("Bulk_add_ons_17",
                                  "Create bulk add-ons on a single booking as rate plan addons with quantity as 0 and"
                                  " without expense_item_id for credit to company",
                                  [{'id': "Booking_11", 'type': 'booking', 'enable_rate_manager': True}], 400,
                                  None, "", "", "", False, ""),
                                 ("Bulk_add_ons_18",
                                  "Create bulk add-ons on a single booking as rate plan addons with quantity as 0 and"
                                  " without expense_item_id for non-credit to guest",
                                  [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True}], 400,
                                  None, "", "", "", False, "")
                             ])
    @pytest.mark.regression
    def test_create_bulk_add_on_v2(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                   user_type, error_code, dev_message, error_payload, skip_case,
                                   skip_message):
        if skip_case:
            pytest.skip(skip_message)
        with mock_tax_calculator_service() and mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            if previous_actions:
                self.common_request_caller(client_, previous_actions)

        response = self.booking_request.create_bulk_addon_v2(client_, test_case_id,
                                                             self.booking_request.booking_id, status_code, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_)
        else:
            assert False, "Response status code is not matching"

    def validation(self, response, test_case_id, client_):
        validation = BulkAddOnValidations(test_case_id)
        validation.validate_response(response, client_, self.booking_request, self.booking_request.booking_id
                                     , self.billing_request, self.booking_request.bill_id)
