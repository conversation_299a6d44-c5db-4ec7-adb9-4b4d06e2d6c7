import pytest
from prometheus.integration_tests.tests import before_test_actions
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.booking.validations.validation_add_allowance import *


class TestAllowance(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, previous_actions", [
            ("Add_Allowance_01", "Add allowance on booked charge", 400, "", "04010398",
             "", "", "", "", before_test_actions.Checkin_Booking_Expense01),
            ("Add_Allowance_02", "Add an allowance on posted charge with partial allowance", 200, "", "",
             "", "", "", "", before_test_actions.Posted_Charges02),
            ("Add_Allowance_03", "Add an allowance on posted charge with full allowance", 200, "", "",
             "", "", "", "", before_test_actions.Posted_Charges02),
            ("Add_Allowance_04", "Add an allowance on posted charge with more than total expense charge", 400, "", "",
             "", "", "", "", before_test_actions.Posted_Charges02),
            ("Add_Allowance_05", "Add allowance on booked charge with amount greater than expense amount", 400, "",
             "04010398", "", "", "", "", before_test_actions.Checkin_Booking_Expense01),
            ("Add_Allowance_08", "Add allowance on invoiced charge ", 200, "", "",
             "", "", "", "", before_test_actions.FULL_CHECKOUT_03),
            ("Add_Allowance_09", "Add allowance on booked charge as posttax", 400, "", "04010398",
             "", "", "", "", before_test_actions.Checkin_Booking_Expense01),
            ("Add_Allowance_10", "Add an allowance on posted charge with partial allowance as posttax", 200, "", "",
             "", "", "", "", before_test_actions.Posted_Charges02),
            ("Add_Allowance_11", "Add an allowance on posted charge with full allowance as posttax", 200, "", "",
             "", "", "", "", before_test_actions.Posted_Charges02),
            ("Add_Allowance_12", "Add an allowance on posted charge with more than total expense charge as posttax",
             400, "", "", "", "", "", "", before_test_actions.Posted_Charges02),
            ("Add_Allowance_13", "Add allowance on booked charge with amount greater than expense amount as posttax",
             400, "", "04010398", "", "", "", "", before_test_actions.Checkin_Booking_Expense01),
            ("Add_Allowance_16", "Add allowance on invoiced charge as posttax", 200, "", "",
             "", "", "", "", before_test_actions.FULL_CHECKOUT_03),
        ])
    @pytest.mark.regression
    def test_allowance(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                       error_message, dev_message, error_payload, skip_case, previous_actions,
                       room_allotment_repo=None):
        if skip_case:
            pytest.skip(skip_case)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'

        self.expense_request.charge_id = None

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.charge_request.add_allowance(client_, test_case_id, self.expense_request.charge_id,
                                                     status_code, self.billing_request, self.booking_request.bill_id,
                                                     user_type)

        if status_code in (400, 403):
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in (200, 201):
            self.validation(client_, response, test_case_id)
        else:
            assert False, "Response status code is not matching"

    def validation(self, client, response, test_case_id):
        validation = ValidationAddAllowance(client, response, test_case_id)
        validation.validate_response(self.billing_request)
