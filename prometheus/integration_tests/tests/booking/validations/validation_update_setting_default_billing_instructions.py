import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.tests.base_validations import BaseValidations


class ValidationUpdateSettingDefaultBillingInstructions(BaseValidations):
    def __init__(self, client, response, test_case_id, booking_id, billing_request, bill_id, booking_request):
        self.test_data = get_test_case_data(sheet_names.update_default_billing_instructions_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client
        self.booking_id = booking_id
        self.billing_request = billing_request
        self.bill_id = bill_id
        self.booking_request = booking_request

    def validate_response(self):
        assert_(self.response['data']['booking_id'], self.booking_id)
        assert_(self.response['data']['default_billed_entity_category'],
                self.test_data['expected_default_billed_entity_category'])
        assert_(self.response['data']['default_billed_entity_category_for_extras'],
                self.test_data['expected_default_billed_entity_category_for_extras'])
        assert_(self.response['data']['default_payment_instruction'],
                self.test_data['expected_default_payment_instruction'])
        assert_(self.response['data']['default_payment_instruction_for_extras'],
                self.test_data['expected_default_payment_instruction_for_extras'])

    def validate_billed_entity_response(self):
        actual_billed_entity_response = self.billing_request.get_bill_entity_request(self.client, self.bill_id, 200)
        expected_billed_entity_response = json.loads(self.test_data['expected_billed_entity_response'])
        actual_billed_entity_response_sorted = sorted(actual_billed_entity_response['data'],
                                                      key=lambda i: i['billed_entity_id'])
        expected_billed_entity_response_sorted = sorted(expected_billed_entity_response,
                                                        key=lambda i: i['billed_entity_id'])
        self.validate_billed_entity(actual_billed_entity_response_sorted, expected_billed_entity_response_sorted)

    def validate_audit_trail(self):
        actual_audit_trail_response = self.booking_request.get_booking_audit_trail(self.client, self.booking_id)
        actual_audit_type = []
        if sanitize_test_data(self.test_data['expected_audit_trail']):
            expected_audit_trail = json.loads(self.test_data['expected_audit_trail'])
            for actual_audit_trail in actual_audit_trail_response['data']:
                actual_audit_type.append(actual_audit_trail['audit_type'])
                if actual_audit_trail['audit_type'] == 'Booking Details Modified':
                    assert_(actual_audit_trail['application'], expected_audit_trail['application'])
                    assert_(actual_audit_trail['user'], expected_audit_trail['user'])
                    assert_(actual_audit_trail['user_type'], expected_audit_trail['user_type'])
                    actual_event_type = []
                    for actual_domain_events, expected_domain_events in zip(
                            actual_audit_trail['audit_payload']['domain_events'],
                            expected_audit_trail['audit_payload']['domain_events']):
                        actual_event_type.append(actual_domain_events['event_type'])
                        if actual_domain_events['event_type'] == 'Booking Details Modified':
                            for actual_detail, expected_detail in zip(actual_domain_events['event_detail'],
                                                                      expected_domain_events['event_detail']):
                                assert_(actual_detail['attribute'], expected_detail['attribute'])
                                assert_(actual_detail['new_value'], expected_detail['new_value'])
                                assert_(actual_detail['old_value'], expected_detail['old_value'])
                    assert ('Booking Details Modified' in actual_event_type)
            assert ('Booking Details Modified' in actual_audit_type)

    def validate_charge_and_expense_status(self, expense_request):
        expenses_response = expense_request.get_expense_detail(self.client, 200, self.booking_id)['data']
        for expense in expenses_response:
            get_charge_detail = self.billing_request.get_charge_request(self.client, self.bill_id, 200,
                                                                        expense['charge_id'])['data']
            if expense['status'] == 'cancelled' or get_charge_detail['status'] == 'cancelled':
                assert_(expense['status'], get_charge_detail['status'])

    def validate_default_billed_category(self):
        is_sez, has_lut, is_calculated = False, False, False
        booking = self.booking_request.get_booking_request_v2(self.client, self.booking_id, 200)['data']
        company_billed_entity_id = [bc['company_billed_entity_id'] for bc in booking['customers'] if
                                    bc.get('company_billed_entity_id')]
        if booking.get('company_details') and int(company_billed_entity_id[
                                                      0]) == booking.get('company_details').get('billed_entity_id'):
            is_sez = booking.get('company_details').get('legal_details').get('is_sez')
            has_lut = booking.get('company_details').get('legal_details').get('has_lut')

        if booking.get('travel_agent_details') and int(company_billed_entity_id[
                                                           0]) == booking.get('travel_agent_details').get(
            'billed_entity_id'):
            is_sez = booking.get('travel_agent_details').get('legal_details').get('is_sez')
            has_lut = booking.get('travel_agent_details').get('legal_details').get('has_lut')

        assert_([is_sez, has_lut], [False, False])

    def validate_charges(self):
        if sanitize_test_data(self.test_data['expected_charge_details']):
            actual_charge_details = self.billing_request.get_bill_charges(self.client, self.bill_id, 200)['data']
            expected_charge_details = json.loads(self.test_data['expected_charge_details'])
            sorted_actual_charge_details = sorted(actual_charge_details, key=lambda i: i['charge_id'])
            sorted_expected_charge_details = sorted(expected_charge_details, key=lambda i: i['charge_id'])
            for actual_charge_detail, expected_charge_detail in zip(sorted_actual_charge_details,
                                                                    sorted_expected_charge_details):
                actual_charge_detail['applicable_date'] = actual_charge_detail['applicable_date'].split('T')[0]
                expected_charge_detail['applicable_date'] = str(return_date(expected_charge_detail['applicable_date']))
                self.validate_charge(actual_charge_detail, expected_charge_detail)
