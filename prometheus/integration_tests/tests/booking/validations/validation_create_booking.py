from prometheus.integration_tests.config.sheet_names import billed_entity_validation_sheet_name
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.config import common_config
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import assert_, increment_date, get_room_type_id, \
    sanitize_blank, return_date, sanitize_test_data
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.tests.base_validations import BaseValidations
import json


class ValidationCreateBooking(BaseValidations):

    def __init__(self, client_, test_case_id, response, bill_id, billing_request, extra_data=None, hotel_id=None):
        self.test_data = get_test_case_data(sheet_names.new_booking_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.bill_id = bill_id
        self.extra_data = extra_data
        self.hotel_id = hotel_id
        self.billing_request = billing_request

    def validate_response(self, billing_request, customer_request):
        sheet_room_stays = get_test_case_data(sheet_names.add_room_stay_sheet_name, self.test_data['room_stays'])
        api_room_stays = self.response['data']['room_stays']
        for (sheet_room_stay, api_room_stay) in zip(sheet_room_stays, api_room_stays):
            stay_duration = int(sheet_room_stay['checkout_date']) - int(sheet_room_stay['checkin_date'])
            for date_wise_charge_id, date in zip(api_room_stay['date_wise_charge_ids'], range(stay_duration)):
                charge_response = billing_request.get_charge_request(self.client, self.bill_id, 200,
                                                                     date_wise_charge_id['charge_id'])
                self.validate_charges(charge_response, sheet_room_stay, date)
        assert_(set(self.response['data']['allowed_actions']),
                set(self.test_data['expected_allowed_actions'].split(',')))
        assert_(self.response['data']['checkin_date'], increment_date(int(self.test_data['expected_Checkin']),
                                                                      common_config.CHECK_IN_TIME_ZONE))
        assert_(self.response['data']['checkout_date'], increment_date(int(self.test_data['expected_Checkout']),
                                                                       common_config.CHECKOUT_TIME_ZONE))
        if sanitize_test_data(self.response['data']['comments']):
            assert_(self.response['data']['comments'], self.test_data['comments'])
        assert_(self.response['data']['hotel_id'], self.hotel_id)
        assert_(self.response['data']['seller_model'], self.test_data['expected_seller_model'])
        assert_(self.response['data']['source']['application_code'], self.test_data['application_code'])
        assert_(self.response['data']['source']['channel_code'], self.test_data['channel_code'])
        assert_(self.response['data']['source']['subchannel_code'], self.test_data['subchannel_code'])
        assert_(self.response['data']['status'], self.test_data['status_expected'])
        assert_(self.response['data']['stay_end'], str(return_date(self.test_data['expected_Checkout'])))
        assert_(self.response['data']['stay_start'], str(return_date(self.test_data['expected_Checkin'])))
        if self.test_data['TC_ID'] not in ['Booking_30', 'Booking_66', 'Booking_67']:
            actual_booking_owner_data = customer_request.get_customer_request(self.client, 200,
                                                                              self.response['data']['booking_id'],
                                                                              self.response['data'][
                                                                                  'booking_owner_id'])['data']
            expected_booking_owner_data = get_test_case_data(sheet_names.customer_data_sheet_name,
                                                             self.test_data['booking_owner'])[0]
            BaseValidations().validate_customer_details(actual_booking_owner_data, expected_booking_owner_data, False)

        if sanitize_test_data(self.test_data['default_billed_entity_category']):
            assert_(self.response['data']['default_billed_entity_category'],
                    self.test_data['default_billed_entity_category'])

        if sanitize_test_data(self.test_data['travel_agent_details']):
            expected_travel_agent_legal_details = json.loads(self.test_data['travel_agent_details'])['legal_details']
            actual_travel_agent_legal_details = self.response['data']['travel_agent_details']['legal_details']
            assert_(actual_travel_agent_legal_details['address']['city'],
                    expected_travel_agent_legal_details['address']['city'])
            assert_(actual_travel_agent_legal_details['address']['country'],
                    expected_travel_agent_legal_details['address']['country'])
            assert_(actual_travel_agent_legal_details['address']['field_1'],
                    expected_travel_agent_legal_details['address']['field_1'])
            assert_(actual_travel_agent_legal_details['address']['field_2'],
                    expected_travel_agent_legal_details['address']['field_2'])
            assert_(actual_travel_agent_legal_details['address']['pincode'],
                    expected_travel_agent_legal_details['address']['pincode'])
            assert_(actual_travel_agent_legal_details['address']['state'],
                    expected_travel_agent_legal_details['address']['state'])
            assert_(actual_travel_agent_legal_details['legal_name'],
                    expected_travel_agent_legal_details['legal_name'])

    def validate_charges(self, actual_charge_data, sheet_room_stay_data, date):
        if self.extra_data is not None and 'include_kerala_cess' in self.extra_data and self.extra_data[
            'include_kerala_cess']:
            # assert_(actual_charge_data['data']['tax_details'], self.test_data['tax_detail'])
            expected_charge_data = \
                get_test_case_data(sheet_names.room_stay_price_sheet_name, sheet_room_stay_data['Prices'])[date]
            assert_(float(actual_charge_data['data']['posttax_amount']),
                    float(expected_charge_data['kerala_case_expected_posttax']))
            assert_(actual_charge_data['data']['bill_to_type'], expected_charge_data['bill_to_type'])
            assert_(actual_charge_data['data']['type'], expected_charge_data['type'])
            assert_(float(actual_charge_data['data']['tax_amount']),
                    float(expected_charge_data['kerala_case_expected_tax']))
            assert_(actual_charge_data['data']['status'], expected_charge_data['expected_status'])
            assert_(str(actual_charge_data['data']['tax_details']),
                    expected_charge_data['kerala_case_expected_tax_details'])

        else:
            # assert_(str(actual_charge_data['data']['tax_details']), self.test_data['tax_detail'])
            expected_charge_data = \
                get_test_case_data(sheet_names.room_stay_price_sheet_name, sheet_room_stay_data['Prices'])[date]
            assert_(float(actual_charge_data['data']['posttax_amount']),
                    float(expected_charge_data['expected_posttax']))
            assert_(actual_charge_data['data']['bill_to_type'], expected_charge_data['bill_to_type'])
            assert_(actual_charge_data['data']['type'], expected_charge_data['type'])
            assert_(float(actual_charge_data['data']['tax_amount']), float(expected_charge_data['expected_tax']))
            assert_(actual_charge_data['data']['status'], expected_charge_data['expected_status'])
            assert_(str(actual_charge_data['data']['tax_details']), expected_charge_data['expected_tax_details'])

    def validate_roomstay_details(self, customer_request):
        room_test_data = get_test_case_data(sheet_names.add_room_stay_sheet_name, self.test_data['room_stays'])
        for actual_room_data, expected_room_data in zip(self.response['data']['room_stays'], room_test_data):
            assert_(actual_room_data['checkin_date'], increment_date(int(expected_room_data['checkin_date']),
                                                                     common_config.CHECK_IN_TIME_ZONE))
            assert_(actual_room_data['checkout_date'], increment_date(int(expected_room_data['checkout_date']),
                                                                      common_config.CHECKOUT_TIME_ZONE))
            assert_(actual_room_data['room_type_id'], get_room_type_id(expected_room_data['room_type_id']))
            assert_(actual_room_data['type'], expected_room_data['type'])
            if expected_room_data['room_stay_id_forAction']:
                assert_(actual_room_data['room_stay_id'], int(expected_room_data['room_stay_id_forAction']))
            assert_(set(actual_room_data['allowed_actions']),
                    set(expected_room_data['expected_allowed_actions'].split(',')))
            if expected_room_data['expected_booking_status']:
                assert_(actual_room_data['status'], expected_room_data['expected_booking_status'])
            expected_prices_data = get_test_case_data(sheet_names.add_prices_sheet_name, expected_room_data['Prices'])
            expected_guest_stays_data = get_test_case_data(sheet_names.add_guest_stay_sheet_name,
                                                           expected_room_data['GuestStay'])
            self.validate_room_stay_price_data(actual_room_data['room_rents'], expected_prices_data)
            self.validate_guest_stays_data(actual_room_data['guest_stays'], expected_guest_stays_data, customer_request)
            if actual_room_data['room_rate_plans']:
                self.validate_room_rate_plan_data(actual_room_data['room_rate_plans'], expected_prices_data)

    def validate_room_rate_plan_data(self, actual_room_rate_plan, expected_price_data):
        for actual_room_rate_plan, expected_price_data in zip(actual_room_rate_plan, expected_price_data):
            assert_(actual_room_rate_plan['stay_date'], str(return_date(expected_price_data['applicable_date'])))

    def validate_room_stay_price_data(self, actual_prices_data, expected_prices_data):
        for actual_price_data, expected_price_data in zip(actual_prices_data, expected_prices_data):
            assert_(actual_price_data['applicable_date'], str(return_date(expected_price_data['applicable_date'])))
            if self.extra_data is not None and 'include_kerala_cess' in self.extra_data and self.extra_data[
                'include_kerala_cess']:
                assert_(float(actual_price_data['posttax_amount']),
                        float(expected_price_data['kerala_case_expected_posttax']))
            else:
                assert_(float(actual_price_data['posttax_amount']), float(expected_price_data['expected_posttax']))

    def validate_guest_stays_data(self, actual_guest_stays_data, expected_guest_stays_data, customer_request):
        for actual_guest_stay_data, expected_guest_stay_data in zip(actual_guest_stays_data, expected_guest_stays_data):
            assert_(actual_guest_stay_data['age_group'], expected_guest_stay_data['age_group'])
            assert_(set(actual_guest_stay_data['allowed_actions']),
                    set(expected_guest_stay_data['expected_allowed_actions'].split(',')))
            assert_(actual_guest_stay_data['checkin_date'],
                    increment_date(int(expected_guest_stay_data['checkin_date']),
                                   common_config.CHECK_IN_TIME_ZONE))
            assert_(actual_guest_stay_data['checkout_date'],
                    increment_date(int(expected_guest_stay_data['checkout_date']),
                                   common_config.CHECKOUT_TIME_ZONE))
            assert_(actual_guest_stay_data['status'], expected_guest_stay_data['expected_status'])
            assert_(actual_guest_stay_data['stay_end'], str(return_date(expected_guest_stay_data['checkout_date'])))
            assert_(actual_guest_stay_data['stay_start'], str(return_date(expected_guest_stay_data['checkin_date'])))
            if sanitize_test_data(expected_guest_stay_data['guest_details']):
                actual_guest_details = customer_request.get_customer_request(self.client, 200,
                                                                             self.response['data']['booking_id'],
                                                                             actual_guest_stay_data['guest_allocation']
                                                                             ['guest_id'])['data']
                expected_guest_details = get_test_case_data(sheet_names.customer_data_sheet_name,
                                                            expected_guest_stay_data['guest_details'])[0]
                BaseValidations().validate_customer_details(actual_guest_details, expected_guest_details, False)

    def validate_payment(self, billing_request):
        if sanitize_blank(self.test_data['payments']):
            expected_payment_data = get_test_case_data(sheet_names.add_payment_sheet_name, self.test_data['payments'])[
                0]
            actual_payment_data = billing_request.get_bill_request(self.client, self.bill_id, 200,
                                                                   user_type=None)['data']['payments'][0]
            assert_(actual_payment_data['amount'], expected_payment_data['amount'])
            assert_(actual_payment_data['comment'], expected_payment_data['comment'])
            assert_(actual_payment_data['date_of_payment'],
                    increment_date(int(expected_payment_data['date_of_payment']),
                                   common_config.CHECK_IN_TIME_ZONE))
            assert_(actual_payment_data['paid_by'], expected_payment_data['paid_by'])
            assert_(actual_payment_data['paid_to'], expected_payment_data['paid_to'])
            assert_(actual_payment_data['payment_channel'], expected_payment_data['payment_channel'])
            assert_(actual_payment_data['payment_mode'], expected_payment_data['payment_mode'])
            assert_(actual_payment_data['payment_type'], expected_payment_data['payment_type'])
            assert_(actual_payment_data['status'], expected_payment_data['status'])
            assert_(actual_payment_data['payment_ref_id'], expected_payment_data['payment_ref_id'])
            if expected_payment_data['payment_mode_sub_type']:
                assert_(actual_payment_data['payment_mode_sub_type'], expected_payment_data['payment_mode_sub_type'])

    def validate_data(self, billing_request):
        expected_bill_response = self.test_data
        response = billing_request.get_bill_v2_request(self.client, self.bill_id, 200, user_type=None)['data']

        # validation of charge
        for abr_index, abr in enumerate(response['charges']):
            expected_charge_details = json.loads(expected_bill_response['expected_charge_details'])[abr_index]
            assert_(abr['bill_to_type'], expected_charge_details['bill_to_type'])
            assert_(abr['charge_id'], expected_charge_details['charge_id'])
            assert_(abr['comment'], expected_charge_details['comment'])
            assert_(abr['is_inclusion_charge'], expected_charge_details['is_inclusion_charge'])
            # have to add for inclusion_charge_ids and linked_addon_charge_ids
            assert_(abr['posttax_amount'], expected_charge_details['posttax_amount'])
            assert_(abr['posttax_amount_post_allowance'], expected_charge_details['posttax_amount_post_allowance'])
            assert_(abr['pretax_amount'], expected_charge_details['pretax_amount'])
            assert_(abr['pretax_amount_post_allowance'], expected_charge_details['pretax_amount_post_allowance'])
            assert_(abr['split_allowed'], expected_charge_details['split_allowed'])
            assert_(abr['status'], expected_charge_details['status'])
            assert_(abr['tax_amount'], expected_charge_details['tax_amount'])
            assert_(abr['tax_amount_post_allowance'], expected_charge_details['tax_amount_post_allowance'])
            assert_(abr['type'], expected_charge_details['type'])
            for td_index, td in enumerate(abr['tax_details']):
                assert_(td['amount'], expected_charge_details['tax_details'][td_index]['amount'])
                assert_(td['percentage'], expected_charge_details['tax_details'][td_index]['percentage'])
                assert_(td['tax_type'], expected_charge_details['tax_details'][td_index]['tax_type'])
            for tdpa_index, tdpa in enumerate(abr['tax_details_post_allowance']):
                assert_(tdpa['amount'], expected_charge_details['tax_details'][tdpa_index]['amount'])
                assert_(tdpa['percentage'], expected_charge_details['tax_details'][tdpa_index]['percentage'])
                assert_(tdpa['tax_type'], expected_charge_details['tax_details'][tdpa_index]['tax_type'])

            # validation of charge splits
            for cs_index, cs in enumerate(abr['charge_splits']):
                # have to write for allowance
                assert_(cs['bill_to_type'], expected_charge_details['charge_splits'][cs_index]['bill_to_type'])
                assert_(cs['billed_entity_account']['account_number'],
                        expected_charge_details['charge_splits'][cs_index]['billed_entity_account']['account_number'])
                assert_(cs['billed_entity_account']['billed_entity_id'],
                        expected_charge_details['charge_splits'][cs_index]['billed_entity_account']['billed_entity_id'])
                assert_(cs['charge_split_id'], expected_charge_details['charge_splits'][cs_index]['charge_split_id'])
                assert_(cs['charge_type'], expected_charge_details['charge_splits'][cs_index]['charge_type'])
                assert_(cs['credit_note_id'], expected_charge_details['charge_splits'][cs_index]['credit_note_id'])
                assert_(cs['invoice_id'], expected_charge_details['charge_splits'][cs_index]['invoice_id'])
                assert_(cs['payment_id'], expected_charge_details['charge_splits'][cs_index]['payment_id'])
                assert_(cs['percentage'], expected_charge_details['charge_splits'][cs_index]['percentage'])
                assert_(cs['post_tax'], expected_charge_details['charge_splits'][cs_index]['post_tax'])
                assert_(cs['posttax_amount_post_allowance'],
                        expected_charge_details['charge_splits'][cs_index]['posttax_amount_post_allowance'])
                assert_(cs['pre_tax'], expected_charge_details['charge_splits'][cs_index]['pre_tax'])
                assert_(cs['pretax_amount_post_allowance'],
                        expected_charge_details['charge_splits'][cs_index]['pretax_amount_post_allowance'])
                assert_(cs['tax'], expected_charge_details['charge_splits'][cs_index]['tax'])
                assert_(cs['tax_amount_post_allowance'],
                        expected_charge_details['charge_splits'][cs_index]['tax_amount_post_allowance'])
                for td_index, td in enumerate(cs['tax_details']):
                    assert_(td['amount'], expected_charge_details['tax_details'][td_index]['amount'])
                    assert_(td['percentage'], expected_charge_details['tax_details'][td_index]['percentage'])
                    assert_(td['tax_type'], expected_charge_details['tax_details'][td_index]['tax_type'])
                for tdpa_index, tdpa in enumerate(cs['tax_details_post_allowance']):
                    assert_(tdpa['amount'], expected_charge_details['tax_details'][tdpa_index]['amount'])
                    assert_(tdpa['percentage'], expected_charge_details['tax_details'][tdpa_index]['percentage'])
                    assert_(tdpa['tax_type'], expected_charge_details['tax_details'][tdpa_index]['tax_type'])

        # validation of payments
        for apr_index, apr in enumerate(response['payments']):
            expected_payment_details = json.loads(expected_bill_response['expected_payment_details'])[apr_index]
            assert_(apr['amount'], expected_payment_details['amount'])
            assert_ (apr['amount_in_payment_currency'], expected_payment_details['amount_in_payment_currency'])
            assert_ (apr['comment'], expected_payment_details['comment'])
            assert_ (apr['confirmed'], expected_payment_details['confirmed'])
            assert_ (apr['payer'], expected_payment_details['payer'])
            assert_ (apr['paid_by'], expected_payment_details['paid_by'])
            assert_ (apr['paid_to'], expected_payment_details['paid_to'])
            assert_ (apr['payment_channel'], expected_payment_details['payment_channel'])
            assert_ (apr['payment_details'], expected_payment_details['payment_details'])
            assert_ (apr['payment_id'], expected_payment_details['payment_id'])
            assert_ (apr['payment_mode'], expected_payment_details['payment_mode'])
            assert_ (apr['payment_ref_id'], expected_payment_details['payment_ref_id'])
            assert_ (apr['payment_mode_sub_type'], expected_payment_details['payment_mode_sub_type'])
            assert_ (apr['payment_type'], expected_payment_details['payment_type'])

            # validation of payment splits
            for ps_index, ps in enumerate(apr['payment_splits']):
                assert_(ps['amount'], expected_payment_details['payment_splits'][ps_index]['amount'])
                assert_(ps['payment_mode'], expected_payment_details['payment_splits'][ps_index]['payment_mode'])
                assert_(ps['payment_split_id'],
                        expected_payment_details['payment_splits'][ps_index]['payment_split_id'])
                assert_(ps['payment_type'], expected_payment_details['payment_splits'][ps_index]['payment_type'])
                assert_(ps['payment_type'], expected_payment_details['payment_splits'][ps_index]['payment_type'])
                assert_(ps['billed_entity_account']['account_number'],
                        expected_payment_details['payment_splits'][ps_index]['billed_entity_account']['account_number'])
                assert_(ps['billed_entity_account']['billed_entity_id'],
                        expected_payment_details['payment_splits'][ps_index]['billed_entity_account'][
                            'billed_entity_id'])

        # validations on booking details
        expected_booking_details = json.loads(expected_bill_response['expected_booking_details'])
        assert_(response['net_paid_amount'], expected_booking_details['net_paid_amount'])
        assert_(response['net_payable'], expected_booking_details['net_payable'])
        assert_(response['paid_amount'], expected_booking_details['paid_amount'])
        assert_(response['refund_amount'], expected_booking_details['refund_amount'])
        assert_(response['total_credit_posttax_amount'], expected_booking_details['total_credit_posttax_amount'])
        assert_(response['total_credit_posttax_amount_post_allowance'],
                expected_booking_details['total_credit_posttax_amount_post_allowance'])
        assert_(response['total_non_credit_invoiced_amount'],
                expected_booking_details['total_non_credit_invoiced_amount'])
        assert_(response['total_non_credit_reversal_amount'],
                expected_booking_details['total_non_credit_reversal_amount'])
        assert_(response['total_posttax_amount_post_allowance'],
                expected_booking_details['total_posttax_amount_post_allowance'])
        assert_(response['total_pretax_amount'], expected_booking_details['total_pretax_amount'])
        assert_(response['total_tax_amount'], expected_booking_details['total_tax_amount'])

        # validation on summary
        assert_(response['summary']['balance'], expected_booking_details['summary']['balance'])
        assert_(response['summary']['balance_to_clear_after_checkout'],
                expected_booking_details['summary']['balance_to_clear_after_checkout'])
        assert_(response['summary']['balance_to_clear_before_checkout'],
                expected_booking_details['summary']['balance_to_clear_before_checkout'])
        assert_(response['summary']['credit_summary']['total_confirmed_payment'],
                expected_booking_details['summary']['credit_summary']['total_confirmed_payment'])
        assert_(response['summary']['credit_summary']['total_credit'],
                expected_booking_details['summary']['credit_summary']['total_credit'])
        assert_(response['summary']['credit_summary']['total_credit_offered'],
                expected_booking_details['summary']['credit_summary']['total_credit_offered'])
        assert_(response['summary']['credit_summary']['total_refund'],
                expected_booking_details['summary']['credit_summary']['total_refund'])
        assert_(response['summary']['credit_summary']['total_unconfirmed_payment'],
                expected_booking_details['summary']['credit_summary']['total_unconfirmed_payment'])
        assert_(response['summary']['debit_summary']['total_allowance'],
                expected_booking_details['summary']['debit_summary']['total_allowance'])
        assert_(response['summary']['debit_summary']['total_charge'],
                expected_booking_details['summary']['debit_summary']['total_charge'])
        assert_(response['summary']['debit_summary']['total_credit_allowance'],
                expected_booking_details['summary']['debit_summary']['total_credit_allowance'])
        assert_(response['summary']['debit_summary']['total_credit_charge'],
                expected_booking_details['summary']['debit_summary']['total_credit_charge'])
        assert_(response['summary']['debit_summary']['total_debit'],
                expected_booking_details['summary']['debit_summary']['total_debit'])
        assert_(response['summary']['debit_summary']['total_debit_payable_after_checkout'],
                expected_booking_details['summary']['debit_summary']['total_debit_payable_after_checkout'])
        assert_(response['summary']['debit_summary']['total_debit_payable_at_checkout'],
                expected_booking_details['summary']['debit_summary']['total_debit_payable_at_checkout'])
        assert_(response['summary']['debit_summary']['total_non_credit_allowance'],
                expected_booking_details['summary']['debit_summary']['total_non_credit_allowance'])
        assert_(response['summary']['debit_summary']['total_non_credit_charge'],
                expected_booking_details['summary']['debit_summary']['total_non_credit_charge'])

    def validate_billing_entity(self):
        actual_billed_entity_response = self.billing_request.get_bill_entity_request(self.client, self.bill_id, 200)
        expected_billed_entity_response = json.loads(self.test_data['expected_billed_entity_response'])
        actual_billed_entity_response_sorted = sorted(actual_billed_entity_response['data'],
                                                      key=lambda i: i['billed_entity_id'])
        expected_billed_entity_response_sorted = sorted(expected_billed_entity_response['data'],
                                                        key=lambda i: i['billed_entity_id'])
        self.validate_billed_entity(actual_billed_entity_response_sorted, expected_billed_entity_response_sorted)
