import json

from prometheus.integration_tests.builders.external_clients.rate_manager_client import get_rack_manger_data
from prometheus.integration_tests.config import sheet_names, common_config
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data, return_date, increment_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationCheckinV2(BaseValidations):

    def __init__(self, client, response, test_case_id, booking_request, billing_request, booking_id, bill_id, action_id,
                 hotel_id, user_type, extra_data, room_stay_ids_and_room_ids):
        self.client = client
        self.response = response
        self.test_case_id = test_case_id
        self.booking_request = booking_request
        self.billing_request = billing_request
        self.booking_id = booking_id
        self.bill_id = bill_id
        self.hotel_id = hotel_id
        self.user_type = user_type
        self.extra_data = extra_data
        self.action_id = action_id
        self.room_stay_ids_and_room_ids = room_stay_ids_and_room_ids

    def validation_response(self):
        response_data = self.response['data']
        checkin_data_from_sheet = get_test_case_data(sheet_names.booking_actions_sheet_name, self.test_case_id)[0]
        assert response_data['action_id'] is not None
        assert_(response_data['action_type'], sanitize_test_data(checkin_data_from_sheet['action_type']))
        assert_(response_data['reversal'], sanitize_test_data(checkin_data_from_sheet['reversal']))
        assert_(response_data['reversal_side_effects'], json.loads(checkin_data_from_sheet['reversal_side_effects']))
        assert_(response_data['status'], sanitize_test_data(checkin_data_from_sheet['expected_response_status']))

    def validate_get_booking(self):
        get_booking_response = self.booking_request.get_booking_request_v2(self.client, self.booking_id, 200)
        response_data = get_booking_response['data']
        checkin_data_from_sheet = get_test_case_data(sheet_names.booking_actions_sheet_name, self.test_case_id)[0]
        booking_test_case = checkin_data_from_sheet['booking_test_case']
        booking_data = get_test_case_data(sheet_names.new_booking_v2_sheet_name, booking_test_case)[0]
        booking_test_data = booking_data.copy()
        if sanitize_test_data(booking_data['extra_information']):
            expected_extra_information = json.loads(booking_data['extra_information'])
            assert_(response_data['extra_information']['guest_visible_remarks'],
                    expected_extra_information['guest_visible_remarks'])
        booking_test_data.update(checkin_data_from_sheet)
        self.validate_common_create_booking_fields(booking_test_data, response_data)
        non_rate_plan_booking = self.extra_data.get('non_rate_plan_booking') if isinstance(self.extra_data,
                                                                                           dict) else False
        if not non_rate_plan_booking:
            mocked_rate_manager_response = sorted(get_rack_manger_data(),
                                                  key=lambda i: i['rate_plan_id_for_validation'])
            room_rate_plans_from_api_response = sorted(response_data['room_rate_plans'],
                                                       key=lambda i: int(i['rate_plan_id']))
            self.validate_room_rate_plans(room_rate_plans_from_api_response, mocked_rate_manager_response)

        # Assertions Related To Booking Owner
        expected_booking_owner_data = json.loads(checkin_data_from_sheet['expected_booking_owner'])
        booking_owner_from_response = response_data['booking_owner']
        assert_(int(booking_owner_from_response['customer_id']),
                sanitize_test_data(int(expected_booking_owner_data['expected_booking_owner_id'])))
        self.validate_booking_owner_details(expected_booking_owner_data, booking_owner_from_response)

        # Assertions Related To Bill Summary
        actual_bill_summary = response_data['bill_summary']
        expected_bill_summary = json.loads(checkin_data_from_sheet['expected_bill_summary'])
        self.validate_bill_summary(actual_bill_summary, expected_bill_summary, self.hotel_id)

        response_data['room_stays'] = sorted(response_data['room_stays'], key=lambda i: i['room_stay_id'])
        # Assertions Related To Room Stay
        for room_data_from_checkin_sheet, room_stay_from_response in zip(
                json.loads(checkin_data_from_sheet['expected_room_stay_and_status']), response_data['room_stays']):
            room_stay_id = room_data_from_checkin_sheet['room_stay']
            room_stay_status = room_data_from_checkin_sheet['status']
            room_stay_data = get_test_case_data(sheet_names.room_stays_sheet_name, room_stay_id)[0]
            assert_(room_stay_from_response['status'], sanitize_test_data(room_stay_status))
            room_stay_expected_data = json.loads(room_stay_data['expected_booking_actions_data'])
            if self.test_case_id in ['checkin_13', 'checkin_56', 'checkin_52'] and sanitize_test_data(
                    room_stay_data['exception_test_cases']):
                room_stay_expected_data = json.loads(room_stay_data['exception_test_cases'])
            if room_stay_expected_data['expected_checkin_date'] or room_stay_expected_data[
                'expected_checkin_date'] == 0:
                expected_checkin = str(return_date(room_stay_expected_data['expected_checkin_date']))
                response_checkin = room_stay_from_response['actual_checkin_date'].split('T')[0]
                assert_(response_checkin, expected_checkin)
            else:
                assert_(room_stay_from_response['actual_checkin_date'],
                        sanitize_test_data(room_stay_expected_data['expected_checkin_date']))
            if room_stay_expected_data['expected_checkout_date'] or room_stay_expected_data[
                'expected_checkout_date'] == 0:
                expected_checkout = str(return_date(room_stay_expected_data['expected_checkout_date']))
                response_checkout = response_data['actual_checkout_date'].split('T')[0]
                assert_(response_checkout, expected_checkout)
            else:
                assert_(response_data['actual_checkout_date'],
                        sanitize_test_data(room_stay_expected_data['expected_checkout_date']))
            assert_(room_stay_from_response['allowed_actions'], room_stay_expected_data['allowed_actions'])
            assert_(room_stay_from_response['checkin_date'],
                    increment_date(int(room_stay_expected_data['checkin_date']),
                                   common_config.CHECK_IN_TIME_ZONE))
            assert_(room_stay_from_response['checkout_date'],
                    increment_date(int(room_stay_expected_data['checkout_date']), common_config.CHECKOUT_TIME_ZONE))

            assert_(room_stay_from_response['disallow_charge_addition'],
                    sanitize_test_data(json.loads(room_stay_data['expected_room_data'])['disallow_charge_addition']))
            assert_(room_stay_from_response['is_overflow'],
                    sanitize_test_data(json.loads(room_stay_data['expected_room_data'])['is_overflow']))
            assert_(room_stay_from_response['room_stay_id'],
                    int(sanitize_test_data(room_stay_data['expected_room_stay_id'])))
            assert_(room_stay_from_response['room_type_id'], sanitize_test_data(room_stay_data['room_type_id']))
            assert_(room_stay_from_response['stay_start'], str(return_date(room_stay_expected_data['checkin_date'])))
            assert_(room_stay_from_response['stay_end'], str(return_date(room_stay_expected_data['checkout_date'])))
            for rs in self.room_stay_ids_and_room_ids:
                if rs['room_stay_tc_id'] == room_stay_data['TC_ID']:
                    assert_(room_stay_from_response['room_id'], rs['room_id'])
                    assert_(room_stay_from_response['room_allocation']['room_id'], room_stay_from_response['room_id'])
                    assert_(room_stay_from_response['room_allocation']['room_type_id'],
                            sanitize_test_data(room_stay_data['room_type_id']))
                    assert_(room_stay_from_response['stay_start'],
                            str(return_date(room_stay_expected_data['checkin_date'])))

    def validate_bill_exists_in_get_bills(self):
        get_bills_response = self.billing_request.get_bills_request(self.client, self.bill_id, 200, self.user_type)
        assert any(bill_id['bill_id'] == self.bill_id for bill_id in get_bills_response['data']['bills'])
