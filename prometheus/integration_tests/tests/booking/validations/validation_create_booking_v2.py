import ast
import json
import unittest

from prometheus.integration_tests.builders.external_clients.rate_manager_client import (
    get_rack_manger_data,
)
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.sheet_names import new_booking_v2_sheet_name
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import (
    assert_,
    assert_amount_related_fields,
    assert_gstin_regex,
    query_execute_and_convert_to_json,
    return_date,
    sanitize_blank,
    sanitize_test_data,
)
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationBooking(unittest.TestCase, BaseValidations):

    def __init__(self, client_, response, test_case_id, booking_request, billing_request, booking_id, bill_id,
                 hotel_id, user_type, extra_data=None):
        self.client = client_
        self.response = response
        self.test_case_id = test_case_id
        self.booking_request = booking_request
        self.billing_request = billing_request
        self.booking_id = booking_id
        self.bill_id = bill_id
        self.hotel_id = hotel_id
        self.extra_data = extra_data
        self.user_type = user_type

    def validate_response(self, is_put_booking=False, is_put_booking_v2=False):
        actual_data = get_test_case_data(sheet_names.new_booking_v2_sheet_name, self.test_case_id)[0]
        response_data = self.response['data']
        if sanitize_test_data(actual_data['account_details']):
            account_details = query_execute_and_convert_to_json(
                db_queries.GET_ACCOUNT_DETAILS.format(reference_number=self.booking_request.reference_number))[0][
                'account_details']
            assert_(json.loads(actual_data['account_details']),
                    account_details)
        if sanitize_test_data(actual_data['discount_details']):
            expected_discount_details = json.loads(actual_data['discount_details'])
            actual_discount_details = response_data['discount_details']

            for exp_discount_data in expected_discount_details:
                if 'total_discount_value' in exp_discount_data and isinstance(exp_discount_data['total_discount_value'], str):
                    exp_discount_data['total_discount_value'] = exp_discount_data['total_discount_value'].split()[0]

            assert_(actual_discount_details, expected_discount_details)
        if self.test_case_id == 'put_booking_08' or self.test_case_id == 'put_booking_09':
            actual_data['comments'] = "Test Internal Remakrs"
        self.validate_common_create_booking_fields(actual_data, response_data)
        assert_(response_data['booking_level_btc_invoice'],
                sanitize_test_data(actual_data['expected_booking_level_btc_invoice']))
        if not is_put_booking:
            assert_(response_data['booking_owner_id'], sanitize_test_data(actual_data['expected_booking_owner_id']))
        assert_(response_data['cancellation_reason'], sanitize_test_data(actual_data['cancellation_reason']))
        assert response_data['created_at'] is not None
        if sanitize_test_data(actual_data['extra_information']):
            assert_(response_data['extra_information'], json.loads(actual_data['extra_information']))
        else:
            assert response_data['extra_information'] is None
        assert_(response_data['hold_till'], sanitize_test_data(actual_data['hold_till']))
        assert_(response_data['hotel_id'], self.hotel_id)
        assert response_data['modified_at'] is not None
        if sanitize_test_data(actual_data['travel_agent_details']) != 'NULL_KEY':
            travel_agent_details = json.loads(actual_data['travel_agent_details'])
            self.validate_travel_agent_details(travel_agent_details, response_data)

        if sanitize_test_data(actual_data['company_details']) != 'NULL_KEY':
            company_details = json.loads(actual_data['company_details'])
            self.validate_company_details(company_details, response_data)

        source = json.loads(actual_data['source'])
        for customer in response_data['customers']:
            if customer['customer_id'] == actual_data['expected_booking_owner_id']:
                if source['subchannel_code'] not in ('corporates', 'tmc-gaurav'):
                    assert_(customer['billed_entity_id'],
                            sanitize_test_data(actual_data['expected_booking_owner_billed_entity_id']))
                self.validate_booking_owner_details(actual_data, customer)

        # assert correct gstin regex
        for customer in response_data['customers']:
            if customer['customer_id'] == actual_data['expected_booking_owner_id']:
                if sanitize_test_data(customer['gst_details']):
                    gstin_num = customer['gst_details']['gstin_num']
                    assert_gstin_regex(self.test_case_id, gstin_num)

        # Assertion Related To Room Stay of the Booking
        room_stay_ids = actual_data['room_stay'].split(',')
        room_stay_from_response = response_data['room_stays']
        assert_(len(room_stay_ids), len(room_stay_from_response))
        for room_data_from_api, expected_room_data in zip(response_data['room_stays'], room_stay_ids):
            if is_put_booking_v2:
                expected_room_data = expected_room_data.split('#')[0]
            room_stay_data_from_sheet = get_test_case_data(sheet_names.room_stays_sheet_name, expected_room_data)[0]
            self.validate_room_stay_common_fields(room_data_from_api, room_stay_data_from_sheet, True)
            assert_(room_data_from_api['type'], sanitize_test_data(room_stay_data_from_sheet['type']))
            assert room_data_from_api['created_at'] is not None
            assert room_data_from_api['modified_at'] is not None
            if sanitize_test_data(room_stay_data_from_sheet['expected_date_wise_charge_ids']):
                if is_put_booking_v2:
                    date_wise_charge_ids = ast.literal_eval(
                        room_stay_data_from_sheet['expected_charge_ids_stay_id_for_put_booking'])['charge_ids']
                else:
                    date_wise_charge_ids = ast.literal_eval(room_stay_data_from_sheet['expected_date_wise_charge_ids'])
                actual_date_wise_charge_ids_sorted = sorted(room_data_from_api['date_wise_charge_ids'],
                                                            key=lambda i: i['charge_id'])
                expected_date_wise_charge_ids_sorted = sorted(date_wise_charge_ids, key=lambda i: i['charge_id'])
                for actual_charge_id, expected_charge_id in zip(actual_date_wise_charge_ids_sorted,
                                                                expected_date_wise_charge_ids_sorted):
                    assert_(actual_charge_id['charge_date'], str(return_date(expected_charge_id['date'])))
                    if not is_put_booking:
                        assert_(actual_charge_id['charge_id'], sanitize_test_data(expected_charge_id['charge_id']))
            assert_(room_data_from_api['disallow_charge_addition'],
                    sanitize_test_data(room_stay_data_from_sheet['disallow_charge_addition']))
            if "over_booking" in self.test_case_id:
                assert_(room_data_from_api['is_overflow'], True)
            else:
                assert_(room_data_from_api['is_overflow'], sanitize_test_data(room_stay_data_from_sheet['is_overflow']))
            assert room_data_from_api['modified_at'] is not None
            if not is_put_booking:
                assert_(room_data_from_api['room_stay_id'],
                        int(sanitize_test_data(room_stay_data_from_sheet['expected_room_stay_id'])))
            assert_(room_data_from_api['room_type_id'], sanitize_test_data(room_stay_data_from_sheet['room_type_id']))
            assert_(room_data_from_api['status'], sanitize_test_data(room_stay_data_from_sheet['expected_status']))
            assert_(room_data_from_api['stay_start'], str(return_date(room_stay_data_from_sheet['checkin_date'])))
            assert_(room_data_from_api['stay_end'], str(return_date(room_stay_data_from_sheet['checkout_date'])))
            assert_(room_data_from_api['type'], sanitize_test_data(room_stay_data_from_sheet['type']))
            expected_room_rent = ast.literal_eval(room_stay_data_from_sheet['expected_room_rents'])
            for actual_room_rent, expected_rent in zip(room_data_from_api['room_rents'], expected_room_rent):
                assert_amount_related_fields(actual_room_rent['posttax_amount'], expected_rent['posttax_amount'],
                                             self.hotel_id)
                assert_(actual_room_rent['applicable_date'], str(return_date(expected_rent['applicable_date'])))
            expected_guest_stay_data = dict()
            if is_put_booking_v2:
                expected_guest_stays = ast.literal_eval(room_stay_data_from_sheet['expected_guest_stays'])[
                    'put_booking']
                room_guest_stays_from_api = room_data_from_api['guest_stays']
                for guest_stay in expected_guest_stays:
                    guest_stay_data = get_test_case_data(sheet_names.guest_stays_sheet_name, guest_stay['guest_stay'])[
                        0]
                    if self.test_case_id == 'put_booking_21' and expected_room_data == 'room_stay_1':
                        guest_stay['customer_id'] = '4'
                    for gs in room_guest_stays_from_api:
                        if gs['guest_stay_id'] == int(guest_stay['guest_stay_id']):
                            guest_stay_data['checkin_date'] = room_stay_data_from_sheet['checkin_date']
                            guest_stay_data['checkout_date'] = room_stay_data_from_sheet['checkout_date']
                            if "expected_allowed_actions" in guest_stay:
                                guest_stay_data['expected_allowed_actions'] = guest_stay['expected_allowed_actions']
                            self.validate_room_stay_common_fields(gs, guest_stay_data, True)
                            guest_stay_data['checkin_date'] = guest_stay_data['checkout_date'] = None
                            assert_(gs['status'], sanitize_test_data(guest_stay_data['expected_status']))
                            if "guest_allocation" in guest_stay:
                                expected_guest_allocation = guest_stay['guest_allocation']
                            else:
                                expected_guest_allocation = ast.literal_eval(
                                    guest_stay_data['expected_guest_allocation'])
                            if self.test_case_id == 'put_booking_21' and expected_room_data == 'room_stay_1':
                                expected_guest_allocation['guest_id'] = '4'
                            assert_(gs['guest_allocation']['guest_id'],
                                    sanitize_test_data(expected_guest_allocation['guest_id']))
                            assert_(gs['guest_allocation']['guest_allocation_id'],
                                    sanitize_test_data(expected_guest_allocation['guest_allocation_id']))
                            for customer in response_data['customers']:
                                if int(customer['customer_id']) == int(expected_guest_allocation['guest_id']):
                                    assert_(int(customer['billed_entity_id']), int(guest_stay['billed_entity_id']))
                                    assert_(sanitize_test_data(guest_stay_data['first_name']), customer['first_name'])
                                    assert_(sanitize_test_data(guest_stay_data['email']), customer['email'])
                                    assert_(
                                        sanitize_test_data(guest_stay_data['is_primary']), customer['is_primary'])
            else:
                if not is_put_booking:
                    guest_stays = room_stay_data_from_sheet['guest_stays'].split(',')
                    for guest_stay in guest_stays:
                        guest_stay_data = get_test_case_data(sheet_names.guest_stays_sheet_name, guest_stay)[0]
                        expected_guest_stay_data[guest_stay] = guest_stay_data
                    expected_guest_stays = ast.literal_eval(room_stay_data_from_sheet['expected_guest_stay'])
                    for customer in response_data['customers']:
                        for expected_customer in expected_guest_stays:
                            if expected_customer['customer_id'] == int(customer['customer_id']):
                                if "over_booking" in self.test_case_id:
                                    assert_(int(customer['billed_entity_id']), 2)
                                else:
                                    assert_(int(customer['billed_entity_id']), expected_customer['billed_entity_id'])
                                guest_stay_test_id = expected_customer['guest_stay']

                                assert_(sanitize_test_data(expected_guest_stay_data[guest_stay_test_id]['first_name']),
                                        customer['first_name'])
                                assert_(sanitize_test_data(expected_guest_stay_data[guest_stay_test_id]['email']),
                                        customer['email'])
                                # assert_(sanitize_test_data(expected_guest_stay_data[guest_stay_test_id]['is_primary']),
                                #         customer['is_primary'])

        self.validate_segment_data(actual_data, response_data)

    def validate_get_booking(self, is_put_booking=False, is_put_booking_v2=False):
        get_booking_response = self.booking_request.get_booking_request_v2(self.client, self.booking_id, 200)
        response_data = get_booking_response['data']
        if get_rack_manger_data():
            mocked_rate_manager_response = sorted(get_rack_manger_data(),
                                                  key=lambda i: i['rate_plan_id_for_validation'])
            room_rate_plans_from_api_response = sorted(response_data['room_rate_plans'],
                                                       key=lambda i: int(i['rate_plan_id']))
            room_rate_plans_from_api_response[:] = [d for d in room_rate_plans_from_api_response if
                                                    d['is_active'] is True]
            self.validate_room_rate_plans(room_rate_plans_from_api_response, mocked_rate_manager_response,
                                          is_put_booking_v2, self.booking_id)

        actual_data = get_test_case_data(sheet_names.new_booking_v2_sheet_name, self.test_case_id)[0]
        self.validate_common_create_booking_fields(actual_data, response_data)

        # Assertions related to Bil Summary Json
        actual_bill_summary = response_data['bill_summary']
        expected_bill_summary = json.loads(actual_data['expected_bill_summary'])
        self.validate_bill_summary(actual_bill_summary, expected_bill_summary, self.hotel_id)

        # Assertions Related To Booking Owner
        booking_owner_from_response = response_data['booking_owner']
        if not is_put_booking:
            assert_(booking_owner_from_response['customer_id'],
                    sanitize_test_data(actual_data['expected_booking_owner_id']))
        self.validate_booking_owner_details(actual_data, booking_owner_from_response)

        # Assertion Related to Company Details And Travel Agent
        if sanitize_test_data(actual_data['travel_agent_details']) != 'NULL_KEY':
            travel_agent_details = json.loads(actual_data['travel_agent_details'])
            self.validate_travel_agent_details(travel_agent_details, response_data)
        if sanitize_test_data(actual_data['company_details']) != 'NULL_KEY':
            company_details = json.loads(actual_data['company_details'])
            self.validate_company_details(company_details, response_data)

        assert response_data['created_at'] is not None
        assert_(response_data['default_billed_entity_category'],
                sanitize_test_data(actual_data['expected_default_billed_entity_category']))
        assert_(response_data['default_billed_entity_category_for_extras'],
                sanitize_test_data(actual_data['expected_default_billed_entity_category_for_extras']))
        assert_(response_data['default_payment_instruction'],
                sanitize_test_data(actual_data['expected_default_payment_instruction']))
        assert_(response_data['default_payment_instruction_for_extras'],
                sanitize_test_data(actual_data['expected_default_payment_instruction_for_extras']))
        if sanitize_test_data(actual_data['extra_information']):
            assert_(response_data['extra_information'], json.loads(actual_data['extra_information']))
        else:
            assert response_data['extra_information'] is None
        if not is_put_booking:
            assert_(str(response_data['group_name']), str(sanitize_test_data(actual_data['group_name'])))
        assert_(response_data['hold_till'], sanitize_test_data(actual_data['hold_till']))
        assert_(response_data['hotel_id'], self.hotel_id)
        assert response_data['modified_at'] is not None

        # Validations Related To Room Stay
        room_stay_ids = actual_data['room_stay'].split(',')
        room_stay_from_response = response_data['room_stays']
        assert_(len(room_stay_ids), len(room_stay_from_response))
        for room_data_from_api, expected_room_data in zip(response_data['room_stays'], room_stay_ids):
            if is_put_booking_v2:
                expected_room_data = expected_room_data.split('#')[0]
            room_stay_data_from_sheet = get_test_case_data(sheet_names.room_stays_sheet_name, expected_room_data)[0]
            self.validate_room_stay_common_fields(room_data_from_api, room_stay_data_from_sheet,
                                                  test_case_id=self.test_case_id)
            if sanitize_test_data(room_stay_data_from_sheet['expected_date_wise_charge_ids']):
                if is_put_booking_v2:
                    date_wise_charge_ids = ast.literal_eval(
                        room_stay_data_from_sheet['expected_charge_ids_stay_id_for_put_booking'])['charge_ids']
                else:
                    date_wise_charge_ids = ast.literal_eval(room_stay_data_from_sheet['expected_date_wise_charge_ids'])
                for actual_charge_id, expected_charge_id in zip(room_data_from_api['date_wise_charge_ids'],
                                                                date_wise_charge_ids):
                    assert_(actual_charge_id['charge_date'], str(return_date(expected_charge_id['date'])))
                    if not is_put_booking:
                        assert_(actual_charge_id['charge_id'], sanitize_test_data(expected_charge_id['charge_id']))

            room_rate_plan_reference_id = room_stay_data_from_sheet['rate_plan_reference_id']
            non_rate_plan_booking = self.extra_data.get('non_rate_plan_booking') if isinstance(self.extra_data,
                                                                                               dict) else False
            if not non_rate_plan_booking and not is_put_booking_v2:
                for rate_plans in mocked_rate_manager_response:
                    if room_rate_plan_reference_id == rate_plans['rate_plan_id']:
                        assert_(int(room_data_from_api['room_rate_plans_ids'][0]),
                                rate_plans['rate_plan_id_for_validation'])
            if not is_put_booking:
                if is_put_booking_v2:
                    expected_room_stay_id = ast.literal_eval(
                        room_stay_data_from_sheet['expected_charge_ids_stay_id_for_put_booking'])[
                        'expected_room_stay_id']
                    assert_(room_data_from_api['room_stay_id'], int(sanitize_test_data(expected_room_stay_id)))
                else:
                    assert_(room_data_from_api['room_stay_id'],
                            int(sanitize_test_data(room_stay_data_from_sheet['expected_room_stay_id'])))
            if is_put_booking_v2:
                expected_guest_stays = ast.literal_eval(room_stay_data_from_sheet['expected_guest_stays'])[
                    'put_booking']
                room_guest_stays_from_api = room_data_from_api['guest_stays']
                for guest_stay in expected_guest_stays:
                    guest_stay_data = get_test_case_data(sheet_names.guest_stays_sheet_name, guest_stay['guest_stay'])[
                        0]
                    if self.test_case_id == 'put_booking_21' and expected_room_data == 'room_stay_1':
                        guest_stay['customer_id'] = '4'
                    for gs in room_guest_stays_from_api:
                        if gs['guest_stay_id'] == int(guest_stay['guest_stay_id']):
                            if "expected_allowed_actions" in guest_stay:
                                guest_stay_data['expected_allowed_actions'] = guest_stay['expected_allowed_actions']
                            assert_(gs['status'], sanitize_test_data(guest_stay_data['expected_status']))
                            if "guest_allocation" in guest_stay:
                                expected_guest_allocation = guest_stay['guest_allocation']
                            else:
                                expected_guest_allocation = ast.literal_eval(
                                    guest_stay_data['expected_guest_allocation'])
                            if self.test_case_id == 'put_booking_21' and expected_room_data == 'room_stay_1':
                                expected_guest_allocation['guest_id'] = '4'
                            assert_(gs['guest_id'], sanitize_test_data(expected_guest_allocation['guest_id']))
                            for customer in response_data['customers']:
                                if int(customer['customer_id']) == int(expected_guest_allocation['guest_id']):
                                    assert_(int(customer['billed_entity_id']), int(guest_stay['billed_entity_id']))
                                    assert_(sanitize_test_data(guest_stay_data['first_name']),
                                            customer['name']['first_name'])
                                    assert_(sanitize_test_data(guest_stay_data['is_primary']),
                                            customer['is_primary'])
        return get_booking_response

        # -------------- Segment Data Related Assertion ----------------------
        self.validate_segment_data(actual_data, response_data)

    def validate_get_bill(self, is_put_booking=False):
        get_bill_response = self.billing_request.get_bill_request_v2(self.client, self.bill_id, 200, self.user_type)
        response_data = get_bill_response['data']
        actual_data = get_test_case_data(sheet_names.new_booking_v2_sheet_name, self.test_case_id)[0]
        expected_charge_details = sorted(json.loads(actual_data['expected_charge_details']),
                                         key=lambda i: i['charge_id'])
        actual_charge_details = sorted(response_data['charges'], key=lambda i: i['charge_id'])
        for expected_charge, actual_charge in zip(expected_charge_details, actual_charge_details):
            assert_(actual_charge['applicable_date'].split('T')[0],
                    str(return_date(expected_charge['applicable_date'])))
            if not is_put_booking:
                assert_(actual_charge['charge_id'], expected_charge['charge_id'])
            else:
                assert_(actual_charge['posttax_amount'], expected_charge['posttax_amount'])
                assert_(actual_charge['pretax_amount'], expected_charge['pretax_amount'])
                assert_(actual_charge['tax_amount'], expected_charge['tax_amount'])
            self.assertCountEqual(expected_charge, actual_charge)
        assert_(response_data['parent_reference_number'], self.booking_id)
        if sanitize_test_data(actual_data['expected_payments_data']):
            self.validate_all_payments_response(actual_data, actual_payments_data=response_data['payments'])
        else:
            assert len(response_data['payments']) is 0

        assert_(response_data['status'], sanitize_test_data(actual_data['expected_bill_status']))

        actual_bill_summary = response_data['summary']
        expected_bill_summary = json.loads(actual_data['expected_bill_summary'])
        self.validate_bill_summary(actual_bill_summary, expected_bill_summary, self.hotel_id)

        assert_(response_data['vendor_id'], self.hotel_id)

    def compare_get_booking_and_bill_summary(self):
        get_booking_request = self.booking_request.get_booking_request_v2(self.client, self.booking_id, 200,
                                                                          self.user_type)
        get_bill_request = self.billing_request.get_bill_request_v2(self.client, self.bill_id, 200, self.user_type)
        booking_bill_summary = get_booking_request['data']['bill_summary']
        bills_bill_summary = get_bill_request['data']['summary']
        self.validate_bill_summary(booking_bill_summary, bills_bill_summary, self.hotel_id)

    def validate_billed_entities(self, is_put_booking=False):
        response_billed_entities = self.billing_request.get_bill_entity_request(self.client, self.bill_id, 200,
                                                                                self.user_type)
        actual_data = get_test_case_data(sheet_names.new_booking_v2_sheet_name, self.test_case_id)[0]
        expected_billed_entities = json.loads(actual_data['expected_billed_entities'])
        if is_put_booking:
            assert_(len(expected_billed_entities), len(response_billed_entities['data']))
        else:
            self.assertCountEqual(expected_billed_entities, response_billed_entities['data'])
            actual_billed_entity_response_sorted = sorted(response_billed_entities['data'],
                                                          key=lambda i: i['billed_entity_id'])
            expected_billed_entity_response_sorted = sorted(expected_billed_entities,
                                                            key=lambda i: i['billed_entity_id'])
            self.validate_billed_entity(actual_billed_entity_response_sorted, expected_billed_entity_response_sorted)

    def validate_cancelled_charge_billed_entity_id(self):
        response_get_charges = self.billing_request.get_bill_charges(self.client, self.booking_request.bill_id, 200)[
            'data']
        for charge in response_get_charges:
            if charge['status'] == 'cancelled':
                charge_splits = charge['charge_splits']
                for charge_split in charge_splits:
                    assert charge_split['billed_entity_account']['billed_entity_id'] is not None

    def validate_payor_entites_of_payment(self):
        booking_data_from_sheet = excel_utils.get_test_case_data(new_booking_v2_sheet_name, self.test_case_id)[0]
        default_billed_entity_category = sanitize_test_data(
            booking_data_from_sheet['default_billed_entity_category'])
        payments = self.billing_request.get_active_payments_request(self.client, self.bill_id, 200)['data']
        if payments:
            billed_entities = self.billing_request.get_bill_entity_request(self.client, self.bill_id, 200)['data']
            payor_billed_entities = set([payment['payor_billed_entity_id'] for payment in payments])
            active_billed_entities = set(
                [be['billed_entity_id'] for be in billed_entities if be['category'] == default_billed_entity_category])
            assert_(payor_billed_entities, active_billed_entities)

    def validate_patch_booking_response(self, is_sez_lut_update):
        actual_data = get_test_case_data(sheet_names.new_booking_v2_sheet_name, self.test_case_id)[0]
        get_booking_response = self.booking_request.get_booking_request_v2(self.client, self.booking_id, 200)['data']
        if sanitize_test_data(actual_data['account_details']):
            account_details = query_execute_and_convert_to_json(
                db_queries.GET_ACCOUNT_DETAILS.format(reference_number=self.booking_request.reference_number))[0][
                'account_details']
            assert_(json.loads(actual_data['account_details']),
                    account_details)
        if sanitize_blank(actual_data['extra_information']):
            assert (actual_data['extra_information'],
                    json.dumps(get_booking_response['extra_information']))
        if sanitize_blank(actual_data['comments']):
            assert_(actual_data['comments'], get_booking_response['comments'])
        if sanitize_blank(actual_data['group_name']):
            assert_(actual_data['group_name'], get_booking_response['group_name'])
        if sanitize_blank(actual_data['travel_agent_details']):
            assert (json.loads(actual_data['travel_agent_details']),
                    get_booking_response['travel_agent_details']['legal_details'])
        if sanitize_blank(actual_data['company_details']):
            assert (json.loads(actual_data['company_details']),
                    get_booking_response['company_details']['legal_details'])
        if sanitize_blank(actual_data['reference_number']):
            assert_(actual_data['reference_number'], get_booking_response['reference_number'])
        if get_booking_response['booking_owner']['reference_id'] and (
                actual_data.get('travel_agent_details') or actual_data.get('company_details')):
            reference_id = json.loads(actual_data['travel_agent_details'])['legal_details'][
                'external_reference_id'] if actual_data.get('travel_agent_details') else \
                json.loads(actual_data['company_details'])['legal_details']['external_reference_id']
            assert_(get_booking_response['booking_owner']['reference_id'], reference_id)
        if actual_data['expected_charge_details']:
            actual_charge_details = json.loads(actual_data['expected_charge_details'])
            expected_charge_details = self.billing_request.get_bill_charges(self.client, self.bill_id, 200)['data']
            sorted_actual_charge_details = sorted(actual_charge_details, key=lambda i: i['charge_id'])
            sorted_expected_charge_details = sorted(expected_charge_details, key=lambda i: i['charge_id'])
            for actual_charge_detail, expected_charge_detail in zip(sorted_actual_charge_details,
                                                                    sorted_expected_charge_details):
                actual_charge_detail['applicable_date'] = str(return_date(actual_charge_detail['applicable_date']))
                expected_charge_detail['applicable_date'] = expected_charge_detail['applicable_date'].split('T')[0]
                self.validate_charge(actual_charge_detail, expected_charge_detail)

    def validate_primary_guest_in_response(self):
        expected_data = get_test_case_data(sheet_names.new_booking_v2_sheet_name, self.test_case_id)[0][
            'expected_primary_guest_response'].split(',')
        response_data = self.response['data']
        for cdi in range(1, len(response_data['customers'])):
            is_primary = False if expected_data[cdi - 1] == 'False' else True
            assert_(response_data['customers'][cdi]['is_primary'], is_primary)

    def validate_segment_data(self, expected_data, response_data):
        if expected_data['segment']:
            if expected_data['segment'] in ['blank array', '', 'NULL', 'NULL_KEY']:
                assert response_data['segments'] in ([], None)
            else:
                expected_segment_data = json.loads(expected_data['segment'])
                for expected_segment, response_segment in zip(expected_segment_data, response_data['segments']):
                    if 'group_name' in expected_segment:
                        if self.test_case_id == 'segment_07':
                            assert response_segment['group_name'] is ""
                        else:
                            assert_(sanitize_test_data(expected_segment['group_name']), response_segment['group_name'])
                    else:
                        assert response_segment['group_name'] is None
                    if 'name' in expected_segment:
                        assert_(sanitize_test_data(expected_segment['name']), response_segment['name'])
                    else:
                        assert response_segment['group_name'] is None
                    if sanitize_test_data(expected_segment['value']):
                        assert_(expected_segment['value']['code'], response_segment['value']['code'])
                        assert_(expected_segment['value']['name'], response_segment['value']['name'])

    def validate_charge_and_expense_status(self, expense_request):
        expenses_response = expense_request.get_expense_detail(self.client, 200, self.booking_id)['data']
        for expense in expenses_response:
            get_charge_detail = self.billing_request.get_charge_request(self.client, self.bill_id, 200,
                                                                        expense['charge_id'])['data']
            if expense['status'] == 'cancelled' or get_charge_detail['status'] == 'cancelled':
                assert_(expense['status'], get_charge_detail['status'])

    def validate_default_billed_category(self, extra_data):
        is_sez, has_lut, is_calculated = False, False, False
        booking = self.booking_request.get_booking_request_v2(self.client, self.booking_id, 200)['data']
        company_billed_entity_id = [bc['company_billed_entity_id'] for bc in booking['customers'] if
                                    bc.get('company_billed_entity_id')]
        if booking.get('company_details') and int(company_billed_entity_id[
                                                      0]) == booking.get('company_details').get('billed_entity_id'):
            is_sez = booking.get('company_details').get('legal_details').get('is_sez')
            has_lut = booking.get('company_details').get('legal_details').get('has_lut')

        if booking.get('travel_agent_details') and int(company_billed_entity_id[
                                                           0]) == booking.get('travel_agent_details').get(
            'billed_entity_id'):
            is_sez = booking.get('travel_agent_details').get('legal_details').get('is_sez')
            has_lut = booking.get('travel_agent_details').get('legal_details').get('has_lut')
        assert_(extra_data, [is_sez, has_lut])

    def validate_commissions(self):
        expected_data = get_test_case_data(sheet_names.new_booking_v2_sheet_name, self.test_case_id)[0]
        if sanitize_test_data(expected_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(expected_data['expected_commissions']))
        else:
            self.validate_ta_commissions()

    def validate_charges(self):
        expected_test_data = get_test_case_data(sheet_names.new_booking_v2_sheet_name, self.test_case_id)[0]
        actual_charge_details = self.billing_request.get_bill_charges(self.client, self.bill_id, 200)['data']
        expected_charge_details = json.loads(expected_test_data['expected_charge_details'])
        sorted_actual_charge_details = sorted(actual_charge_details, key=lambda i: i['charge_id'])
        sorted_expected_charge_details = sorted(expected_charge_details, key=lambda i: i['charge_id'])
        for actual_charge_detail, expected_charge_detail in zip(sorted_actual_charge_details,
                                                                sorted_expected_charge_details):
            actual_charge_detail['applicable_date'] = actual_charge_detail['applicable_date'].split('T')[0]
            expected_charge_detail['applicable_date'] = str(return_date(expected_charge_detail['applicable_date']))
            self.validate_charge(actual_charge_detail, expected_charge_detail)
