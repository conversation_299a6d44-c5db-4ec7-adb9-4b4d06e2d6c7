from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, increment_date, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.config.common_config import ROOM_TYPE_MAP


class ValidationBulkUpdateRoomStay:

    def __init__(self, client_, test_case_id, response, hotel_id):
        self.test_data = get_test_case_data(sheet_names.update_room_stay_sheet_name, test_case_id)
        self.response = response
        self.client = client_
        self.hotel_id = hotel_id

    def validate_response(self, booking_request, booking_id):

        booking_response = booking_request.get_booking_request(client=self.client, booking_id=booking_id,
                                                               status_code=200)
        available_rooms = []
        for room_stay_data in self.test_data:
            available_room = booking_request.get_available_rooms(hotel_id=self.hotel_id, client=self.client,
                                                                 from_date=str(
                                                                     return_date(room_stay_data['checkin_date'])),
                                                                 to_date=str(
                                                                     return_date(room_stay_data['checkout_date'])),
                                                                 room_type_id=ROOM_TYPE_MAP[
                                                                     room_stay_data['room_type_id'].lower()],
                                                                 status_code=200, room_id_list_required=True)
            available_rooms.append(available_room)

        room_stays = booking_response['data']['room_stays']
        for room_stay_data, per_available_rooms in zip(self.test_data, available_rooms):
            for per_room_stay in room_stays:
                if per_room_stay['room_stay_id'] == int(room_stay_data['room_stay_id']):
                    if per_room_stay['room_allocation']:
                        room_allocation = per_room_stay['room_allocation']
                        assert_(room_allocation['room_id'], int(room_stay_data['room_id_roomAllocation']))
                        assert_(room_allocation['room_type_id'], ROOM_TYPE_MAP[room_stay_data['room_type_id'].lower()])
                        assert_(room_allocation['checkin_date'].split('T')[0],
                                increment_date(room_stay_data['checkin_date_roomAllocation']).split('T')[0])
                        assert room_allocation['room_id'] not in per_available_rooms
                    assert_(per_room_stay['disallow_charge_addition'],
                            bool(int(self.test_data[per_room_stay['room_stay_id'] - 1][
                                         'disallow_charge_addition'].lower() == 'true')))
