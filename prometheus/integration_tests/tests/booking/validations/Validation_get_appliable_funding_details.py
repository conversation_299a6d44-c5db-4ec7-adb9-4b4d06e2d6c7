import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import (
    assert_,
    convert_to_decimal,
    query_execute,
    return_date,
    sanitize_test_data,
)
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationGetApplicableFundingDetails(BaseValidations):

    def __init__(self, client_, test_case_id, billing_request, booking_request, bill_id, response):
        self.test_case_data = get_test_case_data(sheet_names.funding_summary_sheet_name, test_case_id)[0]
        self.response = response["data"]
        self.client = client_
        self.test_case_id = test_case_id
        self.billing_request = billing_request
        self.booking_request = booking_request
        self.bill_id = bill_id

    def validate_response(self):
        assert_(
            convert_to_decimal(str(self.response["applicable_funding_amount"])),
            convert_to_decimal(str(self.test_case_data["expected_applicable_funding_amount"])),
            "Applicable funding amount mismatch"
        )

        assert_(
            convert_to_decimal(str(self.response["actual_funded_amount"])),
            convert_to_decimal(str(self.test_case_data["expected_actual_funded_amount"])),
            "Actual funded amount mismatch"
        )

        expected_funding_amount_breakup = json.loads(self.test_case_data["expected_funding_amount_breakup"])
        for funding_type, actual_amount in self.response["funding_amount_breakup"].items():
            assert_(
                convert_to_decimal(str(actual_amount)),
                convert_to_decimal(str(expected_funding_amount_breakup[funding_type])),
                f"Mismatch in funding_amount_breakup for '{funding_type}'"
            )

    def validate_funding_status(self, booking_id):
        actual_data = query_execute(db_queries.GET_FUNDING_STATUS.format(booking_id=booking_id)).fetchall()
        print(actual_data)
        assert_(actual_data[0][0], str(self.test_case_data["expected_funding_status"]))

    def validate_charges(self):
        expected_test_data = get_test_case_data(sheet_names.funding_summary_sheet_name, self.test_case_id)[0]
        expected_charge_details = json.loads(expected_test_data['expected_charge_details'])
        sorted_expected_charge_details = sorted(expected_charge_details, key=lambda i: i['charge_id'])

        actual_charge_details = self.billing_request.get_bill_charges(self.client, self.bill_id, 200)['data']
        sorted_actual_charge_details = sorted(actual_charge_details, key=lambda i: i['charge_id'])

        for actual_charge_detail, expected_charge_detail in zip(sorted_actual_charge_details,
                                                                sorted_expected_charge_details):
            actual_charge_detail['applicable_date'] = actual_charge_detail['applicable_date'].split('T')[0]
            expected_charge_detail['applicable_date'] = str(return_date(expected_charge_detail['applicable_date']))
            self.validate_charge(actual_charge_detail, expected_charge_detail)


    def validate_audit_trail(self):
        actual_audit_trail_response = (
            self.booking_request.get_booking_audit_trail(self.client, self.booking_request.booking_id, 200))

        btt_request_audit = []
        btt_modified_audit = []

        for actual_audit_trail in actual_audit_trail_response['data']:
            if actual_audit_trail['audit_type'] == 'New BTT request':
                btt_request_audit.append(actual_audit_trail)
            elif actual_audit_trail['audit_type'] == 'BTT amount modified':
                btt_modified_audit.append(actual_audit_trail)

        if sanitize_test_data(self.test_case_data.get('expected_audit_trail')):
            expected_audit_trail = json.loads(self.test_case_data['expected_audit_trail'])

            if 'new_btt_request' in expected_audit_trail and expected_audit_trail['new_btt_request']:
                expected_btt_requests = expected_audit_trail['new_btt_request']
                for actual_audit, expected_audit in zip(btt_request_audit, expected_btt_requests):
                    assert_(actual_audit['application'], expected_audit['application'])
                    assert_(actual_audit['user'], expected_audit['user'])
                    assert_(actual_audit['user_type'], expected_audit['user_type'])
                    assert_(actual_audit['audit_type'], expected_audit['audit_type'])

                    for actual_domain_event in actual_audit['audit_payload']['domain_events']:
                        if actual_domain_event['event_type'] == 'New BTT request':
                            expected_domain_event = None
                            for event in expected_audit['audit_payload']['domain_events']:
                                if event['event_type'] == 'New BTT request':
                                    expected_domain_event = event
                                    break

                            if expected_domain_event:
                                for actual_detail, expected_detail in zip(
                                        actual_domain_event['event_detail'],
                                        expected_domain_event['event_detail']):

                                    assert_(actual_detail['amount'], expected_detail['amount'])
                                    assert_(actual_detail['booking_id'], self.booking_request.booking_id)
                                    assert_(actual_detail['funding_type'], expected_detail['funding_type'])
                                    assert_(actual_detail['reason'], expected_detail['reason'])
                                    assert_(actual_detail['status'], expected_detail['status'])

            if 'btt_amount_modified' in expected_audit_trail and expected_audit_trail['btt_amount_modified']:
                expected_btt_modified = expected_audit_trail['btt_amount_modified']
                for actual_audit, expected_audit in zip(btt_modified_audit, expected_btt_modified):
                    assert_(actual_audit['application'], expected_audit['application'])
                    assert_(actual_audit['user'], expected_audit['user'])
                    assert_(actual_audit['user_type'], expected_audit['user_type'])
                    assert_(actual_audit['audit_type'], expected_audit['audit_type'])

                    for actual_domain_event in actual_audit['audit_payload']['domain_events']:
                        if actual_domain_event['event_type'] == 'BTT amount modified':
                            expected_domain_event = None
                            for event in expected_audit['audit_payload']['domain_events']:
                                if event['event_type'] == 'BTT amount modified':
                                    expected_domain_event = event
                                    break

                            if expected_domain_event:
                                for actual_detail, expected_detail in zip(
                                        actual_domain_event['event_detail'],
                                        expected_domain_event['event_detail']):

                                    assert_(actual_detail['old_value']['amount'], expected_detail['old_value']['amount'])
                                    assert_(actual_detail['old_value']['booking_id'], self.booking_request.booking_id)
                                    assert_(actual_detail['old_value']['funding_type'], expected_detail['old_value']['funding_type'])
                                    assert_(actual_detail['old_value']['reason'], expected_detail['old_value']['reason'])
                                    assert_(actual_detail['old_value']['status'], expected_detail['old_value']['status'])

                                    assert_(actual_detail['new_value']['amount'], expected_detail['new_value']['amount'])
                                    assert_(actual_detail['new_value']['booking_id'], self.booking_request.booking_id)
                                    assert_(actual_detail['new_value']['funding_type'], expected_detail['new_value']['funding_type'])
                                    assert_(actual_detail['new_value']['reason'], expected_detail['new_value']['reason'])
                                    assert_(actual_detail['new_value']['status'], expected_detail['new_value']['status'])
