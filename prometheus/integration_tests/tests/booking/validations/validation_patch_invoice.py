from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import (
    assert_,
    sanitize_test_data,
)
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationPatchInvoice(BaseValidations):
    def __init__(self, client_, test_case_id, response, invoice_account_request, invoice_id):
        self.test_data = get_test_case_data(sheet_names.invoice_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.invoice_account_request = invoice_account_request
        self.invoice_id = invoice_id

    def validate_response(self):
        actual_response_data = self.response['data']
        get_invoice_data = self.invoice_account_request.get_invoice(self.client, 200, self.invoice_id)['data']

        if sanitize_test_data(self.test_data['is_downloaded']):
            assert_(actual_response_data['is_downloaded'], sanitize_test_data(self.test_data['is_downloaded']))

        if sanitize_test_data(get_invoice_data['allowed_bill_tos']):
            for actual_allowed_bill_tos, expected_allowed_bill_tos in zip(actual_response_data['allowed_bill_tos'],
                                                                          get_invoice_data['allowed_bill_tos']):
                assert_(actual_allowed_bill_tos['customer_id'], expected_allowed_bill_tos['customer_id'])
                assert_(actual_allowed_bill_tos['name']['first_name'],
                        expected_allowed_bill_tos['name']['first_name'])
                assert_(actual_allowed_bill_tos['name']['full_name'],
                        expected_allowed_bill_tos['name']['full_name'])
                assert_(actual_allowed_bill_tos['name']['last_name'],
                        expected_allowed_bill_tos['name']['last_name'])
                assert_(actual_allowed_bill_tos['name']['middle_name'],
                        expected_allowed_bill_tos['name']['middle_name'])
                assert_(actual_allowed_bill_tos['name']['salutation'],
                        expected_allowed_bill_tos['name']['salutation'])

        if sanitize_test_data(get_invoice_data['allowed_charge_tos']):
            for actual_allowed_charge_tos, expected_allowed_charge_tos in zip(
                    actual_response_data['allowed_charge_tos'], get_invoice_data['allowed_charge_tos']):
                assert_(actual_allowed_charge_tos['customer_id'], expected_allowed_charge_tos['customer_id'])
                assert_(actual_allowed_charge_tos['name']['first_name'],
                        expected_allowed_charge_tos['name']['first_name'])
                assert_(actual_allowed_charge_tos['name']['full_name'],
                        expected_allowed_charge_tos['name']['full_name'])
                assert_(actual_allowed_charge_tos['name']['last_name'],
                        expected_allowed_charge_tos['name']['last_name'])
                assert_(actual_allowed_charge_tos['name']['middle_name'],
                        expected_allowed_charge_tos['name']['middle_name'])
                assert_(actual_allowed_charge_tos['name']['salutation'],
                        expected_allowed_charge_tos['name']['salutation'])

        assert_(actual_response_data['allowed_charge_types'], get_invoice_data['allowed_charge_types'])
        assert_(actual_response_data['bill_id'], get_invoice_data['bill_id'])

        actual_bill_to_details = actual_response_data['bill_to']
        expected_bill_to_details = get_invoice_data['bill_to']
        if actual_bill_to_details['address']:
            assert_(actual_bill_to_details['address']['city'], expected_bill_to_details['address']['city'])
            assert_(actual_bill_to_details['address']['country'],
                    expected_bill_to_details['address']['country'])
            assert_(actual_bill_to_details['address']['field_1'],
                    expected_bill_to_details['address']['field_1'])
            assert_(actual_bill_to_details['address']['field_2'],
                    expected_bill_to_details['address']['field_2'])
            assert_(actual_bill_to_details['address']['pincode'],
                    expected_bill_to_details['address']['pincode'])
            assert_(actual_bill_to_details['address']['state'], expected_bill_to_details['address']['state'])
        assert_(actual_bill_to_details['customer_id'], expected_bill_to_details['customer_id'])
        assert_(actual_bill_to_details['email'], expected_bill_to_details['email'])
        assert_(actual_bill_to_details['gstin_num'], expected_bill_to_details['gstin_num'])
        assert_(actual_bill_to_details['has_lut'], expected_bill_to_details['has_lut'])
        assert_(actual_bill_to_details['is_sez'], expected_bill_to_details['is_sez'])
        assert_(actual_bill_to_details['name'], expected_bill_to_details['name'])
        assert_(actual_bill_to_details['phone']['country_code'],
                expected_bill_to_details['phone']['country_code'])
        assert_(actual_bill_to_details['phone']['number'], expected_bill_to_details['phone']['number'])

        assert_(actual_response_data['bill_to_type'], get_invoice_data['bill_to_type'])
        assert_(actual_response_data['billed_entity_account']['account_number'],
                get_invoice_data['billed_entity_account']['account_number'])
        assert_(actual_response_data['billed_entity_account']['billed_entity_id'],
                get_invoice_data['billed_entity_account']['billed_entity_id'])
        assert_(actual_response_data['credit_note_amount'], get_invoice_data['credit_note_amount'])
        assert_(actual_response_data['credit_payable'], get_invoice_data['credit_payable'])
        assert_(actual_response_data['generated_by'], get_invoice_data['generated_by'])
        assert_(actual_response_data['hotel_invoice_id'], get_invoice_data['hotel_invoice_id'])
        assert_(actual_response_data['invoice_date'], get_invoice_data['invoice_date'])
        assert_(actual_response_data['invoice_due_date'], get_invoice_data['invoice_due_date'])
        assert_(actual_response_data['invoice_id'], get_invoice_data['invoice_id'])
        assert_(actual_response_data['invoice_number'], get_invoice_data['invoice_number'])
        assert_(actual_response_data['invoice_url'], get_invoice_data['invoice_url'])
        assert_(actual_response_data['irn'], get_invoice_data['irn'])
        assert_(actual_response_data['is_downloaded'], get_invoice_data['is_downloaded'])
        assert_(actual_response_data['is_einvoice'], get_invoice_data['is_einvoice'])

        actual_issued_by_details = actual_response_data['issued_by']
        expected_issued_by_details = get_invoice_data['issued_by']
        if sanitize_test_data(actual_issued_by_details['bank_details']):
            assert_(actual_issued_by_details['bank_details']['branch'],
                    expected_issued_by_details['bank_details']['branch'])
            assert_(actual_issued_by_details['bank_details']['branch_code'],
                    expected_issued_by_details['bank_details']['branch_code'])
            assert_(actual_issued_by_details['bank_details']['swift_code'],
                    expected_issued_by_details['bank_details']['swift_code'])
            assert_(actual_issued_by_details['bank_details']['type'],
                    expected_issued_by_details['bank_details']['type'])
            assert_(actual_issued_by_details['bank_details']['id'],
                    expected_issued_by_details['bank_details']['id'])
            assert_(actual_issued_by_details['bank_details']['account_number'],
                    expected_issued_by_details['bank_details']['account_number'])
            assert_(actual_issued_by_details['bank_details']['bank'],
                    expected_issued_by_details['bank_details']['bank'])
            assert_(actual_issued_by_details['bank_details']['account_name'],
                    expected_issued_by_details['bank_details']['account_name'])
            assert_(actual_issued_by_details['bank_details']['ifsc_code'],
                    expected_issued_by_details['bank_details']['ifsc_code'])
        assert_(actual_issued_by_details['email'], expected_issued_by_details['email'])
        assert_(actual_issued_by_details['gst_details']['address']['city'],
                expected_issued_by_details['gst_details']['address']['city'])
        assert_(actual_issued_by_details['gst_details']['address']['country'],
                expected_issued_by_details['gst_details']['address']['country'])
        assert_(actual_issued_by_details['gst_details']['address']['field_1'],
                expected_issued_by_details['gst_details']['address']['field_1'])
        assert_(actual_issued_by_details['gst_details']['address']['field_2'],
                expected_issued_by_details['gst_details']['address']['field_2'])
        assert_(actual_issued_by_details['gst_details']['address']['pincode'],
                expected_issued_by_details['gst_details']['address']['pincode'])
        assert_(actual_issued_by_details['gst_details']['address']['state'],
                expected_issued_by_details['gst_details']['address']['state'])
        assert_(actual_issued_by_details['gst_details']['gstin_num'],
                expected_issued_by_details['gst_details']['gstin_num'])
        assert_(actual_issued_by_details['gst_details']['has_lut'],
                expected_issued_by_details['gst_details']['has_lut'])
        assert_(actual_issued_by_details['gst_details']['is_sez'],
                expected_issued_by_details['gst_details']['is_sez'])
        assert_(actual_issued_by_details['gst_details']['legal_name'],
                expected_issued_by_details['gst_details']['legal_name'])
        assert_(actual_issued_by_details['legal_signature'], expected_issued_by_details['legal_signature'])
        assert_(actual_issued_by_details['phone']['country_code'],
                expected_issued_by_details['phone']['country_code'])
        assert_(actual_issued_by_details['phone']['number'], expected_issued_by_details['phone']['number'])
        assert_(actual_issued_by_details['url'], expected_issued_by_details['url'])
        assert_(actual_response_data['issued_by_type'], get_invoice_data['issued_by_type'])
        assert_(actual_response_data['issued_to_type'], get_invoice_data['issued_to_type'])
        assert_(actual_response_data['net_payable'], get_invoice_data['net_payable'])
        assert_(actual_response_data['parent_info']['booking_id'], get_invoice_data['parent_info']['booking_id'])
        assert_(actual_response_data['parent_info']['checkin_date'], get_invoice_data['parent_info']['checkin_date'])
        assert_(actual_response_data['parent_info']['checkout_date'], get_invoice_data['parent_info']['checkout_date'])
        assert_(actual_response_data['parent_info']['reference_number'],
                get_invoice_data['parent_info']['reference_number'])
        assert_(actual_response_data['posttax_amount'], get_invoice_data['posttax_amount'])
        assert_(actual_response_data['pretax_amount'], get_invoice_data['pretax_amount'])
        assert_(actual_response_data['qr_code'], get_invoice_data['qr_code'])
        assert_(actual_response_data['signed_url'], get_invoice_data['signed_url'])
        assert_(actual_response_data['status'], get_invoice_data['status'])
        assert_(actual_response_data['tax_amount'], get_invoice_data['tax_amount'])

        for actual_tax_break, expected_tax_break in zip(actual_response_data['tax_breakup'],
                                                        get_invoice_data['tax_breakup']):
            assert_(actual_tax_break['amount'], expected_tax_break['amount'])
            assert_(actual_tax_break['tax_type'], expected_tax_break['tax_type'])
        assert_(actual_response_data['tax_details_breakup'], get_invoice_data['tax_details_breakup'])

        actual_vendor_details = actual_response_data['vendor_details']
        expected_vendor_details = get_invoice_data['vendor_details']
        assert_(actual_vendor_details['email'], expected_vendor_details['email'])
        assert_(actual_vendor_details['hotel_id'], expected_vendor_details['hotel_id'])
        assert_(actual_vendor_details['hotel_name'], expected_vendor_details['hotel_name'])
        assert_(actual_vendor_details['url'], expected_vendor_details['url'])
        assert_(actual_vendor_details['vendor_id'], expected_vendor_details['vendor_id'])
        assert_(actual_vendor_details['vendor_name'], expected_vendor_details['vendor_name'])
        assert_(actual_vendor_details['state_code'], expected_vendor_details['state_code'])
        assert_(actual_vendor_details['address']['city'], expected_vendor_details['address']['city'])
        assert_(actual_vendor_details['address']['country'], expected_vendor_details['address']['country'])
        assert_(actual_vendor_details['address']['field_1'], expected_vendor_details['address']['field_1'])
        assert_(actual_vendor_details['address']['field_2'], expected_vendor_details['address']['field_2'])
        assert_(actual_vendor_details['address']['pincode'], expected_vendor_details['address']['pincode'])
        assert_(actual_vendor_details['address']['state'], expected_vendor_details['address']['state'])
        assert_(actual_vendor_details['phone']['country_code'],
                expected_vendor_details['phone']['country_code'])
        assert_(actual_vendor_details['phone']['number'], expected_vendor_details['phone']['number'])

        assert_(actual_response_data['version'], get_invoice_data['version'])


