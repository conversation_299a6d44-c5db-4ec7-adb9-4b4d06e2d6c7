from flask import json

from prometheus.integration_tests.builders.common_request_builder import addon_repo, booking_repo
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_blank
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from ths_common.constants.booking_constants import ExpenseStatus
from prometheus.integration_tests.builders.common_request_builder import set_hotel_context


class RemoveAddOnValidations(object):
    def __init__(self, test_case_id):
        self.test_data = get_test_case_data(sheet_names.add_ons_sheet_name, test_case_id)[0]
    
    def validate_deleted_addons_expenses(self, addon_id, booking_id):
        set_hotel_context(booking_id)
        add_on_db_data = addon_repo().load(addon_id)
        assert_(add_on_db_data.deleted, True)
        for i in range(0, len(add_on_db_data.expense_ids)):
            expense_data = booking_repo().load(booking_id)._expenses[i]
            assert_(expense_data.status, ExpenseStatus.CANCELLED )
    
    def validate_charge_components_linked_addon(self, client, billing_request, bill_id):
        if sanitize_blank(self.test_data['expected_charge_data']):
            expected_charge_ids = json.loads(self.test_data['expected_charge_data'])
            for charge_id in [ids['charge_id'] for ids in expected_charge_ids]:
                charge_response = billing_request.get_charge_request(client, bill_id, 200, charge_id)['data']
                assert_(charge_response['charge_components'], None)
