import json
import unittest

from prometheus.integration_tests.builders.external_clients.rate_manager_client import get_rack_manger_data
from prometheus.integration_tests.requests.booking_requests import BookingRequests
from prometheus.integration_tests.config import sheet_names, common_config
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data, sanitize_blank, \
    return_date, increment_date


class ValidationCheckoutV2(unittest.TestCase, BaseValidations):
    def __init__(self, client, response, test_case_id, booking_request, billing_request, booking_id, bill_id, action_id,
                 hotel_id, user_type, extra_data, room_stay_ids_and_room_ids):
        self.client = client
        self.response = response
        self.test_case_id = test_case_id
        self.booking_request = booking_request
        self.billing_request = billing_request
        self.booking_id = booking_id
        self.bill_id = bill_id
        self.hotel_id = hotel_id
        self.user_type = user_type
        self.extra_data = extra_data
        self.action_id = action_id
        self.room_stay_ids_and_room_ids = room_stay_ids_and_room_ids

    def validation_response(self):
        response_data = self.response['data']
        checkout_data_from_sheet = get_test_case_data(sheet_names.checkout_v2_sheet_name, self.test_case_id)[0]
        assert response_data['action_id'] is not None
        assert_(response_data['action_type'], sanitize_test_data(checkout_data_from_sheet['action_type']))
        assert_(response_data['reversal'], sanitize_test_data(checkout_data_from_sheet['reversal']))
        assert_(response_data['reversal_side_effects'], json.loads(checkout_data_from_sheet['reversal_side_effects']))
        assert_(response_data['status'], sanitize_test_data(checkout_data_from_sheet['expected_response_status']))

    def validate_get_booking(self):
        get_booking_response = self.booking_request.get_booking_request_v2(self.client, self.booking_id, 200)
        response_data = get_booking_response['data']
        checkout_data_from_sheet = get_test_case_data(sheet_names.checkout_v2_sheet_name, self.test_case_id)[0]
        booking_test_case = checkout_data_from_sheet['booking_test_case']
        booking_data = get_test_case_data(sheet_names.new_booking_v2_sheet_name, booking_test_case)[0]
        booking_test_data = booking_data.copy()
        if sanitize_test_data(booking_data['extra_information']):
            expected_extra_information = json.loads(booking_data['extra_information'])
            assert_(response_data['extra_information']['guest_visible_remarks'],
                    expected_extra_information['guest_visible_remarks'])
        if sanitize_test_data(booking_data['extra_information']):
            expected_extra_information = json.loads(booking_data['extra_information'])
            assert_(response_data['extra_information']['guest_visible_remarks'],
                    expected_extra_information['guest_visible_remarks'])
        booking_test_data.update(checkout_data_from_sheet)
        self.validate_common_create_booking_fields(booking_test_data, response_data)
        non_rate_plan_booking = self.extra_data.get('non_rate_plan_booking') if isinstance(self.extra_data,
                                                                                           dict) else False
        is_put_booking_v2 = self.extra_data.get('is_put_booking_v2') if isinstance(self.extra_data,
                                                                                   dict) else False

        if not non_rate_plan_booking and not is_put_booking_v2:
            mocked_rate_manager_response = sorted(get_rack_manger_data(),
                                                  key=lambda i: i['rate_plan_id_for_validation'])
            room_rate_plans_from_api_response = sorted(response_data['room_rate_plans'],
                                                       key=lambda i: int(i['rate_plan_id']))
            self.validate_room_rate_plans(room_rate_plans_from_api_response, mocked_rate_manager_response)

        # Assertions Related To Booking Owner
        expected_booking_owner_data = json.loads(checkout_data_from_sheet['expected_booking_owner'])
        booking_owner_from_response = response_data['booking_owner']
        assert_(int(booking_owner_from_response['customer_id']),
                sanitize_test_data(int(expected_booking_owner_data['expected_booking_owner_id'])))
        self.validate_booking_owner_details(expected_booking_owner_data, booking_owner_from_response)

        # Assertions Related To Bill Summary
        actual_bill_summary = response_data['bill_summary']
        expected_bill_summary = json.loads(checkout_data_from_sheet['expected_bill_summary'])
        self.validate_bill_summary(actual_bill_summary, expected_bill_summary, self.hotel_id)

        # Assertions Related To Room Stay
        checkout_room_stay_data = sorted(response_data['room_stays'], key=lambda i: i['room_stay_id'])
        for room_data_from_checkout_sheet, room_stay_from_response in zip(
                json.loads(checkout_data_from_sheet['expected_room_stay_and_status']), checkout_room_stay_data):
            room_stay_id = room_data_from_checkout_sheet['room_stay']
            room_stay_status = room_data_from_checkout_sheet['status']
            room_stay_data = get_test_case_data(sheet_names.room_stays_sheet_name, room_stay_id)[0]
            room_stay_expected_data = json.loads(room_stay_data['expected_booking_actions_data'])
            if self.test_case_id in ['CheckoutV2_71', 'CheckoutV2_69', 'CheckoutV2_70',
                                     'CheckoutV2_68'] and sanitize_test_data(
                room_stay_data['exception_test_cases']):
                room_stay_expected_data = json.loads(room_stay_data['exception_test_cases'])
            if room_stay_expected_data['expected_checkin_date'] or room_stay_expected_data[
                'expected_checkin_date'] == 0:
                expected_checkout = str(return_date(room_stay_expected_data['expected_checkin_date']))
                response_checkout = room_stay_from_response['actual_checkin_date'].split('T')[0]
                assert_(response_checkout, expected_checkout)
            else:
                assert_(room_stay_from_response['actual_checkin_date'],
                        sanitize_test_data(room_stay_expected_data['expected_checkin_date']))
            if room_stay_expected_data['expected_checkout_date'] or room_stay_expected_data[
                'expected_checkout_date'] == 0:
                expected_checkout = str(return_date(room_stay_expected_data['expected_checkout_date']))
                response_checkout = response_data['actual_checkout_date'].split('T')[0]
                assert_(response_checkout, expected_checkout)
            # else:
            #     assert_(response_data['actual_checkout_date'],
            #             sanitize_test_data(room_stay_expected_data['expected_checkout_date']))
            assert_(room_stay_from_response['checkin_date'],
                    increment_date(int(room_stay_expected_data['checkin_date']),
                                   common_config.CHECK_IN_TIME_ZONE))
            assert_(room_stay_from_response['checkout_date'],
                    increment_date(int(room_stay_expected_data['checkout_date']), common_config.CHECKOUT_TIME_ZONE))

            assert_(room_stay_from_response['disallow_charge_addition'],
                    sanitize_test_data(json.loads(room_stay_data['expected_room_data'])['disallow_charge_addition']))
            assert_(room_stay_from_response['is_overflow'],
                    sanitize_test_data(json.loads(room_stay_data['expected_room_data'])['is_overflow']))
            assert_(room_stay_from_response['room_stay_id'],
                    int(sanitize_test_data(room_stay_data['expected_room_stay_id'])))
            assert_(room_stay_from_response['room_type_id'], sanitize_test_data(room_stay_data['room_type_id']))
            assert_(room_stay_from_response['stay_start'], str(return_date(room_stay_expected_data['checkin_date'])))
            assert_(room_stay_from_response['checkout_date'].split('T')[0],
                    str(return_date(room_stay_expected_data['checkout_date'])))
            for rs in self.room_stay_ids_and_room_ids:
                if rs['room_stay_tc_id'] == room_stay_data['TC_ID']:
                    # assert_(room_stay_from_response['room_id'], rs['room_id'])
                    assert_(room_stay_from_response['room_allocation']['room_id'], room_stay_from_response['room_id'])
                    assert_(room_stay_from_response['room_allocation']['room_type_id'],
                            sanitize_test_data(room_stay_data['room_type_id']))
                    assert_(room_stay_from_response['stay_start'],
                            str(return_date(room_stay_expected_data['checkin_date'])))

    def validate_charge(self):
        get_bill_response = self.billing_request.get_bill_request_v2(self.client, self.bill_id, 200, self.user_type)
        response_data = get_bill_response['data']
        actual_data = get_test_case_data(sheet_names.checkout_v2_sheet_name, self.test_case_id)[0]
        expected_charge_details = sorted(json.loads(actual_data['expected_charge_details']),
                                         key=lambda i: i['charge_id'])
        actual_charge_details = sorted(response_data['charges'], key=lambda i: i['charge_id'])
        for expected_charge, actual_charge in zip(expected_charge_details, actual_charge_details):
            assert_(actual_charge['applicable_date'].split('T')[0],
                    str(return_date(expected_charge['applicable_date'])))
            assert_(actual_charge['charge_id'], expected_charge['charge_id'])
            assert_(actual_charge['posttax_amount'], expected_charge['posttax_amount'])
            assert_(actual_charge['pretax_amount'], expected_charge['pretax_amount'])
            assert_(actual_charge['tax_amount'], expected_charge['tax_amount'])
            assert_(actual_charge['status'], expected_charge['status'])
            if actual_charge['status'] == 'consumed':
                assert actual_charge['item']['details']['room_stay_id'] is not None
            self.assertCountEqual(expected_charge, actual_charge)

    def validate_invoice(self):
        invoice_data_request = sorted(self.booking_request.get_booking_preview_invoices(self.client, self.booking_id,
                                                                                        200)['data'],
                                      key=lambda i: i['bill_to']['customer_id'])
        expected_invoice_data = sorted(json.loads(get_test_case_data(sheet_names.checkout_v2_sheet_name,
                                                                     self.test_case_id)[0]['expected_invoice_details']),
                                       key=lambda i: i['bill_to']['customer_id'])
        for actual_preview_data, expected_invoice_data in zip(invoice_data_request, expected_invoice_data):
            assert_(actual_preview_data['invoice_date'], str(return_date(expected_invoice_data['invoice_date'])))
            assert_(actual_preview_data['irn'], expected_invoice_data['irn'])
            assert_(actual_preview_data['bill_to_type'], expected_invoice_data['bill_to_type'])
            assert_(actual_preview_data['issued_to_type'], expected_invoice_data['issued_to_type'])
            assert_(actual_preview_data['qr_code'], expected_invoice_data['qr_code'])
            assert_(actual_preview_data['is_reissue_allowed'], expected_invoice_data['is_reissue_allowed'])
            assert_(actual_preview_data['invoice_due_date'],
                    str(return_date(expected_invoice_data['invoice_due_date'])))
            assert_(actual_preview_data['posttax_amount'], expected_invoice_data['posttax_amount'])
            assert_(actual_preview_data['tax_amount'], expected_invoice_data['tax_amount'])
            assert_(actual_preview_data['pretax_amount'], expected_invoice_data['pretax_amount'])
            assert_(actual_preview_data['issued_by_type'], expected_invoice_data['issued_by_type'])

            assert_(actual_preview_data['status'], expected_invoice_data['status'])
            assert_(actual_preview_data['hotel_invoice_id'], expected_invoice_data['hotel_invoice_id'])
            assert_(actual_preview_data['credit_payable'], expected_invoice_data['credit_payable'])
            assert_(actual_preview_data['invoice_url'], expected_invoice_data['invoice_url'])
            assert_(actual_preview_data['is_einvoice'], expected_invoice_data['is_einvoice'])
            assert_(actual_preview_data['credit_note_amount'],
                    expected_invoice_data['credit_note_amount'])

            if sanitize_blank(actual_preview_data['allowed_charge_tos']):
                for actual_allowed_charge_tos, expected_allowed_charge_tos in zip(
                        actual_preview_data['allowed_charge_tos'],
                        expected_invoice_data['allowed_charge_tos']):
                    assert_(actual_allowed_charge_tos['customer_id'], expected_allowed_charge_tos['customer_id'])
                    assert_(actual_allowed_charge_tos['name']['first_name'],
                            expected_allowed_charge_tos['name']['first_name'])
                    assert_(actual_allowed_charge_tos['name']['full_name'],
                            expected_allowed_charge_tos['name']['full_name'])
                    assert_(actual_allowed_charge_tos['name']['last_name'],
                            expected_allowed_charge_tos['name']['last_name'])
                    assert_(actual_allowed_charge_tos['name']['middle_name'],
                            expected_allowed_charge_tos['name']['middle_name'])
                    assert_(actual_allowed_charge_tos['name']['salutation'],
                            expected_allowed_charge_tos['name']['salutation'])

            assert_(actual_preview_data['allowed_charge_types'],
                    expected_invoice_data['allowed_charge_types'])

            for actual_tax_break, expected_tax_break in zip(actual_preview_data['tax_breakup'],
                                                            expected_invoice_data['tax_breakup']):
                assert_(actual_tax_break['amount'], expected_tax_break['amount'])
                assert_(actual_tax_break['tax_type'], expected_tax_break['tax_type'])

            assert_(actual_preview_data['tax_details_breakup'],
                    expected_invoice_data['tax_details_breakup'])

            actual_vendor_details = actual_preview_data['vendor_details']
            expected_vendor_details = expected_invoice_data['vendor_details']
            assert_(actual_vendor_details['email'], expected_vendor_details['email'])
            assert_(actual_vendor_details['hotel_id'], expected_vendor_details['hotel_id'])
            assert_(actual_vendor_details['hotel_name'], expected_vendor_details['hotel_name'])
            assert_(actual_vendor_details['url'], 'https://support.treebo.com/')
            assert_(actual_vendor_details['vendor_id'], expected_vendor_details['vendor_id'])
            assert_(actual_vendor_details['vendor_name'], expected_vendor_details['vendor_name'])
            assert_(actual_vendor_details['state_code'], expected_vendor_details['state_code'])
            assert_(actual_vendor_details['address']['city'], expected_vendor_details['address']['city'])
            assert_(actual_vendor_details['address']['country'], expected_vendor_details['address']['country'])
            assert_(actual_vendor_details['address']['field_1'], expected_vendor_details['address']['field_1'])
            assert_(actual_vendor_details['address']['field_2'], expected_vendor_details['address']['field_2'])
            assert_(actual_vendor_details['address']['pincode'], expected_vendor_details['address']['pincode'])
            assert_(actual_vendor_details['address']['state'], expected_vendor_details['address']['state'])
            assert_(actual_vendor_details['phone']['country_code'], expected_vendor_details['phone']['country_code'])
            assert_(actual_vendor_details['phone']['number'], expected_vendor_details['phone']['number'])

            actual_issued_by_details = actual_preview_data['issued_by']
            expected_issued_by_details = expected_invoice_data['issued_by']
            if sanitize_blank(actual_issued_by_details['bank_details']):
                assert_(actual_issued_by_details['bank_details']['branch'],
                        expected_issued_by_details['bank_details']['branch'])
                assert_(actual_issued_by_details['bank_details']['branch_code'],
                        expected_issued_by_details['bank_details']['branch_code'])
                assert_(actual_issued_by_details['bank_details']['swift_code'],
                        expected_issued_by_details['bank_details']['swift_code'])
                assert_(actual_issued_by_details['bank_details']['type'],
                        expected_issued_by_details['bank_details']['type'])
                assert_(actual_issued_by_details['bank_details']['id'],
                        expected_issued_by_details['bank_details']['id'])
                assert_(actual_issued_by_details['bank_details']['account_number'],
                        expected_issued_by_details['bank_details']['account_number'])
                assert_(actual_issued_by_details['bank_details']['bank'],
                        expected_issued_by_details['bank_details']['bank'])
                assert_(actual_issued_by_details['bank_details']['account_name'],
                        expected_issued_by_details['bank_details']['account_name'])
                assert_(actual_issued_by_details['bank_details']['ifsc_code'],
                        expected_issued_by_details['bank_details']['ifsc_code'])
            assert_(actual_issued_by_details['email'], expected_issued_by_details['email'])
            assert_(actual_issued_by_details['gst_details']['address']['city'],
                    expected_issued_by_details['gst_details']['address']['city'])
            assert_(actual_issued_by_details['gst_details']['address']['country'],
                    expected_issued_by_details['gst_details']['address']['country'])
            assert_(actual_issued_by_details['gst_details']['address']['field_1'],
                    expected_issued_by_details['gst_details']['address']['field_1'])
            assert_(actual_issued_by_details['gst_details']['address']['field_2'],
                    expected_issued_by_details['gst_details']['address']['field_2'])
            assert_(actual_issued_by_details['gst_details']['address']['pincode'],
                    expected_issued_by_details['gst_details']['address']['pincode'])
            assert_(actual_issued_by_details['gst_details']['address']['state'],
                    expected_issued_by_details['gst_details']['address']['state'])
            assert_(actual_issued_by_details['gst_details']['gstin_num'],
                    expected_issued_by_details['gst_details']['gstin_num'])
            assert_(actual_issued_by_details['gst_details']['has_lut'],
                    expected_issued_by_details['gst_details']['has_lut'])
            assert_(actual_issued_by_details['gst_details']['is_sez'],
                    expected_issued_by_details['gst_details']['is_sez'])
            assert_(actual_issued_by_details['gst_details']['legal_name'],
                    expected_issued_by_details['gst_details']['legal_name'])
            assert_(actual_issued_by_details['legal_signature'], expected_issued_by_details['legal_signature'])
            assert_(actual_issued_by_details['phone']['country_code'],
                    expected_issued_by_details['phone']['country_code'])
            assert_(actual_issued_by_details['phone']['number'], expected_issued_by_details['phone']['number'])
            assert_(actual_issued_by_details['url'], 'https://support.treebo.com/')

            actual_bill_to_details = actual_preview_data['bill_to']
            expected_bill_to_details = expected_invoice_data['bill_to']
            assert_(expected_bill_to_details['address'], actual_bill_to_details['address'])
            if actual_bill_to_details['address']:
                if 'city' in actual_bill_to_details['address']:
                    assert_(actual_bill_to_details['address']['city'], expected_bill_to_details['address']['city'])
                if 'country' in actual_bill_to_details['address']:
                    assert_(actual_bill_to_details['address']['country'],
                            expected_bill_to_details['address']['country'])
                if 'field_1' in actual_bill_to_details['address']:
                    assert_(actual_bill_to_details['address']['field_1'],
                            expected_bill_to_details['address']['field_1'])
                if 'field_2' in actual_bill_to_details['address']:
                    assert_(actual_bill_to_details['address']['field_2'],
                            expected_bill_to_details['address']['field_2'])
                if 'pincode' in actual_bill_to_details['address']:
                    assert_(actual_bill_to_details['address']['pincode'],
                            expected_bill_to_details['address']['pincode'])
                if 'state' in actual_bill_to_details['address']:
                    assert_(actual_bill_to_details['address']['state'], expected_bill_to_details['address']['state'])
            assert_(actual_bill_to_details['customer_id'], expected_bill_to_details['customer_id'])
            assert_(actual_bill_to_details['email'], expected_bill_to_details['email'])
            assert_(actual_bill_to_details['gstin_num'], expected_bill_to_details['gstin_num'])
            assert_(actual_bill_to_details['has_lut'], expected_bill_to_details['has_lut'])
            assert_(actual_bill_to_details['is_sez'], expected_bill_to_details['is_sez'])
            assert_(actual_bill_to_details['name'], expected_bill_to_details['name'])
            assert_(actual_bill_to_details['phone']['country_code'], expected_bill_to_details['phone']['country_code'])
            assert_(actual_bill_to_details['phone']['number'], expected_bill_to_details['phone']['number'])

            actual_preview_data['allowed_bill_tos'] = sorted(actual_preview_data['allowed_bill_tos'],
                                                             key=lambda i: i['customer_id'])
            expected_invoice_data['allowed_bill_tos'] = sorted(expected_invoice_data['allowed_bill_tos'],
                                                               key=lambda i: i['customer_id'])
            if sanitize_blank(actual_preview_data['allowed_bill_tos']):
                for actual_allowed_bill_tos, expected_allowed_bill_tos in zip(
                        actual_preview_data['allowed_bill_tos'],
                        expected_invoice_data['allowed_bill_tos']):
                    assert_(actual_allowed_bill_tos['customer_id'], expected_allowed_bill_tos['customer_id'])
                    assert_(actual_allowed_bill_tos['name']['first_name'],
                            expected_allowed_bill_tos['name']['first_name'])
                    assert_(actual_allowed_bill_tos['name']['full_name'],
                            expected_allowed_bill_tos['name']['full_name'])
                    assert_(actual_allowed_bill_tos['name']['last_name'],
                            expected_allowed_bill_tos['name']['last_name'])
                    assert_(actual_allowed_bill_tos['name']['middle_name'],
                            expected_allowed_bill_tos['name']['middle_name'])
                    assert_(actual_allowed_bill_tos['name']['salutation'],
                            expected_allowed_bill_tos['name']['salutation'])

            actual_invoice_charge_details = sorted(actual_preview_data['invoice_charges'], key=lambda i: i['charge_id'])
            expected_invoice_charge_details = sorted(expected_invoice_data['invoice_charges'],
                                                     key=lambda i: i['charge_id'])
            assert_(len(actual_invoice_charge_details), len(expected_invoice_charge_details))
            for actual_invoice_charge, expected_invoice_charge in zip(actual_invoice_charge_details,
                                                                      expected_invoice_charge_details):
                assert_(actual_invoice_charge['applicable_date'].split('T')[0], str(return_date(
                    expected_invoice_charge['applicable_date'])))
                assert_(actual_invoice_charge['bill_to_type'], expected_invoice_charge['bill_to_type'])
                assert_(actual_invoice_charge['charge_id'], expected_invoice_charge['charge_id'])
                assert_(actual_invoice_charge['charge_item']['details']['occupancy'],
                        expected_invoice_charge['charge_item']['details']['occupancy'])
                assert actual_invoice_charge['charge_item']['details']['room_stay_id'] is not None
                if 'is_pos_charge' in actual_invoice_charge['charge_item']['details']:
                    assert_(actual_invoice_charge['charge_item']['details']['is_pos_charge'],
                            expected_invoice_charge['charge_item']['details']['is_pos_charge'])
                if 'is_transferred_to_other_booking' in actual_invoice_charge['charge_item']['details']:
                    assert_(actual_invoice_charge['charge_item']['details']['is_transferred_to_other_booking'],
                            expected_invoice_charge['charge_item']['details']['is_transferred_to_other_booking'])
                if 'rate_plan_code' in actual_invoice_charge['charge_item']['details']:
                    assert_(actual_invoice_charge['charge_item']['details']['rate_plan_code'],
                            expected_invoice_charge['charge_item']['details']['rate_plan_code'])
                    assert_(actual_invoice_charge['charge_item']['details']['rate_plan_name'],
                            expected_invoice_charge['charge_item']['details']['rate_plan_name'])
                    assert_(actual_invoice_charge['charge_item']['details']['rate_plan_reference_id'],
                            expected_invoice_charge['charge_item']['details']['rate_plan_reference_id'])
                assert_(actual_invoice_charge['charge_item']['details']['room_no'],
                        expected_invoice_charge['charge_item']['details']['room_no'])
                assert_(actual_invoice_charge['charge_item']['details']['room_type'],
                        expected_invoice_charge['charge_item']['details']['room_type'])
                assert_(actual_invoice_charge['charge_item']['details']['room_type_code'],
                        expected_invoice_charge['charge_item']['details']['room_type_code'])
                assert_(actual_invoice_charge['charge_item']['item_code']['code_type'],
                        expected_invoice_charge['charge_item']['item_code']['code_type'])
                assert_(actual_invoice_charge['charge_item']['item_code']['value'],
                        expected_invoice_charge['charge_item']['item_code']['value'])
                assert_(actual_invoice_charge['charge_item']['item_id'],
                        expected_invoice_charge['charge_item']['item_id'])
                assert_(actual_invoice_charge['charge_item']['name'], expected_invoice_charge['charge_item']['name'])
                assert_(actual_invoice_charge['charge_item']['sku_category_id'],
                        expected_invoice_charge['charge_item']['sku_category_id'])
                assert_(len(actual_invoice_charge['charge_split_ids']),
                        len(expected_invoice_charge['charge_split_ids']))
                assert_(actual_invoice_charge['charge_status'], expected_invoice_charge['charge_status'])
                assert_(sorted(actual_invoice_charge['charge_to_ids']),
                        sorted(expected_invoice_charge['charge_to_ids']))
                assert_(actual_invoice_charge['charge_type'], expected_invoice_charge['charge_type'])
                assert_(actual_invoice_charge['comment'], expected_invoice_charge['comment'])
                assert_(actual_invoice_charge['created_by'], expected_invoice_charge['created_by'])
                assert_(actual_invoice_charge['credit_note_generated_amount'],
                        expected_invoice_charge['credit_note_generated_amount'])
                assert_(actual_invoice_charge['posttax_amount'], expected_invoice_charge['posttax_amount'])
                assert_(actual_invoice_charge['pretax_amount'], expected_invoice_charge['pretax_amount'])
                assert_(actual_invoice_charge['tax_amount'], expected_invoice_charge['tax_amount'])
                assert_(actual_invoice_charge['tax_details'], expected_invoice_charge['tax_details'])

    def validate_charge_and_expense_status(self, expense_request):
        expenses_response = expense_request.get_expense_detail(self.client, 200, self.booking_id)['data']
        for expense in expenses_response:
            get_charge_detail = self.billing_request.get_charge_request(self.client, self.bill_id, 200,
                                                                        expense['charge_id'])['data']
            if expense['status'] == 'cancelled' or get_charge_detail['status'] == 'cancelled':
                assert_(expense['status'], get_charge_detail['status'])

    def validate_commissions(self):
        expected_data = get_test_case_data(sheet_names.checkout_v2_sheet_name, self.test_case_id)[0]
        if sanitize_test_data(expected_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(expected_data['expected_commissions']))
        else:
            self.validate_ta_commissions()
