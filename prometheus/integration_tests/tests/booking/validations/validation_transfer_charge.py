import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationTransferCharge(BaseValidations):

    def __init__(self, client_, test_case_id=None, booking_request=None, extras=None):
        self.client = client_
        self.test_case_id = test_case_id
        self.booking_request = booking_request
        self.extras = extras

    def validate_response(self, response, details_of_charge_to_be_transferred,
                          billing_request, booking_ids, bill_ids, charge_id, expected_charge_id):
        # details_of_charge_transferred_on_new_booking
        ctonb = billing_request.get_charge_request(self.client, bill_ids[1], 200, expected_charge_id)['data']

        # details_of_charge_transferred_from_old_booking
        ctfob = billing_request.get_charge_request(self.client, bill_ids[0], 200, charge_id)['data']

        # details_of_old_charge_to_be_transferred
        octbt = details_of_charge_to_be_transferred['data']

        # validation on old charge before it was transffered and charge transffered on new booking
        assert_(ctonb['applicable_date'], octbt['applicable_date'])
        assert_(ctonb['charge_components'], octbt['charge_components'])
        assert_(int(octbt['charge_id']), int(charge_id))
        assert_(int(ctonb['charge_id']), int(expected_charge_id))
        assert_(ctonb['charge_split_type'], octbt['charge_split_type'])
        for cs_index, cs in enumerate(ctonb['charge_splits']):
            cs_octbt = octbt['charge_splits'][cs_index]
            assert_(cs['bill_to_type'], cs_octbt['bill_to_type'])
            assert_(cs['charge_split_id'], cs_octbt['charge_split_id'])
            assert_(cs['charge_to'], cs_octbt['charge_to'])
            assert_(cs['charge_type'], cs_octbt['charge_type'])
            assert_(cs['credit_note_id'], cs_octbt['credit_note_id'])
            assert_(cs['invoice_id'], cs_octbt['invoice_id'])
            if cs['payment_id']:
                actual_payment_details = billing_request.get_payment_request(self.client, bill_ids[1], cs['payment_id'],
                                                                             200)['data']
                assert_(actual_payment_details['amount'], cs['post_tax'])
                assert_(actual_payment_details['payment_splits'][0]['amount'], cs['post_tax'])
                assert_(actual_payment_details['payment_splits'][0]['billed_entity_account'],
                        cs['billed_entity_account'])
            assert_(cs['percentage'], cs_octbt['percentage'])
            assert_(cs['post_tax'], cs_octbt['post_tax'])
            assert_(cs['posttax_amount_post_allowance'], cs_octbt['posttax_amount_post_allowance'])
            assert_(cs['pre_tax'], cs_octbt['pre_tax'])
            assert_(cs['pretax_amount_post_allowance'], cs_octbt['pretax_amount_post_allowance'])
            assert_(cs['tax'], cs_octbt['tax'])
            assert_(cs['tax_amount_post_allowance'], cs_octbt['tax_amount_post_allowance'])
            assert_(cs['tax_details'], cs_octbt['tax_details'])
            assert_(cs['tax_details_post_allowance'], cs_octbt['tax_details_post_allowance'])
        assert_(ctonb['item']['name'], 'Transferred Charge')
        assert_(ctonb['item']['details']['source_booking_id'], booking_ids[0])
        assert_(ctonb['item']['details']['source_charge_id'], int(charge_id))
        assert_(ctonb['posttax_amount'], octbt['posttax_amount'])
        assert_(ctonb['posttax_amount_post_allowance'], octbt['posttax_amount_post_allowance'])
        assert_(ctonb['pretax_amount'], octbt['pretax_amount'])
        assert_(ctonb['pretax_amount_post_allowance'], octbt['pretax_amount_post_allowance'])
        assert_(ctonb['status'], octbt['status'])
        assert_(ctonb['tax_amount'], octbt['tax_amount'])
        assert_(ctonb['tax_amount_post_allowance'], octbt['tax_amount_post_allowance'])
        assert_(ctonb['tax_details'], octbt['tax_details'])
        assert_(ctonb['tax_details_post_allowance'], octbt['tax_details_post_allowance'])

        # validation on transferred charge on first booking
        assert_(ctfob['charge_splits'][0]['allowances'][0]['posttax_amount'], ctonb['charge_splits'][0]['post_tax'])
        assert_(ctfob['charge_splits'][0]['allowances'][0]['pretax_amount'], ctonb['charge_splits'][0]['pre_tax'])
        assert_(ctfob['charge_splits'][0]['allowances'][0]['tax_amount'], ctonb['charge_splits'][0]['tax'])
        assert_(ctfob['charge_splits'][0]['allowances'][0]['billed_entity_account'],
                octbt['charge_splits'][0]['billed_entity_account'])
        assert_(ctfob['pretax_amount_post_allowance'], '0.00')
        assert_(ctfob['posttax_amount_post_allowance'], '0.00')
        assert_(ctfob['tax_amount_post_allowance'], '0.00')
        assert_(ctfob['status'], 'consumed')
        assert_(ctfob['posttax_amount'], ctonb['posttax_amount'])
        assert_(ctfob['pretax_amount'], ctonb['pretax_amount'])
        assert_(ctfob['tax_amount'], ctonb['tax_amount'])

        # validation on transfer charge response
        assert_(response['data']['destination_bill_id'], bill_ids[1])
        response_charge_data = response['data']['charge']
        assert (response_charge_data['charge_to'] is not None)
        self.validate_charge(response_charge_data, ctonb)

    def validate_bulk_response(self, response, details_of_charge_to_be_transferred,
                               billing_request, booking_ids, bill_ids, charge_ids, expected_charge_ids,
                               reference_number=None):
        # details_of_charge_transferred_on_new_booking
        ctonb = billing_request.get_charges_request(self.client, bill_ids[1], 200)['data']

        # details_of_charge_transferred_from_old_booking
        ctfob = billing_request.get_charges_request(self.client, bill_ids[0], 200)['data']

        # details_of_old_charge_to_be_transferred
        octbt = details_of_charge_to_be_transferred['data']

        # validation on old charge before it was transfered and after transfered on old booking
        for charge in charge_ids:
            before_transfer_charge = [a for a in octbt if a['charge_id'] == int(charge)][0]
            after_transfer_charge = [a for a in ctfob if a['charge_id'] == int(charge)][0]
            assert_(before_transfer_charge['applicable_date'], after_transfer_charge['applicable_date'])
            assert_(before_transfer_charge['bill_to_type'], after_transfer_charge['bill_to_type'])
            assert_(before_transfer_charge['charge_components'], after_transfer_charge['charge_components'])
            assert_(int(before_transfer_charge['charge_id']), int(after_transfer_charge['charge_id']))
            assert_(before_transfer_charge['charge_split_type'], after_transfer_charge['charge_split_type'])
            for cs_index, cs_value in enumerate(before_transfer_charge['charge_splits']):
                cs_value_after_transfer = after_transfer_charge['charge_splits'][cs_index]
                assert_(cs_value['bill_to_type'], cs_value_after_transfer['bill_to_type'])
                assert_(cs_value['billed_entity_account'], cs_value_after_transfer['billed_entity_account'])
                assert_(cs_value['charge_split_id'], cs_value_after_transfer['charge_split_id'])
                assert_(cs_value['charge_to'], cs_value_after_transfer['charge_to'])
                assert_(cs_value['charge_type'], cs_value_after_transfer['charge_type'])
                assert_(cs_value['percentage'], cs_value_after_transfer['percentage'])
                assert_(cs_value['post_tax'], cs_value_after_transfer['post_tax'])
                assert_(cs_value['pre_tax'], cs_value_after_transfer['pre_tax'])
                assert_(cs_value['tax'], cs_value_after_transfer['tax'])
                assert_(cs_value['tax_details'], cs_value_after_transfer['tax_details'])
                assert_(cs_value_after_transfer['pretax_amount_post_allowance'], '0.00')
                assert_(cs_value_after_transfer['posttax_amount_post_allowance'], '0.00')
                assert_(cs_value_after_transfer['tax_amount_post_allowance'], '0.00')
                for tax in cs_value_after_transfer['tax_details_post_allowance']:
                    assert_(tax['amount'], '0.00')
                allowances_after_transfer = cs_value_after_transfer['allowances'][0]
                assert_(allowances_after_transfer['billed_entity_account'], cs_value['billed_entity_account'])
                assert_(allowances_after_transfer['posttax_amount'], cs_value['post_tax'])
                assert_(allowances_after_transfer['pretax_amount'], cs_value['pre_tax'])
                assert_(allowances_after_transfer['tax_amount'], cs_value['tax'])
                assert_(allowances_after_transfer['status'], 'consumed')
                assert_(allowances_after_transfer['remarks'], 'Charge Transferred to Booking: ' + reference_number[1])
                assert_(allowances_after_transfer['is_active'], True)

            assert_(before_transfer_charge['charge_to'], after_transfer_charge['charge_to'])
            assert_(after_transfer_charge['comment'], 'Charge Transferred to Booking: ' + reference_number[1])
            assert_(before_transfer_charge['inclusion_charge_ids'], after_transfer_charge['inclusion_charge_ids'])
            before_transfer_charge['item']['details']['is_transferred_to_other_booking'] = True
            assert_(before_transfer_charge['item']['item_code'], after_transfer_charge['item']['item_code'])
            assert_(before_transfer_charge['posttax_amount'], after_transfer_charge['posttax_amount'])
            assert_(before_transfer_charge['pretax_amount'], after_transfer_charge['pretax_amount'])
            assert_(before_transfer_charge['tax_amount'], after_transfer_charge['tax_amount'])
            assert_(after_transfer_charge['pretax_amount_post_allowance'], '0.00')
            assert_(after_transfer_charge['posttax_amount_post_allowance'], '0.00')
            assert_(after_transfer_charge['tax_amount_post_allowance'], '0.00')
            assert_(after_transfer_charge['status'], 'consumed')
            assert_(before_transfer_charge['tax_details'], after_transfer_charge['tax_details'])
            assert_(before_transfer_charge['type'], after_transfer_charge['type'])
            for tax in after_transfer_charge['tax_details_post_allowance']:
                assert_(tax['amount'], '0.00')

        # validation on old charge before it was transfered and new charge in the new booking
        if self.extras and 'inclusion_club_with_room_rate' in self.extras:
            for charge in expected_charge_ids:
                new_booking_charge = [a for a in ctonb if a['charge_id'] == int(charge['new_charge_id'])][0]
                posttax_amount, pretax_amount, tax_amount = 0, 0, 0
                for old_booking_charges in charge['old_charge_ids']:
                    posttax_amount += float([a for a in octbt if a['charge_id'] == int(old_booking_charges)][0][
                                                'posttax_amount'])
                    pretax_amount += float([a for a in octbt if a['charge_id'] == int(old_booking_charges)][0][
                                               'pretax_amount'])
                    tax_amount += float([a for a in octbt if a['charge_id'] == int(old_booking_charges)][0][
                                            'tax_amount'])
                assert_(float(new_booking_charge['posttax_amount']), posttax_amount)
                assert_(float(new_booking_charge['pretax_amount']), pretax_amount)
                assert_(float(new_booking_charge['tax_amount']), tax_amount)
                assert_((new_booking_charge['status']), 'consumed')
                assert_(new_booking_charge['comment'], 'Transferred from Booking: ' + reference_number[0])
                if len(charge['old_charge_ids']) > 1:
                    assert_(new_booking_charge['item']['item_id'], 'booking_modification')
                    assert_(new_booking_charge['item']['name'], 'Transferred Charge')
                    assert_(new_booking_charge['item']['sku_category_id'], 'stay')
                else:
                    old_booking_charges = [a for a in octbt if a['charge_id'] == int(charge['old_charge_ids'][0])][0]
                    item_id = old_booking_charges['item']['name'].split(' ')[1].lower()
                    sku_category_id = old_booking_charges['item']['name'].split(': ')[0].lower()
                    assert_(new_booking_charge['item']['item_id'], item_id)
                    assert_(new_booking_charge['item']['sku_category_id'], sku_category_id)
                assert_(new_booking_charge['item']['details']['source_charge_id'], int(charge['old_charge_ids'][0]))
                assert_(new_booking_charge['item']['details']['source_booking_id'], booking_ids[0])
            audit_trail_res = self.booking_request.get_booking_audit_trail(self.client, booking_ids[1], 200)['data']
            consuming_guest_name = None
            if 'billed_entity_account' in expected_charge_ids[0]:
                billed_entity_accounts = [expected_charge_ids[0]['billed_entity_account']]
            else:
                billed_entity_accounts = [{"account_number": 1, "billed_entity_id": 1}]
            for charge in expected_charge_ids:
                new_booking_charge = [a for a in ctonb if a['charge_id'] == int(charge['new_charge_id'])][0]
                posttax_amount, pretax_amount, tax_amount = 0, 0, 0
                for old_booking_charges in charge['old_charge_ids']:
                    posttax_amount += float([a for a in octbt if a['charge_id'] == int(old_booking_charges)][0][
                                                'posttax_amount'])
                    pretax_amount += float([a for a in octbt if a['charge_id'] == int(old_booking_charges)][0][
                                               'pretax_amount'])
                    tax_amount += float([a for a in octbt if a['charge_id'] == int(old_booking_charges)][0][
                                            'tax_amount'])
                for audit_event in audit_trail_res:
                    if audit_event['audit_type'] == 'Charge Added':
                        for per_audit_payload in audit_event['audit_payload']['domain_events']:
                            if per_audit_payload['event_type'] == 'Charge Added' and int(
                                    per_audit_payload['event_detail'][0]['charge_id'] == int(charge['new_charge_id'])):
                                audit_trail_event = audit_event['audit_payload']['domain_events']
                                for event in audit_trail_event:
                                    if 'consuming_guest_names' in charge:
                                        consuming_guest_name = charge['consuming_guest_names']
                                    new_booking_charge['consuming_guest_name'] = consuming_guest_name
                                    new_booking_charge['post_tax'] = posttax_amount
                                    new_booking_charge['billed_entity_accounts'] = billed_entity_accounts
                                    self.validate_audit_trail(event, new_booking_charge)

        else:
            if 'billed_entity_account' in expected_charge_ids[0]:
                billed_entity_accounts = expected_charge_ids[0]['billed_entity_account']
            else:
                billed_entity_accounts = {"account_number": 1, "billed_entity_id": 1}
            for charge in expected_charge_ids:
                new_booking_charge = [a for a in ctonb if a['charge_id'] == int(charge['new_charge_id'])][0]
                before_transfer_charge = [a for a in octbt if a['charge_id'] == int(charge['old_charge_id'])][0]
                if self.test_case_id in ['transfer_bulk_charge_05', 'transfer_bulk_charge_10',
                                         'transfer_bulk_charge_13']:
                    before_transfer_applicable_date = before_transfer_charge['applicable_date'].split('T')[
                        0]
                    new_booking_applicable_date = new_booking_charge['applicable_date'].split('T')[0]
                    assert_(before_transfer_applicable_date, new_booking_applicable_date)
                else:
                    assert_(new_booking_charge['applicable_date'], before_transfer_charge['applicable_date'])
                assert_(new_booking_charge['charge_split_type'], before_transfer_charge['charge_split_type'])
                assert_(new_booking_charge['comment'], 'Transferred from Booking: ' + reference_number[0])
                assert_(new_booking_charge['posttax_amount'], before_transfer_charge['posttax_amount'])
                assert_(new_booking_charge['posttax_amount_post_allowance'],
                        before_transfer_charge['posttax_amount_post_allowance'])
                assert_(new_booking_charge['pretax_amount'], before_transfer_charge['pretax_amount'])
                assert_(new_booking_charge['pretax_amount_post_allowance'],
                        before_transfer_charge['pretax_amount_post_allowance'])
                assert_(new_booking_charge['tax_amount'], before_transfer_charge['tax_amount'])
                assert_(new_booking_charge['status'], before_transfer_charge['status'])
                assert_(new_booking_charge['tax_details'], before_transfer_charge['tax_details'])
                for cs_index, cs_value in enumerate(new_booking_charge['charge_splits']):
                    before_transfer_cs_value = before_transfer_charge['charge_splits'][cs_index]
                    assert_(cs_value['charge_type'], before_transfer_cs_value['charge_type'])
                    assert_(cs_value['post_tax'], before_transfer_cs_value['post_tax'])
                    assert_(cs_value['pre_tax'], before_transfer_cs_value['pre_tax'])
                    assert_(cs_value['tax'], before_transfer_cs_value['tax'])
                    assert_(cs_value['posttax_amount_post_allowance'],
                            before_transfer_cs_value['posttax_amount_post_allowance'])
                    assert_(cs_value['pretax_amount_post_allowance'],
                            before_transfer_cs_value['pretax_amount_post_allowance'])
                    assert_(cs_value['tax_amount_post_allowance'],
                            before_transfer_cs_value['tax_amount_post_allowance'])
                    assert_(cs_value['tax_details'], before_transfer_cs_value['tax_details'])
                    assert_(cs_value['billed_entity_account'], billed_entity_accounts)
                assert_(new_booking_charge['item']['name'], 'Transferred Charge')
                if before_transfer_charge['item']['name'] == 'RoomStay':
                    assert_(new_booking_charge['item']['item_id'], 'booking_modification')
                    assert_(new_booking_charge['item']['sku_category_id'], 'stay')
                elif (before_transfer_charge['item']['name']) == 'Beverages':
                    assert_(new_booking_charge['item']['item_id'], 'beverages')
                    assert_(new_booking_charge['item']['sku_category_id'], 'food')
                elif 'is_pos_charge' in before_transfer_charge['item']['details']:
                    assert_(new_booking_charge['item']['item_id'], '3655')
                    assert_(new_booking_charge['item']['sku_category_id'], 'food')
                    assert_(new_booking_charge['item']['name'], 'Transferred Charge')
                else:
                    item_id = before_transfer_charge['item']['name'].split(' ')[1].lower()
                    sku_category_id = before_transfer_charge['item']['name'].split(': ')[0].lower()
                    assert_(new_booking_charge['item']['item_id'], item_id)
                    assert_(new_booking_charge['item']['sku_category_id'], sku_category_id)
                assert_(new_booking_charge['item']['details']['source_charge_id'], int(charge['old_charge_id']))
                assert_(new_booking_charge['item']['details']['source_booking_id'], booking_ids[0])
                if self.test_case_id == 'transfer_pos_charge_01' and before_transfer_charge['charge_id'] == '6':
                    assert new_booking_charge['item']['details']['is_pos_charge'] is True
                    assert_(new_booking_charge['item']['details']['seller_name'], 'Mumbai Tiffin')
                    assert_(new_booking_charge['item']['details']['pos_bill_id'], 'BIL-250522-0435-9915-7243')
                    assert_(new_booking_charge['item']['details']['pos_order_id'], '230522-1910-2909-5233')
            audit_trail_res = self.booking_request.get_booking_audit_trail(self.client, booking_ids[1], 200)['data']
            consuming_guest_name = None
            if 'billed_entity_account' in expected_charge_ids[0]:
                billed_entity_accounts = [expected_charge_ids[0]['billed_entity_account']]
            else:
                billed_entity_accounts = [{"account_number": 1, "billed_entity_id": 1}]
            for charge in expected_charge_ids:
                new_booking_charge = [a for a in ctonb if a['charge_id'] == int(charge['new_charge_id'])][0]
                for audit_event in audit_trail_res:
                    if audit_event['audit_type'] == 'Charge Added':
                        for per_audit_payload in audit_event['audit_payload']['domain_events']:
                            if per_audit_payload['event_type'] == 'Charge Added' and int(
                                    per_audit_payload['event_detail'][0]['charge_id'] == int(
                                        charge['new_charge_id'])):
                                audit_trail_event = audit_event['audit_payload']['domain_events']
                                new_booking_charge['post_tax'] = new_booking_charge['posttax_amount']
                                if 'consuming_guest_names' in charge:
                                    consuming_guest_name = charge['consuming_guest_names']
                                new_booking_charge['consuming_guest_name'] = consuming_guest_name
                                new_booking_charge['old_charge_id'] = charge['old_charge_id']
                                new_booking_charge['billed_entity_accounts'] = billed_entity_accounts
                                for event in audit_trail_event:
                                    self.validate_audit_trail(event, new_booking_charge)

        # validation on transfer charge response
        assert_(response['data']['destination_bill_id'], bill_ids[1])
        response_charge_data = response['data']['charges']
        assert len(expected_charge_ids) == len(response_charge_data)
        for charge in expected_charge_ids:
            new_booking_charge = [a for a in ctonb if a['charge_id'] == int(charge['new_charge_id'])][0]
            response_charge = [a for a in response_charge_data if a['charge_id'] == int(charge['new_charge_id'])][0]
            assert (response_charge['charge_to'] is not None)
            self.validate_charge(response_charge, new_booking_charge)

    def validate_audit_trail(self, event, new_booking_charge):
        if event['event_type'] == 'Charge Added':
            print("******************************** Charge Added **************************************")
            assert_(event['event_detail'][0]['item_name'], 'Transferred Charge')
            assert_(float(event['event_detail'][0]['posttax_amount']),
                    float(new_booking_charge['post_tax']))
            assert_(event['event_detail'][0]['status'], 'consumed')
            assert_(event['event_detail'][0]['sku_category'],
                    new_booking_charge['item']['sku_category_id'])
            assert_(event['event_detail'][0]['room_stay_id'],
                    str(new_booking_charge['item']['details']['room_stay_id']))
            assert_(str(event['event_detail'][0]['room_number']),
                    str(new_booking_charge['item']['details']['room_no']))
            assert_(event['event_detail'][0]['applicable_date'],
                    new_booking_charge['applicable_date'])
            for consuming_guests in event['event_detail'][0]['consuming_guest_names']:
                assert(consuming_guests in new_booking_charge['consuming_guest_name'])
        if event['event_type'] == 'Charge Posted':
            print("******************************** Charge Posted **************************************")
            assert_(event['event_detail'][0]['item_name'], 'Transferred Charge')
            assert_(float(event['event_detail'][0]['posttax_amount']),
                    float(new_booking_charge['post_tax']))
            assert_(event['event_detail'][0]['sku_category'],
                    new_booking_charge['item']['sku_category_id'])
            assert_(event['event_detail'][0]['room_stay_id'],
                    str(new_booking_charge['item']['details']['room_stay_id']))
            assert_(str(event['event_detail'][0]['room_number']),
                    str(new_booking_charge['item']['details']['room_no']))
            assert_(event['event_detail'][0]['applicable_date'],
                    new_booking_charge['applicable_date'])
            if self.test_case_id in ['transfer_bulk_charge_13',
                                     'transfer_bulk_charge_23'] or (
                    self.test_case_id == 'transfer_bulk_charge_14' and
                    new_booking_charge['old_charge_id'] == '7'):
                assert_(event['event_detail'][0]['billed_entity_accounts'],
                        [{'account_number': 1, 'billed_entity_id': 1},
                         {'account_number': 1, 'billed_entity_id': 1}])
            else:
                assert_(event['event_detail'][0]['billed_entity_accounts'],
                        new_booking_charge['billed_entity_accounts'])
        if event['event_type'] == 'Bill Amount Changed':
            print("******************************** Bill Amount Changed **************************************")
            assert_(float(event['event_detail']['new_bill_amount']),
                    float(event['event_detail']['old_bill_amount']) + float(
                        new_booking_charge['posttax_amount']))

    def validate_commissions(self):
        expected_test_data = get_test_case_data(sheet_names.bulk_transfer_charges_sheet_names, self.test_case_id)[0]
        if sanitize_test_data(expected_test_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(expected_test_data['expected_commissions']))
        else:
            self.validate_ta_commissions()
