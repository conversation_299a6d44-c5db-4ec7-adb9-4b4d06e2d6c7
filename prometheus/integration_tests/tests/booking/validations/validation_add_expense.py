from flask import json
from prometheus.integration_tests.config import sheet_names, common_config
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_blank, increment_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.requests.billing_requests import BillingRequests


class ValidationAddExpense:
    def __init__(self, client_, test_case_id, response, bill_id):
        self.test_data = get_test_case_data(sheet_names.add_expense_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.bill_id = bill_id

    def validate_response(self):
        if sanitize_blank(self.test_data["expense_item_id"]):
            assert_(self.response["data"]["expense_item_id"], self.test_data["expense_item_id"])

        if sanitize_blank(self.test_data["expected_expense_id"]):
            assert_(str(self.response["data"]["expense_id"]), self.test_data["expected_expense_id"])

        if sanitize_blank(self.test_data["expected_charge_id"]):
            assert_(str(self.response["data"]["charge_id"]), self.test_data['expected_charge_id'])

        if sanitize_blank(self.test_data["assigned_to"]):
            assert_(self.response["data"]["assigned_to"], self.test_data['assigned_to'].split(','))

        if sanitize_blank(self.test_data["expected_status"]):
            assert_(self.response["data"]["status"], self.test_data['expected_status'])

        if sanitize_blank(self.test_data["comments"]):
            assert_(self.response["data"]["comments"], self.test_data['comments'])

        if sanitize_blank(self.test_data["added_by"]):
            assert_(self.response["data"]["added_by"], self.test_data['added_by'])

        if sanitize_blank(self.test_data["room_stay_id"]):
            assert_(str(self.response["data"]["room_stay_id"]), self.test_data['room_stay_id'])

    def validate_billing(self, billing_request):
        if sanitize_blank(self.test_data['expected_bill_data']):
            expected_billing_details = json.loads(self.test_data['expected_bill_data'])
            actual_billing_details = billing_request.get_bill_request(self.client, self.bill_id, 200)
            assert_(actual_billing_details['data']['total_pretax_amount'], expected_billing_details['total_pretax_amount'])
            assert_(actual_billing_details['data']['total_posttax_amount'],
                    expected_billing_details['total_posttax_amount'])
            assert_(actual_billing_details['data']['total_tax_amount'], expected_billing_details['total_tax_amount'])
            assert_(actual_billing_details['data']['refund_amount'], expected_billing_details['refund_amount'])
            assert_(actual_billing_details['data']['paid_amount'], expected_billing_details['paid_amount'])
            assert_(actual_billing_details['data']['net_paid_amount'], expected_billing_details['net_paid_amount'])
            assert_(actual_billing_details['data']['net_payable'], expected_billing_details['net_payable'])
            assert_(actual_billing_details['data']['total_credit_posttax_amount'],
                    expected_billing_details['total_credit_posttax_amount'])
            assert_(actual_billing_details['data']['total_non_credit_invoiced_amount'],
                    expected_billing_details['total_non_credit_invoiced_amount'])
