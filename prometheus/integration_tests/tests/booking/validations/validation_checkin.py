from prometheus.integration_tests.requests.booking_requests import BookingRequests
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.utilities.common_utils import assert_
from prometheus.integration_tests.tests.base_validations import BaseValidations


class ValidationCheckin(BaseValidations):

    def __init__(self, client, response, test_case_id, booking_request):
        self.booking_request = booking_request
        self.response = response
        self.test_data = get_test_case_data(sheet_names.booking_action_sheet_name, test_case_id)[0]
        self.room_stay_data = get_test_case_data(sheet_names.add_room_stay_sheet_name,
                                                 self.test_data['room_stays'])
        self.room_stay_details = []
        for room_data in self.room_stay_data:
            self.guest_stay_data = get_test_case_data(sheet_names.add_guest_stay_sheet_name,
                                                      room_data['GuestStay'])
            self.guest_details = []
            for guest_data in self.guest_stay_data:
                self.guest_details.append(get_test_case_data(sheet_names.customer_data_sheet_name,
                                                             guest_data['guest_details'])[0])
            self.room_stay_details.append(self.guest_details)
        self.checkin_response_from_get_request = self.booking_request.get_booking_actions(client)

    def validate_checkin(self):
        self.validate_checkin_response_with_sheet()
        self.validate_checkin_response_with_get_response()

    def validate_checkin_response_with_sheet(self):
        room_stay_details = self.room_stay_details
        for room_stay_count in range(len(room_stay_details)):
            for guest_count in range(len(room_stay_details[room_stay_count])):
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count][
                            'guest']['first_name'], room_stay_details[room_stay_count][guest_count]['first_name'])

                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count][
                            'guest']['last_name'], room_stay_details[room_stay_count][guest_count]['last_name'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count][
                            'guest']['profile_type'], room_stay_details[room_stay_count][guest_count]['profile_type'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count][
                            'guest']['gender'], room_stay_details[room_stay_count][guest_count]['gender'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count][
                            'guest']['age'], int(room_stay_details[room_stay_count][guest_count]['age']))

        assert_(self.response['data']['status'], "created")
        assert_(self.response['data']['action_type'], "checkin")

    def validate_checkin_response_with_get_response(self):
        valid_checkin_get_response = 0
        for checkin_get_response in self.checkin_response_from_get_request['data']:
            if checkin_get_response['action_id'] == self.response['data']['action_id']:
                valid_checkin_get_response = checkin_get_response

        if valid_checkin_get_response == 0:
            raise Exception("This action_id doesn't exist " + str(self.response['data']['action_id']))

        for room_stay_count in range(len(self.room_stay_details)):
            for guest_count in range(len(self.room_stay_details[room_stay_count])):
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['first_name'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['first_name'])

                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['last_name'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['last_name'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['profile_type'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['profile_type'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['gender'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['gender'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['age'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['age'])

        assert_(self.response['data']['status'], valid_checkin_get_response['status'])
        assert_(self.response['data']['action_type'], valid_checkin_get_response['action_type'])
