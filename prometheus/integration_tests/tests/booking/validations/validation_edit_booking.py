from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_blank, sanitize_test_data, \
    assert_gstin_regex
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationEditBooking:

    def __init__(self, client_, test_case_id, response, bill_id, extra_data, hotel_id):
        self.test_data = get_test_case_data(sheet_names.new_booking_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.bill_id = bill_id
        self.extra_data = extra_data
        self.hotel_id = hotel_id
        self.test_case_id = test_case_id

    def validate_response(self, billing_requests):
        response_data = self.response['data']

        if sanitize_blank(self.test_data["comments"]):
            assert_(self.response["data"]["comments"], self.test_data["comments"])

        if sanitize_blank(self.test_data["reference_number"]):
            assert_(self.test_data["reference_number"], self.response["data"]["reference_number"])

        if sanitize_blank(self.test_data["application_code"]):
            assert_(self.test_data["application_code"], self.response["data"]["source"]["application_code"])

        if sanitize_blank(self.test_data["channel_code"]):
            assert_(self.test_data["channel_code"], self.response["data"]["source"]["channel_code"])

        if sanitize_blank(self.test_data["subchannel_code"]):
            assert_(self.test_data["subchannel_code"], self.response["data"]["source"]["subchannel_code"])
            
        for customer in response_data['customers']:
            if customer['customer_id'] == self.test_data['expected_booking_owner_id']:
                if sanitize_test_data(customer['gst_details']):
                    gstin_num = customer['gst_details']['gstin_num']
                    assert_gstin_regex(self.test_case_id, gstin_num)
