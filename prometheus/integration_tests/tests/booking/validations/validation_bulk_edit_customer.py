from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.tests.base_validations import BaseValidations


class ValidationBulkEditCustomer:

    def __init__(self, client_, test_case_id, response, booking_id):
        self.test_data = get_test_case_data(sheet_names.customer_data_sheet_name, test_case_id)
        self.response = response
        self.client = client_
        self.booking_id = booking_id

    def validate_customer_details_response(self):
        for actual_data, expected_data in zip(self.response['data'], self.test_data):
            BaseValidations().validate_customer_details(actual_data, expected_data, True)

    def validate_booking_response(self, booking_request):
        booking_response = booking_request.get_booking_request(client=self.client, booking_id=self.booking_id,
                                                               status_code=200)['data']
        for actual_data in booking_response['customers']:
            for expected_data in self.test_data:
                if actual_data['customer_id'] == expected_data['customer_id']:
                    BaseValidations().validate_customer_details(actual_data, expected_data, True)
