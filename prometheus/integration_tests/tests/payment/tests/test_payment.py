import pytest
from prometheus.integration_tests.utilities.inventory_modifications import *
from prometheus.integration_tests.utilities.common_utils import set_inventory_count
from prometheus.integration_tests.tests import before_test_actions
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.payment.validation.payment_split_validation import ValidationPaymentSplit
from prometheus.integration_tests.config.common_config import *


class TestPaymentSplit(BaseTest):
    @pytest.mark.parametrize("test_case_id, tc_description, status_code, user_type, error_code, dev_message,"
                             " error_payload, skip_case, skip_message, booking_case_id, previous_actions, is_mock_rule_req",
                             [
                                 ("Payment_split_01", "Single Payment Split with exact payment amount", 200, "", "", "",
                                  "", "", "", "Booking_01", before_test_actions.CashierSession_04, True),
                                 ("Payment_split_02", "Single Payment Split which payment amount more that actual", 200,
                                  "", "", "", "", "", "", "Booking_01", before_test_actions.CashierSession_04, True),
                                 ("Payment_split_03", "Single Payment Split which payment amount less that actual", 200,
                                  "", "", "", "", "", "", "Booking_01", before_test_actions.CashierSession_04, True),
                             ])
    @pytest.mark.regression
    def test_create_payment(self, client_, test_case_id, tc_description, status_code,
                            user_type, error_code, dev_message, error_payload, skip_case, skip_message, booking_case_id,
                            previous_actions, is_mock_rule_req):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]

        booking_response = self.booking_request.new_booking_request(client_, booking_case_id, status_code)
        if previous_actions:  # Running the pre-requisite for the actual test.
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.payment_split_request.payment_split(client_, test_case_id, status_code,
                                                            self.booking_request.bill_id, user_type, is_mock_rule_req)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, status_code, user_type, self.booking_request.bill_id, test_case_id, response,
                            test_case_id)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, dev_message,error_payload,"
        "skip_case, skip_message, booking_case_id, previous_actions, payment_test_id, is_mock_rule_req",
        [
            ("Update_Payment_06", "update payment status", 200, "", "", "", "",
             "", "", "Booking_01", before_test_actions.CashierSession_04, "Payment_split_01", True),
        ])
    @pytest.mark.regression
    def test_update_payment(self, client_, test_case_id, tc_description, status_code,
                            user_type, error_code, dev_message, error_payload, skip_case, skip_message, booking_case_id,
                            previous_actions, payment_test_id, is_mock_rule_req):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]

        booking_response = self.booking_request.new_booking_request(client_, booking_case_id, status_code)
        if previous_actions:  # Running the pre-requisite for the actual test.
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.payment_split_request.payment_split(client_, payment_test_id, status_code,
                                                            self.booking_request.bill_id, user_type, is_mock_rule_req)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            validation = ValidationPaymentSplit(client_, payment_test_id, response)
            self.payment_id = validation.payment_id
        else:
            assert False, "Response status code is not matching"
        updated_response = self.payment_split_request.update_payment_split(client_, test_case_id, status_code,
                                                                           self.booking_request.bill_id,
                                                                           self.payment_id, user_type, is_mock_rule_req)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, status_code, user_type, self.booking_request.bill_id, payment_test_id, response,
                            test_case_id)
        else:
            assert False, "Response status code is not matching"

    def validation(self, client_, status_code, user_type, bill_id, payment_test_id, response, test_case_id):
        validation = ValidationPaymentSplit(client_, payment_test_id, response)
        validation.update_payment_split_validation(client_, status_code, user_type, bill_id, test_case_id)
