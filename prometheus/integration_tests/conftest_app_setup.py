import os

import pytest
from dotenv import load_dotenv
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from object_registry import finalize_app_initialization
from prometheus import PROMETHEUS_ROOT_DIR, crs_context
from prometheus.app import create_app
from prometheus.domain.billing.models import (
    AccountModel,
    BilledEntityModel,
    BillModel,
    CashRegisterModel,
    ChargeModel,
    ChargeSplitModel,
    CreditNoteModel,
    CreditNoteSequenceModel,
    InvoiceModel,
    InvoiceSequenceModel,
    PaymentModel,
    PaymentSplitModel,
)
from prometheus.domain.booking.models import (
    BookingActionModel,
    BookingAuditTrailModel,
    BookingModel,
    BookingRatePlanModel,
    ExpenseModel,
    RoomAllocationModel,
    RoomStayModel,
    TACommissionModel,
)
from prometheus.domain.domain_events.domain_event_registry import pop_all_events
from prometheus.domain.integration_event.models import IntegrationEventModel
from prometheus.domain.inventory.models import (
    DNRModel,
    HouseKeepingRecordModel,
    RoomAllotmentModel,
    RoomInventoryModel, InventoryBlockModel,
)
from prometheus.infrastructure.database import db_engine
from prometheus.integration_tests.config import common_config, request_uris
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import (
    query_execute,
    return_date,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_company_profile_service,
    mock_notification_service_client,
    mock_rate_manager_client,
    mock_role_manager,
    mock_tax_calculator_service,
    mock_template_service,
)
from ths_common.constants.catalog_constants import SellerType

dir_path = os.path.dirname(os.path.abspath(__file__))


def setup_app_for_test():
    _app = create_app()
    finalize_app_initialization()
    return _app


def ensure_valid_test_env_setup(app):
    assert app.config['DEBUG']
    assert app.config['TESTING'], "App Config 'TESTING' must be True for running tests"
    assert (
            os.environ.get('APP_ENV') == 'testing'
    ), "APP_ENV should be 'testing' for running tests"
    database_uris = db_engine.get_database_uris()
    assert all(
        db_creds.dbname in ['crs_test', 'crs_test_1', 'crs_test_2', 'crs_test_3'] for tenant_id, db_creds in
        database_uris.items()
    ), "Database name should be 'crs_test, crs_test_1, crs_test_2, crs_test_3' for running tests"
    assert all(
        db_creds.host == 'localhost' for tenant_id, db_creds in database_uris.items()
    ), "Database host should be 'localhost' for running tests"


@pytest.fixture(scope='session')
def load_env():
    test_env = PROMETHEUS_ROOT_DIR.joinpath('dotenvs/test.env')
    original_lines = []
    modified_lines = []
    with open(test_env, 'r') as file:
        for line in file:
            original_lines.append(line)
            if not ('DB_NAME=crs_test' in line):
                modified_lines.append(line)
    if os.environ.get('RUNNING_ON_GITHUB'):
        with open(test_env, 'w') as file:
            file.writelines(modified_lines)
    load_dotenv(test_env, override=True)
    with open(test_env, 'w') as file:
        file.writelines(original_lines)


@pytest.fixture(scope='session', autouse=True)
def app(load_env):
    _app = setup_app_for_test()
    ensure_valid_test_env_setup(_app)

    ctx = _app.test_request_context()
    ctx.push()

    print("===========> Dropping and re-creating tables")
    Base.metadata.drop_all(bind=db_engine.get_engine(None))
    Base.metadata.create_all(bind=db_engine.get_engine(None))
    print("=====>>>> Created tables")

    print("===========> Using app fixture")
    yield _app

    print("===========> Teardown app fixture")
    # Clear all domain events
    pop_all_events()
    crs_context.clear()

    ctx.pop()


@pytest.fixture(scope='session')
def client(app):
    print("===========> Using client fixture")
    with mock_tax_calculator_service(), mock_catalog_client(
            mocked_seller_type=SellerType.MARKETPLACE.value
    ), mock_role_manager(), mock_rate_manager_client(), mock_notification_service_client(), mock_template_service(), mock_company_profile_service():
        test_client = app.test_client()
        yield test_client
    print("===========> Teardown client fixture")


@pytest.fixture(scope='session')
def client_(app):
    print("===========> Using client fixture")
    with mock_tax_calculator_service(), mock_catalog_client(
            mocked_seller_type=SellerType.MARKETPLACE.value
    ), mock_role_manager(), mock_rate_manager_client(), mock_notification_service_client(), mock_template_service(), mock_company_profile_service():
        test_client = app.test_client()
        yield test_client
    print("===========> Teardown client fixture")


@pytest.fixture(scope='function', autouse=True)
def clear_data_around_test(
        booking_repo, bill_repo, room_type_inventory_repo, hotel_repo, invoice_repo
):
    yield
    print("======>> Clearing Booking and Bill")
    booking_repo.filter(BookingModel).delete()
    booking_repo.filter(RoomStayModel).delete()
    booking_repo.filter(BookingAuditTrailModel).delete()
    booking_repo.filter(BookingActionModel).delete()
    booking_repo.filter(ExpenseModel).delete()
    booking_repo.filter(BookingRatePlanModel).delete()
    bill_repo.filter(AccountModel).delete()
    bill_repo.filter(BilledEntityModel).delete()
    bill_repo.filter(BillModel).delete()
    invoice_repo.filter(InvoiceModel).delete()
    invoice_repo.filter(InvoiceSequenceModel).delete()
    invoice_repo.filter(CreditNoteModel).delete()
    invoice_repo.filter(CreditNoteSequenceModel).delete()
    bill_repo.filter(ChargeModel).delete()
    bill_repo.filter(ChargeSplitModel).delete()
    bill_repo.filter(PaymentModel).delete()
    bill_repo.filter(PaymentSplitModel).delete()
    db_engine.get_session(None).query(RoomAllotmentModel).delete()
    db_engine.get_session(None).query(RoomInventoryModel).delete()
    db_engine.get_session(None).query(HouseKeepingRecordModel).delete()
    db_engine.get_session(None).query(DNRModel).delete()
    db_engine.get_session(None).query(RoomAllocationModel).delete()
    db_engine.get_session(None).query(CashRegisterModel).delete()
    db_engine.get_session(None).query(TACommissionModel).delete()
    db_engine.get_session(None).query(IntegrationEventModel).delete()
    db_engine.get_session(None).query(InventoryBlockModel).delete()
    db_engine.get_session(None).commit()


@pytest.fixture(scope="session", autouse=True)
def excel_data():
    print("===> Extracting Excel Data")
    excel_utils.extract_excel_data(dir_path + '/resources/TestData.xlsx')
    print("===> Extracted Excel Data")


@pytest.fixture(scope="session", autouse=True)
def seed_data(app):
    """
    This fixture is having dependency to all autouse fixtures.
    Since this is seeding data required on integration test, we don't want any conflicting data between
    seed_data.sql, and data created in autouse fixtures.

    To achieve that, we make sure that autouse fixtures are executed before this, and then delete all entries before
    running seed_data.sql

    :param app:
    :param active_room_type:
    :param rt02_room_type:
    :param food_sku:
    :param stay_sku:
    :param lunch_item:
    :param breakfast_item:
    :param extra_guest:
    :return:
    """
    print("====> seed_data running")
    # Delete any data created by previous auto-use fixture, because seed_data.sql contains data required for
    # integration tests

    with open(dir_path + '/resources/seed_data.sql', 'r') as s:
        db_engine.get_session().execute(s.read())
    print("Data seeded")
    db_engine.get_session().commit()


@pytest.fixture(scope="session", autouse=True)
def setup_hotel(client_):
    '''request_processor(client_, 'POST', request_uris.hotel_sync_uri.format(common_config.HOTEL_ID), 201)
    request_processor(client_, 'POST', request_uris.init_migration_uri.format(common_config.HOTEL_ID), 201)
    request_processor(client_, 'POST', request_uris.complete_migration_uri.format(common_config.HOTEL_ID), 200)
    '''
    print("====> setup_hotel running")
    for hotel_id in common_config.HOTEL_ID:
        BaseRequest().request_processor(
            client_,
            'POST',
            request_uris.sync_inventory.format(
                hotel_id, return_date(-3), return_date(10)
            ),
            201,
        )
        query_execute(db_queries.UPDATE_INVENTORY_COUNT.format(500, hotel_id))
        print("====> setup_hotel completed")


@pytest.fixture(scope="function", autouse=True)
def reporting():
    yield
    print("-------------TC ended ------------")
