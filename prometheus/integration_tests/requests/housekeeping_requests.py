import json
from prometheus.integration_tests.config.sheet_names import house_keeping_sheet_name
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.utilities.common_utils import del_none
from prometheus.integration_tests.builders import housekeeping_status_builder
from prometheus.integration_tests.config.request_uris import *
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class HouseKeepingRequests(BaseRequest):

    def get_roomstatus_request(self, client, hotel_id, status_code, user_type=None):
        uri = get_all_housekeeping_room_status_uri.format(hotel_id)
        response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def edit_housekeeping_status(self, client, test_case_id, status_code, user_type=None):
        request_json = json.dumps(del_none(housekeeping_status_builder.EditHousekeepingStatus
                                           (sheet_name=house_keeping_sheet_name, test_case_id=test_case_id).__dict__))
        self.test_data = get_test_case_data(sheet_names.house_keeping_sheet_name, test_case_id)[0]
        expected_test_data = self.test_data
        room_id = expected_test_data['room_id']
        hotel_id = expected_test_data['hotel_id']
        uri = patch_housekeeping_room_status_uri.format(hotel_id, room_id)
        response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json
