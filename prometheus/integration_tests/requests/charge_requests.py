import json
from prometheus.integration_tests.builders import edit_charge_builder
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.request_uris import *
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.utilities.common_utils import del_none
from prometheus.tests.mockers import mock_role_manager, mock_tax_calculator_service


class ChargeRequests(BaseRequest):
    def __init__(self):
        self.billing_response = None
        self.charges = None
        self.bill_invoices = None

    def add_allowance(self, client, test_case_id, charge_id, status_code, billing_request, bill_id, user_type=None):
        if charge_id is None:
            charge_id = '1'
        charge_response = billing_request.get_charge_request(client, bill_id, 200, charge_id, user_type)
        charge_split_id = charge_response['data']['charge_splits'][0]['charge_split_id']
        request_json = json.dumps(del_none(edit_charge_builder.AddAllowance
                                           (bill_id, sheet_names.add_allowance_sheet_name, test_case_id).__dict__))
        uri = add_allowance_uri.format(bill_id, charge_id, charge_split_id)
        with mock_tax_calculator_service(), mock_role_manager():
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json