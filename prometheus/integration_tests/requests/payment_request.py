import json
from prometheus.integration_tests.builders import payment_split_builder
from prometheus.integration_tests.config.request_uris import *
from prometheus.integration_tests.config.sheet_names import *
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.tests.mockers import mock_role_manager, mock_rule_engine
from prometheus.integration_tests.utilities.common_utils import del_none


class PaymentRequests(BaseRequest):
    def __init__(self):
        self.booking_response = None

    def payment_split(self, client, test_case_id, status_code, bill_id, user_type=None, is_mock_rule_req=False):
        uri = add_payment_uri.format(bill_id)
        request_json = json.dumps(
            del_none(payment_split_builder.PaymentSplit(payment_split_sheet_name, test_case_id).__dict__))
        with mock_role_manager():
            if is_mock_rule_req:
                with mock_rule_engine():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
            else:
                response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        self.booking_response = response.json['data']
        return response.json

    def update_payment_split(self, client, test_case_id, status_code, bill_id, payment_id, user_type=None,
                             is_mock_rule_req=False):
        uri = update_payment_uri.format(bill_id, payment_id)
        request_json = json.dumps(
            del_none(payment_split_builder.PaymentSplit(payment_split_sheet_name, test_case_id).__dict__))
        with mock_role_manager():
            if is_mock_rule_req:
                with mock_rule_engine():
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
            else:
                response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        self.booking_response = response.json['data']
        return response.json
