import json
from prometheus.integration_tests.config.request_uris import get_credit_note_template, get_credit_notes
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.utilities.common_utils import del_none
from prometheus.integration_tests.builders import create_credit_notes_builder
from prometheus.integration_tests.config.request_uris import *
from prometheus.integration_tests.config.sheet_names import *


class CreditNotesRequests(BaseRequest):

    def get_credit_notes(self, client, status_code, bill_id):
        uri = get_credit_notes.format(bill_id=bill_id)
        response = self.request_processor(client, 'GET', uri, status_code)
        self.credit_note_id = [response_data['credit_note_id'] for response_data in response.json['data']]
        return response.json

    def create_credit_notes(self, client, test_case_id, status_code, bill_id, invoicepreviews, user_type=None):
        request_json = json.dumps(
            del_none(create_credit_notes_builder.CreateCreditNotes(credit_notes_sheet_name, test_case_id,
                                                                   invoicepreviews).__dict__))
        uri = create_credit_notes_uri.format(bill_id)
        response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def get_credit_note_template(self, client, status_code, bill_id, credit_note_id):
        uri = get_credit_note_template.format(bill_id=bill_id, credit_note_id=credit_note_id)
        response = self.request_processor(client, 'GET', uri, status_code)
        return response.json
