import json

from prometheus.integration_tests.builders import (
    add_payment_builder,
    bulk_payment_edit_builder,
    edit_charge_builder,
    edit_charge_price_builder,
    edit_payment_builder,
    modify_locked_invoice_builder,
    transfer_charge_builder,
    settle_by_spot_credit_builder,
    recalculate_tax_builder,
    get_refund_mode_builder,
)
from prometheus.integration_tests.config.common_config import SUCCESS_CODES, SPOT_CREDIT_ELIGIBLE_ACCOUNT_CATEGORY
from prometheus.integration_tests.config.request_uris import *
from prometheus.integration_tests.utilities.common_utils import *
from prometheus.integration_tests.config.sheet_names import (
    add_edit_charge_sheet_name,
    add_payment_sheet_name,
    charges_v2_sheet_name,
    edit_charge_price_sheet_name,
    payment_v2_sheet_name,
    folio_summary_sheet_name,
    spot_credit_sheet_name,
    modify_locked_invoice_sheet_name
)
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.resources.db_queries import GET_ALL_ACCOUNT, UPDATE_PAYMENT_TO_POSTED
from prometheus.integration_tests.utilities.common_utils import del_none, query_execute
from prometheus.tests.mockers import (
    mock_aws_service_client,
    mock_refund_rules,
    mock_role_manager,
    mock_rule_engine,
    mock_signed_url_generator,
    mock_tax_calculator_service,
    mock_template_service,
    mock_tenant_config,
    mock_tenant_config_for_payment_rules,
    mock_tenant_config_for_record_payment_max_value,
    mock_tenant_config_for_issue_refund_max_condition,
    mock_tax_call,
    mock_get_setting_value_tenant,
)


class BillingRequests(BaseRequest):
    def __init__(self):
        self.billing_response = None
        self.charges = None
        self.bill_invoices = None
        self.payment_id = None
        self.invoice_id = None
        self.payment_ref_id = None

    def get_bill_request(self, client, bill_id, status_code, user_type=None):
        uri = get_bill_uri.format(bill_id)
        with mock_aws_service_client():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        with mock_role_manager():
            self.billing_response = response.json['data']
        return response.json

    def get_bill_request_v2(self, client, bill_id, status_code, user_type=None):
        uri = get_bill_uri_v2.format(bill_id=bill_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)
        return response.json

    def get_bills_request(self, client, bill_id, status_code, user_type):
        uri = get_bills_uri.format(bill_id=bill_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)
        return response.json

    def get_charge_request(self, client, bill_id, status_code, charge_id, user_type=None):
        uri = get_charge_uri.format(bill_id, charge_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def get_charges_request(self, client, bill_id, status_code, user_type=None):
        uri = add_charge_uri.format(bill_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)
        return response.json

    def edit_charge_request(self, client, test_case_id, bill_id, charge_id, status_code, user_type=None,
                            is_mock_rule_req=False):
        request_json = json.dumps(del_none(edit_charge_builder.EditCharge
                                           (sheet_name=add_edit_charge_sheet_name, test_case_id=test_case_id,
                                            bill_id=bill_id).__dict__))
        uri = edit_charge_uri.format(bill_id, charge_id)
        with mock_role_manager():
            if is_mock_rule_req:
                with mock_rule_engine():
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
            else:
                response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def add_payment_request(self, client, test_case_id, bill_id, status_code, user_type=None, is_mock_rule_req=False,
                            credit_shell_id=None, invoice_group_id=None):
        request_json = json.dumps(del_none(add_payment_builder.AddPayment
                                           (sheet_name=add_payment_sheet_name, test_case_id=test_case_id,
                                            bill_id=bill_id, credit_shell_id=credit_shell_id,
                                            invoice_group_id=invoice_group_id).__dict__))
        uri = add_payment_uri.format(bill_id)
        with mock_role_manager(), mock_template_service(), mock_signed_url_generator():
            if is_mock_rule_req:
                with mock_rule_engine():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
            else:
                response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code in SUCCESS_CODES:
            self.payment_id = response.json['data']['payment_id']
        return response.json

    def edit_payment_request(self, client, test_case_id, bill_id, payment_id, status_code, user_type=None,
                             is_mock_rule_req=False):
        request_json = json.dumps(del_none(edit_payment_builder.EditPayment
                                           (sheet_name=add_payment_sheet_name, test_case_id=test_case_id,
                                            bill_id=bill_id).__dict__))
        if 'EditPayment_08' in test_case_id:
            uri = edit_payment_uri.format("Bil-2530", payment_id)
        elif 'EditPayment_09' in test_case_id:
            uri = edit_payment_uri.format(bill_id, "200")
        else:
            uri = edit_payment_uri.format(bill_id, payment_id)
        with mock_role_manager(), mock_template_service(), mock_signed_url_generator():
            if is_mock_rule_req:
                with mock_rule_engine():
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json,
                                                      user_type)
            else:
                response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def edit_bulk_payment_request(self, client, test_case_id, bill_id, status_code, user_type=None,
                                  is_mock_rule_req=False):
        request_json = json.dumps(del_none(bulk_payment_edit_builder.BulkEditPayment
                                           (sheet_name=payment_v2_sheet_name, test_case_id=test_case_id,
                                            bill_id=bill_id).__dict__))

        uri = bulk_edit_payment_uri.format(bill_id)
        with mock_role_manager():
            if is_mock_rule_req:
                with mock_rule_engine():
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
            else:
                response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def get_bill_charges(self, client, bill_id, status_code, user_type=None):
        uri = get_bill_charges_uri.format(bill_id=bill_id)
        response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        with mock_role_manager():
            self.charges = response.json['data']
        return response.json

    def get_credit_notes(self, client, bill_id, status_code, user_type=None):
        uri = get_bill_credit_notes_uri.format(bill_id=bill_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def get_bill_invoices(self, client, bill_id, status_code, user_type=None):
        uri = get_bill_invoices_uri.format(bill_id=bill_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        self.bill_invoices = response.json
        invoice_ids = [invoice_id['invoice_id'] for invoice_id in response.json['data'] if
                       invoice_id['issued_to_type'] == 'customer']
        self.invoice_id = invoice_ids
        return response.json, self.invoice_id

    def get_bill_credit_notes(self, client, bill_id, status_code, user_type=None):
        uri = get_credit_notes.format(bill_id=bill_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def get_bill_invoices_v2(self, client, bill_id, status_code, user_type=None):
        uri = get_bill_invoices_v2_uri.format(bill_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def get_bill_entity_request(self, client, bill_id, status_code, user_type=None):
        uri = bill_entity.format(bill_id)
        response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def post_void_bill_request(self, client, bill_id, status_code, user_type=None):
        uri = void_bill.format(bill_id=bill_id)
        response = self.request_processor(client, 'POST', uri, status_code, user_type)
        return response.json

    def edit_charge_price_request(self, client, test_case_id, booking_id, status_code, user_type=None,
                                  is_mock_rule_req=False):

        request_json = json.dumps(del_none(edit_charge_price_builder.EditChargePrice
                                           (sheet_name=edit_charge_price_sheet_name, test_case_id=test_case_id,
                                            booking_id=booking_id).__dict__))
        uri = edit_charge_price_uri.format(booking_id, 1)
        with mock_role_manager():
            with mock_tax_calculator_service():
                if is_mock_rule_req:
                    with mock_rule_engine():
                        response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
                else:
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    @staticmethod
    def account_locked_after_checkout(bill_id):
        account_data_from_db = query_execute(GET_ALL_ACCOUNT.format(bill_id=bill_id), False).fetchall()
        locked_account = dict()
        for account_data in account_data_from_db:
            if account_data[3] and account_data[4]:
                locked_account[account_data[1]] = account_data[2]
        return locked_account

    def get_bill_v2_request(self, client, bill_id, status_code, user_type=None):
        uri = get_bill_v2_uri.format(bill_id=bill_id)
        response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        self.billing_response = response.json['data']
        return response.json

    def add_payment_v2_request(self, client, test_case_id, bill_id, status_code, user_type=None, credit_shell_id=None,
                               invoice_group_id=None, payor_billed_entity_id=None, payment_matrix=False, multiplier=2,
                               payment_constraint=False, refund_rule=False):
        request_json = json.dumps(del_none(add_payment_builder.AddPayment
                                           (sheet_name=payment_v2_sheet_name, test_case_id=test_case_id,
                                            bill_id=bill_id, credit_shell_id=credit_shell_id,
                                            invoice_group_id=invoice_group_id,
                                            payor_billed_entity_id=payor_billed_entity_id,
                                            payment_matrix=payment_matrix).__dict__))
        uri = add_payment_uri.format(bill_id)
        with mock_role_manager(), mock_template_service(), mock_signed_url_generator(), mock_rule_engine():
            if payment_matrix and payment_constraint:
                with mock_tenant_config_for_payment_rules(), \
                        mock_tenant_config_for_record_payment_max_value(multiplier), \
                        mock_tenant_config_for_issue_refund_max_condition():
                    if refund_rule:
                        with mock_refund_rules():
                            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
                    else:
                        response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
            elif payment_matrix:
                with mock_tenant_config_for_payment_rules():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
            elif payment_constraint:
                with mock_tenant_config_for_record_payment_max_value(multiplier), \
                        mock_tenant_config_for_issue_refund_max_condition():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
            else:
                response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code in SUCCESS_CODES:
            self.payment_id = response.json['data']['payment_id']
            self.payment_ref_id = response.json['data']['payment_ref_id']
        return response.json

    def edit_payment_v2_request(self, client, test_case_id, bill_id, payment_id, status_code, user_type=None,
                                credit_shell_id=None, payor_billed_entity_id=None, payment_matrix=False,
                                payment_constraint=False, payments_already_posted=False):
        if payments_already_posted:
            query_execute(UPDATE_PAYMENT_TO_POSTED.format(bill_id=bill_id))
        request_json = json.dumps(del_none(edit_payment_builder.EditPaymentV2
                                           (sheet_name=payment_v2_sheet_name, test_case_id=test_case_id,
                                            bill_id=bill_id, credit_shell_id=credit_shell_id,
                                            payment_matrix=payment_matrix,
                                            payor_billed_entity_id=payor_billed_entity_id).__dict__))
        payment_id = 100 if test_case_id == 'EditPaymentV2_07' else payment_id
        uri = edit_payment_uri.format(bill_id, payment_id)
        with mock_role_manager(), mock_template_service(), mock_signed_url_generator(), mock_rule_engine():
            if payment_matrix and payment_constraint:
                with mock_tenant_config_for_payment_rules(), mock_tenant_config_for_record_payment_max_value(), \
                        mock_tenant_config_for_issue_refund_max_condition():
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
            elif payment_matrix:
                with mock_tenant_config_for_payment_rules():
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
            elif payment_constraint:
                with mock_tenant_config_for_record_payment_max_value(), \
                        mock_tenant_config_for_issue_refund_max_condition():
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
            else:
                response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def get_payment_request(self, client, bill_id, payment_id, status_code, user_type=None):
        uri = get_payment_uri.format(bill_id=bill_id, payment_id=payment_id)
        with mock_aws_service_client(), mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def get_payments_request(self, client, bill_id, status_code, user_type=None):
        uri = get_payments_uri.format(bill_id=bill_id)
        response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def edit_charge_v2_request(self, client, test_case_id, bill_id, charge_id, status_code, user_type=None,
                               has_slab_based_taxation=False, is_tax_clubbed=[], new_tax_mocker=False):
        clubbed_taxation = True if is_tax_clubbed else False
        request_json = json.dumps(del_none(edit_charge_builder.EditChargeV2
                                           (sheet_name=charges_v2_sheet_name, test_case_id=test_case_id,
                                            bill_id=bill_id).__dict__))
        uri = edit_charge_uri.format(bill_id, charge_id)
        with mock_role_manager(), mock_rule_engine(), mock_tenant_config(hotel_level_config=is_tax_clubbed):
            if new_tax_mocker:
                with mock_tax_call(calculate_tax=True):
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
            else:
                with mock_tax_calculator_service(has_slab_based_taxation=has_slab_based_taxation,
                                                 clubbed_taxation=clubbed_taxation):
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def transfer_charge_request(self, client, test_case_id, booking_ids, bill_ids, charge_id, status_code,
                                has_slab_based_taxation, user_type=None):
        request_json = json.dumps(transfer_charge_builder.TransferChargeBuilder(booking_ids[1], bill_ids[0]).__dict__)
        uri = transfer_charge_uri.format(bill_ids[0], charge_id)
        with mock_role_manager(), mock_rule_engine(), \
                mock_tax_calculator_service(has_slab_based_taxation=has_slab_based_taxation):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def transfer_bulk_charge_request(self, client, test_case_id, booking_ids, bill_ids, charge_ids, status_code,
                                     user_type, tenant_config=[]):
        request_json = json.dumps(
            transfer_charge_builder.TransferChargeBuilder(destination_booking_id=booking_ids[1],
                                                          source_bill_id=bill_ids[0], is_bulk_transfer=True,
                                                          charge_ids=charge_ids, test_case_id=test_case_id).__dict__)
        uri = transfer_bulk_charges_uri.format(bill_ids=bill_ids[0])
        with mock_role_manager(), mock_rule_engine(), mock_tax_calculator_service(), mock_tenant_config(
                hotel_level_config=tenant_config):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def get_invoice_details(self, client, invoice_id, user_type=None):
        uri = get_invoice_for_invoice_id.format(invoice_id=invoice_id)
        response = self.request_processor(client, 'GET', uri, 200)
        return response.json

    def modify_locked_invoice_request(self, client, test_case_id, status_code, invoice_ids, bill_id, hotel_id,
                                      user_type):
        request_json = modify_locked_invoice_builder.ModifyLockedInvoiceBuilder(
            modify_locked_invoice_sheet_name, test_case_id, invoice_ids, bill_id).modify_locked_invoice_request()
        uri = modify_locked_invoice.format(bill_id)
        with mock_rule_engine(), mock_refund_rules():
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def reissue_non_financial_invoice_request(self, client, test_case_id, status_code, invoice_ids, bill_id, hotel_id,
                                              user_type):
        request_json = modify_locked_invoice_builder.ModifyLockedInvoiceBuilder(
            modify_locked_invoice_sheet_name, test_case_id, invoice_ids, bill_id).modify_locked_invoice_request()
        uri = reissue_non_financial_invoice.format(bill_id)
        with mock_rule_engine(), mock_refund_rules():
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def add_allowance_v2(self, client, test_case_id, charge_id, charge_split_id, status_code, bill_id,
                         user_type=None, is_tax_clubbed=[]):
        clubbed_taxation = True if is_tax_clubbed else False
        request_json = json.dumps(del_none(edit_charge_builder.AddAllowance
                                           (bill_id, charges_v2_sheet_name, test_case_id).__dict__))
        uri = add_allowance_uri.format(bill_id, charge_id, charge_split_id)
        with mock_tax_calculator_service(clubbed_taxation=clubbed_taxation), mock_role_manager(), \
                mock_tenant_config(hotel_level_config=is_tax_clubbed):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def add_buk_allowance(self, client, test_case_id, status_code, bill_id, user_type=None):
        request_json = json.dumps(del_none(edit_charge_builder.AddBulkAllowance
                                           (bill_id, charges_v2_sheet_name, test_case_id).__dict__))
        uri = add_bulk_allowance_uri.format(bill_id)
        with mock_tax_calculator_service(), mock_role_manager():
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def update_allowance_v2(self, client, charge_id, charge_split_id, allowance_id, status, status_code, bill_id,
                            user_type=None):
        request_json = json.dumps(del_none(edit_charge_builder.EditAllowanceV2
                                           (bill_id, status).__dict__))
        uri = edit_allowance_uri.format(bill_id, charge_id, charge_split_id, allowance_id)
        with mock_tax_calculator_service(), mock_role_manager():
            response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def get_folio_summary_request(self, client, test_case_id, bill_id, status_code, user_type=None,
                                  invoice_group_id=None):
        folio_number = get_folio_number(folio_summary_sheet_name, test_case_id)
        if test_case_id in ('FolioSummary_37', 'FolioSummary_38', 'FolioSummary_39', 'FolioSummary_40'):
            uri = get_folio_summary_with_invoice_uri.format(bill_id, folio_number, invoice_group_id)
        else:
            uri = get_folio_summary_uri.format(bill_id, folio_number)
        response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def get_active_payments_request(self, client, bill_id, status_code):
        uri = add_payment_uri.format(bill_id)
        response = self.request_processor(client, 'GET', uri, status_code)
        return response.json

    def get_payment_br_ref_id_request(self, client, status_code, payment_ref_id, user_type=None):
        uri = get_payment_by_ref_id_uri.format(payment_ref_id=payment_ref_id)
        response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def settle_by_spot_credit_request(self, client, test_case_id, bill_id, status_code, user_type=None):
        uri = settle_by_spot_credit_uri.format(bill_id=bill_id)
        request_json = settle_by_spot_credit_builder.SpotCreditRequest(spot_credit_sheet_name,
                                                                       test_case_id).settle_by_spot_credit_request()
        with mock_role_manager(), mock_get_setting_value_tenant(SPOT_CREDIT_ELIGIBLE_ACCOUNT_CATEGORY):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def recalculate_tax_request(self, client, test_case_id, bill_id, status_code, user_type=None):
        uri = recalculate_tax_uri.format(bill_id=bill_id)
        request_json = recalculate_tax_builder.RecalculateTaxRequest(charges_v2_sheet_name,
                                                                     test_case_id).recalculate_tax_request()
        with mock_role_manager(), mock_tax_call():
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def get_refund_mode(self, client, test_case_id, bill_id, status_code, refund_rules=None, user_type=None):
        request_json = get_refund_mode_builder.GetRefundModeRequest(payment_v2_sheet_name,
                                                                    test_case_id).get_refund_mode_request()
        uri = get_refund_mode_uri.format(bill_id=bill_id)
        with mock_role_manager():
            if refund_rules:
                with mock_refund_rules():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
            else:
                response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json
