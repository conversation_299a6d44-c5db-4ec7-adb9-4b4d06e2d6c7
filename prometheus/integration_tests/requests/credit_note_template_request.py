import json

from prometheus.integration_tests.builders import credit_note_template_builder
from prometheus.integration_tests.config.request_uris import upload_credit_note_template_uri, get_credit_notes_v2
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.tests.mockers import mock_role_manager
from prometheus.tests.mockers import mock_signed_url_generator
from prometheus.tests.mockers import mock_template_service


class CreditNoteTemplateRequest(BaseRequest):

    def upload_credit_note_template(self, client, status_code, bill_id, credit_note_id, user_type=None):
        request_json = json.dumps(credit_note_template_builder.CreditNoteTemplateBuilder().__dict__)
        uri = upload_credit_note_template_uri.format(bill_id=bill_id, credit_note_id=credit_note_id)

        with mock_role_manager(), mock_template_service(), mock_signed_url_generator():
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def get_credit_notes_v2(self, client, status_code, bill_id):
        uri = get_credit_notes_v2.format(bill_id=bill_id)
        response = self.request_processor(client, 'GET', uri, status_code)
        if status_code == 200:
            self.credit_note_ids = [response_data['credit_note_id'] for response_data in response.json['data']]
        return response.json
