import json
from prometheus.integration_tests.builders.create_inventory_blocks_builder import CreateInventoryBlockBuilder
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.request_uris import \
    get_inventory_block_details_uri, create_temp_inventory_block_uri
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.utilities.common_utils import del_none


class InventoryBlocksRequests(BaseRequest):
    def __init__(self):
        super().__init__()

    def create_temp_inventory_block(self, client, test_case_id, hotel_id, booking_id, status_code, user_type=None):
        request_body = CreateInventoryBlockBuilder(
            sheet_names.eci_lco_inventory_blocks_sheet_name, test_case_id, booking_id
        ).__dict__
        request_json = json.dumps(del_none(request_body))
        uri = create_temp_inventory_block_uri.format(hotel_id=hotel_id)
        response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def get_inventory_blocks_details(self, client, hotel_id, booking_id, block_type, status_code,
                                     user_type=None):
        uri = get_inventory_block_details_uri.format(hotel_id=hotel_id, booking_id=booking_id, block_type=block_type)
        response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)
        return response.json
