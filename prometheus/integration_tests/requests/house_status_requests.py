from prometheus.integration_tests.config.request_uris import *
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.utilities.common_utils import return_date


class HouseStatusRequests(BaseRequest):

    def house_statistics(self, client, test_case_id, status_code, property_id, user_type=None):
        uri = house_statistics_uri.format(hotel_id=property_id)
        response = self.request_processor(client, 'GET', uri, status_code, user_type)
        return response.json

    def get_current_house_status(self, client, status_code, hotel_id, user_type=None):
        uri = get_current_house_status_uri.format(hotel_id)
        response = self.request_processor(client, 'GET', uri, status_code, user_type)
        return response.json

    def get_arrival_departure_details(self, client, status_code, hotel_id, user_type=None):
        uri = get_arrival_and_departure_uri.format(hotel_id)
        response = self.request_processor(client, 'GET', uri, status_code, user_type)
        return response.json

    def get_house_view_display_booking_details(self, client, status_code, hotel_id, extras, user_type=None):
        checkin_date, checkout_date = return_date(extras[0]), return_date(extras[1])
        uri = get_house_view_display_bookings_uri.format(hotel_id, checkin_date, checkout_date, extras[2], extras[3],
                                                         extras[4], extras[5])
        response = self.request_processor(client, 'GET', uri, status_code, user_type)
        return response.json

    def get_hosue_keeping_todays_stats(self, client, status_code, hotel_id, user_type=None):
        uri = get_housekeeping_today_stats.format(hotel_id)
        response = self.request_processor(client, 'GET', uri, status_code, user_type)
        return response.json
