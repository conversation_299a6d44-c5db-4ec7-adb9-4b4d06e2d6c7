import json

import pytest
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from object_registry import locate_instance
from prometheus.application.end_of_day.night_audit_service import NightAuditService
from prometheus.application.hotel_settings.catalog_application_service import (
    CatalogApplicationService,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.integration_event.models import IntegrationEventModel
from prometheus.infrastructure.database import db_engine
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import checkout_booking
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkout_payload,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_payload,
    create_preview_invoice_with_allowance_decision_payload,
    create_preview_invoice_with_charge_decision_payload,
)
from prometheus.reporting.end_of_day_report.service import EndOfDayReportService
from prometheus.tests.mockers import (
    mock_authn_service_client,
    mock_authz_service_client,
    mock_catalog_client,
    mock_communication_service_client,
    mock_integration_event_publish_to_queue,
    mock_trial_balance_reporting_service,
)
from ths_common.constants.billing_constants import (
    ChargeStatus,
    InvoiceStatus,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.integration_event_constants import IntegrationEventType


@pytest.fixture
def job_scheduler_service() -> JobSchedulerService:
    return locate_instance(JobSchedulerService)


@pytest.fixture(scope='session')
def tenant_settings():
    return locate_instance(TenantSettings)


@pytest.fixture(scope='session')
def catalog_application_service():
    return locate_instance(CatalogApplicationService)


@pytest.fixture(scope='session')
def end_of_day_reporting_service():
    return locate_instance(EndOfDayReportService)


@pytest.mark.usefixtures('hotel_with_switch_over_time_in_next_10_minutes')
def test_booking_repo_returns_only_bookings_which_are_checked_in(
    booking_repo,
    old_checked_out_booking_and_bill,
    future_booking_and_bill,
    checked_in_booking_and_bill,
):
    old_booking, old_bill = old_checked_out_booking_and_bill
    booking_repo.save(old_booking)

    future_booking, future_bill = future_booking_and_bill
    booking_repo.save(future_booking)

    checked_in_booking, checked_in_bill = checked_in_booking_and_bill
    booking_repo.save(checked_in_booking)

    bookings = booking_repo.load_past_bookings_which_are_not_checked_out("0016932")
    assert len(bookings) == 1
    assert bookings[0].booking.booking_id == checked_in_booking.booking.booking_id


def add_payment(client, bill_id, amount):
    url = 'v1/bills/' + bill_id + '/payments'

    new_payment = dict(
        data=dict(
            amount=amount,
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.TREEBO,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    headers = {"X-User-Type": "super-admin"}
    client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )


def test_night_audit_marks_payment_posted(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    booking_repo,
    bill_repo,
    hotel_repo,
    house_status_repository,
):
    # create yesterday booking
    # chekin guest
    # checked_in_booking_and_bill_pending_checkout_2

    booking_aggregate, bill_aggregate = (
        checked_in_booking_and_bill_pending_checkout_2[0],
        checked_in_booking_and_bill_pending_checkout_2[1],
    )

    # add payment in DONE status - payment_in_pending_state
    add_payment(client, booking_aggregate.bill_id, '8000')

    # checkout booking

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(
        booking_aggregate.booking_id, client, preview_invoice_payload
    )
    booking_aggregate = booking_repo.load(booking_aggregate.booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_aggregate.booking_id, checkout_payload)

    # perform night audit
    old_business_date = active_hotel_aggregate.hotel.current_business_date
    night_audit_service = locate_instance(NightAuditService)
    night_audit_aggregate = night_audit_service.schedule_night_audit(
        booking_aggregate.hotel_id
    )
    with mock_catalog_client(), mock_trial_balance_reporting_service():
        with mock_communication_service_client(), mock_authz_service_client(), mock_authn_service_client():
            night_audit_service.perform_night_audit(booking_aggregate.hotel_id)

    # check if payment converted to POSTED
    # re-load the bill_aggregate
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    payments = bill_aggregate.payments

    # search for payment with amount 8000 and check it status is POSTED
    assert payments[-1].status == PaymentStatus.POSTED

    # check if posted date is same as calendar date
    assert payments[-1].posted_date == dateutils.current_date()

    hotel_aggregate = hotel_repo.load(active_hotel_aggregate.hotel_id)
    business_date = hotel_aggregate.hotel.current_business_date
    assert business_date == dateutils.add(old_business_date, days=1)
    house_statistics = house_status_repository.load_house_statistics(
        hotel_id="0016932",
        base_currency=CurrencyType("INR"),
        business_date=old_business_date,
    )
    assert house_statistics is not None


def test_night_audit_creates_integration_event(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    booking_repo,
    bill_repo,
    hotel_repo,
    integration_event_repo,
):
    # create yesterday booking
    # chekin guest
    # checked_in_booking_and_bill_pending_checkout_2

    booking_aggregate, bill_aggregate = (
        checked_in_booking_and_bill_pending_checkout_2[0],
        checked_in_booking_and_bill_pending_checkout_2[1],
    )

    # add payment in DONE status - payment_in_pending_state
    add_payment(client, booking_aggregate.bill_id, '8000')

    # checkout booking

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(
        booking_aggregate.booking_id, client, preview_invoice_payload
    )
    booking_aggregate = booking_repo.load(booking_aggregate.booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_aggregate.booking_id, checkout_payload)

    integration_event_repo.query(IntegrationEventModel).delete()
    db_engine.get_session().commit()

    # perform night audit
    night_audit_service = locate_instance(NightAuditService)

    # Assert integration event generated by schedule_night_audit method
    night_audit_service.schedule_night_audit(booking_aggregate.hotel_id)
    integration_event_aggregate = integration_event_repo.get_oldest_unpublished_event()
    assert (
        integration_event_aggregate.integration_event.event_type
        == IntegrationEventType.NIGHT_AUDIT_SCHEDULED
    )
    integration_event_aggregates = integration_event_repo.get_all_unpublished_events()
    assert len(integration_event_aggregates) == 1

    # Clear all events till now.
    integration_event_repo.query(IntegrationEventModel).delete()
    db_engine.get_session().commit()

    # Assert integration event generated by perform_night_audit method
    with mock_catalog_client(), mock_trial_balance_reporting_service(), mock_integration_event_publish_to_queue():
        with mock_communication_service_client(), mock_authz_service_client(), mock_authn_service_client():
            night_audit_service.perform_night_audit(booking_aggregate.hotel_id)

    integration_event_aggregates = integration_event_repo.get_all_unpublished_events()
    assert any(
        integration_event_aggregate.integration_event.event_type
        == IntegrationEventType.NIGHT_AUDIT_COMPLETED
        for integration_event_aggregate in integration_event_aggregates
    )

    event_types = {
        aggregate.integration_event.event_type
        for aggregate in integration_event_aggregates
    }
    assert event_types == {
        IntegrationEventType.NIGHT_AUDIT_COMPLETED,
        IntegrationEventType.INVOICE_UPDATED,
    }


def test_night_audit_marks_invoice_locked(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    booking_repo,
    bill_repo,
    hotel_repo,
    invoice_repo,
):
    # create yesterday booking
    # chekin guest
    # checked_in_booking_and_bill_pending_checkout_2
    booking_aggregate, bill_aggregate = (
        checked_in_booking_and_bill_pending_checkout_2[0],
        checked_in_booking_and_bill_pending_checkout_2[1],
    )

    # add payment in DONE status - payment_in_pending_state
    add_payment(client, booking_aggregate.bill_id, '8000')

    # checkout booking
    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id, invoice_ids = preview_invoice(
        booking_aggregate.booking_id,
        client,
        preview_invoice_payload,
        show_invoice_ids=True,
    )

    booking_aggregate = booking_repo.load(booking_aggregate.booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_aggregate.booking_id, checkout_payload)

    # perform night audit
    night_audit_service = locate_instance(NightAuditService)
    night_audit_aggregate = night_audit_service.schedule_night_audit(
        booking_aggregate.hotel_id
    )
    with mock_catalog_client(), mock_trial_balance_reporting_service():
        with mock_communication_service_client(), mock_authz_service_client(), mock_authn_service_client():
            night_audit_service.perform_night_audit(booking_aggregate.hotel_id)

    assert invoice_repo.load(invoice_ids[0]).invoice.status == InvoiceStatus.LOCKED


def test_night_audit_marks_allowance_consumed(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    booking_repo,
    bill_repo,
    hotel_repo,
    invoice_repo,
):
    # create yesterday booking
    # chekin guest
    # checked_in_booking_and_bill_pending_checkout_2
    booking_aggregate, bill_aggregate = (
        checked_in_booking_and_bill_pending_checkout_2[0],
        checked_in_booking_and_bill_pending_checkout_2[1],
    )

    bill_aggregate.consume_charges(
        charges=bill_aggregate.charges,
        business_date=active_hotel_aggregate.hotel.current_business_date,
    )
    bill_repo.update(bill_aggregate)

    bill_id, charge_id = bill_aggregate.bill_id, bill_aggregate.charges[0].charge_id
    charge_split_id = bill_aggregate.charges[0].charge_splits[0].charge_split_id

    url = (
        'v1/bills/'
        + bill_id
        + '/charges/'
        + str(charge_id)
        + '/charge-splits/'
        + str(charge_split_id)
        + '/allowances'
    )
    new_allowance = dict(
        data=dict(pretax_amount='50 INR', remarks='add new allowance'),
        resource_version=bill_aggregate.current_version(),
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_allowance),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200

    bill_agg = bill_repo.load(bill_id)
    assert (
        bill_agg.charges[0].charge_splits[0].allowances[0].status.value
        == ChargeStatus.CREATED.value
        == response.json['data']['status']
    )

    # add payment in DONE status - payment_in_pending_state
    add_payment(client, booking_aggregate.bill_id, '8000')

    # checkout booking
    preview_invoice_payload = create_preview_invoice_with_allowance_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id, invoice_ids = preview_invoice(
        booking_aggregate.booking_id,
        client,
        preview_invoice_payload,
        show_invoice_ids=True,
    )
    booking_aggregate = booking_repo.load(booking_aggregate.booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_aggregate.booking_id, checkout_payload)

    # perform night audit
    night_audit_service = locate_instance(NightAuditService)
    night_audit_aggregate = night_audit_service.schedule_night_audit(
        booking_aggregate.hotel_id
    )
    with mock_catalog_client(), mock_trial_balance_reporting_service():
        with mock_communication_service_client(), mock_authz_service_client(), mock_authn_service_client():
            night_audit_service.perform_night_audit(booking_aggregate.hotel_id)
            bill_agg = bill_repo.load(bill_id)
            assert (
                bill_agg.charges[0].charge_splits[0].allowances[0].status
                == ChargeStatus.CONSUMED
            )


def test_night_audit_rollsover_business_date(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    booking_repo,
    bill_repo,
    hotel_repo,
    invoice_repo,
):
    # create yesterday booking
    # chekin guest
    # checked_in_booking_and_bill_pending_checkout_2
    booking_aggregate, bill_aggregate = (
        checked_in_booking_and_bill_pending_checkout_2[0],
        checked_in_booking_and_bill_pending_checkout_2[1],
    )

    bill_aggregate.consume_charges(
        charges=bill_aggregate.charges,
        business_date=active_hotel_aggregate.hotel.current_business_date,
    )
    bill_repo.update(bill_aggregate)

    # checkout booking
    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id, invoice_ids = preview_invoice(
        booking_aggregate.booking_id,
        client,
        preview_invoice_payload,
        show_invoice_ids=True,
    )
    booking_aggregate = booking_repo.load(booking_aggregate.booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_aggregate.booking_id, checkout_payload)

    current_business_date_pre_night_audit = (
        active_hotel_aggregate.hotel.current_business_date
    )
    # perform night audit
    night_audit_service = locate_instance(NightAuditService)
    night_audit_service.schedule_night_audit(booking_aggregate.hotel_id)

    with mock_catalog_client(), mock_trial_balance_reporting_service():
        with mock_communication_service_client(), mock_authz_service_client(), mock_authn_service_client():
            night_audit_service.perform_night_audit(booking_aggregate.hotel_id)
            hotel_aggregate = hotel_repo.load(active_hotel_aggregate.hotel_id)
            current_business_date_post_night_audit = (
                hotel_aggregate.hotel.current_business_date
            )
            assert current_business_date_post_night_audit == dateutils.add(
                current_business_date_pre_night_audit, days=1
            )
