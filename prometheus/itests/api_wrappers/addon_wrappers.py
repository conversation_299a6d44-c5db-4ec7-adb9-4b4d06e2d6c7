import json

from prometheus.tests.mockers import mock_role_manager, mock_tax_calculator_service


def add_addon(client, booking_id, payload, expected_response_code=200):
    url = f'v1/bookings/{booking_id}/addons'
    with mock_role_manager():
        payload = json.dumps(payload)
        response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == expected_response_code
        if response.status_code != 200:
            return None
        addon_id = response.json['data']['addon_id']
    return addon_id


def add_addon_v2(client, booking_id, payload, expected_response_code=200):
    url = f'v2/bookings/{booking_id}/addons'
    with mock_role_manager():
        payload = json.dumps(payload)
        response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == expected_response_code
        if response.status_code != 200:
            return None
        addon_id = response.json['data']['addon_id']
    return addon_id


def delete_addon(client, booking_id, addon_id, expected_response_code=200):
    url = f'v1/bookings/{booking_id}/addons/{addon_id}'
    with mock_role_manager():
        response = client.delete(
            url,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == expected_response_code
        return response.json
