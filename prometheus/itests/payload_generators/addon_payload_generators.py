def addons_request(room_stay_id=1, charge_type="credit", bill_to_type="company"):
    return {
        "data": {
            "room_stay_id": room_stay_id,
            "expense_item_id": "123",
            "name": "Lunch",
            "pretax_price": 100,
            "quantity": 1,
            "charge_checkin": True,
            "charge_checkout": False,
            "charge_other_days": False,
            "charge_type": charge_type,
            "bill_to_type": bill_to_type,
        }
    }
