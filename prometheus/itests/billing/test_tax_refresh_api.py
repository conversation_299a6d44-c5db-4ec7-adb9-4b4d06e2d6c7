import json
from decimal import Decimal

import pytest
from treebo_commons.utils import dateutils

from prometheus.infrastructure.database import db_engine
from prometheus.itests.api_wrappers.booking_wrappers import make_booking_v2
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_rate_manager_client,
    mock_role_manager,
)
from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.booking_constants import BookingStatus
from ths_common.value_objects import TADetails


@pytest.mark.usefixtures("setup_hotel")
def test_refresh_tax_api_should_recalculate_tax(
    booking_repo,
    bill_repo,
    client,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=14)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        set_travel_agent_details=True,
        set_company_details=True,
        default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        set_booking_owner_phone_null=True,
        channel="b2b",
        with_payments=False,
    )

    create_booking_payload['travel_agent_details']['legal_details'].update(
        {'is_sez': True, 'has_lut': True}
    )
    booking_id = make_booking_v2(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_id = booking_aggregate.booking.bill_id
    bill_aggregate = bill_repo.load(bill_id)

    # do direct update in db
    travel_agent_details: TADetails = booking_aggregate.get_travel_agent_details()
    travel_agent_details.legal_details.is_sez = False
    travel_agent_details.legal_details.has_lut = False
    booking_aggregate.update_travel_agent_details(travel_agent_details)
    booking_repo.update(booking_aggregate)
    db_engine.get_session().commit()

    # use tax refresh api to recalculate tax
    billed_entity_of_ta = bill_aggregate.get_billed_entity_for_category(
        BilledEntityCategory.TRAVEL_AGENT
    )
    with mock_catalog_client(), mock_role_manager(), mock_rate_manager_client():
        url = f"v1/bills/{bill_id}/recalculate-tax"
        payload = json.dumps(
            {
                "data": {
                    "billed_entity_accounts": [
                        {
                            "billed_entity_id": billed_entity_of_ta.billed_entity_id,
                            "account_number": 1,
                        }
                    ]
                }
            }
        )
        response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == 200

    bill_aggregate = bill_repo.load(bill_id)
    for charge in bill_aggregate.charges:
        assert charge.tax_amount.amount != Decimal("0")
        sum_of_tax_details = sum(
            [tax_item.tax_amount.amount for tax_item in charge.tax_details]
        )
        assert (
            charge.tax_amount.amount + charge.pretax_amount.amount
            == charge.posttax_amount.amount
        )
        assert charge.tax_amount.amount == sum_of_tax_details

        for split in charge.charge_splits:
            assert split.tax.amount != Decimal("0")
            assert split.tax.amount + split.pre_tax.amount == split.post_tax.amount
            sum_of_tax_details = sum(
                [tax_item.tax_amount.amount for tax_item in split.tax_details]
            )
            assert split.tax.amount == sum_of_tax_details
