import json
from contextlib import contextmanager
from decimal import Decimal

import pytest
from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from prometheus.application.helpers.billed_entity_helper import (
    get_room_stay_default_billed_entity,
)
from prometheus.application.hotel_settings.dtos.payment_config_dto import (
    PaymentConfigDto,
)
from prometheus.application.hotel_settings.dtos.refund_rule_dto import RefundRuleDto
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from prometheus.infrastructure.external_clients.catalog_service.dtos.tenant_config_dto import (
    TenantConfigDto,
)
from prometheus.infrastructure.external_clients.payment_service_client import (
    PaymentServiceClient,
)
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import checkin_booking
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload_without_new_guest,
)
from prometheus.tests.mockers import (
    mock_auto_approve_payout_link_amount,
    mock_get_setting_value_tenant,
    mock_guarantee_enabled_config,
    mock_refund_response_by_payout_link,
    mock_refund_response_by_razorpay_api,
    mock_refund_rules,
    mock_role_manager,
    mock_rule_engine,
    mock_tenant_config,
    mock_tenant_config_for_issue_refund_max_condition,
    mock_tenant_config_for_payment_rules,
    mock_tenant_config_for_record_payment_max_value,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    CashRegisterNames,
    ChargeTypes,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import (
    BookingChannels,
    BookingStatus,
    GuaranteeTypes,
)
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.value_objects import GuaranteeInformation


@pytest.fixture
def closed_booking_and_bill(booking_repo, booking_and_bill):
    booking_aggregate = booking_and_bill[0]
    booking_aggregate.booking.status = BookingStatus.CHECKED_OUT
    booking_repo.update(booking_aggregate)
    return booking_and_bill


@pytest.fixture
def open_booking_and_bill(booking_repo, booking_and_bill):
    booking_aggregate = booking_and_bill[0]
    booking_aggregate.booking.status = BookingStatus.CONFIRMED
    booking_repo.update(booking_aggregate)
    return booking_and_bill


def test_cannot_allow_refund_greater_than_paid_amount_before_check_in(
    client, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    booking_aggregate = open_booking_and_bill[0]

    # Fdm only add payment for paid to hotel

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    default_billed_entity = get_room_stay_default_billed_entity(
        booking_aggregate.get_active_room_stays()[0], booking_aggregate, bill_aggregate
    )
    net_balance = abs(
        bill_aggregate.get_net_balance(
            default_billed_entity.get_account_for_new_assignment(ChargeTypes.NON_CREDIT)
        )
    )
    new_payment = dict(
        data=dict(
            amount=str(net_balance + Money('210', net_balance.currency)),
            paid_by=PaymentReceiverTypes.HOTEL,
            paid_to=PaymentReceiverTypes.GUEST,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    cash_register_id, cashier_session_id = create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 400
    assert response.json.get('errors')[0].get('code') == '********'

    cashier_session_aggregate = cashier_session_repo.load(cashier_session_id)
    assert len(cashier_session_aggregate.payments) == 0


def test_allow_refund_equal_to_account_balance(
    client, open_booking_and_bill, bill_repo, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    booking_aggregate = open_booking_and_bill[0]

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        # Fdm only add payment for paid to hotel

        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        default_billed_entity = bill_aggregate.get_billed_entity_for_category(
            booking_aggregate.get_default_billed_entity_category()
        )
        net_balance = abs(
            bill_aggregate.get_net_balance(
                default_billed_entity.get_account_for_new_assignment(
                    ChargeTypes.NON_CREDIT
                )
            )
        )
        new_payment = dict(
            data=dict(
                amount=str(net_balance),
                paid_by=PaymentReceiverTypes.HOTEL,
                paid_to=PaymentReceiverTypes.GUEST,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.REFUND.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )
        cash_register_id, cashier_session_id = create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200
        payment_id = response.json.get('data').get('payment_id')
        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        payment = bill_aggregate.get_payment(int(payment_id))
        cashier_session_aggregate = cashier_session_repo.load(cashier_session_id)
        cashier_payments = [
            cp
            for cp in cashier_session_aggregate.payments
            if cp.bill_payment_id == payment.payment_id
        ]
        assert len(cashier_payments) > 0


def create_cash_register_and_session(client, vendor_id, cash_register_name):
    new_cash_register_url = 'v1/cashier/cash-registers'
    new_cash_register = dict(
        data=dict(
            cash_register_name=cash_register_name,
            default_opening_balance="0 INR",
            vendor_id=vendor_id,
        )
    )
    cash_register_header = {
        "X-User-Type": "super-admin",
        "X-Hotel-Id": vendor_id,
        "X-User": "backend-system",
    }
    with mock_role_manager():
        cash_register_response = client.post(
            new_cash_register_url,
            data=json.dumps(new_cash_register),
            content_type='application/json',
            headers=cash_register_header,
        )
        assert cash_register_response.status_code == 200
        cash_register_id = cash_register_response.json['data']['cash_register_id']

        new_cashier_session_url = (
            'v1/cashier/cash-registers/' + cash_register_id + '/cashier-sessions'
        )
        new_cashier_session = dict(
            data=dict(
                opening_balance="0 INR",
                opening_balance_in_base_currency="0 INR",
                vendor_id=vendor_id,
            )
        )

        cashier_session_response = client.post(
            new_cashier_session_url,
            data=json.dumps(new_cashier_session),
            content_type='application/json',
            headers=cash_register_header,
        )
        assert cashier_session_response.status_code == 200

    cashier_session_data = cashier_session_response.json['data']
    return cashier_session_data.get('cash_register_id'), cashier_session_data.get(
        'cashier_session_id'
    )


def test_record_voucher_payment(client, open_booking_and_bill, bill_repo):
    bill_aggregate = open_booking_and_bill[1]

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='1000.0000',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.ONLINE,
                payment_mode=PaymentModes.VOUCHER,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        headers = {"X-User-Type": "super-admin"}
        with mock_rule_engine():
            response = client.post(
                url,
                data=json.dumps(new_payment),
                content_type='application/json',
                headers=headers,
            )

        assert response.status_code == 200


def test_payment_gets_recorded_in_default_billed_entity_and_account(
    client, open_booking_and_bill, bill_repo, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        # Fdm only add payment for paid to hotel

        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        cash_register_id, cashier_session_id = create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "fdm"}
        response = None
        with mock_rule_engine():
            response = client.post(
                url,
                data=json.dumps(new_payment),
                content_type='application/json',
                headers=headers,
            )

        assert response.status_code == 200

        payment_id = response.json.get('data').get('payment_id')
        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        payment = bill_aggregate.get_payment(int(payment_id))

        assert len(response.json['data']['payment_splits']) == 1
        billed_entity_account = response.json['data']['payment_splits'][0][
            'billed_entity_account'
        ]
        assert billed_entity_account is not None
        if (
            open_booking_and_bill[0].booking.source.channel_code
            == BookingChannels.B2B.value
        ):
            assert (
                billed_entity_account.get('billed_entity_id')
                == bill_aggregate.get_billed_entity_for_category(
                    BilledEntityCategory.BOOKER_COMPANY
                ).billed_entity_id
            )
            assert (
                billed_entity_account.get('account_number')
                == bill_aggregate.get_default_billed_entity_account(
                    BilledEntityCategory.BOOKER_COMPANY,
                    charge_type=ChargeTypes.NON_CREDIT,
                ).account_number
            )
        else:
            assert (
                billed_entity_account.get('billed_entity_id')
                == bill_aggregate.get_billed_entity_for_category(
                    BilledEntityCategory.BOOKER
                ).billed_entity_id
            )
            assert (
                billed_entity_account.get('account_number')
                == bill_aggregate.get_default_billed_entity_account(
                    BilledEntityCategory.BOOKER, charge_type=ChargeTypes.NON_CREDIT
                ).account_number
            )

        cashier_session_aggregate = cashier_session_repo.load(cashier_session_id)
        cashier_payments = [
            cp
            for cp in cashier_session_aggregate.payments
            if cp.bill_payment_id == payment.payment_id
        ]
        assert len(cashier_payments) > 0


def test_post_payment_api_should_change_payment_status_to_posted(
    client, bill_repo, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        cash_register_id, cashier_session_id = create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

        payment_id = response.json.get('data').get('payment_id')
        patch_payment_url = (
            'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
        )
        # Post Payment
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(status=PaymentStatus.POSTED.value),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )

        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        payment = bill_aggregate.get_payment(int(payment_id))
        assert payment.status == PaymentStatus.POSTED

        cashier_session_aggregate = cashier_session_repo.load(cashier_session_id)
        cashier_payments = [
            cp
            for cp in cashier_session_aggregate.payments
            if cp.bill_payment_id == payment.payment_id
        ]
        assert len(cashier_payments) > 0


def test_cancel_posted_payment_creates_refund(
    client, bill_repo, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    total_payments = len(bill_aggregate.payments)

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        cash_register_id, cashier_session_id = create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

        payment_id = response.json.get('data').get('payment_id')
        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        payment = bill_aggregate.get_payment(int(payment_id))

        cashier_session_aggregate = cashier_session_repo.load(cashier_session_id)
        cashier_payments = [
            cp
            for cp in cashier_session_aggregate.payments
            if cp.bill_payment_id == payment.payment_id
        ]
        assert len(cashier_payments) > 0

        # Post Payment
        patch_payment_url = (
            'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
        )
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(status=PaymentStatus.POSTED.value),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )

        # Cancel Payment. Should create refund. Payment Status should still be posted
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(status=PaymentStatus.CANCELLED.value),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )

        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        payment = bill_aggregate.get_payment(int(payment_id))
        assert payment.status == PaymentStatus.POSTED
        # Cancellation of posted payment should internally create a refund, thus increasing len of payments by 1.
        assert len(bill_aggregate.payments) == total_payments + 2

        refund = bill_aggregate.get_payment(int(payment_id) + 1)
        assert refund.payment_type == PaymentTypes.REFUND
        assert refund.amount == payment.amount

        cashier_session_aggregate = cashier_session_repo.load(cashier_session_id)
        cashier_payments = [
            cp
            for cp in cashier_session_aggregate.payments
            if cp.bill_payment_id in {refund.payment_id, payment.payment_id}
        ]
        # Should create cashier payment for refund and payment both
        assert len(cashier_payments) == 2


def test_edit_payment_should_update_cashier(
    client, bill_repo, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    total_payments = len(bill_aggregate.payments)

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        cash_register_id, cashier_session_id = create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

        payment_id = response.json.get('data').get('payment_id')

        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        payment = bill_aggregate.get_payment(int(payment_id))
        assert payment.amount.amount == Decimal('8165.7700')

        cashier_session_aggregate = cashier_session_repo.load(cashier_session_id)
        cashier_payments = [
            cp
            for cp in cashier_session_aggregate.payments
            if cp.bill_payment_id == payment.payment_id
        ]
        assert len(cashier_payments) == 1

        # Post Payment
        patch_payment_url = (
            'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
        )

        # Edit payment amount to 5000
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(amount='5000'),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )

        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        payment = bill_aggregate.get_payment(int(payment_id))
        assert payment.amount.amount == Decimal('5000')
        cashier_session_aggregate = cashier_session_repo.load(cashier_session_id)

        cashier_payments = [
            cashier_payment
            for cashier_payment in cashier_session_aggregate.payments
            if cashier_payment.bill_payment_id == payment.payment_id
        ]
        assert len(cashier_payments) == 1
        cashier_payment = cashier_payments[0]
        assert cashier_payment.bill_id == bill_aggregate.bill_id
        assert cashier_payment.bill_payment_id == payment.payment_id
        assert cashier_payment.amount.amount == Decimal('5000')


def get_account_type_for_payment(payment):
    if payment.payment_type in {PaymentTypes.PAYMENT, PaymentTypes.REFUND}:
        return ChargeTypes.NON_CREDIT
    else:
        return ChargeTypes.CREDIT


def test_add_payment_with_primary_guest_default_billed_entity_category(
    client, booking_and_bill_with_primary_guest_default_billed_entity_category
):
    booking_and_bill = (
        booking_and_bill_with_primary_guest_default_billed_entity_category
    )
    booking_aggregate = booking_and_bill[0]
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.HOTEL,
            paid_to=PaymentReceiverTypes.GUEST,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    cash_register_id, cashier_session_id = create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    default_billed_entity = get_room_stay_default_billed_entity(
        booking_aggregate.get_active_room_stays()[0], booking_aggregate, bill_aggregate
    )
    for payment in bill_aggregate.payments:
        for payment_split in payment.payment_splits:
            assert (
                payment_split.billed_entity_account
                == default_billed_entity.get_account_for_new_assignment(
                    get_account_type_for_payment(payment)
                )
            )


def test_add_payment_with_travel_agent_default_billed_entity_category(
    client, booking_and_bill_with_travel_agent_default_billed_entity_category
):
    booking_and_bill = booking_and_bill_with_travel_agent_default_billed_entity_category
    booking_aggregate = booking_and_bill[0]
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.HOTEL,
            paid_to=PaymentReceiverTypes.GUEST,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    cash_register_id, cashier_session_id = create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    default_billed_entity = get_room_stay_default_billed_entity(
        booking_aggregate.get_active_room_stays()[0], booking_aggregate, bill_aggregate
    )
    for payment in bill_aggregate.payments:
        for payment_split in payment.payment_splits:
            assert (
                payment_split.billed_entity_account
                == default_billed_entity.get_account_for_new_assignment(
                    get_account_type_for_payment(payment)
                )
            )


def test_can_allow_refund_greater_than_account_balance_for_checked_in_booking(
    client,
    open_booking_and_bill,
    cashier_session_repo,
    hotel_repo,
    active_hotel_aggregate,
    booking_repo,
    bill_repo,
):
    bill_aggregate = open_booking_and_bill[1]
    booking_aggregate = open_booking_and_bill[0]
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    room_stay_id = 1  # room_type_id is rt01
    room_id = "15"
    checkin_date = dateutils.current_datetime()
    checkin_payload = create_checkin_payload_without_new_guest(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        checkin_date,
        room_stay_id,
        room_id,
    )

    checkin_booking(client, booking_aggregate.booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_aggregate.booking_id)

    # Fdm only add payment for paid to hotel
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    default_billed_entity = get_room_stay_default_billed_entity(
        booking_aggregate.get_active_room_stays()[0], booking_aggregate, bill_aggregate
    )
    net_balance = abs(
        bill_aggregate.get_net_balance(
            default_billed_entity.get_account_for_new_assignment(ChargeTypes.NON_CREDIT)
        )
    )
    new_payment = dict(
        data=dict(
            amount=str(net_balance + Money('10', net_balance.currency)),
            paid_by=PaymentReceiverTypes.HOTEL,
            paid_to=PaymentReceiverTypes.GUEST,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    payment_id = response.json.get('data').get('payment_id')
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    payment = bill_aggregate.get_payment(int(payment_id))
    assert payment.amount.amount == Decimal('98')
    assert len(payment.payment_splits) == 1
    assert {
        ps.billed_entity_account.account_number for ps in payment.payment_splits
    } == {1}
    assert {
        ps.billed_entity_account.billed_entity_id for ps in payment.payment_splits
    } == {1}


def test_allow_refund_less_than_paid_amount_before_check_in(
    client, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    booking_aggregate = open_booking_and_bill[0]

    # Fdm only add payment for paid to hotel
    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        default_billed_entity = get_room_stay_default_billed_entity(
            booking_aggregate.get_active_room_stays()[0],
            booking_aggregate,
            bill_aggregate,
        )
        net_balance = abs(
            bill_aggregate.get_net_balance(
                default_billed_entity.get_account_for_new_assignment(
                    ChargeTypes.NON_CREDIT
                )
            )
        )
        new_payment = dict(
            data=dict(
                amount=str(net_balance + Money('20', net_balance.currency)),
                paid_by=PaymentReceiverTypes.HOTEL,
                paid_to=PaymentReceiverTypes.GUEST,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.REFUND.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )
        cash_register_id, cashier_session_id = create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200

        cashier_session_aggregate = cashier_session_repo.load(cashier_session_id)
        assert len(cashier_session_aggregate.payments) == 1


def test_record_payment_less_than_net_balance_without_cashier_session(
    client, open_booking_and_bill
):
    bill_aggregate = open_booking_and_bill[1]
    booking_aggregate = open_booking_and_bill[0]

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "false",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        default_billed_entity = get_room_stay_default_billed_entity(
            booking_aggregate.get_active_room_stays()[0],
            booking_aggregate,
            bill_aggregate,
        )
        net_balance = abs(
            bill_aggregate.get_net_balance(
                default_billed_entity.get_account_for_new_assignment(
                    ChargeTypes.NON_CREDIT
                )
            )
        )
        new_payment = dict(
            data=dict(
                amount=str(net_balance - Money('1', net_balance.currency)),
                paid_by=PaymentReceiverTypes.HOTEL,
                paid_to=PaymentReceiverTypes.GUEST,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200


def test_create_payment_with_splits_should_use_passed_account_in_payment_splits(
    client, bill_repo, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                payment_splits=[
                    dict(
                        billed_entity_account=dict(
                            billed_entity_id=1, account_number=1
                        ),
                        amount="3000",
                    ),
                    dict(
                        billed_entity_account=dict(
                            billed_entity_id=1, account_number=2
                        ),
                        amount="2000",
                    ),
                ],
                amount='5000',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

        payment_id = response.json.get('data').get('payment_id')

        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        payment = bill_aggregate.get_payment(int(payment_id))
        assert payment.amount.amount == Decimal('5000')
        assert len(payment.payment_splits) == 2
        assert {
            ps.billed_entity_account.account_number for ps in payment.payment_splits
        } == {1, 2}
        assert {
            ps.billed_entity_account.billed_entity_id for ps in payment.payment_splits
        } == {1}


def test_edit_payment_splits_should_use_passed_account_in_payment_splits(
    client, bill_repo, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                payment_splits=[
                    dict(
                        billed_entity_account=dict(
                            billed_entity_id=1, account_number=1
                        ),
                        amount="3000",
                    ),
                    dict(
                        billed_entity_account=dict(
                            billed_entity_id=1, account_number=2
                        ),
                        amount="2000",
                    ),
                ],
                amount='5000',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

        payment_id = int(response.json.get('data').get('payment_id'))

        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        payment = bill_aggregate.get_payment(payment_id)
        assert payment.amount.amount == Decimal('5000')
        assert len(payment.payment_splits) == 2
        assert {
            ps.billed_entity_account.account_number for ps in payment.payment_splits
        } == {1, 2}
        assert {
            ps.billed_entity_account.billed_entity_id for ps in payment.payment_splits
        } == {1}

        # Post Payment
        patch_payment_url = (
            'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + str(payment_id)
        )

        # Edit payment amount to 5000
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(
                        payment_splits=[
                            dict(
                                billed_entity_account=dict(
                                    billed_entity_id=1, account_number=3
                                ),
                                amount="2500",
                            ),
                            dict(
                                billed_entity_account=dict(
                                    billed_entity_id=1, account_number=4
                                ),
                                amount="2500",
                            ),
                        ]
                    ),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )
        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        payment = bill_aggregate.get_payment(int(payment_id))
        assert payment.amount.amount == Decimal('5000')
        assert len(payment.payment_splits) == 2
        assert {
            ps.billed_entity_account.account_number for ps in payment.payment_splits
        } == {3, 4}
        assert {
            ps.billed_entity_account.billed_entity_id for ps in payment.payment_splits
        } == {1}


def test_cannot_allow_edit_refund_greater_than_paid_amount_before_check_in(
    client, bill_repo, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    total_payments = len(bill_aggregate.payments)

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='500.00',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        refund = dict(
            data=dict(
                amount='50.00',
                paid_by=PaymentReceiverTypes.HOTEL,
                paid_to=PaymentReceiverTypes.GUEST,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.REFUND.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        first_payment_id = response.json.get('data').get('payment_id')
        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)

        # Post payment
        patch_payment_url = (
            'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + first_payment_id
        )
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(status=PaymentStatus.POSTED.value),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )

        response = client.post(
            url,
            data=json.dumps(refund),
            content_type='application/json',
            headers=headers,
        )
        second_payment_id = response.json.get('data').get('payment_id')
        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)

        # Post payment
        patch_payment_url = (
            'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + second_payment_id
        )
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(status=PaymentStatus.POSTED.value),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )

        first_payment = bill_aggregate.get_payment(int(first_payment_id))
        assert first_payment.status == PaymentStatus.POSTED
        assert len(bill_aggregate.payments) == total_payments + 2
        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
        second_payment = bill_aggregate.get_payment(int(second_payment_id))

        assert second_payment.status == PaymentStatus.POSTED
        assert second_payment.payment_type == PaymentTypes.REFUND

        # Edit Refund. Should disallow update since refund amount is more than account balance and net amount paid.
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(
                        amount='1000.00',
                        paid_by=PaymentReceiverTypes.HOTEL,
                        paid_to=PaymentReceiverTypes.GUEST,
                        payment_channel=PaymentChannels.FRONT_DESK,
                        payment_mode=PaymentModes.CASH,
                        payment_type=PaymentTypes.REFUND.value,
                        status=PaymentStatus.DONE.value,
                    ),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )

        assert response.json['errors'][0]['code'] == '********'
        assert response.status_code == 400


def test_payor_entity_is_populated_even_if_not_provided(
    client, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    booking_aggregate = open_booking_and_bill[0]

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    headers = {"X-User-Type": "super-admin"}
    response = client.get(
        url,
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    assert response.json.get('data')[0].get('payor_billed_entity_id') is not None
    assert response.json.get('data')[0].get(
        'payor_billed_entity_id'
    ) == response.json.get('data')[0].get('payment_splits')[0].get(
        'billed_entity_account'
    ).get(
        'billed_entity_id'
    )


def test_payment_is_created_with_provided_payor_entity(
    client, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    booking_aggregate = open_booking_and_bill[0]

    payor_billed_entity_id = bill_aggregate.billed_entities[1].billed_entity_id

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'

    new_payment = dict(
        data=dict(
            amount=str(Money('210', bill_aggregate.bill.base_currency)),
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
            payor_billed_entity_id=payor_billed_entity_id,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    assert response.json.get('data').get('payor_billed_entity_id') is not None
    assert (
        response.json.get('data').get('payor_billed_entity_id')
        == payor_billed_entity_id
    )


def test_get_payment_by_ref_id(client, open_booking_and_bill, cashier_session_repo):
    bill_aggregate = open_booking_and_bill[1]

    payor_billed_entity_id = bill_aggregate.billed_entities[1].billed_entity_id

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'

    new_payment = dict(
        data=dict(
            amount=str(Money('210', bill_aggregate.bill.base_currency)),
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
            payor_billed_entity_id=payor_billed_entity_id,
            payment_ref_id='PYD-49234809262205',
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    url_for_get_payment = 'v1/' + 'payments/' + 'PYD-49234809262205'
    response_get_payment = client.get(
        url_for_get_payment,
        content_type='application/json',
        headers=headers,
    )
    assert response_get_payment.status_code == 200


def test_refund_is_generated_to_provided_payor_entity(
    client, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    booking_aggregate = open_booking_and_bill[0]

    payor_billed_entity_id = bill_aggregate.billed_entities[1].billed_entity_id

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'

    new_payment = dict(
        data=dict(
            amount=str(Money('100', bill_aggregate.bill.base_currency)),
            paid_by=PaymentReceiverTypes.HOTEL,
            paid_to=PaymentReceiverTypes.GUEST,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
            payor_billed_entity_id=payor_billed_entity_id,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    assert response.json.get('data').get('payor_billed_entity_id') is not None
    assert (
        response.json.get('data').get('payor_billed_entity_id')
        == payor_billed_entity_id
    )


def test_add_payment_for_more_than_one_billed_entity(
    client, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    booking_aggregate = open_booking_and_bill[0]

    payor_billed_entity_id = bill_aggregate.billed_entities[1].billed_entity_id

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'

    new_payment = dict(
        data=dict(
            amount=str(Money('3000', bill_aggregate.bill.base_currency)),
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
            payor_billed_entity_id=payor_billed_entity_id,
            payment_splits=[
                dict(
                    amount="1000.00 INR",
                    billed_entity_account=dict(
                        billed_entity_id="1", account_number="1"
                    ),
                ),
                dict(
                    amount="2000.00 INR",
                    billed_entity_account=dict(
                        billed_entity_id="2", account_number="1"
                    ),
                ),
            ],
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    assert response.json.get('data').get('payor_billed_entity_id') is not None
    assert (
        response.json.get('data').get('payor_billed_entity_id')
        == payor_billed_entity_id
    )
    assert len(response.json.get('data').get('payment_splits')) == 2
    assert [
        ps.get('billed_entity_account').get('billed_entity_id')
        for ps in response.json.get('data').get('payment_splits')
    ] == [1, 2]


def test_update_payment_for_more_than_one_billed_entity(
    client, open_booking_and_bill, cashier_session_repo, bill_repo
):
    bill_aggregate = open_booking_and_bill[1]
    booking_aggregate = open_booking_and_bill[0]

    payor_billed_entity_id = bill_aggregate.billed_entities[1].billed_entity_id

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'

    new_payment = dict(
        data=dict(
            amount=str(Money('3000', bill_aggregate.bill.base_currency)),
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
            payor_billed_entity_id=payor_billed_entity_id,
            payment_splits=[
                dict(
                    amount="1000.00 INR",
                    billed_entity_account=dict(
                        billed_entity_id="1", account_number="1"
                    ),
                ),
                dict(
                    amount="2000.00 INR",
                    billed_entity_account=dict(
                        billed_entity_id="1", account_number="1"
                    ),
                ),
            ],
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    assert response.json.get('data').get('payor_billed_entity_id') is not None
    assert (
        response.json.get('data').get('payor_billed_entity_id')
        == payor_billed_entity_id
    )
    assert len(response.json.get('data').get('payment_splits')) == 2

    bill_aggregate = bill_repo.load(bill_aggregate.bill_id)
    patch_payment_url = (
        'v1/bills/'
        + bill_aggregate.bill.bill_id
        + '/payments/'
        + response.json.get('data').get('payment_id')
    )
    response = client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(
                    payment_splits=[
                        dict(
                            billed_entity_account=dict(
                                billed_entity_id=1, account_number=1
                            ),
                            amount="1000",
                        ),
                        dict(
                            billed_entity_account=dict(
                                billed_entity_id=2, account_number=1
                            ),
                            amount="2000",
                        ),
                    ]
                ),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )
    assert response.status_code == 200
    assert response.json.get('data').get('payor_billed_entity_id') is not None
    assert (
        response.json.get('data').get('payor_billed_entity_id')
        == payor_billed_entity_id
    )
    assert len(response.json.get('data').get('payment_splits')) == 2
    assert [
        ps.get('billed_entity_account').get('billed_entity_id')
        for ps in response.json.get('data').get('payment_splits')
    ] == [1, 2]


def test_add_payment_with_invalid_paid_by_paid_to_should_properly_default_payments_from_config(
    client, booking_and_bill_with_travel_agent_default_billed_entity_category, bill_repo
):
    booking_and_bill = booking_and_bill_with_travel_agent_default_billed_entity_category
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.TREEBO,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.HOTEL_COLLECTIBLE,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_tenant_config_for_payment_rules():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    for payment in bill_aggregate.payments:
        if payment.payment_mode == 'hotel_collectible':
            assert (payment.paid_by, payment.paid_to) == ('hotel', 'hotel')


def test_add_refund_with_invalid_paid_by_paid_to_should_properly_default_payments_from_config(
    client, booking_and_bill_with_travel_agent_default_billed_entity_category, bill_repo
):
    booking_and_bill = booking_and_bill_with_travel_agent_default_billed_entity_category
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.TREEBO,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.RAZORPAY_API,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_tenant_config_for_payment_rules():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    for payment in bill_aggregate.payments:
        if payment.payment_mode == 'razorpay_api':
            assert (payment.paid_by, payment.paid_to) == ('treebo', 'ta')


@contextmanager
def mock_auto_refund_payment_service(payout=True):
    real_refund = PaymentServiceClient.issue_refund
    real_get_setting_value = TenantSettings.get_setting_value

    def mock_auto_refund_config(self, setting_name, hotel_id=None):
        if setting_name == TenantSettingName.IS_PG_REFUND_ENABLED_FROM_PMS.value:
            return True
        return real_get_setting_value(self, setting_name, hotel_id)

    def mock_issue_refund(self, refund_dto):
        py_ids = refund_dto.get('payment_ids')
        if 'bal_available' in py_ids:
            return dict(refund_order_id='test_refund_id')
        if payout:
            return dict(
                payout_link=dict(
                    pg_payout_id='payout_id',
                    short_url='https://test.com/i/ZQVkB93D',
                    expire_by=1696092724,
                )
            )

    TenantSettings.get_setting_value = mock_auto_refund_config
    PaymentServiceClient.issue_refund = mock_issue_refund
    yield
    PaymentServiceClient.issue_refund = real_refund
    TenantSettings.get_setting_value = real_get_setting_value


def test_refund_should_issue_payout_if_no_eligible_payment_is_available(
    client, booking_and_bill_with_travel_agent_default_billed_entity_category, bill_repo
):
    booking_and_bill = booking_and_bill_with_travel_agent_default_billed_entity_category
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'

    new_payment = dict(
        data=dict(
            amount='1',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.TREEBO,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.RAZORPAY_API,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
            payout_contact_details=dict(
                email="<EMAIL>",
                phone=dict(
                    country_code="+91",
                    number="9713708166",
                ),
            ),
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_auto_refund_payment_service(), mock_tenant_config_for_payment_rules(), mock_refund_rules(), mock_refund_response_by_payout_link(), mock_auto_approve_payout_link_amount():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    for payment in bill_aggregate.payments:
        if (
            payment.payment_mode == 'razorpay_api'
            and payment.payment_type == PaymentTypes.REFUND
        ):
            assert payment.payout_details


def test_refund_should_issue_refund_if_eligible_payment_is_available(
    client, booking_and_bill_with_travel_agent_default_billed_entity_category, bill_repo
):
    booking_and_bill = booking_and_bill_with_travel_agent_default_billed_entity_category
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'

    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.TREEBO,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.RAZORPAY_API,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
            payment_ref_id='bal_available',
        ),
        resource_version=1,
    )
    new_refund = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.TREEBO,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.RAZORPAY_API,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_auto_refund_payment_service(), mock_tenant_config_for_payment_rules(), mock_refund_rules(), mock_refund_response_by_razorpay_api():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    for payment in bill_aggregate.payments:
        if (
            payment.payment_mode == 'razorpay_api'
            and payment.payment_type == PaymentTypes.REFUND
        ):
            assert not payment.payout_details
            assert payment.payment_ref_id == 'rfnd_MxVuhzlcisceOL'


def test_update_payment_with_invalid_paid_by_paid_to_should_properly_default_payments_from_config(
    client, booking_and_bill_with_travel_agent_default_billed_entity_category, bill_repo
):
    booking_and_bill = booking_and_bill_with_travel_agent_default_billed_entity_category
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_tenant_config_for_payment_rules():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200

    patch_payment_url = (
        'v1/bills/'
        + bill_aggregate.bill.bill_id
        + '/payments/'
        + response.json.get('data').get('payment_id')
    )
    with mock_tenant_config_for_payment_rules():
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(
                        paid_by=PaymentReceiverTypes.GUEST,
                        paid_to=PaymentReceiverTypes.TREEBO,
                        payment_mode=PaymentModes.HOTEL_COLLECTIBLE,
                    ),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    for payment in bill_aggregate.payments:
        if payment.payment_mode == 'hotel_collectible':
            assert (payment.paid_by, payment.paid_to) == ('hotel', 'hotel')


def test_update_payment_with_invalid_payment_method_should_default_paid_by_paid_to_invoke_slack_alert(
    client, booking_and_bill_with_travel_agent_default_billed_entity_category, bill_repo
):
    booking_and_bill = booking_and_bill_with_travel_agent_default_billed_entity_category
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.ONLINE,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_tenant_config_for_payment_rules():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200

    patch_payment_url = (
        'v1/bills/'
        + bill_aggregate.bill.bill_id
        + '/payments/'
        + response.json.get('data').get('payment_id')
    )
    slack_alert_event = None
    actual_record_event = SlackAlertServiceClient.record_event

    def mocked_record_event(self, event_type, event_payload):
        nonlocal slack_alert_event
        slack_alert_event = event_type

    SlackAlertServiceClient.record_event = mocked_record_event
    with mock_tenant_config_for_payment_rules():
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(
                        paid_by=PaymentReceiverTypes.GUEST,
                        paid_to=PaymentReceiverTypes.TREEBO,
                        payment_mode='treebo_points',
                    ),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    for payment in bill_aggregate.payments:
        if payment.payment_mode == 'treebo_points':
            assert (payment.paid_by, payment.paid_to) == ('ta', 'treebo')

    assert slack_alert_event == 'payment_config_missing'

    SlackAlertServiceClient.record_event = actual_record_event


def test_update_payment_with_invalid_payment_method_should_from_fd_should_raise_exception(
    client, booking_and_bill_with_travel_agent_default_billed_entity_category, bill_repo
):
    booking_and_bill = booking_and_bill_with_travel_agent_default_billed_entity_category
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_tenant_config_for_payment_rules():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200

    patch_payment_url = (
        'v1/bills/'
        + bill_aggregate.bill.bill_id
        + '/payments/'
        + response.json.get('data').get('payment_id')
    )
    with mock_tenant_config_for_payment_rules():
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(
                        paid_by=PaymentReceiverTypes.GUEST,
                        paid_to=PaymentReceiverTypes.TREEBO,
                        payment_mode='treebo_points',
                    ),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )
        assert response.status_code == 400


@contextmanager
def mock_tenant_config_for_payment_rules_with_tr():
    get_payment_configs_for_all_allowed_payment_methods = (
        TenantSettings.get_payment_configs_for_all_allowed_payment_methods
    )

    def mock_allowed_paid_by_and_to_for_payment_methods(self, hotel_id=None):
        config = TenantConfigDto(
            config_name="payment_config",
            config_value='{"payment":[{"payment_method":"paid_by_treebo","paid_to":"treebo","allowed_paid_by":["treebo"]},{"payment_method":"treebo_points","paid_to":"treeebo","allowed_paid_by":["guest"]},{"payment_method":"treebo_corporate_rewards","paid_to":"treebo","allowed_paid_by":["ta","corporate"]},{"payment_method":"hotel_collectible","paid_to":"hotel","allowed_paid_by":["hotel"]},{"payment_method":"UPI","paid_to":"hotel"},{"payment_method":"razorpay_api","paid_to":"treebo"},{"payment_method":"amazon_pay","paid_to":"treebo"},{"payment_method":"bank_transfer_hotel","paid_to":"hotel"},{"payment_method":"bank_transfer_treebo","paid_to":"treebo"},{"payment_method":"cash","paid_to":"hotel"},{"payment_method":"debit_card","paid_to":"hotel"},{"payment_method":"credit_card","paid_to":"hotel"},{"payment_method":"paid_at_ota","paid_to":"treebo"},{"payment_method":"phone_pe","paid_to":"treebo"},{"payment_method":"other","paid_to":"hotel"},{"payment_method":"air_pay","paid_to":"treebo"},{"payment_method":"payment_service","paid_to":"treebo"},{"payment_method":"payment_link","paid_to":"treebo"},{"payment_method":"razorpay_payment_gateway","paid_to":"treebo"},{"payment_method":"credit_shell","paid_to":"treebo"}],"refund":[{"payment_method":"razorpay_payment_gateway","paid_to":"treebo"},{"payment_method":"payment_service","paid_to":"treebo"},{"payment_method":"paid_by_treebo","paid_to":"treebo","allowed_paid_by":["treebo"]},{"payment_method":"razorpay_api","paid_to":"treebo"},{"payment_method":"amazon_pay","paid_to":"treebo"},{"payment_method":"bank_transfer_hotel","paid_to":"hotel"},{"payment_method":"bank_transfer_treebo","paid_to":"treebo"},{"payment_method":"cash","paid_to":"hotel"},{"payment_method":"debit_card","paid_to":"hotel"},{"payment_method":"credit_card","paid_to":"hotel"},{"payment_method":"paid_at_ota","paid_to":"treebo"},{"payment_method":"phone_pe","paid_to":"treebo"},{"payment_method":"UPI","paid_to":"hotel"},{"payment_method":"other","paid_to":"hotel"},{"payment_method":"air_pay","paid_to":"treebo"}]}',
            value_type="json",
        )
        allowed_paid_by_and_to_value = config.get_config_value()
        return {
            "payment": {
                rule['payment_method']: PaymentConfigDto(**rule)
                for rule in allowed_paid_by_and_to_value["payment"]
            },
            "refund": {
                rule['payment_method']: PaymentConfigDto(**rule)
                for rule in allowed_paid_by_and_to_value["refund"]
            },
        }

    TenantSettings.get_payment_configs_for_all_allowed_payment_methods = (
        mock_allowed_paid_by_and_to_for_payment_methods
    )
    yield
    TenantSettings.get_payment_configs_for_all_allowed_payment_methods = (
        get_payment_configs_for_all_allowed_payment_methods
    )


@contextmanager
def mock_refund_configs(refund_rule):
    real_get_refund_rule = TenantSettings.get_refund_rule

    def mock_get_refund_rule(self, hotel_id):
        return RefundRuleDto(**refund_rule)

    TenantSettings.get_refund_rule = mock_get_refund_rule
    yield
    TenantSettings.get_refund_rule = real_get_refund_rule


def get_refund_mode_details(
    client, bill_id, payload, refund_rule, expected_status=200, user_type="super-admin"
):
    headers = {"X-User-Type": user_type}
    url = f'v1/bills/{bill_id}/get-refund-mode'
    with mock_refund_configs(refund_rule), mock_role_manager():
        response = client.post(
            url,
            data=json.dumps(dict(data=payload)),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == expected_status
    if expected_status == 200:
        return response.json.get('data')
    return None


def _get_refund_payload(items):
    payload = []
    for item in items:
        index, expected_mode, expected_refund_splits, requested_mode, amount = item
        payload.append(
            dict(amount=amount, requested_refund_mode=requested_mode, item_id=index)
        )
    return payload


def test_get_refund_mode(
    client, booking_and_bill_with_primary_guest_as_def_be, bill_repo
):
    booking_and_bill = booking_and_bill_with_primary_guest_as_def_be
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payment-list'
    new_payments = dict(
        data=dict(
            payments=[
                dict(
                    amount="3",
                    paid_by=PaymentReceiverTypes.CORPORATE,
                    paid_to=PaymentReceiverTypes.TREEBO,
                    payment_channel=PaymentChannels.FRONT_DESK,
                    payment_mode=PaymentModes.TREEBO_POINTS,
                    payment_type=PaymentTypes.PAYMENT.value,
                    status=PaymentStatus.DONE.value,
                ),
                dict(
                    amount="4",
                    paid_by=PaymentReceiverTypes.GUEST,
                    paid_to=PaymentReceiverTypes.TREEBO,
                    payment_channel=PaymentChannels.FRONT_DESK,
                    payment_mode=PaymentModes.RAZORPAY_API,
                    payment_type=PaymentTypes.PAYMENT.value,
                    status=PaymentStatus.DONE.value,
                ),
                dict(
                    amount="6",
                    paid_by=PaymentReceiverTypes.GUEST,
                    paid_to=PaymentReceiverTypes.TREEBO,
                    payment_channel=PaymentChannels.FRONT_DESK,
                    payment_mode=PaymentModes.AIR_PAY,
                    payment_type=PaymentTypes.PAYMENT.value,
                    status=PaymentStatus.DONE.value,
                ),
            ]
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_tenant_config_for_payment_rules(), mock_tenant_config_for_payment_rules_with_tr():
        response = client.post(
            url,
            data=json.dumps(new_payments),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200

    test_cases = [
        [(1, PaymentModes.TREEBO_POINTS, {PaymentModes.TREEBO_POINTS}, None, "2 INR")],
        [
            (
                1,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API, PaymentModes.TREEBO_POINTS},
                None,
                "4 INR",
            )
        ],
        [
            (
                1,
                PaymentModes.AIR_PAY,
                {PaymentModes.AIR_PAY, PaymentModes.TREEBO_POINTS},
                None,
                "8 INR",
            )
        ],
        [
            (
                1,
                PaymentModes.PAYOUT_LINK,
                {PaymentModes.PAYOUT_LINK, PaymentModes.TREEBO_POINTS},
                None,
                "10 INR",
            )
        ],
        [
            (
                1,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API, PaymentModes.TREEBO_POINTS},
                None,
                "4 INR",
            )
        ],
        [
            (
                1,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API, PaymentModes.TREEBO_POINTS},
                None,
                "4 INR",
            ),
            (2, PaymentModes.RAZORPAY_API, {PaymentModes.RAZORPAY_API}, None, "3 INR"),
        ],
        [
            (
                1,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API, PaymentModes.TREEBO_POINTS},
                None,
                "4 INR",
            ),
            (2, PaymentModes.AIR_PAY, {PaymentModes.AIR_PAY}, None, "5 INR"),
        ],
        [
            (
                1,
                PaymentModes.TREEBO_POINTS,
                {PaymentModes.TREEBO_POINTS},
                None,
                "1 INR",
            ),
            (
                2,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API, PaymentModes.TREEBO_POINTS},
                None,
                "5 INR",
            ),
        ],
        [
            (
                1,
                PaymentModes.TREEBO_POINTS,
                {PaymentModes.TREEBO_POINTS},
                None,
                "1 INR",
            ),
            (
                2,
                PaymentModes.PAYOUT_LINK,
                {PaymentModes.TREEBO_POINTS, PaymentModes.PAYOUT_LINK},
                None,
                "10 INR",
            ),
        ],
    ]

    refund_rule = {
        "refund_mode_priority_list": [
            "treebo_points",
            "treebo_corporate_rewards",
            "razorpay_api",
            "air_pay",
            "payout_link",
        ],
        "refund_modes_eligible_for_partial_refunds": [
            "treebo_points",
            "treebo_corporate_rewards",
        ],
        "is_payout_link_enabled": True,
    }
    for index, test_data in enumerate(test_cases):
        req_payload = _get_refund_payload(test_data)
        response = get_refund_mode_details(
            client,
            bill_aggregate.bill_id,
            req_payload,
            refund_rule,
        )
        response_items = {
            item.get('item_id'): (
                item.get('refund_mode'),
                {split.get('refund_mode') for split in item.get('refund_splits', [])},
            )
            for item in response
        }
        for test_data_item in test_data:
            item_id, refund_mode, split_modes, _, _ = test_data_item
            actual_mode, actual_splits = response_items[item_id]
            assert refund_mode == actual_mode, index
            assert split_modes == actual_splits, index

    test_cases = [
        [
            (
                1,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API},
                PaymentModes.RAZORPAY_API,
                "2 INR",
            )
        ],
        [
            (
                1,
                PaymentModes.PAYOUT_LINK,
                {PaymentModes.PAYOUT_LINK},
                PaymentModes.RAZORPAY_API,
                "5 INR",
            )
        ],
        [
            (
                1,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API},
                PaymentModes.RAZORPAY_API,
                "4 INR",
            )
        ],
        [(1, None, {}, PaymentModes.AIR_PAY, "10 INR")],
        [
            (
                1,
                PaymentModes.AIR_PAY,
                {PaymentModes.AIR_PAY},
                PaymentModes.AIR_PAY,
                "6 INR",
            )
        ],
        [
            (
                1,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API},
                PaymentModes.RAZORPAY_API,
                "2 INR",
            ),
            (
                2,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API},
                PaymentModes.RAZORPAY_API,
                "2 INR",
            ),
        ],
        [
            (
                1,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API},
                PaymentModes.RAZORPAY_API,
                "2 INR",
            ),
            (
                2,
                PaymentModes.PAYOUT_LINK,
                {PaymentModes.PAYOUT_LINK},
                PaymentModes.RAZORPAY_API,
                "3 INR",
            ),
            (
                3,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API},
                PaymentModes.RAZORPAY_API,
                "1 INR",
            ),
        ],
        [
            (
                1,
                PaymentModes.RAZORPAY_API,
                {PaymentModes.RAZORPAY_API},
                PaymentModes.AIR_PAY,
                "2 INR",
            ),
            (2, None, {}, PaymentModes.AIR_PAY, "5 INR"),
        ],
    ]
    refund_rule = {
        "refund_mode_priority_list": [
            "treebo_points",
            "treebo_corporate_rewards",
            "razorpay_api",
            "air_pay",
            "payout_link",
        ],
        "refund_modes_eligible_for_partial_refunds": [
            "treebo_points",
            "treebo_corporate_rewards",
        ],
        "is_payout_link_enabled": True,
    }
    for index, test_data in enumerate(test_cases):
        req_payload = _get_refund_payload(test_data)

        if index in {3, 7}:
            get_refund_mode_details(
                client,
                bill_aggregate.bill_id,
                req_payload,
                refund_rule,
                expected_status=400,
                user_type='backend-system',
            )
            continue
        response = get_refund_mode_details(
            client,
            bill_aggregate.bill_id,
            req_payload,
            refund_rule,
            user_type='backend-system',
        )
        response_items = {
            item.get('item_id'): (
                item.get('refund_mode'),
                {split.get('refund_mode') for split in item.get('refund_splits', [])},
            )
            for item in response
        }
        for test_data_item in test_data:
            item_id, refund_mode, split_modes, _, _ = test_data_item
            actual_mode, actual_splits = response_items[item_id]
            assert refund_mode == actual_mode, index
            assert split_modes == actual_splits, index


def test_create_payment_with_add_payment_with_payment_rules(
    client, booking_and_bill_with_primary_guest_as_def_be, bill_repo
):
    booking_and_bill = booking_and_bill_with_primary_guest_as_def_be
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='300',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.TREEBO_CORPORATE_REWARDS,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_tenant_config_for_record_payment_max_value():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 400

    new_payment['data']['amount'] = '50'

    with mock_tenant_config_for_record_payment_max_value():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200


def test_create_payment_with_add_payment_with_refund_rules(
    client, booking_and_bill_with_primary_guest_as_def_be, bill_repo
):
    booking_and_bill = booking_and_bill_with_primary_guest_as_def_be
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='300',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.TREEBO,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200

    # test refund flow for pah condition

    new_refund = dict(
        data=dict(
            amount='500',
            paid_by=PaymentReceiverTypes.HOTEL,
            paid_to=PaymentReceiverTypes.GUEST,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 400

    new_refund['data']['amount'] = '300'

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    assert response.status_code == 200
    assert (bill_aggregate.get_payment_amount_paid_to_payer('hotel'), Money("100"))

    new_refund['data']['amount'] = '150'

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 400

    # test refund flow for ptt condition

    new_refund = dict(
        data=dict(
            amount='450',
            paid_by=PaymentReceiverTypes.TREEBO,
            paid_to=PaymentReceiverTypes.GUEST,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 400

    new_refund['data']['amount'] = '350'

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    assert response.status_code == 200
    assert (bill_aggregate.get_payment_amount_paid_to_payer('treebo'), Money("50"))


def test_edit_payment_with_add_payment_with_payment_rules(
    client, booking_and_bill_with_primary_guest_as_def_be, bill_repo
):
    booking_and_bill = booking_and_bill_with_primary_guest_as_def_be
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='44',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.TREEBO_CORPORATE_REWARDS,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}

    with mock_tenant_config_for_record_payment_max_value():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)

    payment_id = '1'
    patch_payment_url = (
        'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
    )

    # Edit payment amount to test_edit_payment_with_add_payment_with_payment_rules, test_payment_api.py:1888
    with mock_tenant_config_for_record_payment_max_value():
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(amount='447'),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 400

    with mock_tenant_config_for_record_payment_max_value():
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(amount='407'),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 400
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    assert (bill_aggregate.get_payment_amount_paid_to_payer('hotel'), Money("1"))


def test_edit_payment_with_refund_rules(
    client, booking_and_bill_with_primary_guest_as_def_be, bill_repo
):
    booking_and_bill = booking_and_bill_with_primary_guest_as_def_be
    bill_aggregate = booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='300',
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.RAZORPAY_API,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_tenant_config_for_payment_rules():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200

    # test refund flow for pah condition

    new_refund = dict(
        data=dict(
            amount='500',
            paid_by=PaymentReceiverTypes.HOTEL,
            paid_to=PaymentReceiverTypes.GUEST,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 400

    new_refund['data']['amount'] = '250'

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    assert response.status_code == 200
    assert (bill_aggregate.get_payment_amount_paid_to_payer('hotel'), Money("100"))

    payment_id = '4'
    patch_payment_url = (
        'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
    )

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(amount='500'),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 400

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(amount='300'),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    assert (bill_aggregate.get_payment_amount_paid_to_payer('treebo'), Money("50"))

    new_refund['data']['amount'] = '350'
    new_refund['data']['paid_by'] = PaymentReceiverTypes.TREEBO

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200

    new_refund['data']['amount'] = '70'
    new_refund['data']['paid_by'] = PaymentReceiverTypes.HOTEL

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    assert response.status_code == 400

    new_refund['data']['amount'] = '50'

    with mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    assert response.status_code == 200


def test_edit_payment_with_refund_rules_by_changing_payment_mode(
    client, booking_and_bill_with_primary_guest_as_def_be, bill_repo
):
    booking_and_bill = booking_and_bill_with_primary_guest_as_def_be
    bill_aggregate = booking_and_bill[1]

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    headers = {"X-User-Type": "super-admin"}

    # test refund flow for pah condition
    new_refund = dict(
        data=dict(
            amount='300',
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    with mock_tenant_config_for_payment_rules(), mock_tenant_config_for_issue_refund_max_condition():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200

    payment_id = '3'
    patch_payment_url = (
        'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
    )

    with mock_tenant_config_for_payment_rules(), mock_tenant_config_for_issue_refund_max_condition():
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(payment_mode=PaymentModes.RAZORPAY_API),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='300',
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.RAZORPAY_API,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    with mock_tenant_config_for_payment_rules():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200

    payment_id = '3'
    patch_payment_url = (
        'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
    )

    with mock_tenant_config_for_payment_rules(), mock_tenant_config_for_issue_refund_max_condition():
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(payment_mode=PaymentModes.RAZORPAY_API),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200


def test_post_and_patch_payment_api_to_accept_refund_rule(
    client, bill_repo, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        cash_register_id, cashier_session_id = create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

        new_refund = dict(
            data=dict(
                amount='350',
                paid_by=PaymentReceiverTypes.HOTEL,
                paid_to=PaymentReceiverTypes.GUEST,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.REFUND.value,
                status=PaymentStatus.DONE.value,
                refund_reason="refund",
            ),
            resource_version=1,
        )

        with mock_tenant_config_for_issue_refund_max_condition():
            response = client.post(
                url,
                data=json.dumps(new_refund),
                content_type='application/json',
                headers=headers,
            )
        assert response.status_code == 200
        response = json.loads(response.data)
        assert response.get('data').get('refund_reason') is not None

    payment_id = '3'
    patch_payment_url = (
        'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
    )

    with mock_tenant_config_for_payment_rules(), mock_tenant_config_for_issue_refund_max_condition():
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(
                        refund_reason="edit refund",
                    ),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            content_type='application/json',
            headers=headers,
        )

    assert response.status_code == 200


def test_cancel_posted_refund_should_not_return_refund(
    client, bill_repo, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    total_payments = len(bill_aggregate.payments)

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        cash_register_id, cashier_session_id = create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

        new_refund = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.HOTEL,
                paid_to=PaymentReceiverTypes.GUEST,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.REFUND.value,
                status=PaymentStatus.DONE.value,
                refund_reason="refund",
            ),
            resource_version=1,
        )

        with mock_tenant_config_for_issue_refund_max_condition():
            response = client.post(
                url,
                data=json.dumps(new_refund),
                content_type='application/json',
                headers=headers,
            )
        assert response.status_code == 200

        # Post Payment
        patch_payment_url = (
            'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + '3'
        )
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(status=PaymentStatus.POSTED.value),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )

        # Cancel Posted Refund.
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(status=PaymentStatus.CANCELLED.value),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )
        assert response.status_code == 400


def test_multiple_cancel_posted_payment_should_not_happen(
    client, bill_repo, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]
    total_payments = len(bill_aggregate.payments)

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        cash_register_id, cashier_session_id = create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

        # Post Payment
        patch_payment_url = (
            'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + '2'
        )
        client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(status=PaymentStatus.POSTED.value),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )

        # Cancel Posted Payment.
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(status=PaymentStatus.CANCELLED.value),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )
        assert response.status_code == 200

        # Multiple Cancel Posted Payment.
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(status=PaymentStatus.CANCELLED.value),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            headers=headers,
            content_type='application/json',
        )
        assert response.status_code == 400


def test_create_payment_should_create_online_payment_and_payment_modes_in_refund_rules_config_to_post_state(
    client, bill_repo, open_booking_and_bill
):
    bill_aggregate = open_booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='144',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.ONLINE,
            payment_mode=PaymentModes.RAZORPAY_API,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}

    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    payment = bill_aggregate.get_payment(int(2))
    assert payment.status == PaymentStatus.POSTED

    new_payment['data'].update(
        {
            'amount': '100',
            'payment_channel': PaymentChannels.FRONT_DESK,
            'payment_mode': PaymentModes.CASH,
        }
    )
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    payment = bill_aggregate.get_payment(int(3))
    assert payment.status != PaymentStatus.POSTED

    new_payment['data'].update(
        {'amount': '244', 'payment_mode': PaymentModes.RAZORPAY_API}
    )
    with mock_refund_rules(), mock_tenant_config(
        [
            {
                "config_name": "is_auto_refund_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    payment = bill_aggregate.get_payment(int(4))
    assert payment.status == PaymentStatus.POSTED


def test_edit_payment_should_change_online_payment_and_payment_modes_in_refund_rules_config_to_post_state(
    client, open_booking_and_bill, bill_repo
):
    bill_aggregate = open_booking_and_bill[1]
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='300',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.TREEBO_POINTS,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}

    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)

    payment_id = '2'
    patch_payment_url = (
        'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
    )

    with (
        mock_refund_rules(),
        mock_tenant_config(
            [
                {
                    "config_name": "is_auto_refund_enabled",
                    "config_value": "true",
                    "value_type": "boolean",
                }
            ]
        ),
    ):
        response = client.patch(
            patch_payment_url,
            data=json.dumps(
                dict(
                    data=dict(payment_mode=PaymentModes.RAZORPAY_API),
                    resource_version=bill_aggregate.bill.version,
                )
            ),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    payment = bill_aggregate.get_payment(int(2))
    assert payment.status == PaymentStatus.POSTED


def test_user_not_able_to_refund_payout_link_above_x_amount(
    client, bill_repo, open_booking_and_bill, cashier_session_repo
):
    bill_aggregate = open_booking_and_bill[1]

    payment_url = f'v1/bills/{bill_aggregate.bill.bill_id}/payments'

    def make_post_request(data):
        headers = {"X-User-Type": "cr-team"}
        return client.post(
            payment_url,
            data=json.dumps(data),
            content_type='application/json',
            headers=headers,
        )

    # Define new payment data
    new_payment_data = {
        "data": {
            "amount": "10050.00",
            "paid_by": PaymentReceiverTypes.GUEST,
            "paid_to": PaymentReceiverTypes.HOTEL,
            "payment_channel": PaymentChannels.FRONT_DESK,
            "payment_mode": PaymentModes.CASH,
            "payment_type": PaymentTypes.PAYMENT.value,
            "status": PaymentStatus.DONE.value,
        },
        "resource_version": 1,
    }

    # Make new payment
    make_post_request(new_payment_data)

    # Define refund data with amount above threshold
    refund_data_above_threshold = {
        "data": {
            "amount": "10150",
            "paid_by": PaymentReceiverTypes.HOTEL,
            "paid_to": PaymentReceiverTypes.GUEST,
            "payment_channel": PaymentChannels.FRONT_DESK,
            "payment_mode": PaymentModes.RAZORPAY_API,
            "payment_type": PaymentTypes.REFUND.value,
            "status": PaymentStatus.DONE.value,
            "refund_reason": "refund",
        },
        "resource_version": 1,
    }

    # Test refund with amount above threshold (should return 400)
    with mock_role_manager(), mock_refund_rules(), mock_refund_response_by_payout_link(), mock_auto_approve_payout_link_amount():
        response = make_post_request(refund_data_above_threshold)
        assert response.status_code == 400

    # Define refund data with amount below threshold and payout contact details
    refund_data_below_threshold_with_contact = {
        "data": {
            "amount": "9050",
            "paid_by": PaymentReceiverTypes.HOTEL,
            "paid_to": PaymentReceiverTypes.GUEST,
            "payment_channel": PaymentChannels.FRONT_DESK,
            "payment_mode": PaymentModes.RAZORPAY_API,
            "payment_type": PaymentTypes.REFUND.value,
            "status": PaymentStatus.DONE.value,
            "refund_reason": "refund",
            "payout_contact_details": {
                "email": "<EMAIL>",
                "phone": {
                    "country_code": "+91",
                    "number": "9713708166",
                },
            },
        },
        "resource_version": 1,  # Add resource version
    }

    # Test refund below threshold with contact details (should return 200)
    with mock_role_manager(), mock_refund_rules(), mock_refund_response_by_payout_link(), mock_auto_approve_payout_link_amount():
        response = make_post_request(refund_data_below_threshold_with_contact)
        assert response.status_code == 200


def test_if_payment_guarantee_is_added_on_adding_payment_to_reserved_booking(
    client, booking_and_bill, booking_repo
):
    bill_aggregate = booking_and_bill[1]
    booking_aggregate = booking_and_bill[0]
    booking_aggregate.booking.status = BookingStatus.RESERVED
    booking_repo.update(booking_aggregate)
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='8165.7700',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=booking_aggregate.booking.version,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )

    with mock_guarantee_enabled_config(True):
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers={'X-User-Type': 'super-admin'},
        )
        assert response.status_code == 200

        booking_aggregate = booking_repo.load(booking_aggregate.booking.booking_id)

        assert booking_aggregate.booking.status == BookingStatus.CONFIRMED
        assert booking_aggregate.guarantee_information == GuaranteeInformation(
            GuaranteeTypes.PAYMENT_GUARANTEE
        )


def test_if_payment_guarantee_gets_removed_if_payment_cancelled(
    client, booking_and_bill, booking_repo
):
    bill_aggregate = booking_and_bill[1]
    booking_aggregate = booking_and_bill[0]
    booking_aggregate.update_guarantee_information(
        GuaranteeInformation(GuaranteeTypes.PAYMENT_GUARANTEE)
    )
    booking_repo.update(booking_aggregate)
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )

    with mock_guarantee_enabled_config(True):
        cancel_payment_payload = {
            'data': {'comment': None, 'status': 'cancelled'},
            'resource_version': booking_aggregate.current_version(),
        }

        patch_payment_url = (
            'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + '1'
        )
        response = client.patch(
            patch_payment_url,
            json=cancel_payment_payload,
            headers={'X-User-Type': 'super-admin'},
        )
        assert response.status_code == 200
        booking_aggregate = booking_repo.load(booking_aggregate.booking_id)
        assert booking_aggregate.is_reserved()
        assert booking_aggregate.guarantee_information is None
