import json
from decimal import Decimal

from treebo_commons.money import Money
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import add, current_datetime

from object_registry import locate_instance
from prometheus.domain.billing.dto import ChargeData, ChargeSplitData
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.services import TaxService
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import make_booking
from prometheus.itests.billing.test_payment_api import create_cash_register_and_session
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.tests.mockers import mock_tax_calculator_service
from ths_common.constants.billing_constants import (
    CashRegisterNames,
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.value_objects import ChargeItem


def test_billed_entities_api_returns_folio(app, client, booking_repo, bill_repo):
    checkin_date = current_datetime()
    checkout_date = add(current_datetime(), days=1)
    payload = create_new_booking_payload(
        checkin_date, checkout_date, status=None, with_payments=False
    )
    booking_id = make_booking(client, {"data": payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert len(bill_aggregate.folios) > 0
    billed_entities_url = 'v1/bills/' + booking_aggregate.bill_id + '/billed-entities'
    response = client.get(
        billed_entities_url,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200
    assert response.json is not None
    assert (
        response.json.get('data')[0].get('accounts')[0].get('folio_number') is not None
    )


def test_folio_summary_api(app, client, booking_repo, bill_repo):
    checkin_date = current_datetime()
    checkout_date = add(current_datetime(), days=1)
    payload = create_new_booking_payload(
        checkin_date, checkout_date, status=None, with_payments=False
    )
    booking_id = make_booking(client, {"data": payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    tax_service = locate_instance(TaxService)
    charge_dto_1 = ChargeData(
        pretax_amount=Money('100 INR'),
        posttax_amount=None,
        charge_type=ChargeTypes.CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        applicable_date=dateutils.isoformat_str_to_datetime(
            '2018-01-11 12:00:00+05:30'
        ),
        item=ChargeItem('RoomStay', 'Stay'),
        status=ChargeStatus.CREATED,
        charge_splits=[
            ChargeSplitData(
                'Ram',
                Decimal('60'),
                BilledEntityAccountVO(1, 2),
                ChargeTypes.NON_CREDIT,
            ),
            ChargeSplitData(
                'Ram',
                Decimal('40'),
                BilledEntityAccountVO(1, 3),
                ChargeTypes.NON_CREDIT,
            ),
        ],
    )

    charge_dto_2 = ChargeData(
        posttax_amount=Money('100 INR'),
        pretax_amount=None,
        charge_type=ChargeTypes.CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        applicable_date=dateutils.isoformat_str_to_datetime(
            '2018-01-11 12:00:00+05:30'
        ),
        item=ChargeItem('RoomStay', 'Stay'),
        status=ChargeStatus.CREATED,
        charge_splits=[
            ChargeSplitData(
                'Ram',
                Decimal('60'),
                BilledEntityAccountVO(1, 2),
                ChargeTypes.NON_CREDIT,
            ),
            ChargeSplitData(
                'Ram',
                Decimal('40'),
                BilledEntityAccountVO(1, 1),
                ChargeTypes.NON_CREDIT,
            ),
        ],
    )

    with mock_tax_calculator_service():
        tax_updated_charge_dtos = tax_service.update_taxes(
            [charge_dto_1, charge_dto_2],
            buyer_gst_details=None,
            hotel_id=bill_aggregate.bill.vendor_id,
        )

    bill_aggregate.add_charges(tax_updated_charge_dtos)
    bill_repo.update(bill_aggregate)

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
            payment_splits=[
                dict(
                    billed_entity_account=dict(billed_entity_id=1, account_number=4),
                    amount='5',
                ),
                dict(
                    billed_entity_account=dict(billed_entity_id=1, account_number=5),
                    amount='5',
                ),
            ],
        ),
        resource_version=2,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200

    bill_aggregate = bill_repo.load(bill_aggregate.bill_id)

    assert len(bill_aggregate.folios) == 5

    for folio in bill_aggregate.folios:
        get_folios_url = (
            'v1/bills/'
            + booking_aggregate.bill_id
            + '/folio-summary/'
            + str(folio.folio_number)
        )
        response = client.get(
            get_folios_url,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == 200
        assert response.json is not None
        if folio.folio_number in [1, 2, 3]:
            assert len(response.json.get('data').get('charges')) > 0
            assert len(response.json.get('data').get('payments')) == 0
        if folio.folio_number in [4, 5]:
            assert len(response.json.get('data').get('charges')) == 0
            assert len(response.json.get('data').get('payments')) > 0


def test_folio_reset_sequence(app, client, booking_repo, bill_repo):
    checkin_date = current_datetime()
    checkout_date = add(current_datetime(), days=1)
    payload = create_new_booking_payload(
        checkin_date, checkout_date, status=None, with_payments=False
    )
    booking_id = make_booking(client, {"data": payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load_for_update(booking_aggregate.bill_id)
    assert len(bill_aggregate.folios) > 0
    billed_entities_url = 'v1/bills/' + booking_aggregate.bill_id + '/billed-entities'
    response = client.get(
        billed_entities_url,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200
    assert response.json is not None
    assert (
        response.json.get('data')[0].get('accounts')[0].get('folio_number') is not None
    )

    bill_aggregate.add_folio_if_not_exists(1, 2)
    bill_aggregate.add_folio_if_not_exists(1, 3)
    bill_aggregate.add_folio_if_not_exists(1, 4)
    bill_repo.update(bill_aggregate)
    assert len(bill_aggregate.folios) == 4
    bill_aggregate = bill_repo.load_for_update(booking_aggregate.bill_id)
    bill_aggregate.reset_folio_sequence()
    bill_aggregate.add_folio_if_not_exists(1, 1)
    bill_repo.update(bill_aggregate)
    bill_aggregate = bill_repo.load(bill_aggregate.bill_id)
    assert len(bill_aggregate.folios) == 1
    assert bill_aggregate.folios[0].folio_number == 1
