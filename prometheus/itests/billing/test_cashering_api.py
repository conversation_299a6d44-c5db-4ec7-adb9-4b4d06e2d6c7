import json

import pytest

from prometheus.domain.billing.repositories.cash_register_repository import (
    CashRegisterRepository,
)
from prometheus.domain.billing.repositories.cashier_session_repository import (
    CashierSessionRepository,
)


@pytest.fixture
def cashier_register_repo():
    return CashRegisterRepository()


@pytest.fixture
def cashier_session_repo():
    return CashierSessionRepository()


def create_register_payload(vendor_id, amount=0):
    return {
        "data": {
            "default_opening_balance": "INR " + str(amount),
            "vendor_id": str(vendor_id),
            "cash_register_name": "Testing_Hotel_Register_1",
            "carry_balance_to_next_shift": False,
        }
    }


def create_cash_session_payload(vendor_id, amount=0):
    return {
        "data": {
            "opening_balance": "INR " + str(amount),
            "vendor_id": str(vendor_id),
            "opening_balance_in_base_currency": "INR " + str(amount),
        }
    }


def close_cashier_session_payload(vendor_id):
    return {"data": {"vendor_id": str(vendor_id), "status": "closed"}}


def make_cash_register(client, vendor_id, payload):
    url = f"/v1/cashier/cash-registers"
    response = client.post(
        url,
        data=payload,
        content_type="application/json",
        headers={"X-User-Type": "super-admin", "X-Hotel-Id": str(vendor_id)},
    )
    return response


def make_cashier_session(client, cash_register_id, vendor_id, payload):
    url = f"v1/cashier/cash-registers/{cash_register_id}/cashier-sessions"
    response = client.post(
        url,
        data=payload,
        content_type="application/json",
        headers={"X-User-Type": "super-admin", "X-Hotel-Id": str(vendor_id)},
    )
    return response


def make_cashier_session_close(
    client, cash_register_id, cashier_session_id, vendor_id, payload
):
    url = f"v1/cashier/cash-registers/{cash_register_id}/cashier-sessions/{cashier_session_id}"
    response = client.patch(
        url,
        data=payload,
        content_type="application/json",
        headers={"X-User-Type": "super-admin", "X-Hotel-Id": str(vendor_id)},
    )
    return response


def get_cashier_session_for_cash_register_id(client, cash_register_id, vendor_id):
    url = (
        f"/v1/cashier/cash-registers/{cash_register_id}/cashier-sessions?vendor_id="
        + vendor_id
    )
    response = client.get(
        url,
        content_type="application/json",
        headers={"X-User-Type": "super-admin", "X-Hotel-Id": str(vendor_id)},
    )
    return response


def test_create_cash_register_with_zero_opening_balance(client, cashier_register_repo):
    cash_register_payload = json.dumps(create_register_payload(vendor_id="0016932"))
    response = make_cash_register(client, '0016932', cash_register_payload)
    assert response.status_code == 200
    cash_register_aggregate = cashier_register_repo.get_cash_register_for_vendor(
        vendor_id="0016932"
    )
    assert (cash_register_aggregate.cash_register.default_opening_balance, 0)


def test_create_cashering_session(client, cashier_session_repo):
    cash_register_payload = json.dumps(create_register_payload(vendor_id="0016932"))
    response = make_cash_register(client, '0016932', cash_register_payload)
    cash_register_id = response.json['data']['cash_register_id']

    cashier_session_payload = json.dumps(
        create_cash_session_payload(vendor_id="0016932")
    )
    response = make_cashier_session(
        client, cash_register_id, '0016932', cashier_session_payload
    )
    assert response.status_code == 200

    cashier_session_aggregate = cashier_session_repo.get_latest_cashier_session(
        cash_register_id
    )
    assert cashier_session_aggregate.cashier_session.session_number, 1
    assert cashier_session_aggregate.cashier_session.status, "open"
    assert cashier_session_aggregate.cashier_session.cashier_session_id, response.json[
        'data'
    ]['cashier_session_id']


def test_create_multiple_cashering_session(client):
    cash_register_payload = json.dumps(create_register_payload(vendor_id="0016932"))
    response = make_cash_register(client, '0016932', cash_register_payload)
    cash_register_id = response.json['data']['cash_register_id']

    cashier_session_payload = json.dumps(
        create_cash_session_payload(vendor_id="0016932")
    )
    response = make_cashier_session(
        client, cash_register_id, '0016932', cashier_session_payload
    )
    assert response.status_code == 200

    cashier_session_payload = json.dumps(
        create_cash_session_payload(vendor_id="0016932")
    )
    response = make_cashier_session(
        client, cash_register_id, '0016932', cashier_session_payload
    )
    assert response.status_code == 400


def test_close_cashering_session(client, cashier_session_repo):
    cash_register_payload = json.dumps(create_register_payload(vendor_id="0016932"))
    response = make_cash_register(client, '0016932', cash_register_payload)
    cash_register_id = response.json['data']['cash_register_id']

    cashier_session_payload = json.dumps(
        create_cash_session_payload(vendor_id="0016932")
    )
    response = make_cashier_session(
        client, cash_register_id, '0016932', cashier_session_payload
    )
    cashier_session_id = response.json['data']['cashier_session_id']

    cashier_session_close_payload = json.dumps(close_cashier_session_payload("0016932"))
    response = make_cashier_session_close(
        client,
        cash_register_id,
        cashier_session_id,
        "0016932",
        cashier_session_close_payload,
    )
    assert response.status_code == 200

    cashier_session_aggregate = cashier_session_repo.get_latest_cashier_session(
        cash_register_id
    )
    assert cashier_session_aggregate.cashier_session.status, "closed"
    assert cashier_session_aggregate.cashier_session.cashier_session_id, response.json[
        'data'
    ]['cashier_session_id']


def test_create_cashering_session_after_closing_first_session(
    client, cashier_session_repo
):
    cash_register_payload = json.dumps(create_register_payload(vendor_id="0016932"))
    response = make_cash_register(client, '0016932', cash_register_payload)
    cash_register_id = response.json['data']['cash_register_id']

    cashier_session_payload = json.dumps(
        create_cash_session_payload(vendor_id="0016932")
    )
    response = make_cashier_session(
        client, cash_register_id, '0016932', cashier_session_payload
    )
    cashier_session_id = response.json['data']['cashier_session_id']

    cashier_session_close_payload = json.dumps(close_cashier_session_payload("0016932"))
    make_cashier_session_close(
        client,
        cash_register_id,
        cashier_session_id,
        "0016932",
        cashier_session_close_payload,
    )

    cashier_session_payload = json.dumps(
        create_cash_session_payload(vendor_id="0016932", amount=10)
    )
    response = make_cashier_session(
        client, cash_register_id, '0016932', cashier_session_payload
    )

    cashier_session_aggregate = cashier_session_repo.get_latest_cashier_session(
        cash_register_id
    )
    assert cashier_session_aggregate.cashier_session.session_number, 2
    assert cashier_session_aggregate.cashier_session.status, "open"
    assert cashier_session_aggregate.cashier_session.cashier_session_id, response.json[
        'data'
    ]['cashier_session_id']


def test_get_multiple_cashering_session_for_cash_register(client, cashier_session_repo):
    cash_register_payload = json.dumps(create_register_payload(vendor_id="0016932"))
    response = make_cash_register(client, '0016932', cash_register_payload)
    cash_register_id = response.json['data']['cash_register_id']

    cashier_session_payload = json.dumps(
        create_cash_session_payload(vendor_id="0016932")
    )
    response = make_cashier_session(
        client, cash_register_id, '0016932', cashier_session_payload
    )
    cashier_session_id = response.json['data']['cashier_session_id']

    cashier_session_close_payload = json.dumps(close_cashier_session_payload("0016932"))
    make_cashier_session_close(
        client,
        cash_register_id,
        cashier_session_id,
        "0016932",
        cashier_session_close_payload,
    )

    cashier_session_payload = json.dumps(
        create_cash_session_payload(vendor_id="0016932", amount=10)
    )
    make_cashier_session(client, cash_register_id, '0016932', cashier_session_payload)

    response = get_cashier_session_for_cash_register_id(
        client, cash_register_id, '0016932'
    )
    assert response.json['data']['cashier_sessions'][0]['cashier_session'][
        'status'
    ], "open"
    assert response.json['data']['cashier_sessions'][1]['cashier_session'][
        'status'
    ], "closed"
