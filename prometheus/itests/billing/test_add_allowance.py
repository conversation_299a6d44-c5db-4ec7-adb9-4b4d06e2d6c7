import datetime
import json

import pytest
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    make_booking,
)
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.tests.test_utils import today_minus_days


@pytest.fixture
def checked_in_multiple_day_booking_and_bill_pending_checkout(
    client, active_hotel_aggregate, booking_repo, bill_repo
):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() - datetime.timedelta(days=1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=1),
        hotel_context.checkout_time,
    )

    booking_payload = create_new_booking_payload(checkin_date, checkout_date)
    booking_id = make_booking(client, {"data": booking_payload})

    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version, checkin_datetime=today_minus_days(1)
    )
    checkin_booking(client, booking_id, checkin_payload)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    return booking_aggregate, bill_aggregate


def test_add_allowance(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    bill_repo,
    hotel_repo,
    invoice_repo,
):
    booking_aggregate, bill_aggregate = (
        checked_in_booking_and_bill_pending_checkout_2[0],
        checked_in_booking_and_bill_pending_checkout_2[1],
    )

    bill_aggregate.consume_charges(
        charges=bill_aggregate.charges,
        business_date=active_hotel_aggregate.hotel.current_business_date,
    )
    bill_repo.update(bill_aggregate)
    bill_id, charge_id = bill_aggregate.bill_id, bill_aggregate.charges[0].charge_id
    charge_split_id = bill_aggregate.charges[0].charge_splits[0].charge_split_id
    charge = bill_aggregate.get_charge(charge_id)
    charge_split = charge.get_split(charge_split_id)

    add_allowance_url = (
        'v1/bills/'
        + bill_id
        + '/charges/'
        + str(charge_id)
        + '/charge-splits/'
        + str(charge_split_id)
        + '/allowances'
    )
    new_allowance = dict(
        data=dict(pretax_amount='50 INR', remarks='add new allowance'),
        resource_version=bill_aggregate.current_version(),
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        add_allowance_url,
        data=json.dumps(new_allowance),
        content_type='application/json',
        headers=headers,
    )

    assert response.status_code == 200
    assert response.json['data']['status'] == 'created'
    assert (
        charge_split.billed_entity_account.billed_entity_id
        == response.json['data']['billed_entity_account']['billed_entity_id']
    )
    assert (
        charge_split.billed_entity_account.account_number
        == response.json['data']['billed_entity_account']['account_number']
    )


def test_bulk_add_allowances(
    client,
    active_hotel_aggregate,
    hotel_repo,
    invoice_repo,
    bill_repo,
    checked_in_multiple_day_booking_and_bill_pending_checkout,
):
    booking_aggregate, bill_aggregate = (
        checked_in_multiple_day_booking_and_bill_pending_checkout[0],
        checked_in_multiple_day_booking_and_bill_pending_checkout[1],
    )

    bill_aggregate.consume_charges(
        charges=bill_aggregate.charges,
        business_date=active_hotel_aggregate.hotel.current_business_date,
    )
    bill_repo.update(bill_aggregate)
    bill_id = bill_aggregate.bill_id
    allowance_data = list()
    for charge in bill_aggregate.charges:
        charge_id = charge.charge_id
        charge_split_id = bill_aggregate.charges[0].charge_splits[0].charge_split_id
        allowance_data.append(
            dict(
                pretax_amount='50 INR',
                remarks='add new allowance',
                charge_id=charge_id,
                charge_split_id=charge_split_id,
            )
        )

    add_allowance_url = 'v1/bills/' + bill_id + '/allowances'
    new_allowances = dict(
        data=allowance_data, resource_version=bill_aggregate.current_version()
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        add_allowance_url,
        data=json.dumps(new_allowances),
        content_type='application/json',
        headers=headers,
    )

    assert response.status_code == 200
    for response in response.json['data']:
        assert response['status'] == 'created'
