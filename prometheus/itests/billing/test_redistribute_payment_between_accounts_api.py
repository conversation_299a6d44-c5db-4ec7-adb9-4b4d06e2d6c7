import json

from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    make_booking,
)
from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2
from prometheus.itests.billing.test_payment_api import create_cash_register_and_session
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v2_request,
)
from prometheus.tests.mockers import mock_role_manager, mock_tenant_config
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    CashRegisterNames,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)


def redistribute_payments(
    client, headers, bill_id, billed_entity_ids, invoice_group_id=None
):
    with mock_role_manager():
        url = 'v1/bills/' + bill_id + '/redistribute-payments-between-accounts'
        request = '{"data": { "billed_entity_ids": ' + str(billed_entity_ids)
        if invoice_group_id:
            request = request + ',"booking_invoice_group_id: "' + invoice_group_id + '"'
        request = request + '},"resource_version": 1}'
        response = client.post(
            url,
            json=json.loads(request),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 201
        json_data = json.loads(response.data.decode('utf-8'))
        return json_data


def create_payment_payload(
    amount_per_billed_entity_account,
):
    new_payment = dict(
        data=dict(
            payment_splits=[
                dict(
                    billed_entity_account=dict(
                        billed_entity_id=split[0], account_number=split[1]
                    ),
                    amount=str(split[2]),
                )
                for split in amount_per_billed_entity_account
            ],
            amount=str(sum([split[2] for split in amount_per_billed_entity_account])),
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    return new_payment


def test_redistribute_payment_api_return_all_accounts_with_deficit_balance(
    booking_repo,
    create_booking_payload,
    client,
    active_hotel_aggregate,
    hotel_repo,
    bill_repo,
):
    headers = {'X-User-Type': 'super-admin'}
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    add_expenses_v2(client, booking_id, create_expenses_v2_request())
    add_expenses_v2(
        client,
        booking_id,
        create_expenses_v2_request(billed_entity_accounts=[(1, 2), (2, 2)]),
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    net_balance_B1A1 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=1, billed_entity_id=1)
    )
    net_balance_B1A2 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=2, billed_entity_id=1)
    )
    net_balance_B2A1 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=1, billed_entity_id=2)
    )
    net_balance_B2A2 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=2, billed_entity_id=2)
    )
    response = redistribute_payments(client, headers, bill_aggregate.bill_id, [1, 2])
    assert len(response.get('data')) == 2
    for data in response.get('data'):
        if data.get('billed_entity_id') == 1:
            assert len(data.get('entity_account_summary')) == 2
            assert data.get('entity_account_summary')[1].get('account_summary').get(
                'balance'
            ) == str((net_balance_B1A1 + net_balance_B1A2).amount)
        if data.get('billed_entity_id') == 2:
            assert len(data.get('entity_account_summary')) == 2
            for summary in data.get('entity_account_summary'):
                if summary.get('billed_entity_account').get('account_number') == 1:
                    assert summary.get('account_summary').get('balance') == str(
                        net_balance_B2A1.amount
                    )
                else:
                    assert summary.get('account_summary').get('balance') == str(
                        net_balance_B2A2.amount
                    )


def test_redistribute_payment_api_settle_deficit_with_surplus_and_return_surplus_account(
    booking_repo,
    create_booking_payload,
    client,
    active_hotel_aggregate,
    hotel_repo,
    bill_repo,
):
    headers = {'X-User-Type': 'super-admin'}
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    add_expenses_v2(client, booking_id, create_expenses_v2_request())
    add_expenses_v2(
        client,
        booking_id,
        create_expenses_v2_request(billed_entity_accounts=[(1, 2), (2, 2)]),
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    net_balance_B1A1 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=1, billed_entity_id=1)
    )
    net_balance_B1A2 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=2, billed_entity_id=1)
    )
    net_balance_B2A1 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=1, billed_entity_id=2)
    )
    net_balance_B2A2 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=2, billed_entity_id=2)
    )
    payment_B2A1 = 3000
    payment_B2A2 = 2000
    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                payment_splits=[
                    dict(
                        billed_entity_account=dict(
                            billed_entity_id=2, account_number=1
                        ),
                        amount=str(payment_B2A1),
                    ),
                    dict(
                        billed_entity_account=dict(
                            billed_entity_id=2, account_number=2
                        ),
                        amount=str(payment_B2A2),
                    ),
                ],
                amount=str(payment_B2A1 + payment_B2A2),
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

        payment_id = response.json.get('data').get('payment_id')

    response = redistribute_payments(client, headers, bill_aggregate.bill_id, [1, 2])
    assert len(response.get('data')) == 2
    assert (
        response['data'][0]['entity_account_summary'][1]['account_summary']['balance']
        == '-4596.02'
    )


def test_redistribute_payment_api_creates_audit_log(
    booking_repo,
    create_booking_payload,
    client,
    active_hotel_aggregate,
    hotel_repo,
    bill_repo,
    booking_audit_trail_repo,
):
    headers = {'X-User-Type': 'super-admin'}
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    add_expenses_v2(
        client,
        booking_id,
        create_expenses_v2_request(
            billed_entity_accounts=[(1, 1)], price=1918.2, quantity=1
        ),
    )
    add_expenses_v2(
        client,
        booking_id,
        create_expenses_v2_request(
            billed_entity_accounts=[(1, 2)], price=120, quantity=1
        ),
    )
    add_expenses_v2(
        client,
        booking_id,
        create_expenses_v2_request(
            billed_entity_accounts=[(1, 3)], price=5500, quantity=1
        ),
    )
    payment_B1A4 = create_payment_payload([(1, 4, 400)])
    payment_B1A5A6 = create_payment_payload([(1, 5, 200), (1, 6, 300)])
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        client.post(
            url,
            data=json.dumps(payment_B1A4),
            content_type='application/json',
            headers=headers,
        )
        client.post(
            url,
            data=json.dumps(payment_B1A5A6),
            content_type='application/json',
            headers=headers,
        )

    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    net_balance_B1A1 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=1, billed_entity_id=1)
    )
    net_balance_B1A2 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=2, billed_entity_id=1)
    )
    net_balance_B1A3 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=3, billed_entity_id=1)
    )
    net_balance_B1A4 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=4, billed_entity_id=1)
    )
    net_balance_B1A5 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=5, billed_entity_id=1)
    )
    net_balance_B1A6 = bill_aggregate.get_net_balance(
        BilledEntityAccountVO(account_number=6, billed_entity_id=1)
    )

    response = redistribute_payments(client, headers, bill_aggregate.bill_id, [1])

    assert len(response.get('data')[0].get('entity_account_summary')) == 6
    audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
    assert (
        len(
            [
                audit.audit_trail
                for audit in audit_aggregate
                if audit.audit_trail.audit_type == AuditType.PAYMENT_REDISTRIBUTED
            ]
        )
        > 0
    )
