import datetime
import json
from decimal import Decimal

from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import date_range

from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import checkin_booking
from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2
from prometheus.itests.booking.test_booking_v2 import make_booking
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload_with_edit_guest_details,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v2_request,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_role_manager,
    mock_tenant_config,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeSubTypes,
    ChargeTypes,
    SpotCreditSettlementType,
)
from ths_common.constants.booking_constants import BookingChannels, BookingStatus


def get_spot_credit_payload(billed_entity_id, settlement_type):
    return dict(
        data=dict(
            settlement_data=[
                dict(billed_entity_id=billed_entity_id, settlement_type=settlement_type)
            ]
        )
    )


def issue_spot_credit(
    client, bill_id, payload, expected_status_code=200, hotel_in_posttax=False
):
    with mock_catalog_client(
        hotel_in_posttax=hotel_in_posttax
    ), mock_role_manager(), mock_tenant_config([]):
        url = "v1/bills/{0}/settle-by-spot-credit".format(bill_id)
        response = client.post(
            url,
            data=json.dumps(payload),
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == expected_status_code
        return response.json


def test_issue_booking_level_spot_credit_should_create_spot_credits(
    app, client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
    )
    create_booking_payload['payments'] = []
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    payload = get_spot_credit_payload(
        billed_entity_id=2, settlement_type=SpotCreditSettlementType.BOOKING_LEVEL.value
    )
    issue_spot_credit(client, booking_aggregate.bill_id, payload)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    for charge in bill_aggregate.charges:
        for split in charge.charge_splits:
            assert split.charge_type == ChargeTypes.CREDIT
            assert split.charge_sub_type == ChargeSubTypes.SPOT_CREDIT
            assert split.billed_entity_account.billed_entity_id == 2
    assert bill_aggregate.summary.balance.amount == Decimal('0.00')


def test_issue_booking_level_spot_credit_having_partial_payment_should_split_charge_and_create_spot_credits(
    app, client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
    )
    create_booking_payload['payments'] = [
        {
            "amount": "20.00",
            "comment": "string",
            "date_of_payment": checkin_date.isoformat(),
            "paid_by": "guest",
            "paid_to": "guest",
            "payment_channel": "online",
            "payment_details": {},
            "payment_mode": "cash",
            "payment_mode_sub_type": "Amex",
            "payment_ref_id": "string",
            "payment_type": "payment",
            "status": "done",
        }
    ]
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    expense_v2_request = create_expenses_v2_request(
        dates=[checkin_date], price=50, billed_entity_accounts=[(3, 1)]
    )
    add_expenses_v2(client, booking_id, expense_v2_request)
    payload = get_spot_credit_payload(
        billed_entity_id=2, settlement_type=SpotCreditSettlementType.BOOKING_LEVEL.value
    )
    issue_spot_credit(client, booking_aggregate.bill_id, payload)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    _sum = Decimal('0')
    for charge in bill_aggregate.charges:
        for split in charge.charge_splits:
            if split.billed_entity_account.billed_entity_id == 2:
                assert split.charge_type == ChargeTypes.CREDIT
                assert split.charge_sub_type == ChargeSubTypes.SPOT_CREDIT
            if split.billed_entity_account.billed_entity_id == 3:
                _sum += split.post_tax.amount
    assert _sum == Decimal('20')
    assert bill_aggregate.summary.balance.amount == Decimal('0.00')


def test_issue_billed_entity_level_should_only_settle_given_be_accounts(
    app, client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
    )
    create_booking_payload['payments'] = []
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    summary = bill_aggregate.get_account_summary(BilledEntityAccountVO(3, 1))
    assert summary.balance.amount == Decimal('224.00')
    payload = get_spot_credit_payload(
        billed_entity_id=3,
        settlement_type=SpotCreditSettlementType.BILLED_ENTITY_LEVEL.value,
    )
    issue_spot_credit(client, booking_aggregate.bill_id, payload)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    summary_account_1 = bill_aggregate.get_account_summary(BilledEntityAccountVO(3, 1))
    assert summary_account_1.balance.amount == Decimal('0.00')
    summary_account_2 = bill_aggregate.get_account_summary(
        BilledEntityAccountVO(3, 2)
    )  # credit account
    assert summary_account_2.balance.amount == Decimal('0.00')
    assert (
        summary_account_2.debit_summary.total_debit_payable_after_checkout.amount
        == Decimal('224.00')
    )
    assert summary_account_2.debit_summary.total_spot_credit.amount == Decimal('224.00')


def test_issue_booking_level_should_settle_all_accounts(
    app, client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
    )
    create_booking_payload['payments'] = []
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    summary = bill_aggregate.get_account_summary(BilledEntityAccountVO(3, 1))
    assert summary.balance.amount == Decimal('224.00')
    summary = bill_aggregate.get_account_summary(BilledEntityAccountVO(4, 1))
    assert summary.balance.amount == Decimal('224.00')
    payload = get_spot_credit_payload(
        billed_entity_id=3, settlement_type=SpotCreditSettlementType.BOOKING_LEVEL.value
    )
    issue_spot_credit(client, booking_aggregate.bill_id, payload)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    summary_account_1 = bill_aggregate.get_account_summary(BilledEntityAccountVO(3, 1))
    assert summary_account_1.balance.amount == Decimal('0.00')
    summary_account_2 = bill_aggregate.get_account_summary(
        BilledEntityAccountVO(3, 2)
    )  # credit account
    assert summary_account_2.balance.amount == Decimal('0.00')
    assert (
        summary_account_2.debit_summary.total_debit_payable_after_checkout.amount
        == Decimal('448.00')
    )
    assert summary_account_2.debit_summary.total_spot_credit.amount == Decimal('448.00')

    summary_account_1 = bill_aggregate.get_account_summary(BilledEntityAccountVO(4, 1))
    assert summary_account_1.balance.amount == Decimal('0.00')


def test_issue_booking_level_should_transfer_b2b_non_credit_charges(
    app, client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
    )
    create_booking_payload['payments'] = []
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    summary = bill_aggregate.get_account_summary(BilledEntityAccountVO(2, 1))
    assert summary.balance.amount == Decimal('448.00')
    payload = get_spot_credit_payload(
        billed_entity_id=3, settlement_type=SpotCreditSettlementType.BOOKING_LEVEL.value
    )
    issue_spot_credit(client, booking_aggregate.bill_id, payload)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    summary_account_1 = bill_aggregate.get_account_summary(BilledEntityAccountVO(2, 1))
    assert summary_account_1.balance.amount == Decimal('0.00')
    summary_account_2 = bill_aggregate.get_account_summary(
        BilledEntityAccountVO(3, 2)
    )  # credit account
    assert summary_account_2.balance.amount == Decimal('0.00')
    assert (
        summary_account_2.debit_summary.total_debit_payable_after_checkout.amount
        == Decimal('448.00')
    )
    assert summary_account_2.debit_summary.total_spot_credit.amount == Decimal('448.00')


def test_issue_booking_level_on_booking_with_zero_balance_should_raise_error(
    app, client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.BOOKER_COMPANY.value,
    )
    create_booking_payload['payments'] = [
        {
            "amount": "224.00",
            "comment": "string",
            "date_of_payment": checkin_date.isoformat(),
            "paid_by": "guest",
            "paid_to": "guest",
            "payment_channel": "online",
            "payment_details": {},
            "payment_mode": "cash",
            "payment_mode_sub_type": "Amex",
            "payment_ref_id": "string",
            "payment_type": "payment",
            "status": "done",
        }
    ]
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    summary = bill_aggregate.get_account_summary(BilledEntityAccountVO(1, 1))
    assert summary.balance.amount == Decimal('0.00')
    payload = get_spot_credit_payload(
        billed_entity_id=3, settlement_type=SpotCreditSettlementType.BOOKING_LEVEL.value
    )
    issue_spot_credit(
        client, booking_aggregate.bill_id, payload, expected_status_code=400
    )


def test_issue_multiple_be_level_spot_credit_should_issue_spot_credit(
    app, client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
    )
    create_booking_payload['payments'] = []
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert bill_aggregate.get_account_summary(
        BilledEntityAccountVO(3, 1)
    ).balance.amount == Decimal('224.00')
    assert bill_aggregate.get_account_summary(
        BilledEntityAccountVO(4, 1)
    ).balance.amount == Decimal('224.00')
    payload = dict(
        data=dict(
            settlement_data=[
                dict(
                    billed_entity_id=3,
                    settlement_type=SpotCreditSettlementType.BILLED_ENTITY_LEVEL.value,
                ),
                dict(
                    billed_entity_id=4,
                    settlement_type=SpotCreditSettlementType.BILLED_ENTITY_LEVEL.value,
                ),
            ]
        )
    )
    issue_spot_credit(client, booking_aggregate.bill_id, payload)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    summary_account = bill_aggregate.get_account_summary(BilledEntityAccountVO(3, 1))
    assert summary_account.balance.amount == Decimal('0.00')
    summary_account_1 = bill_aggregate.get_account_summary(BilledEntityAccountVO(3, 2))
    assert summary_account_1.balance.amount == Decimal('0.00')
    assert (
        summary_account_1.debit_summary.total_debit_payable_after_checkout.amount
        == Decimal('224.00')
    )
    assert summary_account_1.debit_summary.total_spot_credit.amount == Decimal('224.00')
    summary_account_2 = bill_aggregate.get_account_summary(
        BilledEntityAccountVO(4, 2)
    )  # credit account
    assert summary_account_2.balance.amount == Decimal('0.00')
    assert (
        summary_account_2.debit_summary.total_debit_payable_after_checkout.amount
        == Decimal('224.00')
    )
    assert summary_account_2.debit_summary.total_spot_credit.amount == Decimal('224.00')


def test_issue_spot_credit_having_inclusion_should_issue_spot_credit(
    app, client, booking_repo, bill_repo, active_hotel_aggregate
):
    payments = ["40.00", "20.0", "30.0", "123.2", "153.2", "140"]
    for index, payment in enumerate(payments):
        checkin_date = dateutils.subtract(dateutils.current_datetime(), days=1)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=0)
        create_booking_payload = create_new_booking_payload(
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=1,
            status=BookingStatus.CONFIRMED,
            reference_number='REF-1',
            channel=BookingChannels.DIRECT.value,
            default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        )
        create_booking_payload['reference_number'] = create_booking_payload[
            'reference_number'
        ] + str(index)
        create_booking_payload['payments'] = [
            {
                "amount": payment,
                "comment": "string",
                "date_of_payment": checkin_date.isoformat(),
                "paid_by": "guest",
                "paid_to": "guest",
                "payment_channel": "online",
                "payment_details": {},
                "payment_mode": "cash",
                "payment_mode_sub_type": "Amex",
                "payment_ref_id": "string",
                "payment_type": "payment",
                "status": "done",
            }
        ]
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "10",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]
        booking_id = make_booking(
            client, {"data": create_booking_payload}, hotel_in_posttax=True
        )
        booking_aggregate = booking_repo.load(booking_id)
        expense_v2_request = create_expenses_v2_request(
            dates=[checkin_date],
            price=20,
            billed_entity_accounts=[(3, 1)],
            charge_to=["2"],
        )
        add_expenses_v2(client, booking_id, expense_v2_request, hotel_in_posttax=True)

        payload = get_spot_credit_payload(
            billed_entity_id=3,
            settlement_type=SpotCreditSettlementType.BOOKING_LEVEL.value,
        )
        issue_spot_credit(
            client, booking_aggregate.bill_id, payload, hotel_in_posttax=True
        )
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        summary_account = bill_aggregate.get_account_summary(
            BilledEntityAccountVO(3, 1)
        )
        assert summary_account.balance.amount == Decimal('0.00')
        summary_account = bill_aggregate.get_account_summary(
            BilledEntityAccountVO(3, 2)
        )
        assert summary_account.balance.amount == Decimal('0.00')


def test_issue_spot_credit_having_inclusion_should_fail_if_payment_cant_be_distributed(
    app, client, booking_repo, bill_repo, active_hotel_aggregate
):
    payments = ["45.00", "200", "163.2"]
    for index, payment in enumerate(payments):
        checkin_date = dateutils.subtract(dateutils.current_datetime(), days=1)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=0)
        create_booking_payload = create_new_booking_payload(
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=1,
            status=BookingStatus.CONFIRMED,
            reference_number='REF-1',
            channel=BookingChannels.DIRECT.value,
            default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        )
        create_booking_payload['reference_number'] = create_booking_payload[
            'reference_number'
        ] + str(index)
        create_booking_payload['payments'] = [
            {
                "amount": payment,
                "comment": "string",
                "date_of_payment": checkin_date.isoformat(),
                "paid_by": "guest",
                "paid_to": "guest",
                "payment_channel": "online",
                "payment_details": {},
                "payment_mode": "cash",
                "payment_mode_sub_type": "Amex",
                "payment_ref_id": "string",
                "payment_type": "payment",
                "status": "done",
            }
        ]
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "10",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]
        booking_id = make_booking(client, {"data": create_booking_payload})
        booking_aggregate = booking_repo.load(booking_id)
        expense_v2_request = create_expenses_v2_request(
            dates=[checkin_date],
            price=20,
            billed_entity_accounts=[(3, 1)],
            charge_to=["2"],
        )
        add_expenses_v2(client, booking_id, expense_v2_request)

        payload = get_spot_credit_payload(
            billed_entity_id=3,
            settlement_type=SpotCreditSettlementType.BOOKING_LEVEL.value,
        )
        issue_spot_credit(
            client, booking_aggregate.bill_id, payload, expected_status_code=400
        )


def test_issue_spot_credit_should_issue_spot_credit_and_merge_splits(
    app, client, booking_repo, bill_repo, active_hotel_aggregate
):
    checkin_date = dateutils.subtract(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=0)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
    )
    create_booking_payload['payments'] = [
        {
            "amount": "123.20",
            "comment": "string",
            "date_of_payment": checkin_date.isoformat(),
            "paid_by": "guest",
            "paid_to": "guest",
            "payment_channel": "online",
            "payment_details": {},
            "payment_mode": "cash",
            "payment_mode_sub_type": "Amex",
            "payment_ref_id": "string",
            "payment_type": "payment",
            "status": "done",
        }
    ]
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "10",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        }
    ]
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    expense_v2_request = create_expenses_v2_request(
        dates=[checkin_date],
        price=20,
        billed_entity_accounts=[(3, 1), (4, 1)],
        charge_to=["2"],
    )
    add_expenses_v2(client, booking_id, expense_v2_request)

    payload = get_spot_credit_payload(
        billed_entity_id=3, settlement_type=SpotCreditSettlementType.BOOKING_LEVEL.value
    )
    issue_spot_credit(client, booking_aggregate.bill_id, payload)
    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload_with_edit_guest_details(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        guest_first_name="TEst",
        guest_last_name="Test las tname",
    )
    checkin_booking(client, booking_id, checkin_payload, expected_status_code=200)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    summary_account = bill_aggregate.get_account_summary(BilledEntityAccountVO(3, 1))
    assert summary_account.balance.amount == Decimal('0.00')
    summary_account = bill_aggregate.get_account_summary(BilledEntityAccountVO(3, 2))
    assert summary_account.balance.amount == Decimal('0.00')


def add_guest_stay_request(checkin_date, checkout_date):
    price_applicable_dates = list(date_range(checkin_date, checkout_date))

    payload = {
        "data": {
            'guest_stays': [
                {
                    'age_group': 'adult',
                    'checkin_date': checkin_date.isoformat(),
                    'checkout_date': checkout_date.isoformat(),
                }
            ],
            "new_room_stay_prices": [
                {
                    "applicable_date": applicable_date.isoformat(),
                    "bill_to_type": "guest",
                    "type": "non-credit",
                    "pretax_amount": 2623,
                }
                for applicable_date in price_applicable_dates
            ],
            'rate_plan_inclusions': [
                {
                    'start_date': checkin_date.isoformat(),
                    'end_date': dateutils.subtract(checkout_date, days=1).isoformat(),
                    'pretax_amount': '200.00 INR',
                    'quantity': 1,
                    'sku_id': '377',
                }
            ],
        },
        "resource_version": 1,
    }
    return json.dumps(payload)


def test_issue_spot_credit_having_inclusion_should_issue_spot_credit_v2(
    app, client, booking_repo, bill_repo, active_hotel_aggregate
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
    )
    create_booking_payload['payments'] = []
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    payload = get_spot_credit_payload(
        billed_entity_id=3, settlement_type=SpotCreditSettlementType.BOOKING_LEVEL.value
    )
    issue_spot_credit(client, booking_aggregate.bill_id, payload)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    req = add_guest_stay_request(checkin_date, checkout_date)
    url = f"/v1/bookings/{booking_id}/room-stays/1/guest-stays-list"
    with mock_role_manager():
        response = client.post(
            url,
            data=req,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
