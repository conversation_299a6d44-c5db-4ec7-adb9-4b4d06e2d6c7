import json

import pytest

from prometheus import crs_context
from ths_common.constants.billing_constants import ChargeStatus


@pytest.fixture
def add_allowance(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    bill_repo,
    hotel_repo,
    invoice_repo,
):
    booking_aggregate, bill_aggregate = (
        checked_in_booking_and_bill_pending_checkout_2[0],
        checked_in_booking_and_bill_pending_checkout_2[1],
    )

    bill_aggregate.consume_charges(
        charges=bill_aggregate.charges,
        business_date=active_hotel_aggregate.hotel.current_business_date,
    )
    bill_repo.update(bill_aggregate)
    bill_id, charge_id = bill_aggregate.bill_id, bill_aggregate.charges[0].charge_id
    charge_split_id = bill_aggregate.charges[0].charge_splits[0].charge_split_id

    add_allowance_url = (
        'v1/bills/'
        + bill_id
        + '/charges/'
        + str(charge_id)
        + '/charge-splits/'
        + str(charge_split_id)
        + '/allowances'
    )
    new_allowance = dict(
        data=dict(pretax_amount='50 INR', remarks='add new allowance'),
        resource_version=bill_aggregate.current_version(),
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        add_allowance_url,
        data=json.dumps(new_allowance),
        content_type='application/json',
        headers=headers,
    )

    assert response.status_code == 200

    return bill_id, charge_id, charge_split_id, response.json['data']['allowance_id']


def test_cancel_allowance_updates_allowance_status_to_cancelled(
    client, add_allowance, bill_repo
):
    bill_id, charge_id, charge_split_id, allowance_id = add_allowance

    add_allowance_url = (
        'v1/bills/'
        + bill_id
        + '/charges/'
        + str(charge_id)
        + '/charge-splits/'
        + str(charge_split_id)
        + '/allowances'
    )
    update_allowance_url = add_allowance_url + '/' + str(allowance_id)

    version = bill_repo.load(bill_id).bill.version
    payload = dict(data=dict(status='cancelled'), resource_version=version)
    headers = {"X-User-Type": "super-admin"}

    cancel_response = client.patch(
        update_allowance_url,
        data=json.dumps(payload),
        content_type='application/json',
        headers=headers,
    )
    assert cancel_response.status_code == 200
    assert cancel_response.json['data']['status'] == ChargeStatus.CANCELLED.value


def test_consume_allowance_updates_allowance_status_to_consumed(
    client, add_allowance, bill_repo
):
    bill_id, charge_id, charge_split_id, allowance_id = add_allowance

    add_allowance_url = (
        'v1/bills/'
        + bill_id
        + '/charges/'
        + str(charge_id)
        + '/charge-splits/'
        + str(charge_split_id)
        + '/allowances'
    )
    update_allowance_url = add_allowance_url + '/' + str(allowance_id)

    payload = dict(
        data=dict(status='consumed'),
        resource_version=bill_repo.load(bill_id).bill.version,
    )
    headers = {"X-User-Type": "super-admin"}

    consume_response = client.patch(
        update_allowance_url,
        data=json.dumps(payload),
        content_type='application/json',
        headers=headers,
    )
    assert consume_response.status_code == 200

    assert consume_response.json['data']['status'] == ChargeStatus.CONSUMED.value


def test_adding_allowance_on_different_accounts_based_on_priority(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    bill_repo,
):
    booking_aggregate, bill_aggregate = (
        checked_in_booking_and_bill_pending_checkout_2[0],
        checked_in_booking_and_bill_pending_checkout_2[1],
    )

    bill_aggregate.consume_charges(
        charges=bill_aggregate.charges,
        business_date=active_hotel_aggregate.hotel.current_business_date,
    )
    bill_repo.update(bill_aggregate)

    def add_allowance(bill):
        bill_id, charge_id = bill.bill_id, bill.charges[0].charge_id
        charge_split_id = bill.charges[0].charge_splits[0].charge_split_id

        add_allowance_url = (
            'v1/bills/'
            + bill_id
            + '/charges/'
            + str(charge_id)
            + '/charge-splits/'
            + str(charge_split_id)
            + '/allowances'
        )
        new_allowance = dict(
            data=dict(pretax_amount='20 INR', remarks='add new allowance'),
            resource_version=bill.current_version(),
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            add_allowance_url,
            data=json.dumps(new_allowance),
            content_type='application/json',
            headers=headers,
        )
        return charge_id, charge_split_id, response

    # First priority is to have allowance's billed entity account same as charge split's billed entity
    # account if the account isn't locked yet. Adding allowance to test the same
    charge_id, charge_split_id, allowance_data = add_allowance(bill_aggregate)
    assert allowance_data.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)
    charge_split = charge.get_split(charge_split_id)
    allowance = charge_split.get_allowance(
        int(allowance_data.json['data']['allowance_id'])
    )
    assert charge_split.billed_entity_account == allowance.billed_entity_account

    # TODO: Have to set hotel context here. Somehow it's not getting set here
    hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
    # Marking the account as invoiced and locking it
    bill_aggregate.mark_billed_entity_account_as_invoiced(
        allowance.billed_entity_account
    )
    bill_repo.update(bill_aggregate)

    # Adding an allowance again to test the third priority as currently there are no  uninvoiced allowance
    # account present in the bill. This will result into a new account which will be an allowance account
    charge_id, charge_split_id, allowance_data = add_allowance(bill_aggregate)
    assert allowance_data.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)
    charge_split = charge.get_split(charge_split_id)
    allowance = charge_split.get_allowance(
        int(allowance_data.json['data']['allowance_id'])
    )
    billed_entity = bill_aggregate.get_billed_entity(
        allowance.billed_entity_account.billed_entity_id
    )
    account = billed_entity.get_account(allowance.billed_entity_account.account_number)
    assert account.is_allowance_account()
    allowance_account_number = account.account_number

    # Adding an allowance now to test second priority which will add allowance on available uninvoiced
    # allowance account
    charge_id, charge_split_id, allowance_data = add_allowance(bill_aggregate)
    assert allowance_data.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)
    charge_split = charge.get_split(charge_split_id)
    allowance = charge_split.get_allowance(
        int(allowance_data.json['data']['allowance_id'])
    )
    billed_entity = bill_aggregate.get_billed_entity(
        allowance.billed_entity_account.billed_entity_id
    )
    account = billed_entity.get_account(allowance.billed_entity_account.account_number)
    assert account.account_number == allowance_account_number

    # Marking the uninvoiced allowance account as invoiced and locking it
    hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
    bill_aggregate.mark_billed_entity_account_as_invoiced(
        allowance.billed_entity_account
    )
    bill_repo.update(bill_aggregate)

    # Adding an allowance now will result into adding a new account altogether as none of other
    # priorities are available
    charge_id, charge_split_id, allowance_data = add_allowance(bill_aggregate)
    assert allowance_data.status_code == 200
    bill_aggregate = bill_repo.load(bill_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)
    charge_split = charge.get_split(charge_split_id)
    allowance = charge_split.get_allowance(
        int(allowance_data.json['data']['allowance_id'])
    )
    billed_entity = bill_aggregate.get_billed_entity(
        allowance.billed_entity_account.billed_entity_id
    )
    account = billed_entity.get_account(allowance.billed_entity_account.account_number)
    assert (
        account.is_allowance_account()
        and allowance_account_number != account.account_number
    )
