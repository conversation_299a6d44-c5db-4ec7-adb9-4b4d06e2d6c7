import json

import pytest
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus.domain.billing.errors import BillingErrors
from prometheus.itests.api_wrappers.billing_wrappers import edit_charge
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    make_booking,
)
from prometheus.itests.booking.test_edit_chares import edit_billing_instruction_payload
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v2_request,
)
from prometheus.tests.domain.conftest import id_generator
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    PaymentInstruction,
)
from ths_common.constants.booking_constants import BookingStatus, ExpenseStatus


def test_transfer_charge_api(
    client,
    booking_repo,
    bill_repo,
    create_booking_payload,
    active_hotel_aggregate,
    hotel_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    source_booking_aggregate = booking_repo.load(booking_id)
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        source_booking_aggregate.booking.version,
        source_booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2

    expense_v2_request = create_expenses_v2_request(
        payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
        expense_status=ExpenseStatus.CONSUMED,
        single_charge_split=True,
    )
    json_data = add_expenses_v2(client, booking_id, expense_v2_request)
    expense_charge_ids_added = [expense['charge_id'] for expense in json_data['data']]
    source_charge_id = expense_charge_ids_added[0]
    source_bill_aggregate = bill_repo.load(source_booking_aggregate.bill_id)
    new_booking_payload = json.loads(create_booking_payload)
    new_booking_payload['reference_number'] = id_generator()
    destination_booking_id = make_booking(client, {"data": new_booking_payload})
    transfer_charges_url = (
        '/v1/bills/'
        + source_booking_aggregate.bill_id
        + '/charges/'
        + str(source_charge_id)
        + '/transfer'
    )
    response = client.post(
        transfer_charges_url,
        data=json.dumps(
            dict(
                data=dict(booking_id=destination_booking_id),
                resource_version=source_bill_aggregate.bill.version,
            )
        ),
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200
    assert response.json is not None
    destination_bill_id = response.json['data']['destination_bill_id']
    destination_charge_id = response.json['data']['charge']['charge_id']
    assert len(response.json['data']['charge']['charge_splits']) == 1

    source_bill_aggregate = bill_repo.load(source_booking_aggregate.bill_id)
    source_charge = source_bill_aggregate.get_charge(source_charge_id)
    assert source_charge.get_pretax_amount_post_allowance() == Money(
        0, CurrencyType.INR
    )
    assert (
        len(
            [
                allowance
                for charge_split in source_charge.charge_splits
                for allowance in charge_split.allowances
            ]
        )
        == 1
    )
    destination_bill_aggregate = bill_repo.load(destination_bill_id)
    assert destination_bill_aggregate.get_charge(destination_charge_id)


@pytest.mark.skip(reason="We will be allowing this from now")
def test_transfer_charge_api_fails_for_charge_split_into_multiple_billed_entities(
    client,
    booking_repo,
    bill_repo,
    create_booking_payload,
    active_hotel_aggregate,
    hotel_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    source_booking_aggregate = booking_repo.load(booking_id)
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        source_booking_aggregate.booking.version,
        source_booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2

    expense_v2_request = create_expenses_v2_request(
        payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
        expense_status=ExpenseStatus.CONSUMED,
        single_charge_split=False,
    )
    json_data = add_expenses_v2(client, booking_id, expense_v2_request)
    expense_charge_ids_added = [expense['charge_id'] for expense in json_data['data']]
    source_charge_id = expense_charge_ids_added[0]
    source_bill_aggregate = bill_repo.load(source_booking_aggregate.bill_id)
    new_booking_payload = json.loads(create_booking_payload)
    new_booking_payload['reference_number'] = id_generator()
    destination_booking_id = make_booking(client, {"data": new_booking_payload})
    transfer_charges_url = (
        '/v1/bills/'
        + source_booking_aggregate.bill_id
        + '/charges/'
        + str(source_charge_id)
        + '/transfer'
    )
    response = client.post(
        transfer_charges_url,
        data=json.dumps(
            dict(
                data=dict(booking_id=destination_booking_id),
                resource_version=source_bill_aggregate.bill.version,
            )
        ),
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 400
    assert response.json is not None
    error = response.json['errors'][0]
    # Strip first 4 characters to get actual error code
    # pylint: disable=no-member
    assert (
        error['code'][4:] == BillingErrors.ALLOWANCE_NOT_ALLOWED_AFTER_SPLIT.error_code
    )


def test_transfer_charge_api_transfers_when_charge_applicable_date_is_after_destination_booking_checkin_date(
    client, booking_repo, bill_repo, active_hotel_aggregate, hotel_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    source_booking_id = response['data']['booking_id']
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_destination_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    response = make_booking(
        client=client,
        payload={"data": create_destination_booking_payload},
        return_complete_response=True,
    )
    destination_booking_id = response['data']['booking_id']

    source_booking_aggregate = booking_repo.load(source_booking_id)
    destination_booking_aggregate = booking_repo.load(destination_booking_id)
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        source_booking_aggregate.booking.version,
        source_booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, source_booking_id, checkin_payload)
    from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2

    expense_v2_request = create_expenses_v2_request(
        payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
        expense_status=ExpenseStatus.CONSUMED,
        single_charge_split=True,
        dates=[dateutils.add(dateutils.current_datetime())],
    )
    json_data = add_expenses_v2(client, source_booking_id, expense_v2_request)
    expense_charge_ids_added = [expense['charge_id'] for expense in json_data['data']]
    source_charge_id = expense_charge_ids_added[0]
    source_bill_aggregate = bill_repo.load(source_booking_aggregate.bill_id)
    source_bill_aggregate.consume_charge(
        source_bill_aggregate.get_charge(source_charge_id),
        dateutils.add(dateutils.current_datetime()),
    )
    bill_repo.update(source_bill_aggregate)
    transfer_charges_url = (
        '/v1/bills/'
        + source_booking_aggregate.bill_id
        + '/charges/'
        + str(source_charge_id)
        + '/transfer'
    )
    response = client.post(
        transfer_charges_url,
        data=json.dumps(
            dict(
                data=dict(booking_id=destination_booking_id),
                resource_version=source_bill_aggregate.bill.version,
            )
        ),
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200
    assert response.json is not None
    destination_bill_id = response.json['data']['destination_bill_id']
    destination_charge_id = response.json['data']['charge']['charge_id']
    assert len(response.json['data']['charge']['charge_splits']) == 1

    source_bill_aggregate = bill_repo.load(source_booking_aggregate.bill_id)
    source_charge = source_bill_aggregate.get_charge(source_charge_id)
    assert source_charge.get_pretax_amount_post_allowance() == Money(
        0, CurrencyType.INR
    )
    assert (
        len(
            [
                allowance
                for charge_split in source_charge.charge_splits
                for allowance in charge_split.allowances
            ]
        )
        == 1
    )
    destination_bill_aggregate = bill_repo.load(destination_bill_id)
    assert destination_bill_aggregate.get_charge(destination_charge_id)


def test_transfer_charge_api_fails_when_charge_applicable_date_is_before_destination_booking_checkin_date(
    client, booking_repo, bill_repo, active_hotel_aggregate, hotel_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    source_booking_id = response['data']['booking_id']
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_destination_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    response = make_booking(
        client=client,
        payload={"data": create_destination_booking_payload},
        return_complete_response=True,
    )
    destination_booking_id = response['data']['booking_id']

    source_booking_aggregate = booking_repo.load(source_booking_id)
    destination_booking_aggregate = booking_repo.load(destination_booking_id)
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        source_booking_aggregate.booking.version,
        source_booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, source_booking_id, checkin_payload)
    from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2

    expense_v2_request = create_expenses_v2_request(
        payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
        expense_status=ExpenseStatus.CONSUMED,
        single_charge_split=True,
    )
    json_data = add_expenses_v2(client, source_booking_id, expense_v2_request)
    expense_charge_ids_added = [expense['charge_id'] for expense in json_data['data']]
    source_charge_id = expense_charge_ids_added[0]
    source_bill_aggregate = bill_repo.load(source_booking_aggregate.bill_id)
    source_bill_aggregate.consume_charge(
        source_bill_aggregate.get_charge(source_charge_id),
        dateutils.add(dateutils.current_datetime()),
    )
    bill_repo.update(source_bill_aggregate)
    transfer_charges_url = (
        '/v1/bills/'
        + source_booking_aggregate.bill_id
        + '/charges/'
        + str(source_charge_id)
        + '/transfer'
    )
    response = client.post(
        transfer_charges_url,
        data=json.dumps(
            dict(
                data=dict(booking_id=destination_booking_id),
                resource_version=source_bill_aggregate.bill.version,
            )
        ),
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 400


def test_transfer_charge_api_creates_charge_with_charge_to_as_checked_in_guest_of_room_of_destination_booking(
    client,
    booking_repo,
    bill_repo,
    create_booking_payload,
    active_hotel_aggregate,
    hotel_repo,
):
    headers = {'X-User-Type': 'super-admin'}
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    source_booking_aggregate = booking_repo.load(booking_id)
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        source_booking_aggregate.booking.version,
        source_booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2

    expense_v2_request = create_expenses_v2_request(
        payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
        expense_status=ExpenseStatus.CONSUMED,
        single_charge_split=True,
    )
    json_data = add_expenses_v2(client, booking_id, expense_v2_request)
    expense_charge_ids_added = [expense['charge_id'] for expense in json_data['data']]
    source_charge_id = expense_charge_ids_added[0]
    source_bill_aggregate = bill_repo.load(source_booking_aggregate.bill_id)
    new_booking_payload = json.loads(create_booking_payload)
    new_booking_payload['reference_number'] = id_generator()
    destination_booking_id = make_booking(client, {"data": new_booking_payload})
    transfer_charges_url = (
        '/v1/bills/'
        + source_booking_aggregate.bill_id
        + '/charges/'
        + str(source_charge_id)
        + '/transfer'
    )
    response = client.post(
        transfer_charges_url,
        data=json.dumps(
            dict(
                data=dict(booking_id=destination_booking_id),
                resource_version=source_bill_aggregate.bill.version,
            )
        ),
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200
    assert response.json is not None
    destination_bill_id = response.json['data']['destination_bill_id']
    destination_charge_id = response.json['data']['charge']['charge_id']
    assert len(response.json['data']['charge']['charge_splits']) == 1

    destination_bill_aggregate = bill_repo.load(destination_bill_id)
    assert destination_bill_aggregate.get_charge(destination_charge_id)
    destination_booking_aggregate = booking_repo.load(destination_booking_id)
    assert destination_bill_aggregate.get_charge(destination_charge_id).charge_to == [
        destination_booking_aggregate.room_stays[0].guest_stays[0].guest_id
    ]


def test_transfer_charges_api_for_rate_plan_charges(
    client,
    booking_repo,
    bill_repo,
    create_booking_payload,
    active_hotel_aggregate,
    hotel_repo,
):
    from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    source_booking_aggregate = booking_repo.load(booking_id)

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        source_booking_aggregate.booking.version,
        source_booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)

    expense_v2_request = create_expenses_v2_request(
        for_rate_plan=True,
        dates=[source_booking_aggregate.booking.checkin_date],
        charge_to=["2"],
        quantity=1,
    )

    json_data = add_expenses_v2(client, booking_id, expense_v2_request)

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    source_charge_ids = [charge.charge_id for charge in bill_aggregate.charges]
    assert len(source_charge_ids) == 2

    # consume charges for transfer
    bill_aggregate.consume_charges(
        charges=bill_aggregate.charges,
        business_date=active_hotel_aggregate.hotel.current_business_date,
    )
    bill_repo.update(bill_aggregate)
    charge_ids = [expense['charge_id'] for expense in json_data['data']]
    rate_plan_expense_charge = bill_aggregate.get_charge(charge_ids[0])
    assert rate_plan_expense_charge.is_inclusion_charge

    source_bill_aggregate = bill_repo.load(source_booking_aggregate.bill_id)
    new_booking_payload = json.loads(create_booking_payload)
    new_booking_payload['reference_number'] = id_generator()
    destination_booking_id = make_booking(client, {"data": new_booking_payload})
    transfer_charges_url = (
        '/v1/bills/' + source_booking_aggregate.bill_id + '/charges' + '/transfer'
    )
    response = client.post(
        transfer_charges_url,
        data=json.dumps(
            dict(
                data=dict(
                    booking_id=destination_booking_id, charge_ids=source_charge_ids
                ),
                resource_version=source_bill_aggregate.bill.version,
            )
        ),
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200
    assert response.json is not None
    destination_bill_id = response.json['data']['destination_bill_id']
    destination_charge_ids = [
        charges['charge_id'] for charges in response.json['data']['charges']
    ]
    assert len(response.json['data']['charges'][0]['charge_splits']) == 1
    assert len(response.json['data']['charges'][1]['charge_splits']) == 1

    source_bill_aggregate = bill_repo.load(source_booking_aggregate.bill_id)
    source_charges = [
        source_bill_aggregate.get_charge(source_charge_id)
        for source_charge_id in source_charge_ids
    ]
    for source_charge in source_charges:
        assert source_charge.get_pretax_amount_post_allowance() == Money(
            0, CurrencyType.INR
        )
        assert (
            len(
                [
                    allowance
                    for charge_split in source_charge.charge_splits
                    for allowance in charge_split.allowances
                ]
            )
            == 1
        )
    destination_bill_aggregate = bill_repo.load(destination_bill_id)
    assert all(
        [
            destination_bill_aggregate.get_charge(destination_charge_id)
            for destination_charge_id in destination_charge_ids
        ]
    )
