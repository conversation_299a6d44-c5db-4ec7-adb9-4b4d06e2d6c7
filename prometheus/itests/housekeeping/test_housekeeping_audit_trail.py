import json

import pytest
from treebo_commons.utils import dateutils

from prometheus.domain.catalog.models import RoomModel
from prometheus.infrastructure.database import db_engine
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    checkout_booking,
    make_booking,
    undo_checkin_booking,
    undo_checkout_booking,
)
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.booking.test_roomstay_api import (
    create_room_change_payload,
    patch_room_stay,
)
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
    create_checkin_payload_without_new_guest,
    create_checkout_payload,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_with_charge_decision_payload,
)
from prometheus.tests.factories.aggregate_factories import RoomAggregateFactory
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.inventory_constants import RoomCurrentStatus


@pytest.fixture()
def temp_create_new_room(request, room_repo):
    room_type_id = (
        request.param.get('room_type_id', 'rt01')
        if hasattr(request, 'param')
        else 'rt01'
    )
    room_aggregate = RoomAggregateFactory(
        room__room_id="16", room__room_type_id=room_type_id
    )
    room_repo.save(room_aggregate)
    db_engine.get_session().commit()

    yield room_aggregate

    room_repo.filter(RoomModel, RoomModel.room_id == "16").delete()
    db_engine.get_session().commit()


def test_room_change_after_checkin_should_update_housekeeping_audit_trail_for_both_room(
    create_booking_payload,
    active_hotel_aggregate,
    booking_repo,
    client,
    hotel_repo,
    room_repo,
    temp_create_new_room,
    housekeeping_audit_trail_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)

    # Roll over business day
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    room_stay_id = 1  # room_type_id is rt01
    room_id = "15"
    checkin_date = dateutils.current_datetime()
    checkin_payload = create_checkin_payload_without_new_guest(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        checkin_date,
        room_stay_id,
        room_id,
    )

    checkin_booking(client, booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_id)

    new_room_id = "16"
    room_change_payload = create_room_change_payload(
        booking_aggregate.booking.version, new_room_id, "rt01"
    )
    patch_room_stay(client, booking_id, room_change_payload)

    audit_trails = housekeeping_audit_trail_repo.load_all(
        hotel_id=active_hotel_aggregate.hotel_id, room_ids=[room_id, new_room_id]
    )

    # Old Room Audit Trail. Should be Occupied + Vacant
    old_room_audit_trails = sorted(
        [
            at
            for at in audit_trails
            if at.housekeeping_audit_trail.room_id == int(room_id)
        ],
        key=lambda at: at.housekeeping_audit_trail.action_datetime,
    )
    assert len(old_room_audit_trails) == 2

    # Checkin
    assert (
        old_room_audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        old_room_audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    # Room Change
    assert (
        old_room_audit_trails[1].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    assert (
        old_room_audit_trails[1].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.VACANT.value
    )

    # New Room Audit Trail. Should be Occupied
    new_room_audit_trails = sorted(
        [
            at
            for at in audit_trails
            if at.housekeeping_audit_trail.room_id == int(new_room_id)
        ],
        key=lambda at: at.housekeeping_audit_trail.action_datetime,
    )
    assert len(new_room_audit_trails) == 1
    # Room Changed to this room
    assert (
        new_room_audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        new_room_audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )


def test_booking_checkin_should_add_housekeeping_audit_trail(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    room_allotment_repo,
    hotel_repo,
    housekeeping_audit_trail_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_id)
    assert (
        booking_aggregate.booking.status == BookingStatus.CHECKED_IN
    ), "Booking should move to checked-in state"

    room_allotment_aggregate = room_allotment_repo.load(
        active_hotel_aggregate.hotel.hotel_id, "15"
    )
    assert (
        len(room_allotment_aggregate.all_room_allotments) == 1
    ), "A room allotment should be created for room 15"

    audit_trails = housekeeping_audit_trail_repo.load_all(
        hotel_id=active_hotel_aggregate.hotel_id, room_ids=["15"]
    )
    assert len(audit_trails) == 1
    assert (
        audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )


@pytest.mark.parametrize('create_booking_payload', [dict(occupancy=2)], indirect=True)
def test_checkin_guests_in_already_checked_in_room_should_not_create_new_audit_trail(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    room_allotment_repo,
    hotel_repo,
    housekeeping_audit_trail_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version, guest_stay_id=1
    )
    checkin_booking(client, booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_id)
    assert (
        booking_aggregate.booking.status == BookingStatus.PART_CHECKIN
    ), "Booking should move to part checked-in state"

    room_allotment_aggregate = room_allotment_repo.load(
        active_hotel_aggregate.hotel.hotel_id, "15"
    )
    assert (
        len(room_allotment_aggregate.all_room_allotments) == 1
    ), "A room allotment should be created for room 15"

    audit_trails = housekeeping_audit_trail_repo.load_all(
        hotel_id=active_hotel_aggregate.hotel_id, room_ids=["15"]
    )
    assert len(audit_trails) == 1
    assert (
        audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version, guest_stay_id=2
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert (
        booking_aggregate.booking.status == BookingStatus.CHECKED_IN
    ), "Booking should move to part checked-in state"

    room_allotment_aggregate = room_allotment_repo.load(
        active_hotel_aggregate.hotel.hotel_id, "15"
    )
    assert len(room_allotment_aggregate.all_room_allotments) == 1, (
        "New allotment shouldn't be created for guest " "checkin in same room"
    )

    audit_trails = housekeeping_audit_trail_repo.load_all(
        hotel_id=active_hotel_aggregate.hotel_id, room_ids=["15"]
    )
    assert (
        len(audit_trails) == 1
    ), "New HK audit trail shouldn't be created for same room checkin"


def test_booking_undo_checkin_should_add_another_housekeeping_audit_trail(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    housekeeping_audit_trail_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    action_id = checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_IN

    undo_checkin_booking(client, booking_id, action_id)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CONFIRMED

    audit_trails = housekeeping_audit_trail_repo.load_all(
        hotel_id=active_hotel_aggregate.hotel_id, room_ids=["15"]
    )
    assert len(audit_trails) == 2

    audit_trails = sorted(
        audit_trails, key=lambda audit: audit.housekeeping_audit_trail.action_datetime
    )
    assert (
        audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )

    assert (
        audit_trails[1].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    assert (
        audit_trails[1].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.VACANT.value
    )


def test_booking_checkout_should_create_housekeeping_audit_trail(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    housekeeping_audit_trail_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_id, checkout_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_OUT

    audit_trails = housekeeping_audit_trail_repo.load_all(
        hotel_id=active_hotel_aggregate.hotel_id, room_ids=["15"]
    )
    assert len(audit_trails) == 2

    audit_trails = sorted(
        audit_trails, key=lambda audit: audit.housekeeping_audit_trail.action_datetime
    )
    assert (
        audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )

    assert (
        audit_trails[1].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    assert (
        audit_trails[1].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.VACANT.value
    )


def test_booking_undo_checkout_should_add_another_housekeeping_audit_trail_after_checkin_and_checkout(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    booking_audit_trail_repo,
    hotel_repo,
    housekeeping_audit_trail_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    action_id = checkout_booking(client, booking_id, checkout_payload)

    undo_checkout_booking(client, booking_id, action_id)

    audit_trails = housekeeping_audit_trail_repo.load_all(
        hotel_id=active_hotel_aggregate.hotel_id, room_ids=["15"]
    )
    assert len(audit_trails) == 3

    audit_trails = sorted(
        audit_trails, key=lambda audit: audit.housekeeping_audit_trail.action_datetime
    )
    # Checkin
    assert (
        audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )

    # Checkout
    assert (
        audit_trails[1].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    assert (
        audit_trails[1].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.VACANT.value
    )

    # Reverse Checkout
    assert (
        audit_trails[2].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        audit_trails[2].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )


def test_undo_checkin_after_room_change_should_make_room_as_vacant(
    create_booking_payload,
    active_hotel_aggregate,
    booking_repo,
    client,
    hotel_repo,
    room_repo,
    temp_create_new_room,
    housekeeping_audit_trail_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)

    # Roll over business day
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    room_stay_id = 1  # room_type_id is rt01
    room_id = "15"
    checkin_date = dateutils.current_datetime()
    checkin_payload = create_checkin_payload_without_new_guest(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        checkin_date,
        room_stay_id,
        room_id,
    )

    action_id = checkin_booking(client, booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_id)

    new_room_id = "16"
    room_change_payload = create_room_change_payload(
        booking_aggregate.booking.version, new_room_id, "rt01"
    )
    patch_room_stay(client, booking_id, room_change_payload)

    # ===== Audit Trail and Current Status after Room Change from 15 to 16
    audit_trails = housekeeping_audit_trail_repo.load_all(
        hotel_id=active_hotel_aggregate.hotel_id, room_ids=[room_id, new_room_id]
    )

    # Old Room Audit Trail. Should be Occupied + Vacant
    old_room_audit_trails = sorted(
        [
            at
            for at in audit_trails
            if at.housekeeping_audit_trail.room_id == int(room_id)
        ],
        key=lambda at: at.housekeeping_audit_trail.action_datetime,
    )
    assert len(old_room_audit_trails) == 2

    # Checkin
    assert (
        old_room_audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        old_room_audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    # Room Change
    assert (
        old_room_audit_trails[1].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    assert (
        old_room_audit_trails[1].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.VACANT.value
    )

    # New Room Audit Trail. Should be Occupied
    new_room_audit_trails = sorted(
        [
            at
            for at in audit_trails
            if at.housekeeping_audit_trail.room_id == int(new_room_id)
        ],
        key=lambda at: at.housekeeping_audit_trail.action_datetime,
    )
    assert len(new_room_audit_trails) == 1
    # Room Changed to this room
    assert (
        new_room_audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        new_room_audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )

    # Change room back from 16 to 15
    room_change_payload = create_room_change_payload(
        booking_aggregate.booking.version + 1, room_id, "rt01"
    )
    patch_room_stay(client, booking_id, room_change_payload)

    # ===== Audit Trail and Current Status after Room Change from 15 to 16
    audit_trails = housekeeping_audit_trail_repo.load_all(
        hotel_id=active_hotel_aggregate.hotel_id, room_ids=[room_id, new_room_id]
    )

    # Old Room Audit Trail. Should be Occupied + Vacant
    old_room_audit_trails = sorted(
        [
            at
            for at in audit_trails
            if at.housekeeping_audit_trail.room_id == int(room_id)
        ],
        key=lambda at: at.housekeeping_audit_trail.action_datetime,
    )
    assert len(old_room_audit_trails) == 3

    # Checkin
    assert (
        old_room_audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        old_room_audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    # Room Change
    assert (
        old_room_audit_trails[1].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    assert (
        old_room_audit_trails[1].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.VACANT.value
    )
    # Room Change Again
    assert (
        old_room_audit_trails[2].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        old_room_audit_trails[2].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )

    # New Room Audit Trail. Should be Vacant again
    new_room_audit_trails = sorted(
        [
            at
            for at in audit_trails
            if at.housekeeping_audit_trail.room_id == int(new_room_id)
        ],
        key=lambda at: at.housekeeping_audit_trail.action_datetime,
    )
    assert len(new_room_audit_trails) == 2
    # Room Changed to this room
    assert (
        new_room_audit_trails[0].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.VACANT.value
    )
    assert (
        new_room_audit_trails[0].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    # Room Changed back from this room to older room
    assert (
        new_room_audit_trails[1].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    assert (
        new_room_audit_trails[1].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.VACANT.value
    )

    # Now do reverse checkin from room 15. It should be VACANT
    undo_checkin_booking(client, booking_id, action_id)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CONFIRMED

    audit_trails = housekeeping_audit_trail_repo.load_all(
        hotel_id=active_hotel_aggregate.hotel_id, room_ids=[room_id]
    )
    # New Room 16 -> Should have one more entry for reverse checkin
    assert len(audit_trails) == 4

    audit_trails = sorted(
        audit_trails, key=lambda audit: audit.housekeeping_audit_trail.action_datetime
    )
    assert (
        audit_trails[3].housekeeping_audit_trail.old_room_status
        == RoomCurrentStatus.OCCUPIED.value
    )
    assert (
        audit_trails[3].housekeeping_audit_trail.new_room_status
        == RoomCurrentStatus.VACANT.value
    )
