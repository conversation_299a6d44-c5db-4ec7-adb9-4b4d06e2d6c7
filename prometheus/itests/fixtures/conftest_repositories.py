import pytest

from prometheus.async_job.job.repositories.job_repository import JobRepository
from prometheus.domain.billing.repositories import (
    CashierSessionRepository,
    CreditNoteRepository,
)
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.cash_register_repository import (
    CashRegisterRepository,
)
from prometheus.domain.billing.repositories.credit_note_series_repository import (
    CreditNoteSequenceRepository,
)
from prometheus.domain.billing.repositories.credit_shell_refund_repository import (
    CreditShellRefundRepository,
)
from prometheus.domain.billing.repositories.credit_shell_repository import (
    CreditShellRepository,
)
from prometheus.domain.billing.repositories.currency_exchange_repository import (
    CurrencyExchangeRepository,
)
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.repositories.invoice_series_repository import (
    InvoiceSequenceRepository,
)
from prometheus.domain.billing.repositories.payment_receipt_repository import (
    PaymentReceiptRepository,
)
from prometheus.domain.booking.repositories.addon_repository import AddonRepository
from prometheus.domain.booking.repositories.booking_action_repository import (
    BookingActionRepository,
)
from prometheus.domain.booking.repositories.booking_audit_trail_repository import (
    BookingAuditTrailRepository,
)
from prometheus.domain.booking.repositories.booking_funding_repository import (
    BookingFundingRepository,
)
from prometheus.domain.booking.repositories.booking_invoice_group_repository import (
    BookingInvoiceGroupRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)
from prometheus.domain.booking.repositories.house_status_repository import (
    HouseStatusRepository,
)
from prometheus.domain.booking.repositories.room_stay_overflow_repository import (
    RoomStayOverflowRepository,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.night_audit_repository import (
    NightAuditRepository,
)
from prometheus.domain.catalog.repositories.reseller_gst_repository import (
    ResellerGstRepository,
)
from prometheus.domain.catalog.repositories.room_repository import RoomRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.catalog.repositories.seller_repository import SellerRepository
from prometheus.domain.catalog.repositories.sku_category_repository import (
    SkuCategoryRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.integration_event.repositories.integration_event_repository import (
    IntegrationEventRepository,
)
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from prometheus.domain.inventory.repositories.housekeeping_audit_trail_repository import (
    HouseKeepingAuditTrailRepository,
)
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from prometheus.domain.inventory.repositories.room_type_inventory_repository import (
    RoomTypeInventoryRepository,
)
from prometheus.reporting.repository.billing.credit_note_report_repository import (
    CreditNoteReportRepository,
)
from prometheus.reporting.repository.billing.invoice_report_repository import (
    InvoiceReportRepository,
)


@pytest.fixture(scope='session')
def booking_repo():
    return BookingRepository()


@pytest.fixture(scope='session')
def addon_repo():
    return AddonRepository()


@pytest.fixture(scope='session')
def bill_repo():
    return BillRepository()


@pytest.fixture(scope='session')
def credit_note_repo():
    return CreditNoteRepository()


@pytest.fixture
def credit_shell_repo():
    return CreditShellRepository()


@pytest.fixture
def credit_shell_refund_repo():
    return CreditShellRefundRepository()


@pytest.fixture(scope='session')
def expense_repo():
    return ExpenseItemRepository()


@pytest.fixture(scope='session')
def integration_event_repo():
    return IntegrationEventRepository()


@pytest.fixture(scope='session')
def room_type_repo():
    return RoomTypeRepository()


@pytest.fixture(scope='session')
def credit_note_report_repo():
    return CreditNoteReportRepository()


@pytest.fixture(scope='session')
def invoice_report_repo():
    return InvoiceReportRepository()


@pytest.fixture(scope='session')
def hotel_repo():
    return HotelRepository()


@pytest.fixture(scope='session')
def job_repo():
    return JobRepository()


@pytest.fixture(scope='session')
def room_type_inventory_repo():
    return RoomTypeInventoryRepository()


@pytest.fixture(scope='session')
def inventory_block_repo():
    return InventoryBlockRepository()


@pytest.fixture(scope='session')
def room_stay_overflow_repo():
    return RoomStayOverflowRepository()


@pytest.fixture(scope='session')
def room_repo():
    return RoomRepository()


@pytest.fixture(scope='session')
def hotel_config_repo():
    return HotelConfigRepository()


@pytest.fixture(scope='session')
def booking_action_repo():
    return BookingActionRepository()


@pytest.fixture(scope='session')
def booking_audit_trail_repo():
    return BookingAuditTrailRepository()


@pytest.fixture(scope='session')
def sku_category_repo():
    return SkuCategoryRepository()


@pytest.fixture(scope='session')
def booking_invoice_group_repo():
    return BookingInvoiceGroupRepository()


@pytest.fixture(scope='session')
def room_allotment_repo():
    return RoomAllotmentRepository()


@pytest.fixture(scope='session')
def reseller_gst_repo():
    return ResellerGstRepository()


@pytest.fixture(scope='session')
def invoice_repo():
    return InvoiceRepository()


@pytest.fixture(scope='session')
def invoice_series_repo():
    return InvoiceSequenceRepository()


@pytest.fixture(scope='session')
def credit_note_series_repo():
    return CreditNoteSequenceRepository()


@pytest.fixture(scope='session')
def night_audit_repo():
    return NightAuditRepository()


@pytest.fixture(scope='session')
def cashier_session_repo():
    return CashierSessionRepository()


@pytest.fixture(scope='session')
def cash_register_repo():
    return CashRegisterRepository()


@pytest.fixture(scope='session')
def payment_receipt_repo():
    return PaymentReceiptRepository()


@pytest.fixture(scope='session')
def currency_exchange_repo():
    return CurrencyExchangeRepository()


@pytest.fixture(scope='session')
def seller_repo():
    return SellerRepository()


@pytest.fixture(scope='session')
def housekeeping_audit_trail_repo():
    return HouseKeepingAuditTrailRepository()


@pytest.fixture(scope='session')
def house_status_repository():
    return HouseStatusRepository()


@pytest.fixture(scope='session')
def dnr_repository():
    return DNRRepository()


@pytest.fixture(scope='session')
def booking_funding_repo():
    return BookingFundingRepository()
