import datetime
import json

import pytest
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import (
    add,
    current_datetime,
    date_to_ymd_str,
    to_date,
)

from object_registry import locate_instance
from prometheus import crs_context
from prometheus.application.end_of_day.night_audit_service import NightAuditService
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.dtos.guest_checkin_data import Guest<PERSON>heckinData
from prometheus.domain.booking.dtos.room_allocation_data import RoomAllocationData
from prometheus.domain.booking.entities.booking import Booking
from prometheus.domain.booking.entities.customer import Customer
from prometheus.domain.booking.entities.guest_stay import GuestStay
from prometheus.domain.booking.entities.room_stay import RoomStay
from prometheus.domain.catalog.entities.room import Room
from prometheus.infrastructure.database import db_engine
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    checkout_booking,
    make_booking,
    undo_checkout_booking,
)
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.billing.test_invoice_modification import create_locked_invoices
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
    create_checkout_payload,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_with_charge_decision_payload,
)
from prometheus.tests.factories.aggregate_factories import (
    BillFactory,
    BookingAggregateFactory,
)
from prometheus.tests.factories.entity_factories import (
    GuestStayFactory,
    RoomStayFactory,
)
from prometheus.tests.mockers import (
    mock_authn_service_client,
    mock_authz_service_client,
    mock_catalog_client,
    mock_communication_service_client,
    mock_role_manager,
    mock_trial_balance_reporting_service,
)
from prometheus.tests.test_utils import today, today_minus_days, tomorrow, yesterday
from ths_common.constants.billing_constants import BilledEntityCategory, InvoiceStatus
from ths_common.constants.booking_constants import (
    AgeGroup,
    BookingStatus,
    BookingTypes,
    ExpenseAddedBy,
    ExpenseStatus,
    Genders,
    RoomStayType,
)
from ths_common.constants.catalog_constants import RoomStatus, SellerType
from ths_common.value_objects import BookingSource


def last_week():
    return dateutils.subtract(current_datetime(), days=7)


def next_week():
    return dateutils.add(current_datetime(), days=7)


@pytest.fixture
def closed_booking_and_bill(booking_repo, booking_and_bill):
    booking_aggregate = booking_and_bill[0]
    booking_aggregate.booking.status = BookingStatus.CHECKED_OUT
    booking_repo.update(booking_aggregate)
    return booking_and_bill


@pytest.fixture
def past_booking_and_bill_not_checked_in():
    checkin_date = today_minus_days(2)
    checkout_date = today_minus_days(1)
    bill = BillFactory(bill__bill_id="BIL2")
    booking = BookingAggregateFactory(
        booking__bill_id=bill.bill.bill_id,
        booking__checkin_date=checkin_date,
        booking__checkout_date=checkout_date,
        room_stays__0__checkin_date=checkin_date,
        room_stays__0__checkout_date=checkout_date,
        room_stays__0__guest_stays__0__checkin_date=checkin_date,
        room_stays__0__guest_stays__0__checkout_date=checkout_date,
    )
    return booking, bill


@pytest.fixture
def some_booking_and_bill():
    bill = BillFactory(bill__bill_id="BIL3")
    booking = BookingAggregateFactory(booking__bill_id=bill.bill.bill_id)
    return booking, bill


@pytest.fixture
def checked_in_booking_and_bill_pending_checkout(active_hotel_aggregate):
    # TODO: Get rid of this fixture
    # TODO: Why is this context setup needed, since active_hotel_aggregate already sets context.
    # test_critical_tasks_api::test_critical_task_search_returns_pending_checkouts fails without setting this context
    active_hotel_aggregate.hotel.current_business_date = dateutils.subtract(
        dateutils.current_date(), days=2
    )
    hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)

    # Create a booking and checkin
    bill = BillFactory(bill__bill_id="BIL13", with_charges=True)
    checkout_date = dateutils.datetime_at_given_time(
        yesterday(), hotel_context.checkout_time
    )
    checkin_date = dateutils.datetime_at_given_time(
        yesterday() - datetime.timedelta(days=1), hotel_context.checkin_time
    )

    charge_id_map = {date_to_ymd_str(to_date(checkout_date)): bill.charges[0].charge_id}

    guest_stay = GuestStayFactory(
        checkin_date=checkin_date, checkout_date=checkout_date
    )
    room_stay = RoomStayFactory(
        guest_stays=[guest_stay],
        charge_id_map=charge_id_map,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
    )
    booking = BookingAggregateFactory(
        booking__checkin_date=checkin_date,
        booking__checkout_date=checkout_date,
        booking__status=BookingStatus.CHECKED_IN,
        booking__bill_id=bill.bill.bill_id,
        room_stays=[room_stay],
    )

    guest_id = booking.customers[0].customer_id
    guest_checkin_data = [
        GuestCheckinData(
            guest_stay_id=guest_stay.guest_stay_id,
            guest_id=guest_id,
            checkin_date=checkin_date,
        )
    ]
    room = Room(
        room_id=1,
        hotel_id="1",
        room_type_id="rt01",
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_checkin_date = min(gc.checkin_date for gc in guest_checkin_data)
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=room_allocation_checkin_date
    )

    crs_context.get_hotel_context()
    booking.checkin_guests(
        room_stay.room_stay_id, guest_checkin_data, room_allocation_data
    )

    return booking, bill


@pytest.fixture
def checked_in_booking_and_bill():
    # Create a booking and checkin
    bill = BillFactory(bill__bill_id="BIL13", with_charges=True)
    today_ = today()
    yesterday_ = yesterday()

    charge_id_map = {date_to_ymd_str(to_date(today_)): bill.charges[0].charge_id}

    guest_stay = GuestStayFactory(checkin_date=yesterday_)
    room_stay = RoomStayFactory(
        guest_stays=[guest_stay], charge_id_map=charge_id_map, checkin_date=yesterday_
    )
    booking = BookingAggregateFactory(
        booking__checkin_date=yesterday_,
        booking__status=BookingStatus.CHECKED_IN,
        booking__bill_id=bill.bill.bill_id,
        room_stays=[room_stay],
    )

    guest_id = booking.customers[0].customer_id
    guest_checkin_data = [
        GuestCheckinData(
            guest_stay_id=guest_stay.guest_stay_id,
            guest_id=guest_id,
            checkin_date=today_,
        )
    ]
    room = Room(
        room_id=1,
        hotel_id="1",
        room_type_id="rt01",
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_checkin_date = min(gc.checkin_date for gc in guest_checkin_data)
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=room_allocation_checkin_date
    )

    booking.checkin_guests(
        room_stay.room_stay_id, guest_checkin_data, room_allocation_data
    )

    return booking, bill


@pytest.fixture
def week_old_booking_and_bill_not_checked_in():
    checkin_date = today_minus_days(7)
    checkout_date = today_minus_days(6)

    bill = BillFactory(bill__bill_id="BIL5")
    booking = BookingAggregateFactory(
        booking__bill_id=bill.bill.bill_id,
        booking__checkin_date=checkin_date,
        booking__checkout_date=checkout_date,
        room_stays__0__checkin_date=checkin_date,
        room_stays__0__checkout_date=checkout_date,
        room_stays__0__guest_stays__0__checkin_date=checkin_date,
        room_stays__0__guest_stays__0__checkout_date=checkout_date,
    )

    return booking, bill


@pytest.fixture
def week_old_checked_in_booking_and_bill():
    checkin_date = today_minus_days(7)
    checkout_date = today_minus_days(6)
    bill = BillFactory(bill__bill_id="BIL6")
    booking = BookingAggregateFactory(
        booking__bill_id=bill.bill.bill_id,
        booking__checkin_date=checkin_date,
        booking__checkout_date=checkout_date,
        booking__status=BookingStatus.CHECKED_IN,
        room_stays__0__checkin_date=checkin_date,
        room_stays__0__checkout_date=checkout_date,
        room_stays__0__status=BookingStatus.CHECKED_IN,
        room_stays__0__guest_stays__0__checkin_date=checkin_date,
        room_stays__0__guest_stays__0__checkout_date=checkout_date,
    )
    return booking, bill


@pytest.fixture
def past_booking_and_bill_not_checked_in_2(client, booking_repo, bill_repo):
    checkin_date = today_minus_days(2)
    checkout_date = today_minus_days(1)
    create_booking_payload = create_new_booking_payload(checkin_date, checkout_date)
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate is not None, "A booking aggregate should be created"
    assert len(booking_aggregate.customers) == 2, "2 customers should be created"

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert bill_aggregate is not None, "A bill should be created"
    assert (
        len(bill_aggregate.billed_entities) >= 2
    ), "At least 1 Billed Entity should be created for each customer"

    return booking_aggregate, bill_aggregate


@pytest.fixture
def some_booking_and_bill_2(booking_and_bill):
    return booking_and_bill[0], booking_and_bill[1]


@pytest.fixture
def checked_in_booking_and_bill_pending_checkout_2(
    client,
    active_hotel_aggregate,
    new_booking_yesterday_payload,
    booking_repo,
    bill_repo,
):
    booking_id = make_booking(
        client, {"data": json.loads(new_booking_yesterday_payload)}
    )

    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version, checkin_datetime=today_minus_days(1)
    )
    checkin_booking(client, booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    return booking_aggregate, bill_aggregate


@pytest.fixture
def checked_in_booking_and_bill_pending_checkout_night_audit(
    client,
    active_hotel_aggregate,
    new_booking_day_before_payload,
    booking_repo,
    bill_repo,
    hotel_repo,
):
    # move hotel business date to 2 days from today (day before yesterday)
    active_hotel_aggregate.hotel.current_business_date = dateutils.subtract(
        dateutils.current_date(), days=2
    )
    hotel_repo.update(active_hotel_aggregate)
    db_engine.get_session().commit()
    # create booking for day before
    booking_id = make_booking(
        client, {"data": json.loads(new_booking_day_before_payload)}
    )

    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version, checkin_datetime=today_minus_days(1)
    )
    checkin_booking(client, booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    return booking_aggregate, bill_aggregate


@pytest.fixture
def checked_in_booking_and_bill_2(checked_in_booking_and_bill_pending_checkout):
    return checked_in_booking_and_bill_pending_checkout


@pytest.fixture
def week_old_checked_in_booking_and_bill_2(client, booking_repo, bill_repo):
    checkin_date = today_minus_days(7)
    checkout_date = today_minus_days(6)
    create_booking_payload = create_new_booking_payload(checkin_date, checkout_date)
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate is not None, "A booking aggregate should be created"
    assert len(booking_aggregate.customers) == 2, "2 customers should be created"

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert bill_aggregate is not None, "A bill should be created"
    assert (
        len(bill_aggregate.billed_entities) >= 2
    ), "At least 1 Billed Entity should be created for each customer"

    return booking_aggregate, bill_aggregate


@pytest.fixture
def expense_request(request):
    guest_ids = (
        request.param.get('guest_ids', ["123"])
        if hasattr(request, 'param')
        else ["123"]
    )
    return {
        "data": {
            "type": "food",
            "room_stay_id": "1",
            "expense_item_id": "123",
            "assigned_to": guest_ids,
            "comments": "some expense",
            "price": {
                "pretax_amount": "INR 100",
                "applicable_date": dateutils.current_datetime().isoformat(),
                "bill_to_type": "company",
                "type": "credit",
            },
        }
    }


@pytest.fixture
def some_expense():
    return {
        "expense_item_id": '123',
        "room_stay_id": 1,
        "status": ExpenseStatus.CONSUMED,
        "comments": "Some comment",
        "guests": ["123"],
        "added_by": ExpenseAddedBy.HOTEL,
        "applicable_date": current_datetime(),
    }


@pytest.fixture
def old_checked_out_booking_and_bill():
    bill = BillFactory(bill__bill_id="BIL1")

    checkin = last_week()
    checkout = dateutils.add(checkin, days=2)
    guest_stay = GuestStayFactory(
        checkin_date=checkin, checkout_date=checkout, status=BookingStatus.CHECKED_OUT
    )
    room_stay = RoomStayFactory(
        checkin_date=checkin,
        checkout_date=checkout,
        status=BookingStatus.CHECKED_OUT,
        guest_stays=[guest_stay],
    )
    booking = BookingAggregateFactory(
        booking__checkin_date=checkin,
        booking__checkout_date=checkout,
        booking__status=BookingStatus.CHECKED_OUT,
        booking__bill_id=bill.bill.bill_id,
        room_stays=[room_stay],
    )

    return booking, bill


@pytest.fixture
def future_booking_and_bill():
    bill = BillFactory(bill__bill_id="BIL2")

    checkin = next_week()
    checkout = dateutils.add(checkin, days=2)
    guest_stay = GuestStayFactory(
        checkin_date=checkin, checkout_date=checkout, status=BookingStatus.RESERVED
    )
    room_stay = RoomStayFactory(
        checkin_date=checkin,
        checkout_date=checkout,
        status=BookingStatus.RESERVED,
        guest_stays=[guest_stay],
    )
    booking = BookingAggregateFactory(
        booking__checkin_date=checkin,
        booking__checkout_date=checkout,
        booking__status=BookingStatus.CHECKED_OUT,
        booking__bill_id=bill.bill.bill_id,
        room_stays=[room_stay],
    )

    return booking, bill


@pytest.fixture
def non_checked_out_room_stay():
    room_stay = RoomStayFactory(
        checkin_date=today(), checkout_date=tomorrow(), status=BookingStatus.RESERVED
    )
    return room_stay


@pytest.fixture
def checked_out_room_stay():
    room_stay = RoomStayFactory(
        checkin_date=today(),
        checkout_date=tomorrow(),
        status=BookingStatus.CHECKED_OUT,
        actual_checkout_date=tomorrow(),
    )
    return room_stay


@pytest.fixture
def past_room_stay():
    room_stay = RoomStayFactory(
        checkin_date=last_week(),
        checkout_date=yesterday(),
        status=BookingStatus.CHECKED_OUT,
        actual_checkout_date=yesterday(),
    )
    return room_stay


@pytest.fixture
def booking_with_different_checkin_checkout_times():
    checkout_datetime = current_datetime() + datetime.timedelta(hours=71)

    guest_stay = GuestStayFactory(checkout_date=checkout_datetime)
    room_stay = RoomStayFactory(
        checkout_date=checkout_datetime, guest_stays=[guest_stay]
    )

    booking_aggregate = BookingAggregateFactory(
        booking__checkout_date=checkout_datetime, room_stays=[room_stay]
    )
    return booking_aggregate


@pytest.fixture
def valid_booking_aggregate_with_one_room_stay():
    room_stay_id = 1
    room_type_id = 1
    guest_stays = [
        GuestStay(
            1,
            BookingStatus.RESERVED,
            AgeGroup.ADULT,
            today(),
            add(dateutils.current_datetime(), days=5),
        )
    ]
    charge_id_map = {
        dateutils.date_to_ymd_str(to_date(today())): 1,
        dateutils.date_to_ymd_str(
            to_date(add(dateutils.current_datetime(), days=1))
        ): 2,
        dateutils.date_to_ymd_str(
            to_date(add(dateutils.current_datetime(), days=2))
        ): 4,
        dateutils.date_to_ymd_str(
            to_date(add(dateutils.current_datetime(), days=3))
        ): 5,
        dateutils.date_to_ymd_str(
            to_date(add(dateutils.current_datetime(), days=4))
        ): 6,
    }
    room_stays = [
        RoomStay(
            room_stay_id=room_stay_id,
            room_type_id=room_type_id,
            type=RoomStayType.NIGHT,
            status=BookingStatus.RESERVED,
            checkin_date=today(),
            checkout_date=add(dateutils.current_datetime(), days=5),
            guest_stays=guest_stays,
            charge_id_map=charge_id_map,
            actual_checkin_date=None,
            actual_checkout_date=None,
            room_allocations=[],
        )
    ]

    booking_owner = Customer(
        customer_id=1,
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    booking = Booking(
        booking_id='123',
        hotel_id='1',
        type=BookingTypes.ROOM,
        checkin_date=today(),
        checkout_date=add(dateutils.current_datetime(), days=5),
        status=BookingStatus.CONFIRMED,
        source=BookingSource(
            channel_code="hotel", subchannel_code="walk-in", application_code="crs-web"
        ),
        seller_model=SellerType.MARKETPLACE,
        owner_id=booking_owner.customer_id,
        hold_till=None,
        version=1,
        comments=None,
        bill_id=1,
        reference_number=None,
        extra_information=None,
    )
    customers = [booking_owner]
    booking_aggregate = BookingAggregate(booking, room_stays, customers=customers)
    booking_aggregate.check_invariance()
    return booking_aggregate


@pytest.fixture
def create_booking_payload(request, active_hotel_aggregate):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    number_of_guest_stays = (
        request.param.get('occupancy', 1) if hasattr(request, 'param') else 1
    )
    checkin_date = (
        request.param.get('checkin_date', dateutils.current_datetime())
        if hasattr(request, 'param')
        else dateutils.current_datetime()
    )
    if not checkin_date:
        checkin_date = dateutils.current_datetime()

    checkin_date = dateutils.datetime_at_given_time(
        checkin_date, hotel_context.checkin_time
    )

    checkout_date = (
        request.param.get('checkout_date', dateutils.add(checkin_date, days=1))
        if hasattr(request, 'param')
        else dateutils.add(checkin_date, days=1)
    )

    checkout_date = dateutils.datetime_at_given_time(
        checkout_date, hotel_context.checkout_time
    )
    payload = create_new_booking_payload(
        checkin_date, checkout_date, number_of_guest_stays=number_of_guest_stays
    )

    yield json.dumps(payload)

    crs_context.clear()


@pytest.fixture
def create_temp_booking_payload(active_hotel_aggregate):
    hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime(), hotel_context.checkin_time
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=1),
        hotel_context.checkout_time,
    )
    payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        status=BookingStatus.TEMPORARY,
        with_payments=False,
        hold_till=checkout_date,
    )

    return json.dumps(payload)


@pytest.fixture
def create_booking_payload_with_hold_till(active_hotel_aggregate):
    hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime(), hotel_context.checkin_time
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=1),
        hotel_context.checkout_time,
    )
    payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        hold_till=checkout_date,
        status=None,
        with_payments=False,
    )
    return json.dumps(payload)


@pytest.fixture
def create_confirmed_booking_payload_with_hold_till(active_hotel_aggregate):
    hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime(), hotel_context.checkin_time
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=1),
        hotel_context.checkout_time,
    )
    payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        status=None,
        with_payments=False,
        hold_till=checkout_date,
    )
    return json.dumps(payload)


@pytest.fixture
def create_booking_payload_with_two_room_stays(active_hotel_aggregate):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime(), hotel_context.checkin_time
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=1),
        hotel_context.checkout_time,
    )
    payload = create_new_booking_payload(checkin_date, checkout_date, number_of_rooms=2)

    return json.dumps(payload)


@pytest.fixture
def create_past_booking_payload_with_two_room_stays(active_hotel_aggregate):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=-1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=0),
        hotel_context.checkout_time,
    )
    payload = create_new_booking_payload(checkin_date, checkout_date, number_of_rooms=2)
    return json.dumps(payload)


@pytest.fixture
def create_past_booking_payload_with_two_room_stays_with_payment_mode_as_rp(
    active_hotel_aggregate, payment_mode="razorpay_api"
):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=-1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=0),
        hotel_context.checkout_time,
    )
    payload = create_new_booking_payload(
        checkin_date, checkout_date, number_of_rooms=2, payment_mode=payment_mode
    )
    return json.dumps(payload)


@pytest.fixture
def new_booking_yesterday_payload(active_hotel_aggregate):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() - datetime.timedelta(days=1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime(), hotel_context.checkout_time
    )

    payload = create_new_booking_payload(checkin_date, checkout_date)

    return json.dumps(payload)


@pytest.fixture
def new_booking_day_before_payload(active_hotel_aggregate):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() - datetime.timedelta(days=2),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() - datetime.timedelta(days=1),
        hotel_context.checkin_time,
    )

    payload = create_new_booking_payload(checkin_date, checkout_date)

    return json.dumps(payload)


@pytest.fixture
def new_booking_yesterday_payload_with_2_guest_stays(active_hotel_aggregate):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() - datetime.timedelta(days=1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime(), hotel_context.checkout_time
    )

    payload = create_new_booking_payload(
        checkin_date, checkout_date, number_of_guest_stays=2
    )

    return json.dumps(payload)


@pytest.fixture
def new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room(
    active_hotel_aggregate,
):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() - datetime.timedelta(days=1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime(), hotel_context.checkout_time
    )

    payload = create_new_booking_payload(
        checkin_date, checkout_date, number_of_guest_stays=2, number_of_rooms=2
    )

    return json.dumps(payload)


@pytest.fixture
def new_booking_yesterday_payload_with_2_room_stays(active_hotel_aggregate):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() - datetime.timedelta(days=1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime(), hotel_context.checkout_time
    )

    payload = create_new_booking_payload(checkin_date, checkout_date, number_of_rooms=2)

    return json.dumps(payload)


@pytest.fixture
def new_booking_yesterday_payload_with_2_room_stays_2_guest_stays(
    active_hotel_aggregate,
):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() - datetime.timedelta(days=1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime(), hotel_context.checkout_time
    )

    payload = create_new_booking_payload(
        checkin_date, checkout_date, number_of_rooms=2, number_of_guest_stays=2
    )

    return json.dumps(payload)


@pytest.fixture
def two_day_booking_payload(active_hotel_aggregate):
    hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)

    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() - datetime.timedelta(days=1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=1),
        hotel_context.checkout_time,
    )

    payload = create_new_booking_payload(checkin_date, checkout_date)

    return json.dumps(payload)


@pytest.fixture
def booking_and_bill(
    create_booking_payload, room_type_inventory_repo, client, booking_repo, bill_repo
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate is not None, "A booking aggregate should be created"
    assert len(booking_aggregate.room_stays) == 1, "A room stay should be created"
    assert (
        len(booking_aggregate.room_stays[0].guest_stays) == 1
    ), "A guest stay should be created"
    assert (
        booking_aggregate.booking.status == BookingStatus.CONFIRMED
    ), "Booking should be in confirmed state"
    assert len(booking_aggregate.customers) == 2, "2 customers should be created"

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert bill_aggregate is not None, "A bill should be created"
    assert len(bill_aggregate.charges) == 1, "Bill should have a charge"

    return booking_aggregate, bill_aggregate


@pytest.fixture
def booking_and_bill_with_primary_guest_default_billed_entity_category(
    create_booking_payload, room_type_inventory_repo, client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        set_travel_agent_details=True,
    )
    booking_id = make_booking(client, {"data": create_booking_payload})
    assert booking_id is not None

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate is not None, "A booking aggregate should be created"
    assert len(booking_aggregate.room_stays) == 2, "two room stays should be created"
    assert (
        len(booking_aggregate.room_stays[0].guest_stays) == 2
    ), "two guest stays should be created"
    assert (
        booking_aggregate.booking.status == BookingStatus.CONFIRMED
    ), "Booking should be in confirmed state"
    assert len(booking_aggregate.customers) == 5, "5 customers should be created"

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert bill_aggregate is not None, "A bill should be created"
    assert len(bill_aggregate.charges) == 4, "Bill should have a charge"
    assert (
        booking_aggregate.get_default_billed_entity_category()
        == BilledEntityCategory.PRIMARY_GUEST
    )

    return booking_aggregate, bill_aggregate


@pytest.fixture
def booking_and_bill_with_travel_agent_default_billed_entity_category(
    create_booking_payload, room_type_inventory_repo, client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        set_travel_agent_details=True,
    )
    booking_id = make_booking(client, {"data": create_booking_payload})
    assert booking_id is not None

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate is not None, "A booking aggregate should be created"
    assert len(booking_aggregate.room_stays) == 2, "two room stays should be created"
    assert (
        len(booking_aggregate.room_stays[0].guest_stays) == 2
    ), "two guest stays should be created"
    assert (
        booking_aggregate.booking.status == BookingStatus.CONFIRMED
    ), "Booking should be in confirmed state"
    assert len(booking_aggregate.customers) == 5, "5 customers should be created"

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert bill_aggregate is not None, "A bill should be created"
    assert len(bill_aggregate.charges) == 4, "Bill should have a charge"
    assert (
        booking_aggregate.get_default_billed_entity_category()
        == BilledEntityCategory.TRAVEL_AGENT
    )

    return booking_aggregate, bill_aggregate


@pytest.fixture
def booking_and_bill_with_primary_guest_as_def_be(
    create_booking_payload, room_type_inventory_repo, client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        channel='direct',
    )
    del create_booking_payload['booking_owner']['gst_details']
    booking_id = make_booking(client, {"data": create_booking_payload})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    return booking_aggregate, bill_aggregate


@pytest.fixture
def create_cash_register_and_session(client, active_hotel_aggregate):
    vendor_id = active_hotel_aggregate.hotel_id
    cash_register_name = 'test_cash_register'
    new_cash_register_url = 'v1/cashier/cash-registers'
    new_cash_register = dict(
        data=dict(
            cash_register_name=cash_register_name,
            default_opening_balance="0 INR",
            vendor_id=vendor_id,
        )
    )
    cash_register_header = {
        "X-User-Type": "super-admin",
        "X-Hotel-Id": vendor_id,
        "X-User": "backend-system",
    }
    with mock_role_manager():
        cash_register_response = client.post(
            new_cash_register_url,
            data=json.dumps(new_cash_register),
            content_type='application/json',
            headers=cash_register_header,
        )
        assert cash_register_response.status_code == 200
        cash_register_id = cash_register_response.json['data']['cash_register_id']

        new_cashier_session_url = (
            'v1/cashier/cash-registers/' + cash_register_id + '/cashier-sessions'
        )
        new_cashier_session = dict(
            data=dict(
                opening_balance="0 INR",
                opening_balance_in_base_currency="0 INR",
                vendor_id=vendor_id,
            )
        )

        cashier_session_response = client.post(
            new_cashier_session_url,
            data=json.dumps(new_cashier_session),
            content_type='application/json',
            headers=cash_register_header,
        )
        assert cashier_session_response.status_code == 200

    cashier_session_data = cashier_session_response.json['data']
    return cashier_session_data.get('cash_register_id'), cashier_session_data.get(
        'cashier_session_id'
    )


@pytest.fixture
def create_add_room_stay_payload():
    return {
        "data": {
            "checkin_date": dateutils.current_datetime().isoformat(),
            "checkout_date": (
                dateutils.current_datetime() + datetime.timedelta(days=1)
            ).isoformat(),
            "guest_stays": [
                {
                    "age_group": "adult",
                    "guest": {
                        "address": {
                            "city": "string",
                            "country": "string",
                            "field_1": "string",
                            "field_2": "string",
                            "pincode": "string",
                            "state": "string",
                        },
                        "email": "<EMAIL>",
                        "first_name": "guest 1",
                        "phone": {"country_code": "ccode", "number": "phone"},
                        "profile_type": "individual",
                        "reference_id": "guest ref 1",
                    },
                },
                {
                    "age_group": "adult",
                    "guest": {
                        "address": {
                            "city": "string",
                            "country": "string",
                            "field_1": "string",
                            "field_2": "string",
                            "pincode": "string",
                            "state": "string",
                        },
                        "email": "<EMAIL>",
                        "first_name": "guest 2",
                        "phone": {"country_code": "ccode", "number": "phone"},
                        "profile_type": "individual",
                        "reference_id": "guest ref 1",
                    },
                },
            ],
            "prices": [
                {
                    "applicable_date": dateutils.current_datetime().isoformat(),
                    "bill_to_type": "company",
                    "posttax_amount": 118,
                    "type": "non-credit",
                }
            ],
            "room_type_id": "rt01",
        },
        "resource_version": 1,
    }


@pytest.fixture
def reverse_checkout_after_booking_and_checkout(
    client, create_booking_payload, booking_repo, hotel_repo, active_hotel_aggregate
):
    # Create & Check-In Booking
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    # Preview Invoice & Check-Out
    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    action_id = checkout_booking(client, booking_id, checkout_payload)

    undo_checkout_booking(client, booking_id, action_id)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_IN

    return booking_aggregate, invoice_group_id, action_id


@pytest.fixture
def locked_invoices(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_id, checkout_payload)
    booking_invoice_group_aggregate = booking_invoice_group_repo.load(invoice_group_id)
    invoice_ids = booking_invoice_group_aggregate.booking_invoice_group.invoice_ids

    # commented perform night audit
    night_audit_service = locate_instance(NightAuditService)
    seller_aggregates = seller_repo.load_for_hotel_id(
        hotel_id=booking_aggregate.hotel_id
    )
    hotel_aggregate = hotel_repo.load_for_update(booking_aggregate.hotel_id)
    night_audit_service.schedule_night_audit(booking_aggregate.hotel_id)
    with mock_catalog_client(), mock_trial_balance_reporting_service():
        with mock_communication_service_client(), mock_authz_service_client(), mock_authn_service_client():
            night_audit_service._post_past_charges_and_payments_and_freeze_invoices(
                hotel_aggregate, seller_aggregates
            )

    assert invoice_repo.load(invoice_ids[0]).invoice.status == InvoiceStatus.LOCKED
    invoice_aggregates = invoice_repo.load_all(invoice_ids)
    return invoice_aggregates


@pytest.fixture
def reseller_locked_invoices(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
):
    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload['booking_owner']['profile_type'] = 'sme'
    booking_id = make_booking(client, {"data": create_booking_payload})
    return create_locked_invoices(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        hotel_repo,
        invoice_repo,
        seller_repo,
    )
