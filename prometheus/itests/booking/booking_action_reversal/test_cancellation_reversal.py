import json
from datetime import timed<PERSON><PERSON>
from itertools import chain

import pytest
from treebo_commons.utils import dateutils

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    cancel_booking,
    make_booking,
)
from prometheus.itests.billing.test_payment_api import create_cash_register_and_session
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    room_stay_cancellation_request,
)
from prometheus.itests.payload_generators.booking_payload_generators import id_generator
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_tax_calculator_service,
    mock_tenant_config,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    CashRegisterNames,
    ChargeStatus,
    ChargeTypes,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import (
    ActionStatus,
    BookingActions,
    BookingStatus,
)
from ths_common.constants.catalog_constants import SellerType


def cancellation_request(booking_version):
    return {
        "data": {
            "action_type": "cancel",
            "payload": {"cancel": {"cancellation_reason": "h"}},
        },
        "resource_version": booking_version,
    }


def cancellation_partial_booking_request(booking_version, room_stay_id):
    return {
        "data": {
            "cancellation_reason": "Marking cancel room",
            "room_stays": [{"room_stay_id": str(room_stay_id)}],
        },
        "resource_version": booking_version,
    }


def no_show_partial_booking_request(booking_version, room_stay_id):
    return {
        "data": {
            "noshow_reason": "Marking room show",
            "room_stays": [{"room_stay_id": str(room_stay_id)}],
        },
        "resource_version": booking_version,
    }


def test_booking_cancellation_reversal_should_uncancel_booking_bill_inventory(
    rt01_inventory,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    booking_action_repo,
    all_days_addon_request,
    booking_audit_trail_repo,
):
    inventory_count = rt01_inventory.room_type_inventory_availabilities[0].actual_count
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    active_charges_before_cancellation = bill_aggregate.active_charges

    # Create addon
    url = "v1/bookings/" + booking_aggregate.booking.booking_id + "/addons"
    response = client.post(
        url,
        data=all_days_addon_request,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200

    booking_aggregate = booking_repo.load(booking_id)
    cancel_payload = cancellation_request(booking_aggregate.booking.version)
    action_id = cancel_booking(client, booking_id, cancel_payload)

    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        url = f"v1/bookings/{booking_id}/actions/{action_id}"
        cancel_reponse = client.delete(
            url,
            content_type="application/json",
            headers={"X-User-Type": "super-admin", 'X-User': 'someone'},
        )
        assert cancel_reponse.status_code == 200

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CONFIRMED
    assert booking_aggregate.booking.cancellation_datetime is None
    assert all(
        rs.status == BookingStatus.RESERVED for rs in booking_aggregate.room_stays
    )
    guest_stays = chain(*[rs.guest_stays for rs in booking_aggregate.room_stays])
    assert all(gs.status == BookingStatus.RESERVED for gs in guest_stays)
    assert booking_aggregate.booking.cancellation_reason is None

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert all(
        charge.status == ChargeStatus.CREATED
        for charge in bill_aggregate.charges
        if charge.charge_id in active_charges_before_cancellation
    )

    booking_action_aggregates = booking_action_repo.load_all_actions_for_booking(
        booking_id
    )
    assert len(booking_action_aggregates) == 1
    assert (
        booking_action_aggregates[0].booking_action.action_type == BookingActions.CANCEL
    )
    assert booking_action_aggregates[0].booking_action.status == ActionStatus.REVERSED
    room_type_inventories = room_type_inventory_repo.load_multiple(
        booking_aggregate.booking.hotel_id,
        dateutils.to_date(booking_aggregate.booking.checkin_date),
        dateutils.to_date(booking_aggregate.booking.checkout_date),
    )
    assert (
        room_type_inventories[0].room_type_inventory_availabilities[0].actual_count
        == inventory_count - 1
    )
    audit_trail = booking_audit_trail_repo.load_for_booking(booking_id)
    undo_cancel_trail = [
        audit
        for audit in audit_trail
        if audit.audit_trail.audit_type == AuditType.BOOKING_CANCELLATION_REVERSED
    ]
    assert (
        len(undo_cancel_trail) == 1
    ), "There should be exactly one audit trail event for cancellation reversal"


@pytest.fixture
def create_booking_with_2_room_stays_payload():
    payload = {
        "booking_owner": {
            "address": {
                "city": "string",
                "country": "string",
                "field_1": "string",
                "field_2": "string",
                "pincode": "string",
                "state": "string",
            },
            "email": "<EMAIL>",
            "gst_details": {
                "address": {
                    "city": "string",
                    "country": "string",
                    "field_1": "string",
                    "field_2": "string",
                    "pincode": "string",
                    "state": "string",
                },
                "gstin_num": "string",
                "legal_name": "string",
            },
            "id_proof": {
                "id_kyc_url": "id_kyc_test",
                "id_number": "id_number_test",
                "id_proof_country_code": "id_proof_country_code_test",
                "id_proof_type": "driving_license",
            },
            "first_name": "string",
            "phone": {"country_code": "91", "number": "1232345"},
            "profile_type": "individual",
            "reference_id": "string",
        },
        "comments": "string",
        "extra_information": {},
        "guests": [],
        "hotel_id": "0016932",
        "reference_number": id_generator(),
        "room_stays": [
            {
                "checkin_date": dateutils.current_datetime().isoformat(),
                "checkout_date": (
                    dateutils.current_datetime() + timedelta(days=1)
                ).isoformat(),
                "guest_stays": [
                    {
                        "age_group": "adult",
                        "guest": {
                            "address": {
                                "city": "string",
                                "country": "string",
                                "field_1": "string",
                                "field_2": "string",
                                "pincode": "string",
                                "state": "string",
                            },
                            "email": "<EMAIL>",
                            "first_name": "guest 1",
                            "phone": {"country_code": "ccode", "number": "phone"},
                            "profile_type": "individual",
                            "reference_id": "guest ref 1",
                        },
                    },
                    {
                        "age_group": "adult",
                        "guest": {
                            "address": {
                                "city": "string",
                                "country": "string",
                                "field_1": "string",
                                "field_2": "string",
                                "pincode": "string",
                                "state": "string",
                            },
                            "email": "<EMAIL>",
                            "first_name": "guest 1",
                            "phone": {"country_code": "ccode", "number": "phone"},
                            "profile_type": "individual",
                            "reference_id": "guest ref 1",
                        },
                    },
                ],
                "prices": [
                    {
                        "applicable_date": dateutils.current_datetime().isoformat(),
                        "bill_to_type": "company",
                        "posttax_amount": 118,
                        "type": "non-credit",
                    }
                ],
                "room_type_id": "rt01",
            },
            {
                "checkin_date": dateutils.current_datetime().isoformat(),
                "checkout_date": (
                    dateutils.current_datetime() + timedelta(days=1)
                ).isoformat(),
                "guest_stays": [
                    {
                        "age_group": "adult",
                        "guest": {
                            "address": {
                                "city": "string",
                                "country": "string",
                                "field_1": "string",
                                "field_2": "string",
                                "pincode": "string",
                                "state": "string",
                            },
                            "email": "<EMAIL>",
                            "first_name": "guest 1",
                            "phone": {"country_code": "ccode", "number": "phone"},
                            "profile_type": "individual",
                            "reference_id": "guest ref 1",
                        },
                    },
                    {
                        "age_group": "adult",
                        "guest": {
                            "address": {
                                "city": "string",
                                "country": "string",
                                "field_1": "string",
                                "field_2": "string",
                                "pincode": "string",
                                "state": "string",
                            },
                            "email": "<EMAIL>",
                            "first_name": "guest 1",
                            "phone": {"country_code": "ccode", "number": "phone"},
                            "profile_type": "individual",
                            "reference_id": "guest ref 1",
                        },
                    },
                ],
                "prices": [
                    {
                        "applicable_date": dateutils.current_datetime().isoformat(),
                        "bill_to_type": "company",
                        "posttax_amount": 118,
                        "type": "non-credit",
                    }
                ],
                "room_type_id": "rt01",
            },
        ],
        "source": {
            "application_code": "string",
            "channel_code": "b2b",
            "subchannel_code": "string",
        },
        "status": "confirmed",
    }
    return payload


def make_pending_payment(client, bill_aggregate, booking_aggregate):
    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        # Fdm only add payment for paid to hotel

        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        default_billed_entity = bill_aggregate.get_billed_entity_for_category(
            booking_aggregate.get_default_billed_entity_category()
        )
        net_balance = abs(
            bill_aggregate.get_net_balance(
                default_billed_entity.get_account_for_new_assignment(
                    ChargeTypes.NON_CREDIT
                )
            )
        )
        new_payment = dict(
            data=dict(
                amount=str(net_balance),
                paid_by=PaymentReceiverTypes.HOTEL,
                paid_to=PaymentReceiverTypes.GUEST,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=booking_aggregate.booking.version,
        )
        create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200


def test_cancellation_of_partially_cancelled_booking_reversal_should_not_uncancelled_already_cancelled_room_stay(
    rt01_inventory,
    create_booking_with_2_room_stays_payload,
    client,
    booking_repo,
    bill_repo,
    booking_action_repo,
    room_type_inventory_repo,
    cancellation_item,
):
    inventory_count = rt01_inventory.room_type_inventory_availabilities[0].actual_count

    bookings_response = make_booking(
        client,
        {"data": create_booking_with_2_room_stays_payload},
        return_complete_response=True,
    )
    assert len(bookings_response["data"]["room_stays"]) > 0
    booking_id = bookings_response["data"]["booking_id"]
    booking_version = bookings_response["data"]["version"]

    booking_aggregate = booking_repo.load(booking_id)
    room_type_inventories = room_type_inventory_repo.load_multiple(
        booking_aggregate.booking.hotel_id,
        dateutils.to_date(booking_aggregate.booking.checkin_date),
        dateutils.to_date(booking_aggregate.booking.checkout_date),
    )
    assert (
        room_type_inventories[0].room_type_inventory_availabilities[0].actual_count
        == inventory_count - 2
    )
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    active_charges_before_cancellation = [
        charge.charge_id
        for charge in bill_aggregate.charges
        if not charge.status == ChargeStatus.CANCELLED
    ]

    # cancel a room_stay
    room_stay_cancel_payload = room_stay_cancellation_request(booking_version)
    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        url = f"v1/bookings/{booking_id}/actions"
        payload = json.dumps(room_stay_cancel_payload)
        cancel_response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert cancel_response.status_code == 200

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CONFIRMED
    assert booking_aggregate.get_room_stay(1).status == BookingStatus.CANCELLED
    assert booking_aggregate.get_room_stay(2).status == BookingStatus.RESERVED

    assert all(
        gs.status == BookingStatus.CANCELLED
        for gs in booking_aggregate.get_room_stay(1).guest_stays
    )

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    already_cancelled_charge_ids = [
        charge.charge_id
        for charge in bill_aggregate.charges
        if charge.status == ChargeStatus.CANCELLED
    ]

    booking_action_aggregates = booking_action_repo.load_all_actions_for_booking(
        booking_id
    )
    assert len(booking_action_aggregates) == 1
    assert (
        booking_action_aggregates[0].booking_action.action_type == BookingActions.CANCEL
    )
    assert booking_action_aggregates[0].booking_action.side_effects

    room_type_inventories = room_type_inventory_repo.load_multiple(
        booking_aggregate.booking.hotel_id,
        dateutils.to_date(booking_aggregate.booking.checkin_date),
        dateutils.to_date(booking_aggregate.booking.checkout_date),
    )
    assert (
        room_type_inventories[0].room_type_inventory_availabilities[0].actual_count
        == inventory_count - 1
    )

    # make pending payments, cancel room stay incurred a cancellation charge.
    # before booking cancellation this need to be paid
    make_pending_payment(client, bill_aggregate, booking_aggregate)

    # cancel booking
    cancel_payload = cancellation_request(booking_aggregate.booking.version)

    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        url = f"v1/bookings/{booking_id}/actions"
        payload = json.dumps(cancel_payload)
        cancel_response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert cancel_response.status_code == 200

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CANCELLED
    assert booking_aggregate.booking.cancellation_datetime is not None
    assert all(
        rs.status == BookingStatus.CANCELLED for rs in booking_aggregate.room_stays
    )
    guest_stays = chain(*[rs.guest_stays for rs in booking_aggregate.room_stays])
    assert all(gs.status == BookingStatus.CANCELLED for gs in guest_stays)
    assert booking_aggregate.booking.cancellation_reason == "h"

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert all(
        charge.status == ChargeStatus.CANCELLED
        for charge in bill_aggregate.charges
        if charge.charge_id in active_charges_before_cancellation
    )

    booking_action_aggregates = booking_action_repo.load_all_actions_for_booking(
        booking_id
    )
    assert len(booking_action_aggregates) == 2

    room_type_inventories = room_type_inventory_repo.load_multiple(
        booking_aggregate.booking.hotel_id,
        dateutils.to_date(booking_aggregate.booking.checkin_date),
        dateutils.to_date(booking_aggregate.booking.checkout_date),
    )
    assert (
        room_type_inventories[0].room_type_inventory_availabilities[0].actual_count
        == inventory_count
    )

    action_id = cancel_response.json["data"]["action_id"]

    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        url = f"v1/bookings/{booking_id}/actions/{action_id}"
        cancel_reponse = client.delete(
            url, content_type="application/json", headers={"X-User-Type": "super-admin"}
        )
        assert cancel_reponse.status_code == 200

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CONFIRMED
    assert booking_aggregate.booking.cancellation_datetime is None
    assert booking_aggregate.get_room_stay(1).status == BookingStatus.CANCELLED
    assert booking_aggregate.get_room_stay(2).status == BookingStatus.RESERVED

    assert all(
        gs.status == BookingStatus.RESERVED
        for gs in booking_aggregate.get_room_stay(2).guest_stays
    )
    assert all(
        gs.status == BookingStatus.CANCELLED
        for gs in booking_aggregate.get_room_stay(1).guest_stays
    )

    assert booking_aggregate.booking.cancellation_reason is None

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    for charge_id in already_cancelled_charge_ids:
        assert bill_aggregate.get_charge(charge_id).status == ChargeStatus.CANCELLED

    booking_action_aggregates = booking_action_repo.load_all_actions_for_booking(
        booking_id
    )
    assert booking_action_aggregates[0].booking_action.status == ActionStatus.REVERSED
    room_type_inventories = room_type_inventory_repo.load_multiple(
        booking_aggregate.booking.hotel_id,
        dateutils.to_date(booking_aggregate.booking.checkin_date),
        dateutils.to_date(booking_aggregate.booking.checkout_date),
    )
    assert (
        room_type_inventories[0].room_type_inventory_availabilities[0].actual_count
        == inventory_count - 1
    )


def test_booking_cancellation_reversal_should_not_create_an_overbooking_if_inventory_not_available(
    rt01_inventory,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    booking_repo,
    cancellation_item,
):
    rt01_inventory.get_availability_for_date(dateutils.current_date()).update_count(1)
    room_type_inventory_repo.update(rt01_inventory)

    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})

    booking_aggregate = booking_repo.load(booking_id)
    room_type_inventories = room_type_inventory_repo.load_multiple(
        booking_aggregate.booking.hotel_id,
        dateutils.to_date(booking_aggregate.booking.checkin_date),
        dateutils.to_date(booking_aggregate.booking.checkout_date),
        room_type_ids=['rt01'],
    )
    assert (
        room_type_inventories[0].room_type_inventory_availabilities[0].actual_count == 0
    )

    booking_aggregate = booking_repo.load(booking_id)
    cancel_payload = cancellation_request(booking_aggregate.booking.version)

    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        url = f"v1/bookings/{booking_id}/actions"
        payload = json.dumps(cancel_payload)
        cancel_response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert cancel_response.status_code == 200

    booking_aggregate = booking_repo.load(booking_id)

    room_type_inventories = room_type_inventory_repo.load_multiple(
        booking_aggregate.booking.hotel_id,
        dateutils.to_date(booking_aggregate.booking.checkin_date),
        dateutils.to_date(booking_aggregate.booking.checkout_date),
    )
    assert (
        room_type_inventories[0].room_type_inventory_availabilities[0].actual_count == 1
    )
    room_type_inventories[0].room_type_inventory_availabilities[0].actual_count = 0
    room_type_inventory_repo.update(room_type_inventories[0])

    action_id = cancel_response.json["data"]["action_id"]

    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        url = f"v1/bookings/{booking_id}/actions/{action_id}"
        undo_cancel_response = client.delete(
            url, content_type="application/json", headers={"X-User-Type": "super-admin"}
        )
        assert undo_cancel_response.status_code == 400

    room_type_inventories = room_type_inventory_repo.load_multiple(
        booking_aggregate.booking.hotel_id,
        dateutils.to_date(booking_aggregate.booking.checkin_date),
        dateutils.to_date(booking_aggregate.booking.checkout_date),
    )
    assert (
        room_type_inventories[0].room_type_inventory_availabilities[0].actual_count > 0
    ), "An overbooking should not be created"


def test_booking_cancellation_reversal_should_reinstance_all_charges_in_same_account(
    create_booking_payload, client, booking_repo, bill_repo, rate_plan_addon
):
    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload['room_stays'][0]['prices'][0]['type'] = 'credit'
    booking_id = make_booking(client, {"data": create_booking_payload})

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    active_charges_before_cancellation = bill_aggregate.active_charges

    rate_plan_addon['start_date'] = dateutils.isoformat_datetime(
        booking_aggregate.get_room_stay(1).checkin_date
    )
    rate_plan_addon['end_date'] = dateutils.isoformat_datetime(
        booking_aggregate.get_room_stay(1).checkin_date
    )

    # Create addon
    url = "v2/bookings/" + booking_aggregate.booking.booking_id + "/addons"
    response = client.post(
        url,
        data=json.dumps(dict(data=rate_plan_addon)),
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    charge_id_map = booking_aggregate.room_stays[0].charge_id_map
    charge_with_inclusion = charge_id_map.get(
        dateutils.date_to_ymd_str(booking_aggregate.room_stays[0].checkin_date)
    )

    charge_ids_to_check = [charge_with_inclusion]
    charge = bill_aggregate.get_charge(charge_with_inclusion)
    # Precondition checks.
    charge_ids_to_check.extend(charge.addon_charge_ids)
    assert len(charge.addon_charge_ids) == 2
    assert all(
        c.status == ChargeStatus.CREATED
        for c in bill_aggregate.get_charges(charge_ids_to_check)
    )

    cancel_payload = cancellation_request(booking_aggregate.booking.version)
    action_id = cancel_booking(client, booking_id, cancel_payload)

    # Validate statuses after cancellation
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CANCELLED
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert all(
        c.status == ChargeStatus.CANCELLED
        for c in bill_aggregate.get_charges(charge_ids_to_check)
    )
    credit_charges = [
        c
        for c in bill_aggregate.charges
        if c.type == ChargeTypes.CREDIT and c.status == ChargeStatus.CANCELLED
    ]

    assert len(credit_charges) == 3
    for charge in credit_charges:
        for split in charge.charge_splits:
            assert not split.payment_id

    old_billed_entity_account = credit_charges[0].charge_splits[0].billed_entity_account

    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        url = f"v1/bookings/{booking_id}/actions/{action_id}"
        cancel_reponse = client.delete(
            url,
            content_type="application/json",
            headers={"X-User-Type": "super-admin", 'X-User': 'someone'},
        )
        assert cancel_reponse.status_code == 200

    # Validate statuses after reverse cancellation
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CONFIRMED
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert all(
        c.status == ChargeStatus.CREATED
        for c in bill_aggregate.get_charges(charge_ids_to_check)
    )

    credit_charges = [
        c
        for c in bill_aggregate.charges
        if c.type == ChargeTypes.CREDIT and c.status == ChargeStatus.CREATED
    ]
    assert len(credit_charges) == 3

    new_billed_entity_account = credit_charges[0].charge_splits[0].billed_entity_account

    assert old_billed_entity_account != new_billed_entity_account
