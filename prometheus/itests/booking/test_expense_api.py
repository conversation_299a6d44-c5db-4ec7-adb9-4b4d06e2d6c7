import json
from decimal import Decimal

import pytest
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import add, current_date

from object_registry import locate_instance
from prometheus.application.booking.command_handlers.add_expense import (
    AddExpenseCommandHandler,
)
from prometheus.application.decorators import session_manager
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    make_booking,
)
from prometheus.itests.api_wrappers.expense_wrappers import (
    add_expense,
    add_expenses_v2,
    add_expenses_v3,
)
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v2_request,
    create_expenses_v3_request,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    expense_request as create_expense_request,
)
from prometheus.tests.factories.aggregate_factories import (
    BillFactory,
    BookingAggregateFactory,
)
from prometheus.tests.factories.entity_factories import CustomerFactory
from prometheus.tests.mockers import (
    mock_rate_manager_client,
    mock_role_manager,
    mock_super_admin_user_headers,
    mock_tax_calculator_service,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    PaymentInstruction,
)
from ths_common.constants.booking_constants import BookingStatus, ExpenseAddedBy
from ths_common.exceptions import ValidationException
from ths_common.value_objects import PriceData, UserData


@pytest.fixture()
def add_expense_v1_command_handler():
    return locate_instance(AddExpenseCommandHandler)


@session_manager(commit=True)
def save_aggregates(booking_aggregate, bill_aggregate, booking_repo, bill_repo):
    booking_repo.save(booking_aggregate)
    bill_repo.save(bill_aggregate)


def test_add_expense_to_booking(
    client,
    booking_repo,
    active_hotel_aggregate,
    add_expense_v1_command_handler,
    checked_in_booking_and_bill_pending_checkout_2,
    expense_request,
    bill_repo,
    hotel_repo,
):
    user_data = UserData(user_type="super-admin")

    booking_aggregate, bill_aggregate = (
        checked_in_booking_and_bill_pending_checkout_2[0],
        checked_in_booking_and_bill_pending_checkout_2[1],
    )

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    expense_request = expense_request.copy()
    expense_request['data']['assigned_to'] = [
        booking_aggregate.room_stays[0].guest_stays[0].guest_allocation.guest_id
    ]

    # Remove this if you want to hit tax service
    with mock_super_admin_user_headers(), mock_role_manager():
        url = 'v1/bookings/' + booking_aggregate.booking.booking_id + '/expenses'
        expense_request['resource_version'] = booking_aggregate.booking.version
        headers = {'X-User-Type': 'super-admin'}
        response = client.post(
            url,
            json=expense_request,
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200
        json_data = json.loads(response.data.decode('utf-8'))
        saved_bill = bill_repo.load(bill_aggregate.bill.bill_id)
        charge = saved_bill.get_charge(json_data['data']['charge_id'])
        assert charge.status == ChargeStatus.CONSUMED
        assert len(charge.charge_splits) == 1
        assert sum([split.percentage for split in charge.charge_splits]) == Decimal(
            '100'
        )


def test_adding_expense_should_fail_if_not_assigned(
    booking_repo, add_expense_v1_command_handler, some_expense, bill_repo, expense_repo
):
    user_data = UserData(user_type="super-admin")
    bill_aggregate = BillFactory()
    for billed_entity in bill_aggregate.billed_entities:
        for account in billed_entity.accounts:
            bill_aggregate.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )
    bill_repo.save(bill_aggregate)

    checked_in_booking = BookingAggregateFactory(
        booking__bill_id=bill_aggregate.bill_id,
        booking__status=BookingStatus.CHECKED_IN,
    )
    checked_in_booking.customers.append(CustomerFactory(customer_id='cust2'))
    booking_repo.save(checked_in_booking)

    some_expense["guests"] = []

    price = PriceData(
        '10',
        None,
        some_expense['applicable_date'],
        bill_to_type=ChargeBillToTypes.COMPANY,
        type=ChargeTypes.CREDIT,
    )

    with pytest.raises(ValidationException):
        with mock_super_admin_user_headers(), mock_role_manager():
            add_expense_v1_command_handler.handle(
                checked_in_booking.booking.booking_id,
                None,
                some_expense,
                price,
                user_data,
            )


def test_adding_expense_to_with_future_applicable_date_should_fail(
    booking_repo, add_expense_v1_command_handler, some_expense, bill_repo
):
    user_data = UserData(user_type="super-admin")
    bill_aggregate = BillFactory()
    for billed_entity in bill_aggregate.billed_entities:
        for account in billed_entity.accounts:
            bill_aggregate.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )
    bill_repo.save(bill_aggregate)

    checked_in_booking = BookingAggregateFactory(
        booking__bill_id=bill_aggregate.bill_id,
        booking__status=BookingStatus.CHECKED_IN,
    )
    booking_repo.save(checked_in_booking)

    future_applicable_date = add(current_date(), days=1)
    some_expense['applicable_date'] = future_applicable_date
    price = PriceData(
        '10',
        None,
        some_expense['applicable_date'],
        bill_to_type=ChargeBillToTypes.COMPANY,
        type=ChargeTypes.CREDIT,
    )

    with pytest.raises(ValidationException):
        with mock_super_admin_user_headers(), mock_role_manager():
            add_expense_v1_command_handler.handle(
                checked_in_booking.booking.booking_id,
                None,
                some_expense,
                price,
                user_data,
            )


def test_add_and_get_expense(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    expense_request,
    booking_audit_trail_repo,
    hotel_repo,
):
    headers = {'X-User-Type': 'super-admin'}

    bill_aggregate = checked_in_booking_and_bill_pending_checkout_2[1]
    checked_in_booking_aggregate = checked_in_booking_and_bill_pending_checkout_2[0]

    booking = checked_in_booking_aggregate.booking

    expense_request = expense_request.copy()
    expense_request['data']['assigned_to'] = [
        checked_in_booking_aggregate.room_stays[0]
        .guest_stays[0]
        .guest_allocation.guest_id
    ]

    # Rollover business date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    # get all expenses when no expenses should return empty list
    get_url = 'v1/bookings/' + booking.booking_id + '/expenses'
    get_response = client.get(get_url, headers=headers)
    assert get_response.status_code == 200
    assert get_response.json['data'] == []

    # Remove this if you want to hit tax service
    with mock_role_manager(), mock_rate_manager_client():
        url = 'v1/bookings/' + booking.booking_id + '/expenses'
        expense_request['resource_version'] = booking.version
        response = client.post(
            url, json=expense_request, content_type='application/json', headers=headers
        )
        assert response.status_code == 200
        json_data = json.loads(response.data.decode('utf-8'))
        assert json_data['data']['charge_id'] is not None
        assert json_data['data']['expense_id'] is not None
        assert json_data['data']['status'] == 'consumed'
        assert json_data['resource_version'] == booking.version + 1

    expense_id = json_data['data']['expense_id']
    get_url = 'v1/bookings/' + booking.booking_id + '/expenses/' + str(expense_id)
    get_response = client.get(get_url, headers=headers)
    assert get_response.status_code == 200
    assert get_response.json['data']['charge_id'] is not None
    assert get_response.json['data']['expense_id'] is not None
    assert get_response.json['resource_version'] is not None

    # Get invalid expense should give 400
    expense_id = 100
    assert expense_id not in [
        e.expense_id for e in checked_in_booking_aggregate.expenses
    ]
    get_url = 'v1/bookings/' + booking.booking_id + '/expenses/' + str(expense_id)
    get_response = client.get(get_url, headers=headers)
    assert get_response.status_code == 404

    # get all expenses
    get_url = 'v1/bookings/' + booking.booking_id + '/expenses'
    get_response = client.get(get_url, headers=headers)
    assert get_response.status_code == 200
    assert len(get_response.json['data']) == 1
    assert get_response.json['data'][0]['expense_id'] is not None
    assert get_response.json['resource_version'] is not None


def test_add_expense_should_add_to_audit_trail(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    expense_request,
    booking_audit_trail_repo,
    hotel_repo,
):
    headers = {'X-User-Type': 'super-admin'}
    bill_aggregate = checked_in_booking_and_bill_pending_checkout_2[1]
    checked_in_booking_aggregate = checked_in_booking_and_bill_pending_checkout_2[0]

    booking = checked_in_booking_aggregate.booking

    expense_request = expense_request.copy()
    expense_request['data']['assigned_to'] = [
        checked_in_booking_aggregate.room_stays[0]
        .guest_stays[0]
        .guest_allocation.guest_id
    ]

    # Rollover business date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    # get all expenses when no expenses should return empty list
    get_url = 'v1/bookings/' + booking.booking_id + '/expenses'
    get_response = client.get(get_url, headers=headers)
    assert get_response.status_code == 200
    assert get_response.json['data'] == []

    # Remove this if you want to hit tax service
    with mock_role_manager(), mock_rate_manager_client():
        url = 'v1/bookings/' + booking.booking_id + '/expenses'
        expense_request['resource_version'] = booking.version
        response = client.post(
            url, json=expense_request, content_type='application/json', headers=headers
        )
        assert response.status_code == 200
        json_data = json.loads(response.data.decode('utf-8'))
        assert json_data['data']['charge_id'] is not None
        assert json_data['data']['expense_id'] is not None
        assert json_data['data']['status'] == 'consumed'
        assert json_data['resource_version'] == booking.version + 1

    audit_trail = booking_audit_trail_repo.load_for_booking(booking.booking_id)

    get_url = '/v1/bookings/' + booking.booking_id + '/audit-trail'
    get_response = client.get(get_url, headers=headers)
    assert get_response.status_code == 200
    assert get_response.json['data'][0]['audit_type'] == AuditType.CHARGE_ADDED.value

    domain_events = get_response.json['data'][0]['audit_payload']['domain_events']
    assert domain_events == audit_trail[0].audit_trail.audit_payload['domain_events']


def test_should_be_able_to_add_expense_without_mandatory_fields(
    client,
    active_hotel_aggregate,
    checked_in_booking_and_bill_pending_checkout_2,
    expense_request,
    hotel_repo,
):
    checked_in_booking_aggregate = checked_in_booking_and_bill_pending_checkout_2[0]
    expense_request = expense_request.copy()
    del expense_request['data']['comments']
    expense_request['data']['assigned_to'] = [
        checked_in_booking_aggregate.room_stays[0]
        .guest_stays[0]
        .guest_allocation.guest_id
    ]

    # Rollover business date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    # Remove this if you want to hit tax service
    with mock_role_manager(), mock_rate_manager_client():
        url = 'v1/bookings/' + checked_in_booking_aggregate.booking_id + '/expenses'
        expense_request[
            'resource_version'
        ] = checked_in_booking_aggregate.current_version()
        response = client.post(
            url,
            json=expense_request,
            content_type='application/json',
            headers={"X-User-Type": "cr-team"},
        )
        assert response.status_code == 200


def test_duplicate_guests_should_throw_error(
    booking_repo, bill_repo, client, expense_request
):
    expense_request = expense_request.copy()
    headers = {'X-User-Type': 'super-admin'}
    expense_request['data']['assigned_to'].append("123")
    bill_aggregate = BillFactory()
    for billed_entity in bill_aggregate.billed_entities:
        for account in billed_entity.accounts:
            bill_aggregate.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )
    checked_in_booking_aggregate = BookingAggregateFactory(
        booking__bill_id=bill_aggregate.bill_id,
        booking__status=BookingStatus.CHECKED_IN,
        with_guest_allocation=True,
    )

    save_aggregates(
        checked_in_booking_aggregate, bill_aggregate, booking_repo, bill_repo
    )

    booking = checked_in_booking_aggregate.booking

    expense_request['resource_version'] = booking.version
    url = 'v1/bookings/' + booking.booking_id + '/expenses'
    response = client.post(
        url, json=expense_request, content_type='application/json', headers=headers
    )
    assert response.status_code == 400
    assert response.json['errors'][0]['code'] == '********'


def test_should_be_able_to_add_expense_with_added_by_as_hotel(
    client, active_hotel_aggregate, create_booking_payload, booking_repo, hotel_repo
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    # Rollover business date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    expense_request = create_expense_request(
        booking_aggregate.booking.version, added_by=ExpenseAddedBy.HOTEL
    )
    add_expense(client, booking_id, expense_request)

    booking_aggregate = booking_repo.load(booking_id)
    assert len(booking_aggregate.expenses) == 1, "Expense should be added"
    assert booking_aggregate.expenses[0].added_by == ExpenseAddedBy.HOTEL


def test_add_expenses_v2(
    booking_repo, create_booking_payload, client, active_hotel_aggregate, hotel_repo
):
    headers = {'X-User-Type': 'super-admin'}
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    # Rollover business date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    expense_v2_request = create_expenses_v2_request()
    json_data = add_expenses_v2(client, booking_id, expense_v2_request)
    charge_ids = [expense['charge_id'] for expense in json_data['data']]
    # Asserting sku_id of both the expenses created are equal to the sku_id passed in the request
    assert (
        list(set([expense['sku_id'] for expense in json_data['data']]))[0]
        == expense_v2_request['data']['skus'][0]['sku_id']
    )
    assert (
        json_data['data'][0]['comments']
        == expense_v2_request['data']['skus'][0]['date_wise_prices'][0]['comments']
    )
    get_charges_url = '/v1/bills/' + booking_aggregate.bill_id + '/charges'
    charges_resp = client.get(get_charges_url, headers=headers)
    charge_map = {charge['charge_id']: charge for charge in charges_resp.json['data']}
    billing_instruction_map = {
        (
            bi['billed_entity_account']['billed_entity_id'],
            bi['billed_entity_account']['account_number'],
        ): bi
        for bi in expense_v2_request['data']['billing_instructions']
    }
    for charge_id in charge_ids:
        charge = charge_map.get(charge_id)
        assert charge is not None
        assert len(charge['charge_splits']) == len(
            expense_v2_request['data']['billing_instructions']
        )
        charge_split_billed_entity_accounts = [
            (
                cs['billed_entity_account']['billed_entity_id'],
                cs['billed_entity_account']['account_number'],
            )
            for cs in charge['charge_splits']
            if cs['billed_entity_account']
        ]
        assert (
            list(billing_instruction_map.keys()) == charge_split_billed_entity_accounts
        )
        for cs in charge['charge_splits']:
            billing_instruction = billing_instruction_map[
                (
                    cs['billed_entity_account']['billed_entity_id'],
                    cs['billed_entity_account']['account_number'],
                )
            ]
            charge_type = ChargeTypes.from_payment_instruction(
                PaymentInstruction(billing_instruction['payment_instruction'])
            )
            assert cs['charge_type'] == charge_type.value
            assert Decimal(billing_instruction['split_percentage']) == cs['percentage']


def test_add_expenses_v2_for_rate_plan_charge(
    booking_repo,
    create_booking_payload,
    client,
    active_hotel_aggregate,
    hotel_repo,
    bill_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    expense_v2_request = create_expenses_v2_request(
        for_rate_plan=True,
        dates=[booking_aggregate.booking.checkin_date],
        charge_to=["2"],
        quantity=1,
    )

    json_data = add_expenses_v2(client, booking_id, expense_v2_request)
    charge_ids = [expense['charge_id'] for expense in json_data['data']]

    assert len(charge_ids) == 1

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)

    rate_plan_expense_charge = bill_aggregate.get_charge(charge_ids[0])
    assert rate_plan_expense_charge.is_inclusion_charge

    room_stay = booking_aggregate.get_room_stay(1)
    charge_id = room_stay.charge_id_map.get(
        dateutils.date_to_ymd_str(booking_aggregate.booking.checkin_date)
    )
    room_charge = bill_aggregate.get_charge(charge_id)

    assert rate_plan_expense_charge.charge_id in room_charge.inclusion_charge_ids
    assert rate_plan_expense_charge.posttax_amount.amount == Decimal("123")


def test_add_expenses_v3(
    booking_repo, create_booking_payload, client, active_hotel_aggregate, hotel_repo
):
    headers = {'X-User-Type': 'super-admin'}
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    # Rollover business date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    expense_v3_request = create_expenses_v3_request()
    json_data = add_expenses_v3(client, booking_id, expense_v3_request)
    charge_ids = [expense['charge_id'] for expense in json_data['data']]
    # Asserting sku_id of both the expenses created are equal to the sku_id passed in the request
    assert (
        list(set([expense['sku_id'] for expense in json_data['data']]))[0]
        == expense_v3_request['data'][0]['skus'][0]['sku_id']
    )
    assert (
        json_data['data'][0]['comments']
        == expense_v3_request['data'][0]['skus'][0]['date_wise_prices'][0]['comments']
    )
    get_charges_url = '/v1/bills/' + booking_aggregate.bill_id + '/charges'
    charges_resp = client.get(get_charges_url, headers=headers)
    charge_map = {charge['charge_id']: charge for charge in charges_resp.json['data']}
    billing_instruction_map = {
        (
            bi['billed_entity_account']['billed_entity_id'],
            bi['billed_entity_account']['account_number'],
        ): bi
        for bi in expense_v3_request['data'][0]['billing_instructions']
    }
    for charge_id in charge_ids:
        charge = charge_map.get(charge_id)
        assert charge is not None
        assert len(charge['charge_splits']) == len(
            expense_v3_request['data'][0]['billing_instructions']
        )
        charge_split_billed_entity_accounts = [
            (
                cs['billed_entity_account']['billed_entity_id'],
                cs['billed_entity_account']['account_number'],
            )
            for cs in charge['charge_splits']
            if cs['billed_entity_account']
        ]
        assert (
            list(billing_instruction_map.keys()) == charge_split_billed_entity_accounts
        )
        for cs in charge['charge_splits']:
            billing_instruction = billing_instruction_map[
                (
                    cs['billed_entity_account']['billed_entity_id'],
                    cs['billed_entity_account']['account_number'],
                )
            ]
            charge_type = ChargeTypes.from_payment_instruction(
                PaymentInstruction(billing_instruction['payment_instruction'])
            )
            assert cs['charge_type'] == charge_type.value
            assert Decimal(billing_instruction['split_percentage']) == cs['percentage']


def test_add_expenses_v3_for_rate_plan_charge(
    booking_repo,
    create_booking_payload,
    client,
    active_hotel_aggregate,
    hotel_repo,
    bill_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    expense_v3_request = create_expenses_v3_request(
        for_rate_plan=True,
        dates=[booking_aggregate.booking.checkin_date],
        charge_to=["2"],
        quantity=1,
    )

    json_data = add_expenses_v3(client, booking_id, expense_v3_request)
    charge_ids = [expense['charge_id'] for expense in json_data['data']]

    assert len(charge_ids) == 1

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)

    rate_plan_expense_charge = bill_aggregate.get_charge(charge_ids[0])
    assert rate_plan_expense_charge.is_inclusion_charge

    room_stay = booking_aggregate.get_room_stay(1)
    charge_id = room_stay.charge_id_map.get(
        dateutils.date_to_ymd_str(booking_aggregate.booking.checkin_date)
    )
    room_charge = bill_aggregate.get_charge(charge_id)

    assert rate_plan_expense_charge.charge_id in room_charge.inclusion_charge_ids
    assert rate_plan_expense_charge.posttax_amount.amount == Decimal("123")


@pytest.mark.usefixtures("setup_hotel")
def test_add_expense_v3_should_calculate_tax_properly(
    booking_repo, bill_repo, client, addon_repo, active_hotel_aggregate
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=14)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        set_travel_agent_details=True,
        set_company_details=True,
        default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        set_booking_owner_phone_null=True,
        channel="b2b",
        with_payments=False,
    )
    create_booking_payload['company_details']['legal_details'].update(
        {'is_sez': True, 'has_lut': False}
    )
    create_booking_payload['travel_agent_details']['legal_details'].update(
        {'is_sez': True, 'has_lut': True}
    )
    booking_id = make_booking(client=client, payload={"data": create_booking_payload})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)
    bill = bill_repo.load(booking_aggregate.booking.bill_id)
    billed_entity_of_company = bill.get_billed_entity_for_category(
        BilledEntityCategory.BOOKER_COMPANY
    )
    expense_v3_request = create_expenses_v3_request(
        dates=[checkin_date, dateutils.add(checkin_date, days=1)],
        charge_to=["2"],
        single_charge_split=True,
        billed_entity_id=billed_entity_of_company.billed_entity_id,
    )
    add_expenses_v3(client, booking_id, expense_v3_request)
    booking_aggregate = booking_repo.load(booking_id)
    booking = booking_aggregate.booking
    bill = bill_repo.load(booking.bill_id)
    for charge in bill.charges:
        if charge.item.sku_category_id == 'stay':
            assert charge.tax_amount.amount == Decimal("0")
        if charge.item.sku_category_id == 'food':
            assert charge.tax_amount.amount != Decimal("0")
