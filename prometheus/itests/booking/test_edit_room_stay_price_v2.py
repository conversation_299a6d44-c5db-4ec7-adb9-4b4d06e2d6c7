import json
from decimal import Decimal

import pytest
from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from prometheus.domain.billing.entities.billed_entity import (
    BilledEntityAccountVO,
    BillingInstructionVO,
)
from prometheus.itests.api_wrappers.addon_wrappers import add_addon, add_addon_v2
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import make_booking
from prometheus.itests.api_wrappers.booking_wrappers import make_booking_v2
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.tests.mockers import (
    mock_role_manager,
    mock_tax_calculator_service,
    mock_tenant_config_club_inclusion,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeTypes,
    PaymentInstruction,
)
from ths_common.constants.booking_constants import BookingStatus


def patch_room_stay_prices(client, booking_id, payload, expected=200):
    with mock_role_manager():
        url = f"/v2/bookings/{booking_id}/room-stays/1/prices"
        payload = json.dumps(payload)
        response = client.patch(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == expected
    return response.json if expected == 200 else None


@pytest.mark.parametrize(
    'create_booking_payload,rate_plan_addon',
    [
        (
            dict(
                checkin_date=dateutils.current_date(),
                checkout_date=dateutils.add(dateutils.current_date(), days=4),
            ),
            dict(
                start_date=dateutils.current_date(),
                end_date=dateutils.add(dateutils.current_date(), days=3),
            ),
        )
    ],
    indirect=True,
)
def test_edit_room_price_with_inclusion_should_update_room_stay_charge_and_inclusion_charge(
    create_booking_payload, rate_plan_addon, client, bill_repo, addon_repo, booking_repo
):
    booking_id = make_booking(
        client=client, payload={"data": json.loads(create_booking_payload)}
    )
    add_addon_v2(client, booking_id, payload={"data": rate_plan_addon})

    booking_aggregate = booking_repo.load(booking_id)
    charge_id = booking_aggregate.get_room_stay(1).charge_ids[0]

    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)
    inclusion_charges = bill_aggregate.get_charges(charge.inclusion_charge_ids)
    inclusion_charge = inclusion_charges[0]

    edit_room_stay_price_payload = {
        "data": [
            {
                "charge_id": charge.charge_id,
                "pretax_amount": "4000",
                "inclusion_charges": [
                    {"charge_id": inclusion_charge.charge_id, "pretax_amount": "500"}
                ],
            }
        ],
        "resource_version": booking_aggregate.current_version(),
    }
    patch_room_stay_prices(client, booking_id, edit_room_stay_price_payload)

    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)

    updated_room_charge = bill_aggregate.get_charge(charge.charge_id)
    updated_inclusion_charge = bill_aggregate.get_charge(inclusion_charge.charge_id)

    assert updated_room_charge.pretax_amount == Money("4000 INR")
    assert updated_inclusion_charge.pretax_amount == Money("500 INR")


@pytest.mark.parametrize(
    'create_booking_payload,rate_plan_addon',
    [
        (
            dict(
                checkin_date=dateutils.current_date(),
                checkout_date=dateutils.add(dateutils.current_date(), days=4),
            ),
            dict(
                start_date=dateutils.current_date(),
                end_date=dateutils.add(dateutils.current_date(), days=3),
            ),
        )
    ],
    indirect=True,
)
def test_edit_room_price_via_posttax_with_inclusion_should_update_room_stay_charge_and_inclusion_charge(
    create_booking_payload, rate_plan_addon, client, bill_repo, addon_repo, booking_repo
):
    booking_id = make_booking(
        client=client, payload={"data": json.loads(create_booking_payload)}
    )
    add_addon_v2(client, booking_id, payload={"data": rate_plan_addon})

    booking_aggregate = booking_repo.load(booking_id)
    charge_id = booking_aggregate.get_room_stay(1).charge_ids[0]

    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)
    inclusion_charges = bill_aggregate.get_charges(charge.inclusion_charge_ids)
    inclusion_charge = inclusion_charges[0]

    edit_room_stay_price_payload = {
        "data": [
            {
                "charge_id": charge.charge_id,
                "posttax_amount": "4000",
                "inclusion_charges": [
                    {"charge_id": inclusion_charge.charge_id, "posttax_amount": "500"}
                ],
            }
        ],
        "resource_version": booking_aggregate.current_version(),
    }
    patch_room_stay_prices(client, booking_id, edit_room_stay_price_payload)

    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)

    updated_room_charge = bill_aggregate.get_charge(charge.charge_id)
    updated_inclusion_charge = bill_aggregate.get_charge(inclusion_charge.charge_id)

    assert updated_room_charge.posttax_amount == Money("4000 INR")
    assert updated_inclusion_charge.posttax_amount == Money("500 INR")


@pytest.mark.parametrize(
    'create_booking_payload,rate_plan_addon',
    [
        (
            dict(
                checkin_date=dateutils.current_date(),
                checkout_date=dateutils.add(dateutils.current_date(), days=4),
            ),
            dict(
                start_date=dateutils.current_date(),
                end_date=dateutils.add(dateutils.current_date(), days=3),
            ),
        )
    ],
    indirect=True,
)
def test_edit_room_charge_billing_instructions_applies_same_billing_instructions_to_all_inclusion_charges(
    create_booking_payload, rate_plan_addon, client, bill_repo, addon_repo, booking_repo
):
    booking_id = make_booking(
        client=client, payload={"data": json.loads(create_booking_payload)}
    )
    add_addon_v2(client, booking_id, payload={"data": rate_plan_addon})

    booking_aggregate = booking_repo.load(booking_id)
    charge_id = booking_aggregate.get_room_stay(1).charge_ids[0]

    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)
    inclusion_charges = bill_aggregate.get_charges(charge.inclusion_charge_ids)
    inclusion_charge = inclusion_charges[0]

    edit_room_stay_price_payload = {
        "data": [
            {
                "charge_id": charge.charge_id,
                "billing_instructions": [
                    {
                        "billed_entity_account": {
                            "billed_entity_id": 2,
                            "account_number": 2,
                        },
                        "payment_instruction": "pay_at_checkout",
                        "split_percentage": 50,
                    },
                    {
                        "billed_entity_account": {
                            "billed_entity_id": 1,
                            "account_number": 2,
                        },
                        "payment_instruction": "pay_after_checkout",
                        "split_percentage": 50,
                    },
                ],
            }
        ],
        "resource_version": booking_aggregate.current_version(),
    }
    patch_room_stay_prices(client, booking_id, edit_room_stay_price_payload)

    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)

    updated_room_charge = bill_aggregate.get_charge(charge.charge_id)
    updated_inclusion_charge = bill_aggregate.get_charge(inclusion_charge.charge_id)

    room_charge_billing_instructions = [
        (
            split.billed_entity_account.billed_entity_id,
            split.billed_entity_account.account_number,
            split.charge_type.value,
            split.percentage,
        )
        for split in updated_room_charge.charge_splits
    ]
    inclusion_charge_billing_instructions = [
        (
            split.billed_entity_account.billed_entity_id,
            split.billed_entity_account.account_number,
            split.charge_type.value,
            split.percentage,
        )
        for split in updated_inclusion_charge.charge_splits
    ]

    expected_billing_instructions = [
        (2, 2, ChargeTypes.NON_CREDIT.value, Decimal('50')),
        (1, 2, ChargeTypes.CREDIT.value, Decimal('50')),
    ]
    assert sorted(room_charge_billing_instructions) == sorted(
        expected_billing_instructions
    )
    assert sorted(room_charge_billing_instructions) == sorted(
        inclusion_charge_billing_instructions
    )


def test_update_room_night_price_should_recalculate_tax(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            set_company_details=True,
            set_travel_agent_details=True,
            default_billed_entity_category=BilledEntityCategory.BOOKER_COMPANY.value,
        )
        create_booking_payload['company_details']['legal_details'].update(
            {'is_sez': False, 'has_lut': False}
        )
        create_booking_payload['travel_agent_details']['legal_details'].update(
            {'is_sez': True, 'has_lut': True}
        )
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "7450",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        edit_room_stay_price_payload = {
            "data": [
                {
                    "charge_id": 1,
                    "posttax_amount": '500',
                }
            ],
            "resource_version": booking_aggregate.current_version(),
        }
        patch_room_stay_prices(client, booking_id, edit_room_stay_price_payload)

        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        for charge in bill_aggregate.charges:
            if charge.charge_id == 1:
                assert charge.tax_amount.amount == Decimal("76.28")


def test_update_gst_details_of_ta_with_lut_and_sez_change_should_recalculate_tax_of_only_ta_folio(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            set_company_details=True,
            set_travel_agent_details=True,
            default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        )
        create_booking_payload['company_details']['legal_details'].update(
            {'is_sez': False, 'has_lut': False}
        )
        create_booking_payload['travel_agent_details']['legal_details'].update(
            {'is_sez': True, 'has_lut': True}
        )
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "7450",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        billed_entity_of_company = bill_aggregate.get_billed_entity_for_category(
            BilledEntityCategory.BOOKER_COMPANY
        )
        edit_room_stay_price_payload = {
            "data": [
                {
                    "charge_id": 1,
                    "billing_instructions": [
                        {
                            "billed_entity_account": {
                                "billed_entity_id": billed_entity_of_company.billed_entity_id,
                                "account_number": 1,
                            },
                            "payment_instruction": "pay_at_checkout",
                            "split_percentage": 100,
                        },
                    ],
                }
            ],
            "resource_version": booking_aggregate.current_version(),
        }
        patch_room_stay_prices(client, booking_id, edit_room_stay_price_payload)

        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        # charges billed to company should have tax and on other hand charge billed to TA (default) should be of non tax
        for charge in bill_aggregate.charges:
            for split in charge.charge_splits:
                if (
                    split.billed_entity_account.billed_entity_id
                    == billed_entity_of_company.billed_entity_id
                ):
                    assert split.tax != Decimal("0")
                else:
                    assert split.tax == Decimal("0")


def test_update_room_night_price_and_billing_instruction_should_recalculate_tax(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            set_company_details=True,
            set_travel_agent_details=True,
            default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        )
        create_booking_payload['company_details']['legal_details'].update(
            {'is_sez': False, 'has_lut': False}
        )
        create_booking_payload['travel_agent_details']['legal_details'].update(
            {'is_sez': True, 'has_lut': True}
        )
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "7450",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        for charge in bill_aggregate.charges:
            if charge.charge_id == 1:
                assert charge.tax_amount.amount == Decimal("0")
        billed_entity_of_company = bill_aggregate.get_billed_entity_for_category(
            BilledEntityCategory.BOOKER_COMPANY
        )
        edit_room_stay_price_payload = {
            "data": [
                {
                    "charge_id": 1,
                    "posttax_amount": '500',
                    "billing_instructions": [
                        {
                            "billed_entity_account": {
                                "billed_entity_id": billed_entity_of_company.billed_entity_id,
                                "account_number": 1,
                            },
                            "payment_instruction": "pay_at_checkout",
                            "split_percentage": 100,
                        },
                    ],
                }
            ],
            "resource_version": booking_aggregate.current_version(),
        }
        patch_room_stay_prices(client, booking_id, edit_room_stay_price_payload)

        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        for charge in bill_aggregate.charges:
            if charge.charge_id == 1:
                assert charge.tax_amount.amount != Decimal("76.28")
            else:
                assert charge.tax_amount.amount == Decimal("0")


def test_update_room_night_price_and_with_conflicting_billing_instruction_should_raise_exception(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            set_company_details=True,
            set_travel_agent_details=True,
            default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        )
        create_booking_payload['company_details']['legal_details'].update(
            {'is_sez': False, 'has_lut': False}
        )
        create_booking_payload['travel_agent_details']['legal_details'].update(
            {'is_sez': True, 'has_lut': True}
        )
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "7450",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        billed_entity_of_company = bill_aggregate.get_billed_entity_for_category(
            BilledEntityCategory.BOOKER_COMPANY
        )
        billed_entity_of_ta = bill_aggregate.get_billed_entity_for_category(
            BilledEntityCategory.TRAVEL_AGENT
        )
        edit_room_stay_price_payload = {
            "data": [
                {
                    "charge_id": 1,
                    "billing_instructions": [
                        {
                            "billed_entity_account": {
                                "billed_entity_id": billed_entity_of_company.billed_entity_id,
                                "account_number": 1,
                            },
                            "payment_instruction": "pay_at_checkout",
                            "split_percentage": 50,
                        },
                        {
                            "billed_entity_account": {
                                "billed_entity_id": billed_entity_of_ta.billed_entity_id,
                                "account_number": 1,
                            },
                            "payment_instruction": "pay_at_checkout",
                            "split_percentage": 50,
                        },
                    ],
                }
            ],
            "resource_version": booking_aggregate.current_version(),
        }
        patch_room_stay_prices(
            client, booking_id, edit_room_stay_price_payload, expected=400
        )
