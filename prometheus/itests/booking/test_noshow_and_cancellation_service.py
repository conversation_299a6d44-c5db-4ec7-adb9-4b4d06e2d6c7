import json

from treebo_commons.utils import dateutils

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    cancel_room_stay,
    checkin_booking,
    checkout_booking,
    delete_action,
    no_show_room_stay,
)
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.billing.test_payment_api import create_cash_register_and_session
from prometheus.itests.booking.booking_action_reversal.test_cancellation_reversal import (
    cancellation_partial_booking_request,
    no_show_partial_booking_request,
)
from prometheus.itests.booking.test_booking_v2 import make_booking
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
    create_checkout_payload,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_payload,
    create_preview_invoice_with_charge_decision_payload,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_rate_manager_client,
    mock_role_manager,
    mock_tax_calculator_service,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    CashRegisterNames,
    ChargeBillToTypes,
    ChargeTypes,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.catalog_constants import SellerType


def test_remove_primary_guest(booking_repo, bill_repo, client, hotel_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
    )

    payload = {"data": create_booking_payload}
    booking_data = make_booking(
        client=client, payload=payload, return_complete_response=True
    )
    booking = booking_repo.load(booking_data["data"]["booking_id"])
    (
        cancellation_payload,
        current_primary_guest_id,
    ) = create_partial_booking_primary_guest_cancellation_payload(booking_data['data'])
    primary_customer = booking.get_customer(customer_id=current_primary_guest_id)

    post_cancel_room_stay(
        client, booking_data["data"]["booking_id"], cancellation_payload
    )
    booking = booking_repo.load(booking_data["data"]["booking_id"])
    updated_primary_customer = booking.get_customer(
        customer_id=current_primary_guest_id
    )
    assert primary_customer.is_primary != updated_primary_customer.is_primary


def create_partial_booking_primary_guest_cancellation_payload(booking_data):
    primary_customer_ids = [
        cust['customer_id'] for cust in booking_data['customers'] if cust['is_primary']
    ]
    guest_stays = booking_data['room_stays'][0]['guest_stays']
    primary_guest_stay = [
        gs
        for gs in guest_stays
        if gs['guest_allocation']['guest_id'] in primary_customer_ids
    ][0]
    room_stay = booking_data['room_stays'][0]
    data = {
        "data": {
            "cancellation_reason": "Test primary guest cancellation",
            "room_stays": [
                {
                    "guest_stay_ids": [primary_guest_stay['guest_stay_id']],
                    "prices": [
                        {
                            "applicable_date": room_stay['checkin_date'],
                            "bill_to_type": ChargeBillToTypes.GUEST.value,
                            "type": ChargeTypes.NON_CREDIT.value,
                            "pretax_amount": room_stay['room_rents'][0][
                                'posttax_amount'
                            ],
                        }
                    ],
                    "room_stay_id": room_stay['room_stay_id'],
                }
            ],
        },
        "resource_version": booking_data['version'],
    }
    return data, primary_guest_stay['guest_allocation']['guest_id']


def create_booking_relocated_by_cancellation_payload(
    reference_number_of_target_booking,
):
    return {
        "data": {
            "cancellation_reason": "Cancel Booking",
            "is_booking_relocated": True,
            "booking_relocation_details": {
                "booking_reference_number": reference_number_of_target_booking,
                "hotel_name": "Example Hotel",
                "reason": "Relocation due to unforeseen circumstances",
                "relocated_to_another_treebo_hotel": True,
            },
        },
        "resource_version": 1,
    }


def post_cancel_room_stay(client, booking_id, payload):
    with mock_role_manager():
        url = f"/v1/bookings/{booking_id}/mark-cancelled"
        payload = json.dumps(payload)
        response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == 200
    return response.json


def test_actual_checkout_date_on_cancel_room_after_partial_checkout(
    create_booking_payload_with_two_room_stays,
    client,
    booking_repo,
    bill_repo,
    active_hotel_aggregate,
    hotel_repo,
):
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            payload = json.dumps(
                {"data": json.loads(create_booking_payload_with_two_room_stays)}
            )
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            booking_id = bookings_response['data']['booking_id']
            assert len(bookings_response['data']['room_stays']) > 0

    booking_aggregate = booking_repo.load(booking_id)
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
        guest_id=2,
    )
    checkin_booking(client, booking_id, checkin_payload)

    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, guest_ids=2
    )
    preview_invoice(booking_id, client, preview_invoice_payload, show_raw_response=True)
    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, guest_id=2
    )
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )

    invoice_group_id = response.get('data').get('invoice_group_id')
    booking_aggregate = booking_repo.load(booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_id, checkout_payload)

    assert booking_aggregate.booking.actual_checkout_date is None
    assert booking_aggregate.booking.actual_checkout_business_date is None
    assert booking_aggregate.booking.actual_checkout_calendar_date is None

    booking_aggregate = booking_repo.load(booking_id)
    booking_aggregate.booking.actual_checkout_date = None
    booking_aggregate.booking.actual_checkout_business_date = None
    booking_aggregate.booking.actual_checkout_calendar_date = None
    cancel_room_payload = cancellation_partial_booking_request(
        booking_aggregate.booking.version, room_stay_id=2
    )
    cancel_room_stay(client, booking_id, cancel_room_payload)
    booking_aggregate = booking_repo.load(booking_id)

    assert (
        booking_aggregate.booking.actual_checkout_date
        == booking_aggregate.get_room_stay(1).actual_checkout_date
    )
    assert (
        booking_aggregate.booking.actual_checkout_business_date
        == booking_aggregate.get_room_stay(1).actual_checkout_business_date
    )


def test_actual_checkout_date_on_no_show_room_after_partial_checkout(
    create_past_booking_payload_with_two_room_stays,
    client,
    booking_repo,
    bill_repo,
    active_hotel_aggregate,
    hotel_repo,
):
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            payload = json.dumps(
                {"data": json.loads(create_past_booking_payload_with_two_room_stays)}
            )
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            booking_id = bookings_response['data']['booking_id']
            assert len(bookings_response['data']['room_stays']) > 0

    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
        guest_id=2,
    )
    checkin_booking(client, booking_id, checkin_payload)

    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, guest_ids=2
    )
    preview_invoice(booking_id, client, preview_invoice_payload, show_raw_response=True)
    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, guest_id=2
    )
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )

    invoice_group_id = response.get('data').get('invoice_group_id')
    booking_aggregate = booking_repo.load(booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_id, checkout_payload)

    assert booking_aggregate.booking.actual_checkout_date is None
    assert booking_aggregate.booking.actual_checkout_business_date is None
    assert booking_aggregate.booking.actual_checkout_calendar_date is None

    booking_aggregate = booking_repo.load(booking_id)
    booking_aggregate.booking.actual_checkout_date = None
    booking_aggregate.booking.actual_checkout_business_date = None
    booking_aggregate.booking.actual_checkout_calendar_date = None
    no_show_room_payload = no_show_partial_booking_request(
        booking_aggregate.booking.version, room_stay_id=2
    )
    action_id = no_show_room_stay(client, booking_id, no_show_room_payload)
    booking_aggregate = booking_repo.load(booking_id)

    assert (
        booking_aggregate.booking.actual_checkout_date
        == booking_aggregate.get_room_stay(1).actual_checkout_date
    )
    assert (
        booking_aggregate.booking.actual_checkout_business_date
        == booking_aggregate.get_room_stay(1).actual_checkout_business_date
    )

    delete_action(client, booking_id, action_id)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.actual_checkout_date is None
    assert booking_aggregate.booking.actual_checkout_business_date is None
    assert booking_aggregate.booking.actual_checkout_calendar_date is None


def test_booking_relocation(booking_repo, bill_repo, client, hotel_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        default_billed_entity_category=BilledEntityCategory.BOOKER_COMPANY.value,
    )

    payload = {"data": create_booking_payload}
    booking_data = make_booking(
        client=client, payload=payload, return_complete_response=True
    )
    booking = booking_repo.load(booking_data["data"]["booking_id"])
    bill_aggregate = bill_repo.load(booking_data['data']['bill_id'])

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            payment_splits=[
                dict(
                    billed_entity_account=dict(billed_entity_id=2, account_number=1),
                    amount="3000",
                )
            ],
            amount='3000',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    # creating second booking for relocation
    create_booking_payload['reference_number'] = "TEST_RELOCATION"
    payload = {"data": create_booking_payload}
    new_booking_data = make_booking(
        client=client, payload=payload, return_complete_response=True
    )

    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )

    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )

    cancellation_payload = create_booking_relocated_by_cancellation_payload(
        "TEST_RELOCATION"
    )
    post_cancel_room_stay(
        client, booking_data["data"]["booking_id"], cancellation_payload
    )
    bill_aggregate = bill_repo.load(booking_data['data']['bill_id'])
    booking_aggregate = booking_repo.load(booking_data['data']['booking_id'])
    assert booking_aggregate.booking.comments is not None
    assert booking_aggregate.booking.extra_information is not None
    target_booking_aggregate = booking_repo.load(new_booking_data['data']['booking_id'])
    assert target_booking_aggregate.booking.comments is not None
    assert target_booking_aggregate.booking.extra_information == {}

    refund_payment = bill_aggregate.payments[2]
    assert refund_payment.amount.amount == 3200
    assert refund_payment.payment_mode == 'transferred_credit'
