import datetime
import json
from decimal import Decimal

from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    add_room_stay,
    cancel_booking,
    cancel_room_stay,
    checkin_booking,
    checkout_booking,
    no_show_room_stay,
)
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.billing.test_invoice_modification import (
    create_locked_invoices,
    modify_invoice_api_call,
)
from prometheus.itests.billing.test_payment_api import create_cash_register_and_session
from prometheus.itests.booking.booking_action_reversal.test_cancellation_reversal import (
    cancellation_partial_booking_request,
    no_show_partial_booking_request,
)
from prometheus.itests.booking.test_add_guests import add_guest_stay_request
from prometheus.itests.booking.test_edit_room_stay_price_v2 import (
    patch_room_stay_prices,
)
from prometheus.itests.booking.test_patch_booking_api import make_booking_v2
from prometheus.itests.booking.test_roomstay_api import (
    create_room_type_and_price_change_payload,
    post_room_stay_allocate_room,
)
from prometheus.itests.booking.test_roomstay_v2_api import (
    change_room_stay_rate_plan,
    extend_rooms_stay_by_shifting,
    reduce_and_then_restore_rooms_stay_by_shifting,
    reduce_rooms_stay_by_shifting,
)
from prometheus.itests.booking.test_ta_commission import set_ta_details
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
    create_checkout_payload,
    guest_stay_cancellation_request,
    guest_stay_noshow_request,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_with_charge_decision_payload,
)
from prometheus.tests.mockers import (
    mock_role_manager,
    mock_rule_engine,
    mock_tenant_config_club_inclusion,
)
from prometheus.tests.test_utils import today_minus_days
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    CashRegisterNames,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingChannels, BookingStatus
from ths_common.constants.domain_event_constants import DomainEvent


def test_update_price_to_change_commission(
    create_booking_payload, client, booking_repo, bill_repo, booking_audit_trail_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        charge_id = booking_aggregate.get_room_stay(1).charge_ids[0]

        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        charge = bill_aggregate.get_charge(charge_id)
        inclusion_charges = bill_aggregate.get_charges(charge.inclusion_charge_ids)
        edit_room_stay_price_payload = {
            "data": [
                {
                    "charge_id": charge.charge_id,
                    "pretax_amount": "4000",
                    "inclusion_charges": [
                        {"charge_id": 2, "pretax_amount": "500"},
                        {"charge_id": 3, "pretax_amount": "500"},
                    ],
                }
            ],
            "resource_version": booking_aggregate.current_version(),
        }

        patch_room_stay_prices(client, booking_id, edit_room_stay_price_payload)
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('250.00')]
        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        charge_modified_audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.CHARGE_MODIFIED
        ][0]
        domain_events = charge_modified_audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['new_commission_amount'] == '250.00'
        assert ta_commission_update_event_data['old_commission_amount'] == '30.00'


def test_update_commission_price_after_allowance_passed_on_room_stay_charge(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        roll_over_business_date(active_hotel_aggregate, hotel_repo)
        booking_aggregate = booking_repo.load(booking_id)
        checkin_payload = create_checkin_payload(
            booking_aggregate.booking.version, checkin_datetime=today_minus_days(1)
        )
        checkin_booking(client, booking_id, checkin_payload)
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        bill_aggregate.consume_charges(
            charges=bill_aggregate.charges,
            business_date=active_hotel_aggregate.hotel.current_business_date,
        )
        bill_repo.update(bill_aggregate)
        add_allowance_url = (
            'v1/bills/'
            + booking_aggregate.bill_id
            + '/charges/'
            + '1'
            + '/charge-splits/'
            + '1'
            + '/allowances'
        )
        new_allowance = dict(
            data=dict(pretax_amount='50 INR', remarks='add new allowance'),
            resource_version=bill_aggregate.current_version(),
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            add_allowance_url,
            data=json.dumps(new_allowance),
            content_type='application/json',
            headers=headers,
        )
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('27.50')]
        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.ALLOWANCE_PASSED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['new_commission_amount'] == '27.50'
        assert ta_commission_update_event_data['old_commission_amount'] == '30.00'


def test_update_commission_price_after_allowance_passed_on_inclusion_charge(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        roll_over_business_date(active_hotel_aggregate, hotel_repo)
        booking_aggregate = booking_repo.load(booking_id)
        checkin_payload = create_checkin_payload(
            booking_aggregate.booking.version, checkin_datetime=today_minus_days(1)
        )
        checkin_booking(client, booking_id, checkin_payload)
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        bill_aggregate.consume_charges(
            charges=bill_aggregate.charges,
            business_date=active_hotel_aggregate.hotel.current_business_date,
        )
        bill_repo.update(bill_aggregate)
        add_allowance_url = (
            'v1/bills/'
            + booking_aggregate.bill_id
            + '/charges/'
            + '2'
            + '/charge-splits/'
            + '1'
            + '/allowances'
        )
        new_allowance = dict(
            data=dict(pretax_amount='50 INR', remarks='add new allowance'),
            resource_version=bill_aggregate.current_version(),
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            add_allowance_url,
            data=json.dumps(new_allowance),
            content_type='application/json',
            headers=headers,
        )
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('27.50')]
        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.ALLOWANCE_PASSED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['new_commission_amount'] == '27.50'
        assert ta_commission_update_event_data['old_commission_amount'] == '30.00'


def test_update_commission_price_after_bulk_allowance_passed_on_charge(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        roll_over_business_date(active_hotel_aggregate, hotel_repo)
        booking_aggregate = booking_repo.load(booking_id)
        checkin_payload = create_checkin_payload(
            booking_aggregate.booking.version, checkin_datetime=today_minus_days(1)
        )
        checkin_booking(client, booking_id, checkin_payload)
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        bill_aggregate.consume_charges(
            charges=bill_aggregate.charges,
            business_date=active_hotel_aggregate.hotel.current_business_date,
        )
        bill_repo.update(bill_aggregate)
        allowance_data = list()
        for charge in bill_aggregate.charges:
            charge_id = charge.charge_id
            charge_split_id = bill_aggregate.charges[0].charge_splits[0].charge_split_id
            allowance_data.append(
                dict(
                    pretax_amount='50 INR',
                    remarks='add new allowance',
                    charge_id=charge_id,
                    charge_split_id=charge_split_id,
                )
            )

        add_allowance_url = 'v1/bills/' + booking_aggregate.bill_id + '/allowances'
        new_allowances = dict(
            data=allowance_data, resource_version=bill_aggregate.current_version()
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            add_allowance_url,
            data=json.dumps(new_allowances),
            content_type='application/json',
            headers=headers,
        )
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('22.50')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.ALLOWANCE_PASSED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['new_commission_amount'] == '22.50'
        assert ta_commission_update_event_data['old_commission_amount'] == '30.00'


def test_update_commission_price_after_allowance_passed_get_cancelled(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        roll_over_business_date(active_hotel_aggregate, hotel_repo)
        booking_aggregate = booking_repo.load(booking_id)
        checkin_payload = create_checkin_payload(
            booking_aggregate.booking.version, checkin_datetime=today_minus_days(1)
        )
        checkin_booking(client, booking_id, checkin_payload)
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        bill_aggregate.consume_charges(
            charges=bill_aggregate.charges,
            business_date=active_hotel_aggregate.hotel.current_business_date,
        )
        bill_repo.update(bill_aggregate)
        add_allowance_url = (
            'v1/bills/'
            + booking_aggregate.bill_id
            + '/charges/'
            + '1'
            + '/charge-splits/'
            + '1'
            + '/allowances'
        )
        new_allowance = dict(
            data=dict(pretax_amount='50 INR', remarks='add new allowance'),
            resource_version=bill_aggregate.current_version(),
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            add_allowance_url,
            data=json.dumps(new_allowance),
            content_type='application/json',
            headers=headers,
        )
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        patch_allowance_url = (
            'v1/bills/'
            + booking_aggregate.bill_id
            + '/charges/'
            + '1'
            + '/charge-splits/'
            + '1'
            + '/allowances/'
            + '1'
        )
        patch_allowance = dict(
            data=dict(status='cancelled'),
            resource_version=bill_aggregate.current_version(),
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.patch(
            patch_allowance_url,
            data=json.dumps(patch_allowance),
            content_type='application/json',
            headers=headers,
        )

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('30.00')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.ALLOWANCE_CANCELLED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '27.50'
        assert ta_commission_update_event_data['new_commission_amount'] == '30.00'


def test_update_commission_price_after_rate_plan_change(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)

        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('25.00')]

        change_room_stay_rate_plan(
            client,
            booking_aggregate.booking_id,
            checkin_date,
            checkout_date,
            resource_version=booking_aggregate.booking.version,
        )
        booking_aggregate = booking_repo.load(booking_id)
        cns_after_update_rate_plan = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns_after_update_rate_plan.append(rn_cn.posttax_amount.amount)
        assert cns_after_update_rate_plan == [Decimal('23.15')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.RATE_PLAN_CHANGED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '25.00'
        assert ta_commission_update_event_data['new_commission_amount'] == '23.15'


def test_update_commission_price_after_room_stay_extended(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        extend_rooms_stay_by_shifting(
            client,
            booking_aggregate.booking_id,
            0,
            2,
            resource_version=booking_aggregate.booking.version,
        )
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('25.00'), Decimal('25.00'), Decimal('22.25')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.ROOM_STAY_DATES_CHANGED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '50.00'
        assert ta_commission_update_event_data['new_commission_amount'] == '72.25'


def test_update_commission_price_after_room_stay_reduced(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        reduce_rooms_stay_by_shifting(client, booking_aggregate.booking_id, 0, 2)
        booking_aggregate = booking_repo.load(booking_id)
        cns_created = []
        cns_cancelled = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                if rn_cn.status.value == 'created':
                    cns_created.append(rn_cn.posttax_amount.amount)
                else:
                    cns_cancelled.append(rn_cn.posttax_amount.amount)
        assert cns_created == [Decimal('25.00')]
        assert set(cns_cancelled) == {Decimal('25.00')}

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.ROOM_STAY_DATES_CHANGED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '50.00'
        assert ta_commission_update_event_data['new_commission_amount'] == '25.00'


def test_update_commission_price_after_room_stay_reduced_and_shifted_again(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        reduce_and_then_restore_rooms_stay_by_shifting(
            client, booking_aggregate.booking_id, 0, 2
        )
        booking_aggregate = booking_repo.load(booking_id)
        cns_created = []
        cns_cancelled = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                if rn_cn.status.value == 'created':
                    cns_created.append(rn_cn.posttax_amount.amount)
                else:
                    cns_cancelled.append(rn_cn.posttax_amount.amount)
        assert cns_created == [Decimal('25.00'), Decimal('22.25')]
        assert set(cns_cancelled) == {Decimal('25.00')}


def modify_invoice_payload(
    invoice_aggregate,
    bill_aggregate,
    transfer_payment_to_new_invoice_account: bool = True,
):
    return {
        "data": {
            "modify_invoices": [
                {
                    "invoice_charge_modification": [
                        {
                            "billed_entity_account": {
                                "account_number": 2,
                                "billed_entity_id": 3,
                            },
                            "invoice_charge_id": 1,
                            "posttax_amount": "200.0 INR",
                        },
                        {
                            "billed_entity_account": {
                                "account_number": 2,
                                "billed_entity_id": 3,
                            },
                            "invoice_charge_id": 2,
                            "posttax_amount": "0.0 INR",
                        },
                    ],
                    "invoice_id": invoice_aggregate.invoice_id,
                    "invoice_version": invoice_aggregate.invoice.version,
                }
            ],
            "transfer_payment_to_new_invoice_account": transfer_payment_to_new_invoice_account,
        },
        "resource_version": bill_aggregate.bill.version,
    }


def test_update_commission_price_after_reissue_of_created_commission(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
    active_hotel_aggregate,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "50",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        }
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        locked_invoices = create_locked_invoices(
            active_hotel_aggregate,
            booking_id,
            booking_invoice_group_repo,
            booking_repo,
            client,
            hotel_repo,
            invoice_repo,
            seller_repo,
        )

        bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

        modify_invoice_request = modify_invoice_payload(
            invoice_aggregate=locked_invoices[0],
            bill_aggregate=bill_aggregate,
            transfer_payment_to_new_invoice_account=True,
        )

        response = modify_invoice_api_call(
            modify_invoice_request, locked_invoices[0].bill_id, client
        )
        booking_aggregate = booking_repo.load(booking_id)
        cns_created = []
        cns_cancelled = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                if rn_cn.is_allowance:
                    continue
                if rn_cn.status.value == 'locked':
                    cns_created.append(rn_cn.posttax_amount.amount)
                elif rn_cn.status.value == 'nullified_by_reissue':
                    cns_cancelled.append(rn_cn.posttax_amount.amount)
        assert set(cns_cancelled) == {Decimal('7.50')}
        assert cns_created == [Decimal('8.93')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.INVOICE_REISSUED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '7.50'
        assert ta_commission_update_event_data['new_commission_amount'] == '8.93'


def test_add_room_stay_should_create_commission_for_added_room(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    create_add_room_stay_payload,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        add_room_stay(client, booking_id, create_add_room_stay_payload)
        roll_over_business_date(active_hotel_aggregate, hotel_repo)
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('30.00'), Decimal('5.27')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.ROOM_STAY_ADDED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '30.00'
        assert ta_commission_update_event_data['new_commission_amount'] == '35.27'


def test_cancel_room_stay_should_mark_commission_as_cancelled_for_cancelled_room(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        roll_over_business_date(active_hotel_aggregate, hotel_repo)
        booking_aggregate = booking_repo.load(booking_id)

        cancel_room_payload = cancellation_partial_booking_request(
            booking_aggregate.booking.version, room_stay_id=2
        )
        cancel_room_stay(client, booking_id, cancel_room_payload)
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('30.00')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.PARTIAL_BOOKING_CANCELLED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '55.00'
        assert ta_commission_update_event_data['new_commission_amount'] == '30.00'


def test_noshow_room_stay_should_mark_commission_as_cancelled_for_noshow_room(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    booking_audit_trail_repo,
):
    hotel_context = crs_context.get_hotel_context()
    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=-1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=0),
        hotel_context.checkout_time,
    )
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        roll_over_business_date(active_hotel_aggregate, hotel_repo)
        booking_aggregate = booking_repo.load(booking_id)

        no_show_room_payload = no_show_partial_booking_request(
            booking_aggregate.booking.version, room_stay_id=2
        )
        action_id = no_show_room_stay(client, booking_id, no_show_room_payload)

        booking_aggregate = booking_repo.load(booking_id)

        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('30.00')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.PARTIAL_BOOKING_MARKED_NOSHOW
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '55.00'
        assert ta_commission_update_event_data['new_commission_amount'] == '30.00'


def test_add_guest_stay_should_update_commission_for_room(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    create_add_room_stay_payload,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        req = add_guest_stay_request()
        url = f"/v1/bookings/{booking_id}/room-stays/1/guest-stays"
        with mock_role_manager():
            response = client.post(
                url,
                data=req,
                content_type="application/json",
                headers={"X-User-Type": "super-admin"},
            )
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('131.15')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.GUEST_STAY_ADDED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '30.00'
        assert ta_commission_update_event_data['new_commission_amount'] == '131.15'


def test_cancel_guest_stay_should_update_commission_for_room(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    create_add_room_stay_payload,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        room_stay_id_to_cancel = 1
        guest_ids = [1]
        booking_aggregate = booking_repo.load(booking_id)
        room_stay_cancel_payload = guest_stay_cancellation_request(
            booking_aggregate.booking.version,
            room_stay_id=room_stay_id_to_cancel,
            guest_ids=guest_ids,
            prices=create_booking_payload['room_stays'][0]['prices'],
        )

        cancel_booking(client, booking_id, room_stay_cancel_payload)

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('5.00')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.PARTIAL_BOOKING_CANCELLED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '30.00'
        assert ta_commission_update_event_data['new_commission_amount'] == '5.00'


def test_noshow_guest_stay_should_update_commission_for_room_if_update_required(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    create_add_room_stay_payload,
):
    hotel_context = crs_context.get_hotel_context()
    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=-1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=0),
        hotel_context.checkout_time,
    )
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        room_stay_id_to_cancel = 1
        guest_ids = [1]
        booking_aggregate = booking_repo.load(booking_id)
        room_stay_cancel_payload = guest_stay_noshow_request(
            booking_aggregate.booking.version,
            room_stay_id=room_stay_id_to_cancel,
            guest_ids=guest_ids,
            prices=create_booking_payload['room_stays'][0]['prices'],
        )

        cancel_booking(client, booking_id, room_stay_cancel_payload)

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('30.00')]


def test_update_commission_price_after_early_checkout(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
    active_hotel_aggregate,
    temp_create_new_room,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(bill_id=booking_aggregate.bill_id)
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        payment_splits = [
            dict(
                amount='400.00',
                billed_entity_account=dict(account_number=1, billed_entity_id=4),
            )
        ]
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                payment_splits=payment_splits,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        with mock_rule_engine():
            response = client.post(
                url,
                data=json.dumps(new_payment),
                content_type='application/json',
                headers=headers,
            )

        from prometheus.itests.payload_generators.booking_action_payload_generators import (
            create_checkin_payload,
        )

        # Rollover Business Date
        roll_over_business_date(active_hotel_aggregate, hotel_repo)

        checkin_payload = create_checkin_payload(
            booking_aggregate.booking.version,
            booking_aggregate.booking.checkin_date,
            guest_id=2,
        )
        checkin_booking(client, booking_id, checkin_payload)
        booking_aggregate = booking_repo.load(booking_id)
        checkin_payload = create_checkin_payload(
            booking_aggregate.booking.version,
            booking_aggregate.booking.checkin_date,
            room_stay_id=2,
            room_id=16,
            guest_id=3,
        )
        checkin_booking(client, booking_id, checkin_payload)
        booking_aggregate = booking_repo.load(booking_id)
        preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
            booking_aggregate.booking.version,
            active_hotel_aggregate,
            room_stay_id=2,
            booked_charges_to_post=[3],
        )
        response = preview_invoice(
            booking_id, client, preview_invoice_payload, show_raw_response=True
        )
        invoice_group_id = response.get('data').get('invoice_group_id')
        booking_aggregate = booking_repo.load(booking_id)
        checkout_payload = create_checkout_payload(
            booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
        )
        checkout_booking(client, booking_id, checkout_payload)
        preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
            booking_aggregate.booking.version,
            active_hotel_aggregate,
            room_stay_id=1,
            guest_id=2,
        )
        response = preview_invoice(
            booking_id, client, preview_invoice_payload, show_raw_response=True
        )
        invoice_group_id = response.get('data').get('invoice_group_id')
        booking_aggregate = booking_repo.load(booking_id)
        checkout_payload = create_checkout_payload(
            booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
        )
        checkout_booking(client, booking_id, checkout_payload)

        booking_aggregate = booking_repo.load(booking_id)

        cns_created, cns_cancelled = [], []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                if rn_cn.status.value == 'locked':
                    cns_created.append(rn_cn.posttax_amount.amount)
                else:
                    cns_cancelled.append(rn_cn.posttax_amount.amount)
        assert cns_created == [Decimal('5.00'), Decimal('5.00')]
        assert set(cns_cancelled) == {Decimal('5.00'), Decimal('5.00')}


def test_update_commission_price_after_early_checkout_with_inclusions(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
    active_hotel_aggregate,
    temp_create_new_room,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "5",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "5",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "5",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "5",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    set_ta_details(create_booking_payload, set_commission_details=False)
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(bill_id=booking_aggregate.bill_id)
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        payment_splits = [
            dict(
                amount='400.00',
                billed_entity_account=dict(account_number=1, billed_entity_id=4),
            )
        ]
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                payment_splits=payment_splits,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        with mock_rule_engine():
            response = client.post(
                url,
                data=json.dumps(new_payment),
                content_type='application/json',
                headers=headers,
            )

        from prometheus.itests.payload_generators.booking_action_payload_generators import (
            create_checkin_payload,
        )

        # Rollover Business Date
        roll_over_business_date(active_hotel_aggregate, hotel_repo)

        checkin_payload = create_checkin_payload(
            booking_aggregate.booking.version,
            booking_aggregate.booking.checkin_date,
            guest_id=2,
        )
        checkin_booking(client, booking_id, checkin_payload)
        booking_aggregate = booking_repo.load(booking_id)
        checkin_payload = create_checkin_payload(
            booking_aggregate.booking.version,
            booking_aggregate.booking.checkin_date,
            room_stay_id=2,
            room_id=16,
            guest_id=3,
        )
        checkin_booking(client, booking_id, checkin_payload)
        bill_aggregate = bill_repo.load(bill_id=booking_aggregate.bill_id)
        booking_aggregate = booking_repo.load(booking_id)
        preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
            booking_aggregate.booking.version,
            active_hotel_aggregate,
            room_stay_id=2,
            booked_charges_to_post=[3],
        )
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        response = preview_invoice(
            booking_id, client, preview_invoice_payload, show_raw_response=True
        )
        invoice_group_id = response.get('data').get('invoice_group_id')
        booking_aggregate = booking_repo.load(booking_id)
        checkout_payload = create_checkout_payload(
            booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
        )
        response = checkout_booking(client, booking_id, checkout_payload)
        preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
            booking_aggregate.booking.version,
            active_hotel_aggregate,
            room_stay_id=1,
            guest_id=2,
        )
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        response = preview_invoice(
            booking_id, client, preview_invoice_payload, show_raw_response=True
        )
        invoice_group_id = response.get('data').get('invoice_group_id')
        booking_aggregate = booking_repo.load(booking_id)
        checkout_payload = create_checkout_payload(
            booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
        )
        response = checkout_booking(client, booking_id, checkout_payload)

        booking_aggregate = booking_repo.load(booking_id)

        cns_created, cns_cancelled = [], []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                if rn_cn.status.value == 'locked':
                    cns_created.append(rn_cn.posttax_amount.amount)
                else:
                    cns_cancelled.append(rn_cn.posttax_amount.amount)
        assert cns_created == [Decimal('5.50'), Decimal('5.50')]
        assert set(cns_cancelled) == {Decimal('5.25'), Decimal('5.50')}


def test_change_room_type_with_diff_price_should_update_commission_for_changed_room(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
    create_add_room_stay_payload,
    booking_audit_trail_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        pretax_amount = 2000
        room_stay_patch_payload = create_room_type_and_price_change_payload(
            booking_aggregate.booking.version,
            "rt02",
            booking_aggregate.booking.checkin_date,
            pretax_amount,
        )

        post_room_stay_allocate_room(
            client,
            booking_id,
            booking_aggregate.room_stays[0].room_stay_id,
            room_stay_patch_payload,
        )
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('105.00')]

        audit_aggregate = booking_audit_trail_repo.load_for_booking(booking_id)
        audit = [
            ad
            for ad in audit_aggregate
            if ad.audit_trail.audit_type == AuditType.ROOM_STAY_ROOM_TYPE_CHANGED
        ][0]
        domain_events = audit.audit_trail.audit_payload['domain_events']
        ta_commission_update_event_data = [
            event
            for event in domain_events
            if event['event_type'] == DomainEvent.TA_COMMISSION_UPDATED.value
        ][0]['event_detail']
        assert ta_commission_update_event_data['old_commission_amount'] == '30.00'
        assert ta_commission_update_event_data['new_commission_amount'] == '105.00'
