import datetime
import json
from decimal import Decimal

from treebo_commons.utils import dateutils

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    cancel_booking,
    checkin_booking,
    make_booking,
)
from prometheus.itests.booking.test_booking_api import cancellation_request
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload_without_new_guest,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_package_details,
    mock_rate_manager_client,
    mock_rate_plan_details,
    mock_role_manager,
    mock_tax_calculator_service,
    mock_tenant_config,
)
from ths_common.constants.billing_constants import ChargeStatus
from ths_common.constants.booking_constants import BookingStatus, ExpenseStatus
from ths_common.constants.catalog_constants import SellerType


def create_booking_payload_with_rate_plan(create_booking_payload):
    _payload = dict(json.loads(create_booking_payload))
    _payload['room_stays'][0]['checkout_date'] = (
        dateutils.current_datetime() + datetime.timedelta(days=3)
    ).isoformat()
    prices = _payload['room_stays'][0]['prices'][0].copy()
    prices['applicable_date'] = (
        dateutils.current_datetime() + datetime.timedelta(days=1)
    ).isoformat()
    _payload['room_stays'][0]['prices'].append(prices)
    prices = _payload['room_stays'][0]['prices'][0].copy()
    prices['applicable_date'] = (
        dateutils.current_datetime() + datetime.timedelta(days=2)
    ).isoformat()
    _payload['room_stays'][0]['prices'].append(prices)
    return json.dumps({"data": _payload})


def get_addon_payload(no_days=2, charge_type='non-credit'):
    return json.dumps(
        {
            "data": {
                "addons": [
                    {
                        "added_by": "hotel",
                        "bill_to_type": "guest",
                        "start_date": dateutils.current_datetime().isoformat(),
                        "end_date": (
                            dateutils.current_datetime()
                            + datetime.timedelta(days=no_days)
                        ).isoformat(),
                        "charge_type": charge_type,
                        "name": "Breakfast",
                        "posttax_price": "50.00 INR",
                        "quantity": 1,
                        "room_stay_id": "1",
                        "is_rate_plan_addon": True,
                        "sku_id": "377",
                    },
                    {
                        "added_by": "hotel",
                        "bill_to_type": "guest",
                        "start_date": dateutils.current_datetime().isoformat(),
                        "end_date": dateutils.current_datetime().isoformat(),
                        "charge_type": charge_type,
                        "name": "Champagne",
                        "posttax_price": "70.00 INR",
                        "quantity": 1,
                        "room_stay_id": "1",
                        "is_rate_plan_addon": True,
                        "sku_id": "378",
                    },
                ]
            },
            "resource_version": 1,
        }
    )


def get_non_room_night_inclusion_addon_payload():
    return json.dumps(
        {
            "data": {
                "addons": [
                    {
                        "added_by": "hotel",
                        "bill_to_type": "guest",
                        "start_date": dateutils.current_datetime().isoformat(),
                        "end_date": dateutils.current_datetime().isoformat(),
                        "charge_type": "non-credit",
                        "name": "Buffet lunch",
                        "posttax_price": "50.00 INR",
                        "quantity": 1,
                        "room_stay_id": "1",
                        "is_rate_plan_addon": True,
                        "sku_id": "391",
                    },
                    {
                        "added_by": "hotel",
                        "bill_to_type": "guest",
                        "start_date": dateutils.current_datetime().isoformat(),
                        "end_date": dateutils.current_datetime().isoformat(),
                        "charge_type": "non-credit",
                        "name": "Buffet breakfast",
                        "posttax_price": "70.00 INR",
                        "quantity": 1,
                        "room_stay_id": "1",
                        "is_rate_plan_addon": True,
                        "sku_id": "393",
                    },
                ]
            },
            "resource_version": 1,
        }
    )


def get_room_stay_preponed_payload(start=-1, end=3):
    return json.dumps(
        {
            "data": {
                "checkin_date": (
                    dateutils.current_datetime() + datetime.timedelta(days=start)
                ).isoformat(),
                "checkout_date": (
                    dateutils.current_datetime() + datetime.timedelta(days=end)
                ).isoformat(),
                "prices": [
                    {
                        "applicable_date": (
                            dateutils.current_datetime()
                            + datetime.timedelta(days=start)
                        ).isoformat(),
                        "bill_to_type": "company",
                        "posttax_amount": 118,
                        "type": "non-credit",
                        "rate_plan_id": "1",
                        "rate_plan_reference_id": "014f54aa-60fe-4ff2-b4cc-9b5fd2933815",
                    }
                ],
            },
            "resource_version": 2,
        }
    )


def get_room_stay_preponed_with_incl_payload(start=-1, end=3):
    return json.dumps(
        {
            "data": {
                "checkin_date": (
                    dateutils.current_datetime() + datetime.timedelta(days=start)
                ).isoformat(),
                "checkout_date": (
                    dateutils.current_datetime() + datetime.timedelta(days=end)
                ).isoformat(),
                "prices": [
                    {
                        "applicable_date": (
                            dateutils.current_datetime()
                            + datetime.timedelta(days=start)
                        ).isoformat(),
                        "bill_to_type": "company",
                        "posttax_amount": 118,
                        "type": "non-credit",
                        "rate_plan_id": "1",
                        "rate_plan_reference_id": "014f54aa-60fe-4ff2-b4cc-9b5fd2933815",
                    }
                ],
                "rate_plan_inclusions": [
                    {
                        "start_date": (
                            dateutils.current_datetime()
                            + datetime.timedelta(days=start)
                        ).isoformat(),
                        "end_date": (
                            dateutils.current_datetime()
                            + datetime.timedelta(days=start)
                        ).isoformat(),
                        "posttax_amount": "50.00 INR",
                        "quantity": 1,
                        "sku_id": "391",
                    }
                ],
            },
            "resource_version": 2,
        }
    )


def get_room_stay_reduction_payload(start=0, end=1):
    return json.dumps(
        {
            "data": {
                "checkin_date": (
                    dateutils.current_datetime() + datetime.timedelta(days=start)
                ).isoformat(),
                "checkout_date": (
                    dateutils.current_datetime() + datetime.timedelta(days=end)
                ).isoformat(),
                "prices": [],
            },
            "resource_version": 2,
        }
    )


def get_room_stay_rate_plan_change_payload(start=0, end=1):
    return json.dumps(
        {
            "data": {
                "prices": [
                    {
                        "applicable_date": (
                            dateutils.current_datetime() + datetime.timedelta(days=0)
                        ).isoformat(),
                        "bill_to_type": "company",
                        "posttax_amount": 118,
                        "type": "non-credit",
                        "rate_plan_id": "1",
                        "rate_plan_reference_id": "Changed",
                    },
                    {
                        "applicable_date": (
                            dateutils.current_datetime() + datetime.timedelta(days=1)
                        ).isoformat(),
                        "bill_to_type": "company",
                        "posttax_amount": 118,
                        "type": "non-credit",
                        "rate_plan_id": "1",
                        "rate_plan_reference_id": "Changed",
                    },
                    {
                        "applicable_date": (
                            dateutils.current_datetime() + datetime.timedelta(days=2)
                        ).isoformat(),
                        "bill_to_type": "company",
                        "posttax_amount": 118,
                        "type": "non-credit",
                        "rate_plan_id": "1",
                        "rate_plan_reference_id": "Changed",
                    },
                ],
            },
            "resource_version": 1,
        }
    )


def get_room_stay_rate_plan_change_room_level_payload(start=0, end=1):
    return json.dumps(
        {
            "data": {
                "prices": [
                    {
                        "applicable_date": (
                            dateutils.current_datetime() + datetime.timedelta(days=0)
                        ).isoformat(),
                        "bill_to_type": "company",
                        "posttax_amount": 118,
                        "type": "non-credit",
                    },
                    {
                        "applicable_date": (
                            dateutils.current_datetime() + datetime.timedelta(days=1)
                        ).isoformat(),
                        "bill_to_type": "company",
                        "posttax_amount": 118,
                        "type": "non-credit",
                    },
                    {
                        "applicable_date": (
                            dateutils.current_datetime() + datetime.timedelta(days=2)
                        ).isoformat(),
                        "bill_to_type": "company",
                        "posttax_amount": 118,
                        "type": "non-credit",
                    },
                ],
                "rate_plan": {"rate_plan_reference_id": "RoomLevelChanged"},
            },
            "resource_version": 1,
        }
    )


def create_booking_with_rate_pan(
    create_booking_payload, client, include_non_room_inclusion_in_rate_plan=False
):
    with mock_role_manager() and mock_rate_manager_client(
        include_non_room_inclusion_in_rate_plan=include_non_room_inclusion_in_rate_plan
    ):
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            payload = create_booking_payload_with_rate_plan(create_booking_payload)
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            assert len(bookings_response['data']['room_stays']) > 0
    return bookings_response


def add_non_room_night_inclusions(client, booking_id):
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            addon_url = 'v2/bookings/' + booking_id + '/addons-list'
            addon_payload = get_non_room_night_inclusion_addon_payload()
            response = client.post(
                addon_url,
                data=addon_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200


def reduce_rooms_stay_by_shifting_check_in_date_by_one(client, booking_id):
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            room_stay_url = 'v1/bookings/{0}/room-stays/{1}'.format(booking_id, 1)
            room_stay_reduction_payload = get_room_stay_reduction_payload(
                start=1, end=3
            )
            response = client.patch(
                room_stay_url,
                data=room_stay_reduction_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200


def reduce_rooms_stay_by_stay_duration_update_api_shifting_check_in_date_by_one(
    client, booking_id
):
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            room_stay_url = (
                'v1/bookings/{0}/room-stays/{1}/update-stay-duration'.format(
                    booking_id, 1
                )
            )
            room_stay_reduction_payload = get_room_stay_reduction_payload(
                start=1, end=3
            )
            response = client.post(
                room_stay_url,
                data=room_stay_reduction_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200


def prepone_rooms_stay_by_stay_duration_update_api_shifting_check_in_date_by_one(
    client, booking_id, with_incl=False
):
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            room_stay_url = (
                'v1/bookings/{0}/room-stays/{1}/update-stay-duration'.format(
                    booking_id, 1
                )
            )
            room_stay_reduction_payload = (
                get_room_stay_preponed_with_incl_payload(start=-1, end=3)
                if with_incl
                else get_room_stay_preponed_payload(start=-1, end=3)
            )
            response = client.post(
                room_stay_url,
                data=room_stay_reduction_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200


def reduce_room_stay_to_one_day(client, booking_id):
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            room_stay_url = 'v1/bookings/{0}/room-stays/{1}'.format(booking_id, 1)
            room_stay_reduction_payload = get_room_stay_reduction_payload(
                start=0, end=1
            )
            # stay reduced to single day
            response = client.patch(
                room_stay_url,
                data=room_stay_reduction_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200


def change_room_stay_rate_plan(client, booking_id, room_level=False):
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(
            mocked_seller_type=SellerType.MARKETPLACE.value
        ), mock_tenant_config(
            [
                {
                    "config_name": "rate_manager_enabled",
                    "config_value": "true",
                    "value_type": "boolean",
                }
            ]
        ):
            room_stay_url = 'v1/bookings/{0}/room-stays/{1}'.format(booking_id, 1)
            room_stay_rate_plan_change_payload = (
                get_room_stay_rate_plan_change_room_level_payload(start=0, end=3)
                if room_level
                else get_room_stay_rate_plan_change_payload(start=0, end=3)
            )
            response = client.patch(
                room_stay_url,
                data=room_stay_rate_plan_change_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200


def add_addon_to_booking(client, booking_id, charge_type='non-credit'):
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            addon_url = 'v2/bookings/' + booking_id + '/addons-list'
            addon_payload = get_addon_payload(charge_type=charge_type)
            response = client.post(
                addon_url,
                data=addon_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200


def verify_rate_plan_in_created_booking(rate_plan_details):
    mocked_rate_plan_details = dict(
        mock_rate_plan_details(rate_plan_details.rate_plan_reference_id)
    )
    assert rate_plan_details.name == mocked_rate_plan_details.get('name')
    assert rate_plan_details.rate_plan_code == mocked_rate_plan_details.get(
        'short_code'
    )

    assert len(rate_plan_details.policies.cancellation_policies) == 1
    assert rate_plan_details.policies.cancellation_policies[
        0
    ].cancellation_charge_unit == mocked_rate_plan_details.get('policies').get(
        'cancellation_policies'
    )[
        0
    ].get(
        'cancellation_charge_unit'
    )
    assert rate_plan_details.policies.cancellation_policies[
        0
    ].cancellation_charge_value == mocked_rate_plan_details.get('policies').get(
        'cancellation_policies'
    )[
        0
    ].get(
        'cancellation_charge_value'
    )
    assert rate_plan_details.policies.cancellation_policies[
        0
    ].cancellation_duration_before_checkin_end == mocked_rate_plan_details.get(
        'policies'
    ).get(
        'cancellation_policies'
    )[
        0
    ].get(
        'cancellation_duration_before_checkin_end'
    )
    assert rate_plan_details.policies.cancellation_policies[
        0
    ].cancellation_duration_before_checkin_start == mocked_rate_plan_details.get(
        'policies'
    ).get(
        'cancellation_policies'
    )[
        0
    ].get(
        'cancellation_duration_before_checkin_start'
    )

    assert (
        rate_plan_details.policies.child_policy.charge_per_child
        == mocked_rate_plan_details.get('policies')
        .get('child_policy')
        .get('charge_per_child')
    )
    assert (
        rate_plan_details.policies.child_policy.child_allowed
        == mocked_rate_plan_details.get('policies')
        .get('child_policy')
        .get('child_allowed')
    )
    assert (
        rate_plan_details.policies.child_policy.unit_of_charge
        == mocked_rate_plan_details.get('policies')
        .get('child_policy')
        .get('unit_of_charge')
    )

    if rate_plan_details.policies.payment_policies:
        payment_policies = rate_plan_details.policies.payment_policies[0]
        mocked_payment_policies = mocked_rate_plan_details.get('policies').get(
            'payment_policies'
        )[0]

        assert (
            payment_policies.advance_payment_percentage
            == mocked_payment_policies.get('advance_payment_percentage')
        )

        assert (
            payment_policies.days_before_checkin_to_make_payment
            == mocked_payment_policies.get('days_before_checkin_to_make_payment')
        )

        assert payment_policies.occupancy_percentage == mocked_payment_policies.get(
            'occupancy_percentage'
        )

        assert (
            payment_policies.unit_of_payment_percentage
            == mocked_payment_policies.get('unit_of_payment_percentage')
        )

    if rate_plan_details.restrictions:
        assert (
            rate_plan_details.restrictions.maximum_los
            == mocked_rate_plan_details.get('restrictions').get('maximum_los')
        )
        assert (
            rate_plan_details.restrictions.minimum_los
            == mocked_rate_plan_details.get('restrictions').get('minimum_los')
        )
        assert (
            rate_plan_details.restrictions.minimum_abw
            == mocked_rate_plan_details.get('restrictions').get('minimum_abw')
        )

    mocked_package_details = dict(
        mock_package_details(rate_plan_details.package.package_id).get('package')
    )
    assert mocked_package_details
    assert rate_plan_details.package.package_name == mocked_package_details.get(
        'package_name'
    )

    assert len(rate_plan_details.package.inclusions) == 2
    assert rate_plan_details.package.inclusions[0].name == mocked_package_details.get(
        'inclusions'
    )[0].get('display_name')
    assert rate_plan_details.package.inclusions[0].sku_id == mocked_package_details.get(
        'inclusions'
    )[0].get('sku_id')
    assert rate_plan_details.package.inclusions[
        0
    ].frequency.count == mocked_package_details.get('inclusions')[0].get(
        'frequency'
    ).get(
        'count'
    )
    assert rate_plan_details.package.inclusions[
        0
    ].frequency.day_of_serving == mocked_package_details.get('inclusions')[0].get(
        'frequency'
    ).get(
        'day_of_serving'
    )
    assert rate_plan_details.package.inclusions[
        0
    ].frequency.frequency_type == mocked_package_details.get('inclusions')[0].get(
        'frequency'
    ).get(
        'frequency_type'
    )

    assert rate_plan_details.package.inclusions[
        0
    ].offering.offering_type == mocked_package_details.get('inclusions')[0].get(
        'offering'
    ).get(
        'offering_type'
    )
    assert rate_plan_details.package.inclusions[
        0
    ].offering.quantity == mocked_package_details.get('inclusions')[0].get(
        'offering'
    ).get(
        'offered_quantity'
    )


def test_create_booking_with_rate_plan_should_add_rate_plan_details_to_booking(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # Create booking for 3 days
    bookings_response = create_booking_with_rate_pan(create_booking_payload, client)
    booking_id = bookings_response['data']['booking_id']

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.seller_model == SellerType.MARKETPLACE

    assert len(booking_aggregate.rate_plans) == 1
    rate_plan_details = booking_aggregate.rate_plans[0]
    verify_rate_plan_in_created_booking(rate_plan_details)


def test_create_addon_to_booking_should_add_expenses(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # Create booking for 3 days
    bookings_response = create_booking_with_rate_pan(create_booking_payload, client)

    # Add 2 addons to the created booking - one addon is of full stay type, one of daily type
    booking_id = bookings_response['data']['booking_id']
    add_addon_to_booking(client, booking_id)

    # Verify if expense is added to booking
    booking = booking_repo.load(booking_id).booking
    updated_booking = booking_repo.load(booking.booking_id)
    assert len(updated_booking.expenses) == 4
    for expense in updated_booking.expenses:
        assert expense.sku_id in ['377', '378']
        assert expense.via_rate_plan == True
        assert expense.status == ExpenseStatus.CREATED


def test_create_addon_to_booking_should_add_charges(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # Create booking for 3 days
    bookings_response = create_booking_with_rate_pan(create_booking_payload, client)

    # Add 2 addons to the created booking - one addon is of full stay type, one of daily type
    booking_id = bookings_response['data']['booking_id']
    add_addon_to_booking(client, booking_id)

    booking = booking_repo.load(booking_id).booking
    # Verify if charge is added to bill
    updated_bill = bill_repo.load(booking.bill_id)
    assert updated_bill is not None
    assert len(updated_bill.charges) == 3 + 3 + 1
    for charge in updated_bill.charges:
        assert charge.posttax_amount.amount in [50, 70, 112]


def test_add_non_room_night_inclusion_should_add_expense_to_check_in_date(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # create booking with rate plan having non_room_night_inclusion
    bookings_response = create_booking_with_rate_pan(
        create_booking_payload, client, include_non_room_inclusion_in_rate_plan=True
    )

    booking_id = bookings_response['data']['booking_id']

    # Add non room night addons to booking
    add_non_room_night_inclusions(client, booking_id)

    booking = booking_repo.load(booking_id).booking
    updated_booking = booking_repo.load(booking.booking_id)

    active_expenses = []
    check_in_day = dateutils.current_datetime().day
    for expense in updated_booking.expenses:
        if expense.is_active:
            active_expenses.append(expense)
            # verify non room night inclusions are added to check_in day
            assert expense.applicable_date.day == check_in_day
    assert len(active_expenses) == 2


def test_change_check_in_date_should_shift_non_room_inclusion_to_new_check_in_date(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # create booking with rate plan having non_room_night_inclusion
    bookings_response = create_booking_with_rate_pan(
        create_booking_payload, client, include_non_room_inclusion_in_rate_plan=True
    )

    booking_id = bookings_response['data']['booking_id']
    # Add non room night addons to booking
    add_non_room_night_inclusions(client, booking_id)

    curr_check_in_day = dateutils.current_datetime().day

    # reduce stay from 3 to 2 days , check_in date is shifted by 1 day
    # verify if non room night inclusions are shifted to new check-in date
    reduce_rooms_stay_by_shifting_check_in_date_by_one(client, booking_id)

    booking = booking_repo.load(booking_id).booking
    updated_booking = booking_repo.load(booking_id)
    updated_bill = bill_repo.load(booking.bill_id)
    active_expenses = []

    # verify if non room night inclusions are shifted to new check-in date
    prev_check_in_date = curr_check_in_day
    new_check_in_day = (dateutils.current_datetime() + datetime.timedelta(days=1)).day
    for expense in updated_booking.expenses:
        if expense.is_active:
            active_expenses.append(expense)
            assert expense.applicable_date.day == new_check_in_day
    assert len(active_expenses) == 2

    active_charges = []
    for charge in updated_bill.charges:
        if charge.is_active:
            active_charges.append(charge)
            assert charge.applicable_date.day != prev_check_in_date
    assert len(active_charges) == 3 + 1


def test_change_check_in_date_via_stay_duration_should_shift_non_room_inclusion_to_new_check_in_date(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # create booking with rate plan having non_room_night_inclusion
    bookings_response = create_booking_with_rate_pan(
        create_booking_payload, client, include_non_room_inclusion_in_rate_plan=True
    )

    booking_id = bookings_response['data']['booking_id']
    # Add non room night addons to booking
    add_non_room_night_inclusions(client, booking_id)

    curr_check_in_day = dateutils.current_datetime().day

    # reduce stay from 3 to 2 days , check_in date is shifted by 1 day
    # verify if non room night inclusions are shifted to new check-in date
    reduce_rooms_stay_by_stay_duration_update_api_shifting_check_in_date_by_one(
        client, booking_id
    )

    booking = booking_repo.load(booking_id).booking
    updated_booking = booking_repo.load(booking_id)
    updated_bill = bill_repo.load(booking.bill_id)
    active_expenses = []

    # verify if non room night inclusions are shifted to new check-in date
    prev_check_in_date = curr_check_in_day
    new_check_in_day = (dateutils.current_datetime() + datetime.timedelta(days=1)).day
    for expense in updated_booking.expenses:
        if expense.is_active:
            active_expenses.append(expense)
            assert expense.applicable_date.day == new_check_in_day
    assert len(active_expenses) == 2

    active_charges = []
    for charge in updated_bill.charges:
        if charge.is_active:
            active_charges.append(charge)
            assert charge.applicable_date.day != prev_check_in_date
    assert len(active_charges) == 3 + 1


def test_prepone_check_in_date_via_stay_duration_should_cancel_non_room_inclusion_from_old_check_in_date(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # create booking with rate plan having non_room_night_inclusion
    bookings_response = create_booking_with_rate_pan(
        create_booking_payload, client, include_non_room_inclusion_in_rate_plan=True
    )

    booking_id = bookings_response['data']['booking_id']
    # Add non room night addons to booking
    add_non_room_night_inclusions(client, booking_id)

    curr_check_in_day = dateutils.current_datetime().day

    # extend stay from 2 to 3 days , check_in date is shifted by 1 day
    # verify if non room night inclusions are removed from old checkin date
    prepone_rooms_stay_by_stay_duration_update_api_shifting_check_in_date_by_one(
        client, booking_id
    )

    booking = booking_repo.load(booking_id).booking
    updated_booking = booking_repo.load(booking_id)
    updated_bill = bill_repo.load(booking.bill_id)

    prev_check_in_date = curr_check_in_day
    active_expenses = [
        expense for expense in updated_booking.expenses if expense.is_active
    ]
    assert len(active_expenses) == 0

    active_charges = []
    for charge in updated_bill.charges:
        if charge.is_active:
            active_charges.append(charge)
            if charge.is_inclusion_charge:
                assert charge.applicable_date.day != prev_check_in_date
    assert len(active_charges) == 3 + 1


def test_prepone_check_with_incl_in_date_via_stay_duration_should_cancel_non_room_inclusion_from_old_check_in_date_and_add_new(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # create booking with rate plan having non_room_night_inclusion
    bookings_response = create_booking_with_rate_pan(
        create_booking_payload, client, include_non_room_inclusion_in_rate_plan=True
    )

    booking_id = bookings_response['data']['booking_id']
    # Add non room night addons to booking
    add_non_room_night_inclusions(client, booking_id)

    curr_check_in_day = dateutils.current_datetime().day

    # extend stay from 2 to 3 days , check_in date is shifted by 1 day
    # verify if non room night inclusions are removed from old checkin date
    # here we are sending incl for new dates, check if expenses and charges are getting added for that
    prepone_rooms_stay_by_stay_duration_update_api_shifting_check_in_date_by_one(
        client, booking_id, with_incl=True
    )

    booking = booking_repo.load(booking_id).booking
    updated_booking = booking_repo.load(booking_id)
    updated_bill = bill_repo.load(booking.bill_id)

    # verify if non room night inclusions are shifted to new check-in date
    prev_check_in_date = curr_check_in_day
    active_expenses = [
        expense for expense in updated_booking.expenses if expense.is_active
    ]
    assert len(active_expenses) == 1

    active_charges = []
    for charge in updated_bill.charges:
        if charge.is_active:
            active_charges.append(charge)
            if charge.is_inclusion_charge:
                assert charge.applicable_date.day != prev_check_in_date
    assert len(active_charges) == 3 + 1 + 1


def test_change_check_in_date_should_remove_charge_from_old_check_in_date(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # create booking with rate plan having non_room_night_inclusion
    bookings_response = create_booking_with_rate_pan(
        create_booking_payload, client, include_non_room_inclusion_in_rate_plan=True
    )

    booking_id = bookings_response['data']['booking_id']
    # Add non room night addons to booking
    add_non_room_night_inclusions(client, booking_id)

    curr_check_in_day = dateutils.current_datetime().day

    # reduce stay from 3 to 2 days , check_in date is shifted by 1 day
    # verify if non room night inclusions are shifted to new check-in date
    reduce_rooms_stay_by_shifting_check_in_date_by_one(client, booking_id)

    booking = booking_repo.load(booking_id).booking
    updated_bill = bill_repo.load(booking.bill_id)

    # verify if charges are not present for old check_in date
    prev_check_in_date = curr_check_in_day
    active_charges = []
    for charge in updated_bill.charges:
        if charge.is_active:
            active_charges.append(charge)
            assert charge.applicable_date.day != prev_check_in_date
    assert len(active_charges) == 3 + 1


def test_booking_cancellation_should_consume_cancellation_charge_as_per_policy_is_present_in_rate_plan(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # create booking with rate plan having non_room_night_inclusion
    bookings_response = create_booking_with_rate_pan(
        create_booking_payload, client, include_non_room_inclusion_in_rate_plan=True
    )

    hotel_config = [
        {
            'config_name': 'rate_manager_enabled',
            'config_value': 'true',
            'value_type': 'json',
        }
    ]

    # cancel the booking
    with mock_tenant_config(hotel_level_config=hotel_config):
        booking_id = bookings_response['data']['booking_id']
        booking_aggregate = booking_repo.load(booking_id)
        cancel_payload = cancellation_request(booking_aggregate.booking.version)
        cancel_booking(client, booking_id, cancel_payload)

    booking = booking_repo.load(booking_id).booking
    updated_bill = bill_repo.load(booking.bill_id)

    consumed_charges = []
    cancelled_charges = []
    # check if cancellation charges are consumed as per cancellation policy
    charges = updated_bill.charges
    for charge in charges:
        if charge.status == ChargeStatus.CONSUMED:
            # 30% charge for 3 days
            assert charge.posttax_amount.amount == Decimal('110.88')
            consumed_charges.append(charge)
        elif charge.status == ChargeStatus.CANCELLED:
            cancelled_charges.append(charge)
        else:
            assert (
                False
            ), 'Raise error active charges are not allowed after cancellation'
    assert len(charges) == 4
    assert len(consumed_charges) == 1
    assert len(cancelled_charges) == 3


def test_update_room_stay_date_should_remove_expenses_from_old_dates(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # create booking with rate plan having non_room_night_inclusion
    bookings_response = create_booking_with_rate_pan(create_booking_payload, client)
    booking_id = bookings_response['data']['booking_id']
    add_addon_to_booking(client, booking_id)

    # modify booking by reducing room stays(checkout dates is shifted back) and verify inclusions are removed
    reduce_room_stay_to_one_day(client, booking_id)

    # Verify if active expense is reduced
    booking = booking_repo.load(booking_id).booking
    updated_booking = booking_repo.load(booking.booking_id)
    updated_bill = bill_repo.load(booking.bill_id)

    # Verify if active expense is cancelled properly
    active_expenses = []
    cancelled_expenses = []
    cancelled_days = [
        (dateutils.current_datetime() + datetime.timedelta(days=1)).day,
        (dateutils.current_datetime() + datetime.timedelta(days=2)).day,
    ]
    allowed_days = [
        (dateutils.current_datetime() + datetime.timedelta(days=delta)).day
        for delta in range(1)
    ]
    for expense in updated_booking.expenses:
        if expense.is_active:
            assert expense.applicable_date.day in allowed_days
            active_expenses.append(expense)
        else:
            assert expense.applicable_date.day in cancelled_days
            cancelled_expenses.append(expense)
    active_expenses = [
        expense for expense in updated_booking.expenses if expense.is_active
    ]
    assert len(updated_booking.expenses) == 4
    assert len(active_expenses) == 2
    assert len(cancelled_expenses) == 2

    active_charges = [charge for charge in updated_bill.charges if charge.is_active]
    assert len(active_charges) == 3


def test_update_room_stay_with_new_rate_plan_should_update_charges(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    bookings_response = create_booking_with_rate_pan(create_booking_payload, client)
    booking_id = bookings_response['data']['booking_id']

    # modify room stay rate plan
    change_room_stay_rate_plan(client, booking_id)

    # Verify if rate plan got  updated in charges
    booking = booking_repo.load(booking_id).booking
    updated_bill = bill_repo.load(booking.bill_id)
    assert updated_bill is not None

    for charge in updated_bill.charges:
        item = charge.item
        assert "Changed" in item.details["rate_plan_name"]


def test_update_room_stay_with_new_rate_plan_room_level_should_update_charges(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    bookings_response = create_booking_with_rate_pan(create_booking_payload, client)
    booking_id = bookings_response['data']['booking_id']

    # modify room stay rate plan
    change_room_stay_rate_plan(client, booking_id, room_level=True)

    # Verify if rate plan got  updated in charges
    booking = booking_repo.load(booking_id).booking
    updated_bill = bill_repo.load(booking.bill_id)
    assert updated_bill is not None

    for charge in updated_bill.charges:
        item = charge.item
        assert "RoomLevelChanged" in item.details["rate_plan_name"]


def test_create_booking_with_rate_plan_should_have_print_rate_and_suppress_rate(
    create_booking_payload, client
):
    url = 'v1/bookings'
    payload = create_booking_payload_with_rate_plan(create_booking_payload)
    response = client.post(
        url,
        data=payload,
        content_type='application/json',
        headers={'X-User-Type': 'super-admin'},
    )
    assert response.status_code == 200
    bookings_response = json.loads(response.data.decode('utf-8'))
    booking_id = bookings_response['data']['booking_id']
    url = 'v2/bookings/' + booking_id + '?show_bill_summary=true'
    response = client.get(
        url,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    booking_details_response = json.loads(response.data.decode('utf-8'))
    assert 'print_rate' in booking_details_response['data']['room_rate_plans'][0]
    assert 'suppress_rate' in booking_details_response['data']['room_rate_plans'][0]


def test_create_booking_with_current_and_future_date_rooms(
    client,
    create_booking_payload,
    booking_repo,
    bill_repo,
    active_hotel_aggregate,
    hotel_repo,
):
    """Test creating a booking with two rooms - one with current date checkin and one with future date checkin.
    Then check in on the current date room and change the rate plan for the future scheduled room.
    """
    current_date = dateutils.current_datetime()
    future_date = dateutils.add(current_date, days=7)

    booking_payload_dict = create_new_booking_payload(
        checkin_date=current_date,
        checkout_date=dateutils.add(current_date, days=2),
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
    )

    future_room = booking_payload_dict['room_stays'][0].copy()
    future_room['checkin_date'] = future_date.isoformat()
    future_room['checkout_date'] = dateutils.add(future_date, days=2).isoformat()

    # Update prices for the future room
    future_room['prices'] = []
    for i in range(2):
        price = {
            "applicable_date": dateutils.add(future_date, days=i).isoformat(),
            "bill_to_type": "company",
            "pretax_amount": "100.00",
            "type": "non-credit",
            "rate_plan_id": "1",
            "rate_plan_reference_id": "014f54aa-60fe-4ff2-b4cc-9b5fd2933815",
        }
        future_room['prices'].append(price)

    # Add the future room to the booking payload
    booking_payload_dict['room_stays'].append(future_room)

    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            booking_id = make_booking(client, {"data": booking_payload_dict})

            assert booking_id is not None

            url = f'v2/bookings/{booking_id}?show_bill_summary=true'
            response = client.get(
                url,
                content_type="application/json",
                headers={"X-User-Type": "super-admin"},
            )
            booking_details = json.loads(response.data.decode('utf-8'))

            assert len(booking_details['data']['room_stays']) == 2

            room_stays = booking_details['data']['room_stays']
            current_date_room_stay = None
            future_date_room_stay = None

            for room_stay in room_stays:
                if current_date.strftime('%Y-%m-%d') in room_stay['checkin_date']:
                    current_date_room_stay = room_stay
                elif future_date.strftime('%Y-%m-%d') in room_stay['checkin_date']:
                    future_date_room_stay = room_stay

            assert (
                current_date_room_stay is not None
            ), "Current date room stay not found"
            assert future_date_room_stay is not None, "Future date room stay not found"

            roll_over_business_date(active_hotel_aggregate, hotel_repo)

            booking_aggregate = booking_repo.load(booking_id)
            current_room_stay_id = current_date_room_stay['room_stay_id']

            checkin_payload = create_checkin_payload_without_new_guest(
                booking_version=booking_aggregate.booking.version,
                active_hotel_aggregate=active_hotel_aggregate,
                room_stay_id=current_room_stay_id,
                room_id="15",
            )

            try:
                checkin_booking(client, booking_id, checkin_payload)
            except AssertionError as e:
                raise e

            response = client.get(
                url,
                content_type="application/json",
                headers={"X-User-Type": "super-admin"},
            )
            booking_details = json.loads(response.data.decode('utf-8'))

            for room_stay in booking_details['data']['room_stays']:
                if room_stay['room_stay_id'] == current_room_stay_id:
                    assert (
                        room_stay['status'] == 'checked_in'
                    ), "Room was not checked in successfully"

            booking_aggregate = booking_repo.load(booking_id)
            future_room_stay_id = future_date_room_stay['room_stay_id']

            rate_plan_change_payload = {
                "data": {
                    "prices": [
                        {
                            "applicable_date": dateutils.add(
                                future_date, days=i
                            ).isoformat(),
                            "bill_to_type": "company",
                            "posttax_amount": 150,
                            "type": "non-credit",
                            "rate_plan_id": "1",
                            "rate_plan_reference_id": "Changed",
                        }
                        for i in range(2)
                    ],
                },
                "resource_version": booking_aggregate.booking.version,
            }

            with mock_role_manager(), mock_rate_manager_client():
                with mock_catalog_client(
                    mocked_seller_type=SellerType.MARKETPLACE.value
                ), mock_tenant_config(
                    [
                        {
                            "config_name": "rate_manager_enabled",
                            "config_value": "true",
                            "value_type": "boolean",
                        }
                    ]
                ):
                    room_stay_url = (
                        f'v1/bookings/{booking_id}/room-stays/{future_room_stay_id}'
                    )
                    response = client.patch(
                        room_stay_url,
                        data=json.dumps(rate_plan_change_payload),
                        content_type='application/json',
                        headers={'X-User-Type': 'super-admin'},
                    )
                    assert response.status_code == 200

            response = client.get(
                url,
                content_type="application/json",
                headers={"X-User-Type": "super-admin"},
            )
            booking_details = json.loads(response.data.decode('utf-8'))

            print(
                "Room rate plans structure:",
                json.dumps(
                    booking_details['data'].get('room_rate_plans', []), indent=2
                ),
            )

            bill_id = booking_details['data']['bill_id']
            bill_aggregate = bill_repo.load(bill_id)

            rate_plan_changed = False

            print("\nAll charges in bill after rate plan change:")
            for i, charge in enumerate(bill_aggregate.charges):
                if str(charge.item.details.get('room_stay_id')) == str(
                    future_room_stay_id
                ):
                    if "Changed" in charge.item.details.get(
                        'rate_plan_reference_id', ''
                    ):
                        rate_plan_changed = True
                        print(
                            f"  Rate plan was changed to: {charge.item.details.get('rate_plan_reference_id')}"
                        )
                        break

            if not rate_plan_changed:
                for charge in bill_aggregate.charges:
                    if "Changed" in charge.item.details.get(
                        'rate_plan_reference_id', ''
                    ):
                        print(
                            f"Found 'Changed' rate plan in charge with room stay ID: {charge.item.details.get('room_stay_id')}"
                        )
                        rate_plan_changed = True
                        break

            assert (
                rate_plan_changed
            ), "Rate plan was not changed successfully for the future room"
