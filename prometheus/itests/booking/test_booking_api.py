import datetime
import json
import random
from copy import copy
from datetime import time, timedelta
from itertools import chain

from treebo_commons.utils import dateutils

from prometheus.infrastructure.database import db_engine
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    cancel_booking,
    checkin_booking,
    confirm_booking,
    make_booking,
)
from prometheus.itests.api_wrappers.booking_wrappers import get_booking, patch_booking
from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    booking_cancellation_request,
    create_checkin_payload_with_edit_guest_details,
    create_confirm_payload,
    create_update_customer_payload,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v2_request,
)
from prometheus.tests.factories.aggregate_factories import (
    BillFactory,
    BookingAggregateFactory,
    RoomTypeInventoryAggregateFactory,
)
from prometheus.tests.factories.entity_factories import (
    CustomerFactory,
    RoomTypeInventoryAvailabilityFactory,
)
from prometheus.tests.mockers import (
    mock_aws_service_client,
    mock_catalog_client,
    mock_guarantee_enabled_config,
    mock_package_details,
    mock_rate_manager_client,
    mock_rate_plan_details,
    mock_role_manager,
    mock_tax_calculator_service,
    mock_template_service,
    mock_tenant_config,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    BilledEntityStatus,
    ChargeStatus,
)
from ths_common.constants.booking_constants import (
    BookingActions,
    BookingChannels,
    BookingStatus,
    BookingSubChannels,
    ExpenseStatus,
    GuaranteeTypes,
)
from ths_common.constants.catalog_constants import SellerType
from ths_common.value_objects import GuaranteeInformation, Name, PhoneNumber


def room_type_inventory_for_daterange(start_date, end_date, inventory_per_day):
    room_type_inventory_availabilities = list()
    for date_ in dateutils.date_range(start_date, end_date):
        date = dateutils.to_date(date_)
        room_type_inventory_availabilities.append(
            RoomTypeInventoryAvailabilityFactory(
                date=date, actual_count=inventory_per_day
            )
        )

    return RoomTypeInventoryAggregateFactory(
        room_type_inventory_availabilities=room_type_inventory_availabilities
    )


def test_booking_search(booking_repo, bill_repo, client, hotel_repo):
    bill_aggregate = BillFactory()
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(
        booking__bill_id=bill_aggregate.bill.bill_id
    )
    booking_repo.save(booking_aggregate)

    db_engine.get_scoped_session().commit()

    booking = booking_aggregate.booking

    search_start_date = booking.checkin_date - timedelta(days=2)
    search_end_date = booking.checkout_date + timedelta(days=2)
    hotel_id = booking.hotel_id

    # Search for house view
    url = (
        'v1/bookings'
        + '?hotel_id='
        + str(hotel_id)
        + '&from_date='
        + dateutils.date_to_ymd_str(search_start_date)
        + '&to_date='
        + dateutils.date_to_ymd_str(search_end_date)
        + '&status=confirmed'
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = json.loads(response.data.decode('utf-8'))
    assert len(bookings_response['data']['bookings'])

    # Search by reference number
    url = (
        'v1/bookings'
        + '?hotel_id='
        + str(hotel_id)
        + '&query='
        + booking.reference_number
    )

    # TODO: Fails if we remove session in teardown_request of Flask.
    # Works when we remove session in teardown_appcontext
    # This could be because the "app" used in conftest, itself registers a request context, and in turn an app context
    # So previous client call, doesn't create new app context automatically, but probably does create and pop the
    # request
    # context in this request, which removes session, and thus the booking. Which is not found in the below API call
    # NOTE: To make it work, we can add db_engine.get_scoped_session().commit() above, to actually commit the booking
    # and bill, thus remove session won't rollback these changes
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = json.loads(response.data.decode('utf-8'))
    assert bookings_response['data']['bookings']
    assert not bookings_response['data']['bookings'][0]['room_stays'][0][
        'is_overflow'
    ], "Room stay must not be an overflow"


def test_booking_create_with_overflow(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    # d1, d2 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
    )
    payload = {"data": create_booking_payload}
    make_booking(client, payload)

    # d3, d4 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=12)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=14)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-2',
    )
    payload = {"data": create_booking_payload}
    make_booking(client, payload)

    # d1, d2, d3, d4 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=14)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-3',
    )
    payload = {"data": create_booking_payload}
    make_booking(client, payload)

    # d4, d5 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-4',
    )
    payload = {"data": create_booking_payload}
    make_booking(client, payload)

    room_type_inventory_aggregates = room_type_inventory_repo.load_multiple(
        active_hotel_aggregate.hotel.hotel_id,
        dateutils.add(dateutils.current_date(), days=10),
        dateutils.add(dateutils.current_date(), days=15),
        room_type_ids=['rt01'],
    )

    for availability in room_type_inventory_aggregates[
        0
    ].room_type_inventory_availabilities:
        availability.update_count(0)

    room_type_inventory_repo.update(room_type_inventory_aggregates[0])

    # d1, d2, d3 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=13)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-5',
        channel='direct',
    )
    payload = {"data": create_booking_payload}

    # d2, d3, d4, d5 - room nights
    room_stay_two = payload['data']['room_stays'][0].copy()
    room_stay_two['checkin_date'] = dateutils.add(
        dateutils.current_datetime(), days=11
    ).isoformat()
    room_stay_two['checkout_date'] = dateutils.add(
        dateutils.current_datetime(), days=15
    ).isoformat()
    room_stay_two['prices'] = [
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=11
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=12
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=13
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=14
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
    ]
    payload['data']['room_stays'].append(room_stay_two)
    response = make_booking(
        client=client, payload=payload, return_complete_response=True
    )
    assert response['data']['room_stays'][0]['is_overflow'] is True
    assert response['data']['room_stays'][1]['is_overflow'] is True
    rs_overflows = room_stay_overflow_repo.get_overflowed_room_stays_for_booking(
        response['data']['booking_id']
    )
    assert len(rs_overflows) == 2
    assert rs_overflows[0].room_stay_overflow.room_stay_id == 1
    assert rs_overflows[0].room_stay_overflow.start_date == dateutils.to_date(
        dateutils.add(dateutils.current_datetime(), days=10)
    )
    assert rs_overflows[0].room_stay_overflow.end_date == dateutils.to_date(
        dateutils.add(dateutils.current_datetime(), days=12)
    )


def test_booking_create(create_booking_payload, client, bill_repo, booking_repo):
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            payload = json.dumps({"data": json.loads(create_booking_payload)})
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            assert len(bookings_response['data']['room_stays']) > 0

    booking_id = bookings_response['data']['booking_id']
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.seller_model == SellerType.MARKETPLACE

    bill_id = bookings_response['data']['bill_id']
    bill_aggregate = bill_repo.load(bill_id)
    assert all(
        charge.applicable_date.time() == time(23, 59)
        for charge in bill_aggregate.charges
    )


def test_booking_create_with_rate_plan_details(
    create_booking_payload, client, bill_repo, booking_repo, checkin_addon_dict_for_v2
):
    # Create booking for 3 days
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            _payload = dict(json.loads(create_booking_payload))
            _payload['room_stays'][0]['checkout_date'] = (
                dateutils.current_datetime() + datetime.timedelta(days=3)
            ).isoformat()
            prices = _payload['room_stays'][0]['prices'][0].copy()
            prices['applicable_date'] = (
                dateutils.current_datetime() + datetime.timedelta(days=1)
            ).isoformat()
            _payload['room_stays'][0]['prices'].append(prices)
            prices = _payload['room_stays'][0]['prices'][0].copy()
            prices['applicable_date'] = (
                dateutils.current_datetime() + datetime.timedelta(days=2)
            ).isoformat()
            _payload['room_stays'][0]['prices'].append(prices)

            payload = json.dumps({"data": _payload})
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            assert len(bookings_response['data']['room_stays']) > 0

    # Add 2 addons to the created booking - one addon is of full stay type, one of daily type
    booking_id = bookings_response['data']['booking_id']
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            addon_url = 'v2/bookings/' + booking_id + '/addons-list'

            addon_payload = json.dumps(
                {
                    "data": {
                        "addons": [
                            {
                                "added_by": "hotel",
                                "bill_to_type": "guest",
                                "start_date": dateutils.current_datetime().isoformat(),
                                "end_date": (
                                    dateutils.current_datetime()
                                    + datetime.timedelta(days=2)
                                ).isoformat(),
                                "charge_type": "non-credit",
                                "name": "Breakfast",
                                "posttax_price": "50.00 INR",
                                "quantity": 1,
                                "room_stay_id": "1",
                                "is_rate_plan_addon": True,
                                "sku_id": "377",
                            },
                            {
                                "added_by": "hotel",
                                "bill_to_type": "guest",
                                "start_date": dateutils.current_datetime().isoformat(),
                                "end_date": dateutils.current_datetime().isoformat(),
                                "charge_type": "non-credit",
                                "name": "Champagne",
                                "posttax_price": "70.00 INR",
                                "quantity": 1,
                                "room_stay_id": "1",
                                "is_rate_plan_addon": True,
                                "sku_id": "378",
                            },
                        ]
                    },
                    "resource_version": 1,
                }
            )
            response = client.post(
                addon_url,
                data=addon_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200

            # Verify if expense is added to booking
            booking = booking_repo.load(booking_id).booking
            updated_booking = booking_repo.load(booking.booking_id)
            assert len(updated_booking.expenses) == 4
            for expense in updated_booking.expenses:
                assert expense.sku_id in ['377', '378']
                assert expense.via_rate_plan == True
                assert expense.status == ExpenseStatus.CREATED

            # Verify if charge is added to bill
            updated_bill = bill_repo.load(booking.bill_id)
            assert updated_bill is not None
            assert (
                len([c for c in updated_bill.charges if c.item.name == 'RoomStay']) == 3
            )
            assert (
                len([c for c in updated_bill.charges if c.item.name == 'Champagne'])
                == 1
            )
            assert (
                len([c for c in updated_bill.charges if c.item.name == 'Breakfast'])
                == 3
            )

            assert {
                c.posttax_amount.amount
                for c in updated_bill.charges
                if c.item.name == 'RoomStay'
            } == {
                112
            }, "Room Stay charge amount not correct. All room night charges should be Rs 112"
            assert {
                c.posttax_amount.amount
                for c in updated_bill.charges
                if c.item.name == 'Champagne'
            } == {
                70
            }, "Champagne inclusion charge amount not correct. All inclusion charge should be Rs 70"
            assert {
                c.posttax_amount.amount
                for c in updated_bill.charges
                if c.item.name == 'Breakfast'
            } == {
                50
            }, "Breakfast inclusion charge amount not correct. All inclusion charge should be Rs 50"

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.seller_model == SellerType.MARKETPLACE

    assert len(booking_aggregate.rate_plans) == 1
    rate_plan_details = booking_aggregate.rate_plans[0]

    mocked_rate_plan_details = dict(
        mock_rate_plan_details(rate_plan_details.rate_plan_reference_id)
    )
    assert rate_plan_details.name == mocked_rate_plan_details.get('name')
    assert rate_plan_details.rate_plan_code == mocked_rate_plan_details.get(
        'short_code'
    )

    assert len(rate_plan_details.policies.cancellation_policies) == 1
    assert rate_plan_details.policies.cancellation_policies[
        0
    ].cancellation_charge_unit == mocked_rate_plan_details.get('policies').get(
        'cancellation_policies'
    )[
        0
    ].get(
        'cancellation_charge_unit'
    )
    assert rate_plan_details.policies.cancellation_policies[
        0
    ].cancellation_charge_value == mocked_rate_plan_details.get('policies').get(
        'cancellation_policies'
    )[
        0
    ].get(
        'cancellation_charge_value'
    )
    assert rate_plan_details.policies.cancellation_policies[
        0
    ].cancellation_duration_before_checkin_end == mocked_rate_plan_details.get(
        'policies'
    ).get(
        'cancellation_policies'
    )[
        0
    ].get(
        'cancellation_duration_before_checkin_end'
    )
    assert rate_plan_details.policies.cancellation_policies[
        0
    ].cancellation_duration_before_checkin_start == mocked_rate_plan_details.get(
        'policies'
    ).get(
        'cancellation_policies'
    )[
        0
    ].get(
        'cancellation_duration_before_checkin_start'
    )

    assert (
        rate_plan_details.policies.child_policy.charge_per_child
        == mocked_rate_plan_details.get('policies')
        .get('child_policy')
        .get('charge_per_child')
    )
    assert (
        rate_plan_details.policies.child_policy.child_allowed
        == mocked_rate_plan_details.get('policies')
        .get('child_policy')
        .get('child_allowed')
    )
    assert (
        rate_plan_details.policies.child_policy.unit_of_charge
        == mocked_rate_plan_details.get('policies')
        .get('child_policy')
        .get('unit_of_charge')
    )

    payment_policies = rate_plan_details.policies.payment_policies[0]
    mocked_payment_policies = mocked_rate_plan_details.get('policies').get(
        'payment_policies'
    )[0]

    assert payment_policies.advance_payment_percentage == mocked_payment_policies.get(
        'advance_payment_percentage'
    )
    assert (
        payment_policies.days_before_checkin_to_make_payment
        == mocked_payment_policies.get('days_before_checkin_to_make_payment')
    )
    assert payment_policies.occupancy_percentage == mocked_payment_policies.get(
        'occupancy_percentage'
    )
    assert payment_policies.unit_of_payment_percentage == mocked_payment_policies.get(
        'unit_of_payment_percentage'
    )

    if rate_plan_details.restrictions:
        assert (
            rate_plan_details.restrictions.maximum_los
            == mocked_rate_plan_details.get('restrictions').get('maximum_los')
        )
        assert (
            rate_plan_details.restrictions.minimum_los
            == mocked_rate_plan_details.get('restrictions').get('minimum_los')
        )
        assert (
            rate_plan_details.restrictions.minimum_abw
            == mocked_rate_plan_details.get('restrictions').get('minimum_abw')
        )

    mocked_package_details = dict(
        mock_package_details(rate_plan_details.package.package_id).get('package')
    )
    assert mocked_package_details
    assert rate_plan_details.package.package_name == mocked_package_details.get(
        'package_name'
    )

    assert len(rate_plan_details.package.inclusions) == 2
    assert rate_plan_details.package.inclusions[0].name == mocked_package_details.get(
        'inclusions'
    )[0].get('display_name')
    assert rate_plan_details.package.inclusions[0].sku_id == mocked_package_details.get(
        'inclusions'
    )[0].get('sku_id')
    assert rate_plan_details.package.inclusions[
        0
    ].frequency.count == mocked_package_details.get('inclusions')[0].get(
        'frequency'
    ).get(
        'count'
    )
    assert rate_plan_details.package.inclusions[
        0
    ].frequency.day_of_serving == mocked_package_details.get('inclusions')[0].get(
        'frequency'
    ).get(
        'day_of_serving'
    )
    assert rate_plan_details.package.inclusions[
        0
    ].frequency.frequency_type == mocked_package_details.get('inclusions')[0].get(
        'frequency'
    ).get(
        'frequency_type'
    )

    assert rate_plan_details.package.inclusions[
        0
    ].offering.offering_type == mocked_package_details.get('inclusions')[0].get(
        'offering'
    ).get(
        'offering_type'
    )
    assert rate_plan_details.package.inclusions[
        0
    ].offering.quantity == mocked_package_details.get('inclusions')[0].get(
        'offering'
    ).get(
        'offered_quantity'
    )

    expense_item_sku_id_wise_name = {
        expense.sku_id: expense.sku_name for expense in updated_booking.expenses
    }
    for inclusion in rate_plan_details.package.inclusions:
        assert inclusion.sku_id in expense_item_sku_id_wise_name.keys()
        assert inclusion.name == expense_item_sku_id_wise_name[inclusion.sku_id]


def test_booking_search_by_attributes(booking_repo, bill_repo, client, hotel_repo):
    # Create a reserved booking
    bill_aggregate = BillFactory()
    bill_repo.save(bill_aggregate)

    reserved_booking = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(reserved_booking)

    # Required so that session removed in request context of app, doesn't remove these data. Since 2 client calls are
    # made in this test
    # If we remove session in teardown_appcontext instead, then this commit is not required
    db_engine.get_scoped_session().commit()

    booking = reserved_booking.booking
    hotel_id = booking.hotel_id

    # Should be able to search by part_checkin status
    url = 'v1/bookings' + '?hotel_id=' + str(hotel_id) + '&status=part_checked_in'
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = json.loads(response.data.decode('utf-8'))
    assert len(bookings_response['data']['bookings']) == 0

    # Should be able to search by bil_id
    url = 'v1/bookings' + '?hotel_id=' + str(hotel_id) + '&bill_id=' + booking.bill_id
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = json.loads(response.data.decode('utf-8'))
    assert len(bookings_response['data']['bookings']) == 1


def test_booking_get_by_id(booking_repo, bill_repo, client, hotel_repo):
    bill_aggregate = BillFactory()
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(booking_aggregate)

    booking = booking_aggregate.booking

    url = 'v1/bookings/' + booking.booking_id
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    room_stays = response.json.get('data').get('room_stays')
    assert 'disallow_charge_addition' in room_stays[0]
    assert room_stays[0].get('disallow_charge_addition') is False


def _test_booking_search_by_customer(
    active_hotel_aggregate, booking_repo, bill_repo, client
):
    bill_aggregate = BillFactory()
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(booking_aggregate)

    booking = booking_aggregate.booking
    customer = booking_aggregate.customers[0]
    second_customer = CustomerFactory(
        email="<EMAIL>",
        phone=PhoneNumber("999"),
        first_name="Jon",
        last_name="Snow",
        external_ref_id="kinginthenorth",
    )
    second_booking = BookingAggregateFactory(
        customers=[second_customer], booking__booking_id='124'
    )
    booking_repo.save(second_booking)

    # Search by email
    url = 'v1/bookings' + '?hotel_id={0}&query={1}'.format(
        active_hotel_aggregate.hotel.hotel_id, customer.email
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = response.json
    assert len(bookings_response['data']['bookings']) == 1
    assert bookings_response['data']['bookings'][0]['booking_id'] == booking.booking_id

    # Search by phone number
    url = 'v1/bookings' + '?hotel_id={0}&query={1}'.format(
        active_hotel_aggregate.hotel.hotel_id, customer.phone.number
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = response.json
    assert len(bookings_response['data']['bookings']) == 1
    assert bookings_response['data']['bookings'][0]['booking_id'] == booking.booking_id

    # Search by first name prefix
    url = 'v1/bookings' + '?hotel_id={0}&query={1}'.format(
        active_hotel_aggregate.hotel.hotel_id, customer.first_name[:3]
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = response.json
    assert len(bookings_response['data']['bookings']) == 1
    assert bookings_response['data']['bookings'][0]['booking_id'] == booking.booking_id

    # Search by last name prefix
    url = 'v1/bookings' + '?hotel_id={0}&query={1}'.format(
        active_hotel_aggregate.hotel.hotel_id,
        customer.first_name + ' ' + customer.last_name[:3],
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = response.json
    assert len(bookings_response['data']['bookings']) == 1
    assert bookings_response['data']['bookings'][0]['booking_id'] == booking.booking_id

    # Search by no args should give everything
    url = 'v1/bookings' + '?hotel_id={0}'.format(active_hotel_aggregate.hotel.hotel_id)
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = response.json
    assert len(bookings_response['data']['bookings']) == 2
    assert bookings_response['data']['total'] == 2

    second_customer = CustomerFactory(
        email="<EMAIL>",
        phone=PhoneNumber("999"),
        first_name="Jon",
        last_name="Snow",
        external_ref_id="kinginthenorth",
    )
    second_customer.deleted = True
    second_booking = BookingAggregateFactory(
        customers=[second_customer], booking__booking_id='125'
    )
    booking_repo.save(second_booking)

    # Search by last name prefix
    url = 'v1/bookings' + '?hotel_id={0}&query={1}'.format(
        active_hotel_aggregate.hotel.hotel_id,
        customer.first_name + ' ' + customer.last_name[:3],
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = response.json
    assert len(bookings_response['data']['bookings']) == 1
    assert bookings_response['data']['bookings'][0]['booking_id'] != '125'


def test_booking_search_gives_400_if_limit_or_offset_is_negative(client):
    bad_urls = ['v1/bookings?offset=-1', 'v1/bookings?limit=-1']

    for bad_url in bad_urls:
        response = client.get(bad_url, headers={"X-User-Type": "cr-team"})
        assert response.status_code == 400


def test_booking_search_gives_400_if_invalid_keys_passed(client):
    invalid_search_url = 'v1/bookings?xyz=something'

    response = client.get(invalid_search_url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 400


def cancellation_request(booking_version):
    return {
        "data": {
            "action_type": "cancel",
            "payload": {"cancel": {"cancellation_reason": "winter is coming"}},
        },
        "resource_version": booking_version,
    }


def noshow_request(booking_version):
    return {
        "data": {
            "action_type": "noshow",
            "payload": {"noshow": {"noshow_reason": "winter is coming"}},
        },
        "resource_version": booking_version,
    }


def test_booking_create_will_default_checkin_checkout_times_to_hotels(
    active_hotel_aggregate, create_booking_payload, client, booking_repo
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})

    booking_aggregate = booking_repo.load(booking_id)
    assert (
        str(booking_aggregate.booking.checkin_date.time())
        == active_hotel_aggregate.hotel.checkin_time
    )
    assert (
        str(booking_aggregate.booking.checkout_date.time())
        == active_hotel_aggregate.hotel.checkout_time
    )
    assert all(
        str(rs.checkin_date.time()) == active_hotel_aggregate.hotel.checkin_time
        for rs in booking_aggregate.room_stays
    )
    assert all(
        str(rs.checkout_date.time()) == active_hotel_aggregate.hotel.checkout_time
        for rs in booking_aggregate.room_stays
    )

    guest_stays = chain(*[rs.guest_stays for rs in booking_aggregate.room_stays])
    assert all(
        str(gs.checkin_date.time()) == active_hotel_aggregate.hotel.checkin_time
        for gs in guest_stays
    )
    assert all(
        str(gs.checkout_date.time()) == active_hotel_aggregate.hotel.checkout_time
        for gs in guest_stays
    )


def test_booking_cancel(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    booking_action_repo,
    booking_audit_trail_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    active_charges_before_cancellation = [
        charge.charge_id
        for charge in bill_aggregate.charges
        if not charge.status == ChargeStatus.CANCELLED
    ]

    cancel_payload = cancellation_request(booking_aggregate.booking.version)

    with mock_catalog_client(
        mocked_seller_type=SellerType.MARKETPLACE.value
    ), mock_role_manager(), mock_rate_manager_client():
        url = f'v1/bookings/{booking_id}/actions'
        payload = json.dumps(cancel_payload)
        cancel_response = client.post(
            url,
            data=payload,
            content_type='application/json',
            headers={'X-User-Type': 'super-admin', 'X-User': 'someone'},
        )
        assert cancel_response.status_code == 200

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CANCELLED
    assert booking_aggregate.booking.cancellation_datetime is not None
    assert all(
        rs.status == BookingStatus.CANCELLED for rs in booking_aggregate.room_stays
    )
    assert booking_aggregate.booking.cancellation_reason == "winter is coming"
    assert all(
        rs.cancellation_date == active_hotel_aggregate.hotel.current_business_date
        for rs in booking_aggregate.room_stays
    )

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert all(
        charge.status == ChargeStatus.CANCELLED
        for charge in bill_aggregate.charges
        if charge.charge_id in active_charges_before_cancellation
    )

    booking_action_aggregates = booking_action_repo.load_all_actions_for_booking(
        booking_id
    )
    assert len(booking_action_aggregates) == 1
    assert (
        booking_action_aggregates[0].booking_action.action_type == BookingActions.CANCEL
    )

    audit_trail = booking_audit_trail_repo.load_for_booking(booking_id)
    cancel_trail = [
        audit
        for audit in audit_trail
        if audit.audit_trail.audit_type == AuditType.BOOKING_CANCELLED
    ]
    assert (
        len(cancel_trail) == 1
    ), "There should be exactly one audit trail event for cancellation"
    assert (
        cancel_trail[0].audit_trail.action_id
        == booking_action_aggregates[0].booking_action.action_id
    ), "Action id should be populated in audit trail"
    cancel_event = [
        event
        for event in cancel_trail[0].audit_trail.audit_payload['domain_events']
        if event['event_type'] == 'Booking Cancelled'
    ]
    assert (
        cancel_event[0]['event_detail']['reason'] == 'winter is coming'
    ), "Reason should be in audit trail"
    assert cancel_trail[0].audit_trail.user == "someone"


def test_booking_replace(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    booking_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    booking_version = booking_aggregate.booking.version

    with mock_role_manager(), mock_rate_manager_client():
        url = f'v1/bookings/{booking_id}'
        payload = json.dumps(
            {
                "data": json.loads(create_booking_payload),
                "resource_version": booking_version,
            }
        )
        response = client.put(
            url,
            data=payload,
            content_type='application/json',
            headers={'X-User-Type': 'super-admin'},
        )
        assert response.status_code == 200
        bookings_response = json.loads(response.data.decode('utf-8'))
        assert len(bookings_response['data']['room_stays']) > 0
        booking_id = bookings_response['data']['booking_id']

    booking_aggregate = booking_repo.load(booking_id)
    assert (
        str(booking_aggregate.booking.checkin_date.time())
        == active_hotel_aggregate.hotel.checkin_time
    )
    assert (
        str(booking_aggregate.booking.checkout_date.time())
        == active_hotel_aggregate.hotel.checkout_time
    )
    assert all(
        str(rs.checkin_date.time()) == active_hotel_aggregate.hotel.checkin_time
        for rs in booking_aggregate.room_stays
    )
    assert all(
        str(rs.checkout_date.time()) == active_hotel_aggregate.hotel.checkout_time
        for rs in booking_aggregate.room_stays
    )

    guest_stays = chain(*[rs.guest_stays for rs in booking_aggregate.room_stays])
    assert all(
        str(gs.checkin_date.time()) == active_hotel_aggregate.hotel.checkin_time
        for gs in guest_stays
    )
    assert all(
        str(gs.checkout_date.time()) == active_hotel_aggregate.hotel.checkout_time
        for gs in guest_stays
    )


def test_temp_booking_create(create_temp_booking_payload, client, booking_repo):
    booking_id = make_booking(client, {"data": json.loads(create_temp_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.TEMPORARY
    booking = get_booking(client, booking_id)
    assert 'confirm' in booking['data']['allowed_actions']


def test_booking_should_be_created_in_temporary_state_for_hold_till(
    create_booking_payload_with_hold_till, client
):
    booking_response = make_booking(
        client,
        {"data": json.loads(create_booking_payload_with_hold_till)},
        expected_status_code=200,
        return_complete_response=True,
    )
    assert booking_response['data']['status'] == BookingStatus.TEMPORARY.value


def test_confirm_booking_creation_with_hold_till_creates_in_temporary_state(
    create_confirmed_booking_payload_with_hold_till, client
):
    booking_response = make_booking(
        client,
        {"data": json.loads(create_confirmed_booking_payload_with_hold_till)},
        expected_status_code=200,
        return_complete_response=True,
    )
    assert booking_response['data']['status'] == BookingStatus.TEMPORARY.value


def test_temp_booking_can_be_cancelled(
    create_temp_booking_payload, client, booking_repo, cancellation_item
):
    booking_id = make_booking(client, {"data": json.loads(create_temp_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.TEMPORARY

    cancellation_req = booking_cancellation_request(booking_aggregate.booking.version)
    cancel_booking(client, booking_id, cancellation_req)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CANCELLED


def test_temp_booking_can_be_confirmed(
    create_temp_booking_payload, client, booking_repo
):
    booking_id = make_booking(client, {"data": json.loads(create_temp_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.TEMPORARY

    cancellation_req = create_confirm_payload(booking_aggregate.booking.version)
    confirm_booking(client, booking_id, cancellation_req)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CONFIRMED


def test_create_booking_validated_all_created_billed_entities(
    create_booking_payload, client, bill_repo, booking_repo
):
    with mock_catalog_client(
        mocked_seller_type=SellerType.MARKETPLACE.value
    ), mock_role_manager(), mock_rate_manager_client():
        url = 'v1/bookings'
        payload = json.dumps({"data": json.loads(create_booking_payload)})
        response = client.post(
            url,
            data=payload,
            content_type='application/json',
            headers={'X-User-Type': 'super-admin'},
        )
        assert response.status_code == 200
        bookings_response = json.loads(response.data.decode('utf-8'))
        assert len(bookings_response['data']['room_stays']) > 0

    booking_id = bookings_response['data']['booking_id']
    bill_id = bookings_response['data']['bill_id']

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(bill_id)

    grouped_billed_entities = {
        b.billed_entity_id: b for b in bill_aggregate.billed_entities
    }
    for c in booking_aggregate.customers:
        if booking_aggregate.is_booking_owner(c.customer_id):
            if booking_aggregate.is_b2b_ta_bulk_booking():
                assert c.billed_entity_id is not None
                assert (
                    len(grouped_billed_entities.get(c.billed_entity_id).accounts) == 1
                )
                assert grouped_billed_entities.get(c.billed_entity_id).name == c.name
                assert (
                    grouped_billed_entities.get(c.billed_entity_id).category
                    == BilledEntityCategory.BOOKER
                )
            else:
                assert c.billed_entity_id is None
            assert c.company_billed_entity_id is not None
            assert (
                grouped_billed_entities.get(c.company_billed_entity_id).category
                == BilledEntityCategory.BOOKER_COMPANY
            )
            assert grouped_billed_entities.get(c.company_billed_entity_id).name == Name(
                c.company_legal_name()
            )

        else:
            assert c.company_billed_entity_id is None
            assert grouped_billed_entities.get(c.billed_entity_id).category in {
                BilledEntityCategory.CONSUMING_GUESTS,
                BilledEntityCategory.PRIMARY_GUEST,
            }


def test_create_booking_should_assign_default_billed_entities_to_charges(
    create_booking_payload, client, bill_repo, booking_repo
):
    with mock_catalog_client(
        mocked_seller_type=SellerType.MARKETPLACE.value
    ), mock_role_manager(), mock_rate_manager_client():
        url = 'v1/bookings'
        payload = json.dumps({"data": json.loads(create_booking_payload)})
        response = client.post(
            url,
            data=payload,
            content_type='application/json',
            headers={'X-User-Type': 'super-admin'},
        )
        assert response.status_code == 200
        bookings_response = json.loads(response.data.decode('utf-8'))
        assert len(bookings_response['data']['room_stays']) > 0

    booking_id = bookings_response['data']['booking_id']
    bill_id = bookings_response['data']['bill_id']

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(bill_id)

    default_billed_entity = bill_aggregate.get_billed_entity_for_category(
        booking_aggregate.get_default_billed_entity_category()
    )

    for c in bill_aggregate.charges:
        assert len(c.charge_splits) == 1
        assert c.charge_splits[
            0
        ].billed_entity_account == default_billed_entity.get_account_for_new_assignment(
            c.charge_splits[0].charge_type
        )


def update_customer(client, payload, booking_id):
    url = "v1/bookings/" + booking_id + "/customers"
    payload = json.dumps(payload)
    with mock_aws_service_client():
        response = client.patch(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
    return response


def test_update_booking_customer_data(
    new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room,
    client,
    booking_repo,
):
    booking_id = make_booking(
        client,
        {
            "data": json.loads(
                new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room
            )
        },
    )
    booking_aggregate = booking_repo.load(booking_id)

    # update booking details
    first_primary_guest_id = [
        c.customer_id
        for c in sorted(booking_aggregate.customers, key=lambda c: c.customer_id)
        if c.is_primary
    ][0]
    second_primary_guest_id = [
        c.customer_id
        for c in sorted(booking_aggregate.customers, key=lambda c: c.customer_id)
        if c.is_primary and c.customer_id != first_primary_guest_id
    ][0]
    customer_details = [
        {'first_name': 'Kadam', 'last_name': 'Jain', 'id': first_primary_guest_id},
        {'first_name': 'Rohit', 'last_name': 'Jain', 'id': second_primary_guest_id},
    ]

    update_customer_payload = create_update_customer_payload(
        customer_details, booking_aggregate.booking.version
    )
    resp = None
    hotel_config = [
        {
            'config_name': 'e_reg_card',
            'config_value': '{"enabled":true,"required":true, "level":"room"}',
            'value_type': 'json',
        }
    ]
    with mock_tenant_config(
        hotel_level_config=hotel_config
    ), mock_template_service(), mock_aws_service_client(), mock_rate_manager_client():
        resp = update_customer(client, update_customer_payload, booking_id)
    booking_aggregate = booking_repo.load(booking_id)
    customers = booking_aggregate.customers
    customer_1 = [
        customer
        for customer in customers
        if customer.customer_id == first_primary_guest_id
    ]
    customer_2 = [
        customer
        for customer in customers
        if customer.customer_id == second_primary_guest_id
    ]

    assert resp.status_code == 200
    assert customer_2[0].first_name == "Rohit"
    assert customer_2[0].last_name == "Jain"
    assert (
        customer_2[0].eregcard_url is not None
    ), "eregcard_url for customer_id: '{0}' is None".format(customer_2[0].customer_id)
    assert customer_1[0].first_name == "Kadam"
    assert customer_1[0].last_name == "Jain"
    assert (
        customer_1[0].eregcard_url is not None
    ), "eregcard_url for customer_id: '{0}' is None".format(customer_1[0].customer_id)


def patch_customer_details(client, booking_id, customer_id, payload):
    with mock_role_manager():
        url = f"/v1/bookings/{booking_id}/customers/{customer_id}"
        payload = json.dumps(payload)
        response = client.patch(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )

    return response.json


def test_update_customer_guest_preferences(
    create_booking_payload, client, booking_repo
):
    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload['source']['channel_code'] = BookingChannels.DIRECT.value
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )

    payload = {
        "data": {
            "guest_preferences": {
                "fnb_preferences": [{"name": "smoking", "values": ["ok Fine"]}],
                "newspaper_preferences": [
                    {"name": "newspaper_name", "values": ["ET", "HT"]}
                ],
            }
        },
        "resource_version": response['data']['version'],
    }
    booking_id = response["data"]["booking_id"]
    customer_id = response['data']['customers'][0]['customer_id']
    response = patch_customer_details(client, booking_id, customer_id, payload)

    booking_aggregate = booking_repo.load(booking_id)

    customer = booking_aggregate.get_customer(customer_id)
    assert (
        customer.guest_preferences.newspaper_preferences[0].get('name')
        == "newspaper_name"
    )


def test_booking_group_name(client, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    group_name = "vacation"
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-4',
        group_name=group_name,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)

    assert booking_aggregate.booking.group_name == group_name


def test_booking_company_details(client, booking_repo, bill_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-4',
        set_company_details=True,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)
    company_details_json = booking_aggregate.get_company_details().to_json()
    request_company_details_json: dict = create_booking_payload['company_details']
    if not request_company_details_json:
        request_company_details_json = {}

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    assert any(
        billed_entity.category == BilledEntityCategory.BOOKER_COMPANY
        and billed_entity.name.full_name == booking_aggregate.get_company_legal_name()
        for billed_entity in bill_aggregate.billed_entities
    )
    assert company_details_json['legal_details'] is not None
    assert booking_aggregate.get_company_legal_name() is not None
    assert (
        booking_aggregate.get_company_legal_name()
        == request_company_details_json['legal_details']['legal_name']
    )
    assert company_details_json['legal_details']['address'] is not None
    assert (
        company_details_json['legal_details']['address']
        == request_company_details_json['legal_details']['address']
    )
    assert (
        company_details_json['legal_details']['tin']
        == request_company_details_json['legal_details']['tin']
    )


def test_booking_travel_agent_details(client, booking_repo, bill_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-4',
        set_travel_agent_details=True,
        set_booking_owner_gst_details_from_travel_agent_details=True,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    travel_agent_details_json = booking_aggregate.get_travel_agent_details().to_json()
    request_travel_agent_details_json = create_booking_payload['travel_agent_details']
    assert any(
        billed_entity.category == BilledEntityCategory.TRAVEL_AGENT
        and billed_entity.name.full_name
        == booking_aggregate.get_travel_agent_legal_name()
        for billed_entity in bill_aggregate.billed_entities
    )
    assert travel_agent_details_json['legal_details'] is not None
    assert booking_aggregate.get_travel_agent_legal_name() is not None
    assert (
        booking_aggregate.get_travel_agent_legal_name()
        == request_travel_agent_details_json['legal_details']['legal_name']
    )
    assert travel_agent_details_json['legal_details']['address'] is not None
    assert (
        travel_agent_details_json['legal_details']['address']
        == request_travel_agent_details_json['legal_details']['address']
    )
    assert (
        travel_agent_details_json['legal_details']['tin']
        == request_travel_agent_details_json['legal_details']['tin']
    )


def test_company_code_for_travel_agent(client, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-4',
        set_travel_agent_details=True,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)
    assert (
        booking_aggregate.get_company_code()
        == create_booking_payload["travel_agent_details"]["legal_details"][
            "client_internal_code"
        ]
    )


def test_company_code_for_company(client, booking_repo, bill_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-4',
        set_company_details=True,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)

    # pylint: disable=unsubscriptable-object
    assert (
        booking_aggregate.get_company_code()
        == create_booking_payload["company_details"]["legal_details"][
            "client_internal_code"
        ]
    )


def test_update_company_details_on_customer_gst_details_update(client, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    payload = {
        "data": {
            "gst_details": {
                "address": {
                    "city": "updated_city",
                    "country": "update_country",
                    "field_1": "update_field1",
                    "field_2": "update_field2",
                    "pincode": "321321",
                    "state": "update_state",
                },
                "gstin_num": "12ACCDE0000A1ZM",
                "legal_name": "update_company_legal_name",
            }
        },
        "resource_version": response['data']['version'],
    }
    booking_id = response["data"]["booking_id"]
    customer_id = response['data']['customers'][0]['customer_id']
    response = patch_customer_details(client, booking_id, customer_id, payload)
    booking_aggregate = booking_repo.load(booking_id)
    customer = booking_aggregate.get_customer(customer_id)
    assert (
        booking_aggregate.get_company_details().billed_entity_id
        == customer.company_billed_entity_id
    )
    assert (
        booking_aggregate.get_company_details()
        and booking_aggregate.get_company_details().legal_details is not None
    )
    assert (
        booking_aggregate.get_company_details().legal_details.legal_name
        == customer.gst_details.legal_name
    )
    assert (
        booking_aggregate.get_company_details().legal_details.tin
        == customer.gst_details.gstin_num
    )
    assert (
        booking_aggregate.get_company_details().legal_details.address
        == customer.gst_details.address
    )


def test_update_travel_details_on_customer_gst_details_update(client, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        set_travel_agent_details=True,
        set_company_details=True,
        set_booking_owner_gst_details_from_travel_agent_details=True,
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    payload = {
        "data": {
            "gst_details": {
                "address": {
                    "city": "update_city",
                    "country": "update_country",
                    "field_1": "update_field1",
                    "field_2": "update_field2",
                    "pincode": "321321",
                    "state": "update_state",
                },
                "gstin_num": "12ACCDE0000A1ZM",
                "legal_name": "update_travel_agent_legal_name",
            }
        },
        "resource_version": response['data']['version'],
    }
    booking_id = response["data"]["booking_id"]
    customer_id = response['data']['customers'][0]['customer_id']
    response = patch_customer_details(client, booking_id, customer_id, payload)
    booking_aggregate = booking_repo.load(booking_id)
    customer = booking_aggregate.get_customer(customer_id)

    assert (
        booking_aggregate.get_travel_agent_details().billed_entity_id
        == customer.company_billed_entity_id
    )
    assert (
        booking_aggregate.get_travel_agent_details()
        and booking_aggregate.get_company_details().legal_details is not None
    )
    assert (
        booking_aggregate.get_travel_agent_details().legal_details.legal_name
        == customer.gst_details.legal_name
    )
    assert (
        booking_aggregate.get_travel_agent_details().legal_details.tin
        == customer.gst_details.gstin_num
    )
    assert (
        booking_aggregate.get_travel_agent_details().legal_details.address
        == customer.gst_details.address
    )


def test_update_booking_group_name(client, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    group_name = "vacation"
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        group_name=group_name,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    payload = {
        "data": {"group_name": "update_vacation"},
        "resource_version": response['data']['version'],
    }
    response = patch_booking(client, booking_id, payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.group_name == payload["data"]["group_name"]


def test_update_booking_company_details(client, booking_repo, bill_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    payload = {
        "data": {
            "company_details": {
                "legal_details": {
                    "address": {
                        "city": "city_update",
                        "country": "country_update",
                        "field_1": "field1_update",
                        "field_2": "field2_update",
                        "pincode": "123123",
                        "state": "state_update",
                    },
                    "tin": "12ABCDE0000A1ZMN1",
                    "legal_name": "update_company_legal_name",
                    "client_internal_code": "company-01",
                    "external_reference_id": None,
                    "is_sez": False,
                    "has_lut": False,
                    "email": None,
                    "phone": None,
                }
            }
        },
        "resource_version": response['data']['version'],
    }
    response = patch_booking(client, booking_id, payload)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert any(
        billed_entity.category == BilledEntityCategory.BOOKER_COMPANY
        and billed_entity.name.full_name == booking_aggregate.get_company_legal_name()
        for billed_entity in bill_aggregate.billed_entities
    )
    assert booking_aggregate.get_company_details().billed_entity_id is not None
    assert (
        booking_aggregate.get_company_details().legal_details.to_json()
        == payload["data"]["company_details"]["legal_details"]
    )


def test_update_booking_travel_agent_details(client, booking_repo, bill_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    payload = {
        "data": {
            "travel_agent_details": {
                "legal_details": {
                    "address": {
                        "city": "city_update",
                        "country": "country_update",
                        "field_1": "field1_update",
                        "field_2": "field2_update",
                        "pincode": "123123",
                        "state": "state_update",
                    },
                    "tin": "12ABCDE0000A1ZMN1",
                    "legal_name": "update_travel_agent_legal_name",
                    "client_internal_code": "travel_agent-01",
                    "external_reference_id": None,
                    "is_sez": False,
                    "has_lut": False,
                    "email": None,
                    "phone": None,
                }
            }
        },
        "resource_version": response['data']['version'],
    }
    response = patch_booking(client, booking_id, payload)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert any(
        billed_entity.category == BilledEntityCategory.TRAVEL_AGENT
        and billed_entity.name.full_name
        == booking_aggregate.get_travel_agent_legal_name()
        for billed_entity in bill_aggregate.billed_entities
    )
    assert booking_aggregate.get_travel_agent_details().billed_entity_id is not None
    assert (
        booking_aggregate.get_travel_agent_details().legal_details.to_json()
        == payload["data"]["travel_agent_details"]["legal_details"]
    )


def test_create_booking_with_booking_owner_name_exist_in_guest_list(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    # d1, d2 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        make_booking_owner_name_and_guest_name_same=True,
    )
    payload = {"data": create_booking_payload}
    booking_id = make_booking(client, payload)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 3


def test_create_booking_with_booking_owner_name_not_exist_in_guest_list(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    # d1, d2 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
    )
    payload = {"data": create_booking_payload}
    booking_id = make_booking(client, payload)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 4


def test_booking_create_non_bulk_b2b_booking(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    # d1, d2 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
    )
    payload = {"data": create_booking_payload}
    booking_id = make_booking(client, payload)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 3
    assert not any(
        billed_entity.category == BilledEntityCategory.BOOKER
        for billed_entity in bill_aggregate.billed_entities
    )
    assert any(
        billed_entity.category == BilledEntityCategory.BOOKER_COMPANY
        for billed_entity in bill_aggregate.billed_entities
    )


def test_booking_create_b2b_bulk(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    # d1, d2 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
    )
    create_booking_payload["source"]["subchannel_code"] = "bulk"
    payload = {"data": create_booking_payload}
    booking_id = make_booking(client, payload)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 4
    assert any(
        billed_entity.category == BilledEntityCategory.BOOKER_COMPANY
        for billed_entity in bill_aggregate.billed_entities
    )


def test_booking_with_specific_default_billed_entity_category(
    client, booking_repo, bill_repo
):
    default_billed_entity_categories = [
        BilledEntityCategory.BOOKER,
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.TRAVEL_AGENT,
        BilledEntityCategory.PRIMARY_GUEST,
    ]
    for default_billed_entity_category in default_billed_entity_categories:
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=2,
            channel="direct"
            if default_billed_entity_category is BilledEntityCategory.BOOKER
            else "b2b",
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            default_billed_entity_category=default_billed_entity_category.value,
            set_travel_agent_details=True,
            set_company_details=True,
        )

        response = make_booking(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_aggregate = booking_repo.load(response.get('data').get('booking_id'))
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        for charge in bill_aggregate.charges:
            for charge_split in charge.charge_splits:
                billed_entity = bill_aggregate.billed_entity_dict.get(
                    charge_split.billed_entity_account.billed_entity_id
                )
                assert billed_entity.category == default_billed_entity_category
        assert (
            booking_aggregate.get_default_billed_entity_category()
            == default_billed_entity_category
        )


def test_booking_replace_with_specific_default_billed_entity_category(
    client, booking_repo, bill_repo
):
    default_billed_entity_categories = [
        BilledEntityCategory.BOOKER,
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.TRAVEL_AGENT,
        BilledEntityCategory.PRIMARY_GUEST,
    ]
    allowed_categories_for_b2b = [
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.PRIMARY_GUEST,
    ]
    allowed_categories_for_direct = [
        BilledEntityCategory.BOOKER,
        BilledEntityCategory.PRIMARY_GUEST,
        BilledEntityCategory.BOOKER_COMPANY,
    ]
    for default_billed_entity_category in default_billed_entity_categories:
        channel = (
            "direct"
            if default_billed_entity_category is BilledEntityCategory.BOOKER
            else "b2b"
        )
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=2,
            channel=channel,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            default_billed_entity_category=default_billed_entity_category.value,
            set_travel_agent_details=True,
            set_company_details=True,
        )

        response = make_booking(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response['data']['booking_id']
        update_default_billed_entity_category = random.choice(
            allowed_categories_for_b2b
            if channel == "b2b"
            else allowed_categories_for_direct
        )
        update_booking_payload = create_booking_payload
        update_booking_payload[
            'default_billed_entity_category'
        ] = update_default_billed_entity_category.value
        with mock_role_manager():
            url = f'v1/bookings/{booking_id}'
            payload = json.dumps(
                {
                    "data": update_booking_payload,
                    "resource_version": response['data']['version'],
                }
            )
            response = client.put(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            response = json.loads(response.data.decode('utf-8'))

        booking_aggregate = booking_repo.load(response.get('data').get('booking_id'))
        bill_aggregate = bill_repo.load(response.get('data').get('bill_id'))
        for charge in bill_aggregate.charges:
            for charge_split in charge.charge_splits:
                billed_entity = bill_aggregate.billed_entity_dict.get(
                    charge_split.billed_entity_account.billed_entity_id
                )
                assert billed_entity.category == update_default_billed_entity_category
        assert (
            booking_aggregate.get_default_billed_entity_category()
            == update_default_billed_entity_category
        )


def test_booking_with_only_travel_agent_details(client, booking_repo, bill_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        set_travel_agent_details=True,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response.get('data').get('booking_id'))
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    for charge in bill_aggregate.charges:
        for charge_split in charge.charge_splits:
            billed_entity = bill_aggregate.billed_entity_dict.get(
                charge_split.billed_entity_account.billed_entity_id
            )
            assert billed_entity.category == BilledEntityCategory.TRAVEL_AGENT
    assert (
        booking_aggregate.get_default_billed_entity_category()
        == BilledEntityCategory.TRAVEL_AGENT
    )
    for billed_entity in bill_aggregate.billed_entities:
        if billed_entity.category == BilledEntityCategory.BOOKER_COMPANY:
            assert False, "Booker company billed entity should not be created"


# backward compatibility test
def test_booking_with_legal_details_in_booking_owner(client, booking_repo, bill_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response.get('data').get('booking_id'))
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    for charge in bill_aggregate.charges:
        for charge_split in charge.charge_splits:
            billed_entity = bill_aggregate.billed_entity_dict.get(
                charge_split.billed_entity_account.billed_entity_id
            )
            assert billed_entity.category == BilledEntityCategory.BOOKER_COMPANY
    assert (
        booking_aggregate.get_default_billed_entity_category()
        == BilledEntityCategory.BOOKER_COMPANY
    )


def test_create_b2b_booking_with_booker_as_default_billed_entity(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        make_booking_owner_name_and_guest_name_same=True,
    )
    payload = {"data": create_booking_payload}
    # Booker can't be default_billed_entity_category for B2B booking
    # This will raise error
    response = make_booking(client, payload, expected_status_code=400)
    assert response.json['errors'][0]['request_id'] is not None


def test_create_b2b_bulk_booking_with_booker_company_as_default_billed_entity(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
        sub_channel=BookingSubChannels.B2B_BULK.value,
        default_billed_entity_category=BilledEntityCategory.BOOKER_COMPANY.value,
        make_booking_owner_name_and_guest_name_same=False,
    )


def test_create_b2b_bulk_booking_with_booker_as_default_billed_entity(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
        sub_channel=BookingSubChannels.B2B_BULK.value,
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    payload = {"data": create_booking_payload}
    # Booker can be default billed entity
    booking_id = make_booking(client, payload, expected_status_code=200)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 4
    assert any(
        billed_entity.category == BilledEntityCategory.BOOKER
        for billed_entity in bill_aggregate.billed_entities
    )


def test_create_b2b_bulk_booking_with_guest_name_same_as_booking_owner(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    # two guest in one room, primary guest name is same as owner name
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
        sub_channel=BookingSubChannels.B2B_BULK.value,
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        make_booking_owner_name_and_guest_name_same=True,
    )
    payload = {"data": create_booking_payload}
    # Only two billed entity created
    # one for owner and other for guest(consuming) having distinct name
    booking_id = make_booking(client, payload, expected_status_code=200)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    booking_owner_be = bill_aggregate.get_billed_entity(
        booking_aggregate.get_booking_owner().billed_entity_id
    )
    assert booking_owner_be.category == BilledEntityCategory.BOOKER
    assert booking_owner_be.secondary_category == BilledEntityCategory.PRIMARY_GUEST
    # billed entity is not created for primary guest as primary guest name is same as booking owner
    assert any(
        billed_entity.category == BilledEntityCategory.CONSUMING_GUESTS
        for billed_entity in bill_aggregate.billed_entities
    )


def test_un_mark_guest_as_booker_should_create_new_be_and_customer_for_guest(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
        sub_channel=BookingSubChannels.B2B_BULK.value,
        make_booking_owner_name_and_guest_name_same=True,
    )
    payload = {"data": create_booking_payload}
    # Only two billed entity created
    # one for owner (who is also a guest) and other for guest(consuming) having distinct name
    booking_id = make_booking(client, payload, expected_status_code=200)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    booking_owner_be = bill_aggregate.get_billed_entity(
        booking_aggregate.get_booking_owner().billed_entity_id
    )
    assert booking_owner_be.category == BilledEntityCategory.BOOKER
    assert booking_owner_be.secondary_category == BilledEntityCategory.PRIMARY_GUEST
    assert booking_aggregate.is_guest(booking_aggregate.booking.owner_id)
    customer_details = [
        {
            'first_name': 'Kadam',
            'last_name': 'Jain',
            'id': booking_aggregate.get_booking_owner().customer_id,
            'is_booker': False,
        }
    ]
    update_customer_payload = create_update_customer_payload(
        customer_details, booking_aggregate.booking.version
    )
    resp = update_customer(
        client, update_customer_payload, booking_aggregate.booking_id
    )
    assert resp.status_code == 200
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    booking_aggregate = booking_repo.load(booking_id)
    # BE for primary guest should comes in as booker is unmarked as guest
    assert any(
        billed_entity.category == BilledEntityCategory.PRIMARY_GUEST
        for billed_entity in bill_aggregate.billed_entities
    )
    booking_owner_be = bill_aggregate.get_billed_entity(
        booking_aggregate.get_booking_owner().billed_entity_id
    )
    assert booking_owner_be.category == BilledEntityCategory.BOOKER
    assert booking_owner_be.secondary_category is None
    assert not booking_aggregate.is_guest(booking_aggregate.booking.owner_id)


def test_mark_guest_as_booker_should_delete_be_and_customer_of_guest_should_also_move_charges_to_booker(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
        sub_channel=BookingSubChannels.B2B_BULK.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
    )
    payload = {"data": create_booking_payload}
    # Two BE created
    booking_id = make_booking(client, payload, expected_status_code=200)
    expense_v2_request = create_expenses_v2_request(
        dates=[checkin_date],
        price=50,
        billed_entity_accounts=[(2, 1)],
        charge_to=["2", "1"],
    )
    add_expenses_v2(client, booking_id, expense_v2_request)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert (
        len(
            [
                be
                for be in bill_aggregate.billed_entities
                if be.status == BilledEntityStatus.ACTIVE
            ]
        )
        == 3
    )
    mark_booker_as_customer = [
        {
            'first_name': 'Kadam',
            'last_name': 'Jain',
            'id': booking_aggregate.room_stays[0].guest_stays[0].guest_id,
            'is_booker': True,
        }
    ]
    update_customer_payload = create_update_customer_payload(
        mark_booker_as_customer, booking_aggregate.booking.version
    )
    resp = update_customer(
        client, update_customer_payload, booking_aggregate.booking_id
    )
    assert resp.status_code == 200
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    booking_aggregate = booking_repo.load(booking_id)
    assert (
        len(
            [
                be
                for be in bill_aggregate.billed_entities
                if be.status == BilledEntityStatus.ACTIVE
            ]
        )
        == 2
    )
    booking_owner_be = bill_aggregate.get_billed_entity(
        booking_aggregate.get_booking_owner().billed_entity_id
    )
    assert booking_owner_be.category == BilledEntityCategory.BOOKER
    assert booking_owner_be.secondary_category == BilledEntityCategory.PRIMARY_GUEST
    assert booking_aggregate.is_guest(booking_aggregate.booking.owner_id)
    assert len(booking_aggregate.customers) == 1


def test_create_b2b_bulk_booking_with_guest_name_same_as_booking_owner_and_def_bll_ent_primary_guest(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    # two guest in one room, primary guest name is same as owner name
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
        sub_channel=BookingSubChannels.B2B_BULK.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=True,
    )
    payload = {"data": create_booking_payload}
    # 2BE: one for booker (with secondary be category as PRIMARY_GUEST) and other for consuming
    booking_id = make_booking(client, payload, expected_status_code=200)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 3
    booking_owner_be = bill_aggregate.get_billed_entity(
        booking_aggregate.get_booking_owner().billed_entity_id
    )
    assert booking_owner_be.category == BilledEntityCategory.BOOKER
    assert booking_owner_be.secondary_category == BilledEntityCategory.PRIMARY_GUEST
    assert any(
        billed_entity.category == BilledEntityCategory.CONSUMING_GUESTS
        for billed_entity in bill_aggregate.billed_entities
    )


def test_create_direct_booking(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    # two guest in one room, one of the guest name is same as owner name
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    payload = {"data": create_booking_payload}
    booking_id = make_booking(client, payload, expected_status_code=200)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 4
    allowed_category = {
        BilledEntityCategory.BOOKER,
        BilledEntityCategory.CONSUMING_GUESTS,
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.PRIMARY_GUEST,
    }
    assert all(
        billed_entity.category in allowed_category
        for billed_entity in bill_aggregate.billed_entities
    )


def test_create_direct_booking_with_guest_having_same_name_as_owner(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
):
    # two guest in one room, one of the guest name is same as owner name
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        make_booking_owner_name_and_guest_name_same=True,
    )
    payload = {"data": create_booking_payload}
    # Only three billed entity created
    # 1) booking owner (primary: BOOKER, secondary: PRIMARY)
    # 2) One Consuming Guest
    # 3) Booker company
    booking_id = make_booking(client, payload, expected_status_code=200)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 3
    allowed_category = {
        BilledEntityCategory.BOOKER,
        BilledEntityCategory.CONSUMING_GUESTS,
        BilledEntityCategory.BOOKER_COMPANY,
    }
    assert all(
        billed_entity.category in allowed_category
        for billed_entity in bill_aggregate.billed_entities
    )
    booking_owner_be = bill_aggregate.get_billed_entity(
        booking_aggregate.get_booking_owner().billed_entity_id
    )
    assert booking_owner_be.category == BilledEntityCategory.BOOKER
    assert booking_owner_be.secondary_category == BilledEntityCategory.PRIMARY_GUEST


def test_create_direct_booking_and_do_checkin_with_def_bil_ent_cat_as_prim_guest(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
    hotel_repo,
):
    # two guest in one room, one of the guest name is same as owner name
    checkin_date = dateutils.subtract(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    payload = {"data": create_booking_payload}
    # Only two billed entity created
    # one for owner and other for guest having distinct name
    booking_id = make_booking(client, payload, expected_status_code=200)
    booking_aggregate = booking_repo.load(booking_id)
    booking_owner = booking_aggregate.get_booking_owner()
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 3
    allowed_category = {
        BilledEntityCategory.BOOKER,
        BilledEntityCategory.PRIMARY_GUEST,
        BilledEntityCategory.BOOKER_COMPANY,
    }
    assert all(
        billed_entity.category in allowed_category
        for billed_entity in bill_aggregate.billed_entities
    )

    # do the check-in by making guest name and booking owner name same
    checkin_payload = create_checkin_payload_with_edit_guest_details(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        guest_first_name=booking_owner.first_name,
        guest_last_name=booking_owner.last_name,
    )

    checkin_booking(client, booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 4
    allowed_category = {
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.PRIMARY_GUEST,
        BilledEntityCategory.GUEST_COMPANY,
        BilledEntityCategory.BOOKER,
    }
    assert all(
        billed_entity.category in allowed_category
        for billed_entity in bill_aggregate.billed_entities
    )


def test_create_direct_booking_and_do_checkin_with_def_bil_ent_cat_as_booker_guest(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
    hotel_repo,
):
    # two guest in one room, one of the guest name is same as owner name
    checkin_date = dateutils.subtract(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    payload = {"data": create_booking_payload}
    # Only two billed entity created
    # one for owner and other for guest having distinct name
    booking_id = make_booking(client, payload, expected_status_code=200)
    booking_aggregate = booking_repo.load(booking_id)
    booking_owner = booking_aggregate.get_booking_owner()
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 3
    allowed_category = {
        BilledEntityCategory.BOOKER,
        BilledEntityCategory.PRIMARY_GUEST,
        BilledEntityCategory.BOOKER_COMPANY,
    }
    assert all(
        billed_entity.category in allowed_category
        for billed_entity in bill_aggregate.billed_entities
    )

    # roll_over_business_date(active_hotel_aggregate, hotel_repo)

    # do the check-in by making guest name and booking owner name same
    checkin_payload = create_checkin_payload_with_edit_guest_details(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        guest_first_name=booking_owner.first_name,
        guest_last_name=booking_owner.last_name,
    )

    checkin_booking(client, booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert len(bill_aggregate.billed_entities) == 4
    allowed_category = {
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.BOOKER,
        BilledEntityCategory.GUEST_COMPANY,
        BilledEntityCategory.PRIMARY_GUEST,
    }
    assert all(
        billed_entity.category in allowed_category
        for billed_entity in bill_aggregate.billed_entities
    )


def test_booking_owner_name_hyphen_and_phone_null(client, booking_repo, bill_repo):
    # create booking
    # given booking owner name hyphen
    # booking should happen and booker billed entity should not create
    default_billed_entity_categories = [
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.TRAVEL_AGENT,
        BilledEntityCategory.PRIMARY_GUEST,
    ]
    for default_billed_entity_category in default_billed_entity_categories:
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=2,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            default_billed_entity_category=default_billed_entity_category.value,
            set_travel_agent_details=True,
            set_company_details=True,
            set_booking_owner_name_hyphen=True,
            set_booking_owner_phone_null=True,
            channel="direct",
        )

        response = make_booking(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_aggregate = booking_repo.load(response.get('data').get('booking_id'))
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        booking_owner = booking_aggregate.get_booking_owner()
        assert booking_owner.name.full_name == "-"
        assert not booking_owner.phone
        assert not any(
            [
                billed_entity == BilledEntityCategory.BOOKER
                for billed_entity in bill_aggregate.billed_entities
            ]
        )


def test_booking_owner_name_given_after_booking_creation(
    client, booking_repo, bill_repo
):
    # create booking
    # given booking owner name hyphen
    # booking should happen and booker billed entity should not create
    # added booker owner name and billed entity should create
    default_billed_entity_categories = [
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.TRAVEL_AGENT,
        BilledEntityCategory.PRIMARY_GUEST,
    ]
    for default_billed_entity_category in default_billed_entity_categories:
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=2,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            default_billed_entity_category=default_billed_entity_category.value,
            set_travel_agent_details=True,
            set_company_details=True,
            set_booking_owner_name_hyphen=True,
            set_booking_owner_phone_null=True,
            channel="direct",
        )

        response = make_booking(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_aggregate = booking_repo.load(response.get('data').get('booking_id'))
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        booking_owner = booking_aggregate.get_booking_owner()
        assert booking_owner.name.full_name == "-"
        assert not booking_owner.phone
        assert not any(
            [
                billed_entity.category == BilledEntityCategory.BOOKER
                for billed_entity in bill_aggregate.billed_entities
            ]
        )
        customer_details = [
            {
                'first_name': 'Kadam',
                'last_name': 'Jain',
                'id': booking_aggregate.get_booking_owner().customer_id,
            }
        ]
        update_customer_payload = create_update_customer_payload(
            customer_details, booking_aggregate.booking.version
        )
        resp = update_customer(
            client, update_customer_payload, booking_aggregate.booking_id
        )
        assert resp.status_code == 200
        booking_aggregate = booking_repo.load(booking_aggregate.booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        booking_owner = booking_aggregate.get_booking_owner()
        assert booking_owner.name.full_name
        assert booking_owner.name.first_name == customer_details[0]["first_name"]
        assert booking_owner.name.last_name == customer_details[0]["last_name"]
        assert any(
            [
                billed_entity.category == BilledEntityCategory.BOOKER
                for billed_entity in bill_aggregate.billed_entities
            ]
        )


def test_create_booking_without_company_details_and_def_be_as_booker_company_should_raise_validation_error(
    client, booking_repo, bill_repo
):
    default_billed_entity_categories = [
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.TRAVEL_AGENT,
    ]
    for default_billed_entity_category in default_billed_entity_categories:
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=2,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            default_billed_entity_category=default_billed_entity_category.value,
            set_travel_agent_details=False,
            set_company_details=False,
            set_booking_owner_name_hyphen=True,
            set_booking_owner_phone_null=True,
            channel="direct",
        )
        create_booking_payload["booking_owner"]["gst_details"] = None
        make_booking(
            client=client,
            payload={"data": create_booking_payload},
            expected_status_code=400,
        )


def test_create_booking_without_company_details_company_should_copy_details_from_booking_owner(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        default_billed_entity_category=BilledEntityCategory.BOOKER_COMPANY.value,
        set_travel_agent_details=False,
        set_company_details=False,
        set_booking_owner_name_hyphen=True,
        set_booking_owner_phone_null=True,
        channel="direct",
    )
    booking_owner_gst_details = create_booking_payload["booking_owner"]["gst_details"]
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response.get('data').get('booking_id'))
    booking_company_legal_details = (
        booking_aggregate.get_company_details().legal_details
    )
    assert (
        booking_owner_gst_details.get('legal_name')
        == booking_company_legal_details.legal_name
    )
    assert (
        booking_owner_gst_details.get('gstin_num') == booking_company_legal_details.tin
    )


def test_create_booking_without_travel_agent_should_should_copy_details_from_booking_owner(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        set_travel_agent_details=False,
        set_company_details=False,
        set_booking_owner_name_hyphen=True,
        set_booking_owner_phone_null=True,
        channel="direct",
    )
    booking_owner_gst_details = create_booking_payload["booking_owner"]["gst_details"]
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response.get('data').get('booking_id'))
    travel_agent_details = booking_aggregate.get_travel_agent_details().legal_details
    assert (
        booking_owner_gst_details.get('legal_name') == travel_agent_details.legal_name
    )
    assert booking_owner_gst_details.get('gstin_num') == travel_agent_details.tin


def test_update_customers_gst_info_should_update_booking(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
    hotel_repo,
):
    for billed_entity_category in [
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.TRAVEL_AGENT,
    ]:
        checkin_date = dateutils.subtract(dateutils.current_datetime(), days=1)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
        create_booking_payload = create_new_booking_payload(
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=1,
            status=BookingStatus.CONFIRMED,
            channel=BookingChannels.DIRECT.value,
            default_billed_entity_category=billed_entity_category.value,
            make_booking_owner_name_and_guest_name_same=False,
        )
        payload = {"data": create_booking_payload}
        booking_id = make_booking(client, payload, expected_status_code=200)
        booking_aggregate = booking_repo.load(booking_id)
        booking_owner = booking_aggregate.get_booking_owner()
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        assert len(bill_aggregate.billed_entities) == 3
        customer_details = [
            {
                'first_name': 'Kadam',
                'last_name': 'Jain',
                'id': booking_owner.customer_id,
                'gst_details': {
                    "address": {
                        "city": "string",
                        "country": "string",
                        "field_1": "string",
                        "field_2": "string",
                        "pincode": "string",
                        "state": "string",
                    },
                    "gstin_num": "string",
                    "has_lut": True,
                    "is_sez": True,
                    'legal_name': "changed_to_new",
                },
            },
        ]
        update_customer_payload = create_update_customer_payload(
            customer_details, booking_aggregate.booking.version
        )
        resp = update_customer(
            client, update_customer_payload, booking_aggregate.booking_id
        )
        assert resp.status_code == 200
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        assert len(bill_aggregate.billed_entities) == 3
        if billed_entity_category == BilledEntityCategory.BOOKER_COMPANY:
            assert (
                booking_aggregate.booking.company_details.legal_details.legal_name
                == customer_details[0]['gst_details']['legal_name']
            )
        else:
            assert (
                booking_aggregate.booking.travel_agent_details.legal_details.legal_name
                == customer_details[0]['gst_details']['legal_name']
            )


def test_update_customer_gst_info_should_update_booking(
    active_hotel_aggregate,
    room_type_inventory_repo,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    room_stay_overflow_repo,
    hotel_repo,
):
    for billed_entity_category in [
        BilledEntityCategory.BOOKER_COMPANY,
        BilledEntityCategory.TRAVEL_AGENT,
    ]:
        checkin_date = dateutils.subtract(dateutils.current_datetime(), days=1)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
        create_booking_payload = create_new_booking_payload(
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=1,
            status=BookingStatus.CONFIRMED,
            channel=BookingChannels.DIRECT.value,
            default_billed_entity_category=billed_entity_category.value,
            make_booking_owner_name_and_guest_name_same=False,
        )
        payload = {"data": create_booking_payload}
        booking_id = make_booking(client, payload, expected_status_code=200)
        booking_aggregate = booking_repo.load(booking_id)
        booking_owner = booking_aggregate.get_booking_owner()
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        assert len(bill_aggregate.billed_entities) == 3
        customer_detail = {
            'first_name': 'Kadam',
            'last_name': 'Jain',
            'id': booking_owner.customer_id,
            'gst_details': {
                "address": {
                    "city": "string",
                    "country": "string",
                    "field_1": "string",
                    "field_2": "string",
                    "pincode": "string",
                    "state": "string",
                },
                "gstin_num": "string",
                "has_lut": True,
                "is_sez": True,
                'legal_name': "changed_to_new",
            },
        }
        update_customer_payload = {
            "data": customer_detail,
            "resource_version": booking_aggregate.booking.version,
        }
        patch_customer_details(
            client,
            booking_aggregate.booking_id,
            booking_owner.customer_id,
            update_customer_payload,
        )
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        assert len(bill_aggregate.billed_entities) == 3
        if billed_entity_category == BilledEntityCategory.BOOKER_COMPANY:
            assert (
                booking_aggregate.booking.company_details.legal_details.legal_name
                == customer_detail['gst_details']['legal_name']
            )
        else:
            assert (
                booking_aggregate.booking.travel_agent_details.legal_details.legal_name
                == customer_detail['gst_details']['legal_name']
            )


def test_edit_booking_with_guarantee_information_and_no_payments(client, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.RESERVED,
        reference_number='REF-2',
        set_booking_guarantee_information=True,
    )
    create_booking_payload['payments'] = []
    with mock_guarantee_enabled_config(True):
        booking_id = make_booking(client, {'data': create_booking_payload})
        booking_aggregate = booking_repo.load(booking_id)
        assert booking_aggregate.booking.status == BookingStatus.CONFIRMED
        assert (
            booking_aggregate.guarantee_information.to_json()
            == create_booking_payload['guarantee_information']
        )

        patch_booking_payload = {
            'data': {'guarantee_information': None},
            'resource_version': booking_aggregate.booking.version,
        }
        patch_booking(client, booking_id, patch_booking_payload, 200)
        booking_aggregate = booking_repo.load(booking_id)
        assert booking_aggregate.guarantee_information is None
        assert booking_aggregate.booking.status == BookingStatus.RESERVED


def test_edit_booking_with_no_guarantee_information_but_with_payments(
    client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.RESERVED,
        reference_number='REF-2',
        set_booking_guarantee_information=True,
    )
    with mock_guarantee_enabled_config(True):
        booking_id = make_booking(client, {"data": create_booking_payload})
        booking_aggregate = booking_repo.load(booking_id)
        assert booking_aggregate.booking.status == BookingStatus.CONFIRMED
        assert (
            booking_aggregate.guarantee_information.to_json()
            == create_booking_payload['guarantee_information']
        )

        patch_booking_payload = {
            'data': {'guarantee_information': None},
            'resource_version': booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, patch_booking_payload, 200)
        booking_aggregate = booking_repo.load(booking_id)
        assert booking_aggregate.guarantee_information == GuaranteeInformation(
            GuaranteeTypes.PAYMENT_GUARANTEE
        )
        assert booking_aggregate.booking.status == BookingStatus.CONFIRMED


def test_edit_booking_with_no_guarantee_information_but_with_payments_and_guarantee_disabled(
    client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.RESERVED,
        reference_number='REF-2',
        set_booking_guarantee_information=True,
    )
    with mock_guarantee_enabled_config(False):
        booking_id = make_booking(client, {"data": create_booking_payload})
        booking_aggregate = booking_repo.load(booking_id)
        assert booking_aggregate.booking.status == BookingStatus.CONFIRMED
        assert (
            booking_aggregate.guarantee_information.to_json()
            == create_booking_payload["guarantee_information"]
        )

        patch_booking_payload = {
            'data': {'guarantee_information': None},
            'resource_version': booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, patch_booking_payload, 200)
        booking_aggregate = booking_repo.load(booking_id)
        assert booking_aggregate.guarantee_information is None
        assert booking_aggregate.booking.status == BookingStatus.CONFIRMED
