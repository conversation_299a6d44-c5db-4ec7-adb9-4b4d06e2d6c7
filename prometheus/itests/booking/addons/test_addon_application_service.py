from datetime import timed<PERSON><PERSON>

import pytest
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from object_registry import locate_instance
from prometheus.application.booking.command_handlers.add_expense import (
    AddExpenseCommandHandler,
)
from prometheus.application.booking.command_handlers.bulk_create_addon import (
    BulkCreateAddonCommandHandler,
)
from prometheus.application.booking.command_handlers.create_addon import (
    CreateAddonCommandHandler,
)
from prometheus.application.booking.command_handlers.delete_addon import (
    DeleteAddonCommandHandler,
)
from prometheus.application.booking.command_handlers.patch_addon import (
    PatchAddonCommandHandler,
)
from prometheus.application.booking.query_handlers.get_addon import GetAddonQueryHandler
from prometheus.application.booking.query_handlers.get_addon_by_id import (
    GetAddonByIdQueryHandler,
)
from prometheus.domain.booking.exceptions import ExpenseItemNotFound
from prometheus.domain.booking.services.addon_domain_service import AddonDomainService
from prometheus.tests.factories.aggregate_factories import (
    BillFactory,
    BookingAggregateFactory,
)
from prometheus.tests.factories.entity_factories import (
    AddonFactory,
    ChargeFactory,
    ChargeItemFactory,
)
from prometheus.tests.mockers import mock_role_manager, mock_tax_calculator_service
from ths_common.constants.billing_constants import ChargeStatus
from ths_common.constants.booking_constants import ExpenseStatus
from ths_common.utils.dateutils import get_settlement_date
from ths_common.value_objects import UserData

headers = {'X-User-Type': 'super-admin'}


@pytest.fixture
def some_addon():
    addon = AddonFactory.build()
    return addon


@pytest.fixture(scope='session')
def add_expense_v1_command_handler():
    return locate_instance(AddExpenseCommandHandler)


@pytest.fixture(scope='session')
def get_addon_by_id_addon_query_handler() -> GetAddonByIdQueryHandler:
    return locate_instance(GetAddonByIdQueryHandler)


@pytest.fixture(scope='session')
def delete_addon_command_handler() -> DeleteAddonCommandHandler:
    return locate_instance(DeleteAddonCommandHandler)


@pytest.fixture(scope='session')
def bulk_create_addon_command_handler() -> BulkCreateAddonCommandHandler:
    return locate_instance(BulkCreateAddonCommandHandler)


@pytest.fixture(scope='session')
def create_addon_command_handler() -> CreateAddonCommandHandler:
    return locate_instance(CreateAddonCommandHandler)


@pytest.fixture(scope='session')
def patch_addon_command_handler() -> PatchAddonCommandHandler:
    return locate_instance(PatchAddonCommandHandler)


@pytest.fixture(scope='session')
def get_addon_query_handler() -> GetAddonQueryHandler:
    return locate_instance(GetAddonQueryHandler)


@pytest.fixture
def addon_domain_service() -> AddonDomainService:
    return AddonDomainService()


def setup_booking_bill_customer_expense_item(booking_repo, bill_repo):
    bill_aggregate = BillFactory()
    for billed_entity in bill_aggregate.billed_entities:
        for account in billed_entity.accounts:
            bill_aggregate.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(booking_aggregate)

    return booking_aggregate, bill_aggregate


def test_add_addon_to_booking(
    booking_repo,
    get_addon_by_id_addon_query_handler,
    create_addon_command_handler,
    checkin_addon_dict,
    bill_repo,
    lunch_item,
):
    booking_aggregate, bill_aggregate = setup_booking_bill_customer_expense_item(
        booking_repo, bill_repo
    )
    booking = booking_aggregate.booking

    checkin_addon_dict['expense_item_id'] = lunch_item.expense_item_id

    # Add addon
    with mock_role_manager():
        user_data = UserData(user_type="super-admin")
        addon = create_addon_command_handler.handle(
            booking.booking_id, checkin_addon_dict, user_data
        )
    assert addon is not None

    # Verify if addon is created
    saved_addon = get_addon_by_id_addon_query_handler.handle(
        booking.booking_id, addon.addon.addon_id
    )
    assert saved_addon is not None
    assert saved_addon.addon.room_stay_id == checkin_addon_dict['room_stay_id']

    # Verify if expense is added to booking
    updated_booking = booking_repo.load(booking.booking_id)
    assert len(updated_booking.expenses) == 1
    assert (
        updated_booking.expenses[0].expense_item_id
        == checkin_addon_dict['expense_item_id']
    )

    # Verify if charge is added to bill
    updated_bill = bill_repo.load(booking.bill_id)
    assert updated_bill is not None
    assert len(updated_bill.charges) == 1
    assert updated_bill.charges[0].item.sku_category_id == lunch_item.sku_category_id

    # Check if integtation event is sent
    # TODO: Set hotel context above
    # integration_event = integration_event_repo.get_oldest_unpublished_event()
    # bill = [entity for entity in integration_event.integration_event.body.get('events')
    #         if entity['entity_name'] == 'bill'][0]
    # charges = bill['payload']['charges']
    # charge_item_names = [charge['item']['name'] for charge in charges]
    # assert checkin_addon_dict['name'] in charge_item_names


def test_add_v2_linked_addon_to_booking_adds_new_charge_to_bill(
    booking_repo,
    get_addon_by_id_addon_query_handler,
    create_addon_command_handler,
    checkin_addon_dict_for_v2,
    bill_repo,
    extra_guest,
):
    bill_aggregate = BillFactory(charges=[ChargeFactory(charge_id=1)])
    for billed_entity in bill_aggregate.billed_entities:
        for account in billed_entity.accounts:
            bill_aggregate.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(booking_aggregate)

    booking = booking_aggregate.booking

    checkin_addon_dict_for_v2['expense_item_id'] = extra_guest.expense_item_id
    checkin_addon_dict_for_v2['posttax_price'] = Money(100, CurrencyType.INR)
    checkin_addon_dict_for_v2['pretax_price'] = Money(0, CurrencyType.INR)

    # Add addon
    with mock_role_manager():
        user_data = UserData(user_type="super-admin")
        addon = create_addon_command_handler.handle(
            booking.booking_id, checkin_addon_dict_for_v2, user_data
        )
    assert addon is not None

    # Verify if addon is created
    saved_addon = get_addon_by_id_addon_query_handler.handle(
        booking.booking_id, addon.addon.addon_id
    )
    assert saved_addon is not None
    assert saved_addon.addon.room_stay_id == checkin_addon_dict_for_v2['room_stay_id']
    assert len(saved_addon.expenses) == 1

    # Verify if expense is added to booking
    updated_booking = booking_repo.load(booking.booking_id)

    # Charge should be added for linked addon too
    updated_bill = bill_repo.load(booking.bill_id)
    assert updated_bill is not None
    assert len(updated_bill.charges) == 2

    # TODO: Verify linked charge detail

    # Check if integtation event is sent
    # TODO: Set hotel context above
    # integration_event = integration_event_repo.get_oldest_unpublished_event()
    # bill = [entity for entity in integration_event.integration_event.body.get('events')
    #         if entity['entity_name'] == 'bill'][0]
    # charges = bill['payload']['charges']
    # charge_item_names = [charge['item']['name'] for charge in charges]
    # assert checkin_addon_dict['name'] in charge_item_names


@pytest.mark.skip("Linked Addon Update API is no more supported")
def test_add_v2_linked_addon_update_adds_more_charge_to_bill(
    booking_repo,
    create_addon_command_handler,
    get_addon_by_id_addon_query_handler,
    patch_addon_command_handler,
    checkin_addon_dict_for_v2,
    bill_repo,
    update_addon_date_dict_linked,
    extra_guest,
):
    bill_aggregate = BillFactory(charges=[ChargeFactory(charge_id=1)])
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(booking_aggregate)

    booking = booking_aggregate.booking

    checkin_addon_dict_for_v2['expense_item_id'] = extra_guest.expense_item_id
    checkin_addon_dict_for_v2['posttax_price'] = Money(100, CurrencyType.INR)
    checkin_addon_dict_for_v2['pretax_price'] = Money(0, CurrencyType.INR)

    # Add addon
    with mock_role_manager():
        user_data = UserData(user_type="super-admin")
        addon = create_addon_command_handler.handle(
            booking.booking_id, checkin_addon_dict_for_v2, user_data
        )
    assert addon is not None
    updated_booking = booking_repo.load(booking.booking_id)
    # Verify if addon is created
    saved_addon = get_addon_by_id_addon_query_handler.handle(
        booking.booking_id, addon.addon.addon_id
    )
    assert saved_addon is not None
    assert saved_addon.addon.room_stay_id == checkin_addon_dict_for_v2['room_stay_id']
    assert len(saved_addon.expenses) == 1, "Total expenses added by addon should be 1"

    updated_booking = booking_repo.load(booking.booking_id)

    updated_bill = bill_repo.load(booking.bill_id)
    assert updated_bill is not None
    assert (
        len(updated_bill.charges) == 2
    ), "Total charge in booking should be 2. 1 for room and 1 for addon expense"

    # update addon
    with mock_role_manager():
        user_data = UserData(user_type="super-admin")
        addon = patch_addon_command_handler.handle(
            booking.booking_id,
            saved_addon.addon.addon_id,
            saved_addon.addon.version,
            update_addon_date_dict_linked,
            user_data,
        )

    updated_addon = get_addon_by_id_addon_query_handler.handle(
        booking.booking_id, addon.addon.addon_id
    )
    assert updated_addon is not None
    assert updated_addon.addon.room_stay_id == checkin_addon_dict_for_v2['room_stay_id']
    assert (
        len(updated_addon.expenses) == 2
    ), "Update addon with quantity = 2 should have added 1 more expense"

    # Should add 1 more charge for addon update
    updated_booking = booking_repo.load(booking.booking_id)

    updated_bill = bill_repo.load(booking.bill_id)
    assert updated_bill is not None
    assert len(updated_bill.charges) == 4, (
        "Total charge should be 4. 1 for room, 1 deleted charge, and 2 for new "
        "addon update"
    )

    # Check if integtation event is sent
    # TODO: Set hotel context above
    # integration_event = integration_event_repo.get_oldest_unpublished_event()
    # bill = [entity for entity in integration_event.integration_event.body.get('events')
    #         if entity['entity_name'] == 'bill'][0]
    # charges = bill['payload']['charges']
    # charge_item_names = [charge['item']['name'] for charge in charges]
    # assert checkin_addon_dict['name'] in charge_item_names


def test_add_v2_addon_update(
    booking_repo,
    patch_addon_command_handler,
    create_addon_command_handler,
    checkin_addon_dict_for_v2,
    bill_repo,
    update_addon_date_dict_linked,
    lunch_item,
):
    bill_aggregate = BillFactory(charges=[ChargeFactory(charge_id=1)])
    for billed_entity in bill_aggregate.billed_entities:
        for account in billed_entity.accounts:
            bill_aggregate.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(booking_aggregate)

    booking = booking_aggregate.booking

    checkin_addon_dict_for_v2['expense_item_id'] = lunch_item.expense_item_id
    checkin_addon_dict_for_v2['posttax_price'] = Money(100, CurrencyType.INR)
    checkin_addon_dict_for_v2['pretax_price'] = Money(0, CurrencyType.INR)

    # Add addon
    with mock_role_manager():
        user_data = UserData(user_type="super-admin")
        addon = create_addon_command_handler.handle(
            booking.booking_id, checkin_addon_dict_for_v2, user_data
        )
    assert addon is not None

    # Verify if addon is created
    assert addon.expenses == [1]

    updated_booking = booking_repo.load(booking_aggregate.booking.booking_id)
    assert all(
        map(lambda ex: not ex.deleted, updated_booking.expenses)
    ), "Some expenses are in 'deleted' state"
    assert all(
        map(lambda ex: ex.charge_id is not None, updated_booking.expenses)
    ), "Charges are not linked"

    updated_bill = bill_repo.load(bill_aggregate.bill.bill_id)
    assert all(
        map(lambda ex: not ex.deleted, updated_bill.charges)
    ), "Some charges are in 'deleted' state"
    assert addon.addon.room_stay_id == checkin_addon_dict_for_v2['room_stay_id']

    # update addon
    with mock_role_manager():
        user_data = UserData(user_type="super-admin")
        addon = patch_addon_command_handler.handle(
            booking.booking_id,
            addon.addon.addon_id,
            addon.addon.version,
            update_addon_date_dict_linked,
            user_data,
        )

    # Verify if expense is added to booking
    updated_booking = booking_repo.load(booking.booking_id)

    # Verify if charge is added to bill
    updated_bill = bill_repo.load(booking.bill_id)
    assert updated_bill is not None
    assert len(updated_bill.charges) == 4

    # Check if integtation event is sent
    # TODO: Set hotel context above
    # integration_event = integration_event_repo.get_oldest_unpublished_event()
    # bill = [entity for entity in integration_event.integration_event.body.get('events')
    #         if entity['entity_name'] == 'bill'][0]
    # charges = bill['payload']['charges']
    # charge_item_names = [charge['item']['name'] for charge in charges]
    # assert checkin_addon_dict['name'] in charge_item_names


def test_add_addon_for_non_existing_expense_item_id_should_throw_error(
    booking_repo,
    create_addon_command_handler,
    checkin_addon_dict,
    bill_repo,
):
    booking_aggregate, bill_aggregate = setup_booking_bill_customer_expense_item(
        booking_repo, bill_repo
    )
    booking = booking_aggregate.booking

    checkin_addon_dict['expense_item_id'] = '123123123123'

    user_data = UserData(user_type="super-admin")
    with pytest.raises(ExpenseItemNotFound):
        create_addon_command_handler.handle(
            booking.booking_id, checkin_addon_dict, user_data
        )


def test_add_addon_to_booking_with_existing_addon(
    booking_repo,
    create_addon_command_handler,
    checkin_addon_dict,
    bill_repo,
    lunch_item,
):
    booking_aggregate, bill_aggregate = setup_booking_bill_customer_expense_item(
        booking_repo, bill_repo
    )
    booking = booking_aggregate.booking

    checkin_addon_dict['expense_item_id'] = lunch_item.expense_item_id

    user_data = UserData(user_type="super-admin")
    with mock_role_manager():
        addon = create_addon_command_handler.handle(
            booking.booking_id, checkin_addon_dict, user_data
        )
        assert addon is not None
        addon2 = create_addon_command_handler.handle(
            booking.booking_id, checkin_addon_dict, user_data
        )
        assert addon2 is not None


def test_update_booking_dates_should_update_addons_and_expenses_and_charges(
    booking_repo,
    create_addon_command_handler,
    get_addon_query_handler,
    all_days_addon_dict,
    bill_repo,
    lunch_item,
):
    booking_aggregate, bill_aggregate = setup_booking_bill_customer_expense_item(
        booking_repo, bill_repo
    )
    all_days_addon_dict['expense_item_id'] = lunch_item.expense_item_id

    booking = booking_aggregate.booking

    user_data = UserData(user_type="super-admin")
    with mock_role_manager():
        addon = create_addon_command_handler.handle(
            booking.booking_id, all_days_addon_dict, user_data
        )

    assert addon is not None

    addons = get_addon_query_handler.handle(
        booking.booking_id, linked_addons_required=True
    )

    addon_domain_service = AddonDomainService()
    updated_booking = booking_repo.load(booking.booking_id)
    assert (
        len(updated_booking.expenses)
        == (booking.checkout_date - booking.checkin_date).days + 1
    )

    new_checkout_date = booking.checkout_date + timedelta(days=3)
    updated_booking.booking.checkout_date = new_checkout_date
    updated_booking.room_stays[0].checkout_date = new_checkout_date
    updated_booking.room_stays[0].guest_stays[0].checkout_date = new_checkout_date

    settlement_date = get_settlement_date(new_checkout_date, next_month=True)

    with mock_role_manager():
        addon_domain_service.update_addon_dates(
            updated_booking, addons[0], booking.checkin_date, new_checkout_date
        )

    booking_repo.update(updated_booking)
    assert (
        len(updated_booking.expenses)
        == (new_checkout_date - booking.checkin_date).days + 1
    )
    assert (
        len(addons[0].expenses) == (new_checkout_date - booking.checkin_date).days + 1
    )


def test_delete_addons(
    booking_repo,
    delete_addon_command_handler,
    create_addon_command_handler,
    all_days_addon_dict,
    bill_repo,
):
    booking_aggregate, bill_aggregate = setup_booking_bill_customer_expense_item(
        booking_repo, bill_repo
    )

    # Add addon
    user_data = UserData(user_type="super-admin")
    with mock_role_manager():
        addon = create_addon_command_handler.handle(
            booking_aggregate.booking.booking_id, all_days_addon_dict, user_data
        )

    # Verify if addon is created properly
    assert addon is not None
    assert addon.expenses == [1, 2]

    updated_booking = booking_repo.load(booking_aggregate.booking.booking_id)
    assert all(
        map(lambda ex: not ex.deleted, updated_booking.expenses)
    ), "Some expenses are in 'deleted' state"
    assert all(
        map(lambda ex: ex.charge_id is not None, updated_booking.expenses)
    ), "Charges are not linked"

    updated_bill = bill_repo.load(bill_aggregate.bill.bill_id)
    assert all(
        map(lambda ex: not ex.deleted, updated_bill.charges)
    ), "Some charges are in 'deleted' state"

    # delete addon
    delete_addon_command_handler.handle(
        booking_aggregate.booking.booking_id, addon.addon.addon_id, user_data
    )

    # verify end states
    updated_booking = booking_repo.load(booking_aggregate.booking.booking_id)
    assert all(
        map(lambda ex: ex.status == ExpenseStatus.CANCELLED, updated_booking.expenses)
    ), "All expenses are in not 'deleted' state"

    updated_bill = bill_repo.load(bill_aggregate.bill.bill_id)
    assert all(
        map(lambda ch: ch.status == ChargeStatus.CANCELLED, updated_bill.charges)
    ), "All charges are in not 'deleted' state"

    # Check if integtation event is sent
    # TODO: Set hotel context for this check
    # last_integration_event = integration_event_repo.get_all_unpublished_events()[-1]
    # bill = [entity for entity in last_integration_event.integration_event.body.get('events')
    #         if entity['entity_name'] == 'bill'][0]
    # charges = bill['payload']['charges']
    # assert charges == []


@pytest.mark.skip("Linked Addon Delete API is no more supported")
def test_delete_of_linked_addons(
    booking_repo,
    create_addon_command_handler,
    get_addon_by_id_addon_query_handler,
    delete_addon_command_handler,
    bill_repo,
    checkin_addon_dict_for_v2,
    extra_guest,
):
    charge_item = ChargeItemFactory(details={'occupancy': 3, 'room_type_code': 'RT01'})
    bill_aggregate = BillFactory(
        charges=[ChargeFactory(charge_id=1, charge_item=charge_item)]
    )
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(booking_aggregate)

    booking = booking_aggregate.booking

    checkin_addon_dict_for_v2['expense_item_id'] = extra_guest.expense_item_id
    checkin_addon_dict_for_v2['posttax_price'] = Money(100, CurrencyType.INR)
    checkin_addon_dict_for_v2['pretax_price'] = Money(0, CurrencyType.INR)

    # Add addon
    with mock_role_manager():
        user_data = UserData(user_type="super-admin")
        addon = create_addon_command_handler.handle(
            booking.booking_id, checkin_addon_dict_for_v2, user_data
        )
    assert addon is not None

    # Verify if addon is created
    saved_addon = get_addon_by_id_addon_query_handler.handle(
        booking.booking_id, addon.addon.addon_id
    )
    assert saved_addon is not None
    assert saved_addon.addon.room_stay_id == checkin_addon_dict_for_v2['room_stay_id']

    updated_booking = booking_repo.load(booking_aggregate.booking.booking_id)
    assert all(
        map(lambda ex: not ex.deleted, updated_booking.expenses)
    ), "Some expenses are in 'deleted' state"
    assert all(
        map(lambda ex: ex.charge_id is not None, updated_booking.expenses)
    ), "Charges are not linked"

    updated_bill = bill_repo.load(bill_aggregate.bill.bill_id)
    assert all(
        map(lambda ex: not ex.deleted, updated_bill.charges)
    ), "Some charges are in 'deleted' state"

    # delete addon
    with mock_role_manager():
        delete_addon_command_handler.handle(
            booking_aggregate.booking.booking_id, saved_addon.addon.addon_id, user_data
        )

    updated_booking = booking_repo.load(booking_aggregate.booking.booking_id)
    assert all(
        map(lambda ex: ex.deleted, updated_booking.expenses)
    ), "All expenses are in not 'deleted' state"

    updated_bill = bill_repo.load(bill_aggregate.bill.bill_id)
    assert all(
        map(lambda ex: not ex.deleted, updated_bill.charges)
    ), "All charges are in not 'deleted' state"

    # Check if integtation event is sent
    # TODO: Set hotel context for this check
    # last_integration_event = integration_event_repo.get_all_unpublished_events()[-1]
    # bill = [entity for entity in last_integration_event.integration_event.body.get('events')
    #         if entity['entity_name'] == 'bill'][0]
    # charges = bill['payload']['charges']
    # assert charges == []


def test_addon_quantity_of_2_creates_2_expenses_per_day(
    booking_repo,
    create_addon_command_handler,
    all_days_addon_dict,
    bill_repo,
):
    quantity = 2
    all_days_addon_dict['quantity'] = quantity
    booking_aggregate, bill_aggregate = setup_booking_bill_customer_expense_item(
        booking_repo, bill_repo
    )

    booking_duration = (
        booking_aggregate.booking.checkout_date - booking_aggregate.booking.checkin_date
    ).days + 1

    # Add addon
    user_data = UserData(user_type="super-admin")
    with mock_role_manager():
        addon = create_addon_command_handler.handle(
            booking_aggregate.booking.booking_id, all_days_addon_dict, user_data
        )

    # Verify if 4 expenses are linked to addon
    assert addon is not None
    assert len(addon.expenses) == quantity * booking_duration

    # Verify if 4 expenses are created in booking
    updated_booking = booking_repo.load(booking_aggregate.booking.booking_id)
    assert len(updated_booking.expenses) == quantity * booking_duration

    # Verify if 4 charges are created in bill
    updated_bill = bill_repo.load(bill_aggregate.bill.bill_id)
    assert len(updated_bill.charges) == quantity * booking_duration


def test_extending_booking_dates_should_not_add_new_expense_as_per_addons(
    client,
    booking_repo,
    create_addon_command_handler,
    all_days_addon_dict,
    bill_repo,
    lunch_item,
    addon_repo,
    active_hotel_aggregate,
):
    booking_aggregate, bill_aggregate = setup_booking_bill_customer_expense_item(
        booking_repo, bill_repo
    )

    # Addon configuration is charge_checkout = True
    all_days_addon_dict['expense_item_id'] = lunch_item.expense_item_id

    booking = booking_aggregate.booking

    user_data = UserData(user_type="super-admin")
    with mock_role_manager():
        addon = create_addon_command_handler.handle(
            booking.booking_id, all_days_addon_dict, user_data
        )

    assert addon is not None

    updated_booking = booking_repo.load(booking.booking_id)
    assert len(updated_booking.expenses) == 2

    # Update booking dates
    new_checkout_date = booking.checkout_date + timedelta(days=1)

    old_expense_count = len(updated_booking.expenses)

    # Extend booking checkout date
    url = "v1/bookings/" + booking.booking_id + "/room-stays/1"
    booking_edit_body = """
    {
     "resource_version" : %s,
     "data": {
     "checkout_date": "%s",
     "prices": [
       {
         "applicable_date": "%s",
         "bill_to_type": "company",
         "posttax_amount": "100",
         "type": "credit"
       }
     ]}}
     """ % (
        booking.version + 1,
        new_checkout_date,
        booking.checkout_date,
    )

    with mock_role_manager():
        response = client.patch(
            url,
            data=booking_edit_body,
            content_type='application/json',
            headers=headers,
        )

    assert response.status_code == 200

    updated_booking = booking_repo.load(booking.booking_id)
    assert updated_booking.booking.checkout_date.date() == new_checkout_date.date()

    # Expense count should remain the same
    assert len(updated_booking.expenses) == old_expense_count

    from prometheus import crs_context

    crs_context.set_hotel_context(active_hotel_aggregate)

    updated_addon = addon_repo.load(addon.addon.addon_id)
    assert set(updated_addon.expenses) == {
        expense.expense_id for expense in updated_booking.expenses
    }

    updated_bill = bill_repo.load(bill_aggregate.bill.bill_id)
    charge_ids = [charge.charge_id for charge in updated_bill.charges]
    for expense in updated_booking.expenses:
        assert expense.charge_id in charge_ids
