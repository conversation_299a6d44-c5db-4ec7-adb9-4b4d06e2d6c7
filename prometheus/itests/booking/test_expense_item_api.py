def test_get_expense_items(client, lunch_item):
    # Make api call
    headers = {'X-User-Type': 'super-admin'}
    url = 'v1/expense-items'
    response = client.get(url, content_type='application/json', headers=headers)

    # Validate
    assert response.status_code == 200

    expense_items = response.json['data']
    # TODO: This assumes that 9 expense items are created in session scope fixture in conftest_sku. Shouldn't make
    #  that assumption
    assert len(expense_items) == 11

    item_names = [item['name'] for item in expense_items]
    assert lunch_item.name in item_names
