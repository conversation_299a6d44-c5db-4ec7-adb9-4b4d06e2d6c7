import datetime
import json
from decimal import Decimal

import pytz
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import date_range

from prometheus.itests.api_wrappers.billing_wrappers import edit_charge
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    checkout_booking,
)
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.api_wrappers.room_stay_wrapper import change_room_stay_rate_plan
from prometheus.itests.booking.test_booking_v2 import make_booking
from prometheus.itests.booking.test_edit_chares import edit_billing_instruction_payload
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
    create_checkout_payload,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_with_charge_decision_payload,
)
from prometheus.tests.factories.aggregate_factories import (
    BillFactory,
    BookingAggregateFactoryV2,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_rate_manager_client,
    mock_role_manager,
    mock_tax_calculator_service,
    mock_tenant_config,
)
from prometheus.tests.test_utils import today_minus_days
from ths_common.constants.billing_constants import BilledEntityCategory, ChargeStatus
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.catalog_constants import SellerType


def test_room_stay_list_with_rate_plan_inclusion_should_add_charges(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response['data']['booking_id'])
    new_room_stays = create_booking_payload.get('room_stays')
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert len(bill_aggregate.charges) == 4
    end_date = checkout_date - datetime.timedelta(days=1)
    for room_stay in new_room_stays:
        room_stay["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.replace(
                    hour=00,
                    minute=00,
                    second=00,
                    microsecond=00,
                    tzinfo=pytz.timezone('Pacific/Auckland'),
                ).isoformat(),
                "end_date": end_date.replace(
                    hour=00,
                    minute=00,
                    second=00,
                    microsecond=00,
                    tzinfo=pytz.timezone('Pacific/Auckland'),
                ).isoformat(),
            }
        ]
    url = "v1/bookings/" + booking_aggregate.booking_id + "/room-stays-list"
    with mock_role_manager():
        response = client.post(
            url,
            data=json.dumps(
                {
                    "data": {"room_stays": new_room_stays},
                    "resource_version": booking_aggregate.booking.version,
                }
            ),
            content_type='application/json',
            headers={'X-User-Type': 'super-admin'},
        )

    assert response.status_code == 201
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert len(bill_aggregate.charges) == 12


def add_guest_stay_request(checkin_date, checkout_date):
    price_applicable_dates = list(date_range(checkin_date, checkout_date))

    payload = {
        "data": {
            "age_group": "adult",
            "checkin_date": checkin_date.isoformat(),
            "checkout_date": checkout_date.isoformat(),
            "new_room_stay_prices": [
                {
                    "applicable_date": applicable_date.isoformat(),
                    "bill_to_type": "guest",
                    "type": "non-credit",
                    "pretax_amount": 2623,
                }
                for applicable_date in price_applicable_dates
            ],
        },
        "resource_version": 2,
    }
    return json.dumps(payload)


def test_guest_add_should_cancel_charges_rate_plan_inclusion_charges(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=14)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response['data']['booking_id'])
    new_room_stays = create_booking_payload.get('room_stays')
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert len(bill_aggregate.charges) == 1
    end_date = checkout_date - datetime.timedelta(days=1)
    for room_stay in new_room_stays:
        room_stay["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.replace(
                    hour=00,
                    minute=00,
                    second=00,
                    microsecond=00,
                    tzinfo=pytz.timezone('Pacific/Auckland'),
                ).isoformat(),
                "end_date": end_date.replace(
                    hour=00,
                    minute=00,
                    second=00,
                    microsecond=00,
                    tzinfo=pytz.timezone('Pacific/Auckland'),
                ).isoformat(),
            }
        ]
    url = "v1/bookings/" + booking_aggregate.booking_id + "/room-stays-list"
    with mock_role_manager():
        response = client.post(
            url,
            data=json.dumps(
                {
                    "data": {"room_stays": new_room_stays},
                    "resource_version": booking_aggregate.booking.version,
                }
            ),
            content_type='application/json',
            headers={'X-User-Type': 'super-admin'},
        )

    assert response.status_code == 201
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    booking_id = booking_aggregate.booking.booking_id
    assert len(bill_aggregate.charges) == 3
    req = add_guest_stay_request(checkin_date, checkout_date)
    url = f"/v1/bookings/{booking_id}/room-stays/2/guest-stays"
    with mock_role_manager():
        response = client.post(
            url,
            data=req,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    active_charges = [
        charge
        for charge in bill_aggregate.charges
        if charge.status == ChargeStatus.CREATED
    ]
    cancelled_charges = [
        charge
        for charge in bill_aggregate.charges
        if charge.status == ChargeStatus.CANCELLED
    ]
    assert len(active_charges) == 2
    assert len(cancelled_charges) == 1


def test_get_roomstay(booking_repo, bill_repo, client):
    bill = BillFactory(bill__bill_id="BIL13", with_charges=True)
    checkin_date = today_minus_days(2)
    checkout_date = today_minus_days(1)

    booking_aggregate = BookingAggregateFactoryV2(
        booking__checkin_date=checkin_date,
        booking__checkout_date=checkout_date,
        booking__status=BookingStatus.CHECKED_IN,
        booking__bill_id=bill.bill_id,
    )
    booking_repo.save(booking_aggregate)
    booking = booking_aggregate.booking

    url = (
        'v2/bookings/'
        + booking.booking_id
        + f'/room-stays/{booking_aggregate.room_stays[0].room_stay_id}'
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    assert response.json.get('data') is not None
    assert response.json.get('resource_version') is not None


def test_get_roomstay_required_fields(booking_repo, bill_repo, client):
    bill = BillFactory(bill__bill_id="BIL13", with_charges=True)
    checkin_date = today_minus_days(2)
    checkout_date = today_minus_days(1)

    booking_aggregate = BookingAggregateFactoryV2(
        booking__checkin_date=checkin_date,
        booking__checkout_date=checkout_date,
        booking__status=BookingStatus.CHECKED_IN,
        booking__bill_id=bill.bill_id,
    )
    booking_repo.save(booking_aggregate)
    booking = booking_aggregate.booking

    url = (
        'v2/bookings/'
        + booking.booking_id
        + f'/room-stays/{booking_aggregate.room_stays[0].room_stay_id}'
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    data = response.json.get('data')
    root_fields = response.json.get('data').keys()
    assert root_fields.__contains__('actual_checkin_date') is True
    assert root_fields.__contains__('actual_checkout_date') is True
    assert root_fields.__contains__('allowed_actions') is True
    assert root_fields.__contains__('checkin_date') is True
    assert root_fields.__contains__('checkout_date') is True
    assert root_fields.__contains__('is_overflow') is True

    guest_stays = data.get('guest_stays')
    guest_stays_fields = guest_stays[0].keys()
    assert guest_stays_fields.__contains__('actual_checkin_date') is True
    assert guest_stays_fields.__contains__('actual_checkout_date') is True
    assert guest_stays_fields.__contains__('age_group') is True
    assert guest_stays_fields.__contains__('allowed_actions') is True
    assert guest_stays_fields.__contains__('checkin_date') is True
    assert guest_stays_fields.__contains__('checkout_date') is True
    assert guest_stays_fields.__contains__('guest_details') is True

    guest_details = guest_stays[0].get('guest_details')
    guest_details_fields = guest_details.keys()
    assert guest_details_fields.__contains__('dummy') is True
    assert guest_details_fields.__contains__('name') is True
    assert guest_details_fields.__contains__('phone') is True
    assert guest_details_fields.__contains__('is_primary') is True

    assert guest_stays_fields.__contains__('guest_id') is True
    assert guest_stays_fields.__contains__('guest_stay_id') is True
    assert guest_stays_fields.__contains__('status') is True
    assert guest_stays_fields.__contains__('stay_end') is True
    assert guest_stays_fields.__contains__('stay_start') is True

    assert root_fields.__contains__('room_id') is True
    assert root_fields.__contains__('room_number') is True
    assert root_fields.__contains__('room_rate_plans_ids') is True
    assert root_fields.__contains__('room_stay_id') is True
    assert root_fields.__contains__('room_type_id') is True
    assert root_fields.__contains__('status') is True
    assert root_fields.__contains__('stay_end') is True
    assert root_fields.__contains__('stay_start') is True


def test_get_roomstay_key_fields_values(booking_repo, bill_repo, client):
    bill = BillFactory(bill__bill_id="BIL13", with_charges=True)
    checkin_date = today_minus_days(2)
    checkout_date = today_minus_days(1)

    booking_aggregate = BookingAggregateFactoryV2(
        booking__checkin_date=checkin_date,
        booking__checkout_date=checkout_date,
        booking__status=BookingStatus.CHECKED_IN,
        booking__bill_id=bill.bill_id,
    )
    booking_repo.save(booking_aggregate)
    booking = booking_aggregate.booking

    url = (
        'v2/bookings/'
        + booking.booking_id
        + f'/room-stays/{booking_aggregate.room_stays[0].room_stay_id}'
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    assert response.json.get('resource_version') == 1
    data = response.json.get('data')
    guest_stays = data.get('guest_stays')
    guest_details = guest_stays[0].get('guest_details')
    assert guest_details.get('dummy') is False
    assert guest_details.get('name') is not None
    assert guest_details.get('name').get('first_name') == 'Tyrion'
    assert guest_details.get('name').get('last_name') == 'Lannister'
    assert guest_details.get('phone') is not None
    assert guest_details.get('phone').get('country_code') == "+91"
    assert guest_details.get('phone').get('number') == '123'
    assert guest_details.get('is_primary') is False


def get_room_stay_extension(start=0, end=3, resource_version=1):
    return json.dumps(
        {
            "data": {
                "checkin_date": (
                    dateutils.current_datetime() + datetime.timedelta(days=start)
                ).isoformat(),
                "checkout_date": (
                    dateutils.current_datetime() + datetime.timedelta(days=end)
                ).isoformat(),
                "prices": [
                    {
                        "applicable_date": (
                            dateutils.current_datetime()
                            + datetime.timedelta(days=end - 1)
                        ).isoformat(),
                        "bill_to_type": "company",
                        "pretax_amount": 100,
                        "type": "non-credit",
                        "rate_plan_id": "1",
                        "rate_plan_reference_id": "014f54aa-60fe-4ff2-b4cc-9b5fd2933815",
                    }
                ],
                "rate_plan_inclusions": [
                    {
                        "sku_id": "377",
                        "pretax_amount": "345",
                        "start_date": (
                            dateutils.current_datetime()
                            + datetime.timedelta(days=end - 1)
                        ).isoformat(),
                        "end_date": (
                            dateutils.current_datetime()
                            + datetime.timedelta(days=end - 1)
                        ).isoformat(),
                    }
                ],
            },
            "resource_version": resource_version,
        }
    )


def get_room_stay_reduction_payload(
    start=0, end=3, resource_version=1, pre_pone_checkin=False
):
    return json.dumps(
        {
            "data": {
                "checkin_date": (
                    dateutils.current_datetime() + datetime.timedelta(days=start)
                ).isoformat(),
                "checkout_date": (
                    dateutils.current_datetime() + datetime.timedelta(days=end)
                ).isoformat(),
                "prices": [
                    {
                        "applicable_date": (
                            dateutils.current_datetime()
                            + datetime.timedelta(days=start)
                        ).isoformat(),
                        "bill_to_type": "company",
                        "pretax_amount": 100,
                        "type": "non-credit",
                        "rate_plan_id": "1",
                        "rate_plan_reference_id": "014f54aa-60fe-4ff2-b4cc-9b5fd2933815",
                    }
                ]
                if pre_pone_checkin
                else [],
            },
            "resource_version": resource_version,
        }
    )


def extend_rooms_stay_by_shifting(
    client, booking_id, check_in_date, checkout_date, resource_version=1
):
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            room_stay_url = (
                'v1/bookings/{0}/room-stays/{1}/update-stay-duration'.format(
                    booking_id, 1
                )
            )
            room_stay_extension_payload = get_room_stay_extension(
                start=check_in_date,
                end=checkout_date + 1,
                resource_version=resource_version,
            )
            response = client.post(
                room_stay_url,
                data=room_stay_extension_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )

            assert response.status_code == 200


def reduce_and_then_restore_rooms_stay_by_shifting(
    client, booking_id, check_in_date, checkout_date
):
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            room_stay_url = (
                'v1/bookings/{0}/room-stays/{1}/update-stay-duration'.format(
                    booking_id, 1
                )
            )
            room_stay_extension_payload = get_room_stay_reduction_payload(
                start=check_in_date, end=checkout_date - 1
            )
            response = client.post(
                room_stay_url,
                data=room_stay_extension_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )

            assert response.status_code == 200
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            room_stay_url = (
                'v1/bookings/{0}/room-stays/{1}/update-stay-duration'.format(
                    booking_id, 1
                )
            )
            room_stay_extension_payload = get_room_stay_extension(
                start=check_in_date, end=checkout_date, resource_version=2
            )
            response = client.post(
                room_stay_url,
                data=room_stay_extension_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )

            assert response.status_code == 200


def reduce_rooms_stay_by_shifting(
    client,
    booking_id,
    check_in_date,
    checkout_date,
    pre_pone_checkin=False,
    resource_version=1,
):
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            room_stay_url = (
                'v1/bookings/{0}/room-stays/{1}/update-stay-duration'.format(
                    booking_id, 1
                )
            )
            if pre_pone_checkin:
                end = checkout_date
                start = check_in_date - 1
            else:
                end = checkout_date - 1
                start = check_in_date
            room_stay_extension_payload = get_room_stay_reduction_payload(
                start=start,
                end=end,
                pre_pone_checkin=pre_pone_checkin,
                resource_version=resource_version,
            )
            response = client.post(
                room_stay_url,
                data=room_stay_extension_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )

            assert response.status_code == 200


def test_change_room_stay_date_of_booking_with_no_incl_should_add_rate_plan_inclusion(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response['data']['booking_id'])
    extend_rooms_stay_by_shifting(
        client,
        booking_aggregate.booking_id,
        13,
        15,
        resource_version=booking_aggregate.booking.version,
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert len(bill_aggregate.charges) == 6


def test_change_room_stay_date_of_booking_should_use_default(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    create_booking_payload['travel_agent_details']['legal_details'].update(
        {'is_sez': True, 'has_lut': True}
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response['data']['booking_id'])
    extend_rooms_stay_by_shifting(
        client,
        booking_aggregate.booking_id,
        13,
        15,
        resource_version=booking_aggregate.booking.version,
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    for charge in bill_aggregate.charges:
        assert charge.tax_amount.amount == Decimal("0")
        sum_of_tax_details = sum(
            [tax_item.tax_amount.amount for tax_item in charge.tax_details]
        )
        assert (
            charge.tax_amount.amount + charge.pretax_amount.amount
            == charge.posttax_amount.amount
        )
        assert charge.tax_amount.amount == sum_of_tax_details

        for split in charge.charge_splits:
            assert split.tax.amount == Decimal("0")
            assert split.tax.amount + split.pre_tax.amount == split.post_tax.amount
            sum_of_tax_details = sum(
                [tax_item.tax_amount.amount for tax_item in split.tax_details]
            )
            assert split.tax.amount == sum_of_tax_details


def test_change_room_stay_date_of_booking_with_incl_should_add_new_rate_plan_inclusion(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    for room_saty in create_booking_payload['room_stays']:
        room_saty["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response['data']['booking_id'])
    extend_rooms_stay_by_shifting(
        client,
        booking_aggregate.booking_id,
        13,
        15,
        resource_version=booking_aggregate.booking.version,
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert len(bill_aggregate.charges) == 10


def test_reduce_and_then_restore_room_stay_date_of_booking_with_incl_should_restore_total_charge(
    client, booking_repo, bill_repo
):
    # PROM-9610
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    for room_saty in create_booking_payload['room_stays']:
        room_saty["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response['data']['booking_id'])
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    initial_charges = sum(
        [
            charge.posttax_amount.amount
            for charge in bill_aggregate.charges
            if charge.status == ChargeStatus.CREATED
        ]
    )
    reduce_and_then_restore_rooms_stay_by_shifting(
        client, booking_aggregate.booking_id, 13, 15
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert (
        sum(
            [
                charge.posttax_amount.amount
                for charge in bill_aggregate.charges
                if charge.status == ChargeStatus.CREATED
            ]
        )
        == initial_charges
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert len(bill_aggregate.charges) == 10


def cancel_charges(client, bill_id, charge_ids):
    data = [{"charge_id": _id, "status": "cancelled"} for _id in charge_ids]
    payload = json.dumps({"data": data, "resource_version": 1})
    with mock_role_manager() and mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            patch_charges_url = '/v1/bills/{0}/charges'.format(bill_id)
            response = client.patch(
                patch_charges_url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )

            assert response.status_code == 200


def test_change_room_stay_date_to_new_window_should_cancel_old_charges_and_add_new(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    for room_saty in create_booking_payload['room_stays']:
        room_saty["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response['data']['booking_id'])
    extend_rooms_stay_by_shifting(
        client,
        booking_aggregate.booking_id,
        15,
        15,
        resource_version=booking_aggregate.booking.version,
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    active_charges = [
        charge
        for charge in bill_aggregate.charges
        if charge.status == ChargeStatus.CREATED
    ]
    cancelled_charges = [
        charge
        for charge in bill_aggregate.charges
        if charge.status == ChargeStatus.CANCELLED
    ]
    assert len(active_charges) == 6
    assert len(cancelled_charges) == 4


def test_change_room_stay_rate_plan_should_cancel_old_charges_and_add_new(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    for room_saty in create_booking_payload['room_stays']:
        room_saty["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response['data']['booking_id'])
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    active_charges = [
        charge
        for charge in bill_aggregate.charges
        if charge.status == ChargeStatus.CREATED
    ]
    assert len(active_charges) == 8
    change_room_stay_rate_plan(
        client,
        booking_aggregate.booking_id,
        checkin_date,
        checkout_date,
        resource_version=booking_aggregate.booking.version,
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    active_charges = [
        charge
        for charge in bill_aggregate.charges
        if charge.status == ChargeStatus.CREATED
    ]
    cancelled_charges = [
        charge
        for charge in bill_aggregate.charges
        if charge.status == ChargeStatus.CANCELLED
    ]
    assert len(active_charges) == 8
    assert len(cancelled_charges) == 2


def test_update_rate_plan_when_rate_man_disabled_should_raise_error(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    for room_saty in create_booking_payload['room_stays']:
        room_saty["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_aggregate = booking_repo.load(response['data']['booking_id'])
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    active_charges = [
        charge
        for charge in bill_aggregate.charges
        if charge.status == ChargeStatus.CREATED
    ]
    assert len(active_charges) == 8
    change_room_stay_rate_plan(
        client,
        booking_aggregate.booking_id,
        checkin_date,
        checkout_date,
        rate_manager_enabled=False,
        resource_version=booking_aggregate.booking.version,
    )


def test_mark_no_show_of_last_room_should_fail(client, booking_repo, bill_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=-1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
        with_payments=False,
    )
    for room_saty in create_booking_payload['room_stays']:
        room_saty["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]

    booking_id = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=False,
    )
    mark_no_show_request = {"room_stays": [{"room_stay_id": 1}]}
    with mock_catalog_client(), mock_role_manager(), mock_rate_manager_client():
        url = "v1/bookings/{}/mark-noshow".format(booking_id)
        payload = {"data": mark_no_show_request, "resource_version": 1}
        payload = json.dumps(payload)
        response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == 400


def test_mark_no_show_of_all_rooms_without_room_stay_id_should_pass(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=-1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
        with_payments=False,
    )
    for room_saty in create_booking_payload['room_stays']:
        room_saty["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]

    booking_id = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=False,
    )
    mark_no_show_request = {"room_stays": []}
    with mock_catalog_client(), mock_role_manager(), mock_rate_manager_client():
        url = "v1/bookings/{}/mark-noshow".format(booking_id)
        payload = {"data": mark_no_show_request, "resource_version": 1}
        payload = json.dumps(payload)
        response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == 200


def test_mark_no_show_of_last_room_night_should_fail(
    client, booking_repo, bill_repo, active_hotel_aggregate, hotel_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=-1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=0)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
        with_payments=False,
    )
    for room_saty in create_booking_payload['room_stays']:
        room_saty["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]

    booking_id = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=False,
    )

    # roll_over_business_date(active_hotel_aggregate, hotel_repo)

    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version, checkin_date
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        guest_id=4,
        room_stay_id=1,
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        invoice_group_id,
        checkout_datetime=checkout_date,
    )
    checkout_booking(client, booking_id, checkout_payload)
    mark_no_show_request = {"room_stays": [{"room_stay_id": 2}]}
    booking_aggregate = booking_repo.load(booking_id)
    with mock_catalog_client(), mock_role_manager(), mock_rate_manager_client():
        url = "v1/bookings/{}/mark-noshow".format(booking_id)
        payload = {
            "data": mark_no_show_request,
            "resource_version": booking_aggregate.booking.version,
        }
        payload = json.dumps(payload)
        response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        # payment pending
        assert response.status_code == 400


def test_change_room_stay_date_of_booking_create_charges_without_splits(
    client, booking_repo, bill_repo, sku_category_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    create_booking_payload['company_details']['legal_details'].update(
        {'is_sez': False, 'has_lut': False}
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    bill_id = response['data']['bill_id']
    sku_category_aggregate = sku_category_repo.load_all()[1]
    sku_category_aggregate.sku_category.has_slab_based_taxation = True
    sku_category_repo.update(sku_category_aggregate)
    percentage = 50
    bill_aggregate = bill_repo.load(bill_id)
    for charge in bill_aggregate.charges:
        bill_aggregate = bill_repo.load(bill_id)
        payload = edit_billing_instruction_payload(bill_aggregate.version, percentage)
        edit_charge(client, bill_id, charge.charge_id, payload)

    booking_aggregate = booking_repo.load(response['data']['booking_id'])
    extend_rooms_stay_by_shifting(
        client,
        booking_aggregate.booking_id,
        13,
        15,
        resource_version=booking_aggregate.booking.version,
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert len(bill_aggregate.charges) == 6

    total_charge_splits = 0
    for charge in bill_aggregate.charges:
        total_charge_splits += len(charge.charge_splits)
    assert (
        total_charge_splits == 10
    )  # 8 older charge with splits and 2 new charge without splits


def test_do_not_retain_splits_in_change_rateplan(
    client, booking_repo, bill_repo, sku_category_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    create_booking_payload['company_details']['legal_details'].update(
        {'is_sez': False, 'has_lut': False}
    )
    for room_saty in create_booking_payload['room_stays']:
        room_saty["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "345",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            }
        ]

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    bill_id = response['data']['bill_id']
    booking_id = response['data']['booking_id']
    booking_aggregate = booking_repo.load(booking_id)

    sku_category_aggregate = sku_category_repo.load_all()[1]
    sku_category_aggregate.sku_category.has_slab_based_taxation = True
    sku_category_repo.update(sku_category_aggregate)
    percentage = 50
    bill_aggregate = bill_repo.load(bill_id)
    for charge in bill_aggregate.charges:
        bill_aggregate = bill_repo.load(bill_id)
        payload = edit_billing_instruction_payload(bill_aggregate.version, percentage)
        edit_charge(client, bill_id, charge.charge_id, payload)

    change_room_stay_rate_plan(
        client,
        booking_aggregate.booking_id,
        checkin_date,
        checkout_date,
        resource_version=5,
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)

    new_stay_charges = []
    for charge in bill_aggregate.charges:
        if charge.status.value == 'created':
            assert len(charge.charge_splits) == 1
            if charge.item.sku_category_id == 'stay':
                new_stay_charges.append(charge.charge_id)
    new_charge_id_map_charges = []
    for charge_id in booking_repo.load(booking_id).room_stays[0].charge_id_map.values():
        new_charge_id_map_charges.append(charge_id)

    assert sorted(new_stay_charges) == sorted(new_charge_id_map_charges)
