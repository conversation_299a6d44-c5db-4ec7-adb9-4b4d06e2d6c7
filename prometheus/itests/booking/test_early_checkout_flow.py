import datetime
import json
from contextlib import contextmanager

from treebo_commons.utils import dateutils

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    checkout_booking,
)
from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v3
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.billing.test_payment_api import create_cash_register_and_session
from prometheus.itests.booking.test_booking_v2 import make_booking
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkout_payload,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v2_request,
    create_expenses_v3_request,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_payload,
    create_preview_invoice_with_charge_decision_payload,
)
from prometheus.tests.mockers import mock_tenant_config
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    CashRegisterNames,
    ChargeStatus,
    PaymentChannels,
    PaymentInstruction,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingStatus, ExpenseStatus


def test_early_checkout_flow_one_room_stay_and_guest_with_room_stay_charged_to_booker_expense_to_guest_post_only_room_stay(
    client,
    booking_repo,
    bill_repo,
    active_hotel_aggregate,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response['data']['booking_id']

    booking_aggregate = booking_repo.load(booking_id)
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version, booking_aggregate.booking.checkin_date
    )
    checkin_booking(client, booking_id, checkin_payload)
    billed_entity_id = booking_aggregate.get_customer('2').billed_entity_id
    from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2

    expense_v2_request = create_expenses_v2_request(
        payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
        expense_status=ExpenseStatus.CREATED,
        single_charge_split=False,
        dates=[
            dateutils.add(dateutils.current_datetime(), days=0),
            dateutils.add(dateutils.current_datetime(), days=1),
            dateutils.add(dateutils.current_datetime(), days=2),
        ],
        billed_entity_accounts=[(billed_entity_id, 1)],
        charge_to=["2"],
    )
    json_data = add_expenses_v2(client, booking_id, expense_v2_request)
    expense_charge_ids_added = [expense['charge_id'] for expense in json_data['data']]
    charge_ids = expense_charge_ids_added[0]
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )
    pending_booked_charges_for_today = response.get('data').get(
        'pending_booked_charges_for_today'
    )
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )
    invoice_group_id = response.get('data').get('invoice_group_id')
    booking_aggregate = booking_repo.load(booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    response = checkout_booking(client, booking_id, checkout_payload)
    booking_invoice_group_aggregate = booking_invoice_group_repo.load(invoice_group_id)
    for invoice_id in booking_invoice_group_aggregate.invoice_ids:
        invoice_aggregate = invoice_repo.load(invoice_id)
        assert (
            invoice_aggregate.invoice.posttax_amount
            == bill_aggregate.get_charge(1).posttax_amount
        )


def test_early_checkout_flow_with_charge_decision_having_invoice_balance_in_sync_with_billed_entity_balance(
    client,
    booking_repo,
    bill_repo,
    active_hotel_aggregate,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response['data']['booking_id']

    booking_aggregate = booking_repo.load(booking_id)
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version, booking_aggregate.booking.checkin_date
    )
    checkin_booking(client, booking_id, checkin_payload)
    billed_entity_id = booking_aggregate.get_customer('2').billed_entity_id
    from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2

    expense_v2_request = create_expenses_v2_request(
        payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
        expense_status=ExpenseStatus.CONSUMED,
        single_charge_split=False,
        dates=[
            dateutils.add(dateutils.current_datetime(), days=0),
            dateutils.add(dateutils.current_datetime(), days=1),
            dateutils.add(dateutils.current_datetime(), days=2),
        ],
        billed_entity_accounts=[(billed_entity_id, 1)],
        charge_to=["2"],
    )
    json_data = add_expenses_v2(client, booking_id, expense_v2_request)
    expense_charge_ids_added = [expense['charge_id'] for expense in json_data['data']]
    source_charge_id = expense_charge_ids_added[0]
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    url = (
        'v1/bills/'
        + booking_aggregate.bill_id
        + '/charges/'
        + str(source_charge_id)
        + '/charge-splits/'
        + str(1)
        + '/allowances'
    )
    new_allowance = dict(
        data=dict(pretax_amount='50 INR', remarks='add new allowance'),
        resource_version=bill_aggregate.current_version(),
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        url,
        data=json.dumps(new_allowance),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200

    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )
    pending_booked_charges_for_today = response.get('data').get(
        'pending_booked_charges_for_today'
    )
    pending_booked_allowances_for_today = response.get('data').get(
        'pending_booked_allowances_for_today'
    )[0]
    booking_aggregate = booking_repo.load(booking_id)
    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )
    invoice_group_id = response.get('data').get('invoice_group_id')
    booking_aggregate = booking_repo.load(booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                payment_splits=[
                    dict(
                        billed_entity_account=dict(
                            billed_entity_id=3, account_number=1
                        ),
                        amount="246",
                    )
                ],
                amount='226',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

        payment_id = response.json.get('data').get('payment_id')

        bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)

    response = checkout_booking(client, booking_id, checkout_payload)
    booking_invoice_group_aggregate = booking_invoice_group_repo.load(invoice_group_id)
    bill_aggregate = bill_repo.load(bill_aggregate.bill_id)
    for invoice_id in booking_invoice_group_aggregate.invoice_ids:
        invoice_aggregate = invoice_repo.load(invoice_id)
        assert invoice_aggregate.invoice.posttax_amount == sum(
            [
                cs.post_tax
                for c in bill_aggregate.charges
                for cs in c.charge_splits
                if c.status == ChargeStatus.CONSUMED
                and cs.billed_entity_account
                == invoice_aggregate.invoice.billed_entity_account
            ]
        )


def test_checkout_of_one_room_stay_out_of_two_where_all_charges_mapped_to_booker_doesnt_cancel_the_current_date_room_stay_of_guest_checking_out(
    client,
    booking_repo,
    bill_repo,
    active_hotel_aggregate,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    temp_create_new_room,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response['data']['booking_id']

    booking_aggregate = booking_repo.load(booking_id)
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
        guest_id=2,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
        room_stay_id=2,
        room_id=16,
        guest_id=3,
    )
    checkin_booking(client, booking_id, checkin_payload)
    bill_aggregate = bill_repo.load(bill_id=booking_aggregate.bill_id)
    booking_aggregate = booking_repo.load(booking_id)
    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        room_stay_id=2,
        booked_charges_to_post=[3],
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )
    invoice_group_id = response.get('data').get('invoice_group_id')
    booking_aggregate = booking_repo.load(booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    response = checkout_booking(client, booking_id, checkout_payload)
    assert (
        len(
            [
                c
                for c in bill_repo.load(bill_aggregate.bill_id).charges
                if c.status == ChargeStatus.CANCELLED
            ]
        )
        == 1
    )


def test_full_checkout_of_booking_with_two_room_stays_cancels_the_future_charges_and_todays_charges_provided_for_cancellation(
    client,
    booking_repo,
    bill_repo,
    active_hotel_aggregate,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    temp_create_new_room,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response['data']['booking_id']

    booking_aggregate = booking_repo.load(booking_id)
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
        guest_id=2,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
        room_stay_id=2,
        room_id=16,
        guest_id=3,
    )
    checkin_booking(client, booking_id, checkin_payload)
    bill_aggregate = bill_repo.load(bill_id=booking_aggregate.bill_id)
    booking_aggregate = booking_repo.load(booking_id)
    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        room_stay_id=2,
        booked_charges_to_post=[3],
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )
    invoice_group_id = response.get('data').get('invoice_group_id')
    booking_aggregate = booking_repo.load(booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    response = checkout_booking(client, booking_id, checkout_payload)
    assert (
        len(
            [
                c
                for c in bill_repo.load(bill_aggregate.bill_id).charges
                if c.status == ChargeStatus.CANCELLED
            ]
        )
        == 1
    )
    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        room_stay_id=1,
        guest_id=2,
    )
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )
    invoice_group_id = response.get('data').get('invoice_group_id')
    booking_aggregate = booking_repo.load(booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    response = checkout_booking(client, booking_id, checkout_payload)
    assert (
        len(
            [
                c
                for c in bill_repo.load(bill_aggregate.bill_id).charges
                if c.status == ChargeStatus.CANCELLED
            ]
        )
        == 2
    )


@contextmanager
def mock_current_time():
    real_date_time = dateutils.current_datetime

    def mocked_date_time(timezone=None):
        current_date_time = datetime.datetime.now(
            dateutils.get_timezone(timezone=timezone)
        )
        nex_day_early_morning = dateutils.add(current_date_time, days=1).replace(hour=4)
        return nex_day_early_morning

    dateutils.current_datetime = mocked_date_time
    yield
    dateutils.current_datetime = real_date_time


def test_early_checkout_done_on_early_morning_before_night_audit_should_consume_charges_properly(
    client,
    booking_repo,
    bill_repo,
    active_hotel_aggregate,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    temp_create_new_room,
):
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=True,
        set_company_details=True,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response['data']['booking_id']
    expense_v3_request = create_expenses_v3_request(
        dates=[checkout_date],
        charge_to=["1"],
        quantity=1,
        single_charge_split=True,
        unit_post_tax="50",
        billed_entity_id=2,
    )
    add_expenses_v3(client, booking_id, expense_v3_request)
    booking_aggregate = booking_repo.load(booking_id)
    from prometheus.itests.payload_generators.booking_action_payload_generators import (
        create_checkin_payload,
    )

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
        guest_id=1,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    with mock_current_time():
        preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
            booking_aggregate.booking.version,
            active_hotel_aggregate,
            room_stay_id=1,
            booked_charges_to_post=[1, 2],
        )
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
        response = preview_invoice(
            booking_id, client, preview_invoice_payload, show_raw_response=True
        )
        invoice_group_id = response.get('data').get('invoice_group_id')
        booking_aggregate = booking_repo.load(booking_id)
        checkout_payload = create_checkout_payload(
            booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
        )
        checkout_booking(client, booking_id, checkout_payload)
        assert (
            len(
                [
                    c
                    for c in bill_repo.load(bill_aggregate.bill_id).charges
                    if c.status == ChargeStatus.CANCELLED
                ]
            )
            == 0
        )
        assert (
            len(
                [
                    c
                    for c in bill_repo.load(bill_aggregate.bill_id).charges
                    if c.status == ChargeStatus.CONSUMED
                ]
            )
            == 2
        )
