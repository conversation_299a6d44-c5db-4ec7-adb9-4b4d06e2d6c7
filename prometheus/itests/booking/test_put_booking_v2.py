import json

import pytest
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.itests.api_wrappers.addon_wrappers import add_addon
from prometheus.itests.api_wrappers.billing_wrappers import edit_charge
from prometheus.itests.billing.test_payment_api import create_cash_register_and_session
from prometheus.itests.booking.test_edit_chares import edit_billing_instruction_payload
from prometheus.itests.payload_generators.addon_payload_generators import (
    addons_request as create_addon_request,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    put_booking_payload,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_rate_manager_client,
    mock_role_manager,
    mock_rule_engine,
    mock_tax_calculator_service,
    mock_tenant_config,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    CashRegisterNames,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.catalog_constants import SellerType


def make_booking(
    client, payload, expected_status_code=200, return_complete_response=False
):
    with mock_catalog_client(), mock_role_manager(), mock_rate_manager_client():
        url = "v2/bookings"
        payload = json.dumps(payload)
        response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == expected_status_code
        if expected_status_code != 200:
            return response
        if return_complete_response:
            return response.json

        booking_id = response.json["data"]["booking_id"]
    return booking_id


@pytest.fixture
def open_booking_and_bill(booking_repo, booking_and_bill):
    booking_aggregate = booking_and_bill[0]
    booking_aggregate.booking.status = BookingStatus.CONFIRMED
    booking_repo.update(booking_aggregate)
    return booking_and_bill


def test_booking_create(create_booking_payload, client, bill_repo, booking_repo):
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            payload = json.dumps({"data": json.loads(create_booking_payload)})
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            assert len(bookings_response['data']['room_stays']) > 0

    booking_id = bookings_response['data']['booking_id']
    bill_id = bookings_response['data']['bill_id']
    reference_number = bookings_response['data']['reference_number']
    booking_aggregate = booking_repo.load(booking_id)

    bill_aggregate = bill_repo.load(bill_id)

    with mock_tenant_config(
        [
            {
                "config_name": "cashiering_enabled",
                "config_value": "true",
                "value_type": "boolean",
            }
        ]
    ):
        # Fdm only add payment for paid to hotel

        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CASH,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
            ),
            resource_version=1,
        )

        cash_register_id, cashier_session_id = create_cash_register_and_session(
            client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
        )

        headers = {"X-User-Type": "fdm"}
        response = None
        with mock_rule_engine():
            response = client.post(
                url,
                data=json.dumps(new_payment),
                content_type='application/json',
                headers=headers,
            )

        assert response.status_code == 200

    bill_aggregate = bill_repo.load(bill_id)
    for payments in bill_aggregate.payments:
        payments.post()
    bill_repo.update(bill_aggregate)

    bill_aggregate = bill_repo.load(bill_id)

    checkin_date = dateutils.add(dateutils.current_datetime())
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    payload = put_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        default_billed_entity=BilledEntityCategory.PRIMARY_GUEST.value,
        channel="hotel",
        sub_channel="walk-in",
        status=BookingStatus.CONFIRMED.value,
        reference_number=reference_number,
        resource_version=booking_aggregate.booking.version,
    )
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v2/bookings/' + booking_id
            payload = json.dumps(payload)
            response = client.put(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
    billed_entity_id = None
    bill_aggregate = bill_repo.load(bill_id)
    for be in bill_aggregate.billed_entities:
        if be.category == BilledEntityCategory.PRIMARY_GUEST:
            billed_entity_id = be.billed_entity_id

    for payment in bill_aggregate.payments:
        if payment.status == PaymentStatus.POSTED:
            assert payment.payor_billed_entity_id == billed_entity_id


def test_split_not_retain_after_put_booking(
    create_booking_payload, client, bill_repo, booking_repo, sku_category_repo
):
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            payload = json.dumps({"data": json.loads(create_booking_payload)})
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            assert len(bookings_response['data']['room_stays']) > 0
            bill_id = bookings_response['data']['bill_id']
            booking_id = bookings_response['data']['booking_id']
            reference_number = bookings_response['data']['reference_number']
    sku_category_aggregate = sku_category_repo.load_all()[1]
    sku_category_aggregate.sku_category.has_slab_based_taxation = True
    sku_category_repo.update(sku_category_aggregate)
    charge_id = '1'
    bill_aggregate = bill_repo.load(bill_id)
    percentage = 50
    payload = edit_billing_instruction_payload(bill_aggregate.version, percentage)
    edit_charge(client, bill_id, charge_id, payload)

    bill_aggregate = bill_repo.load(bill_id)
    for charge in bill_aggregate.charges:
        if charge.charge_id == int(charge_id):
            assert len(charge.charge_splits) > 1

    booking_aggregate = booking_repo.load(booking_id)
    checkin_date = dateutils.add(dateutils.current_datetime())
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    payload = put_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        default_billed_entity=BilledEntityCategory.PRIMARY_GUEST.value,
        channel="hotel",
        sub_channel="walk-in",
        status=BookingStatus.CONFIRMED.value,
        reference_number=reference_number,
        resource_version=booking_aggregate.booking.version,
    )
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v2/bookings/' + booking_id
            payload = json.dumps(payload)
            response = client.put(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
    charge_id = 2
    bill_aggregate = bill_repo.load(bill_id)
    for charge in bill_aggregate.charges:
        if charge.charge_id == int(charge_id):
            assert len(charge.charge_splits) == 1


def test_retain_addon_as_well_with_addon_charge_retention_put_booking(
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    sku_category_repo,
    addon_repo,
    active_hotel_aggregate,
):
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            payload = json.dumps({"data": json.loads(create_booking_payload)})
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            assert len(bookings_response['data']['room_stays']) > 0
            booking_id = bookings_response['data']['booking_id']
            reference_number = bookings_response['data']['reference_number']
            addon_id = add_addon(
                client,
                booking_id,
                create_addon_request(
                    room_stay_id=1, bill_to_type="guest", charge_type="non-credit"
                ),
            )
    booking_aggregate = booking_repo.load(booking_id)
    checkin_date = dateutils.add(dateutils.current_datetime())
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    payload = put_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        default_billed_entity=BilledEntityCategory.PRIMARY_GUEST.value,
        channel="hotel",
        sub_channel="walk-in",
        status=BookingStatus.CONFIRMED.value,
        reference_number=reference_number,
        resource_version=booking_aggregate.booking.version,
    )
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v2/bookings/' + booking_id
            payload = json.dumps(payload)
            response = client.put(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
    crs_context.set_hotel_context(active_hotel_aggregate)
    addon = addon_repo.load(addon_id)
    assert addon.deleted == False
