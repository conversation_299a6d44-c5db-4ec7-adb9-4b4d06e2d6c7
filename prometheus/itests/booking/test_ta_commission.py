import datetime
import json
from contextlib import contextmanager
from decimal import Decimal

from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    checkout_booking,
    delete_action,
    no_show_room_stay,
)
from prometheus.itests.api_wrappers.booking_wrappers import patch_booking
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.booking.booking_action_reversal.test_cancellation_reversal import (
    no_show_partial_booking_request,
)
from prometheus.itests.booking.test_booking_api import (
    cancellation_request,
    noshow_request,
)
from prometheus.itests.booking.test_patch_booking_api import (
    add_expenses_v3,
    make_booking_v2,
)
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
    create_checkout_payload,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v3_request,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_with_charge_decision_payload,
)
from prometheus.reporting.finance_erp_reporting.external_clients.finance_service_client import (
    FinanceServiceClient,
)
from prometheus.reporting.finance_erp_reporting.finance_reporting_service import (
    FinanceReportingService,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_company_profile_service,
    mock_rate_manager_client,
    mock_role_manager,
    mock_tax_call,
    mock_tenant_config_club_inclusion,
)
from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.booking_constants import BookingChannels, BookingStatus
from ths_common.constants.catalog_constants import SellerType


def set_ta_details(
    create_booking_payload,
    set_commission_details=True,
    sh_code='erp1ea',
    commission_value=10,
):
    create_booking_payload['travel_agent_details'] = {
        "legal_details": {
            "legal_name": "travel_agent_legal_name",
            "email": "<EMAIL>",
            "phone": {"country_code": "+91", "number": "1234512345"},
            "address": {
                "city": "string",
                "country": "string",
                "field_1": "string",
                "field_2": "string",
                "pincode": "123123",
                "state": "string",
            },
            "tin": "12ABCDE0000A1ZM",
            "is_sez": False,
            "has_lut": False,
            "client_internal_code": "travel-client-code",
            "external_reference_id": sh_code,
        }
    }

    if set_commission_details:
        create_booking_payload['travel_agent_details']['ta_commission_details'] = {
            "commission_type": 'percent',
            "commission_value": commission_value,
            "post_commission_amount": True,
        }


def test_create_booking_without_commission_should_add_commission_for_room_night(
    create_booking_payload, client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)

    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]

    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('25.00'), Decimal('30.00'), Decimal('15.00')}


def test_create_booking_with_commission_rule_in_payload_should_add_commission_for_room_night(
    create_booking_payload, client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload)

    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]

    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {
            Decimal('50.00'),
            Decimal('60.00'),
            Decimal('30.00'),
            Decimal('30.00'),
        }


def test_create_non_ta_booking_should_also_create_commission(
    create_booking_payload, client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload)

    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]

    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)
    cns = []
    for rs in booking_aggregate.room_stays:
        for rn_cn in rs.room_night_ta_commissions:
            cns.append(rn_cn.posttax_amount.amount)
    assert len(cns) == 4


def update_ta_commission_percentage(
    client, booking_id, version, new_percentage, expected_status_code=200
):
    url = "v1/bookings/{}/update-ta-commission".format(booking_id)
    payload = dict(
        data=dict(
            commission_type='percent',
            commission_value=new_percentage,
            commission_tax=dict(tax=0),
        ),
        resource_version=version,
    )
    payload = json.dumps(payload)
    response = client.post(
        url,
        data=payload,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == expected_status_code
    if expected_status_code != 200:
        return response
    return response.json


def test_update_ta_commission_percentage_should_recalculate_commission(
    create_booking_payload, client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.B2B.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload)
    response = make_booking_v2(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)
    update_ta_commission_percentage(
        client, booking_id, booking_aggregate.current_version(), 5.0
    )
    booking_aggregate = booking_repo.load(booking_id)
    cns = []
    for rs in booking_aggregate.room_stays:
        for rn_cn in rs.room_night_ta_commissions:
            cns.append(rn_cn.posttax_amount.amount)
    assert set(cns) == {Decimal('5')}


def test_create_direct_booking_should_not_create_commission(
    create_booking_payload, client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload)

    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]

    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion(), mock_tax_call():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)
    cns = []
    for rs in booking_aggregate.room_stays:
        for rn_cn in rs.room_night_ta_commissions:
            cns.append(rn_cn.posttax_amount.amount)
    assert len(cns) == 0


def test_create_booking_with_ta_profile_not_having_commission_rule_should_not_add_commission(
    create_booking_payload, client, booking_repo
):
    # Since commission rule is not present, we'll check if superhero_company_code is present or not.
    # if it's present we'll fetch commission details from company profile service (Empty in this case)
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(
        create_booking_payload, set_commission_details=False, sh_code='no_commission'
    )

    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]

    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert len(cns) == 0


def replace_booking_v2(
    client,
    payload,
    booking_id,
    expected_status_code=200,
    return_complete_response=False,
):
    with mock_catalog_client(), mock_role_manager(), mock_rate_manager_client(), mock_company_profile_service():
        url = f"v2/bookings/{booking_id}"
        payload = json.dumps(payload)
        response = client.put(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == expected_status_code
        if expected_status_code != 200:
            return response
        if return_complete_response:
            return response.json

        booking_id = response.json["data"]["booking_id"]
    return booking_id


def test_replace_booking_with_new_inclusion_charges_should_recalculate_the_commission(
    create_booking_payload,
    client,
    booking_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        booking_version = booking_aggregate.booking.version
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {
            Decimal('25.00'),
            Decimal('30.00'),
            Decimal('15.00'),
            Decimal('25.00'),
        }

        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "300",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            },
            {
                "sku_id": "378",
                "pretax_amount": "500",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            },
        ]
        create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "800",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            },
            {
                "sku_id": "378",
                "pretax_amount": "400",
                "start_date": checkin_date.isoformat(),
                "end_date": checkin_date.isoformat(),
            },
        ]
        replace_booking_v2(
            client=client,
            payload={
                "data": create_booking_payload,
                "resource_version": booking_version,
            },
            booking_id=booking_id,
            return_complete_response=True,
        )

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('65.00'), Decimal('45.00')}


def test_replace_booking_with_ta_profile_having_no_commission_rule_should_remove_commission(
    create_booking_payload,
    client,
    booking_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        booking_version = booking_aggregate.booking.version
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('25.00'), Decimal('30.00'), Decimal('15.00')}
        set_ta_details(
            create_booking_payload,
            set_commission_details=False,
            sh_code='no_commission',
        )
        create_booking_payload['room_stays'][0]['room_stay_id'] = 1
        replace_booking_v2(
            client=client,
            payload={
                "data": create_booking_payload,
                "resource_version": booking_version,
            },
            booking_id=booking_id,
            return_complete_response=True,
        )

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert len(cns) == 0


def test_replace_booking_without_commission_rule_should_preserve_existing(
    create_booking_payload,
    client,
    booking_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    with mock_tenant_config_club_inclusion():
        set_ta_details(
            create_booking_payload, set_commission_details=True, sh_code='N/A'
        )
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        booking_version = booking_aggregate.booking.version
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('50.00'), Decimal('60.00'), Decimal('30.00')}
        del create_booking_payload['travel_agent_details']['ta_commission_details']
        create_booking_payload['room_stays'][0]['room_stay_id'] = 1
        replace_booking_v2(
            client=client,
            payload={
                "data": create_booking_payload,
                "resource_version": booking_version,
            },
            booking_id=booking_id,
            return_complete_response=True,
        )

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('50.00'), Decimal('60.00'), Decimal('30.00')}


def test_replace_booking_without_commission_rule_explicitly_setting_null_should_remove_commission(
    create_booking_payload,
    client,
    booking_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    with mock_tenant_config_club_inclusion():
        set_ta_details(
            create_booking_payload, set_commission_details=True, sh_code='N/A'
        )
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        booking_version = booking_aggregate.booking.version
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('50.00'), Decimal('60.00'), Decimal('30.00')}
        create_booking_payload['travel_agent_details']['ta_commission_details'] = None
        replace_booking_v2(
            client=client,
            payload={
                "data": create_booking_payload,
                "resource_version": booking_version,
            },
            booking_id=booking_id,
            return_complete_response=True,
        )

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert len(cns) == 0


def test_replace_booking_with_ta_profile_change_should_not_preserve_existing(
    create_booking_payload,
    client,
    booking_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    with mock_tenant_config_club_inclusion():
        set_ta_details(
            create_booking_payload, set_commission_details=True, sh_code='N/A'
        )
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        booking_version = booking_aggregate.booking.version
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('50.00'), Decimal('60.00'), Decimal('30.00')}
        del create_booking_payload['travel_agent_details']['ta_commission_details']
        create_booking_payload['travel_agent_details']['legal_details'][
            'external_reference_id'
        ] = 'not_applicable'
        replace_booking_v2(
            client=client,
            payload={
                "data": create_booking_payload,
                "resource_version": booking_version,
            },
            booking_id=booking_id,
            return_complete_response=True,
        )
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert len(cns) == 0


def test_replace_booking_change_in_booking_source_to_direct_should_remove_commission(
    create_booking_payload,
    client,
    booking_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        booking_version = booking_aggregate.booking.version
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('25.00'), Decimal('30.00'), Decimal('15.00')}
        create_booking_payload['source']['channel_code'] = BookingChannels.DIRECT.value
        create_booking_payload['room_stays'][0]['room_stay_id'] = 1
        replace_booking_v2(
            client=client,
            payload={
                "data": create_booking_payload,
                "resource_version": booking_version,
            },
            booking_id=booking_id,
            return_complete_response=True,
        )

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert len(cns) == 0


def test_replace_booking_with_new_new_commission_rule_should_recalculate_commission(
    create_booking_payload,
    client,
    booking_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        booking_version = booking_aggregate.booking.version
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('25.00'), Decimal('30.00'), Decimal('15.00')}
        set_ta_details(create_booking_payload, commission_value=20)
        create_booking_payload['room_stays'][0]['room_stay_id'] = 1
        replace_booking_v2(
            client=client,
            payload={
                "data": create_booking_payload,
                "resource_version": booking_version,
            },
            booking_id=booking_id,
            return_complete_response=True,
        )

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('120.00'), Decimal('100.00'), Decimal('60.00')}


def test_edit_booking_for_ta_commission_change_should_recalculate_commission(
    client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('25.00'), Decimal('30.00'), Decimal('15.00')}
        travel_agent_details = create_booking_payload['travel_agent_details']
        travel_agent_details['ta_commission_details'] = {
            "commission_type": None,
            "commission_value": 20,
            "post_commission_amount": True,
        }
        payload = {
            "data": {"travel_agent_details": travel_agent_details},
            "resource_version": booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, payload, expected_status_code=200)
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('120.00'), Decimal('100.00'), Decimal('60.00')}


def test_edit_booking_for_ta_profile_change_should_recalculate_commission(
    client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    create_booking_payload['travel_agent_details']['legal_details'][
        'external_reference_id'
    ] = 'no_commission'
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert len(cns) == 0
        travel_agent_details = create_booking_payload['travel_agent_details']
        travel_agent_details['legal_details'][
            'external_reference_id'
        ] = 'rule_available'
        payload = {
            "data": {"travel_agent_details": travel_agent_details},
            "resource_version": booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, payload, expected_status_code=200)
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('25.00'), Decimal('30.00'), Decimal('15.00')}


def test_edit_booking_for_ta_rule_change_should_recalculate_commission(
    client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    create_booking_payload['travel_agent_details']['legal_details'][
        'external_reference_id'
    ] = 'no_commission'
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert len(cns) == 0
        travel_agent_details = create_booking_payload['travel_agent_details']
        travel_agent_details['ta_commission_details'] = {
            "commission_type": 'percent',
            "commission_value": 20,
            "post_commission_amount": True,
        }
        payload = {
            "data": {"travel_agent_details": travel_agent_details},
            "resource_version": booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, payload, expected_status_code=200)
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('120.00'), Decimal('100.00'), Decimal('60.00')}


def test_edit_booking_with_ta_having_no_commission_should_cancel_existing_commission(
    client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('25.00'), Decimal('30.00'), Decimal('15.00')}
        travel_agent_details = create_booking_payload['travel_agent_details']
        travel_agent_details['legal_details']['external_reference_id'] = 'no_commission'
        payload = {
            "data": {"travel_agent_details": travel_agent_details},
            "resource_version": booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, payload, expected_status_code=200)
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert len(cns) == 0


def test_edit_booking_with_ta_commission_details_not_given_should_preserve_current_commission(
    client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    set_ta_details(create_booking_payload, set_commission_details=True, sh_code='N/A')
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('50.00'), Decimal('60.00'), Decimal('30.00')}
        travel_agent_details = create_booking_payload['travel_agent_details']
        del create_booking_payload['travel_agent_details']['ta_commission_details']
        payload = {
            "data": {"travel_agent_details": travel_agent_details},
            "resource_version": booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, payload, expected_status_code=200)
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('50.00'), Decimal('60.00'), Decimal('30.00')}


def test_edit_booking_with_ta_commission_details_not_given_should_not_preserve_current_commission_if_profile_is_changed(
    client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    set_ta_details(create_booking_payload, set_commission_details=True, sh_code='N/A')
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('50.00'), Decimal('60.00'), Decimal('30.00')}
        travel_agent_details = create_booking_payload['travel_agent_details']
        del create_booking_payload['travel_agent_details']['ta_commission_details']
        travel_agent_details['legal_details'][
            'external_reference_id'
        ] = 'not_applicable'
        payload = {
            "data": {"travel_agent_details": travel_agent_details},
            "resource_version": booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, payload, expected_status_code=200)
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert len(cns) == 0


def test_add_rate_plan_expense_should_recalculate_the_commission(client, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=3)
    create_booking_payload = _create_booking_payload(checkin_date, checkout_date)
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.room_night_ta_commissions:
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('25.00'), Decimal('30.00'), Decimal('15.00')}
        expense_v3_request = create_expenses_v3_request(
            dates=[checkin_date, dateutils.add(checkin_date, days=1)],
            charge_to=["2", "3"],
            sku_id='377',
            unit_post_tax="300",
            for_rate_plan=True,
        )
        add_expenses_v3(client, booking_id, expense_v3_request)
        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert set(cns) == {Decimal('25.00'), Decimal('56.79'), Decimal('41.79')}


def test_mark_booking_cancel_should_mark_commission_as_cancelled_for_all_rooms(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cancel_payload = cancellation_request(booking_aggregate.booking.version)

        with mock_catalog_client(
            mocked_seller_type=SellerType.MARKETPLACE.value
        ), mock_role_manager(), mock_rate_manager_client():
            url = f'v1/bookings/{booking_id}/actions'
            payload = json.dumps(cancel_payload)
            cancel_response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin', 'X-User': 'someone'},
            )
            assert cancel_response.status_code == 200

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == []


def test_mark_booking_noshow_should_mark_commission_as_cancelled_for_all_rooms(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
):
    hotel_context = crs_context.get_hotel_context()
    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=-1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=0),
        hotel_context.checkout_time,
    )
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        noshow_payload = noshow_request(booking_aggregate.booking.version)

        with mock_catalog_client(
            mocked_seller_type=SellerType.MARKETPLACE.value
        ), mock_role_manager(), mock_rate_manager_client():
            url = f'v1/bookings/{booking_id}/actions'
            payload = json.dumps(noshow_payload)
            noshow_response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin', 'X-User': 'someone'},
            )
            assert noshow_response.status_code == 200

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == []


def test_booking_cancellation_reversal_should_create_commission_for_all_reverse_cancelled_rooms(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        cancel_payload = cancellation_request(booking_aggregate.booking.version)

        with mock_catalog_client(
            mocked_seller_type=SellerType.MARKETPLACE.value
        ), mock_role_manager(), mock_rate_manager_client():
            url = f'v1/bookings/{booking_id}/actions'
            payload = json.dumps(cancel_payload)
            cancel_response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin', 'X-User': 'someone'},
            )
            assert cancel_response.status_code == 200
            action_id = cancel_response.json['data']['action_id']
            delete_action(client, booking_id, action_id)

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('30.00'), Decimal('25.00')]


def test_booking_noshow_reversal_should_create_commission_for_all_reverse_noshow_rooms(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
):
    hotel_context = crs_context.get_hotel_context()
    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=-1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=0),
        hotel_context.checkout_time,
    )
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        noshow_payload = noshow_request(booking_aggregate.booking.version)

        with mock_catalog_client(
            mocked_seller_type=SellerType.MARKETPLACE.value
        ), mock_role_manager(), mock_rate_manager_client():
            url = f'v1/bookings/{booking_id}/actions'
            payload = json.dumps(noshow_payload)
            noshow_response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin', 'X-User': 'someone'},
            )
            assert noshow_response.status_code == 200
            action_id = noshow_response.json['data']['action_id']
            delete_action(client, booking_id, action_id)

        booking_aggregate = booking_repo.load(booking_id)
        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('30.00'), Decimal('25.00')]


def test_booking_partially_noshow_reversal_should_create_commission_for_reverse_noshow_rooms(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
    active_hotel_aggregate,
):
    hotel_context = crs_context.get_hotel_context()
    checkin_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=-1),
        hotel_context.checkin_time,
    )
    checkout_date = dateutils.datetime_at_given_time(
        dateutils.current_datetime() + datetime.timedelta(days=0),
        hotel_context.checkout_time,
    )
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "100",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
    ]
    with mock_tenant_config_club_inclusion():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)

        no_show_room_payload = no_show_partial_booking_request(
            booking_aggregate.booking.version, room_stay_id=2
        )
        action_id = no_show_room_stay(client, booking_id, no_show_room_payload)

        delete_action(client, booking_id, action_id)
        booking_aggregate = booking_repo.load(booking_id)

        cns = []
        for rs in booking_aggregate.room_stays:
            for rn_cn in rs.get_all_active_room_night_ta_commissions():
                cns.append(rn_cn.posttax_amount.amount)
        assert cns == [Decimal('30.00'), Decimal('25.00')]


@contextmanager
def mock_fin_client(expected_data_length):
    original_push = FinanceServiceClient.push_to_finance_portal

    def mocked_push(self, data_to_push, json_key):
        assert len(data_to_push) == expected_data_length

    FinanceServiceClient.push_to_finance_portal = mocked_push
    yield
    FinanceServiceClient.push_to_finance_portal = original_push


def test_checkout_booking_should_lock_commission(
    client, booking_repo, active_hotel_aggregate, hotel_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime())
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = _create_booking_payload(
        checkin_date, checkout_date, number_of_room=1, number_of_guest_stays=1
    )
    create_booking_payload_1 = _create_booking_payload(
        checkin_date, checkout_date, number_of_room=1, number_of_guest_stays=1
    )
    create_booking_payload_1['reference_number'] = 'Ref2'
    create_booking_payload['payments'] = [
        {
            "amount": "672.00",
            "comment": "string",
            "date_of_payment": dateutils.current_datetime().isoformat(),
            "paid_by": "guest",
            "paid_to": "hotel",
            "payment_channel": "online",
            "payment_details": {},
            "payment_mode": "cash",
            "payment_mode_sub_type": "Amex",
            "payment_ref_id": "string",
            "payment_type": "payment",
            "status": "done",
        }
    ]
    with mock_tenant_config_club_inclusion(), mock_tax_call(), mock_fin_client(
        1
    ), mock_catalog_client():
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload_1},
            return_complete_response=True,
        )
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        # Rollover Business Date
        roll_over_business_date(active_hotel_aggregate, hotel_repo)

        checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
        checkin_booking(client, booking_id, checkin_payload)
        booking_aggregate = booking_repo.load(booking_id)

        preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
            booking_aggregate.booking.version, active_hotel_aggregate
        )
        invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
        booking_aggregate = booking_repo.load(booking_id)

        checkout_payload = create_checkout_payload(
            booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
        )
        checkout_booking(client, booking_id, checkout_payload)
        booking_aggregate = booking_repo.load(booking_id)
        assert booking_aggregate.booking.status == BookingStatus.CHECKED_OUT
        finance_reporting_service = FinanceReportingService(
            booking_repository=booking_repo,
            bill_repository=None,
            invoice_repository=None,
            hotel_repository=hotel_repo,
            room_type_repository=None,
            sku_category_repository=None,
            job_registry=None,
            credit_note_repository=None,
            expense_item_repository=None,
            job_scheduler_service=None,
            reporting_job_publisher=None,
            einvoicing_service=None,
            marvin_service_client=None,
            payment_service_client=None,
            finance_service_client=FinanceServiceClient(),
            financial_data_reporting_service=None,
            invoice_report_repository=None,
            cn_report_repository=None,
            company_profile_service_client=None,
            tenant_settings=None,
        )
        date = dateutils.current_datetime().strftime('%Y-%m-%d')
        assert finance_reporting_service.push_ta_commission_reports(date) == 1


def _create_booking_payload(
    checkin_date, checkout_date, number_of_room=2, number_of_guest_stays=2
):
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=number_of_room,
        number_of_guest_stays=number_of_guest_stays,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.OTA.value,
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
        make_booking_owner_name_and_guest_name_same=False,
    )
    set_ta_details(create_booking_payload, set_commission_details=False)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "200",
            "start_date": checkin_date.isoformat(),
            "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
        },
        {
            "sku_id": "378",
            "pretax_amount": "300",
            "start_date": checkin_date.isoformat(),
            "end_date": checkin_date.isoformat(),
        },
    ]
    if number_of_room > 1:
        create_booking_payload['room_stays'][1]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "300",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            },
            {
                "sku_id": "378",
                "pretax_amount": "100",
                "start_date": checkin_date.isoformat(),
                "end_date": (checkout_date - datetime.timedelta(days=1)).isoformat(),
            },
        ]
    return create_booking_payload
