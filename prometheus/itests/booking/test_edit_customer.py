import json

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import make_booking
from prometheus.tests.mockers import mock_aws_service_client, mock_role_manager
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.value_objects import GSTDetails


def update_single_customer(client, booking_id, customer_id, payload):
    with mock_role_manager():
        url = f"/v1/bookings/{booking_id}/customers/{customer_id}"
        payload = json.dumps(payload)
        response = client.patch(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )

    return response.json


def update_multiple_customers(client, payload, booking_id):
    url = "v1/bookings/" + booking_id + "/customers"
    payload = json.dumps(payload)
    response = client.patch(
        url,
        data=payload,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    return response


def test_edit_customer_details_does_not_create_audit_trail_if_same_details_are_sent_and_booking_version_does_not_change(
    new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room,
    client,
    booking_repo,
    booking_audit_trail_repo,
):
    booking_id = make_booking(
        client,
        {
            "data": json.loads(
                new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room
            )
        },
    )
    booking_aggregate = booking_repo.load(booking_id)
    old_booking_version = booking_aggregate.current_version()
    customer = booking_aggregate.customers[0]

    payload = {
        "data": {
            "guest_preferences": customer.guest_preferences,
            "first_name": customer.first_name,
            "last_name": customer.last_name,
            "is_vip": customer.is_vip,
            "gst_details": customer.gst_details.to_json(),
            "reference_id": customer.reference_id,
            "id_proof": customer.id_proof.to_json(),
        },
        "resource_version": booking_aggregate.current_version(),
    }

    response = update_single_customer(
        client, booking_id, booking_aggregate.customers[0].customer_id, payload
    )

    audit_trail = booking_audit_trail_repo.load_for_booking(booking_id)
    # Only create booking audit trail should be there
    assert len(audit_trail) == 1
    assert audit_trail[0].audit_trail.audit_type == AuditType.BOOKING_CREATED

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.current_version() == old_booking_version


def test_edit_customer_details_stores_domain_events_in_audit_trail(
    new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room,
    client,
    booking_repo,
    booking_audit_trail_repo,
):
    booking_id = make_booking(
        client,
        {
            "data": json.loads(
                new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room
            )
        },
    )
    old_gst_details = (
        json.loads(
            new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room
        )
        .get('booking_owner')
        .get('gst_details')
    )
    booking_aggregate = booking_repo.load(booking_id)
    payload = {
        "data": {
            "guest_preferences": {
                "fnb_preferences": [{"name": "smoking", "values": ["ok Fine"]}],
                "newspaper_preferences": [
                    {"name": "newspaper_name", "values": ["ET", "HT"]}
                ],
            },
            "first_name": "Rohit",
            "last_name": "Jain",
            "is_vip": True,
            "gst_details": {
                "legal_name": "Legal Name New",
                "gstin_num": "GSTIN Num New",
                "is_sez": True,
                "has_lut": True,
                "address": {"city": "Bangalore", "state": "Karnataka", "country": None},
            },
            "reference_id": "XYZ_123",
            "id_proof": {
                "id_kyc_url": "id_kyc_test",
                "id_number": "id_number_test",
                "id_proof_country_code": "id_proof_country_code_test",
                "id_proof_type": "driving_license",
            },
        },
        "resource_version": booking_aggregate.current_version(),
    }

    response = update_single_customer(
        client, booking_id, booking_aggregate.customers[0].customer_id, payload
    )

    audit_trail = booking_audit_trail_repo.load_for_booking(booking_id)
    assert len(audit_trail) == 2
    assert (
        audit_trail[0].audit_trail.audit_type
        == AuditType.BOOKING_OWNER_DETAILS_MODIFIED
    )
    audit_payload = audit_trail[0].audit_trail.audit_payload
    # 4 type of domain event is raised due to charge modification
    assert len(audit_payload.get('domain_events')) == 4
    customer_modified_events = (
        audit_payload.get('domain_events')[0].get('event_detail').get('details')
    )

    # 5 attribute got updated
    assert len(customer_modified_events) == 6

    events_group_by_attribute = {
        event.get('attribute'): event for event in customer_modified_events
    }
    assert events_group_by_attribute.keys() == {
        'guest_preferences',
        'first_name',
        'last_name',
        'is_vip',
        'gst_details',
        'reference_id',
    }

    guest_preference_update = events_group_by_attribute.get('guest_preferences')
    first_name_update = events_group_by_attribute.get('first_name')
    last_name_update = events_group_by_attribute.get('last_name')
    is_vip_update = events_group_by_attribute.get('is_vip')
    gst_details_update = events_group_by_attribute.get('gst_details')
    reference_id_update = events_group_by_attribute.get('reference_id')

    import ast

    expected_value = payload['data']['guest_preferences']
    expected_value.update(
        dict(
            room_preferences=None,
            housekeeping_preferences=None,
            transfers_preferences=None,
            spa_preferences=None,
            others_preferences=None,
        )
    )
    assert ast.literal_eval(guest_preference_update['new_value']) == expected_value
    assert not guest_preference_update.get('old_value')

    assert first_name_update['new_value'] == payload['data']['first_name']
    assert first_name_update['old_value'] == 'first_name_string'
    assert last_name_update['new_value'] == payload['data']['last_name']
    assert last_name_update['old_value'] == 'last_name_string'
    assert is_vip_update['new_value'] == 'True'
    assert is_vip_update['old_value'] == 'False'
    assert gst_details_update['new_value'] == str(
        GSTDetails.from_json(payload['data']['gst_details'])
    )
    assert gst_details_update['old_value'] == str(GSTDetails.from_json(old_gst_details))
    assert reference_id_update['new_value'] == 'XYZ_123'
    assert reference_id_update['old_value'] == 'string'

    # Since ID Proof value sent in json was same as old value, it's domain event shouldn't be raised
    id_proof_update = events_group_by_attribute.get('id_proof')
    assert not id_proof_update


def test_edit_customer_details_with_old_booking_version_does_not_fail_with_version_error(
    new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room,
    client,
    booking_repo,
    booking_audit_trail_repo,
):
    booking_id = make_booking(
        client,
        {
            "data": json.loads(
                new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room
            )
        },
    )
    old_gst_details = (
        json.loads(
            new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room
        )
        .get('booking_owner')
        .get('gst_details')
    )
    booking_aggregate = booking_repo.load(booking_id)
    payload = {
        "data": {
            "guest_preferences": {
                "fnb_preferences": [{"name": "smoking", "values": ["ok Fine"]}],
                "newspaper_preferences": [
                    {"name": "newspaper_name", "values": ["ET", "HT"]}
                ],
            },
            "first_name": "Rohit",
            "last_name": "Jain",
            "is_vip": True,
            "gst_details": {
                "legal_name": "Legal Name New",
                "gstin_num": "GSTIN Num New",
                "is_sez": True,
                "has_lut": True,
                "address": {"city": "Bangalore", "state": "Karnataka", "country": None},
            },
            "reference_id": "XYZ_123",
            "id_proof": {
                "id_kyc_url": "id_kyc_test",
                "id_number": "id_number_test",
                "id_proof_country_code": "id_proof_country_code_test",
                "id_proof_type": "driving_license",
            },
        },
        "resource_version": booking_aggregate.current_version() - 1,
    }

    response = update_single_customer(
        client, booking_id, booking_aggregate.customers[0].customer_id, payload
    )

    audit_trail = booking_audit_trail_repo.load_for_booking(booking_id)
    assert len(audit_trail) == 2
    assert (
        audit_trail[0].audit_trail.audit_type
        == AuditType.BOOKING_OWNER_DETAILS_MODIFIED
    )
    audit_payload = audit_trail[0].audit_trail.audit_payload
    # 4 types of domain event is raised due to charge modification
    assert len(audit_payload.get('domain_events')) == 4
    customer_modified_events = (
        audit_payload.get('domain_events')[0].get('event_detail').get('details')
    )

    # 5 attribute got updated
    assert len(customer_modified_events) == 6

    events_group_by_attribute = {
        event.get('attribute'): event for event in customer_modified_events
    }
    assert events_group_by_attribute.keys() == {
        'guest_preferences',
        'first_name',
        'last_name',
        'is_vip',
        'gst_details',
        'reference_id',
    }

    guest_preference_update = events_group_by_attribute.get('guest_preferences')
    first_name_update = events_group_by_attribute.get('first_name')
    last_name_update = events_group_by_attribute.get('last_name')
    is_vip_update = events_group_by_attribute.get('is_vip')
    gst_details_update = events_group_by_attribute.get('gst_details')
    reference_id_update = events_group_by_attribute.get('reference_id')

    import ast

    expected_value = payload['data']['guest_preferences']
    expected_value.update(
        dict(
            room_preferences=None,
            housekeeping_preferences=None,
            transfers_preferences=None,
            spa_preferences=None,
            others_preferences=None,
        )
    )
    assert ast.literal_eval(guest_preference_update['new_value']) == expected_value
    assert not guest_preference_update.get('old_value')

    assert first_name_update['new_value'] == payload['data']['first_name']
    assert first_name_update['old_value'] == 'first_name_string'
    assert last_name_update['new_value'] == payload['data']['last_name']
    assert last_name_update['old_value'] == 'last_name_string'
    assert is_vip_update['new_value'] == 'True'
    assert is_vip_update['old_value'] == 'False'
    assert gst_details_update['new_value'] == str(
        GSTDetails.from_json(payload['data']['gst_details'])
    )
    assert gst_details_update['old_value'] == str(GSTDetails.from_json(old_gst_details))
    assert reference_id_update['new_value'] == 'XYZ_123'
    assert reference_id_update['old_value'] == 'string'

    # Since ID Proof value sent in json was same as old value, it's domain event shouldn't be raised
    id_proof_update = events_group_by_attribute.get('id_proof')
    assert not id_proof_update


def create_update_customer_payload(customer_details, booking_version):
    payload = []
    for customer in customer_details:
        data = {
            "first_name": customer['first_name'],
            "last_name": customer['last_name'],
            "customer_id": customer['id'],
        }
        if customer.get("gst_details"):
            data["gst_details"] = customer.get("gst_details")
        payload.append(data)
    return {"data": payload, "resource_version": booking_version}


def test_edit_multiple_customer_details_does_not_create_audit_trail_with_and_no_version_change_if_same_details_are_sent(
    new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room,
    client,
    booking_repo,
    booking_audit_trail_repo,
):
    booking_id = make_booking(
        client,
        {
            "data": json.loads(
                new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room
            )
        },
    )
    booking_aggregate = booking_repo.load(booking_id)
    old_booking_version = booking_aggregate.current_version()
    customer1 = booking_aggregate.customers[0]
    customer2 = booking_aggregate.customers[1]

    payload = {
        "data": [
            {
                "guest_preferences": customer1.guest_preferences,
                "first_name": customer1.first_name,
                "last_name": customer1.last_name,
                "is_vip": customer1.is_vip,
                "gst_details": customer1.gst_details.to_json()
                if customer1.gst_details
                else None,
                "reference_id": customer1.reference_id,
                "id_proof": customer1.id_proof.to_json()
                if customer1.id_proof
                else None,
                "customer_id": customer1.customer_id,
            },
            {
                "guest_preferences": customer2.guest_preferences.to_json(),
                "first_name": customer2.first_name,
                "last_name": customer2.last_name,
                "is_vip": customer2.is_vip,
                "gst_details": customer2.gst_details.to_json()
                if customer2.gst_details
                else None,
                "reference_id": customer2.reference_id,
                "id_proof": customer2.id_proof.to_json()
                if customer2.id_proof
                else None,
                "customer_id": customer2.customer_id,
            },
        ],
        "resource_version": booking_aggregate.current_version(),
    }

    with mock_aws_service_client():
        response = update_multiple_customers(client, payload, booking_id)
        assert response.status_code == 200

    audit_trail = booking_audit_trail_repo.load_for_booking(booking_id)
    # Only create booking audit trail should be there
    assert len(audit_trail) == 1, "More audit trail got created than expected"
    assert audit_trail[0].audit_trail.audit_type == AuditType.BOOKING_CREATED

    booking_aggregate = booking_repo.load(booking_id)
    # New booking version is same as previous version, as all data in customer patch was same
    assert booking_aggregate.current_version() == old_booking_version


def test_edit_multiple_customer_details_with_old_version_does_not_give_version_error(
    new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room,
    client,
    booking_repo,
    booking_audit_trail_repo,
):
    booking_id = make_booking(
        client,
        {
            "data": json.loads(
                new_booking_yesterday_payload_with_2_room_stays_2_guest_stays_per_room
            )
        },
    )
    booking_aggregate = booking_repo.load(booking_id)
    old_booking_version = booking_aggregate.current_version()
    customer1 = booking_aggregate.customers[0]
    customer2 = booking_aggregate.customers[1]

    payload = {
        "data": [
            {"first_name": "New Name", "customer_id": customer1.customer_id},
            {"last_name": "New Last Name", "customer_id": customer2.customer_id},
        ],
        "resource_version": booking_aggregate.current_version() - 1,
    }

    with mock_aws_service_client():
        response = update_multiple_customers(client, payload, booking_id)
        assert response.status_code == 200

    booking_aggregate = booking_repo.load(booking_id)
    # New booking version is same as previous version, as all data in customer patch was same
    assert booking_aggregate.current_version() == old_booking_version + 1
