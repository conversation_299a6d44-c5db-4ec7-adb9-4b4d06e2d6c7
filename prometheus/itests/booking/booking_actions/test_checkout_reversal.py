import json
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

import pytest
from treebo_commons.utils import dateutils

from prometheus.application.decorators import session_manager
from prometheus.domain.inventory.factories.room_allotment_factory import (
    RoomAllotmentFactory,
)
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    checkout_booking,
    make_booking,
    undo_checkout_booking,
)
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.billing.test_invoice_modification import (
    marked_invoices_locked,
    modify_invoice_api_call,
    modify_invoice_payload,
)
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
    create_checkout_payload,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_payload,
)
from prometheus.tests.factories.aggregate_factories import RoomAggregateFactory
from prometheus.tests.factories.entity_factories import (
    HousekeepingRecordFactory,
    RoomInventoryFactory,
)
from prometheus.tests.mockers import mock_tax_call, mock_tenant_config_club_inclusion
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.inventory_constants import AllottedFor


@session_manager(commit=True)
def save_room_allotment(room_allotment_repo, allotment):
    room_allotment_repo.save(allotment)


@pytest.mark.skip("Failing tests on billing and payments privilege. Fix this later.")
def test_booking_undo_early_checkout_should_fail_if_room_got_assigned_to_some_other_booking_but_should_succeed_if_there_is_another_room_available(
    active_hotel_aggregate,
    two_day_booking_payload,
    client,
    booking_repo,
    booking_audit_trail_repo,
    room_allotment_repo,
    room_repo,
):
    # use 2 day booking
    booking_id = make_booking(client, {"data": json.loads(two_day_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        invoice_group_id,
        dateutils.current_datetime(),
    )
    action_id = checkout_booking(client, booking_id, checkout_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_OUT

    # Create another allotment to block the inventory after early checkout.
    # This is like checking in another booking
    allotment = RoomAllotmentFactory.create_new_allotment(
        active_hotel_aggregate.hotel.hotel_id,
        15,
        dateutils.current_datetime(),
        dateutils.current_datetime() + timedelta(days=1),
        AllottedFor.STAY,
    )
    save_room_allotment(room_allotment_repo, allotment)

    # This should fail because the room is now unavailable after actual checkout happened to expected
    # checkout
    undo_checkout_booking(client, booking_id, action_id, expected_response_code=400)
    booking_aggregate = booking_repo.load(booking_id)

    # make another room available
    room_aggregate = RoomAggregateFactory(
        room__room_id="16", room__room_type_id="rt01", room__room_number="302"
    )
    room_repo.save(room_aggregate)

    room_inventory = RoomInventoryFactory(room_id="16")
    housekeeping_record = HousekeepingRecordFactory(room_id="16")
    room_allotment_repo.save_room_inventories([room_inventory])
    room_allotment_repo.save_housekeeping_records([housekeeping_record])

    undo_response = undo_checkout_booking(
        client, booking_id, action_id, expected_response_code=200
    )
    audit_trail = booking_audit_trail_repo.load_for_booking(booking_id)
    assert audit_trail[0].audit_trail.audit_type == AuditType.CHECKOUT_REVERSED
    checkout_reversal_domain_events = audit_trail[0].audit_trail.audit_payload[
        "domain_events"
    ]
    assert len(checkout_reversal_domain_events) == 12
    assert checkout_reversal_domain_events[11]["event_type"] == "Checkout Reversed"

    side_effect_messages = [
        alert["message"]
        for alert in undo_response["data"]["reversal_side_effects"]["alerts"]
    ]
    assert "Room Changed from 301 to 302." in side_effect_messages
    booking_aggregate = booking_repo.load(booking_id)
    assert (
        booking_aggregate.room_stays[0].room_allocation.room_id == 16
    ), "An available room should be alloted"
    assert (
        booking_aggregate.room_stays[0].room_allocation_history[0].checkout_date
        == booking_aggregate.room_stays[0].room_allocation.checkin_date
    ), "The available room should be allotted from the end of previous allotment"


def test_addons_getting_linked_to_stay_charge_after_locked_invoice_checkout_reversal(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
    bill_repo,
    credit_note_repo,
):
    from treebo_commons.utils import dateutils

    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "10",
            "start_date": dateutils.current_datetime().isoformat(),
            "end_date": dateutils.current_datetime().isoformat(),
        }
    ]
    from prometheus.itests.booking.test_booking_v2 import make_booking

    booking_id = make_booking(client, {"data": create_booking_payload})
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    action_id = marked_invoices_locked(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        invoice_repo,
    )
    undo_checkout_booking(client, booking_id, action_id, expected_response_code=200)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert (
        len(
            bill_aggregate.get_charge(3).addon_charge_ids
            + bill_aggregate.get_charge(4).addon_charge_ids
        )
        == 1
    )


def test_inclusion_charges_are_taxed_properly_after_locked_invoice_checkout_reversal(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
    bill_repo,
    credit_note_repo,
):
    from treebo_commons.utils import dateutils

    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "10",
            "start_date": dateutils.current_datetime().isoformat(),
            "end_date": dateutils.current_datetime().isoformat(),
        }
    ]
    from prometheus.itests.booking.test_booking_v2 import make_booking

    booking_id = make_booking(client, {"data": create_booking_payload})
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    action_id = marked_invoices_locked(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        invoice_repo,
    )
    with mock_tenant_config_club_inclusion(), mock_tax_call():
        undo_checkout_booking(client, booking_id, action_id, expected_response_code=200)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    assert (
        len(
            bill_aggregate.get_charge(3).addon_charge_ids
            + bill_aggregate.get_charge(4).addon_charge_ids
        )
        == 1
    )
    expected_tax_percent = Decimal("6")
    for charge in bill_aggregate.charges:
        assert charge.tax_amount != Decimal("0")
        for tax_item in charge.tax_details:
            if tax_item.tax_type == 'cgst':
                assert tax_item.tax_amount.amount != Decimal("0")
                assert tax_item.percentage == expected_tax_percent
        for split in charge.charge_splits:
            assert split.tax != Decimal("0")
            for tax_item in split.tax_details:
                if tax_item.tax_type == 'cgst':
                    assert tax_item.tax_amount.amount != Decimal("0")
                    assert tax_item.percentage == expected_tax_percent
