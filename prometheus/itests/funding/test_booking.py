import json

from treebo_commons.utils import dateutils

from prometheus.itests.booking.test_booking_v2 import make_booking
from prometheus.itests.funding.test_booking_funding_details import (
    get_booking_funding_details,
)
from prometheus.itests.funding.test_booking_funding_requests import (
    get_booking_funding_request,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_company_profile_service,
    mock_is_booking_funding_enabled,
    mock_role_manager,
    mock_tenant_config,
)
from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.catalog_constants import SellerType


def create_booking_with_discount(
    client,
    checkin_date,
    checkout_date,
    reference_number="REF-1",
    discount_value=100,
    extra_field=None,
):
    booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number=reference_number,
        discount_value=discount_value,
        extra_field=extra_field,
    )

    with mock_is_booking_funding_enabled(), mock_company_profile_service(), mock_tenant_config(
        [
            {
                "config_name": "rate_config.hotel_uses_posttax_price",
                "config_value": "true",
                "value_type": "boolean",
            },
        ]
    ):
        response = make_booking(
            client,
            {"data": booking_payload},
            expected_status_code=200,
            return_complete_response=True,
        )

    return response


def booking_room_stay_cancellation_request(booking_version, room_stay_id=1):
    return {
        "data": {
            "action_type": "cancel",
            "payload": {
                "cancel": {
                    "cancellation_reason": "h",
                }
            },
        },
        "resource_version": booking_version,
    }


def test_booking_create_booking_with_discounts(client, bill_repo, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    response = create_booking_with_discount(
        client, checkin_date, checkout_date, discount_value=100
    )

    booking_id = response["data"]["booking_id"]
    bill_id = response["data"]["bill_id"]

    bill_aggregate = bill_repo.load(bill_id)
    booking_aggregate = booking_repo.load(booking_id)

    # Assertions for discounts
    assert len(booking_aggregate.booking.discount_details) == 3
    assert len(booking_aggregate.room_stays[0].discounts) == 6

    # Check if a new billed entity is created
    franchiser = next(
        (
            entity
            for entity in bill_aggregate.billed_entities
            if entity.category == BilledEntityCategory.FRANCHISER
        ),
        None,
    )
    assert franchiser is not None

    # Check if dummy customer is added
    assert any(
        customer.company_billed_entity_id == franchiser.billed_entity_id
        for customer in booking_aggregate.customers
    )

    # Booking funding config assertions
    funding_requests = get_booking_funding_request(client, booking_id)
    assert not funding_requests

    # funding details
    funding_details = get_booking_funding_details(client, booking_id)
    assert funding_details and funding_details['actual_funded_amount'] == '0.00'
    assert funding_details['applicable_funding_amount'] == '0.00'
    assert funding_details['funding_amount_breakup']['auto_funding_amount'] == '100.00'
    assert funding_details['funding_amount_breakup']['manual_funding_amount'] == '0.00'


def test_edit_booking_with_different_discount(client, bill_repo, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    response = create_booking_with_discount(
        client, checkin_date, checkout_date, discount_value=100
    )

    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)

    assert len(booking_aggregate.booking.discount_details) == 3
    assert len(booking_aggregate.room_stays[0].discounts) == 6
    with mock_role_manager(), mock_is_booking_funding_enabled():
        replace_booking_payload = create_new_booking_payload(
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=1,
            status=BookingStatus.CONFIRMED,
            reference_number=booking_aggregate.booking.reference_number,
            discount_value=50,
        )

        url = f'v2/bookings/{booking_id}'
        payload = json.dumps(
            {
                'data': replace_booking_payload,
                'resource_version': booking_aggregate.current_version(),
            }
        )
        response = client.put(
            url,
            data=payload,
            content_type='application/json',
            headers={'X-User-Type': 'super-admin'},
        )
        assert response.status_code == 200
        bill_id = response.json["data"]["bill_id"]

        bill_aggregate = bill_repo.load(bill_id)
        booking_aggregate = booking_repo.load(booking_id)

        assert len(booking_aggregate.booking.discount_details) == 3
        assert len(booking_aggregate.room_stays[0].discounts) == 6

        franchiser = next(
            (
                entity
                for entity in bill_aggregate.billed_entities
                if entity.category == BilledEntityCategory.FRANCHISER
            ),
            None,
        )
        assert franchiser is not None

        assert any(
            customer.company_billed_entity_id == franchiser.billed_entity_id
            for customer in booking_aggregate.customers
        )

        funding_requests = get_booking_funding_request(client, booking_id)
        assert not funding_requests

        funding_details = get_booking_funding_details(client, booking_id)
        assert funding_details and funding_details['actual_funded_amount'] == '0.00'
        assert funding_details['applicable_funding_amount'] == '0.00'
        assert (
            funding_details['funding_amount_breakup']['auto_funding_amount'] == '50.00'
        )
        assert (
            funding_details['funding_amount_breakup']['manual_funding_amount'] == '0.00'
        )


def test_edit_booking_with_no_discount(client, bill_repo, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    response = create_booking_with_discount(
        client, checkin_date, checkout_date, discount_value=100
    )

    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)

    assert len(booking_aggregate.booking.discount_details) == 3
    assert len(booking_aggregate.room_stays[0].discounts) == 6
    with mock_role_manager(), mock_is_booking_funding_enabled():
        replace_booking_payload = create_new_booking_payload(
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=1,
            status=BookingStatus.CONFIRMED,
            reference_number=booking_aggregate.booking.reference_number,
        )

        url = f'v2/bookings/{booking_id}'
        payload = json.dumps(
            {
                'data': replace_booking_payload,
                'resource_version': booking_aggregate.current_version(),
            }
        )
        response = client.put(
            url,
            data=payload,
            content_type='application/json',
            headers={'X-User-Type': 'super-admin'},
        )
        assert response.status_code == 200
        bill_id = response.json["data"]["bill_id"]

        bill_aggregate = bill_repo.load(bill_id)
        booking_aggregate = booking_repo.load(booking_id)

        assert len(booking_aggregate.booking.discount_details) == 0
        assert len(booking_aggregate.room_stays[0].discounts) == 0

        franchiser = next(
            (
                entity
                for entity in bill_aggregate.billed_entities
                if entity.category == BilledEntityCategory.FRANCHISER
            ),
            None,
        )
        assert franchiser is not None

        assert any(
            customer.company_billed_entity_id == franchiser.billed_entity_id
            for customer in booking_aggregate.customers
        )

        funding_requests = get_booking_funding_request(client, booking_id)
        assert len(funding_requests) == 0
        funding_details = get_booking_funding_details(client, booking_id)
        assert funding_details and funding_details['actual_funded_amount'] == '0.00'
        assert funding_details['applicable_funding_amount'] == '0.00'
        assert (
            funding_details['funding_amount_breakup']['auto_funding_amount'] == '0.00'
        )
        assert (
            funding_details['funding_amount_breakup']['manual_funding_amount'] == '0.00'
        )


def test_create_booking_with_funding_disabled(client, booking_repo, bill_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)
    booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
    )
    booking_id = make_booking(
        client,
        {"data": booking_payload},
        expected_status_code=200,
    )
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    assert len(booking_aggregate.booking.discount_details) == 0
    assert len(booking_aggregate.room_stays[0].discounts) == 0
    franchiser = next(
        (
            entity
            for entity in bill_aggregate.billed_entities
            if entity.category == BilledEntityCategory.FRANCHISER
        ),
        None,
    )
    assert franchiser is None
    funding_requests = get_booking_funding_request(client, booking_id)
    assert len(funding_requests) == 0
    funding_details = get_booking_funding_details(client, booking_id)
    assert funding_details and funding_details['actual_funded_amount'] == '0.00'
    assert funding_details['applicable_funding_amount'] == '0.00'
    assert funding_details['funding_amount_breakup']['auto_funding_amount'] == '0.00'
    assert funding_details['funding_amount_breakup']['manual_funding_amount'] == '0.00'


def test_reverse_cancel_should_remove_discounts_details(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    response = create_booking_with_discount(
        client,
        checkin_date,
        checkout_date,
        discount_value=100,
        extra_field={"source": "campaign", "custom_flag": True},
    )

    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)

    booking_version = booking_aggregate.booking.version
    room_stay_cancel_payload = booking_room_stay_cancellation_request(booking_version)
    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        url = f"v1/bookings/{booking_id}/actions"
        payload = json.dumps(room_stay_cancel_payload)
        cancel_response = client.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert cancel_response.status_code == 200

    action_id = cancel_response.json["data"]["action_id"]

    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        url = f"v1/bookings/{booking_id}/actions/{action_id}"
        cancel_response = client.delete(
            url, content_type="application/json", headers={"X-User-Type": "super-admin"}
        )
        assert cancel_response.status_code == 200

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.discount_details == []
    for rs in booking_aggregate.room_stays:
        assert rs.discounts == []


def test_booking_create_booking_with_extra_fields_on_discounts(
    client, bill_repo, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    response = create_booking_with_discount(
        client,
        checkin_date,
        checkout_date,
        discount_value=100,
        extra_field={"source": "campaign", "custom_flag": True},
    )

    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.discount_details[0].source == 'campaign'
    assert booking_aggregate.booking.discount_details[0].custom_flag is True
