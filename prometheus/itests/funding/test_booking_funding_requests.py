from contextlib import contextmanager

from treebo_commons.utils import dateutils

from prometheus.itests.booking.test_booking_v2 import make_booking
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_get_maximum_amount_allowed_for_manual_funding,
    mock_is_booking_funding_enabled,
    mock_rate_manager_client,
    mock_role_manager,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.funding_constants import FundingType

COMMON_HEADERS = {"X-User-Type": "super-admin"}


@contextmanager
def common_mocks():
    with mock_catalog_client(
        hotel_in_posttax=True
    ), mock_role_manager(), mock_rate_manager_client():
        yield


def get_booking_funding_request(client, booking_id, expected_status_code=200):
    with common_mocks():
        response = client.get(
            f"/v1/booking-funding?booking_id={booking_id}",
            content_type="application/json",
            headers=COMMON_HEADERS,
        )
        assert response.status_code == expected_status_code
        return response if expected_status_code != 200 else response.json["data"]


def update_booking_funding_request(
    client, booking_id, payload, expected_status_code=200
):
    with common_mocks():
        response = client.patch(
            f"/v1/booking-funding/{booking_id}",
            json=payload,
            content_type="application/json",
            headers=COMMON_HEADERS,
        )
        assert response.status_code == expected_status_code
        return response if expected_status_code != 200 else response.json["data"]


def create_booking_funding_request(
    client, booking_id, payload, expected_status_code=200
):
    with common_mocks():
        response = client.post(
            f"/v1/booking-funding/{booking_id}",
            json=payload,
            content_type="application/json",
            headers=COMMON_HEADERS,
        )
        assert response.status_code == expected_status_code
        return response if expected_status_code != 200 else response.json["data"]


def create_or_update_manual_funding_payload(
    amount, funding_type, reason, funding_id=None
):
    payload = {
        "data": {
            "amount": f"{amount} INR",
            "funding_type": funding_type,
            "reason": reason,
        }
    }
    if funding_id:
        payload["data"]["funding_id"] = funding_id
    return payload


def get_booking_funding(
    client, booking_id, funding_type=None, expected_status_code=200
):
    """
    Helper to test GET /v1/booking-funding API.
    """
    with common_mocks():
        url = f"/v1/booking-funding?booking_id={booking_id}"
        if funding_type:
            url += f"&funding_type={funding_type}"

        response = client.get(
            url,
            content_type="application/json",
            headers=COMMON_HEADERS,
        )

        assert response.status_code == expected_status_code
        return response if expected_status_code != 200 else response.json["data"]


def test_add_manual_funding(client, bill_repo, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number="test-booking",
        discount_value=100,
    )

    with mock_is_booking_funding_enabled():
        response = make_booking(
            client,
            {"data": booking_payload},
            expected_status_code=200,
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]

    # Create manual funding
    payload = create_or_update_manual_funding_payload(
        1000, FundingType.MANUAL_FUNDING.value, "test-case"
    )
    response = create_booking_funding_request(client, booking_id, payload)
    assert response["amount"] == "1000.00"
    assert response["funding_type"] == FundingType.MANUAL_FUNDING.value
    assert response["reason"] == "test-case"

    funding_id = get_booking_funding(client, booking_id)[0].get("funding_id")

    # Update manual funding
    payload = create_or_update_manual_funding_payload(
        3000, "manual_funding", "test-case", funding_id=funding_id
    )
    response = update_booking_funding_request(client, booking_id, payload)
    assert response["amount"] == "3000.00"
    assert response["funding_type"] == FundingType.MANUAL_FUNDING.value
    assert response["reason"] == "test-case"

    # Exceed funding limit
    payload = create_or_update_manual_funding_payload(
        6000, "manual_funding", "test-case", funding_id=funding_id
    )
    with mock_get_maximum_amount_allowed_for_manual_funding():
        update_booking_funding_request(
            client, booking_id, payload, expected_status_code=400
        )
