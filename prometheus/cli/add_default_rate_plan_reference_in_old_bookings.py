import logging
from copy import deepcopy
from datetime import timedelta

import click as click
import requests
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.common.decorators import timed
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.dtos.rate_plan_dtos import (
    RatePlanNonRoomNightInclusion,
    RatePlanPackage,
    RatePlanPolicies,
    RatePlanRestrictions,
)
from prometheus.domain.booking.entities.rate_plan import RatePlan
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from shared_kernel.infrastructure.external_clients.rate_manager_client import (
    RateManagerClient,
)
from ths_common.constants.booking_constants import RatePlanCodes
from ths_common.constants.user_constants import UserType
from ths_common.utils.collectionutils import chunks
from ths_common.value_objects import RoomRatePlan

logger = logging.getLogger(__name__)


@click.command('add_rate_plan_details_in_booking_related_entity')
@click.option(
    '--hotel_ids',
    help="comma separated Hotel IDs for which this command should be run.",
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option('--from_date', help="From Date")
@click.option('--to_date', help="To Date")
@click.option(
    '--booking_ids',
    help="comma separated booking_ids to which Rate Plan details to be Added",
)
@click.option(
    '--booking_statuses',
    help="comma separated booking_statuses to which Rate Plan details to be Added",
)
@inject(
    booking_repository=BookingRepository,
    bill_repository=BillRepository,
    invoice_repository=InvoiceRepository,
)
@with_appcontext
def add_default_rate_plan_reference_in_old_bookings(
    booking_repository,
    bill_repository,
    invoice_repository,
    from_date,
    to_date,
    booking_ids,
    hotel_ids,
    booking_statuses,
    tenant_id,
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    request_context.user_type = UserType.BACKEND_SYSTEM.value
    booking_details = booking_repository.load_bookings_for_default_rate_plan_migration(
        checkout_start_date=from_date,
        checkout_end_date=to_date,
        booking_ids=booking_ids.split(',') if booking_ids else None,
        booking_statuses=booking_statuses.split(',') if booking_statuses else None,
        hotel_ids=hotel_ids.split(',') if hotel_ids else None,
        search_query=BookingSearchQuery(sort_by='-checkin'),
    )
    hotel_grouped_booking_ids = {}
    for booking_id, hotel_id in booking_details:
        if hotel_id in hotel_grouped_booking_ids:
            hotel_grouped_booking_ids[hotel_id].append(booking_id)
        else:
            hotel_grouped_booking_ids[hotel_id] = [booking_id]
    for hotel_id, booking_ids in hotel_grouped_booking_ids.items():
        rate_plan_response_dtos = RateManagerClient().get_rate_plans(hotel_id)
        applicable_rate_plan_info = [
            rate_plan_response_dto
            for rate_plan_response_dto in rate_plan_response_dtos
            if rate_plan_response_dto.short_code == RatePlanCodes.TRB_DEFAULT.value
        ]
        if not applicable_rate_plan_info:
            continue
        else:
            applicable_rate_plan_info = applicable_rate_plan_info[0]
        for bookings in chunks(booking_ids, 100):
            _add_default_rate_plan_reference_in_old_bookings(
                booking_repository,
                bookings,
                applicable_rate_plan_info,
                rate_plan_response_dtos,
                bill_repository,
                invoice_repository,
                tenant_id,
            )


@session_manager(commit=True)
def _add_default_rate_plan_reference_in_old_bookings(
    booking_repository,
    bookings,
    applicable_rate_plan_info,
    all_rate_plans,
    bill_repository,
    invoice_repository,
    tenant_id,
):
    booking_aggregates = booking_repository.load_all_for_update(bookings)
    for booking_aggregate in booking_aggregates:
        try:
            create_booking_rate_plan_and_update_room_stay(
                booking_repository,
                booking_aggregate.booking_id,
                all_rate_plans,
                tenant_id,
            )
            update_charge_item_details(
                booking_repository,
                bill_repository,
                booking_aggregate.booking_id,
                applicable_rate_plan_info,
            )
            update_invoice_charge_item_details(
                booking_repository,
                booking_aggregate.booking_id,
                applicable_rate_plan_info,
                invoice_repository,
            )
        except Exception as e:
            logger.exception(
                "Exception occurred while migrating Rate Plan details for booking_id: %s",
                booking_aggregate.booking_id,
            )


def daterange(start_date, end_date):
    if end_date == start_date:
        yield start_date
    for n in range(int((end_date - start_date).days) + 1):
        yield start_date + timedelta(n)


def get_rate_plan_id(hotel_rate_plans, rate_plan_code):
    for rate_plan in hotel_rate_plans:
        if rate_plan.short_code == rate_plan_code:
            return rate_plan.rate_plan_id


def get_rate_plan_object(
    rate_plan_json, package_mapping, total_rate_plans, tenant_id, is_flexi=False
):
    rate_plan_json = deepcopy(rate_plan_json)
    package_id = rate_plan_json['package_id']
    rate_plan_id = rate_plan_json['rate_plan_id']
    package_data, package_mapping = get_package_data(
        package_id=package_id, package_mapping=package_mapping, tenant_id=tenant_id
    )
    for inclusion in package_data['inclusions']:
        offering = inclusion['offering']
        offering['quantity'] = offering['offered_quantity']
        if not inclusion.get('name'):
            inclusion['name'] = inclusion.get('display_name')

    policies = rate_plan_json['policies']
    rate_plan_json['policies']['child_data'] = policies['child_policy']

    existing_rate_plans_group_by_ref_id = {
        rp.rate_plan_reference_id: rp for rp in total_rate_plans if not rp.deleted
    }
    if not is_flexi and existing_rate_plans_group_by_ref_id.get(rate_plan_id):
        return existing_rate_plans_group_by_ref_id.get(rate_plan_id)
    rate_plan_id_new = max([int(rp.rate_plan_id) for rp in total_rate_plans], default=0)
    rate_plan_id_new += 1
    rate_plan = RatePlan(
        rate_plan_id=rate_plan_id_new,
        rate_plan_reference_id=rate_plan_id,
        rate_plan_code=rate_plan_json['short_code'],
        name=rate_plan_json['name'],
        package=RatePlanPackage.from_json(package_data=package_data),
        policies=RatePlanPolicies.from_json(rate_plan_json['policies']),
        restrictions=RatePlanRestrictions.from_json(rate_plan_json['restrictions'])
        if rate_plan_json.get('restrictions')
        else None,
        non_room_night_inclusions=[
            RatePlanNonRoomNightInclusion.from_json(r)
            for r in rate_plan_json['non_room_night_inclusion_rates']
        ]
        if rate_plan_json.get('non_room_night_inclusion_rates')
        else [],
        is_flexi=rate_plan_json['is_flexi'],
    )
    total_rate_plans.append(rate_plan)
    return rate_plan, package_mapping, total_rate_plans


def get_package_data(package_id, package_mapping, tenant_id):
    if package_mapping and package_mapping.get(package_id):
        return package_mapping[package_id], package_mapping
    url = (
        ServiceRegistryClient.get_rate_manager_service_url()
        + f"/rate-manager/v1/packages/{package_id}"
    )
    url.format(package_id=package_id)
    custom_header = {
        "X-tenant-Id": tenant_id,
        "X-User-Type": UserType.BACKEND_SYSTEM.value,
    }
    response = requests.get(url, headers=custom_header)
    package = response.json()['data']['package']
    if not package_mapping:
        package_mapping = dict()
    package_mapping[package_id] = package
    return package, package_mapping


def get_rate_plan_from_rate_manager(rate_plan_id, rate_plan_id_mapping, tenant_id):
    if rate_plan_id_mapping and rate_plan_id_mapping.get(rate_plan_id):
        return rate_plan_id_mapping[rate_plan_id], rate_plan_id_mapping

    url = (
        ServiceRegistryClient.get_rate_manager_service_url()
        + f"/rate-manager/v1/rate-plans/{rate_plan_id}"
    )
    url = url.format(rate_plan_id=rate_plan_id)
    custom_header = {
        "X-tenant-Id": tenant_id,
        "X-User-Type": UserType.BACKEND_SYSTEM.value,
    }
    response = requests.get(url, headers=custom_header)
    rate_plan = response.json()['data']['rate_plan']
    if not rate_plan_id_mapping:
        rate_plan_id_mapping = dict()
    rate_plan_id_mapping[rate_plan_id] = rate_plan
    return rate_plan, rate_plan_id_mapping


def create_booking_rate_plan_and_update_room_stay(
    booking_repository, booking_id, all_rate_plan, tenant_id
):
    rate_plan_id_mapping = dict()
    package_mapping = dict()
    booking_for_update = booking_repository.load_for_update(booking_id=booking_id)
    room_stays = booking_for_update.room_stays
    if booking_for_update.rate_plans:
        return
    rate_plan_id_set = set()
    booking_rate_plans = list()
    rate_plan_id_dict = dict()
    for room_stay in room_stays:
        rate_plan_code = RatePlanCodes.TRB_DEFAULT.value
        rate_plan_id = get_rate_plan_id(
            hotel_rate_plans=all_rate_plan, rate_plan_code=rate_plan_code
        )
        if not rate_plan_id:
            raise Exception(
                "Rate Plan " + rate_plan_code + " Not created for this hotel"
            )
        new_room_rate_plans = list()
        if rate_plan_id not in rate_plan_id_set:
            rate_plan_id_set.add(rate_plan_id)
            (
                rate_plan_from_rate_manager,
                rate_plan_id_mapping,
            ) = get_rate_plan_from_rate_manager(
                rate_plan_id=rate_plan_id,
                rate_plan_id_mapping=rate_plan_id_mapping,
                tenant_id=tenant_id,
            )
            (
                rate_plan_object,
                package_mapping,
                booking_rate_plans,
            ) = get_rate_plan_object(
                rate_plan_json=rate_plan_from_rate_manager,
                package_mapping=package_mapping,
                total_rate_plans=booking_rate_plans,
                tenant_id=tenant_id,
            )
            rate_plan_id_dict[rate_plan_id] = rate_plan_object.rate_plan_id

        sample_room_rate_plan = RoomRatePlan(stay_date=None, rate_plan_id=None)
        if room_stay.actual_stay_start_date:
            start_date = room_stay.actual_stay_start_date
            end_date = room_stay.actual_stay_end_date
        else:
            start_date = room_stay.checkin_date
            end_date = room_stay.checkout_date
        for single_date in daterange(start_date, end_date):
            new_room_rate_plan = deepcopy(sample_room_rate_plan)
            new_room_rate_plan.rate_plan_id = rate_plan_id_dict[rate_plan_id]
            new_room_rate_plan.stay_date = single_date
            new_room_rate_plans.append(new_room_rate_plan)
        room_stay.room_rate_plans = new_room_rate_plans
        room_stay.dirty = True
    booking_for_update.rate_plans = booking_rate_plans
    booking_repository.update(booking_aggregate=booking_for_update)


def modify_charge_item(charge_item_detail, applicable_rate_plan_info):
    isModified = False
    if not charge_item_detail.get('rate_plan_name'):
        charge_item_detail['rate_plan_name'], isModified = (
            applicable_rate_plan_info.short_code,
            True,
        )
    if not charge_item_detail.get('rate_plan_reference_id'):
        charge_item_detail["rate_plan_reference_id"], isModified = (
            applicable_rate_plan_info.rate_plan_id,
            True,
        )
    if not charge_item_detail.get('rate_plan_code'):
        charge_item_detail["rate_plan_code"], isModified = (
            applicable_rate_plan_info.short_code,
            True,
        )
    return isModified


def update_charge_item_details(
    booking_repository, bill_repository, booking_id, applicable_rate_plan_info
):
    # Updating Charge Information
    # with Rate Plan Details
    booking_aggregates = booking_repository.load_all_with_yield_per([booking_id])
    bill_ids = [aggregate.bill_id for aggregate in booking_aggregates]
    bill_aggregates = bill_repository.load_all_for_update(bill_ids)
    for bill_aggregate in bill_aggregates:
        for charge in bill_aggregate._charges:
            if charge.item.details.keys() >= {
                'rate_plan_name',
                'rate_plan_reference_id',
                'rate_plan_code',
            }:
                isModified = modify_charge_item(
                    charge.item.details, applicable_rate_plan_info
                )
                if isModified:
                    charge.mark_dirty()
        bill_repository.update_charge_model(bill_aggregate)


def update_invoice_charge_item_details(
    booking_repository, booking_id, applicable_rate_plan_info, invoice_repository
):
    # Updating Invoice Charge
    # Rate Plan Details
    booking_aggregates = booking_repository.load_all_with_yield_per([booking_id])
    bill_ids = [aggregate.bill_id for aggregate in booking_aggregates]
    invoices = []
    for bill_id in bill_ids:
        invoices.extend(invoice_repository.load_for_bill_id(bill_id))
    for invoice_aggregate in invoices:
        for invoice_charges in invoice_aggregate.invoice_charges:
            if invoice_charges.charge_item.details.keys() >= {
                'rate_plan_name',
                'rate_plan_reference_id',
                'rate_plan_code',
            }:
                isModified = modify_charge_item(
                    invoice_charges.charge_item.details, applicable_rate_plan_info
                )
                if isModified:
                    invoice_charges.mark_dirty()
        invoice_repository.update_invoice_charge(invoice_aggregate)
