import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories import BookingActionRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository


@click.command()
@click.option(
    '--booking_ids',
    help="comma separated booking_ids to for which we have to attach the charges in room_stay for cancelled/noshow "
    "rooms/bookings.",
    default=None,
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(
    booking_repo=BookingRepository,
    bill_repo=BillRepository,
    booking_action_repository=BookingActionRepository,
)
@session_manager(commit=True)
def attach_charge_ids_to_cancelled_nowshow_room_stays(
    booking_repo, bill_repo, booking_action_repository, booking_ids, tenant_id
):
    request_context.tenant_id = tenant_id
    for booking_id in booking_ids.split(','):
        booking_aggregate = booking_repo.load_for_update(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.bill_id)

        booking_action_aggregates = (
            booking_action_repository.load_all_actions_for_booking(booking_id)
        )
        for bca in booking_action_aggregates:
            if bca.booking_action.action_type.value in ['cancel', 'noshow']:
                bill_side_effect = bca.booking_action.side_effects.bill
                if bill_side_effect.grouped_cancelled_charges:
                    for (
                        room_stay_id_wise_charges
                    ) in bill_side_effect.grouped_cancelled_charges.room_stay_charges:
                        click.echo(
                            f"Processing for Booking: {booking_id}, Room Stay Id: "
                            f"{room_stay_id_wise_charges.room_stay_id}"
                        )
                        for charge_id in room_stay_id_wise_charges.charge_ids:
                            room_stay = booking_aggregate.get_room_stay(
                                room_stay_id_wise_charges.room_stay_id
                            )
                            room_stay.charge_id_map[
                                dateutils.date_to_ymd_str(
                                    bill_aggregate.get_charge(charge_id).applicable_date
                                )
                            ] = charge_id
                            room_stay.charge_ids.append(charge_id)
                            room_stay.mark_dirty()
                        click.echo(
                            f"Processed Booking: {booking_id}, Room Stay Id:  "
                            f"{room_stay_id_wise_charges.room_stay_id}. Added charges: "
                            f"{room_stay_id_wise_charges.charge_ids}"
                        )
        booking_repo.update(booking_aggregate)
