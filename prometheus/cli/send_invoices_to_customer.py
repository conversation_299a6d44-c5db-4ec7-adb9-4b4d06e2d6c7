import logging
from collections import defaultdict
from time import sleep

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.billing.repositories import InvoiceRepository
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.repositories import BookingRepository
from ths_common.constants.billing_constants import InvoiceStatus
from ths_common.constants.booking_constants import BookingChannels
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--booking_ids',
    help="comma separated booking_ids to which invoices are to be sent",
    default=None,
)
@click.option(
    '--start_date',
    help="starting date for which invoices to be sent ",
    default=None,
)
@click.option(
    '--end_date',
    help="end date for which invoices to be sent",
    default=None,
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    '--batch_size',
    help="Batch Size - Number of records That should be processed & persisted at a given point of time. Default - 500",
    default=500,
)
@click.option(
    '--sleep_interval',
    help="Slee interval for letting the job to process, default 10s",
    default=10,
)
@with_appcontext
@inject(
    booking_repo=BookingRepository,
    invoice_repo=InvoiceRepository,
    job_scheduler_service=JobSchedulerService,
)
@with_appcontext
def send_invoices_to_customer(
    booking_repo,
    invoice_repo,
    job_scheduler_service,
    booking_ids,
    start_date,
    end_date,
    tenant_id,
    batch_size,
    sleep_interval,
):
    request_context.tenant_id = tenant_id
    logger.info('Started send_invoices_to_customer.. ')
    if booking_ids and (start_date or end_date):
        raise Exception(
            'Please provide either the booking_ids or [start_date & end_date].'
        )

    if not (start_date and end_date) and not booking_ids:
        raise Exception('Please provide both start_date & end_date.')
    if booking_ids:
        logger.info('Booking IDs were provided. Proceeding with provided bookings.')
        booking_aggregates = booking_repo.load_all(booking_ids.split(','))
    else:
        logger.info('Start & End Date were provided. Proceeding with Start & End Date.')
        booking_aggregates = booking_repo.search(
            BookingSearchQuery(
                checkout_start=dateutils.ymd_str_to_date(start_date),
                checkout_end=dateutils.ymd_str_to_date(end_date),
                limit=10000000,
            )
        )

    filtered_booking_aggregates = [
        booking_aggregate
        for booking_aggregate in booking_aggregates
        if booking_aggregate.booking.source.channel_code
        in [
            BookingChannels.DIRECT.value,
            BookingChannels.OTA.value,
            BookingChannels.ASSISTED_SALES.value,
        ]
    ]

    invoice_aggregates = invoice_repo.load_for_bill_ids_with_yield_per(
        bill_ids=[ba.bill_id for ba in filtered_booking_aggregates],
        status_to_be_filtered=[
            InvoiceStatus.CANCELLED.value,
            InvoiceStatus.PREVIEW.value,
        ],
    )

    invoices_by_booking_map = defaultdict(list)
    for invoice_aggregate in invoice_aggregates:
        invoices_by_booking_map.setdefault(
            invoice_aggregate.invoice.parent_info.get('booking_id')
            + '--'
            + invoice_aggregate.invoice.vendor_id,
            [],
        ).append(invoice_aggregate.invoice.invoice_id)

    count = 0
    for invoices_by_booking_map_keys in chunks(
        list(invoices_by_booking_map.keys()), batch_size
    ):
        _schedule_send_email_job_for_invoices(
            job_scheduler_service, invoices_by_booking_map_keys, invoices_by_booking_map
        )
        count += batch_size
        logger.info(f'Sleeping for {sleep_interval} seconds')
        sleep(sleep_interval)

    logger.info('Completed send_invoices_to_customer')


@session_manager(commit=True)
def _schedule_send_email_job_for_invoices(
    job_scheduler_service, invoices_by_booking_map_keys, invoices_by_booking_map
):
    for key in invoices_by_booking_map_keys:
        invoice_aggregates = invoices_by_booking_map.get(key)
        logger.info(
            f'Scheduling job with booking_id_vendor_id: {key}, '
            f'invoice_ids: {[ia for ia in invoice_aggregates]}'
        )
        job_scheduler_service.schedule_invoice_email_on_checkout(
            hotel_id=key.split('--')[1],
            booking_id=key.split('--')[0],
            invoice_ids=[ia for ia in invoice_aggregates],
        )
