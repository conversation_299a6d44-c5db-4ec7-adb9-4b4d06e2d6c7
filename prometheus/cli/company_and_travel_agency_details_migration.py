import logging

import click as click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.common.decorators import timed
from prometheus.domain.billing.dto.billed_entity_data import BilledEntityData
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.repositories import BookingRepository
from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.booking_constants import BookingStatus
from ths_common.utils.collectionutils import chunks
from ths_common.value_objects import (
    Address,
    CompanyDetails,
    LegalDetails,
    Name,
    PhoneNumber,
)

logger = logging.getLogger(__name__)


@click.command('add_company_and_travel_agent_details_in_booking')
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option('--from_date', help="From Date")
@click.option('--to_date', help="To Date")
@click.option(
    '--booking_ids',
    help="comma separated booking_ids to which company details to be migrated",
)
@inject(booking_repository=BookingRepository, bill_repository=BillRepository)
@with_appcontext
def add_company_and_travel_agent_details_in_booking(
    booking_repository,
    bill_repository,
    from_date,
    to_date,
    booking_ids,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id

    if booking_ids:
        booking_ids = booking_repository.load_all_bookings_with_gst_legal_name(
            checkout_start_date=from_date,
            checkout_end_date=to_date,
            booking_ids=booking_ids.split(',') if booking_ids else None,
            search_query=BookingSearchQuery(sort_by='-checkin'),
        )
    else:
        booking_ids = booking_repository.load_all_bookings_with_gst_legal_name(
            exclude_booking_statuses=[
                BookingStatus.CANCELLED.value,
                BookingStatus.CHECKED_OUT.value,
                BookingStatus.NOSHOW.value,
            ],
            checkout_start_date=from_date,
            checkout_end_date=to_date,
            booking_ids=booking_ids.split(',') if booking_ids else None,
            search_query=BookingSearchQuery(sort_by='-checkin'),
        )

    for bookings in chunks(booking_ids, 100):
        _add_company_and_travel_agent_details_in_booking(
            booking_repository, bill_repository, bookings
        )


@session_manager(commit=True)
@timed
def _add_company_and_travel_agent_details_in_booking(
    booking_repository, bill_repository, bookings
):
    bookings_to_be_updated = []
    bills_to_be_updated = []
    failed_booking_ids = []
    booking_aggregates = booking_repository.load_all_for_update(bookings)
    bill_ids = [booking_aggregate.bill_id for booking_aggregate in booking_aggregates]
    bill_map = {
        bill_aggregate.bill_id: bill_aggregate
        for bill_aggregate in bill_repository.load_all_for_update(bill_ids)
    }
    for booking_aggregate in booking_aggregates:
        bill_aggregate = bill_map.get(booking_aggregate.bill_id)
        booking_owner = booking_aggregate.get_booking_owner()
        try:
            _add_company_data_in_booking(
                booking_aggregate, bill_aggregate, booking_owner
            )
            _add_travel_agent_data_in_booking(booking_aggregate, bill_aggregate)
            bookings_to_be_updated.append(booking_aggregate)
            bills_to_be_updated.append(bill_aggregate)
        except Exception as e:
            failed_booking_ids.append(booking_aggregate.booking_id)
            logger.exception(
                "Exception occurred while migrating company details for booking_id: %s",
                booking_aggregate.booking_id,
            )

    logger.info(
        "Failed bookings {0} while company data migration".format(failed_booking_ids)
    )
    booking_repository.update_all(bookings_to_be_updated)
    bill_repository.update_all(list(bills_to_be_updated))


def _add_company_data_in_booking(booking_aggregate, bill_aggregate, booking_owner):
    external_service_info = (
        booking_aggregate.booking.extra_information.get(
            'unordered_external_service_info', dict()
        )
        if booking_aggregate.booking.extra_information
        else dict()
    )
    company_details = external_service_info.get('company_data') or dict()

    contact_info = company_details.get('contact') or dict()
    booking_aggregate.update_company_details(
        CompanyDetails(
            legal_details=LegalDetails(
                legal_name=booking_owner.gst_details.legal_name,
                email=contact_info.get('email'),
                phone=PhoneNumber(contact_info.get('phone'))
                if contact_info.get('phone')
                else None,
                address=booking_owner.gst_details.address,
                tin=booking_owner.gst_details.gstin_num,
                client_internal_code=company_details.get('iata'),
                external_reference_id=booking_owner.reference_id,
                is_sez=booking_owner.gst_details.is_sez,
                has_lut=booking_owner.gst_details.has_lut,
            )
        )
    )
    if not booking_owner.billed_entity_id:
        booker_billed_entity = bill_aggregate.add_billed_entity(
            BilledEntityData(
                name=booking_owner.name, category=BilledEntityCategory.BOOKER
            )
        )
        booking_owner.update_billed_entity_id(booker_billed_entity.billed_entity_id)

    if booking_owner.company_billed_entity_id:
        booking_aggregate.update_billed_entity_id_in_company_detail(
            booking_owner.company_billed_entity_id
        )
    else:
        company_billed_entity = bill_aggregate.add_billed_entity(
            BilledEntityData(
                name=Name(booking_aggregate.get_company_legal_name()),
                category=BilledEntityCategory.BOOKER_COMPANY,
            )
        )
        booking_aggregate.update_billed_entity_id_in_company_detail(
            company_billed_entity.billed_entity_id
        )
        booking_owner.update_company_billed_entity_id(
            company_billed_entity.billed_entity_id
        )


def _add_travel_agent_data_in_booking(booking_aggregate, bill_aggregate):
    external_service_info = (
        booking_aggregate.booking.extra_information.get(
            'unordered_external_service_info', dict()
        )
        if booking_aggregate.booking.extra_information
        else dict()
    )
    travel_agency_details = external_service_info.get('travel_agency_data', dict())
    if not travel_agency_details:
        return
    contact_info = travel_agency_details.get('contact', dict())
    if contact_info.get('address'):
        address = None
    else:
        address = Address(
            field_1=contact_info['address'].get('street'),
            field_2='NA',
            state=contact_info['address'].get('state'),
            city=contact_info['address'].get('city'),
            country=contact_info['address'].get('country'),
            pincode=contact_info['address'].get('postal_code'),
        )

    booking_aggregate.update_travel_agent_details(
        CompanyDetails(
            legal_details=LegalDetails(
                legal_name=travel_agency_details.get('name'),
                email=contact_info.get('email'),
                phone=PhoneNumber(contact_info.get('phone'))
                if contact_info.get('phone')
                else None,
                address=address,
                tin=None,
                client_internal_code=travel_agency_details.get('iata'),
                external_reference_id=None,
            )
        )
    )
    # adding new billed entity as travel agent
    travel_agent_billed_entity = bill_aggregate.add_billed_entity(
        BilledEntityData(
            name=Name(booking_aggregate.get_travel_agent_legal_name()),
            category=BilledEntityCategory.TRAVEL_AGENT,
        )
    )
    booking_aggregate.update_billed_entity_id_in_travel_agent_detail(
        travel_agent_billed_entity.billed_entity_id
    )
