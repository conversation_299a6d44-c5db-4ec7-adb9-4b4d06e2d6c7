import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.domain.billing.repositories import InvoiceRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from ths_common.constants.booking_constants import BookingStatus


@click.command()
@click.option(
    '--booking_ids',
    help="comma separated booking_ids to which billed entity accounts are to be attached",
    default=None,
)
@click.option(
    '--start_date',
    help="checkout start date of bookings that we need to attach billed entity "
    "accounts to charges in ymd format",
    default=None,
)
@click.option(
    '--end_date',
    help="checkout end date of bookings that we need to attach billed entity "
    "accounts to charges in ymd format",
    default=None,
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(
    booking_repo=BookingRepository,
    invoice_repository=InvoiceRepository,
)
@session_manager(commit=True)
def attach_room_stay_id_on_invoice_charges(
    booking_repo,
    invoice_repository,
    start_date,
    end_date,
    booking_ids,
    tenant_id,
):
    request_context.tenant_id = tenant_id

    if booking_ids:
        booking_ids = booking_repo.load_all_bookings_ids(
            booking_ids=booking_ids.split(',')
        )
    else:
        booking_ids = booking_repo.load_all_bookings_ids(
            start_date=start_date,
            end_date=end_date,
        )
    click.echo(f"Total bookings loaded:  {len(booking_ids)}")
    invoice_id = set()
    for booking_id in booking_ids:
        booking_aggregate = booking_repo.load(booking_id)
        if booking_aggregate.booking.status not in (
            BookingStatus.RESERVED,
            BookingStatus.TEMPORARY,
            BookingStatus.CONFIRMED,
        ):
            room_stay_to_charge_id_map = booking_aggregate.get_room_stay_charge_id_map()
            invoice_aggregates = invoice_repository.load_for_bill_id(
                booking_aggregate.booking.bill_id
            )
            for ia in invoice_aggregates:
                if ia.status.value in ('generated', 'locked'):
                    click.echo(f"{ia.invoice_id}")
                    for ic in ia.invoice_charges:
                        if not ic.charge_item.details.get('room_stay_id'):
                            if room_stay_to_charge_id_map.get(ic.charge_id):
                                click.echo(f"Updating for  {ic.invoice_charge_id}")
                                ia.update_invoice_charge_item_details(
                                    ic.invoice_charge_id,
                                    room_stay_to_charge_id_map.get(ic.charge_id),
                                )
                            elif ic.charge_item.details.get('charged_entity_id'):
                                click.echo(f"Updating for  {ic.invoice_charge_id}")
                                room_stay_id = int(
                                    ic.charge_item.details.get(
                                        'charged_entity_id'
                                    ).split(f'{booking_aggregate.booking_id}-')[1]
                                )
                                ia.update_invoice_charge_item_details(
                                    ic.invoice_charge_id, room_stay_id
                                )
                            elif ic.charge_item.details.get('room_no'):
                                for rs in booking_aggregate.room_stays:
                                    if (
                                        rs.room_number
                                        == ic.charge_item.details['room_no']
                                    ):
                                        click.echo(
                                            f"Updating for  {ic.invoice_charge_id}"
                                        )
                                        ia.update_invoice_charge_item_details(
                                            ic.invoice_charge_id, rs.room_stay_id
                                        )
                            else:
                                click.echo(
                                    f"Not able to find for {ic.invoice_charge_id}, {ia.invoice_id}"
                                )
                                invoice_id.add(ia.invoice_id)
            invoice_repository.update_all(invoice_aggregates)
    click.echo(f"failed for {invoice_id}")
