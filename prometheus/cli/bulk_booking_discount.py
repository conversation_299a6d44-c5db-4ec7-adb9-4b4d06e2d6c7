import csv
import logging
from decimal import Decimal

import click
from flask.cli import with_appcontext
from treebo_commons.money.money import Money
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.application.helpers.event_payload_generator import EventPayloadGenerator
from prometheus.application.services.integration_event_application_service import (
    write_event,
)
from prometheus.common.decorators import consumer_middleware, timed
from prometheus.domain.billing.dto.edit_charge_data import EditChargeData
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.services.charge_edit_service import ChargeEditService
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.constants.user_constants import UserType
from ths_common.utils.collectionutils import chunks
from ths_common.value_objects import NotAssigned, PriceData, UserData

logger = logging.getLogger(__name__)


@click.command()
@consumer_middleware
@with_appcontext
@timed
@session_manager(commit=True)
@inject(
    charge_edit_service=ChargeEditService,
    booking_repo=BookingRepository,
    bill_repo=BillRepository,
    hotel_repo=HotelRepository,
)
def bulk_booking_discount(charge_edit_service, booking_repo, bill_repo, hotel_repo):
    all_reference_numbers = []

    with open('booking_id_discount.csv', 'r') as csvfile:
        file_data = csv.reader(csvfile, delimiter=',')
        for row_data in file_data:
            # set parameters
            all_reference_numbers.append(row_data[0])

    discount_report_rows = [
        (
            "WRID",
            "Booking ID",
            "Checkin Date",
            "Checkout Date",
            "Channel",
            "SubChannel",
            "Hotel Name",
            "Old Pretax",
            "New Pretax",
            "Old Posttax",
            "New Posttax",
        )
    ]

    for reference_numbers in chunks(all_reference_numbers, 5):
        booking_aggregates = booking_repo.load_all(reference_numbers=reference_numbers)
        bill_ids = [
            booking_aggregate.booking.bill_id
            for booking_aggregate in booking_aggregates
        ]
        hotel_ids = [
            booking_aggregate.hotel_id for booking_aggregate in booking_aggregates
        ]
        hotel_aggregates = hotel_repo.load_all(hotel_ids)
        hotel_name_map = {
            hotel_aggregate.hotel_id: hotel_aggregate.hotel.name
            for hotel_aggregate in hotel_aggregates
        }

        booking_id_gstins = dict()
        for booking_aggregate in booking_aggregates:
            assert (
                booking_aggregate.is_reserved()
            ), "This command supports discounting only reserved booking"
            booking_id_gstins[booking_aggregate.booking.bill_id] = booking_aggregate

        discount_to_apply = Decimal("5")
        for bill_id in bill_ids:
            bill_aggregate = bill_repo.load_for_update(bill_id)
            old_posttax, old_pretax = (
                bill_aggregate.total_posttax_amount(),
                bill_aggregate.total_pretax_amount(),
            )
            booking_aggregate = booking_id_gstins.get(bill_id)
            edit_charge_dtos = []
            for charge in bill_aggregate.active_charges:
                posttax_amount = charge.posttax_amount
                discounted_posttax = Money(
                    posttax_amount - discount_to_apply / 100 * posttax_amount.amount
                )
                price_data = PriceData(
                    pretax_amount=NotAssigned,
                    posttax_amount=discounted_posttax,
                    applicable_date=NotAssigned,
                    bill_to_type=NotAssigned,
                    type=NotAssigned,
                    charge_id=charge.charge_id,
                    charge_to=NotAssigned,
                )
                edit_charge_dtos.append(
                    EditChargeData(charge_id=charge.charge_id, price_data=price_data)
                )

            edit_charge_dtos = charge_edit_service.update_tax_amounts_of_charges(
                bill_aggregate,
                edit_charge_dtos,
                gst_details=booking_aggregate.booking_owner_gst_details(),
            )

            user_data = UserData(user_type=UserType.BACKEND_SYSTEM.value)
            charge_edit_service.edit_charges(
                bill_aggregate, edit_charge_dtos, None, user_data, None
            )
            bill_repo.update(bill_aggregate)
            new_posttax, new_pretax = (
                bill_aggregate.total_posttax_amount(),
                bill_aggregate.total_pretax_amount(),
            )

            discount_report_rows.append(
                (
                    booking_aggregate.booking.reference_number,
                    booking_aggregate.booking_id,
                    dateutils.to_date(booking_aggregate.booking.checkin_date),
                    dateutils.to_date(booking_aggregate.booking.checkout_date),
                    booking_aggregate.booking.source.channel_code,
                    booking_aggregate.booking.source.subchannel_code,
                    hotel_name_map.get(
                        booking_aggregate.hotel_id, booking_aggregate.hotel_id
                    ),
                    old_pretax.amount,
                    new_pretax.amount,
                    old_posttax.amount,
                    new_posttax.amount,
                )
            )

            event_dto = EventPayloadGenerator.generate_event_dto(
                event_type=IntegrationEventType.BILL_UPDATED,
                bill_aggregate=bill_aggregate,
            )
            write_event(event_dto, ignore_context=True)

    with open('discount_report.csv', 'w') as discount_report_file:
        csv_writer = csv.writer(discount_report_file, delimiter=',')
        for row in discount_report_rows:
            csv_writer.writerow(row)
