from copy import deepcopy
from datetime import timedelta

import click as click
import requests
from flask.cli import with_appcontext
from sqlalchemy import text
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus import crs_context
from prometheus.application.decorators import session_manager
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.dtos.rate_plan_dtos import (
    RatePlanNonRoomNightInclusion,
    RatePlanPackage,
    RatePlanPolicies,
    RatePlanRestrictions,
)
from prometheus.domain.booking.entities.rate_plan import RatePlan
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from ths_common.value_objects import RoomRatePlan


def get_domain_endpoint(domain):
    if domain == 'rate_manager':
        return ServiceRegistryClient.get_rate_manager_service_url()
    elif domain == 'catalog':
        return ServiceRegistryClient.get_catalog_service_url()


def get_base_url(component):
    return get_domain_endpoint(domain=component)


def daterange(start_date, end_date):
    if end_date == start_date:
        yield start_date
    for n in range(int((end_date - start_date).days)):
        yield start_date + timedelta(n)


def load_all_rate_plan_for_property(component, property_id, tenant_id):
    url = (
        get_base_url(component)
        + f"/rate-manager/v1/rate-plans?property_id={property_id}"
    )
    url.format(property_id=property_id)
    custom_header = {"X-tenant-Id": tenant_id}
    api_response = requests.get(url, headers=custom_header)
    return api_response.json()['data']['rate_plans']


def get_rate_plan_id(hotel_rate_plans, rate_plan_code):
    for rate_plan in hotel_rate_plans:
        if rate_plan['short_code'] == rate_plan_code:
            return rate_plan['rate_plan_id']


def get_rate_plan_object(
    rate_plan_json,
    component,
    package_mapping,
    total_rate_plans,
    tenant_id,
    is_flexi=False,
):
    package_data = rate_plan_json['package_id']
    package_data, package_mapping = get_package_data(
        package_id=package_data,
        component=component,
        package_mapping=package_mapping,
        tenant_id=tenant_id,
    )
    for inclusion in package_data['inclusions']:
        offering = inclusion['offering']
        offering['quantity'] = offering['offered_quantity']
        if not inclusion.get('name'):
            inclusion['name'] = inclusion.get('display_name')

    if rate_plan_json['policies']:
        policies = rate_plan_json['policies']
        rate_plan_json['policies']['child_data'] = policies['child_policy']
    existing_rate_plans_group_by_ref_id = {
        rp.rate_plan_reference_id: rp for rp in total_rate_plans if not rp.deleted
    }
    if not is_flexi and existing_rate_plans_group_by_ref_id.get(
        rate_plan_json['rate_plan_id']
    ):
        return existing_rate_plans_group_by_ref_id.get(rate_plan_json['rate_plan_id'])
    rate_plan_id = max([int(rp.rate_plan_id) for rp in total_rate_plans], default=0)
    rate_plan_id += 1
    rate_plan = RatePlan(
        rate_plan_id=rate_plan_id,
        rate_plan_reference_id=rate_plan_json['rate_plan_id'],
        rate_plan_code=rate_plan_json['short_code'],
        name=rate_plan_json['name'],
        package=RatePlanPackage.from_json(package_data=package_data),
        policies=RatePlanPolicies.from_json(rate_plan_json['policies']),
        restrictions=RatePlanRestrictions.from_json(rate_plan_json['restrictions'])
        if rate_plan_json['restrictions']
        else None,
        non_room_night_inclusions=[
            RatePlanNonRoomNightInclusion.from_json(r)
            for r in rate_plan_json['non_room_night_inclusion_rates']
        ]
        if rate_plan_json['non_room_night_inclusion_rates']
        else [],
        is_flexi=rate_plan_json['is_flexi'],
    )
    total_rate_plans.append(rate_plan)
    return rate_plan, package_mapping, total_rate_plans


def get_package_data(package_id, component, package_mapping, tenant_id):
    if package_mapping:
        if package_mapping.get(package_id):
            return package_mapping[package_id], package_mapping
    url = get_base_url(component=component) + f"/rate-manager/v1/packages/{package_id}"
    url.format(package_id=package_id)
    custom_header = {"X-tenant-Id": tenant_id}
    response = requests.get(url, headers=custom_header)
    package = response.json()['data']['package']
    if not package_mapping:
        package_mapping = dict()
    package_mapping[package_id] = package
    return response.json()['data']['package'], package_mapping


def get_rate_plan_from_rate_manager(
    rate_plan_id, component, rate_plan_id_mapping, tenant_id
):
    if rate_plan_id_mapping:
        if rate_plan_id_mapping.get(rate_plan_id):
            return rate_plan_id_mapping[rate_plan_id], rate_plan_id_mapping

    url = get_base_url(component) + f"/rate-manager/v1/rate-plans/{rate_plan_id}"
    url = url.format(rate_plan_id=rate_plan_id)
    custom_header = {"X-tenant-Id": tenant_id}
    response = requests.get(url, headers=custom_header)
    rate_plan = response.json()['data']['rate_plan']
    if not rate_plan_id_mapping:
        rate_plan_id_mapping = dict()
    rate_plan_id_mapping[rate_plan_id] = rate_plan
    return rate_plan, rate_plan_id_mapping


@click.command('create_rate_plan_for_existing_booking')
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    '--hotel_id',
    help="Hotel ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    '--from_date', help="From Date", default=TenantClient.get_default_tenant()
)
@click.option('--to_date', help="To Date", default=TenantClient.get_default_tenant())
@click.option('--limit', help="limit")
@inject(booking_repository=BookingRepository, hotel_repository=HotelRepository)
@with_appcontext
@session_manager(commit=True)
def create_rate_plan_for_existing_booking(
    booking_repository,
    hotel_repository,
    tenant_id=TenantClient.get_default_tenant(),
    hotel_id=None,
    from_date=None,
    to_date=None,
    limit=None,
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    hotel_aggregate = hotel_repository.load(hotel_id)
    hotel_context = crs_context.set_hotel_context(hotel_aggregate)
    all_rate_plan = load_all_rate_plan_for_property(
        'rate_manager', hotel_id, tenant_id=tenant_id
    )
    if not from_date:
        raise Exception("Send Data Range in args : from_date , to_date")
    booking_search_query = BookingSearchQuery(hotel_id=hotel_id, limit=limit)
    booking_ids = booking_repository.load_all_bookings(
        search_query=booking_search_query,
        checkout_end_date=to_date,
        checkout_start_date=from_date,
    )
    rate_plan_id_mapping = dict()
    package_mapping = dict()
    for booking_id in booking_ids:
        booking_for_update = booking_repository.load_for_update(booking_id=booking_id)
        if booking_for_update.rate_plans:
            continue
        room_stays = booking_for_update.room_stays
        rate_plan_id_set = set()
        booking_rate_plans = list()
        rate_plan_id_dict = dict()
        custom_query = text('select * from room_stay where booking_id=:booking_id')
        old_room_stays = booking_repository.load_old_room_stay_model(
            booking_id=booking_id, custom_query=custom_query
        )
        default_rate_plan_code = None
        for room_stay in old_room_stays:
            if (
                not default_rate_plan_code
                and room_stay.rate_plan_code
                and room_stay.rate_plan_code != 'null'
            ):
                default_rate_plan_code = room_stay.rate_plan_code
                break
        for room_stay, old_room_stay in zip(room_stays, old_room_stays):
            rate_plan_code = (
                None
                if old_room_stay.rate_plan_code == 'null'
                else old_room_stay.rate_plan_code
            )
            rate_plan_code = (
                rate_plan_code if rate_plan_code else default_rate_plan_code
            )
            if not rate_plan_code:
                rate_plan_code = 'ACO'
            rate_plan_id = get_rate_plan_id(
                hotel_rate_plans=all_rate_plan, rate_plan_code=rate_plan_code
            )
            if not rate_plan_id:
                raise Exception(
                    "Rate Plan " + rate_plan_code + " Not created for this hotel"
                )
            new_room_rate_plans = list()
            if rate_plan_id not in rate_plan_id_set:
                rate_plan_id_set.add(rate_plan_id)
                (
                    rate_plan_from_rate_manager,
                    rate_plan_id_mapping,
                ) = get_rate_plan_from_rate_manager(
                    rate_plan_id=rate_plan_id,
                    component='rate_manager',
                    rate_plan_id_mapping=rate_plan_id_mapping,
                    tenant_id=tenant_id,
                )
                (
                    rate_plan_object,
                    package_mapping,
                    booking_rate_plans,
                ) = get_rate_plan_object(
                    rate_plan_json=rate_plan_from_rate_manager,
                    component='rate_manager',
                    package_mapping=package_mapping,
                    total_rate_plans=booking_rate_plans,
                    tenant_id=tenant_id,
                )
                rate_plan_id_dict[rate_plan_id] = rate_plan_object.rate_plan_id
            sample_room_rate_plan = RoomRatePlan(stay_date=None, rate_plan_id=None)
            if room_stay.actual_stay_start_date:
                start_date = room_stay.actual_stay_start_date
                end_date = room_stay.actual_stay_end_date
            else:
                start_date = room_stay.checkin_date
                end_date = room_stay.checkout_date
            for single_date in daterange(start_date, end_date):
                new_room_rate_plan = deepcopy(sample_room_rate_plan)
                new_room_rate_plan.rate_plan_id = rate_plan_id_dict[rate_plan_id]
                new_room_rate_plan.stay_date = single_date
                new_room_rate_plans.append(new_room_rate_plan)
            room_stay.room_rate_plans = new_room_rate_plans
            room_stay.dirty = True
        booking_for_update.rate_plans = booking_rate_plans
        result = booking_repository.update(booking_aggregate=booking_for_update)
