import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus.application.booking import ERegCardUrlGenerationJobHandler
from prometheus.application.decorators import session_manager
from prometheus.domain.booking.repositories.booking_repository import BookingRepository

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@session_manager(commit=True)
@inject(
    eregcard_url_generation_job_handler=ERegCardUrlGenerationJobHandler,
    booking_repo=BookingRepository,
)
def populate_regcard_url(
    eregcard_url_generation_job_handler: ERegCardUrlGenerationJobHandler,
    booking_repo,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id

    bookings = booking_repo.get_all_future_bookings()

    print(str(bookings))

    for booking_id, hotel_id in bookings:
        eregcard_url_generation_job_handler.execute_eregcard_url_generation(
            booking_id, hotel_id
        )
