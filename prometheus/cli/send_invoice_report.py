import logging

import click
from flask.cli import with_appcontext
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.reporting.reporting_service import ReportingApplicationService

logger = logging.getLogger(__name__)


@click.command()
@click.option('--start_date', help="start_date value in isoformat")
@click.option('--end_date', help="end_date value in isoformat")
@click.option('--email', help="email to send the report")
@click.option('--hotel_id', help="hotel to filter the bookings")
@click.option('--report_type', help="report type in string")
@click.option('--corporate_id', help="hotel to filter the bookings")
@with_appcontext
@session_manager(commit=True)
@inject(reporting_app_service=ReportingApplicationService)
def generate_invoice_report(
    reporting_app_service,
    start_date,
    end_date,
    email,
    corporate_id,
    hotel_id=None,
    report_type="aggregated_report",
):
    if report_type == "granular_report":
        if corporate_id:
            start_date = dateutils.ymd_str_to_date(start_date)
            end_date = dateutils.ymd_str_to_date(end_date)
            reporting_app_service.generate_granular_corporate_report_between_date_range_and_trigger_email(
                email=email,
                start_date=start_date,
                end_date=end_date,
                corporate_id=corporate_id,
            )
    elif report_type == "aggregated_report":
        if hotel_id is None:
            start_date = dateutils.ymd_str_to_date(start_date)
            end_date = dateutils.ymd_str_to_date(end_date)
            reporting_app_service.schedule_invoice_report_generation(
                start_date=start_date, end_date=end_date, email=email
            )
        else:
            reporting_app_service.generate_between_date_range_and_trigger_invoice_report_email(
                email=email,
                start_date=start_date,
                end_date=end_date,
                hotel_ids=[hotel_id],
                report_type=report_type,
                corporate_id=None,
            )
    return None
