import csv
import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus import crs_context
from prometheus.application.decorators import session_manager
from prometheus.application.helpers.event_payload_generator import EventPayloadGenerator
from prometheus.application.services.integration_event_application_service import (
    write_event,
)
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.services.charge_edit_service import ChargeEditService
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    '--change_posttax_amount',
    help="tax to be calculated on posttax amount or pretax amount",
    default=True,
)
@with_appcontext
@session_manager(commit=True)
@inject(
    charge_edit_service=ChargeEditService,
    booking_repo=BookingRepository,
    bill_repo=BillRepository,
    hotel_repo=HotelRepository,
    hotel_config_repository=HotelConfigRepository,
)
def recompute_tax(
    charge_edit_service,
    booking_repo,
    bill_repo,
    hotel_repo,
    hotel_config_repository,
    tenant_id,
    change_posttax_amount,
):
    request_context.tenant_id = tenant_id
    all_booking_ids = []

    with open('booking_id.csv', 'r') as csvfile:
        file_data = csv.reader(csvfile, delimiter=',')
        for row_data in file_data:
            # set parameters
            all_booking_ids.append(row_data[0])

    tax_recompute_report = [
        (
            "WRID",
            "Booking ID",
            "Checkin Date",
            "Checkout Date",
            "Channel",
            "SubChannel",
            "Hotel Name",
            "Old Pretax",
            "New Pretax",
            "Old Posttax",
            "New Posttax",
            "Net Amount Paid",
        )
    ]

    for booking_ids in chunks(all_booking_ids, 50):
        booking_aggregates = booking_repo.load_all(booking_ids)
        bill_ids = [
            booking_aggregate.booking.bill_id
            for booking_aggregate in booking_aggregates
        ]

        hotel_ids = [
            booking_aggregate.hotel_id for booking_aggregate in booking_aggregates
        ]
        hotel_aggregates = hotel_repo.load_all(hotel_ids)
        hotel_map = {
            hotel_aggregate.hotel_id: hotel_aggregate
            for hotel_aggregate in hotel_aggregates
        }
        hotel_config_aggregates = hotel_config_repository.load_all(hotel_ids)
        hotel_config_map = {
            hotel_config_aggregate.hotel_config.hotel_id: hotel_config_aggregate
            for hotel_config_aggregate in hotel_config_aggregates
        }

        bill_to_booking_map = dict()
        for booking_aggregate in booking_aggregates:
            bill_to_booking_map[booking_aggregate.booking.bill_id] = booking_aggregate

        for bill_id in bill_ids:
            bill_aggregate = bill_repo.load_for_update(bill_id)
            old_posttax, old_pretax = (
                bill_aggregate.total_posttax_amount(),
                bill_aggregate.total_pretax_amount(),
            )
            booking_aggregate = bill_to_booking_map.get(bill_id)
            set_hotel_context(
                hotel_map.get(booking_aggregate.hotel_id),
                hotel_config_map.get(booking_aggregate.hotel_id),
            )
            change_posttax_amount = True if change_posttax_amount else False
            charge_edit_service.recompute_tax_on_all_charges(
                bill_aggregate,
                gst_details=booking_aggregate.booking_owner_gst_details(),
                change_posttax=change_posttax_amount,
                seller_model=booking_aggregate.booking.seller_model,
            )
            bill_repo.update(bill_aggregate)
            new_posttax, new_pretax = (
                bill_aggregate.total_posttax_amount(),
                bill_aggregate.total_pretax_amount(),
            )

            amount_paid = bill_aggregate.net_paid_amount.amount

            tax_recompute_report.append(
                (
                    booking_aggregate.booking.reference_number,
                    booking_aggregate.booking_id,
                    dateutils.to_date(booking_aggregate.booking.checkin_date),
                    dateutils.to_date(booking_aggregate.booking.checkout_date),
                    booking_aggregate.booking.source.channel_code,
                    booking_aggregate.booking.source.subchannel_code,
                    hotel_map.get(
                        booking_aggregate.hotel_id, booking_aggregate.hotel_id
                    ).hotel.name,
                    old_pretax.amount,
                    new_pretax.amount,
                    old_posttax.amount,
                    new_posttax.amount,
                    amount_paid,
                )
            )

            event_dto = EventPayloadGenerator.generate_event_dto(
                event_type=IntegrationEventType.BILL_UPDATED,
                bill_aggregate=bill_aggregate,
            )
            write_event(event_dto, ignore_context=True)

    with open('tax_recompute_report.csv', 'w', newline='') as tax_recompute_report_file:
        csv_writer = csv.writer(tax_recompute_report_file, delimiter=',')
        for row in tax_recompute_report:
            csv_writer.writerow(row)


def set_hotel_context(hotel_aggregate, hotel_config_aggregate):
    hotel_context = crs_context.set_hotel_context(
        hotel_aggregate, hotel_config_aggregate
    )
    return hotel_aggregate, hotel_config_aggregate, hotel_context
