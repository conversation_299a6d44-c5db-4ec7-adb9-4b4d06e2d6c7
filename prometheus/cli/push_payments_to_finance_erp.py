import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.reporting.finance_erp_reporting.external_clients.finance_service_client import (
    FinanceServiceClient,
)
from prometheus.reporting.finance_erp_reporting.finance_reports_generator import (
    FinanceReportsGenerator,
)
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--bill_ids',
    help="List of bill ids for which data needs to be pushed",
    default=None,
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@session_manager(commit=False)
@with_appcontext
@inject(finance_service_client=FinanceServiceClient)
def push_payments_to_finance_erp(
    finance_service_client,
    bill_ids,
    tenant_id,
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    try:
        bill_ids = bill_ids.split(',')
        payment_data_report_aggregates = []
        for chunk in chunks(bill_ids, 100):
            aggregates = FinanceReportsGenerator().generate_payment_reports(chunk)
            payment_data_report_aggregates.extend(aggregates)
        finance_service_client.push_payments_data(payment_data_report_aggregates)
    except Exception as e:
        logger.info(
            "Failed to push payments data to erp for bill ids: %s. error: %s",
            bill_ids,
            str(e),
        )
