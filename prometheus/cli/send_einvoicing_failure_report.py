import logging

import click
from flask.cli import with_appcontext
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.reporting.reporting_service import ReportingApplicationService

logger = logging.getLogger(__name__)


@click.command()
@click.option('--start_date', help="start_date value in isoformat")
@click.option('--end_date', help="end_date value in isoformat")
@click.option('--hotel_ids', help="hotel to filter the bookings")
@click.option('--invoice_ids', help="invoice ids to filter the bookings")
@click.option('--booking_ids', help="booking ids to filter the bookings")
@with_appcontext
@session_manager(commit=True)
@inject(reporting_app_service=ReportingApplicationService)
def submit_to_irp_and_report(
    reporting_app_service,
    start_date,
    end_date=None,
    hotel_ids=None,
    bill_ids=None,
    invoice_ids=None,
    credit_note_ids=None,
):
    if end_date is None:
        end_date = dateutils.current_date()

    if (invoice_ids or credit_note_ids) and (bill_ids or hotel_ids):
        raise RuntimeError(
            "Invoce ids or credit note ids cannot be present when bill_ids or hotel ids are given"
        )

    if bill_ids and hotel_ids:
        raise RuntimeError("Both bill ids and hotel ids cannot be given")

    reporting_app_service.schedule_irp_submission_and_report_generation(
        invoice_ids, credit_note_ids, hotel_ids=hotel_ids, bill_ids=bill_ids
    )
