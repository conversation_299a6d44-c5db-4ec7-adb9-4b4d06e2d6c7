import csv
import logging

import click
from flask.cli import with_appcontext

from object_registry import inject, locate_instance
from prometheus import crs_context
from prometheus.application.decorators import session_manager
from prometheus.common.decorators import consumer_middleware, timed
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository, RoomTypeRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from prometheus.infrastructure.external_clients.notification_service_client import (
    NotificationEmailIds,
    NotificationServiceClient,
)
from prometheus.infrastructure.external_clients.template_service_client import (
    TemplateFormat,
    TemplateService,
)
from ths_common.constants.booking_constants import AgeGroup, BookingStatus
from ths_common.utils.collectionutils import chunks
from ths_common.utils.common_utils import is_valid_email
from ths_common.utils.dateutils import format_date_human_readable

logger = logging.getLogger(__name__)


@click.command()
@consumer_middleware
@with_appcontext
@timed
@session_manager(commit=True)
@inject(
    booking_repo=BookingRepository,
    bill_repo=BillRepository,
    hotel_repo=HotelRepository,
    room_type_map_repo=RoomTypeRepository,
)
def email_booking_voucher(booking_repo, bill_repo, hotel_repo, room_type_map_repo):
    all_reference_numbers = []
    with open('email_voucher_booking_id.csv', 'r') as csvfile:
        file_data = csv.reader(csvfile, delimiter=',')
        for row_data in file_data:
            # set parameters
            all_reference_numbers.append(row_data[0])

    logger.info("Booking ids to send email: %s", all_reference_numbers)

    for reference_numbers in chunks(all_reference_numbers, 50):
        bill_ids = list()
        hotel_ids = set()
        hotel_hashmap = dict()
        bill_hashmap = dict()
        bill_to_booking_hashmap = dict()

        booking_aggregates = []
        if reference_numbers:
            booking_aggregates = booking_repo.load_all(
                reference_numbers=reference_numbers
            )
            for booking_aggregate in booking_aggregates:
                bill_to_booking_hashmap[
                    booking_aggregate.booking.bill_id
                ] = booking_aggregate
                bill_ids.append(booking_aggregate.booking.bill_id)
                hotel_ids.add(booking_aggregate.booking.hotel_id)

        if hotel_ids:
            hotel_aggregates = hotel_repo.load_all(list(hotel_ids))
            for hotel_aggregate in hotel_aggregates:
                hotel_hashmap[hotel_aggregate.hotel_id] = hotel_aggregate

        if bill_ids:
            bill_aggregates = bill_repo.load_all_with_yield_per(bill_ids)
            for bill_aggregate in bill_aggregates:
                bill_hashmap[bill_aggregate.bill.bill_id] = bill_aggregate

        room_type_map = room_type_map_repo.load_type_map()

        for booking_aggregate in booking_aggregates:
            bill_aggregate = bill_hashmap.get(booking_aggregate.booking.bill_id)
            hotel_aggregate = hotel_hashmap.get(booking_aggregate.booking.hotel_id)
            bill_to_booking_aggregate = bill_to_booking_hashmap.get(
                booking_aggregate.booking.bill_id
            )
            _email_booking_voucher(
                bill_to_booking_aggregate,
                hotel_aggregate,
                bill_aggregate,
                room_type_map,
            )


def set_hotel_context(hotel_id):
    hotel_aggregate = locate_instance(HotelRepository).load(hotel_id)
    hotel_config_aggregate = locate_instance(HotelConfigRepository).load(hotel_id)
    hotel_context = crs_context.set_hotel_context(
        hotel_aggregate, hotel_config_aggregate
    )
    return hotel_aggregate, hotel_config_aggregate, hotel_context


def _email_booking_voucher(
    booking_aggregate,
    hotel_aggregate,
    bill_aggregate,
    room_type_map,
    ref_number_booking_amount_change=None,
    namespace=None,
):
    if not namespace:
        namespace = 'ths_booking_confirmation'

    def booking_room_stays(booking_aggregate, room_type_map):
        room_stays = []
        for rs in booking_aggregate.room_stays:
            room_stay = {
                "room_type_name": room_type_map.get(rs.room_type_id).room_type.type,
                "guest_stays": guest_stays(rs, booking_aggregate),
                "addons": [],
            }
            room_stays.append(room_stay)
        return room_stays

    def guest_stays(room_stays, booking_aggregate):
        guest_stays = []
        for gs in room_stays.guest_stays:
            guest_detail = [
                customer
                for customer in booking_aggregate.customers
                if customer.customer_id == gs.guest_id
            ]
            guest_first_name = None
            guest_last_name = None
            if guest_detail:
                guest_first_name = guest_detail[0].first_name
                guest_last_name = guest_detail[0].last_name
            guest_stay = {
                "first_name": guest_first_name,
                "last_name": guest_last_name,
                "checkin_date": format_date_human_readable(gs.checkin_date),
                "checkout_date": format_date_human_readable(gs.checkout_date),
            }
            guest_stays.append(guest_stay)
        return guest_stays

    def guest_adult_and_child_count(booking_aggregate):
        adult_count = 0
        chilren_count = 0
        for rs in booking_aggregate.room_stays:
            for guest_stay in rs.guest_stays:
                if (
                    guest_stay.age_group == AgeGroup.ADULT
                    and guest_stay.status != BookingStatus.CANCELLED
                ):
                    adult_count += 1
                if (
                    guest_stay.age_group == AgeGroup.CHILD
                    and guest_stay.status != BookingStatus.CANCELLED
                ):
                    chilren_count += 1
        return adult_count, chilren_count

    def booking_owner_details(booking_aggregate):
        owner_id = booking_aggregate.booking.owner_id
        guest_detail = [
            customer
            for customer in booking_aggregate.customers
            if customer.customer_id == owner_id
        ][0]

        try:
            country_code = guest_detail.phone.country_code
            phone_number = guest_detail.phone.number
        except AttributeError:
            country_code = phone_number = None

        try:
            booking_owner_address_line1 = guest_detail.address.field_1
            booking_owner_address_line2 = guest_detail.address.field_2
            booking_owner_address_city = guest_detail.address.city
            booking_owner_address_state = guest_detail.address.state
            booking_owner_address_pincode = guest_detail.address.pincode
        except AttributeError:
            booking_owner_address_line1 = (
                booking_owner_address_line2
            ) = booking_owner_address_city = None
            booking_owner_address_state = booking_owner_address_pincode = None

        try:
            gstin_number = guest_detail.gstin_number
            legal_name = guest_detail.legal_number
            gst_details_address_line1 = guest_detail.address.field_1
            gst_details_address_line2 = guest_detail.address.field_2
            gst_details_address_city = guest_detail.address.city
            gst_details_address_state = guest_detail.address.state
            gst_details_address_pincode = guest_detail.address.pincode
        except AttributeError:
            gstin_number = (
                legal_name
            ) = gst_details_address_line1 = gst_details_address_line2 = None
            gst_details_address_state = (
                gst_details_address_pincode
            ) = gst_details_address_city = None

        booking_owner = {
            "first_name": guest_detail.first_name,
            "last_name": guest_detail.last_name,
            "phone": {"country_code": country_code, "number": phone_number},
            "address": {
                "line1": booking_owner_address_line1,
                "line2": booking_owner_address_line2,
                "city": booking_owner_address_city,
                "state": booking_owner_address_state,
                "pincode": booking_owner_address_pincode,
            },
            "gst_details": {
                "gstin_number": gstin_number,
                "legal_name": legal_name,
                "address": {
                    "line1": gst_details_address_line1,
                    "line2": gst_details_address_line2,
                    "city": gst_details_address_city,
                    "state": gst_details_address_state,
                    "pincode": gst_details_address_pincode,
                },
            },
        }
        return booking_owner

    def generate_and_upload_booking_voucher(
        booking_aggregate, bill_aggregate, hotel_aggregate
    ):
        (
            hotel_aggregate,
            hotel_config_aggregate,
            hotel_context,
        ) = set_hotel_context(hotel_aggregate.hotel_id)
        adult_count, children_count = guest_adult_and_child_count(booking_aggregate)
        catalog_hotel_response = CatalogServiceClient().fetch_hotel(
            hotel_aggregate.hotel_id
        )
        bank_details = catalog_hotel_response['property_details'].get('bank_details')
        change_in_booking_amount = (
            ref_number_booking_amount_change.get(
                booking_aggregate.booking.reference_number
            )
            if ref_number_booking_amount_change
            else None
        )
        payload = {
            "booking": {
                "id": booking_aggregate.booking_id,
                "status": booking_aggregate.booking.status,
                "creation_date": format_date_human_readable(
                    booking_aggregate.booking.created_at
                ),
                "reference_number": booking_aggregate.booking.reference_number,
                "checkin_date": format_date_human_readable(
                    booking_aggregate.booking.checkin_date
                ),
                "no_of_nights": (
                    booking_aggregate.booking.checkout_date.date()
                    - booking_aggregate.booking.checkin_date.date()
                ).days,
                "checkout_date": format_date_human_readable(
                    booking_aggregate.booking.checkout_date
                ),
                "guest_info": {
                    "adult_count": adult_count,
                    "child_count": children_count,
                },
                "room_stays": booking_room_stays(booking_aggregate, room_type_map),
            },
            "bill": {
                "total_posttax_amount": str(
                    bill_aggregate.total_posttax_amount().amount
                ),
                "net_paid_amount": str(bill_aggregate.net_paid_amount.amount),
                "total_credit_posttax_amount": str(
                    bill_aggregate.total_credit_posttax_amount().amount
                ),
                "net_payable": str(bill_aggregate.net_payable.amount),
            },
            "booking_owner": booking_owner_details(booking_aggregate),
            "hotel": {
                "name": hotel_aggregate.hotel.name,
                "logo": hotel_aggregate.hotel.logo,
                "bank_details": dict(),
                "email": hotel_aggregate.hotel.email,
                "phone_number": f"{catalog_hotel_response['property_details']['reception_landline']} ,"
                f"{catalog_hotel_response['property_details']['reception_mobile']}",
                "address": {
                    "line1": hotel_aggregate.hotel.address.field_1,
                    "line2": hotel_aggregate.hotel.address.field_2,
                    "city": hotel_aggregate.hotel.address.city,
                    "state": hotel_aggregate.hotel.address.state,
                    "pincode": hotel_aggregate.hotel.address.pincode,
                },
            },
            "change_in_booking_amount": change_in_booking_amount,
        }
        if bank_details:
            payload['hotel'].update(
                {
                    "bank_details": {
                        "account_name": f"{bank_details.get('account_name')}",
                        "account_number": f"{bank_details.get('account_number')}",
                        "account_type": f"{bank_details.get('type')}",
                        "bank_name": f"{bank_details.get('bank')}",
                        "branch_address": f"{bank_details.get('branch')}",
                        "ifsc_code": f"{bank_details.get('ifsc_code')}",
                    }
                }
            )
        return payload

    booking_voucher_json_data = generate_and_upload_booking_voucher(
        booking_aggregate, bill_aggregate, hotel_aggregate
    )
    response = TemplateService().generate_email_body(
        namespace, booking_voucher_json_data, TemplateFormat.HTML
    )

    logger.debug("template response: %s", response)
    email_subject = (
        f"Booking Confirmation - {hotel_aggregate.hotel.name} - "
        f"{booking_aggregate.booking.reference_number}"
    )
    booking_owner = booking_aggregate.get_booking_owner()
    if not booking_owner.email:
        receiver_email = booking_aggregate.get_first_guest_email_id()

    else:
        receiver_email = booking_owner.email

    if is_valid_email(receiver_email):
        NotificationServiceClient().email(
            body_html=response,
            subject=email_subject,
            sender=NotificationEmailIds.BOOKING.value,
            recievers=[receiver_email],
        )
    return True
