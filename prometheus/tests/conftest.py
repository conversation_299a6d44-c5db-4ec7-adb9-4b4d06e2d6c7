import pytest
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import current_datetime, yesterday

from prometheus import crs_context
from prometheus.domain.hotel_config.aggregates.hotel_config_aggregate import (
    HotelConfigAggregate,
)
from prometheus.domain.hotel_config.entities.hotel_config import HotelConfig
from prometheus.tests.factories.aggregate_factories import HotelFactory
from prometheus.tests.factories.entity_factories import AddonFactory
from ths_common.constants.booking_constants import ExpenseAddedBy, ExpenseStatus
from ths_common.constants.hotel_constants import ManagedBy


@pytest.fixture
def checkin_addon():
    addon = AddonFactory.build(
        charge_checkin=True, charge_checkout=False, charge_other_days=False
    )

    return addon


@pytest.fixture
def some_expense():
    return {
        "expense_item_id": '123',
        "room_stay_id": 1,
        "status": ExpenseStatus.CONSUMED,
        "comments": "Some comment",
        "guests": ["123"],
        "added_by": ExpenseAddedBy.HOTEL,
        "applicable_date": current_datetime(),
    }


@pytest.fixture(scope="function")
def active_hotel_aggregate():
    yesterday_ = dateutils.to_date(yesterday())
    hotel_aggregate = HotelFactory(
        hotel__hotel_id="0016932", hotel__current_business_date=yesterday_
    )
    yield hotel_aggregate


@pytest.fixture(scope='function', autouse=True)
def hotel_config_crs(active_hotel_aggregate):
    hotel_config = HotelConfig(
        hotel_id="0016932",
        migration_start_date=dateutils.current_datetime(),
        migration_end_date=dateutils.current_datetime(),
        live_date=dateutils.current_datetime(),
        managed_by=ManagedBy.CRS,
    )

    hotel_config_aggregate = HotelConfigAggregate(hotel_config=hotel_config)
    crs_context.set_hotel_context(active_hotel_aggregate, hotel_config_aggregate)
    yield hotel_config_aggregate
