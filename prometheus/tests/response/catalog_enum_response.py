response = [
    {
        "enum_name": "dnr_sub_type",
        "enum_values": [
            {"label": "AC not working", "value": "ac_not_working"},
            {"label": "No hot water", "value": "no_hot_water"},
            {"label": "Dirty linen", "value": "dirty_linen"},
            {"label": "Issue with bathroom", "value": "bathroom_issue"},
            {"label": "Issue with mattress", "value": "bed_issue"},
            {"label": "Issue with curtains", "value": "curtain_issue"},
            {"label": "Issue with Doors/Windows", "value": "windows_door_issue"},
            {"label": "Issue with Table/Chair", "value": "table_chair_issue"},
            {"label": "Renovation in progress", "value": "renovation_in_progress"},
            {"label": "Others", "value": "others"},
            {"label": "Stop Sell", "value": "stop_sell"},
            {"label": "House Use", "value": "house_use"},
            {"label": "Churn", "value": "churn"},
            {"label": "Inactive-Room", "value": "inactive-room"},
            {"label": "TV NOT working", "value": "tv_not_working"},
        ],
        "label": "Dnr Sub Type",
    },
    {
        "enum_name": "dnr_source",
        "enum_values": [
            {"label": "Prowl", "value": "Prowl"},
            {"label": "CRS-Web", "value": "CRS-Web"},
        ],
        "label": "Dnr Source",
    },
    {
        "enum_name": "payment_channels",
        "enum_values": [
            {"label": "Front Desk", "value": "front_desk"},
            {"label": "Online", "value": "online"},
        ],
        "label": "Payment Channels",
    },
    {
        "enum_name": "charge_type",
        "enum_values": [
            {"label": "Credit", "value": "credit"},
            {"label": "Non Credit", "value": "non-credit"},
        ],
        "label": "Charge Type",
    },
    {
        "enum_name": "booking_search_parameter",
        "enum_values": [
            {"label": "Guest Name", "value": "guest_name"},
            {"label": "Guest Phone Number", "value": "guest_phone"},
            {"label": "Guest Email", "value": "guest_email"},
            {"label": "Booking ID", "value": "reference_number"},
            {"label": "Status", "value": "status"},
            {
                "label": "Booking Owner Reference ID",
                "value": "booking_owner_reference_id",
            },
        ],
        "label": "Booking Search Parameter",
    },
    {
        "enum_name": "booking_status",
        "enum_values": [
            {"label": "Temporary", "value": "temporary"},
            {"label": "Confirmed", "value": "confirmed"},
            {"label": "Checked In", "value": "checked_in"},
            {"label": "Checked Out", "value": "checked_out"},
            {"label": "No Show", "value": "noshow"},
            {"label": "Cancelled", "value": "cancelled"},
            {"label": "Part Checked In", "value": "part_checked_in"},
            {"label": "Part Checked Out", "value": "part_checked_out"},
        ],
        "label": "Booking Status",
    },
    {
        "enum_name": "id_proof_type",
        "enum_values": [
            {"label": "Passport", "value": "passport"},
            {"label": "Aadhar", "value": "aadhar"},
            {"label": "Voter ID", "value": "voter_id"},
            {"label": "Driving License", "value": "driving_license"},
            {"label": "PAN card", "value": "pan_card"},
        ],
        "label": "Id Proof Type",
    },
    {
        "enum_name": "charge_bill_to_type",
        "enum_values": [
            {"label": "Company", "value": "company"},
            {"label": "Guest", "value": "guest"},
        ],
        "label": "Charge Bill To Type",
    },
    {
        "enum_name": "payment_mode",
        "enum_values": [
            {"label": "Cash", "value": "cash"},
            {"label": "Credit Card", "value": "credit_card"},
            {"label": "Debit Card", "value": "debit_card"},
            {"label": "Payment Link", "value": "payment_link"},
            {"label": "Hotel Collectible", "value": "hotel_collectible"},
            {"label": "Phone Pe", "value": "phone_pe"},
            {"label": "Amazon Pay", "value": "amazon_pay"},
            {"label": "Air Pay", "value": "air_pay"},
            {"label": "RazorPay Payment Gateway", "value": "razorpay_payment_gateway"},
            {"label": "RazorPay API", "value": "razorpay_api"},
            {"label": "Payment Gateway/Wallet", "value": "payment_service"},
            {"label": "Paid By Treebo", "value": "paid_by_treebo"},
            {"label": "Paid At OTA", "value": "paid_at_ota"},
            {"label": "Treebo Points", "value": "treebo_points"},
            {"label": "Treebo Corporate Rewards", "value": "treebo_corporate_rewards"},
            {"label": "Other", "value": "other"},
            {"label": "Credit Shell", "value": "credit_shell"},
            {"label": "Voucher", "value": "Voucher"},
            {"label": "UPI", "value": "UPI"},
            {"label": "bank_transfer_hotel", "value": "bank_transfer_hotel"},
            {"label": "bank_transfer_treebo", "value": "bank_transfer_treebo"},
            {"label": "Transferred Credit", "value": "transferred_credit"},
        ],
        "label": "Payment Mode",
    },
    {
        "enum_name": "payment_type",
        "enum_values": [
            {"label": "Payment", "value": "payment"},
            {"label": "Refund", "value": "refund"},
        ],
        "label": "Payment Type",
    },
    {
        "enum_name": "paid_to",
        "enum_values": [
            {"label": "Guest", "value": "guest"},
            {"label": "Corporate", "value": "corporate"},
            {"label": "TA", "value": "ta"},
            {"label": "Treebo", "value": "treebo"},
            {"label": "Ota", "value": "ota"},
            {"label": "Hotel", "value": "hotel"},
            {"label": "Seller", "value": "seller"},
        ],
        "label": "Paid To",
    },
    {
        "enum_name": "paid_by",
        "enum_values": [
            {"label": "Guest", "value": "guest"},
            {"label": "Corporate", "value": "corporate"},
            {"label": "TA", "value": "ta"},
            {"label": "Treebo", "value": "treebo"},
            {"label": "OTA", "value": "ota"},
            {"label": "Hotel", "value": "hotel"},
            {"label": "Seller", "value": "seller"},
        ],
        "label": "Paid By",
    },
    {
        "enum_name": "dnr_type",
        "enum_values": [
            {"label": "Maintenance", "value": "maintenance"},
            {"label": "Non_Maintenance", "value": "non_maintenance"},
        ],
        "label": "DNR Type",
    },
    {
        "enum_name": "dnr_maintenance_sub_type",
        "enum_values": [
            {"label": "TV not working", "value": "tv_not_working"},
            {"label": "AC not working", "value": "ac_not_working"},
            {"label": "No hot water", "value": "no_hot_water"},
            {"label": "Dirty linen", "value": "dirty_linen"},
            {"label": "Issue with bathroom", "value": "bathroom_issue"},
            {"label": "Issue with mattress", "value": "bed_issue"},
            {"label": "Issue with curtains", "value": "curtain_issue"},
            {"label": "Issue with Doors/Windows", "value": "windows_door_issue"},
            {"label": "Issue with Table/Chair", "value": "table_chair_issue"},
            {"label": "Renovation in progress", "value": "renovation_in_progress"},
            {"label": "Others", "value": "others"},
        ],
        "label": "DNR Maintenance Sub Type",
    },
    {
        "enum_name": "dnr_non_maintenance_sub_type",
        "enum_values": [
            {"label": "Stop Sell", "value": "stop_sell"},
            {"label": "House Use", "value": "house_use"},
            {"label": "Churn", "value": "churn"},
            {"label": "Others", "value": "others"},
        ],
        "label": "DNR Non-Maintenance Sub Type",
    },
    {
        "enum_name": "booking_type",
        "enum_values": [
            {"label": "room", "value": "room"},
            {"label": "conference", "value": "conference"},
        ],
        "label": "Booking Type",
    },
    {
        "enum_name": "cancel_reason",
        "enum_values": [
            {"label": "Guest Plan Changed", "value": "plan_changed"},
            {"label": "Guest Found Better Deal", "value": "found_better_deal"},
            {"label": "Created By Mistake", "value": "created_by_mistake"},
            {"label": "Booking Moved", "value": "booking_moved"},
            {"label": "Other", "value": "other"},
            {"label": "Guest Not Responding", "value": "not_responding"},
        ],
        "label": "Cancel Reason",
    },
    {
        "enum_name": "credit_card",
        "enum_values": [
            {"label": "VISA", "value": "VISA"},
            {"label": "Mastercard", "value": "Mastercard"},
            {"label": "Amex", "value": "Amex"},
            {"label": "RuPay", "value": "RuPay"},
        ],
        "label": "Credit Card Types",
    },
    {
        "enum_name": "phone_pe",
        "enum_values": [
            {"label": "Phonepe App", "value": "phonepe_app"},
            {"label": "Phonepe Web", "value": "phonepe_web"},
        ],
        "label": "PhonePe Types",
    },
    {
        "enum_name": "debit_card",
        "enum_values": [
            {"label": "VISA", "value": "VISA"},
            {"label": "Mastercard", "value": "Mastercard"},
            {"label": "RuPay", "value": "RuPay"},
        ],
        "label": "Debit Card Types",
    },
    {
        "enum_name": "effective_status",
        "enum_values": [
            {"label": "active", "value": "active"},
            {"label": "past", "value": "past"},
            {"label": "deleted", "value": "deleted"},
        ],
        "label": "DNR Status",
    },
    {
        "enum_name": "addon_relative_date",
        "enum_values": [
            {"label": "Checkin", "value": "checkin"},
            {"label": "Checkin Plus One", "value": "checkin_plus_one"},
            {"label": "Checkout", "value": "checkout"},
            {"label": "Checkout Minus One", "value": "checkout_minus_one"},
        ],
        "label": "Addon Relative Date Options",
    },
    {
        "enum_name": "amazon_pay",
        "enum_values": [
            {"label": "Amazonpay App", "value": "amazonpay_app"},
            {"label": "Amazonpay Web", "value": "amazonpay_web"},
        ],
        "label": "AmazonPay Types",
    },
    {
        "enum_name": "payment_mode_sub_types",
        "enum_values": [
            {"label": "VISA", "value": "VISA"},
            {"label": "Mastercard", "value": "Mastercard"},
            {"label": "Amex", "value": "Amex"},
            {"label": "RuPay", "value": "RuPay"},
            {"label": "Amazonpay App", "value": "amazonpay_app"},
            {"label": "Amazonpay Web", "value": "amazonpay_web"},
            {"label": "Phonepe App", "value": "phonepe_app"},
            {"label": "Phonepe Web", "value": "phonepe_web"},
        ],
    },
    {
        "enum_name": "cancellation_policy",
        "enum_values": [
            {
                "label": "Cancellation Fee As Per Rate Plan",
                "value": "cancellation_fee_as_per_rate_plan",
            },
            {
                "label": "Complete Cancellation Fee Waiver",
                "value": "complete_cancellation_fee_waiver",
            },
            {"label": "Retain Complete Payment", "value": "retain_complete_payment"},
            {
                "label": "Retain Complete Booking Amount",
                "value": "retain_complete_booking_amount",
            },
            {"label": "Custom Cancellation Fee", "value": "custom_cancellation_fee"},
        ],
        "label": "Cancellation Policy",
    },
]
