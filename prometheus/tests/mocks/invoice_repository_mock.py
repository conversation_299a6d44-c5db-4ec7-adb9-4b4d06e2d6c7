from collections import defaultdict

from ths_common.exceptions import AggregateNotFound, OutdatedVersion


class InvoiceRepositoryMock(object):
    """
    Invoice repository mock
    """

    def __init__(self):
        self.invoice_map = dict()
        self.vendor_invoice_no_map = defaultdict(lambda: 0)

    def verify_version(self, invoice_id, version):
        return self.load(invoice_id).invoice.version == version

    def save(self, invoice_aggregate):
        """

        Args:
            invoice_aggregate:

        Returns:

        """
        self.invoice_map[invoice_aggregate.invoice.invoice_id] = invoice_aggregate

    def update(self, invoice_aggregate):
        """

        Args:
            invoice_aggregate:

        Returns:

        """
        self.invoice_map[invoice_aggregate.invoice.invoice_id] = invoice_aggregate

    def load_for_bill_id(self, bill_id):
        """
        Load all the invoices for a given bill_id
        Args:
            bill_id: bill id to be used as the filter

        Returns:

        """
        invoices = []
        for invoice_id, invoice_aggregate in self.invoice_map.items():
            if invoice_aggregate.invoice.bill_id == bill_id:
                invoices.append(invoice_aggregate)

        return invoices

    def load(self, invoice_id):
        """
        load invoice
        Args:
            invoice_id:

        Returns:

        """
        if not self.invoice_map.get(invoice_id):
            raise AggregateNotFound("InvoiceAggregate", invoice_id)
        return self.invoice_map.get(invoice_id)

    def load_for_update(self, invoice_id, version=None):
        if not self.invoice_map.get(invoice_id):
            raise AggregateNotFound("InvoiceAggregate", invoice_id)
        invoice_aggregate = self.invoice_map.get(invoice_id)
        if version is not None and invoice_aggregate.invoice.version != version:
            raise OutdatedVersion(
                'InvoiceAggregate', version, invoice_aggregate.invoice.version
            )
        return invoice_aggregate

    def generate_invoice_number(self, vendor_id):
        current_count_obj = self.vendor_invoice_no_map[vendor_id]
        sequence_number = current_count_obj + 1
        self.vendor_invoice_no_map[vendor_id] = sequence_number
        return sequence_number
