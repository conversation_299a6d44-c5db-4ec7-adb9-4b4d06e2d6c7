import factory

from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.aggregates.invoice_aggregate import InvoiceAggregate
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.catalog.aggregates.hotel_aggregate import HotelAggregate
from prometheus.domain.catalog.aggregates.reseller_gst_aggregate import (
    ResellerGSTAggregate,
)
from prometheus.domain.catalog.aggregates.room_aggregate import RoomAggregate
from prometheus.domain.catalog.aggregates.room_type_aggregate import RoomTypeAggregate
from prometheus.domain.catalog.aggregates.sku_category_aggregate import (
    SkuCategoryAggregate,
)
from prometheus.domain.inventory.aggregates.room_allotment_aggregate import (
    RoomAllotmentAggregate,
)
from prometheus.domain.inventory.aggregates.room_type_inventory_aggregate import (
    RoomTypeInventoryAggregate,
)
from prometheus.tests.factories.entity_factories import (
    BilledEntityFactory,
    BillEntityFactory,
    BookingFactory,
    ChargeFactory,
    CustomerFactory,
    CustomerFactoryV2,
    HotelEntityFactory,
    HotelRoomTypeConfigFactory,
    HousekeepingRecordFactory,
    InvoiceFactory,
    ResellerGSTFactory,
    RoomAllotmentFactory,
    RoomFactory,
    RoomInventoryFactory,
    RoomRatePlanFactory,
    RoomStayFactory,
    RoomStayFactoryV2,
    RoomTypeFactory,
    RoomTypeInventoryAvailabilityFactory,
    RoomTypeInventoryFactory,
    SkuCategoryFactory,
    TravelAgentBilledEntityFactory,
)
from prometheus.tests.test_utils import yesterday


class HotelFactory(factory.Factory):
    hotel = factory.SubFactory(HotelEntityFactory)
    room_type_configs = factory.List(
        [
            factory.SubFactory(HotelRoomTypeConfigFactory),
            factory.SubFactory(HotelRoomTypeConfigFactory, room_type_id="rt02"),
        ]
    )

    class Meta:
        model = HotelAggregate


class RoomTypeAggregateFactory(factory.Factory):
    room_type = factory.SubFactory(RoomTypeFactory)

    class Meta:
        model = RoomTypeAggregate


class BillFactory(factory.Factory):
    bill = factory.SubFactory(BillEntityFactory)
    payments = []
    charges = []
    billed_entities = factory.List(
        [
            factory.SubFactory(BilledEntityFactory),
            factory.SubFactory(TravelAgentBilledEntityFactory),
        ]
    )

    class Meta:
        model = BillAggregate

    class Params:
        with_charges = factory.Trait(
            charges=factory.List([factory.SubFactory(ChargeFactory)])
        )


class RoomTypeInventoryAggregateFactory(factory.Factory):
    room_type_inventory = factory.SubFactory(RoomTypeInventoryFactory)
    room_type_inventory_availabilities = factory.List(
        [
            RoomTypeInventoryAvailabilityFactory(),
            RoomTypeInventoryAvailabilityFactory(date=yesterday()),
        ]
    )

    class Meta:
        model = RoomTypeInventoryAggregate

    class Params:
        with_one_room = factory.Trait(
            room_type_inventory_availabilities=factory.List(
                [RoomTypeInventoryAvailabilityFactory(actual_count=1)]
            )
        )


class RoomAggregateFactory(factory.Factory):
    room = factory.SubFactory(RoomFactory)

    class Meta:
        model = RoomAggregate


class RoomAllotmentAggregateFactory(factory.Factory):
    room_inventory = factory.SubFactory(RoomInventoryFactory)
    housekeeping_record = factory.SubFactory(HousekeepingRecordFactory)
    room_allotments = factory.List([RoomAllotmentFactory])

    class Meta:
        model = RoomAllotmentAggregate


class SkuCategoryAggregateFactory(factory.Factory):
    sku_category = factory.SubFactory(SkuCategoryFactory)

    class Meta:
        model = SkuCategoryAggregate


class BookingAggregateFactory(factory.Factory):
    booking = factory.SubFactory(BookingFactory)
    room_stays = factory.List([factory.SubFactory(RoomStayFactory)])
    customers = factory.List([factory.SubFactory(CustomerFactory)])

    class Meta:
        model = BookingAggregate

    class Params:
        with_guest_allocation = factory.Trait(
            room_stays=factory.List(
                [factory.SubFactory(RoomStayFactory, with_guest_allocation=True)]
            )
        )
        with_2_guest_stays = factory.Trait(
            room_stays=factory.List(
                [factory.SubFactory(RoomStayFactory, with_2_guest_stays=True)]
            )
        )


class BookingAggregateFactoryV2(factory.Factory):
    booking = factory.SubFactory(BookingFactory)
    room_stays = factory.List([factory.SubFactory(RoomStayFactoryV2)])
    customers = factory.List([factory.SubFactory(CustomerFactoryV2)])
    rate_plans = factory.List([factory.SubFactory(RoomRatePlanFactory)])

    class Meta:
        model = BookingAggregate

    class Params:
        with_guest_allocation = factory.Trait(
            room_stays=factory.List(
                [factory.SubFactory(RoomStayFactoryV2, with_guest_allocation=True)]
            )
        )
        with_2_guest_stays = factory.Trait(
            room_stays=factory.List(
                [factory.SubFactory(RoomStayFactoryV2, with_2_guest_stays=True)]
            )
        )


class ResellerGSTAggregateFactory(factory.Factory):
    reseller_gst_detail = factory.SubFactory(ResellerGSTFactory)

    class Meta:
        model = ResellerGSTAggregate


class InvoiceAggregateFactory(factory.Factory):
    invoice = factory.SubFactory(InvoiceFactory)
    invoice_charges = factory.List(
        []
    )  # factory.List([factory.SubFactory(RoomStayFactory)])

    class Meta:
        model = InvoiceAggregate
