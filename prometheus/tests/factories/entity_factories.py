from datetime import timedelta
from decimal import Decimal
from functools import lru_cache

import factory
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import current_date as today
from treebo_commons.utils.dateutils import current_datetime

from prometheus.domain.billing.entities.bill import Bill
from prometheus.domain.billing.entities.billed_entity import (
    BilledEntity,
    BilledEntityAccount,
)
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.billing.entities.invoice import Invoice
from prometheus.domain.billing.entities.invoice_charge import InvoiceCharge
from prometheus.domain.billing.entities.payment import Payment
from prometheus.domain.booking.dtos.rate_plan_dtos import (
    RatePlanCancellationPolicy,
    RatePlanChildPolicy,
    RatePlanInclusion,
    RatePlanInclusionFrequency,
    RatePlanInclusionOffering,
    RatePlanNonRoomNightInclusion,
    RatePlanPackage,
    RatePlanPaymentPolicies,
    RatePlanPolicies,
    RatePlanRestrictions,
)
from prometheus.domain.booking.entities import RoomAllocation
from prometheus.domain.booking.entities.addon import Addon
from prometheus.domain.booking.entities.booking import Booking
from prometheus.domain.booking.entities.booking_funding import BookingFundingConfig
from prometheus.domain.booking.entities.customer import Customer
from prometheus.domain.booking.entities.guest_allocation import GuestAllocation
from prometheus.domain.booking.entities.guest_stay import GuestStay
from prometheus.domain.booking.entities.rate_plan import RatePlan
from prometheus.domain.booking.entities.room_stay import RoomStay
from prometheus.domain.catalog.entities.expense_item import ExpenseItem
from prometheus.domain.catalog.entities.hotel import Hotel
from prometheus.domain.catalog.entities.reseller_gst import ResellerGST
from prometheus.domain.catalog.entities.room import Room, RoomStatus
from prometheus.domain.catalog.entities.room_type import RoomType
from prometheus.domain.catalog.entities.room_type_config import HotelRoomTypeConfig
from prometheus.domain.catalog.entities.sku_category import SkuCategory
from prometheus.domain.company_profiles.dto.company_profiles_dto import SubEntity
from prometheus.domain.inventory.entities.housekeeping_record import HouseKeepingRecord
from prometheus.domain.inventory.entities.room_allotment import RoomAllotment
from prometheus.domain.inventory.entities.room_inventory import RoomInventory
from prometheus.domain.inventory.entities.room_type_inventory import RoomTypeInventory
from prometheus.domain.inventory.entities.room_type_inventory_availability import (
    RoomTypeInventoryAvailability,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    InvoiceStatus,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import (
    AgeGroup,
    BookingStatus,
    BookingTypes,
    Genders,
    ProfileTypes,
    RoomStayType,
    Salutation,
)
from ths_common.constants.catalog_constants import (
    HotelStatus,
    RoomTypeStatus,
    SellerType,
    SkuCategoryStatus,
)
from ths_common.constants.hotel_constants import BrandCodes
from ths_common.constants.inventory_constants import (
    AllottedFor,
    HouseKeepingStatus,
    RoomCurrentStatus,
)
from ths_common.value_objects import (
    Address,
    BookingSource,
    ChargeItem,
    City,
    CompanyDetails,
    DateRange,
    GSTDetails,
    ItemCode,
    LegalDetails,
    Name,
    PhoneNumber,
    RoomRent,
    State,
    VendorDetails,
)


@lru_cache(maxsize=20)
def today():
    return current_datetime()


def tomorrow():
    return today() + timedelta(days=1)


class AddonFactory(factory.Factory):
    room_stay_id = 1
    addon_id = 1
    booking_id = '123'
    expense_item_id = '123'
    name = "Lunch"
    pretax_price = "100"
    posttax_price = "105"
    quantity = 1
    charge_checkin = True
    charge_checkout = True
    charge_other_days = True
    charge_type = ChargeTypes.CREDIT
    bill_to_type = ChargeBillToTypes.COMPANY
    status = None
    start_date = today()
    end_date = today()
    new = True
    dirty = True

    class Meta:
        model = Addon


class RoomTypeFactory(factory.Factory):
    room_type_id = 'rt01'
    type = 'ACACIA'

    class Meta:
        model = RoomType


class SkuCategoryFactory(factory.Factory):
    item_code = ItemCode.from_string('HSN:996331')
    name = 'food'
    sku_category_id = 'food'
    status = SkuCategoryStatus.ACTIVE

    class Meta:
        model = SkuCategory


class RoomTypeInventoryAvailabilityFactory(factory.Factory):
    room_type_id = "rt01"
    date = today()
    actual_count = 5
    dirty = True
    new = True

    class Meta:
        model = RoomTypeInventoryAvailability


class RoomTypeInventoryFactory(factory.Factory):
    hotel_id = "0016932"
    room_type_id = "rt01"
    date_range = DateRange(today() - timedelta(days=5), today() + timedelta(days=5))

    class Meta:
        model = RoomTypeInventory


class RoomFactory(factory.Factory):
    hotel_id = "0016932"
    room_id = "38161"
    room_type_id = "rt02"
    room_number = "301"
    status = RoomStatus.ACTIVE
    deleted = False

    class Meta:
        model = Room


class RoomInventoryFactory(factory.Factory):
    hotel_id = "0016932"
    room_id = "38161"
    current_status = RoomCurrentStatus.VACANT

    class Meta:
        model = RoomInventory


class HousekeepingRecordFactory(factory.Factory):
    hotel_id = "0016932"
    room_id = "38161"
    housekeeping_status = HouseKeepingStatus.CLEAN
    remarks = ""
    housekeeper_id = None

    class Meta:
        model = HouseKeepingRecord


class RoomAllotmentFactory(factory.Factory):
    allotment_id = ""
    start_time = dateutils.current_datetime()
    expected_end_time = dateutils.add(dateutils.current_datetime(), days=5)
    actual_end_time = None
    allotted_for = AllottedFor.STAY
    deleted = False

    class Meta:
        model = RoomAllotment


class HotelRoomTypeConfigFactory(factory.Factory):
    room_type_config_id = factory.Sequence(lambda n: n + 1)
    room_type_id = "rt01"
    base_pax = 1
    max_adult = 3
    max_child = 1
    max_adult_plus_children = 3
    status = RoomTypeStatus.ACTIVE
    deleted = False

    class Meta:
        model = HotelRoomTypeConfig


class StateFactory(factory.Factory):
    state_id = "1"
    name = "Karnataka"

    class Meta:
        model = State


class ChargeItemFactory(factory.Factory):
    name = "roomnight"
    sku_category_id = "roomnight"

    class Meta:
        model = ChargeItem


class CityFactory(factory.Factory):
    city_id = "1"
    name = "Bangalore"

    class Meta:
        model = City


class HotelEntityFactory(factory.Factory):
    hotel_id = "0016932"
    name = "Treebo CRS"
    area = "CRS Bay"
    status = HotelStatus.ACTIVE
    legal_name = "Treebo CRS"
    legal_address = "AMR Tech park"
    gstin_num = "IN111"
    switch_over_time = "06:00:00"
    checkin_grace_time = 60
    checkout_grace_time = 60
    checkin_time = "11:00:00"
    checkout_time = "12:00:00"
    free_late_checkout_time = "15:00:00"
    system_freeze_time = "15:00:00"
    brands = [BrandCodes.TREND]
    state = factory.SubFactory(StateFactory)
    city = factory.SubFactory(CityFactory)
    current_business_date = dateutils.current_date()
    is_test = False

    class Meta:
        model = Hotel


class ChargeFactory(factory.Factory):
    charge_id = factory.Sequence(lambda n: n + 1)
    pretax_amount = Money(10, CurrencyType('INR'))
    tax_amount = Money(10, CurrencyType('INR'))
    tax_details = None
    posttax_amount = Money(100, CurrencyType('INR'))
    charge_type = ChargeTypes.NON_CREDIT
    bill_to_type = ChargeBillToTypes.GUEST

    status = ChargeStatus.CREATED
    recorded_time = today()
    applicable_date = today()
    comment = "some comment"
    created_by = "someone"
    charge_item = factory.SubFactory(ChargeItemFactory)
    new = True
    dirty = True

    class Meta:
        model = Charge


class BillEntityFactory(factory.Factory):
    # TODO make ids sequence after removing all hard
    # id dependencies from test cases
    # bill_id = factory.Sequence(lambda n: 'BIL-{0}'.format(n))
    bill_id = factory.sequence(lambda x: f"BIL-{x}")
    bill_date = today()
    vendor_id = "0016932"
    app_id = "com.treebo.ths"
    parent_reference_number = "19-2323232-23232"
    vendor_details = VendorDetails(
        hotel_id="0016932",
        name="Test Hotel",
        state_code="04",
        gst_details=None,
        phone=None,
        email="<EMAIL>",
        url=None,
        address=None,
    )
    version = 1
    parent_info = dict()

    class Meta:
        model = Bill


class CustomerFactory(factory.Factory):
    customer_id = "123"
    external_ref_id = "halfman"
    profile_type = ProfileTypes.INDIVIDUAL
    first_name = "Tyrion"
    last_name = "Lannister"
    gender = Genders.MALE
    age = 40
    address = Address("f1", "f2", "city", "state", "country", "pin")
    phone = PhoneNumber("123")
    email = "<EMAIL>"
    new = True
    dirty = True
    billed_entity_id = 1

    class Meta:
        model = Customer


class CustomerFactoryV2(factory.Factory):
    customer_id = "123"
    external_ref_id = "halfman"
    profile_type = ProfileTypes.INDIVIDUAL
    first_name = "Tyrion"
    last_name = "Lannister"
    gender = Genders.MALE
    age = 40
    address = Address("f1", "f2", "city", "state", "country", "pin")
    phone = PhoneNumber("123")
    email = "<EMAIL>"
    salutation = Salutation("Mr.")
    new = True
    dirty = True
    billed_entity_id = 1
    gst_details = GSTDetails(
        legal_name="random string", gstin_num='1000300', address=address
    )

    class Meta:
        model = Customer


class ExpenseItemFactory(factory.Factory):
    expense_item_id = "123"
    name = "Lunch"
    description = "Lunch expense"
    short_name = "LNCH"
    sku_category_id = "food"
    linked = False
    sku_id = None

    class Meta:
        model = ExpenseItem


class BookingSourceFactory(factory.Factory):
    channel_code = "b2b"
    subchannel_code = "123"
    application_code = "sdf"

    class Meta:
        model = BookingSource


class GuestAllocationFactory(factory.Factory):
    guest_allocation_id = 1
    guest_id = "123"
    assigned_by = "fdm"
    checkin_date = today()
    checkout_date = tomorrow()
    is_current = True
    new = True
    dirty = True

    class Meta:
        model = GuestAllocation


class GuestStayFactory(factory.Factory):
    # guest_stay_id = 1
    guest_stay_id = factory.Sequence(lambda n: n + 1)
    status = BookingStatus.RESERVED
    age_group = AgeGroup.ADULT
    checkin_date = today()
    checkout_date = tomorrow()
    new = True
    dirty = True
    guest_allocations = factory.List([factory.SubFactory(GuestAllocationFactory)])

    class Meta:
        model = GuestStay

    class Params:
        with_guest_allocation = factory.Trait(
            guest_allocations=factory.List(
                [factory.SubFactory(GuestAllocationFactory)]
            ),
            status=BookingStatus.CHECKED_IN,
        )


class RoomAllocationFactory(factory.Factory):
    room_allocation_id = 1
    room_stay_id = 1
    room_id = 1
    room_type_id = 'rt01'
    assigned_by = 'test'
    checkin_date = today()
    checkout_date = tomorrow()
    room_no = '1'
    new = True
    dirty = True

    class Meta:
        model = RoomAllocation


class RoomStayFactory(factory.Factory):
    # room_stay_id = factory.Sequence(lambda n: n+1)
    room_stay_id = 1
    room_type_id = 'rt01'
    type = RoomStayType.NIGHT
    status = BookingStatus.RESERVED
    checkin_date = today()
    checkout_date = tomorrow()
    actual_checkin_date = None
    actual_checkout_date = None
    guest_stays = factory.List([factory.SubFactory(GuestStayFactory)])
    # guest_stays = factory.List([factory.SubFactory(GuestStayFactory, checkin_date=factory.SelfAttribute(
    # '..checkin_date'))])
    charge_id_map = {dateutils.date_to_ymd_str(dateutils.to_date(today())): 1}
    room_rents = [
        RoomRent(
            applicable_date=dateutils.to_date(today()), posttax_amount=Money("1500 INR")
        )
    ]
    new = True
    dirty = True

    class Meta:
        model = RoomStay

    class Params:
        with_guest_allocation = factory.Trait(
            guest_stays=factory.List(
                [factory.SubFactory(GuestStayFactory, with_guest_allocation=True)]
            )
        )
        with_2_guest_stays = factory.Trait(
            guest_stays=factory.List(
                [
                    factory.SubFactory(GuestStayFactory),
                    factory.SubFactory(GuestStayFactory),
                ]
            )
        )


class RoomStayFactoryV2(factory.Factory):
    # room_stay_id = factory.Sequence(lambda n: n+1)
    room_stay_id = 1
    room_type_id = 'rt01'
    type = RoomStayType.NIGHT
    status = BookingStatus.RESERVED
    checkin_date = today()
    checkout_date = tomorrow()
    actual_checkin_date = None
    actual_checkout_date = None
    guest_stays = factory.List([factory.SubFactory(GuestStayFactory)])
    # guest_stays = factory.List([factory.SubFactory(GuestStayFactory, checkin_date=factory.SelfAttribute(
    # '..checkin_date'))])
    charge_id_map = {dateutils.date_to_ymd_str(dateutils.to_date(today())): 1}
    room_rents = [
        RoomRent(
            applicable_date=dateutils.to_date(today()), posttax_amount=Money("1500 INR")
        )
    ]
    room_allocations = factory.List([factory.SubFactory(RoomAllocationFactory)])
    new = True
    dirty = True

    class Meta:
        model = RoomStay

    class Params:
        with_guest_allocation = factory.Trait(
            guest_stays=factory.List(
                [factory.SubFactory(GuestStayFactory, with_guest_allocation=True)]
            )
        )
        with_2_guest_stays = factory.Trait(
            guest_stays=factory.List(
                [
                    factory.SubFactory(GuestStayFactory),
                    factory.SubFactory(GuestStayFactory),
                ]
            )
        )


class RoomRatePlanFactory(factory.Factory):
    rate_plan_reference_id = "TEST-RP-103735-1929-3294"
    rate_plan_id = "1"
    rate_plan_code = "test-rate-plan-code"
    name = "test-rate-plan"
    package = RatePlanPackage(
        package_id="TEST-PKG-103718-3247-7318",
        package_name="test-package-name",
        inclusions=[
            RatePlanInclusion(
                sku_id="1",
                name="test-rate-plan-inclusion-name",
                frequency=RatePlanInclusionFrequency(
                    count=1, day_of_serving="Check-In", frequency_type="frequency_type"
                ),
                offering=RatePlanInclusionOffering(
                    quantity=1, offering_type="per_guest"
                ),
            )
        ],
    )
    policies = RatePlanPolicies(
        cancellation_policies=[
            RatePlanCancellationPolicy(
                cancellation_charge_unit="percent_of_booking_value",
                cancellation_charge_value="100",
                cancellation_duration_before_checkin_end=None,
                cancellation_duration_before_checkin_start=None,
            )
        ],
        child_policy=RatePlanChildPolicy(
            charge_per_child=None, child_allowed=False, unit_of_charge=None
        ),
        payment_policies=[
            RatePlanPaymentPolicies(
                advance_payment_percentage="None",
                unit_of_payment_percentage=None,
                days_before_checkin_to_make_payment=None,
                occupancy_percentage=0,
            )
        ],
    )
    restrictions = RatePlanRestrictions(maximum_los=0, minimum_los=0, minimum_abw=0)
    non_room_night_inclusions = [RatePlanNonRoomNightInclusion(price=10000, sku_id="1")]

    class Meta:
        model = RatePlan


class BookingFactory(factory.Factory):
    # booking_id = "123"
    booking_id = factory.Sequence(lambda n: '{0}'.format(n))

    hotel_id = "0016932"
    type = BookingTypes.ROOM
    checkin_date = today()
    checkout_date = tomorrow()
    status = BookingStatus.CONFIRMED
    source = factory.SubFactory(BookingSourceFactory)
    owner_id = "123"

    # lang = factory.SelfAttribute('country.lang')
    # country = factory.SubFactory(CountryFactory)

    hold_till = None
    version = 1
    comments = "something"
    bill_id = factory.sequence(lambda x: f"BIL-{x}")
    reference_number = factory.sequence(lambda x: f"WLK-{x}")
    extra_information = "some extra info"
    deleted = False
    cancellation_reason = None
    cancellation_datetime = None
    seller_model = SellerType.MARKETPLACE
    new = True
    dirty = True
    company_details = CompanyDetails(
        legal_details=LegalDetails(
            legal_name="Test",
            email=None,
            phone=None,
            address=Address("f1", "f2", "city", "state", "country", "pin"),
            tin="tin",
            client_internal_code=None,
            external_reference_id="external",
            is_sez=False,
            has_lut=True,
        )
    )
    travel_agent_details = CompanyDetails(
        legal_details=LegalDetails(
            legal_name="ta",
            email=None,
            phone=None,
            address=None,
            tin="GDSDBNOHGSD",
            client_internal_code=None,
            external_reference_id=None,
            is_sez=True,
            has_lut=False,
        )
    )

    class Meta:
        model = Booking


class AddressFactory(factory.Factory):
    country = "Westeros"
    state = "Crownlands"
    field_1 = "something"
    field_2 = "something else"
    city = "some other thing"
    pincode = "6234"

    class Meta:
        model = Address


class ResellerGSTFactory(factory.Factory):
    id = "1"
    state = factory.SubFactory(StateFactory)
    gstin_num = "GST123"
    date_of_registration = today()
    address = "something"

    class Meta:
        model = ResellerGST


class InvoiceFactory(factory.Factory):
    bill_id = "123"
    invoice_id = factory.Sequence(lambda n: 'INV-{0}'.format(n))
    invoice_number = factory.Sequence(lambda n: 'INVNO-{0}'.format(n))
    vendor_id = "123"
    vendor_details = None
    invoice_date = today()
    invoice_due_date = None
    parent_info = dict()
    bill_to = None
    status = InvoiceStatus.GENERATED
    generated_by = "test"
    generation_channel = "test"

    pretax_amount = Money(Decimal(100.0), CurrencyType.INR)
    tax_amount = Money(Decimal(100.0), CurrencyType.INR)
    posttax_amount = Money(Decimal(100.0), CurrencyType.INR)

    invoice_url = "something"
    bill_to_type = None
    user_info_map = dict()
    allowed_charge_types = []
    allowed_charge_to_ids = []
    version = 1
    tax_details_breakup = dict()
    base_currency = CurrencyType.INR

    class Meta:
        model = Invoice


class PaymentFactory(factory.Factory):
    payment_id = factory.Sequence(lambda n: n)
    amount = Money(Decimal(100.0), CurrencyType.INR)
    date_of_payment = today()
    payment_mode = PaymentModes.CASH

    payment_type = PaymentTypes.PAYMENT
    payment_details = dict()
    status = PaymentStatus.DONE

    paid_to = PaymentReceiverTypes.HOTEL
    payment_channel = PaymentChannels.ONLINE
    payment_ref_id = "something"

    paid_by = PaymentReceiverTypes.GUEST
    comment = "some comment"
    payment_mode_sub_type = None
    amount_in_payment_currency = None
    deleted = False

    class Meta:
        model = Payment

    class Params:
        refund = factory.Trait(payment_type=PaymentTypes.REFUND)


class InvoiceChargeFactory(factory.Factory):
    invoice_charge_id = factory.Sequence(lambda n: n)
    charge_id = factory.Sequence(lambda n: n)
    charge_split_ids = factory.List([])
    pretax_amount = Money(10, CurrencyType.INR)

    tax_amount = Money(20, CurrencyType.INR)
    tax_details = factory.List([])
    posttax_amount = Money(20, CurrencyType.INR)
    charge_type = ChargeTypes.NON_CREDIT

    bill_to_type = ChargeBillToTypes.GUEST
    charge_status = ChargeStatus.CONSUMED
    recorded_time = today()

    applicable_date = today()
    comment = "some comment"
    created_by = "test"
    charge_item = ChargeItem
    charge_to_ids = "1,2"

    deleted = False
    credit_note_generated_amount = None

    class Meta:
        model = InvoiceCharge


class BilledEntityAccountFactory(factory.Factory):
    account_number = 1
    deleted = False
    invoiced = False
    locked = False

    class Meta:
        model = BilledEntityAccount


class BilledEntityFactory(factory.Factory):
    billed_entity_id = 1
    name = Name(first_name="test_company")
    accounts = factory.List([factory.SubFactory(BilledEntityAccountFactory)])
    category = BilledEntityCategory.BOOKER_COMPANY
    deleted = False
    new = True
    dirty = True

    class Meta:
        model = BilledEntity


class TravelAgentBilledEntityFactory(factory.Factory):
    billed_entity_id = 2
    name = Name(first_name="ta_company")
    accounts = factory.List([factory.SubFactory(BilledEntityAccountFactory)])
    category = BilledEntityCategory.TRAVEL_AGENT
    deleted = False
    new = True
    dirty = True

    class Meta:
        model = BilledEntity


# create factory class for SubEntity with all the fields
class SubEntityFactory(factory.Factory):
    agency_type = "OTA"
    business_code = "123"
    client_internal_code = "123"
    created_at = today()
    folio_rules = [
        {"is_btc_allowed": True, "is_credit_enabled": True, "sku_category": "food"}
    ]
    hotel_id = "0016932"
    is_sez_applicable = True
    legal_entity_name = "Treebo CRS"
    parent_entity_id = "0016932"
    point_of_contacts = [
        {
            "designation": "test",
            "email_ids": ["test@gmail"],
            "name": "test",
            "phone_number": {"number": "123", "country_code": "91"},
            "poc_type": "test",
            "user_id": "123",
            "contact_types": ["test"],
            "department": "test",
        }
    ]
    registered_address = {
        "address_line_1": "test",
        "address_line_2": "test",
        "city": "test",
        "country": "test",
        "pincode": "test",
        "state": "test",
    }
    phone_number = None
    status = "ACTIVE"
    statutory_details = [
        {"attachment_url": "test", "field_name": "test", "value": "test"}
    ]
    superhero_company_code = "123"
    trade_name = "Treebo CRS"
    sub_entity_id = "123"
    has_lut = True
    email_id = "test@gmail"
    communication_address = {
        "address_line_1": "test",
        "address_line_2": "test",
        "city": "test",
        "country": "test",
        "pincode": "test",
        "state": "test",
    }
    parent_superhero_company_code = "parent_superhero_company_code"

    class Meta:
        model = SubEntity


class BookingFundingConfigFactory(factory.Factory):
    booking_id = "REF-1"
    guardrails = []
    extra_information = []
    deleted = False

    class Meta:
        model = BookingFundingConfig
