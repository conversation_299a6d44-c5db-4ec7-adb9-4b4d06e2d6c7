import datetime
import random
import string

import pytest
from treebo_commons.money.constants import CurrencyType
from treebo_commons.money.money import Money
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import current_date, to_date

from object_registry import locate_instance
from prometheus import crs_context
from prometheus.application.booking.dtos.new_booking_dto import NewBookingDto
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.common.serializers import NewBookingSchema, UpdateRoomStaySchema
from prometheus.domain.billing.entities.bill import Bill
from prometheus.domain.billing.entities.billed_entity import (
    BilledEntity,
    BilledEntityAccount,
    BilledEntityAccountVO,
)
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.billing.entities.charge_split import ChargeSplit
from prometheus.domain.billing.entities.payment import Payment
from prometheus.domain.billing.entities.payment_split import PaymentSplit
from prometheus.domain.billing.factories.invoice_factory import InvoiceFactory
from prometheus.domain.billing.services.einvoicing_service import EInvoicingService
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.entities import GuestAllocation
from prometheus.domain.booking.entities.booking import Booking
from prometheus.domain.booking.entities.customer import Customer
from prometheus.domain.booking.entities.guest_stay import GuestStay
from prometheus.domain.booking.entities.room_stay import RoomStay
from prometheus.domain.catalog.aggregates.hotel_aggregate import HotelAggregate
from prometheus.domain.catalog.entities.hotel import Hotel
from prometheus.infrastructure.external_clients.cleartax_client import ClearTaxClient
from prometheus.tests.factories.aggregate_factories import (
    BillFactory,
    BookingAggregateFactory,
)
from prometheus.tests.mockers import mock_catalog_client
from prometheus.tests.mocks.catalog_service_client_mock import CatalogServiceClientMock
from prometheus.tests.mocks.invoice_repository_mock import InvoiceRepositoryMock
from prometheus.tests.mocks.invoice_series_repository_mock import (
    InvoiceSeriesRepositoryMock,
)
from prometheus.tests.mocks.tax_service_client_mock import TaxationServiceClientMock
from prometheus.tests.test_utils import today, today_plus_days, yesterday
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    IssuedByType,
    IssuedToType,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
    TaxTypes,
)
from ths_common.constants.booking_constants import (
    AgeGroup,
    BookingStatus,
    BookingTypes,
    ExpenseStatus,
    Genders,
    RoomStayType,
)
from ths_common.constants.catalog_constants import HotelStatus, SellerType
from ths_common.constants.hotel_constants import BrandCodes
from ths_common.value_objects import (
    Address,
    BookingBillParentInfo,
    BookingSource,
    ChargeItem,
    City,
    GSTDetails,
    InvoiceBillToInfo,
    InvoiceIssuedByInfo,
    LoyaltyProgramDetails,
    Name,
    PhoneNumber,
    RoomRatePlan,
    RoomRent,
    State,
    TaxDetail,
    VendorDetails,
)


@pytest.fixture
def address():
    return Address(
        field_1="House no 420",
        field_2="lane no 9211",
        city="Bangalore",
        state="Karnataka",
        country="India",
        pincode=34234234,
    )


@pytest.fixture
def gst_details(address):
    return GSTDetails(
        legal_name="Ram sharma", gstin_num="GST-12433243", address=address
    )


@pytest.fixture
def some_booking_and_bill():
    bill = BillFactory(bill__bill_id="BIL3")
    booking = BookingAggregateFactory(booking__bill_id=bill.bill.bill_id)
    return booking, bill


@pytest.fixture
def phone_no():
    return PhoneNumber(number="23299193922", country_code="001")


@pytest.fixture
def booking_bill_parent_info():
    return BookingBillParentInfo(
        booking_id='01-1243-23213-23232-2211',
        reference_number="TRB-2312312",
        creation_date=datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
    )


@pytest.fixture
def payment_in_done_state():
    return Payment(
        payment_id=1,
        amount=Money('234.56', CurrencyType('INR')),
        date_of_payment=dateutils.isoformat_str_to_datetime(
            '2018-01-12 12:00:00+05:30'
        ),
        payment_mode=PaymentModes.CREDIT_CARD,
        payment_type=PaymentTypes.PAYMENT,
        payment_details=dict(),
        status=PaymentStatus.DONE,
        paid_to=PaymentReceiverTypes.TREEBO,
        payment_channel=21,
        payment_ref_id="sdfjadslkfjasd",
        paid_by=PaymentReceiverTypes.GUEST,
        comment='Test payment',
        amount_in_payment_currency=Money(20, CurrencyType('EUR')),
        payment_splits=[
            PaymentSplit(
                payment_split_id=1, amount=Money("234.56", CurrencyType("INR"))
            )
        ],
    )


@pytest.fixture
def payment_in_pending_state():
    return Payment(
        payment_id=2,
        amount=Money(100, CurrencyType('INR')),
        date_of_payment=dateutils.isoformat_str_to_datetime(
            '2018-01-12 12:00:00+05:30'
        ),
        payment_mode=PaymentModes.CREDIT_CARD,
        payment_type=PaymentTypes.PAYMENT,
        payment_details=dict(),
        status=PaymentStatus.PENDING,
        paid_to=PaymentReceiverTypes.TREEBO,
        payment_channel=21,
        payment_ref_id="sdfjadslkfjasd",
        paid_by=PaymentReceiverTypes.GUEST,
        comment='Test payment1',
        amount_in_payment_currency=Money(20, CurrencyType('EUR')),
        payment_splits=[
            PaymentSplit(payment_split_id=1, amount=Money("100", CurrencyType("INR")))
        ],
    )


@pytest.fixture
def payment_in_pending_state_treebo():
    return Payment(
        payment_id=3,
        amount=Money(200, CurrencyType('INR')),
        date_of_payment=dateutils.isoformat_str_to_datetime(
            '2018-01-12 12:00:00+05:30'
        ),
        payment_mode=PaymentModes.CREDIT_CARD,
        payment_type=PaymentTypes.PAYMENT,
        payment_details=dict(),
        status=PaymentStatus.PENDING,
        paid_to=PaymentReceiverTypes.TREEBO,
        payment_channel=21,
        payment_ref_id="sdfjadslkfjasd",
        paid_by=PaymentReceiverTypes.CORPORATE,
        comment='Test payment 2',
        amount_in_payment_currency=Money(20, CurrencyType('EUR')),
        payment_splits=[
            PaymentSplit(payment_split_id=1, amount=Money("200", CurrencyType("INR")))
        ],
    )


@pytest.fixture
def consumed_charge_non_credit():
    charge_split_1 = ChargeSplit(
        charge_split_id=1,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
        billed_entity_account=BilledEntityAccountVO(1, 1),
    )

    charge_split_2 = ChargeSplit(
        charge_split_id=2,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
        billed_entity_account=BilledEntityAccountVO(1, 2),
    )

    charge_splits = [charge_split_1, charge_split_2]

    return Charge(
        charge_id=1,
        charge_to=["CST-23232", "CST-23233"],
        pretax_amount=Money(100, CurrencyType('INR')),
        tax_amount=Money(10, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money(5, CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money(5, CurrencyType('INR'))),
        ],
        posttax_amount=Money(110, CurrencyType('INR')),
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        status=ChargeStatus.CONSUMED,
        recorded_time=dateutils.localize_datetime(
            datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        applicable_date=dateutils.localize_datetime(
            datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        comment="Charge done",
        created_by="AUTH-12121",
        charge_item=ChargeItem(
            name="RoomStay", sku_category_id="cat_1", details=dict()
        ),
        charge_splits=charge_splits,
    )


@pytest.fixture
def consumed_charge_credit_company():
    charge_split_1 = ChargeSplit(
        charge_split_id=1,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        percentage=50,
        billed_entity_account=BilledEntityAccountVO(1, 1),
    )

    charge_split_2 = ChargeSplit(
        charge_split_id=2,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        percentage=50,
        billed_entity_account=BilledEntityAccountVO(1, 1),
    )

    charge_splits = [charge_split_1, charge_split_2]

    return Charge(
        charge_id=2,
        charge_to=["CST-23232", "CST-23233"],
        pretax_amount=Money(100, CurrencyType('INR')),
        tax_amount=Money(10, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money(5, CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money(5, CurrencyType('INR'))),
        ],
        posttax_amount=Money(110, CurrencyType('INR')),
        charge_type=ChargeTypes.CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        status=ChargeStatus.CONSUMED,
        recorded_time=dateutils.localize_datetime(
            datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        applicable_date=dateutils.localize_datetime(
            datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        comment="Charge done",
        created_by="AUTH-12121",
        charge_item=ChargeItem(
            name="RoomStay", sku_category_id="cat_1", details=dict()
        ),
        charge_splits=charge_splits,
    )


@pytest.fixture
def consumed_charge_non_credit_company():
    charge_split_1 = ChargeSplit(
        charge_split_id=1,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        percentage=50,
        billed_entity_account=BilledEntityAccountVO(1, 1),
    )

    charge_split_2 = ChargeSplit(
        charge_split_id=2,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        percentage=50,
        billed_entity_account=BilledEntityAccountVO(1, 2),
    )

    charge_splits = [charge_split_1, charge_split_2]

    return Charge(
        charge_id=3,
        charge_to=["CST-23232", "CST-23233"],
        pretax_amount=Money(100, CurrencyType('INR')),
        tax_amount=Money(10, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money(5, CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money(5, CurrencyType('INR'))),
        ],
        posttax_amount=Money(110, CurrencyType('INR')),
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        status=ChargeStatus.CONSUMED,
        recorded_time=dateutils.localize_datetime(
            datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        applicable_date=dateutils.localize_datetime(
            datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        comment="Charge done",
        created_by="AUTH-12121",
        charge_item=ChargeItem(
            name="RoomStay", sku_category_id="cat_1", details=dict()
        ),
        charge_splits=charge_splits,
    )


@pytest.fixture
def consumed_charge_non_credit_treebo():
    charge_split_1 = ChargeSplit(
        charge_split_id=1,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
        billed_entity_account=BilledEntityAccountVO(1, 1),
    )

    charge_split_2 = ChargeSplit(
        charge_split_id=2,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
        billed_entity_account=BilledEntityAccountVO(1, 2),
    )

    charge_splits = [charge_split_1, charge_split_2]

    return Charge(
        charge_id=4,
        charge_to=["CST-23232", "CST-23233"],
        pretax_amount=Money(100, CurrencyType('INR')),
        tax_amount=Money(10, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money(5, CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money(5, CurrencyType('INR'))),
        ],
        posttax_amount=Money(110, CurrencyType('INR')),
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        status=ChargeStatus.CONSUMED,
        recorded_time=dateutils.localize_datetime(
            datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        applicable_date=dateutils.localize_datetime(
            datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        comment="Charge done",
        created_by="AUTH-12121",
        charge_item=ChargeItem(
            name="RoomStay", sku_category_id="cat_1", details=dict()
        ),
        charge_splits=charge_splits,
    )


@pytest.fixture
def created_charge_non_credit_guest():
    charge_split_1 = ChargeSplit(
        charge_split_id=1,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
        billed_entity_account=BilledEntityAccountVO(1, 1),
    )

    charge_split_2 = ChargeSplit(
        charge_split_id=2,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
        billed_entity_account=BilledEntityAccountVO(1, 2),
    )

    charge_splits = [charge_split_1, charge_split_2]

    return Charge(
        charge_id=4,
        charge_to=["CST-23232", "CST-23233"],
        pretax_amount=Money(100, CurrencyType('INR')),
        tax_amount=Money(10, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money(5, CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money(5, CurrencyType('INR'))),
        ],
        posttax_amount=Money(110, CurrencyType('INR')),
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        status=ChargeStatus.CREATED,
        recorded_time=dateutils.localize_datetime(
            datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        applicable_date=dateutils.localize_datetime(
            datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        comment="Charge done",
        created_by="AUTH-12121",
        charge_item=ChargeItem(
            name="RoomStay", sku_category_id="cat_1", details=dict()
        ),
        charge_splits=charge_splits,
    )


@pytest.fixture
def bill_aggregate(
    phone_no,
    payment_in_done_state,
    payment_in_pending_state,
    payment_in_pending_state_treebo,
    consumed_charge_non_credit,
    consumed_charge_credit_company,
    consumed_charge_non_credit_company,
    consumed_charge_non_credit_treebo,
    booking_bill_parent_info,
    gst_details,
):
    from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate

    bill = Bill(
        bill_id="BIL-12123232334",
        bill_date=datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
        app_id="ths",
        parent_reference_number="19-22323232-2323",
        vendor_id="12345",
        vendor_details=VendorDetails(
            name="Treebo Elmas",
            hotel_id="12345",
            state_code="02",
            gst_details=gst_details,
            phone=phone_no,
            email="<EMAIL>",
            url="www.treebotest.com",
        ),
        version=1,
        parent_info=booking_bill_parent_info,
    )

    payments = [
        payment_in_done_state,
        payment_in_pending_state,
        payment_in_pending_state_treebo,
    ]
    charges = [
        consumed_charge_non_credit,
        consumed_charge_credit_company,
        consumed_charge_non_credit_company,
        consumed_charge_non_credit_treebo,
    ]

    billed_entities = [
        BilledEntity(
            billed_entity_id=1,
            name=Name("BE1"),
            category=BilledEntityCategory.BOOKER_COMPANY,
            accounts=[
                BilledEntityAccount(
                    account_number=1, account_type=ChargeTypes.NON_CREDIT
                ),
                BilledEntityAccount(account_number=2, account_type=ChargeTypes.CREDIT),
            ],
        )
    ]

    return BillAggregate(bill, payments, charges, billed_entities)


@pytest.fixture
def invoice_aggregate(bill_aggregate, address, phone_no, active_hotel_aggregate):
    charge_split_map = {2: [1, 2], 3: [1]}

    generated_by = '2324231'
    generation_channel = '2'
    parent_info = BookingBillParentInfo(
        booking_id='***********',
        reference_number='TEST_BOOKING_REF_1',
        creation_date=dateutils.current_date(),
        checkin_date=datetime.datetime.strptime('21Jun2018', '%d%b%Y'),
        checkout_date=datetime.datetime.strptime('23Jun2018', '%d%b%Y'),
    )

    bill_aggregate.update_parent_info(parent_info)
    bill_to_type = ChargeBillToTypes.COMPANY
    bill_to = InvoiceBillToInfo(
        customer_id='CMR-**********',
        name="ram",
        address=address,
        gstin="GST-34343",
        phone=phone_no,
        email="<EMAIL>",
    )
    user_info_map = dict()
    user_info_map["CST-23232"] = dict(name="Ram")
    user_info_map["CST-23233"] = dict(name="Shyam")

    invoice_date = datetime.datetime.strptime('25Jun2018', '%d%b%Y').date()

    hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
    vendor_details = hotel_context.build_vendor_details()
    issued_by = InvoiceIssuedByInfo(
        gst_details=vendor_details.gst_details,
        phone=vendor_details.phone,
        email=vendor_details.email,
        url=vendor_details.url,
        legal_signature=None,
    )
    return InvoiceFactory.create_preview_invoice(
        bill_aggregate=bill_aggregate,
        charge_split_map=charge_split_map,
        bill_to=bill_to,
        bill_to_type=bill_to_type,
        generated_by=generated_by,
        invoice_date=invoice_date,
        user_info_map=user_info_map,
        generation_channel=generation_channel,
        issued_by_type=IssuedByType.HOTEL,
        issued_to_type=IssuedToType.CUSTOMER,
        issued_by=issued_by,
    )


@pytest.fixture
def invoice_repository():
    return InvoiceRepositoryMock()


@pytest.fixture
def invoice_series_repository():
    return InvoiceSeriesRepositoryMock()


@pytest.fixture
def tenant_settings():
    return locate_instance(TenantSettings)


@pytest.fixture
def cleartax_client(tenant_settings):
    return ClearTaxClient(tenant_settings)


@pytest.fixture
def einvoicing_service(cleartax_client, tenant_settings):
    from prometheus.domain.billing.repositories import (
        CreditNoteRepository,
        InvoiceRepository,
    )

    invoice_repository = InvoiceRepository()
    credit_note_repository = CreditNoteRepository()
    return EInvoicingService(
        cleartax_client, invoice_repository, credit_note_repository, None, None
    )


@pytest.fixture
def tax_service_client():
    return TaxationServiceClientMock()


@pytest.fixture
def catalog_service_client():
    return CatalogServiceClientMock()


@pytest.fixture
def active_hotel_aggregate():
    hotel = Hotel(
        hotel_id="0016932",
        name="Test Hotel",
        area=None,
        status=HotelStatus.ACTIVE,
        legal_name=None,
        checkin_time="12:00:00",
        checkout_time="11:00:00",
        free_late_checkout_time=None,
        legal_address=None,
        gstin_num="29ADFFFADFADFDF",
        brands=[BrandCodes.TREND],
        city=City(city_id="1", name="Test"),
        state=State(state_id="1", name="Test"),
        current_business_date=dateutils.subtract(dateutils.current_date(), days=1),
    )
    hotel_aggregate = HotelAggregate(hotel=hotel, room_type_configs=list())

    from prometheus import crs_context

    crs_context.set_hotel_context(hotel_aggregate)

    print("=======>> Returning hotel_aggregate fixture")
    yield hotel_aggregate

    print("=======>> Teardown hotel_aggregate fixture")
    crs_context.clear()


@pytest.fixture
def valid_booking_aggregate_with_one_room_stay(request, active_hotel_aggregate):
    from prometheus import crs_context

    crs_context.set_hotel_context(active_hotel_aggregate)

    room_stay_id = 1
    room_type_id = 1
    guest_stays = [
        GuestStay(
            1,
            BookingStatus.RESERVED,
            AgeGroup.ADULT,
            today(),
            today_plus_days(5),
            guest_allocations=[
                GuestAllocation('1', '1', None, None, None, is_current=True)
            ],
        )
    ]
    charge_id_map = {
        dateutils.date_to_ymd_str(to_date(today())): 1,
        dateutils.date_to_ymd_str(to_date(today_plus_days(1))): 2,
        dateutils.date_to_ymd_str(to_date(today_plus_days(2))): 4,
        dateutils.date_to_ymd_str(to_date(today_plus_days(3))): 5,
        dateutils.date_to_ymd_str(to_date(today_plus_days(4))): 6,
    }

    room_rents = []
    for d, charge_id in charge_id_map.items():
        room_rents.append(
            RoomRent(
                posttax_amount=Money("1500"),
                applicable_date=dateutils.ymd_str_to_date(d),
            )
        )

    room_stays = [
        RoomStay(
            room_stay_id=room_stay_id,
            room_type_id=room_type_id,
            type=RoomStayType.NIGHT,
            status=BookingStatus.RESERVED,
            checkin_date=today(),
            checkout_date=today_plus_days(5),
            guest_stays=guest_stays,
            charge_id_map=charge_id_map,
            actual_checkin_date=None,
            actual_checkout_date=None,
            room_allocations=[],
            room_rents=room_rents,
            room_rate_plans=[RoomRatePlan(stay_date=None, rate_plan_id='1')],
        )
    ]

    booking_owner = Customer(
        customer_id="1",
        external_ref_id='123444',
        profile_type=None,
        first_name='Treebo',
        last_name='Hotels',
        gender=Genders.MALE,
        age=None,
        address=Address(
            field_1='field1',
            field_2='addr_field2',
            city='addr_city',
            state='addr_state',
            country='India',
            pincode='123098',
        ),
        phone='900000000',
        email='<EMAIL>',
        date_of_birth='02-06-1999',
        user_profile_id='AWER123',
        loyalty_program_details=LoyaltyProgramDetails(
            membership_level="level_1",
            membership_number="123",
            program_name=None,
            current_points_balance=None,
            external_url=None,
            program_start_date=None,
            program_end_date=None,
        ),
        is_vip='TRUE',
    )

    booking = Booking(
        booking_id='123',
        hotel_id=active_hotel_aggregate.hotel.hotel_id,
        type=BookingTypes.ROOM,
        checkin_date=today(),
        checkout_date=today_plus_days(5),
        status=BookingStatus.CONFIRMED,
        source=BookingSource(
            channel_code="hotel", subchannel_code="walk-in", application_code="crs-web"
        ),
        seller_model=SellerType.MARKETPLACE,
        owner_id=booking_owner.customer_id,
        hold_till=None,
        version=1,
        comments=None,
        bill_id=1,
        reference_number=None,
        extra_information=None,
    )
    customers = [booking_owner]
    booking_aggregate = BookingAggregate(booking, room_stays, customers=customers)
    booking_aggregate.check_invariance()

    def clear_threadlocal_context():
        crs_context.clear()

    request.addfinalizer(clear_threadlocal_context)
    return booking_aggregate


@pytest.fixture
def cancelled_booking_aggregate_with_one_room_stay(request, active_hotel_aggregate):
    from prometheus import crs_context

    crs_context.set_hotel_context(active_hotel_aggregate)

    room_stay_id = 1
    room_type_id = 1
    guest_stays = [
        GuestStay(
            1, BookingStatus.CANCELLED, AgeGroup.ADULT, today(), today_plus_days(5)
        )
    ]
    charge_id_map = {
        dateutils.date_to_ymd_str(to_date(today())): 1,
        dateutils.date_to_ymd_str(to_date(today_plus_days(1))): 2,
        dateutils.date_to_ymd_str(to_date(today_plus_days(2))): 4,
        dateutils.date_to_ymd_str(to_date(today_plus_days(3))): 5,
        dateutils.date_to_ymd_str(to_date(today_plus_days(4))): 6,
    }
    room_stays = [
        RoomStay(
            room_stay_id=room_stay_id,
            room_type_id=room_type_id,
            type=RoomStayType.NIGHT,
            status=BookingStatus.CANCELLED,
            checkin_date=today(),
            checkout_date=today_plus_days(5),
            guest_stays=guest_stays,
            charge_id_map=charge_id_map,
            actual_checkin_date=None,
            actual_checkout_date=None,
            room_allocations=[],
        )
    ]

    booking_owner = Customer(
        customer_id="1",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    booking = Booking(
        booking_id='123',
        hotel_id=active_hotel_aggregate.hotel.hotel_id,
        type=BookingTypes.ROOM,
        checkin_date=today(),
        checkout_date=today_plus_days(5),
        status=BookingStatus.CANCELLED,
        source=BookingSource(
            channel_code="hotel", subchannel_code="walk-in", application_code="crs-web"
        ),
        seller_model=SellerType.MARKETPLACE,
        owner_id=booking_owner.customer_id,
        hold_till=None,
        version=1,
        comments=None,
        bill_id=1,
        reference_number=None,
        extra_information=None,
    )
    customers = [booking_owner]
    booking_aggregate = BookingAggregate(booking, room_stays, customers=customers)
    booking_aggregate.check_invariance()

    def clear_threadlocal_context():
        crs_context.clear()

    request.addfinalizer(clear_threadlocal_context)
    return booking_aggregate


@pytest.fixture
def valid_temp_booking_aggregate_with_one_room_stay(request, active_hotel_aggregate):
    from prometheus import crs_context

    crs_context.set_hotel_context(active_hotel_aggregate)

    room_stay_id = 1
    room_type_id = 1
    guest_stays = [
        GuestStay(
            1, BookingStatus.RESERVED, AgeGroup.ADULT, today(), today_plus_days(5)
        )
    ]
    charge_id_map = {
        dateutils.date_to_ymd_str(to_date(today())): 1,
        dateutils.date_to_ymd_str(to_date(today_plus_days(1))): 2,
        dateutils.date_to_ymd_str(to_date(today_plus_days(2))): 4,
        dateutils.date_to_ymd_str(to_date(today_plus_days(3))): 5,
        dateutils.date_to_ymd_str(to_date(today_plus_days(4))): 6,
    }
    room_stays = [
        RoomStay(
            room_stay_id=room_stay_id,
            room_type_id=room_type_id,
            type=RoomStayType.NIGHT,
            status=BookingStatus.RESERVED,
            checkin_date=today(),
            checkout_date=today_plus_days(5),
            guest_stays=guest_stays,
            charge_id_map=charge_id_map,
            actual_checkin_date=None,
            actual_checkout_date=None,
            room_allocations=[],
        )
    ]

    booking_owner = Customer(
        customer_id="1",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    booking = Booking(
        booking_id='123',
        hotel_id=active_hotel_aggregate.hotel.hotel_id,
        type=BookingTypes.ROOM,
        checkin_date=today(),
        checkout_date=today_plus_days(5),
        status=BookingStatus.TEMPORARY,
        source=BookingSource(
            channel_code="hotel", subchannel_code="walk-in", application_code="crs-web"
        ),
        seller_model=SellerType.MARKETPLACE,
        owner_id=booking_owner.customer_id,
        hold_till=None,
        version=1,
        comments=None,
        bill_id=1,
        reference_number=None,
        extra_information=None,
    )
    customers = [booking_owner]
    booking_aggregate = BookingAggregate(booking, room_stays, customers=customers)
    booking_aggregate.check_invariance()

    def clear_threadlocal_context():
        crs_context.clear()

    request.addfinalizer(clear_threadlocal_context)
    return booking_aggregate


@pytest.fixture
def valid_past_booking_aggregate_with_one_room_stay():
    room_stay_id = 1
    room_type_id = 1
    guest_stays = [
        GuestStay(
            1, BookingStatus.RESERVED, AgeGroup.ADULT, yesterday(), today_plus_days(5)
        )
    ]
    charge_id_map = {
        dateutils.date_to_ymd_str(to_date(yesterday())): 1,
        dateutils.date_to_ymd_str(to_date(today())): 2,
        dateutils.date_to_ymd_str(to_date(today_plus_days(1))): 4,
        dateutils.date_to_ymd_str(to_date(today_plus_days(2))): 5,
        dateutils.date_to_ymd_str(to_date(today_plus_days(3))): 6,
    }
    room_stays = [
        RoomStay(
            room_stay_id=room_stay_id,
            room_type_id=room_type_id,
            type=RoomStayType.NIGHT,
            status=BookingStatus.RESERVED,
            checkin_date=yesterday(),
            checkout_date=today_plus_days(5),
            guest_stays=guest_stays,
            charge_id_map=charge_id_map,
            actual_checkin_date=None,
            actual_checkout_date=None,
            room_allocations=[],
        )
    ]

    booking_owner = Customer(
        customer_id=1,
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    booking = Booking(
        booking_id='123',
        hotel_id='1',
        type=BookingTypes.ROOM,
        checkin_date=yesterday(),
        checkout_date=today_plus_days(5),
        status=BookingStatus.CONFIRMED,
        source=BookingSource(
            channel_code="hotel", subchannel_code="walk-in", application_code="crs-web"
        ),
        seller_model=SellerType.MARKETPLACE,
        owner_id=booking_owner.customer_id,
        hold_till=None,
        version=1,
        comments=None,
        bill_id=1,
        reference_number=None,
        extra_information=None,
    )
    customers = [booking_owner]
    booking_aggregate = BookingAggregate(booking, room_stays, customers=customers)
    booking_aggregate.check_invariance()
    return booking_aggregate


@pytest.fixture
def valid_single_day_booking_aggregate_with_one_room_stay(
    request, active_hotel_aggregate
):
    from prometheus import crs_context

    crs_context.set_hotel_context(active_hotel_aggregate)

    room_stay_id = 1
    room_type_id = 1
    guest_stays = [
        GuestStay(
            1, BookingStatus.RESERVED, AgeGroup.ADULT, today(), today_plus_days(1)
        )
    ]
    charge_id_map = {dateutils.date_to_ymd_str(to_date(today())): 1}
    room_stays = [
        RoomStay(
            room_stay_id=room_stay_id,
            room_type_id=room_type_id,
            type=RoomStayType.NIGHT,
            status=BookingStatus.RESERVED,
            checkin_date=today(),
            checkout_date=today_plus_days(1),
            guest_stays=guest_stays,
            charge_id_map=charge_id_map,
            actual_checkin_date=None,
            actual_checkout_date=None,
            room_allocations=[],
        )
    ]

    booking_owner = Customer(
        customer_id="1",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    booking = Booking(
        booking_id='123',
        hotel_id=active_hotel_aggregate.hotel.hotel_id,
        type=BookingTypes.ROOM,
        checkin_date=today(),
        checkout_date=today_plus_days(1),
        status=BookingStatus.CONFIRMED,
        source=BookingSource(
            channel_code="hotel", subchannel_code="walk-in", application_code="crs-web"
        ),
        seller_model=SellerType.MARKETPLACE,
        owner_id=booking_owner.customer_id,
        hold_till=None,
        version=1,
        comments=None,
        bill_id=1,
        reference_number=None,
        extra_information=None,
    )
    customers = [booking_owner]
    booking_aggregate = BookingAggregate(booking, room_stays, customers=customers)
    booking_aggregate.check_invariance()

    def clear_threadlocal_context():
        crs_context.clear()

    request.addfinalizer(clear_threadlocal_context)
    return booking_aggregate


@pytest.fixture
def some_expense_with_valid_guest():
    return {
        "expense_item_id": '123',
        "room_stay_id": 1,
        "status": ExpenseStatus.CONSUMED,
        "comments": "Some comment",
        "guests": ["1"],
        "added_by": "hotel",
        "applicable_date": current_date(),
    }


def id_generator(size=14, chars=string.ascii_uppercase + string.digits):
    return ''.join(random.choice(chars) for _ in range(size))


@pytest.fixture
def create_booking_payload():
    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        payload = {
            "booking_owner": {
                "address": {
                    "city": "string",
                    "country": "string",
                    "field_1": "string",
                    "field_2": "string",
                    "pincode": "string",
                    "state": "string",
                },
                "email": "<EMAIL>",
                "gst_details": {
                    "address": {
                        "city": "string",
                        "country": "string",
                        "field_1": "string",
                        "field_2": "string",
                        "pincode": "string",
                        "state": "string",
                    },
                    "gstin_num": "string",
                    "legal_name": "string",
                },
                "id_proof": {
                    "id_kyc_url": "id_kyc_test",
                    "id_number": "id_number_test",
                    "id_proof_country_code": "id_proof_country_code_test",
                    "id_proof_type": "driving_license",
                },
                "first_name": "string",
                "phone": {"country_code": "string", "number": "string"},
                "profile_type": "individual",
                "reference_id": "string",
            },
            "comments": "string",
            "extra_information": {},
            "guests": [],
            "hotel_id": "0016932",
            "reference_number": id_generator(),
            "room_stays": [
                {
                    "checkin_date": dateutils.current_datetime().isoformat(),
                    "checkout_date": (
                        dateutils.current_datetime() + datetime.timedelta(days=1)
                    ).isoformat(),
                    "guest_stays": [
                        {
                            "age_group": "adult",
                            "guest": {
                                "address": {
                                    "city": "string",
                                    "country": "string",
                                    "field_1": "string",
                                    "field_2": "string",
                                    "pincode": "string",
                                    "state": "string",
                                },
                                "email": "<EMAIL>",
                                "first_name": "guest 1",
                                "phone": {"country_code": "ccode", "number": "phone"},
                                "profile_type": "individual",
                                "reference_id": "guest ref 1",
                            },
                        },
                        {
                            "age_group": "adult",
                            "guest": {
                                "address": {
                                    "city": "string",
                                    "country": "string",
                                    "field_1": "string",
                                    "field_2": "string",
                                    "pincode": "string",
                                    "state": "string",
                                },
                                "email": "<EMAIL>",
                                "first_name": "guest 1",
                                "phone": {"country_code": "ccode", "number": "phone"},
                                "profile_type": "individual",
                                "reference_id": "guest ref 1",
                            },
                        },
                    ],
                    "prices": [
                        {
                            "applicable_date": dateutils.current_datetime().isoformat(),
                            "bill_to_type": "company",
                            "posttax_amount": 118,
                            "type": "non-credit",
                            "rate_plan_id": "1",
                            "rate_plan_reference_id": "014f54aa-60fe-4ff2-b4cc-9b5fd2933815",
                        }
                    ],
                    "room_type_id": "rt02",
                    "rate_plan_reference_id": "014f54aa-60fe-4ff2-b4cc-9b5fd2933815",
                }
            ],
            "source": {
                "application_code": "string",
                "channel_code": "b2b",
                "subchannel_code": "string",
            },
            "status": "reserved",
        }
        parsed_payload = NewBookingSchema().load(payload).data

    return NewBookingDto.create_new(parsed_payload)


@pytest.fixture
def extend_room_stay_payload():
    with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
        payload = {
            "checkout_date": (
                dateutils.current_datetime() + datetime.timedelta(days=2)
            ).isoformat(),
            "prices": {
                "applicable_date": dateutils.current_datetime().isoformat(),
                "bill_to_type": "company",
                "posttax_amount": 118,
                "type": "non-credit",
                "rate_plan_id": "1",
                "rate_plan_reference_id": "014f54aa-60fe-4ff2-b4cc-9b5fd2933815",
            },
        }
        parsed_payload = UpdateRoomStaySchema().load(payload).data
    return parsed_payload


@pytest.fixture
def valid_booking_aggregate_with_one_room_stay_one_night(
    request, active_hotel_aggregate
):
    from prometheus import crs_context

    crs_context.set_hotel_context(active_hotel_aggregate)

    room_stay_id = 1
    room_type_id = 1
    guest_stays = [
        GuestStay(
            1,
            BookingStatus.RESERVED,
            AgeGroup.ADULT,
            today(),
            today_plus_days(1),
            guest_allocations=[
                GuestAllocation('1', '1', None, None, None, is_current=True)
            ],
        )
    ]
    charge_id_map = {
        dateutils.date_to_ymd_str(to_date(today())): 1,
    }

    room_rents = []
    for d, charge_id in charge_id_map.items():
        room_rents.append(
            RoomRent(
                posttax_amount=Money("1500"),
                applicable_date=dateutils.ymd_str_to_date(d),
            )
        )

    room_stays = [
        RoomStay(
            room_stay_id=room_stay_id,
            room_type_id=room_type_id,
            type=RoomStayType.NIGHT,
            status=BookingStatus.RESERVED,
            checkin_date=today(),
            checkout_date=today_plus_days(1),
            guest_stays=guest_stays,
            charge_id_map=charge_id_map,
            actual_checkin_date=None,
            actual_checkout_date=None,
            room_allocations=[],
            room_rents=room_rents,
            room_rate_plans=[RoomRatePlan(stay_date=None, rate_plan_id='1')],
        )
    ]

    booking_owner = Customer(
        customer_id="1",
        external_ref_id='123444',
        profile_type=None,
        first_name='Treebo',
        last_name='Hotels',
        gender=Genders.MALE,
        age=None,
        address=Address(
            field_1='field1',
            field_2='addr_field2',
            city='addr_city',
            state='addr_state',
            country='India',
            pincode='123098',
        ),
        phone='900000000',
        email='<EMAIL>',
        date_of_birth='02-06-1999',
        user_profile_id='AWER123',
        loyalty_program_details=LoyaltyProgramDetails(
            membership_level="level_1",
            membership_number="123",
            program_name=None,
            current_points_balance=None,
            external_url=None,
            program_start_date=None,
            program_end_date=None,
        ),
        is_vip='TRUE',
    )

    booking = Booking(
        booking_id='123',
        hotel_id=active_hotel_aggregate.hotel.hotel_id,
        type=BookingTypes.ROOM,
        checkin_date=today(),
        checkout_date=today_plus_days(1),
        status=BookingStatus.CONFIRMED,
        source=BookingSource(
            channel_code="hotel", subchannel_code="walk-in", application_code="crs-web"
        ),
        seller_model=SellerType.MARKETPLACE,
        owner_id=booking_owner.customer_id,
        hold_till=None,
        version=1,
        comments=None,
        bill_id=1,
        reference_number=None,
        extra_information=None,
    )
    customers = [booking_owner]
    booking_aggregate = BookingAggregate(booking, room_stays, customers=customers)
    booking_aggregate.check_invariance()

    def clear_threadlocal_context():
        crs_context.clear()

    request.addfinalizer(clear_threadlocal_context)
    return booking_aggregate
