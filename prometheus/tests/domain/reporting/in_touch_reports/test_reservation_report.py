from prometheus.reporting.in_touch_reports.reservation_report.reservation_report_generator import (
    InTouchReservationReportGenerator,
)


def test_reservation_report(
    valid_booking_aggregate_with_one_room_stay, active_hotel_aggregate, bill_aggregate
):
    reservation_report = InTouchReservationReportGenerator(
        valid_booking_aggregate_with_one_room_stay,
        bill_aggregate,
        active_hotel_aggregate,
        'RTO4',
    ).generate()[0]
    for room_stay in valid_booking_aggregate_with_one_room_stay.room_stays:
        assert reservation_report.hotel_code == active_hotel_aggregate.hotel_id
        assert (
            reservation_report.confirmation_number
            == valid_booking_aggregate_with_one_room_stay.booking.reference_number
        )
        assert reservation_report.hotel_name == active_hotel_aggregate.hotel.name
        assert (
            reservation_report.pms_visit_id
            == f"{reservation_report.booking_id}_{room_stay.room_stay_id}"
        )
        assert (
            reservation_report.cancellation_date
            == valid_booking_aggregate_with_one_room_stay.booking.cancellation_datetime
        )
        assert (
            reservation_report.channel_code
            == valid_booking_aggregate_with_one_room_stay.booking.source.channel_id
        )

        room_type_id = (
            room_stay.room_allocation.room_type_id
            if room_stay.room_allocation
            else None
        )
        assert reservation_report.room_type == room_type_id

        pms_company_id = (
            valid_booking_aggregate_with_one_room_stay.booking.company_details.legal_details.client_internal_code
            if (valid_booking_aggregate_with_one_room_stay.booking.company_details)
            else None
        )
        assert reservation_report.pms_company_id == pms_company_id

        pms_travel_agent_id = (
            valid_booking_aggregate_with_one_room_stay.booking.travel_agent_details.legal_details.client_internal_code
            if (valid_booking_aggregate_with_one_room_stay.booking.travel_agent_details)
            else None
        )
        assert reservation_report.pms_travel_agent_id == pms_travel_agent_id

        reservation_report_total_guest_count = (
            reservation_report.children + reservation_report.adults
        )
        (
            adult_count,
            child_count,
        ) = valid_booking_aggregate_with_one_room_stay.get_adult_and_child_count()
        assert reservation_report_total_guest_count == adult_count + child_count

        room_stay_charge_ids = (
            valid_booking_aggregate_with_one_room_stay.get_room_stay_charges(
                [room_stay.room_stay_id]
            )
        )

        total_room_revenue_charges_posttax_amount = sum(
            [
                c.get_posttax_amount_post_allowance()
                for c in bill_aggregate.get_charges(room_stay_charge_ids)
            ]
        )
        assert (
            reservation_report.room_revenue == total_room_revenue_charges_posttax_amount
        )
