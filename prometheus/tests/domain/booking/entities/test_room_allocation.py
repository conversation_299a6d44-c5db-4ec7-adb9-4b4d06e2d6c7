import pytest
from treebo_commons.utils.dateutils import to_date

from prometheus import crs_context
from prometheus.domain.booking.entities.room_allocation import RoomAllocation
from prometheus.tests.test_utils import today, today_plus_days


@pytest.fixture
def room_allocation_for_two_days():
    room_id = 1
    room_stay_id = 1
    room_type_id = 'oak'
    room_allocation_id = 1
    room_allocation = RoomAllocation(
        room_allocation_id,
        room_stay_id,
        room_id,
        room_type_id,
        None,
        today(),
        today_plus_days(2),
        room_no=501,
        is_current=True,
    )
    return room_allocation


def test_room_allocation_stay_start(
    active_hotel_aggregate, room_allocation_for_two_days
):
    hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
    expected_stay_start = hotel_context.hotel_checkin_date(
        room_allocation_for_two_days.checkin_date
    )
    assert room_allocation_for_two_days.stay_start == expected_stay_start
