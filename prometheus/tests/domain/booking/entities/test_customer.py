import pytest

from prometheus.domain.booking.entities.customer import Customer
from ths_common.constants.booking_constants import Genders, IDProofType
from ths_common.value_objects import Address, GSTDetails, IDProof, Name, PhoneNumber


@pytest.fixture
def id_proof():
    return IDProof(
        id_proof_type=IDProofType.PASSPORT,
        id_number="232",
        id_kyc_url="http://treebo.com/kyc/url",
        id_proof_country_code="91",
    )


@pytest.fixture
def customer(address, gst_details, phone_no):
    return Customer(
        customer_id="1",
        external_ref_id="1000",
        profile_type=None,
        first_name="fname",
        last_name="lname",
        gender=Genders.MALE,
        age="20",
        address=address,
        phone=phone_no,
        email=None,
        nationality="Indian",
        gst_details=gst_details,
        id_proof=None,
        dummy=True,
        dirty=False,
        new=False,
    )


def test_customer_name(customer):
    assert str(customer.name) == str(
        Name(first_name=customer.first_name, last_name=customer.last_name)
    )


def test_update_first_name(customer):
    new_first_name = 'new_fname'
    customer.update_first_name(new_first_name)
    assert customer.first_name == new_first_name


def test_update_last_name(customer):
    new_last_name = 'new_lname'
    customer.update_last_name(new_last_name)
    assert customer.last_name == new_last_name


def test_update_phone(customer):
    new_phone = PhoneNumber('123456789')
    customer.update_phone(new_phone)
    assert customer.phone == new_phone


def test_update_email(customer):
    new_email = '<EMAIL>'
    customer.update_email(new_email)
    assert customer.email == new_email


def test_update_gender(customer):
    new_gender = Genders.FEMALE
    customer.update_gender(new_gender)
    assert customer.gender == new_gender


def test_update_age(customer):
    new_age = '23'
    customer.update_age(new_age)
    assert customer.age == new_age


def test_update_address(customer):
    new_address = Address(
        "new field1", "new field2", "new city", "new state", "new country", "989288"
    )
    customer.update_address(new_address)
    assert str(customer.address) == str(new_address)


def test_update_nationality(customer):
    new_nationality = "Canadian"
    customer.update_nationality(new_nationality)
    assert customer.nationality == new_nationality


def test_update_id_proof(customer, id_proof):
    new_id_proof = id_proof
    customer.update_id_proof(new_id_proof)
    assert str(customer.id_proof) == str(new_id_proof)


def test_update_image_url(customer):
    new_image_url = "http://treebo.com/favicon.png"
    customer.update_image_url(new_image_url)
    assert customer.image_url == new_image_url


def test_update_reference_id(customer):
    new_reference_id = "1001"
    customer.update_reference_id(new_reference_id)
    assert customer.external_ref_id == new_reference_id


def test_update_gst_details(customer, address):
    new_gst_details = GSTDetails(
        legal_name="random string", gstin_num='1000300', address=address
    )
    customer.update_gst_details(new_gst_details)
    assert customer.gst_details == new_gst_details


def test_mark_deleted(customer):
    customer.mark_deleted()
    assert customer.deleted


def test_update_same_first_name(customer: Customer):
    customer.update_first_name(customer.first_name)
    assert not customer.is_dirty()
    assert customer.dummy


def test_update_same_last_name(customer: Customer):
    customer.update_last_name(customer.last_name)
    assert not customer.is_dirty()
    assert customer.dummy


def test_update_same_phone(customer: Customer):
    customer.update_phone(customer.phone)
    assert not customer.is_dirty()


def test_update_same_email(customer: Customer):
    customer.update_email(customer.email)
    assert not customer.is_dirty()


def test_update_same_gender(customer: Customer):
    customer.update_gender(customer.gender)
    assert not customer.is_dirty()


def test_update_same_age(customer: Customer):
    customer.update_age(customer.age)
    assert not customer.is_dirty()


def test_update_same_address(customer: Customer):
    customer.update_address(customer.address)
    assert not customer.is_dirty()


def test_update_same_nationality(customer: Customer):
    customer.update_nationality(customer.nationality)
    assert not customer.is_dirty()


def test_update_same_id_proof(customer: Customer, id_proof):
    customer.update_id_proof(customer.id_proof)
    assert not customer.is_dirty()


def test_update_same_image_url(customer: Customer):
    customer.update_image_url(customer.image_url)
    assert not customer.is_dirty()


def test_update_same_reference_id(customer: Customer):
    customer.update_reference_id(customer.reference_id)
    assert not customer.is_dirty()


def test_update_same_gst_details(customer: Customer, address):
    customer.update_gst_details(customer.gst_details)
    assert not customer.is_dirty()
