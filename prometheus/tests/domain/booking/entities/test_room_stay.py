import pytest
from treebo_commons.money.money import Money
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import date_range, to_date

from prometheus import crs_context
from prometheus.domain.booking.dtos.guest_checkout_data import GuestCheckoutRequestData
from prometheus.domain.booking.dtos.guest_stay_data import GuestStayData
from prometheus.domain.booking.dtos.room_allocation_data import RoomAllocationData
from prometheus.domain.booking.entities.guest_allocation import GuestAllocation
from prometheus.domain.booking.entities.guest_stay import GuestStay
from prometheus.domain.booking.entities.room_allocation import RoomAllocation
from prometheus.domain.booking.entities.room_stay import RoomStay
from prometheus.domain.booking.exceptions import BookingUpdateError, InvalidActionError
from prometheus.domain.catalog.entities.room import Room
from prometheus.itests.helpers import roll_over_business_date
from prometheus.tests.test_utils import (
    items_equal,
    today,
    today_minus_days,
    today_plus_days,
)
from ths_common.constants.booking_constants import AgeGroup, BookingStatus, RoomStayType
from ths_common.constants.catalog_constants import RoomStatus
from ths_common.exceptions import InvalidOperationError
from ths_common.value_objects import (
    GuestAllocationSideEffect,
    GuestStaySideEffect,
    RoomRent,
    RoomStayConfig,
)


@pytest.fixture
def reserved_five_day_guest_stay_with_stay_starting_today(
    request, active_hotel_aggregate
):
    active_hotel_aggregate.hotel.current_business_date = dateutils.current_date()
    crs_context.set_hotel_context(active_hotel_aggregate)
    checkin_date = today()
    checkout_date = today_plus_days(4)
    guest_allocation = GuestAllocation(
        1, 1, None, checkin_date, checkout_date, is_current=True
    )

    def clear_threadlocal_context():
        crs_context.clear()

    request.addfinalizer(clear_threadlocal_context)
    return GuestStay(
        1,
        BookingStatus.RESERVED,
        AgeGroup.ADULT,
        checkin_date,
        checkout_date,
        guest_allocations=[guest_allocation],
    )


@pytest.fixture
def reserved_five_day_guest_stay_with_stay_starting_yesterday(active_hotel_aggregate):
    crs_context.set_hotel_context(active_hotel_aggregate)
    checkin_date = today_minus_days(1)
    checkout_date = today_plus_days(3)
    guest_allocation = GuestAllocation(
        1, 1, None, checkin_date, checkout_date, is_current=True
    )
    return GuestStay(
        1,
        BookingStatus.RESERVED,
        AgeGroup.ADULT,
        checkin_date,
        checkout_date,
        guest_allocations=[guest_allocation],
    )


@pytest.fixture
def reserved_room_stay_without_guest_stay(request, active_hotel_aggregate):
    crs_context.set_hotel_context(active_hotel_aggregate)
    hotel_context = crs_context.get_hotel_context()
    room_stay_id = 1
    room_type_id = 'oak'
    charge_id_map = {
        dateutils.date_to_ymd_str(to_date(today())): 1,
        dateutils.date_to_ymd_str(to_date(today_plus_days(1))): 2,
        dateutils.date_to_ymd_str(to_date(today_plus_days(2))): 4,
        dateutils.date_to_ymd_str(to_date(today_plus_days(3))): 5,
        dateutils.date_to_ymd_str(to_date(today_plus_days(4))): 6,
    }
    room_rents = []
    for d, charge_id in charge_id_map.items():
        room_rents.append(
            RoomRent(
                posttax_amount=Money("1500"),
                applicable_date=dateutils.ymd_str_to_date(d),
            )
        )
    room_stay = RoomStay(
        room_stay_id=room_stay_id,
        room_type_id=room_type_id,
        type=RoomStayType.NIGHT,
        status=BookingStatus.RESERVED,
        checkin_date=dateutils.datetime_at_given_time(
            today(), hotel_context.checkin_time
        ),
        checkout_date=dateutils.datetime_at_given_time(
            today_plus_days(5), hotel_context.checkout_time
        ),
        guest_stays=None,
        charge_id_map=charge_id_map,
        actual_checkin_date=None,
        actual_checkout_date=None,
        room_allocations=[],
        room_rents=room_rents,
    )

    def clear_threadlocal_context():
        crs_context.clear()

    request.addfinalizer(clear_threadlocal_context)
    return room_stay


@pytest.fixture
def reserved_room_stay_with_one_guest_stay_starting_today(
    reserved_five_day_guest_stay_with_stay_starting_today,
):
    room_stay_id = 1
    room_type_id = 'oak'
    charge_id_map = {
        dateutils.date_to_ymd_str(to_date(today_minus_days(1))): 1,
        dateutils.date_to_ymd_str(to_date(today())): 2,
        dateutils.date_to_ymd_str(to_date(today_plus_days(1))): 4,
        dateutils.date_to_ymd_str(to_date(today_plus_days(2))): 5,
        dateutils.date_to_ymd_str(to_date(today_plus_days(3))): 6,
    }
    room_rents = []
    for d, charge_id in charge_id_map.items():
        room_rents.append(
            RoomRent(
                posttax_amount=Money("1500"),
                applicable_date=dateutils.ymd_str_to_date(d),
            )
        )
    room_stay = RoomStay(
        room_stay_id=room_stay_id,
        room_type_id=room_type_id,
        type=RoomStayType.NIGHT,
        status=BookingStatus.RESERVED,
        checkin_date=today_minus_days(1),
        checkout_date=today_plus_days(3),
        guest_stays=[reserved_five_day_guest_stay_with_stay_starting_today],
        charge_id_map=charge_id_map,
        actual_checkin_date=None,
        actual_checkout_date=None,
        room_allocations=[],
        room_rents=room_rents,
    )

    return room_stay


@pytest.fixture
def reserved_room_stay_with_one_guest_stay_starting_yesterday(
    reserved_five_day_guest_stay_with_stay_starting_yesterday,
):
    room_stay_id = 1
    room_type_id = 'oak'
    checkin_date = (
        reserved_five_day_guest_stay_with_stay_starting_yesterday.checkin_date
    )
    checkout_date = (
        reserved_five_day_guest_stay_with_stay_starting_yesterday.checkout_date
    )
    charge_id_map = {
        dateutils.date_to_ymd_str(to_date(today_minus_days(1))): 1,
        dateutils.date_to_ymd_str(to_date(today())): 2,
        dateutils.date_to_ymd_str(to_date(today_plus_days(1))): 4,
        dateutils.date_to_ymd_str(to_date(today_plus_days(2))): 5,
        dateutils.date_to_ymd_str(to_date(today_plus_days(3))): 6,
    }
    room_rents = []
    for d, charge_id in charge_id_map.items():
        room_rents.append(
            RoomRent(
                posttax_amount=Money("1500"),
                applicable_date=dateutils.ymd_str_to_date(d),
            )
        )
    room_stay = RoomStay(
        room_stay_id=room_stay_id,
        room_type_id=room_type_id,
        type=RoomStayType.NIGHT,
        status=BookingStatus.RESERVED,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        guest_stays=[reserved_five_day_guest_stay_with_stay_starting_yesterday],
        charge_id_map=charge_id_map,
        actual_checkin_date=None,
        actual_checkout_date=None,
        room_allocations=[],
        room_rents=room_rents,
    )

    return room_stay


@pytest.fixture
def checked_in_room_stay_with_one_guest_stay(
    reserved_five_day_guest_stay_with_stay_starting_today,
):
    room_stay_id = 1
    room_id = 1
    guest_id = 1
    room_type_id = 'oak'
    checkin_date = reserved_five_day_guest_stay_with_stay_starting_today.checkin_date
    checkout_date = reserved_five_day_guest_stay_with_stay_starting_today.checkout_date
    reserved_five_day_guest_stay_with_stay_starting_today.perform_checkin(
        guest_id, checkin_date
    )
    guest_stays = [reserved_five_day_guest_stay_with_stay_starting_today]
    charge_id_map = {
        dateutils.date_to_ymd_str(to_date(today())): 1,
        dateutils.date_to_ymd_str(to_date(today_plus_days(1))): 2,
        dateutils.date_to_ymd_str(to_date(today_plus_days(2))): 3,
        dateutils.date_to_ymd_str(to_date(today_plus_days(3))): 4,
    }
    room_rents = []
    for d, charge_id in charge_id_map.items():
        room_rents.append(
            RoomRent(
                posttax_amount=Money("1500"),
                applicable_date=dateutils.ymd_str_to_date(d),
            )
        )

    actual_checkin_date = today()
    room_allocation = RoomAllocation(
        1, room_stay_id, room_id, room_type_id, None, checkin_date, checkout_date
    )
    room_stay = RoomStay(
        room_stay_id=room_stay_id,
        room_type_id=room_type_id,
        type=RoomStayType.NIGHT,
        status=BookingStatus.CHECKED_IN,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        guest_stays=guest_stays,
        charge_id_map=charge_id_map,
        actual_checkin_date=actual_checkin_date,
        actual_checkout_date=None,
        room_allocations=[room_allocation],
        room_rents=room_rents,
    )
    return room_stay


@pytest.fixture
def checked_out_room_stay_with_one_guest_stay(checked_in_room_stay_with_one_guest_stay):
    guest_stay = checked_in_room_stay_with_one_guest_stay.active_guest_stays()[0]
    checkout_datetime = guest_stay.checkout_date
    guest_checkout_request = GuestCheckoutRequestData(
        guest_stay.guest_stay_id, guest_stay.guest_id, checkout_datetime
    )
    checked_in_room_stay_with_one_guest_stay.checkout_guests(
        [guest_checkout_request], checkout_datetime
    )
    return checked_in_room_stay_with_one_guest_stay


def test_room_stay_stay_start(reserved_room_stay_without_guest_stay):
    assert reserved_room_stay_without_guest_stay.stay_start == to_date(today())


def test_room_stay_fetch_date_wise_occupancies(
    checked_in_room_stay_with_one_guest_stay,
):
    date_wise_occupancy = checked_in_room_stay_with_one_guest_stay.date_wise_occupancies
    for i in range(0, 4):
        assert date_wise_occupancy[to_date(today_plus_days(i))].adult == 1


def test_check_room_stay_is_fully_checked_in(checked_in_room_stay_with_one_guest_stay):
    assert checked_in_room_stay_with_one_guest_stay.is_fully_checked_in()


def test_room_stay_mark_noshow(
    reserved_room_stay_with_one_guest_stay_starting_yesterday,
):
    reserved_room_stay_with_one_guest_stay_starting_yesterday.mark_noshow()
    assert (
        reserved_room_stay_with_one_guest_stay_starting_yesterday.status
        == BookingStatus.NOSHOW
    )
    assert (
        len(
            reserved_room_stay_with_one_guest_stay_starting_yesterday.active_guest_stays()
        )
        == 0
    )


def test_room_stay_mark_noshow_on_last_guest_stay_should_throw_InvalidActionError(
    reserved_room_stay_with_one_guest_stay_starting_yesterday,
):
    guest_stay = (
        reserved_room_stay_with_one_guest_stay_starting_yesterday.active_guest_stays()[
            0
        ]
    )
    with pytest.raises(InvalidActionError):
        reserved_room_stay_with_one_guest_stay_starting_yesterday.mark_guest_stay_noshow(
            guest_stay.guest_stay_id
        )


def test_room_stay_mark_guest_stay_noshow(
    reserved_room_stay_with_one_guest_stay_starting_yesterday,
):
    guest_stay = (
        reserved_room_stay_with_one_guest_stay_starting_yesterday.active_guest_stays()[
            0
        ]
    )
    guest_stay_data = GuestStayData(
        BookingStatus.RESERVED,
        AgeGroup.ADULT,
        guest_stay.checkin_date,
        guest_stay.checkout_date,
    )
    reserved_room_stay_with_one_guest_stay_starting_yesterday.add_guest_stay(
        guest_stay_data
    )
    reserved_room_stay_with_one_guest_stay_starting_yesterday.mark_guest_stay_noshow(
        guest_stay.guest_stay_id
    )


def test_room_stay_update_charges_with_charges_to_delete(
    checked_out_room_stay_with_one_guest_stay,
):
    updated_charge_id_map = checked_out_room_stay_with_one_guest_stay.charge_id_map
    dt = to_date(today()).isoformat()
    updated_charge_id_map[dt] = 6
    checked_out_room_stay_with_one_guest_stay.update_charge_id_map(
        updated_charge_id_map, [1]
    )
    assert checked_out_room_stay_with_one_guest_stay.charge_id_map[dt] == 6


def test_room_stay_change_stay_dates(checked_out_room_stay_with_one_guest_stay):
    new_checkin_date = today_plus_days(0)
    new_checkout_date = today_plus_days(4)
    with pytest.raises(BookingUpdateError):
        checked_out_room_stay_with_one_guest_stay.change_stay_dates(
            new_checkin_date, new_checkout_date
        )


def test_refresh_room_stay_checkin_checkout_dates(
    checked_out_room_stay_with_one_guest_stay,
):
    new_checkin_date = to_date(today_plus_days(2))
    new_checkout_date = to_date(today_plus_days(7))
    guest_stay = checked_out_room_stay_with_one_guest_stay.active_guest_stays()[0]
    guest_stay.checkin_date = new_checkin_date
    guest_stay.checkout_date = new_checkout_date
    checked_out_room_stay_with_one_guest_stay.refresh_room_stay_checkin_checkout_dates()
    assert checked_out_room_stay_with_one_guest_stay.checkin_date == new_checkin_date
    assert checked_out_room_stay_with_one_guest_stay.checkout_date == new_checkout_date


def test_room_stay_update_room_type_on_inactive_room_stay_should_throw_InvalidOperationError(
    reserved_room_stay_without_guest_stay,
):
    new_room_type_id = 'maple'
    reserved_room_stay_without_guest_stay.mark_cancel()
    with pytest.raises(InvalidOperationError):
        reserved_room_stay_without_guest_stay.update_room_type(new_room_type_id)


def test_room_stay_update_room_type_with_new_room_type_without_room_allocation_should_throw_BookingUpdateError(
    checked_in_room_stay_with_one_guest_stay,
):
    new_room_type_id = 'maple'
    with pytest.raises(BookingUpdateError):
        checked_in_room_stay_with_one_guest_stay.update_room_type(new_room_type_id)


def test_room_stay_update_room_type(checked_out_room_stay_with_one_guest_stay):
    new_room_type_id = 'maple'
    guest_stay = checked_out_room_stay_with_one_guest_stay.active_guest_stays()[0]
    room = Room(
        room_id=1,
        hotel_id='1',
        room_type_id=new_room_type_id,
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=guest_stay.checkin_date
    )
    checked_out_room_stay_with_one_guest_stay.update_room_type(
        new_room_type_id, room_allocation_data
    )
    assert checked_out_room_stay_with_one_guest_stay.room_type_id == new_room_type_id


def test_room_stay_get_charges_for_stay_dates(
    checked_out_room_stay_with_one_guest_stay,
):
    charge_id_map = checked_out_room_stay_with_one_guest_stay.charge_id_map
    charges = list(charge_id_map.values())
    dates = [
        dateutils.to_date(dateutils.ymd_str_to_date(d)) for d in charge_id_map.keys()
    ]
    assert (
        charges
        == checked_out_room_stay_with_one_guest_stay.get_charges_for_stay_dates(dates)
    )


class TestRoomStayActionReversal(object):
    def test_room_stay_part_checkin_reversal(self, room_stay_part_checked_in):
        assert room_stay_part_checked_in.status == BookingStatus.PART_CHECKIN
        gs_1_checkin_data = GuestStaySideEffect(
            1, GuestAllocationSideEffect(guest_allocation_id=1, is_new=True)
        )
        room_stay_part_checked_in.undo_checkin_guests([gs_1_checkin_data])
        assert room_stay_part_checked_in.status == BookingStatus.RESERVED
        for guest_stay in room_stay_part_checked_in.guest_stays:
            assert guest_stay.status == BookingStatus.RESERVED
            assert guest_stay.guest_allocation is None
        assert room_stay_part_checked_in.actual_checkin_date is None
        assert room_stay_part_checked_in.room_allocation is None
        for room_allocation in room_stay_part_checked_in.room_allocation_history:
            assert room_allocation.deleted is True

    def test_room_stay_checkin_reversal(self, room_stay_checked_in):
        assert room_stay_checked_in.status == BookingStatus.CHECKED_IN

        gs_1_checkin_data = GuestStaySideEffect(
            1, GuestAllocationSideEffect(guest_allocation_id=1, is_new=True)
        )
        gs_2_checkin_data = GuestStaySideEffect(
            2, GuestAllocationSideEffect(guest_allocation_id=1, is_new=True)
        )
        room_stay_checked_in.undo_checkin_guests([gs_1_checkin_data, gs_2_checkin_data])
        assert room_stay_checked_in.status == BookingStatus.RESERVED
        for guest_stay in room_stay_checked_in.guest_stays:
            assert guest_stay.status == BookingStatus.RESERVED
            assert guest_stay.guest_allocation is None
        assert room_stay_checked_in.actual_checkin_date is None
        assert room_stay_checked_in.room_allocation is None
        for room_allocation in room_stay_checked_in._room_allocation_history:
            assert room_allocation.deleted is True
            assert room_allocation.is_current is False

    def test_room_stay_checkin_reversal_of_part_checkin(
        self, room_stay_checked_in_two_steps
    ):
        assert room_stay_checked_in_two_steps.status == BookingStatus.CHECKED_IN
        gs_2_checkin_data = GuestStaySideEffect(
            2, GuestAllocationSideEffect(guest_allocation_id=1, is_new=True)
        )
        room_stay_checked_in_two_steps.undo_checkin_guests([gs_2_checkin_data])
        assert room_stay_checked_in_two_steps.status == BookingStatus.PART_CHECKIN
        for guest_stay in room_stay_checked_in_two_steps.guest_stays:
            if guest_stay.guest_stay_id == 1:
                assert guest_stay.status == BookingStatus.CHECKED_IN
                assert guest_stay.guest_allocation is not None
                assert guest_stay.guest_allocation.deleted is False
                assert guest_stay.guest_allocation.is_current is True
            elif guest_stay.guest_stay_id == 2:
                assert guest_stay.status == BookingStatus.RESERVED
                assert guest_stay.guest_allocation is None
        assert (
            room_stay_checked_in_two_steps.actual_checkin_date
            == room_stay_checked_in_two_steps.get_guest_stay(1).actual_checkin_date
        )
        assert room_stay_checked_in_two_steps.room_allocation is not None
        assert room_stay_checked_in_two_steps.room_allocation.deleted is False
        assert room_stay_checked_in_two_steps.room_allocation.is_current is True
        for room_allocation in room_stay_checked_in_two_steps.room_allocation_history:
            assert room_allocation.deleted is False

    def test_room_stay_part_checkout_reversal(self, room_stay_part_checkedout, guest):
        assert room_stay_part_checkedout.status == BookingStatus.PART_CHECKOUT
        checkout_datetime = dateutils.today()
        gs_1_checkout_data = GuestCheckoutRequestData(
            guest_stay_id=1,
            guest_id=guest.customer_id,
            checkout_datetime=checkout_datetime,
        )
        room_stay_part_checkedout.undo_checkout_guests([gs_1_checkout_data])
        assert room_stay_part_checkedout.status == BookingStatus.CHECKED_IN
        for guest_stay in room_stay_part_checkedout.guest_stays:
            guest_stay.status == BookingStatus.CHECKED_IN
        assert room_stay_part_checkedout.actual_checkout_date is None

    def test_room_stay_checkout_reversal(self, room_stay_checkedout, guest, guest_two):
        assert room_stay_checkedout.status == BookingStatus.CHECKED_OUT
        checkout_datetime = dateutils.today()
        gs_1_checkout_data = GuestCheckoutRequestData(
            guest_stay_id=1,
            guest_id=guest.customer_id,
            checkout_datetime=checkout_datetime,
        )
        gs_2_checkout_data = GuestCheckoutRequestData(
            guest_stay_id=2,
            guest_id=guest_two.customer_id,
            checkout_datetime=checkout_datetime,
        )
        room_stay_checkedout.undo_checkout_guests(
            [gs_1_checkout_data, gs_2_checkout_data]
        )
        assert room_stay_checkedout.status == BookingStatus.CHECKED_IN
        for guest_stay in room_stay_checkedout.guest_stays:
            guest_stay.status == BookingStatus.CHECKED_IN
        assert room_stay_checkedout.actual_checkout_date is None

    def test_room_stay_checkout_reversal_of_part_checkout(
        self, room_stay_checked_out_two_steps, guest_two
    ):
        assert room_stay_checked_out_two_steps.status == BookingStatus.CHECKED_OUT
        checkout_datetime = dateutils.today()
        gs_2_checkout_data = GuestCheckoutRequestData(
            guest_stay_id=2,
            guest_id=guest_two.customer_id,
            checkout_datetime=checkout_datetime,
        )
        room_stay_checked_out_two_steps.undo_checkout_guests([gs_2_checkout_data])
        assert room_stay_checked_out_two_steps.status == BookingStatus.PART_CHECKOUT
        for guest_stay in room_stay_checked_out_two_steps.guest_stays:
            if guest_stay.guest_stay_id == 1:
                assert guest_stay.status == BookingStatus.CHECKED_OUT
            elif guest_stay.guest_stay_id == 2:
                assert guest_stay.status == BookingStatus.CHECKED_IN
        assert room_stay_checked_out_two_steps.actual_checkout_date == None
        assert room_stay_checked_out_two_steps.room_allocation.deleted is not True
        assert room_stay_checked_out_two_steps.room_allocation.checkout_date == None

    def test_room_stay_cancel_reversal(self, room_stay_canceled_in_two_steps):
        assert room_stay_canceled_in_two_steps.status == BookingStatus.CANCELLED
        room_stay_canceled_in_two_steps.undo_mark_cancel([GuestStaySideEffect(2)], True)
        assert room_stay_canceled_in_two_steps.status == BookingStatus.RESERVED
        assert (
            room_stay_canceled_in_two_steps.get_guest_stay(2).status
            == BookingStatus.RESERVED
        )
        assert (
            room_stay_canceled_in_two_steps.get_guest_stay(1).status
            == BookingStatus.CANCELLED
        )

        with pytest.raises(InvalidOperationError):
            room_stay_canceled_in_two_steps.undo_mark_cancel([2], True)

    def test_room_stay_noshow_reversal(self, room_stay_no_showed_in_two_steps):
        assert room_stay_no_showed_in_two_steps.status == BookingStatus.NOSHOW
        guest_stay_side_effect = GuestStaySideEffect(2)
        room_stay_no_showed_in_two_steps.undo_mark_noshow([guest_stay_side_effect])
        assert room_stay_no_showed_in_two_steps.status == BookingStatus.RESERVED
        assert (
            room_stay_no_showed_in_two_steps.get_guest_stay(2).status
            == BookingStatus.RESERVED
        )
        assert (
            room_stay_no_showed_in_two_steps.get_guest_stay(1).status
            == BookingStatus.NOSHOW
        )

        with pytest.raises(InvalidOperationError):
            room_stay_no_showed_in_two_steps.undo_mark_noshow([dict(guest_stay_id=2)])
