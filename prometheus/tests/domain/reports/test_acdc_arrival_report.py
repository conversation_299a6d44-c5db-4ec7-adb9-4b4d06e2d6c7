from prometheus.reporting.acdc_arrival_list_report.acdc_arrival_list_generator import (
    ACDCArrivalListReportGenerator,
)
from prometheus.tests.mockers import mock_tenant_sftp_client


def test_acdc_arrival_list(
    valid_booking_aggregate_with_one_room_stay, active_hotel_aggregate, bill_aggregate
):
    acdc_arrival_list_report = ACDCArrivalListReportGenerator(
        valid_booking_aggregate_with_one_room_stay,
        bill_aggregate,
        active_hotel_aggregate,
        valid_booking_aggregate_with_one_room_stay.room_stays,
        False,
    ).generate()[0]
    with mock_tenant_sftp_client():
        for room_stay in valid_booking_aggregate_with_one_room_stay.room_stays:
            customer = valid_booking_aggregate_with_one_room_stay.customers[0]
            assert acdc_arrival_list_report.GUES_NAME == ' '.join(
                filter(None, (customer.first_name, customer.last_name))
            )
            assert acdc_arrival_list_report.COUNTRY == 'IN'
            assert acdc_arrival_list_report.RATE == None
            assert getattr(acdc_arrival_list_report, 'E-mail') == customer.email
            assert acdc_arrival_list_report.FIRST_NAME == customer.first_name
            assert acdc_arrival_list_report.LAST_NAME == customer.last_name
            assert (
                acdc_arrival_list_report.RESERVATION_NUMBER
                == valid_booking_aggregate_with_one_room_stay.booking.booking_id
            )
            assert (
                acdc_arrival_list_report.TOTAL_AMOUNT
                == bill_aggregate.total_posttax_amount().amount
            )
            assert (
                acdc_arrival_list_report.DEPOSIT
                == bill_aggregate.net_paid_amount.amount
            )
