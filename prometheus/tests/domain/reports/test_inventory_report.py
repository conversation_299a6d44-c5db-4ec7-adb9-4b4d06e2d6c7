import pytest

from prometheus.reporting.in_touch_reports.inventory_report.inventory_report_generator import (
    InTouchInventoryReportGenerator,
)


@pytest.fixture
def housekeeping_room_status_count():
    return {'out_of_order_rooms': 1, 'out_of_service_rooms': 2}


def test_inventory_report(housekeeping_room_status_count):
    total_rooms_count = 20
    date = '2021-11-02'
    rooms_inventory_count = 2
    room_type_id = 'RT03'
    inventory_report = InTouchInventoryReportGenerator(
        rooms_inventory_count,
        room_type_id,
        date,
        housekeeping_room_status_count,
        total_rooms_count,
        1,
    ).generate()[0]

    assert inventory_report.room_type == room_type_id
    assert inventory_report.date == date
    assert inventory_report.inventory == rooms_inventory_count
    assert (
        inventory_report.out_of_order_rooms
        == housekeeping_room_status_count['out_of_order_rooms']
    )
    assert (
        inventory_report.out_of_service_rooms
        == housekeeping_room_status_count['out_of_service_rooms']
    )
