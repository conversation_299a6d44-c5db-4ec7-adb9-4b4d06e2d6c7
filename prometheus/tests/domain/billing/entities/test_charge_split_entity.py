import pytest
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus.domain.billing.dto.allowance_data import AllowanceData
from ths_common.constants.billing_constants import ChargeStatus, TaxTypes
from ths_common.exceptions import ValidationException
from ths_common.value_objects import TaxDetail


class TestChargeSplitEntity(object):
    def test_readonly_property(self, consumed_charge_non_credit):
        charge_split = consumed_charge_non_credit.get_split(1)

        assert charge_split.charge_split_id == 1
        with pytest.raises(AttributeError):
            charge_split.charge_split_id = 2

        assert charge_split.charge_to is None
        with pytest.raises(AttributeError):
            charge_split.charge_to = "CST-23233"

        assert charge_split.pre_tax == Money('50 INR')
        with pytest.raises(AttributeError):
            charge_split.pre_tax = Money('55 INR')

        assert charge_split.tax == Money('5 INR')
        with pytest.raises(AttributeError):
            charge_split.tax = Money('6 INR')

        assert charge_split.post_tax == Money('55 INR')
        with pytest.raises(AttributeError):
            charge_split.post_tax = Money('60 INR')

        assert charge_split.percentage == 50
        with pytest.raises(AttributeError):
            charge_split.percentage = 40

        assert len(charge_split.tax_details) == 2
        with pytest.raises(AttributeError):
            charge_split.tax_details = [
                TaxDetail(TaxTypes.CGST, 5, '2.5 INR'),
                TaxDetail(TaxTypes.SGST, 5, '2.5 INR'),
            ]

    def test_charge_split_invoice_info(self, consumed_charge_non_credit):
        charge_split = consumed_charge_non_credit.get_split(1)

        assert charge_split.is_invoiced is False
        assert charge_split.invoice_id is None

        # direct update should fail
        with pytest.raises(AttributeError):
            charge_split.invoice_id = "INV-*********"

        # update invoice info
        charge_split.update_invoice_id("INV-*********")

        assert charge_split.is_invoiced is True
        assert charge_split.invoice_id == "INV-*********"

        # update again should fail
        with pytest.raises(ValidationException):
            charge_split.update_invoice_id("INV-*********")

        # update again should pass with over-ride
        charge_split.update_invoice_id("INV-*********", override=True)

        assert charge_split.invoice_id == "INV-*********"

    def test_charge_split_update_amount(self, consumed_charge_non_credit):
        charge_split = consumed_charge_non_credit.get_split(1)

        tax_details = [
            TaxDetail(
                TaxTypes.SGST, amount=Money('10', CurrencyType.INR), percentage=10
            ),
            TaxDetail(
                TaxTypes.CGST, amount=Money('10', CurrencyType.INR), percentage=10
            ),
            TaxDetail(TaxTypes.IGST, amount=Money('0', CurrencyType.INR), percentage=0),
        ]
        charge_split.update_amount(
            pre_tax=Money('100', CurrencyType.INR),
            tax=Money('20', CurrencyType.INR),
            post_tax=Money('120', CurrencyType.INR),
            tax_details=tax_details,
        )

        assert charge_split.pre_tax == Money('100', CurrencyType.INR)
        assert charge_split.post_tax == Money('120', CurrencyType.INR)
        assert charge_split.tax == Money('20', CurrencyType.INR)
        assert len(charge_split.tax_details) == 3

        # Negative test
        # when pretax, posttax and tax does not sum up

        with pytest.raises(ValidationException):
            charge_split.update_amount(
                pre_tax=Money('100', CurrencyType.INR),
                tax=Money('20', CurrencyType.INR),
                post_tax=Money('130', CurrencyType.INR),
                tax_details=tax_details,
            )

        # when the tax details does not sum up
        with pytest.raises(ValidationException):
            charge_split.update_amount(
                pre_tax=Money('100', CurrencyType.INR),
                tax=Money('30', CurrencyType.INR),
                post_tax=Money('130', CurrencyType.INR),
                tax_details=tax_details,
            )

    def test_charge_split_delete(self, consumed_charge_non_credit):
        charge_split = consumed_charge_non_credit.get_split(1)

        assert charge_split.deleted is False

        charge_split.delete()

        assert charge_split.deleted is True

    def test_charge_split_adding_allowance_adds_allowance_in_created_state(
        self, consumed_charge_non_credit
    ):
        charge = consumed_charge_non_credit
        charge_split = charge.get_split(1)

        assert len(charge_split.allowances) == 0

        allowance_data = AllowanceData(pretax_amount=10, remarks='Adding new allowance')
        allowance = charge_split.add_allowance(
            allowance_dto=allowance_data, posting_date=dateutils.current_date()
        )

        assert len(charge_split.allowances) == 1
        assert allowance.status == ChargeStatus.CREATED
