import datetime

from treebo_commons.utils import dateutils

from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.factories.invoice_factory import InvoiceFactory
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    InvoiceStatus,
    IssuedByType,
    IssuedToType,
)
from ths_common.value_objects import BookingBillParentInfo, InvoiceBillToInfo


class TestInvoiceFactory(object):
    @staticmethod
    def __validate_invoice_charge(invoice_charge, charge, charge_split):
        assert invoice_charge.charge_id == charge.charge_id
        assert invoice_charge.pretax_amount == charge_split.pre_tax
        assert invoice_charge.tax_amount == charge_split.tax
        # assert invoice_charge.tax_details == tax_details
        assert invoice_charge.posttax_amount == charge_split.post_tax
        assert invoice_charge.charge_type == charge_split.charge_type
        assert invoice_charge.bill_to_type == charge_split.bill_to_type
        assert invoice_charge.charge_status == charge.status
        assert invoice_charge.recorded_time == charge.recorded_time
        assert invoice_charge.applicable_date == charge.applicable_date
        assert invoice_charge.comment == charge.comment
        assert invoice_charge.created_by == charge.created_by
        assert invoice_charge.charge_item == charge.item
        assert invoice_charge.charge_to_ids == charge.charge_to
        assert invoice_charge.charge_split_ids == [charge_split.charge_split_id]

    def test_create_preview_invoice(
        self, bill_aggregate, address, phone_no, active_hotel_aggregate
    ):
        # create mocks
        charge_split_map = {2: [1, 2], 3: [1]}

        bill_to = InvoiceBillToInfo(
            customer_id='CMR-1242342323',
            name="ram",
            address=address,
            gstin="GST-34343",
            phone=phone_no,
            email="<EMAIL>",
        )
        generated_by = '2324231'
        generation_channel = '1'
        parent_info = BookingBillParentInfo(
            booking_id='***********',
            reference_number='TEST_BOOKING_REF_1',
            creation_date=dateutils.current_date(),
            checkin_date=datetime.datetime.strptime('21Jun2018', '%d%b%Y'),
            checkout_date=datetime.datetime.strptime('23Jun2018', '%d%b%Y'),
        )

        bill_aggregate.update_parent_info(parent_info)
        pretax_amount = 0
        tax_amount = 0
        posttax_amount = 0

        for key, value in charge_split_map.items():
            charge = bill_aggregate.get_charge(key)
            for split in value:
                charge_split = charge.get_split(split)
                pretax_amount += charge_split.pre_tax
                tax_amount += charge_split.tax
                posttax_amount += charge_split.post_tax

        bill_to_type = ChargeBillToTypes.COMPANY

        user_info_map = dict()
        user_info_map["CST-23232"] = dict(name="Ram")
        user_info_map["CST-23233"] = dict(name="Shyam")

        invoice_date = datetime.datetime.strptime('25Jun2018', '%d%b%Y').date()

        invoice_aggregate = InvoiceFactory.create_preview_invoice(
            bill_aggregate=bill_aggregate,
            charge_split_map=charge_split_map,
            bill_to=bill_to,
            bill_to_type=bill_to_type,
            generated_by=generated_by,
            invoice_date=invoice_date,
            generation_channel=generation_channel,
            user_info_map=user_info_map,
            issued_by_type=IssuedByType.HOTEL,
            issued_to_type=IssuedToType.CUSTOMER,
            issued_by=None,
            billed_entity_account=BilledEntityAccountVO(1, 1),
        )

        # Test the invoice is in preview state
        assert invoice_aggregate.status == InvoiceStatus.PREVIEW

        # Check the bill to set to the right value
        assert invoice_aggregate.bill_to == bill_to

        # Check the generated_by
        assert invoice_aggregate.invoice.generated_by == generated_by

        assert invoice_aggregate.invoice.parent_info == bill_aggregate.bill.parent_info

        # Check the generation channel
        assert invoice_aggregate.invoice.generation_channel == generation_channel

        # Check vendor id
        assert invoice_aggregate.invoice.vendor_id == bill_aggregate.bill.vendor_id

        # check invoice date
        assert invoice_aggregate.invoice.invoice_date == invoice_date

        # check invoice due date
        assert invoice_aggregate.invoice.invoice_due_date == invoice_date

        # check bill linking
        assert invoice_aggregate.invoice.bill_id == bill_aggregate.bill.bill_id

        # check invoice no is null
        assert invoice_aggregate.invoice_number is None

        # check amount
        assert invoice_aggregate.invoice.pretax_amount == pretax_amount
        assert invoice_aggregate.invoice.tax_amount == tax_amount
        assert invoice_aggregate.invoice.posttax_amount == posttax_amount

        # check invoice charges
        assert len(invoice_aggregate.invoice_charges) == 3

        # check first invoice charge
        invoice_charge = invoice_aggregate.invoice_charges[0]
        charge = bill_aggregate.get_charge(2)

        assert invoice_charge.invoice_charge_id == 1
        self.__validate_invoice_charge(invoice_charge, charge, charge.get_split(1))

        invoice_charge = invoice_aggregate.invoice_charges[1]
        charge = bill_aggregate.get_charge(2)

        assert invoice_charge.invoice_charge_id == 2
        self.__validate_invoice_charge(invoice_charge, charge, charge.get_split(2))

        # Test invoice factory should not take unconsumed charge
