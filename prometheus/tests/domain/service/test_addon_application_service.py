# pylint: disable=W0621,invalid-name

import pytest
from treebo_commons.utils.dateutils import date_range

from prometheus.domain.booking.services.addon_domain_service import AddonDomainService
from prometheus.tests.factories.entity_factories import AddonFactory
from prometheus.tests.test_utils import today, tomorrow


@pytest.fixture
def some_addon():
    addon = AddonFactory.build()
    return addon


@pytest.fixture
def addon_domain_service() -> AddonDomainService:
    return AddonDomainService()


def test_addon_get_applicable_dates(addon_domain_service, checkin_addon):
    applicable_dates = addon_domain_service.get_applicable_dates(checkin_addon)
    assert len(applicable_dates) == 1


def test_create_expense_for_date_range(addon_domain_service, some_addon):
    date_rang = list(date_range(today(), tomorrow(), end_inclusive=True))
    expenses = addon_domain_service.create_expenses_for_dates(date_rang, some_addon)

    assert len(expenses) == len(date_rang)

    for expense in expenses:
        assert expense.expense_item_id == some_addon.expense_item_id
        assert expense.applicable_date in date_rang
