# def test_edit_booking_source_can_be_changed_by_cr_team(valid_booking_aggregate_with_one_room_stay):
#     edit_booking_allowed = EditBookingRule().allow(
#         Facts(user_type="cr-team", booking_aggregate=valid_booking_aggregate_with_one_room_stay,
#               action_payload=dict(source=None)))
#     assert edit_booking_allowed is True
#
#
# def test_edit_booking_source_can_be_changed_by_super_admin(valid_booking_aggregate_with_one_room_stay):
#     edit_booking_allowed = EditBookingRule().allow(
#         Facts(user_type="super-admin", booking_aggregate=valid_booking_aggregate_with_one_room_stay,
#               action_payload=dict(source=None)))
#     assert edit_booking_allowed is True
#
#
# def test_edit_booking_source_cannot_be_changed_by_anyone_other_than_cr_team_or_super_admin(
#         valid_booking_aggregate_with_one_room_stay):
#     # NOTE: Even if source is empty in the payload, rule will fail
#     with pytest.raises(PolicyAuthError):
#         edit_booking_allowed = EditBookingRule().allow(
#             Facts(user_type="anyone", booking_aggregate=valid_booking_aggregate_with_one_room_stay,
#                 action_payload=dict(source=None)))
