import pytest

from prometheus import crs_context
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.change_booking_owner_rules import (
    ChangeBookingOwnerDetailRule,
)
from prometheus.tests.response.role_manager_privileges_response import (
    get_privilege_details_by_role,
)
from ths_common.constants.booking_constants import BookingChannels
from ths_common.exceptions import PolicyAuthException
from ths_common.value_objects import Address, GSTDetails


def test_fdm_cannot_update_booking_owner_for_ta_booking(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.TA.value
    )
    privileges = get_privilege_details_by_role(role='fdm')

    with pytest.raises(PolicyAuthException):
        change_booking_owner_allowed = ChangeBookingOwnerDetailRule().allow(
            Facts(
                user_type="fdm",
                booking_aggregate=valid_booking_aggregate_with_one_room_stay,
                generated_invoice_aggregates=[],
                hotel_context=crs_context.get_hotel_context(),
            ),
            privileges=privileges,
        )


def test_fdm_cannot_update_booking_owner_for_b2b_booking(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.B2B.value
    )
    privileges = get_privilege_details_by_role(role='fdm')

    with pytest.raises(PolicyAuthException):
        change_booking_owner_allowed = ChangeBookingOwnerDetailRule().allow(
            Facts(
                user_type="fdm",
                booking_aggregate=valid_booking_aggregate_with_one_room_stay,
                generated_invoice_aggregates=[],
                hotel_context=crs_context.get_hotel_context(),
            ),
            privileges=privileges,
        )


def test_fdm_cannot_update_booking_owner_for_b2b_bulk_booking(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.B2B.value
    )
    privileges = get_privilege_details_by_role(role='fdm')

    with pytest.raises(PolicyAuthException):
        change_booking_owner_allowed = ChangeBookingOwnerDetailRule().allow(
            Facts(
                user_type="fdm",
                booking_aggregate=valid_booking_aggregate_with_one_room_stay,
                action_payload=dict(
                    gst_details=GSTDetails(
                        legal_name="test",
                        address=Address(
                            field_1='test_address',
                            field_2='',
                            city='test_city',
                            state='test_state',
                            country='test_country',
                            pincode='test_pincode',
                        ),
                        gstin_num="12345",
                    )
                ),
                generated_invoice_aggregates=[],
                hotel_context=crs_context.get_hotel_context(),
            ),
            privileges=privileges,
        )
