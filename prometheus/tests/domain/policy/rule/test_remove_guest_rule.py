import datetime

import pytest
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.policy.facts.guest_stay_facts import GuestStayFacts
from prometheus.domain.policy.rule.remove_guest_rule import RemoveGuestRule
from prometheus.tests.response.role_manager_privileges_response import (
    get_privilege_details_by_role,
)
from ths_common.constants.booking_constants import BookingChannels
from ths_common.exceptions import PolicyAuthException


def test_fdm_cannot_remove_guest_from_walkin_booking_after_checkin_date_midnight(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.HOTEL.value
    )
    guest_stay = valid_booking_aggregate_with_one_room_stay.room_stays[0].guest_stays[0]

    checkin_date_midnight = dateutils.datetime_at_midnight(
        dateutils.add(guest_stay.checkin_date, days=1)
    )
    current_time_after_checkin_date_midnight = (
        checkin_date_midnight + datetime.timedelta(minutes=1)
    )
    privileges = get_privilege_details_by_role(role='fdm')

    with pytest.raises(PolicyAuthException):
        allow_remove_guest = RemoveGuestRule().allow(
            GuestStayFacts(
                guest_stay=guest_stay,
                user_type="fdm",
                current_time=current_time_after_checkin_date_midnight,
                booking_aggregate=valid_booking_aggregate_with_one_room_stay,
                hotel_context=crs_context.get_hotel_context(),
            ),
            privileges,
        )


def test_fdm_can_remove_guest_from_walkin_booking_before_checkin_date_midnight(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.HOTEL.value
    )
    guest_stay = valid_booking_aggregate_with_one_room_stay.room_stays[0].guest_stays[0]

    checkin_date_midnight = dateutils.datetime_at_midnight(
        dateutils.add(guest_stay.checkin_date, days=1)
    )
    current_time_before_checkin_date_midnight = (
        checkin_date_midnight - datetime.timedelta(minutes=1)
    )
    privileges = get_privilege_details_by_role(role='fdm')

    allow_remove_guest = RemoveGuestRule().allow(
        GuestStayFacts(
            guest_stay=guest_stay,
            user_type="fdm",
            current_time=current_time_before_checkin_date_midnight,
            booking_aggregate=valid_booking_aggregate_with_one_room_stay,
            hotel_context=crs_context.get_hotel_context(),
        ),
        privileges,
    )
    assert allow_remove_guest is True


def test_fdm_cannot_remove_guest_from_non_walkin_booking_before_checkin_date_midnight(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.B2B.value
    )
    guest_stay = valid_booking_aggregate_with_one_room_stay.room_stays[0].guest_stays[0]

    checkin_date_midnight = dateutils.datetime_at_midnight(
        dateutils.add(guest_stay.checkin_date, days=1)
    )
    current_time_before_checkin_date_midnight = (
        checkin_date_midnight - datetime.timedelta(minutes=1)
    )
    privileges = get_privilege_details_by_role(role='fdm')

    with pytest.raises(PolicyAuthException):
        allow_remove_guest = RemoveGuestRule().allow(
            GuestStayFacts(
                guest_stay=guest_stay,
                user_type="fdm",
                current_time=current_time_before_checkin_date_midnight,
                booking_aggregate=valid_booking_aggregate_with_one_room_stay,
                hotel_context=crs_context.get_hotel_context(),
            ),
            privileges,
        )


def test_cr_team_can_remove_guest_from_non_walkin_booking_before_checkin_date_midnight(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.B2B.value
    )
    guest_stay = valid_booking_aggregate_with_one_room_stay.room_stays[0].guest_stays[0]

    checkin_date_midnight = dateutils.datetime_at_midnight(
        dateutils.add(guest_stay.checkin_date, days=1)
    )
    current_time_before_checkin_date_midnight = (
        checkin_date_midnight - datetime.timedelta(minutes=1)
    )
    privileges = get_privilege_details_by_role(role='cr-team')

    allow_remove_guest = RemoveGuestRule().allow(
        GuestStayFacts(
            guest_stay=guest_stay,
            user_type="cr-team",
            current_time=current_time_before_checkin_date_midnight,
            booking_aggregate=valid_booking_aggregate_with_one_room_stay,
            hotel_context=crs_context.get_hotel_context(),
        ),
        privileges,
    )
    assert allow_remove_guest is True


def test_cr_team_can_remove_guest_from_non_walkin_booking_after_checkin_date_midnight(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.B2B.value
    )
    guest_stay = valid_booking_aggregate_with_one_room_stay.room_stays[0].guest_stays[0]

    checkin_date_midnight = dateutils.datetime_at_midnight(
        dateutils.add(guest_stay.checkin_date, days=1)
    )
    current_time_after_checkin_date_midnight = (
        checkin_date_midnight + datetime.timedelta(minutes=1)
    )
    privileges = get_privilege_details_by_role(role='cr-team')

    allow_remove_guest = RemoveGuestRule().allow(
        GuestStayFacts(
            guest_stay=guest_stay,
            user_type="cr-team",
            current_time=current_time_after_checkin_date_midnight,
            booking_aggregate=valid_booking_aggregate_with_one_room_stay,
            hotel_context=crs_context.get_hotel_context(),
        ),
        privileges,
    )
    assert allow_remove_guest is True
