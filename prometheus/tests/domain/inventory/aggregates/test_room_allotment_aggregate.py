from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.inventory.aggregates.room_allotment_aggregate import (
    RoomAllotmentAggregate,
)
from prometheus.domain.inventory.entities.housekeeping_record import HouseKeepingRecord
from prometheus.domain.inventory.entities.room_allotment import RoomAllotment
from prometheus.domain.inventory.entities.room_inventory import RoomInventory
from ths_common.constants.inventory_constants import AllottedFor, HouseKeepingStatus


def get_min_and_max_slot(from_date, to_date, hotel_context):
    min_start_time = hotel_context.attach_switch_over_time_to_date(from_date)
    # max_start_time = hotel_context.attach_free_late_checkout_time_to_date(from_date)
    min_end_time = dateutils.subtract(
        hotel_context.attach_switch_over_time_to_date(to_date), minutes=1
    )
    max_end_time = dateutils.subtract(
        hotel_context.attach_free_late_checkout_time_to_date(to_date), minutes=1
    )
    # min_start_time = dateutils.datetime_at_given_time(from_date, hotel_context.switch_over_time)
    max_start_time = dateutils.subtract(
        dateutils.add(min_start_time, days=1), minutes=1
    )
    # min_end_time = dateutils.datetime_at_given_time(dateutils.subtract(to_date, days=1),
    #                                                 hotel_context.free_late_checkout_time)
    # max_end_time = dateutils.subtract(dateutils.datetime_at_given_time(to_date,
    #                                                                    hotel_context.free_late_checkout_time),
    #                                   minutes=1)
    return min_start_time, max_start_time, min_end_time, max_end_time


class TestGetBroadestSlotReturnMaxPossibleSlot(object):
    def test_without_any_existing_allotment(self, active_hotel_aggregate):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = []
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time == min_start
        assert end_time == max_end

    def test_with_one_allotment_ended_in_past(self, active_hotel_aggregate):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.subtract(min_start, days=4),
                expected_end_time=dateutils.subtract(min_start, minutes=1),
                actual_end_time=dateutils.subtract(min_start, minutes=1),
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time == min_start
        assert end_time == max_end

    def test_with_one_allotment_starting_in_future(self, active_hotel_aggregate):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.add(max_end, minutes=1),
                expected_end_time=dateutils.add(max_end, days=2),
                actual_end_time=dateutils.add(max_end, days=2),
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time == min_start
        assert end_time == max_end

    def test_with_allotments_ending_in_past_or_starting_in_future(
        self, active_hotel_aggregate
    ):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.add(max_end, minutes=1),
                expected_end_time=dateutils.add(max_end, days=2),
                actual_end_time=dateutils.add(max_end, days=2),
                allotted_for=AllottedFor.STAY,
            ),
            RoomAllotment(
                1,
                start_time=dateutils.subtract(min_start, days=4),
                expected_end_time=dateutils.subtract(min_start, minutes=1),
                actual_end_time=dateutils.subtract(min_start, minutes=1),
                allotted_for=AllottedFor.STAY,
            ),
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time == min_start
        assert end_time == max_end


class TestGetBroadestSlotReturnNoPossibleSlot(object):
    def test_with_single_allotment_starting_before_max_start_and_ending_after_max_start_time(
        self, active_hotel_aggregate
    ):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.subtract(max_start, days=1),
                expected_end_time=dateutils.add(max_start, minutes=1),
                actual_end_time=dateutils.add(max_start, days=1),
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time is None
        assert end_time is None

    def test_with_single_allotment_starting_before_min_end_and_ending_after_min_end_time(
        self, active_hotel_aggregate
    ):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.subtract(min_end, days=1),
                expected_end_time=dateutils.add(min_end, minutes=1),
                actual_end_time=dateutils.add(min_end, days=1),
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time is None
        assert end_time is None

    def test_with_single_allotment_starting_after_max_start_and_ending_before_min_end_time(
        self, active_hotel_aggregate
    ):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.add(max_start, minutes=10),
                expected_end_time=dateutils.subtract(min_end, minutes=1),
                actual_end_time=dateutils.subtract(min_end, minutes=5),
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time is None
        assert end_time is None

    def test_with_single_allotment_starting_before_min_start_having_expected_end_time_before_max_start_time(
        self, active_hotel_aggregate
    ):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.subtract(min_start, days=1),
                expected_end_time=dateutils.subtract(max_start, days=1),
                actual_end_time=None,
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time == start_time
        assert end_time == end_time

    def test_with_single_allotment_starting_before_max_start_having_expected_end_time_before_max_start_time_but_null_actual_end_time(
        self, active_hotel_aggregate
    ):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.subtract(max_start, days=3),
                expected_end_time=dateutils.subtract(max_start, days=2),
                actual_end_time=None,
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time == start_time
        assert end_time == end_time


class TestGetBroadestSlotReturnSlotBetweenMaxAndMinPossibleSlot(object):
    def test_with_single_allotment_starting_before_min_start_time_and_ending_before_max_start_time(
        self, active_hotel_aggregate
    ):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.subtract(min_start, days=3),
                expected_end_time=dateutils.subtract(max_start, days=2),
                actual_end_time=dateutils.subtract(max_start, minutes=30),
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        # start time will be 1 minute after end of previous allotment
        assert start_time == dateutils.add(
            room_allotments[0].actual_end_time, minutes=1
        )
        assert end_time == max_end

    def test_with_single_allotment_starting_after_min_start_time_and_ending_before_max_start_time(
        self, active_hotel_aggregate
    ):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.add(min_start, minutes=15),
                expected_end_time=dateutils.subtract(max_start, days=2),
                actual_end_time=dateutils.subtract(max_start, minutes=30),
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        # start time will be 1 minute after end of previous allotment
        assert start_time == dateutils.add(
            room_allotments[0].actual_end_time, minutes=1
        )
        assert end_time == max_end

    def test_with_single_allotment_starting_after_min_end_time_and_ending_before_max_end_time(
        self, active_hotel_aggregate
    ):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.add(min_end, minutes=15),
                expected_end_time=dateutils.subtract(max_end, days=2),
                actual_end_time=dateutils.subtract(max_end, minutes=30),
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time == min_start
        # end time will be 1 minute before start of previous allotment
        assert end_time == dateutils.subtract(room_allotments[0].start_time, minutes=1)

    def test_with_single_allotment_starting_after_min_end_time_and_ending_after_max_end_time(
        self, active_hotel_aggregate
    ):
        hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
        from_date = dateutils.ymd_str_to_date("2018-01-26")
        to_date = dateutils.ymd_str_to_date("2018-01-29")
        min_start, max_start, min_end, max_end = get_min_and_max_slot(
            from_date, to_date, hotel_context
        )

        room_allotments = [
            RoomAllotment(
                1,
                start_time=dateutils.add(min_end, minutes=15),
                expected_end_time=dateutils.add(max_end, days=2),
                actual_end_time=dateutils.add(max_end, minutes=30),
                allotted_for=AllottedFor.STAY,
            )
        ]
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=RoomInventory(hotel_id="1", room_id="1"),
            housekeeping_record=HouseKeepingRecord(
                hotel_id="1", room_id="1", housekeeping_status=HouseKeepingStatus.CLEAN
            ),
            room_allotments=room_allotments,
        )

        (
            start_time,
            end_time,
        ) = room_allotment_aggregate.get_broadest_slot_available_covering_all_dates_between_dates(
            from_date, to_date, hotel_context
        )

        assert start_time == min_start
        # end time will be 1 minute before start of previous allotment
        assert end_time == dateutils.subtract(room_allotments[0].start_time, minutes=1)
