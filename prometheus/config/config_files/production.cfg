import logging
import os

DEBUG = False

# Logging
LOG_LEVEL = logging.INFO
LOG_ROOT = '/var/log/prometheus/'

AWS_ACCESS_KEY_ID = '********************'
AWS_SECRET_ACCESS_KEY = '3+DhXLfkKq9cbFEzTeKlo4vFohHbqJj71y6RLT2k'
AWS_S3_BUCKET_NAME = 'crs-p-mum'

TEMPORARY_REPORTS_DIRECTORY = '/tmp/reports/'

# Segment Event
SEGMENT_EVENTS_KEY = 'UbEpyQqlMetVonEDQddjh9DjA8F4tdhg'

CLEARTAX_ENDPOINT_URL = "https://api-einv.cleartax.in"
B2B_COMPANY_DETAILS_ALERTS_SLACK_WEBHOOK_URL = '*******************************************************************************'
SUPERHERO_GENERIC_ALERTS_SLACK_WEBHOOK_URL = '*******************************************************************************'

B2B_SOFT_BOOKING_EXPIRY_EMAIL_RECEIVER_LIST = ['<EMAIL>', '<EMAIL>']
DUMMY_HOTEL_IDS = ('9907195', '1438960', '1502702')
DUMMY_SUPERHERO_COMPANY_CODES = ('grwt1684', 'bbts795b', 'bbdm2b33')
METABASE_B2B_TA_BOOKING_DASHBOARD = 'https://metabase.treebo.com/question/15102-booking-company-and-ta-details?booking='
INTERNAL_PAYMENT_SERVICE_URL = 'https://payments-navision.treebo.com'
