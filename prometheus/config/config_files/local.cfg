import logging

#####
# Example config file. Please copy and use it. Add the path of the copied file to PROM_CONFIG_FILE_PATH env variable.
#####

DEBUG = True

# Database
DB_PASSWORD = "crs"
DB_USER = "crs_user"
DB_URL = "localhost"
DB_SLAVE_URL = "localhost"
DB_PORT = "5432"
DB = "crs"

SQLALCHEMY_TRACK_MODIFICATIONS = True
SQLALCHEMY_DATABASE_URI = 'postgresql://%s:%s@%s:%s/%s' % (DB_USER, DB_PASSWORD, DB_URL,
                                                           DB_PORT, DB)
SQLALCHEMY_BINDS = {
    'slave': 'postgresql://%s:%s@%s:%s/%s' % (DB_USER, DB_PASSWORD, DB_URL, DB_PORT, DB)}

# Logging
LOG_LEVEL = logging.DEBUG
LOG_ROOT = '.'

# RabbitMQ
RABBITMQ_HOST = 'amqp://guest:guest@localhost:5672/'
