from treebo_commons.money import Money
from treebo_commons.utils.dateutils import today

from ths_common.constants.billing_constants import (
    AmazonPayTypes,
    ChargeStatus,
    CreditCardTypes,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
    PhonePeTypes,
)
from thsc.crs.entities.billing import Bill, Charge, Payment


def test_add_payment(booking_with_one_room_two_guests_for_seven_days):
    # add a payment
    date_of_payment = today()

    payment = Payment.create_instance(
        Money('234.23'),
        date_of_payment,
        PaymentReceiverTypes.TREEBO,
        PaymentChannels.ONLINE,
        PaymentModes.CREDIT_CARD,
        PaymentTypes.PAYMENT,
        PaymentStatus.DONE,
        PaymentReceiverTypes.GUEST,
        dict(),
        "pay_1232313",
        payment_mode_sub_type=CreditCardTypes.VISA,
    )
    bill = Bill.get(booking_with_one_room_two_guests_for_seven_days.bill_id)
    bill.add_payment(payment)

    # add another payment
    payment = Payment.create_instance(
        Money('100.23'),
        date_of_payment,
        PaymentReceiverTypes.TREEBO,
        PaymentChannels.ONLINE,
        PaymentModes.CREDIT_CARD,
        PaymentTypes.PAYMENT,
        PaymentStatus.DONE,
        PaymentReceiverTypes.GUEST,
        dict(),
        "pay_1232314",
        CreditCardTypes.VISA,
    )

    bill.add_payment(payment)

    payment = Payment.create_instance(
        Money('100.23'),
        date_of_payment,
        PaymentReceiverTypes.TREEBO,
        PaymentChannels.ONLINE,
        PaymentModes.PHONE_PE,
        PaymentTypes.PAYMENT,
        PaymentStatus.DONE,
        PaymentReceiverTypes.GUEST,
        dict(),
        "pay_1232314",
        PhonePeTypes.PHONEPE_APP,
    )

    bill.add_payment(payment)

    payment = Payment.create_instance(
        Money('100.23'),
        date_of_payment,
        PaymentReceiverTypes.TREEBO,
        PaymentChannels.ONLINE,
        PaymentModes.AMAZON_PAY,
        PaymentTypes.PAYMENT,
        PaymentStatus.DONE,
        PaymentReceiverTypes.GUEST,
        dict(),
        "pay_1232314",
        AmazonPayTypes.AMAZONPAY_APP,
    )

    bill.add_payment(payment)

    assert len(bill.payments) == 4


def test_update_payment(booking_with_one_room_two_guests_for_seven_days):
    # add a payment
    date_of_payment = today()

    payment = Payment.create_instance(
        Money('234.23'),
        date_of_payment,
        PaymentReceiverTypes.TREEBO,
        PaymentChannels.ONLINE,
        PaymentModes.CREDIT_CARD,
        PaymentTypes.PAYMENT,
        PaymentStatus.DONE,
        PaymentReceiverTypes.GUEST,
        dict(),
        "pay_1232313",
        CreditCardTypes.VISA,
    )

    bill = Bill.get(booking_with_one_room_two_guests_for_seven_days.bill_id)
    bill.add_payment(payment)

    # update payment
    payment = Payment.create_instance_for_update(bill.payments[0].payment_id)
    payment.payment_ref_id = "new_ref_id"
    payment = bill.update_payment(payment)
    assert payment.payment_ref_id == "new_ref_id"


def test_update_charge(booking_with_one_room_two_guests_for_seven_days):
    bill = Bill.get(booking_with_one_room_two_guests_for_seven_days.bill_id)

    charge_1 = Charge.create_instance_for_update(bill.charges[0].charge_id)
    charge_1.posttax_amount = Money("1500")
    charge_2 = Charge.create_instance_for_update(bill.charges[1].charge_id)
    charge_2.posttax_amount = Money("1600")
    charges = bill.update_charges([charge_1, charge_2])
    assert len(charges) == 2

    charge_2 = Charge.create_instance_for_update(bill.charges[1].charge_id)
    charge_2.status = ChargeStatus.CANCELLED
    charges = bill.update_charges([charge_2])
