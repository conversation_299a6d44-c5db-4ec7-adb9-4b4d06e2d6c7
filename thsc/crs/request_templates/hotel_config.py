from thsc.crs.convertors.hotel_config_convertor import HotelConfigConvertor
from thsc.crs.request_templates.request import Request


class HotelConfig(Request):
    _convertor = HotelConfigConvertor

    def __init__(self, url, request_method, data=None):
        super(HotelConfig, self).__init__(url, request_method, data)

    def get_uri(self):
        return super(HotelConfig, self).get_uri()


class GetHotelConfig(HotelConfig):
    _payload = False

    def __init__(self, hotel_id):
        super(GetHotelConfig, self).__init__(
            url='/hotel-configs/{hotel_id}', request_method='GET'
        )
        self.hotel_id = hotel_id

    def get_uri(self):
        return super(HotelConfig, self).get_uri().format(hotel_id=self.hotel_id)
