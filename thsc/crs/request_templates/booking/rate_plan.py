from thsc.crs.convertors.rate_plan_convertors import BookingRatePlanResponseConvertor
from thsc.crs.request_templates.request import Request


class BookingRatePlans(Request):
    _payload = False
    _convertor = BookingRatePlanResponseConvertor

    def __init__(self, booking_id):
        super(BookingRatePlans, self).__init__(
            '/bookings/{booking_id}/rate-plans', 'GET'
        )
        self.booking_id = booking_id

    def get_uri(self):
        return (
            super(BookingRatePlans, self).get_uri().format(booking_id=self.booking_id)
        )
