from thsc.crs.convertors.booking_convertors import ExpenseConverter
from thsc.crs.request_templates.request import Request


class CreateExpense(Request):
    _convertor = ExpenseConverter

    def __init__(self, booking_id, resource_version, expense):
        super(CreateExpense, self).__init__(
            '/bookings/{booking_id}/expenses', 'POST', expense
        )
        self.booking_id = booking_id
        self.resource_version = resource_version

    def get_uri(self):
        return super(CreateExpense, self).get_uri().format(booking_id=self.booking_id)

    def to_dict(self):
        response = super(CreateExpense, self).to_dict()
        response['resource_version'] = self.resource_version
        return response


class GetExpenses(Request):
    _payload = False
    _convertor = ExpenseConverter

    def __init__(self, booking_id):
        super(GetExpenses, self).__init__('/bookings/{booking_id}/expenses', 'GET')
        self.booking_id = booking_id

    def get_uri(self):
        return super(GetExpenses, self).get_uri().format(booking_id=self.booking_id)
