from thsc.crs.convertors.billing_convertors import PaymentConvertor
from thsc.crs.request_templates.billing.payment import Payment


class UpdatePayment(Payment):
    _convertor = PaymentConvertor

    def __init__(self, bill_id, payment_id, version, payment):
        super(UpdatePayment, self).__init__(
            '/bills/%s/payments/%s' % (bill_id, payment_id), 'PATCH', payment
        )
        self.bill_id = bill_id
        self.payment_id = payment_id
        self.resource_version = version
