from thsc.crs.convertors.billing_convertors import PrintBillUrlConvertor
from thsc.crs.request_templates.request import Request


class PrintBill(Request):
    _payload = False
    _response_convertor = PrintBillUrlConvertor

    def __init__(self, bill_id):
        super(PrintBill, self).__init__('/bills/{0}/print'.format(bill_id), 'GET')

    def url(self):
        return super(PrintBill, self).uri()

    def to_dict(self):
        return {}

    def from_dict(self, response):
        resp = self._response_convertor().from_dict(response, many=False)
        return resp
