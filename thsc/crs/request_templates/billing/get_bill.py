from thsc.crs.convertors.billing_convertors import Bill<PERSON>onvertor, BillsConvertor
from thsc.crs.request_templates.request import Request


class GetBill(Request):
    _convertor = BillConvertor
    _payload = False

    def __init__(self, bill_id):
        super(GetBill, self).__init__(('/bills/%s' % bill_id), 'GET')
        self.bill_id = bill_id


class GetBills(Request):
    _convertor = BillsConvertor
    _payload = False

    def __init__(self, bill_ids):
        super(GetBills, self).__init__(('/bills?bill_ids=%s' % bill_ids), 'GET')
        self.bill_ids = bill_ids


class GetBillV2(Request):
    _convertor = BillConvertor
    _payload = False

    def __init__(self, bill_id):
        super(GetBillV2, self).__init__(('/bills/%s' % bill_id), 'GET')
        self.bill_id = bill_id

    def get_uri(self):
        return super(GetBillV2, self).get_uri_v2()
