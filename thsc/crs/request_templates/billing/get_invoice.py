from thsc.crs.convertors.billing_convertors import (
    InvoiceConvertor,
    InvoiceGetQueryConverter,
    InvoicesGetQueryConverter,
)
from thsc.crs.entities.billing import InvoiceGetQuery, InvoicesGetQuery
from thsc.crs.request_templates.request import Request


class GetInvoice(Request):
    _payload = True
    _convertor = InvoiceGetQueryConverter
    _response_convertor = InvoiceConvertor

    def __init__(self, invoice_id, with_invoice_charges):
        super(GetInvoice, self).__init__(
            '/invoices/{0}'.format(invoice_id),
            'GET',
            data=InvoiceGetQuery(show_raw=with_invoice_charges),
        )

    def url(self):
        return super(GetInvoice, self).uri()

    def to_dict(self):
        request = self._convertor().to_dict(self.data, many=self._many)
        return request

    def from_dict(self, response):
        resp = self._response_convertor().from_dict(response, many=False)
        return resp


class GetInvoices(Request):
    _payload = True
    _convertor = InvoicesGetQueryConverter
    _response_convertor = InvoiceConvertor

    def __init__(self, invoice_ids, with_invoice_charges, generate_signed_url):
        super(GetInvoices, self).__init__(
            '/invoices?invoice_ids={invoice_ids}'.format(invoice_ids=invoice_ids),
            'GET',
            data=InvoicesGetQuery(
                show_raw=with_invoice_charges, generate_signed_url=generate_signed_url
            ),
        )

    def url(self):
        return super(GetInvoices, self).uri()

    def to_dict(self):
        request = self._convertor().to_dict(self.data, many=self._many)
        return request

    def from_dict(self, response):
        resp = self._response_convertor().from_dict(response, many=True)
        return resp
