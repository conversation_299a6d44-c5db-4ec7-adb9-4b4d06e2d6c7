from thsc.crs.convertors.billing_convertors import UpdateChargeConvertor
from thsc.crs.request_templates.request import Request


class UpdateCharges(Request):
    _convertor = UpdateChargeConvertor
    _many = True
    _payload = True

    def __init__(self, bill_id, version, charges):
        super(UpdateCharges, self).__init__(
            '/bills/%s/charges' % bill_id, 'PATCH', charges
        )
        self.bill_id = bill_id
        self.resource_version = version
