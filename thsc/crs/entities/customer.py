from ths_common.value_objects import NotAssigned


class Customer(object):
    def __init__(
        self,
        first_name,
        last_name=NotAssigned,
        email=NotAssigned,
        country_code=NotAssigned,
        phone_number=NotAssigned,
        address=NotAssigned,
        age=NotAssigned,
        gender=NotAssigned,
        nationality=NotAssigned,
        date_of_birth=NotAssigned,
        status=NotAssigned,
        id_proof=NotAssigned,
        image_url=NotAssigned,
        reference_id=NotAssigned,
        profile_type=NotAssigned,
        gst_details=NotAssigned,
        customer_id=NotAssigned,
        user_profile_id=NotAssigned,
        loyalty_program_details=NotAssigned,
        salutation=NotAssigned,
        is_primary=False,
        is_vip=False,
        vip_details=None,
        guest_metadata=NotAssigned,
    ):
        """
        \n
        Args:
            first_name (str):
            last_name (str):
            email (str):
            country_code (str):
            phone_number (str):
            address (Address):
            age (int):
            gender (prometheus.constants.booking_constants.Genders):
            nationality (str):
            status (prometheus.constants.booking_constants.GuestStatus):
            id_proof (IDProof):
            gst_details (GSTDetails):
            loyalty_program_details (LoyaltyProgramDetails):
            salutation (prometheus.constants.booking_constants.Salutation):
        """
        self.first_name = first_name
        self.last_name = last_name
        self.email = email
        self.phone_number = phone_number
        self.address = address
        self.age = age
        self.country_code = country_code
        self.gender = gender
        self.nationality = nationality
        self.date_of_birth = date_of_birth

        self.customer_id = customer_id
        self.status = status
        self.id_proof = id_proof
        self.image_url = image_url
        self.reference_id = reference_id
        self.profile_type = profile_type
        self.gst_details = gst_details
        self.user_profile_id = user_profile_id
        self.loyalty_program_details = loyalty_program_details
        self.salutation = salutation
        self.is_primary = is_primary
        self.is_vip = is_vip
        self.vip_details = vip_details
        self.guest_metadata = guest_metadata

    @staticmethod
    def create_empty_instance():
        customer = Customer(first_name=NotAssigned)
        return customer

    @staticmethod
    def create_booking_owner(
        first_name,
        profile_type,
        last_name=NotAssigned,
        phone_number=NotAssigned,
        country_code=NotAssigned,
        reference_id=NotAssigned,
        email=NotAssigned,
        address=NotAssigned,
        gst_details=NotAssigned,
        user_profile_id=NotAssigned,
        loyalty_program_details=NotAssigned,
        salutation=NotAssigned,
    ):
        return Customer(
            first_name=first_name,
            profile_type=profile_type,
            last_name=last_name,
            phone_number=phone_number,
            country_code=country_code,
            reference_id=reference_id,
            email=email,
            address=address,
            gst_details=gst_details,
            user_profile_id=user_profile_id,
            loyalty_program_details=loyalty_program_details,
            salutation=salutation,
        )

    @staticmethod
    def create_instance_for_update(customer_id):
        return Customer(first_name=NotAssigned, customer_id=customer_id)

    @staticmethod
    def get(booking_id, customer_id):
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.get_customer(booking_id, customer_id)

    def __eq__(self, other):
        return self.__dict__ == other.__dict__
