from ths_common.exceptions import BookingReferenceIdCollision, CRSException


class THSCException(CRSException):
    error_code = '9001'
    message = 'Something went wrong in THSC.'

    def __init__(self, error=None, description=None, message=None, extra_payload=None):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error_code
            self.message = message if message else self.message
        super(THSCException, self).__init__(
            description=description, extra_payload=extra_payload
        )


class CRSAPIException(THSCException):
    error_code = '9002'
    message = "THS backend failed."
    http_status_code = 500

    def __init__(
        self,
        error=None,
        description=None,
        message=None,
        extra_payload=None,
        http_status_code=None,
        errors=None,
    ):
        if http_status_code:
            self.http_status_code = http_status_code
        self.errors = errors
        super(CRSAPIException, self).__init__(
            error=error,
            message=message,
            description=description,
            extra_payload=extra_payload,
        )


class DuplicateReferenceNumber(BookingReferenceIdCollision):
    http_status_code = 400


class AuthenticationException(THSCException):
    error_code = '9005'
