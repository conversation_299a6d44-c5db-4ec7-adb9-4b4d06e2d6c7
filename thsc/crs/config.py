import os

from treebo_commons.service_discovery.service_registry import ServiceRegistry

from ths_common.exceptions import ValidationException
from thsc.crs.constants import THSEnvironment
from thsc.crs.errors import THSCErrors


class THSConfig(object):
    CRS_SERVICE_URL = "crs_service_url"
    CRS_READ_ONLY_SERVICE_URL = "crs_reports_service_url"

    def __init__(self, env):
        self.client_id = None
        self.client_secret = None
        self.crs_host = 'http://{h}:{p}'.format(
            h=os.environ.get('HOST', 'localhost'), p=os.environ.get('PORT', '5000')
        )
        self.auth_domain = None
        self.max_tries = 3
        self.read_only_crs_host = None

        try:
            env = THSEnvironment(env)
        except ValueError:
            raise ValidationException(THSCErrors.INVALID_THS_ENV)

        if env in [THSEnvironment.PRODUCTION, THSEnvironment.STAGING]:
            self._setup_crs_host()

    def _setup_crs_host(self):
        all_service_endpoints = ServiceRegistry.get_all_service_endpoints()
        self.crs_host = all_service_endpoints.get(
            self.CRS_SERVICE_URL, os.environ.get(self.CRS_SERVICE_URL)
        )
        self.read_only_crs_host = all_service_endpoints.get(
            self.CRS_READ_ONLY_SERVICE_URL,
            os.environ.get(self.CRS_READ_ONLY_SERVICE_URL),
        )

    def set_config(self, **kwargs):
        return False
