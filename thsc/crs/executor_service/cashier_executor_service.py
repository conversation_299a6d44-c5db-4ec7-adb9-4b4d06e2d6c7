from thsc.crs.request_templates.billing.cashiering import (
    AddCashierPayment,
    SearchCashierSession,
    SearchCashRegister,
)


class CashierExecutorService(object):
    def __init__(self, executor):
        self.executor = executor

    def add_cashier_payment(
        self, cash_register_id, cashier_session_id, cashier_payment
    ):
        request = AddCashierPayment(
            cash_register_id=cash_register_id,
            cashier_session_id=cashier_session_id,
            cashier_payment=cashier_payment,
        )
        return self.executor.execute(request)

    def search_cashier_session(self, cash_register_id, cashier_session_search_query):
        request = SearchCashierSession(
            cash_register_id=cash_register_id,
            cashier_session_search_query=cashier_session_search_query,
        )
        return self.executor.execute(request)

    def search_cash_register(self, cash_register_search_query):
        request = SearchCashRegister(
            cash_register_search_query=cash_register_search_query
        )
        return self.executor.execute(request)
