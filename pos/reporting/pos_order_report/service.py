import logging
from collections import defaultdict
from decimal import Decimal

from object_registry import register_instance
from pos.infrastructure.database.repositories.order.order_repository import (
    OrderRepository,
)
from pos.infrastructure.external_clients.aws_service_client import AwsServiceClient
from pos.infrastructure.external_clients.crs_client import CRSClient
from pos.reporting.pos_order_report.pos_order_report_generator import (
    PosOrderReportGenerator,
)
from pos.reporting.utils import CsvWriter
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@register_instance(dependencies=[OrderRepository])
class OrderReportingService(object):
    """
    Generic application service for reports/reporting
    """

    def __init__(self, order_repository: OrderRepository):
        self.order_repository = order_repository

    def report_summary(self, start_date, end_date, seller_id, room_number):
        order_aggregates = self.order_repository.pos_order_report_query(
            start_date, end_date, seller_id, room_number
        )
        order_report_response = {"summary": [], "datewise_summary": []}
        for order_aggregates in chunks(order_aggregates, 1000):
            bill_ids = [order_aggregate.bill_id for order_aggregate in order_aggregates]
            bills = CRSClient.load_bills(bill_ids)
            bill_id_to_object_map = {bill.bill_id: bill for bill in bills.bills}
            order_report_response_chunk = self._generate_report_summary(
                order_aggregates, bill_id_to_object_map
            )
            order_report_response["summary"].extend(
                order_report_response_chunk["summary"]
            )
            order_report_response["datewise_summary"].extend(
                order_report_response_chunk["datewise_summary"]
            )

        return order_report_response

    def _generate_report_summary(self, order_aggregates, bill_id_to_object_map):
        return {
            'summary': self._order_type_wise_summary(
                order_aggregates, bill_id_to_object_map
            ),
            'datewise_summary': self._get_datewise_report_summary(
                order_aggregates, bill_id_to_object_map
            ),
        }

    def _order_type_wise_summary(self, order_aggregates, bill_id_to_object_map):
        order_type_wise_summary = defaultdict(dict)
        for order_aggregate in order_aggregates:
            order_report_aggregate = PosOrderReportGenerator(
                order_aggregate, bill_id_to_object_map[order_aggregate.bill_id]
            ).generate()
            if not order_type_wise_summary[order_report_aggregate.seller_id].get(
                order_report_aggregate.order_type
            ):
                order_type_wise_summary[order_report_aggregate.seller_id][
                    order_report_aggregate.order_type
                ] = {
                    'pre_tax_amount': Decimal(0),
                    'tax_amount': Decimal(0),
                    'total_amount': Decimal(0),
                }
            order_type_wise_summary[order_report_aggregate.seller_id][
                order_report_aggregate.order_type
            ]['pre_tax_amount'] += order_report_aggregate.pre_tax_amount
            order_type_wise_summary[order_report_aggregate.seller_id][
                order_report_aggregate.order_type
            ]['total_amount'] += order_report_aggregate.total_amount
            order_type_wise_summary[order_report_aggregate.seller_id][
                order_report_aggregate.order_type
            ]['tax_amount'] += order_report_aggregate.tax_amount
            order_type_wise_summary[order_report_aggregate.seller_id][
                order_report_aggregate.order_type
            ]['order_type'] = order_report_aggregate.order_type
            order_type_wise_summary[order_report_aggregate.seller_id][
                order_report_aggregate.order_type
            ]['seller_name'] = order_report_aggregate.seller_name

        summary_response = []
        for seller_id, seller_wise_summary in order_type_wise_summary.items():
            for order_type, order_type_wise_summary in seller_wise_summary.items():
                summary_response.append(order_type_wise_summary)
        return summary_response

    def _get_datewise_report_summary(self, order_aggregates, bill_id_to_object_map):
        datewise_seller_wise_summary = defaultdict(dict)
        for order_aggregate in order_aggregates:
            order_report_aggregate = PosOrderReportGenerator(
                order_aggregate, bill_id_to_object_map[order_aggregate.bill_id]
            ).generate()
            if not datewise_seller_wise_summary[order_report_aggregate.seller_id].get(
                order_report_aggregate.order_datetime
            ):
                datewise_seller_wise_summary[order_report_aggregate.seller_id][
                    order_report_aggregate.order_datetime
                ] = {
                    'pre_tax_amount': Decimal(0),
                    'tax_amount': Decimal(0),
                    'total_amount': Decimal(0),
                }
            datewise_seller_wise_summary[order_report_aggregate.seller_id][
                order_report_aggregate.order_datetime
            ]['pre_tax_amount'] += order_report_aggregate.pre_tax_amount
            datewise_seller_wise_summary[order_report_aggregate.seller_id][
                order_report_aggregate.order_datetime
            ]['total_amount'] += order_report_aggregate.total_amount
            datewise_seller_wise_summary[order_report_aggregate.seller_id][
                order_report_aggregate.order_datetime
            ]['tax_amount'] += order_report_aggregate.tax_amount
            datewise_seller_wise_summary[order_report_aggregate.seller_id][
                order_report_aggregate.order_datetime
            ]['date'] = order_report_aggregate.order_datetime
            datewise_seller_wise_summary[order_report_aggregate.seller_id][
                order_report_aggregate.order_datetime
            ]['seller_name'] = order_report_aggregate.seller_name

        summary_response = []
        for date, date_wise_summary in datewise_seller_wise_summary.items():
            for seller, seller_wise_summary in date_wise_summary.items():
                summary_response.append(seller_wise_summary)
        return summary_response

    def report_details(self, start_date, end_date, seller_id, room_number):
        order_aggregates = self.order_repository.pos_order_report_query(
            start_date, end_date, seller_id, room_number
        )
        order_report_response = []
        for order_aggregates in chunks(order_aggregates, 1000):
            bill_ids = [order_aggregate.bill_id for order_aggregate in order_aggregates]
            bills = CRSClient.load_bills(bill_ids)
            bill_id_to_object_map = {bill.bill_id: bill for bill in bills.bills}
            order_report_response.extend(
                self._generate_report_details(order_aggregates, bill_id_to_object_map)
            )
        return order_report_response

    def _generate_report_details(self, order_aggregates, bill_id_to_object_map):
        order_report_aggregats = []
        for order_aggregate in order_aggregates:
            bill = bill_id_to_object_map[order_aggregate.bill_id]
            order_report_aggregate = PosOrderReportGenerator(
                order_aggregate, bill
            ).generate()
            if order_report_aggregate:
                order_report_aggregats.append(
                    {
                        "booking_id": order_report_aggregate.booking_id,
                        "seller_id": order_report_aggregate.seller_name,
                        "order_id": order_report_aggregate.order_id,
                        "order_type": order_report_aggregate.order_type,
                        "order_date": order_report_aggregate.order_datetime,
                        "room_number": order_report_aggregate.room_number,
                        "order_status": order_report_aggregate.order_status,
                        "pre_tax_amount": order_report_aggregate.pre_tax_amount,
                        "tax_amount": order_report_aggregate.tax_amount,
                        "total_amount": order_report_aggregate.total_amount,
                    }
                )
        return order_report_aggregats

    def generate_csv_report(self, start_date, end_date, seller_ids, room_number):
        order_aggregates = (
            self.order_repository.pos_order_report_query_for_multiple_sellers(
                start_date, end_date, seller_ids, room_number
            )
        )

        file_path = PosOrderReportGenerator.generate_pos_order_report_file_name()
        bill_id_to_object_map = dict()
        presigned_url = None
        with CsvWriter(file_path) as csv_writer:
            for order_aggregates in chunks(order_aggregates, 1000):
                bill_ids = [
                    order_aggregate.bill_id for order_aggregate in order_aggregates
                ]
                bills = CRSClient.load_bills(bill_ids)
                bill_id_to_object_map.update(
                    {bill.bill_id: bill for bill in bills.bills}
                )
            if order_aggregates:
                self._generate_csv_report(
                    order_aggregates, csv_writer, bill_id_to_object_map
                )
                presigned_url = (
                    AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                        PosOrderReportGenerator.POS_ORDER_REPORT_FOLDER_NAME,
                        csv_writer.file_path,
                        PosOrderReportGenerator.get_default_expiration_time(),
                    )
                )

        return presigned_url

    def _generate_csv_report(self, order_aggregates, csv_writer, bill_id_to_object_map):
        report_aggregates = []
        for order_aggregate in order_aggregates:
            csv_report_aggregates = PosOrderReportGenerator(
                order_aggregate, bill_id_to_object_map[order_aggregate.bill_id]
            ).generate()
            if csv_report_aggregates:
                report_aggregates.append(csv_report_aggregates)

        csv_writer.write_aggregates(
            report_aggregates, PosOrderReportGenerator.REPORT_COLUMNS
        )
