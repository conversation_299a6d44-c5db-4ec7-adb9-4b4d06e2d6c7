"""
Flask command to start Easy Job Lite Workers
"""

import logging

import click
import newrelic
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from object_registry import inject
from pos.core.globals import consumer_context
from pos.domain.reports.erp_job_consumer import ERPServiceConsumer
from pos.infrastructure.database.repositories.catalog.seller_repository import (
    SellerRepository,
)
from pos.infrastructure.database.repositories.order.order_repository import (
    OrderRepository,
)

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@newrelic.agent.background_task()
@inject(
    seller_repo=SellerRepository,
    order_repo=OrderRepository,
)
def start_erp_worker(seller_repo, order_repo, tenant_id):
    """Start the erp job consumers"""
    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.tenant_id = tenant_id

    consumer = ERPServiceConsumer(seller_repo, order_repo, tenant_id)
    consumer.start_consumer()
