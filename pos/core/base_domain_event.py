import abc


class BaseDomainEvent(metaclass=abc.ABCMeta):
    def serialize_event(self):
        return {"event_type": self.event_type().value, "event_detail": self.serialize()}

    @abc.abstractmethod
    def serialize(self):
        raise NotImplementedError(
            "This method needs to be implemented by subclass event"
        )

    @abc.abstractmethod
    def event_type(self):
        raise NotImplementedError(
            "This method needs to be implemented by subclass event"
        )

    @abc.abstractmethod
    def update_mapping(self, **kwargs):
        raise NotImplementedError(
            "This method needs to be implemented by subclass event"
        )


class MergeableDomainEvent(BaseDomainEvent, metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def merge(self, mergeable_domain_event):
        raise NotImplementedError(
            "This method needs to be implemented by subclass event"
        )

    @abc.abstractmethod
    def can_merge(self, mergeable_domain_event):
        raise NotImplementedError(
            "This method needs to be implemented by subclass event"
        )
