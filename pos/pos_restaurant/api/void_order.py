from object_registry import inject
from pos.api import pos_bp
from pos.api.serializers.response.kot import KOTResponseSchema
from pos.common.request_parsers import (
    read_user_data_from_request_header_and_set_context,
)
from pos.core.api_docs import swag_route
from pos.pos_restaurant.application.services.restaurant_order_service import (
    RestaurantOrderApplicationService,
)
from shared_kernel.api_response import ApiResponse


@swag_route
@pos_bp.route('/orders/<string:order_id>/void', methods=['POST'])
@inject(restaurant_order_app_service=RestaurantOrderApplicationService)
def void_order(restaurant_order_app_service, order_id):
    """The order_id of the pos order that needs to be cancelled
    ---
    operationId: void_order
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Mark the order with given order id as voided
        tags:
            - Order
        parameters:
            - in: path
              name: order_id
              description: The order_id of the pos order that needs to be voided
              required: True
              type: string
        responses:
            200:
                description: Voided POS Order object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/OrderResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    read_user_data_from_request_header_and_set_context(default="backend-system")
    kots = restaurant_order_app_service.void_order(order_id)
    kot_response_schema = KOTResponseSchema()
    response = kot_response_schema.dump(kots, many=True)
    return ApiResponse.build(data=response.data, status_code=200)
