from object_registry import inject
from pos.api import pos_bp
from pos.api.serializers.request.order import (
    CreateReservationSchema,
    EditReservationSchema,
    SearchPosReservationSchema,
)
from pos.api.serializers.response.order import ReservationResponseSchema
from pos.application.dtos.reservation_dtos import (
    EditReservationDto,
    SearchReservationQueryDto,
)
from pos.core.api_docs import swag_route
from pos.pos_restaurant.application.reservation_service import ReservationService
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)
from shared_kernel.api_response import ApiResponse


@swag_route
@pos_bp.route('/sellers/<seller_id>/reservations', methods=['POST'])
@schema_wrapper_parser(CreateReservationSchema)
@inject(reservation_service=ReservationService)
def create_reservation(reservation_service, parsed_request, seller_id):
    """Creates a new reservation
    ---
    operationId: create_reservation
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: creates a new reservation
        tags:
            - Reservation
        parameters:
            - in: body
              name: new_reservation
              description: The reservation object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/CreateReservationSchema"
        responses:
            200:
                description: Reservation object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/ReservationResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    reservation = reservation_service.create_reservation(parsed_request, seller_id)
    reservation_response_schema = ReservationResponseSchema()
    response = reservation_response_schema.dump(reservation)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@pos_bp.route('/sellers/<seller_id>/reservations', methods=['GET'])
@schema_wrapper_parser(SearchPosReservationSchema, param_type=RequestTypes.ARGS)
@inject(reservation_service=ReservationService)
def list_reservation(reservation_service, parsed_request, seller_id):
    """List Reservations
    ---
    operationId: list_reservations
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: list reservations
        tags:
            - Reservation
        parameters:
            - in: url
              name: seller_id
              description: Seller id for which reservations list to be fetched
              required: True
        responses:
            200:
                description: Reservation objects.
                schema:
                    type: array
                    properties:
                        data:
                            $ref: "#/definitions/ReservationResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    reservations = reservation_service.get_reservations_for_seller(
        parsed_request, seller_id
    )
    reservation_response_schema = ReservationResponseSchema()
    response = reservation_response_schema.dump(reservations, many=True)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@pos_bp.route('/sellers/<seller_id>/reservations/<reservation_id>', methods=['PATCH'])
@schema_wrapper_parser(EditReservationSchema)
@inject(reservation_service=ReservationService)
def edit_reservation(reservation_service, parsed_request, seller_id, reservation_id):
    """edit reservation
    ---
    operationId: edit_reservation
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: edit reservation
        tags:
            - Reservation
        parameters:
            - in: body
              name: edit_reservation
              description: The reservation object properties which need to be edited
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/EditReservationSchema"
        responses:
            200:
                description: Reservation object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/ReservationResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    reservation = reservation_service.edit_reservation(
        parsed_request, seller_id, reservation_id
    )
    reservation_response_schema = ReservationResponseSchema()
    response = reservation_response_schema.dump(reservation)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@pos_bp.route('/sellers/<seller_id>/reservations/<reservation_id>', methods=['DELETE'])
@inject(reservation_service=ReservationService)
def delete_reservation(reservation_service, seller_id, reservation_id):
    """
    ---
    delete:
        tags:
            - Reservation
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to delete reservation for
              required: True
              schema:
                  type: string
            - name: reservation_id
              in: path
              description: Reservation Id to delete reservation for
              required: True
              schema:
                  type: int
        description: Delete Reservation
        responses:
            204:
                description: The resource was deleted successfully.
    """
    reservation_service.soft_delete_reservation(
        seller_id=seller_id, reservation_id=reservation_id
    )
    return ApiResponse.build(data={}, status_code=204)
