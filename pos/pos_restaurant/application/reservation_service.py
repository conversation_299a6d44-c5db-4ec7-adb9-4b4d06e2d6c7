from object_registry import register_instance
from pos.application.decorators import session_manager
from pos.application.dtos.reservation_dtos import EditReservationDto, NewReservationDto
from pos.core.globals import pos_context
from pos.infrastructure.database.repositories.catalog.seller_repository import (
    SellerRepository,
)
from pos.infrastructure.database.repositories.order.order_repository import (
    OrderRepository,
)
from pos.pos_restaurant.entities.reservation import Reservation
from pos.pos_restaurant.repositories.reservation_repository import ReservationRepository
from ths_common.exceptions import ValidationException
from ths_common.pos.constants.order_constants import PosReservationStatus
from ths_common.value_objects import NotAssigned


@register_instance(
    dependencies=[ReservationRepository, OrderRepository, SellerRepository]
)
class ReservationService(object):
    def __init__(self, reservation_repository, order_repository, seller_repository):
        self.reservation_repository = reservation_repository
        self.order_repository = order_repository
        self.seller_repository = seller_repository

    @session_manager(commit=False)
    def get_next_reservation_for_table(self, table_id):
        return self.reservation_repository.get_next_reservation_for_table(table_id)

    @session_manager(commit=False)
    def get_reservations_for_seller(self, reservation_search_query, seller_id):
        seller_aggregate = self.seller_repository.load(seller_id)
        seller_context = pos_context.set_seller_context(seller_aggregate)
        current_business_date = seller_context.current_business_date
        return self.reservation_repository.get_reservations(
            reservation_search_query, seller_id, current_business_date
        )

    @session_manager(commit=True)
    def create_reservation(self, dto_object: NewReservationDto, seller_id):
        reservation = Reservation(
            reservation_id=self.reservation_repository.next_reservation_id(seller_id),
            table_id=dto_object.table_id,
            start_datetime=dto_object.start_datetime,
            duration=dto_object.duration,
            guests=dto_object.guests,
            guest_count=dto_object.guest_count,
            occasion=dto_object.occasion,
            allergens=dto_object.allergens,
            special_requests=dto_object.special_requests,
            status=PosReservationStatus.CREATED,
            seller_id=seller_id,
        )
        return self.reservation_repository.save(reservation)

    @session_manager(commit=True)
    def edit_reservation(
        self, dto_object: EditReservationDto, seller_id, reservation_id
    ):
        reservation = self.reservation_repository.load(reservation_id, seller_id)

        if (
            dto_object.table_id != NotAssigned
            and reservation.table_id != dto_object.table_id
        ):
            reservation.update_table_id(dto_object.table_id)

        if (
            dto_object.start_datetime != NotAssigned
            and reservation.start_datetime != dto_object.start_datetime
        ):
            reservation.update_start_datetime(dto_object.start_datetime)

        if (
            dto_object.duration != NotAssigned
            and reservation.duration != dto_object.duration
        ):
            reservation.update_duration(dto_object.duration)

        if dto_object.guests != NotAssigned and reservation.guests != dto_object.guests:
            reservation.update_guests(dto_object.guests)

        if (
            dto_object.guest_count != NotAssigned
            and reservation.guest_count != dto_object.guest_count
        ):
            reservation.update_guest_count(dto_object.guest_count)

        if (
            dto_object.occasion != NotAssigned
            and reservation.occasion != dto_object.occasion
        ):
            reservation.update_occasion(dto_object.occasion)

        if (
            dto_object.allergens != NotAssigned
            and reservation.allergens != dto_object.allergens
        ):
            reservation.update_allergens(dto_object.allergens)

        if dto_object.status != NotAssigned and reservation.status != dto_object.status:
            reservation.update_status(PosReservationStatus(dto_object.status))

        if (
            dto_object.special_requests != NotAssigned
            and reservation.special_requests != dto_object.special_requests
        ):
            reservation.update_special_requests(dto_object.special_requests)

        return self.reservation_repository.update(reservation)

    @session_manager(commit=True)
    def soft_delete_reservation(self, seller_id, reservation_id):
        reservation = self.reservation_repository.load(reservation_id, seller_id)
        reservation.delete()
        return self.reservation_repository.update(reservation)
