from sqlalchemy import types as types

from ths_common.value_objects import ItemCode, TaxDetail


class TaxDetailDBType(types.TypeDecorator):
    """
    Contains tax details
    """

    impl = types.Unicode

    def process_bind_param(self, value, dialect):
        return str(value)

    def process_result_value(self, value, dialect):
        return TaxDetail.from_string(value)

        # def copy(self):
        #     return TaxDetail(self.impl.length)


class ItemCodeDBType(types.TypeDecorator):
    """
    Contains item code
    """

    impl = types.Unicode

    def process_bind_param(self, value, dialect):
        return str(value)

    def process_result_value(self, value, dialect):
        return ItemCode.from_string(value)

        # def copy(self):
        #     return ItemCode(self.impl.length)
