from object_registry import register_instance
from pos.infrastructure.database.base_repository import BaseRepository
from pos.models import OrderNumberSequenceModel


@register_instance()
class OrderNumberRepository(BaseRepository):
    def from_aggregate(self, aggregate=None):
        pass

    def to_aggregate(self, **kwargs):
        pass

    def get_next_order_number(self, seller_id, order_date):
        sequence = self.get_for_update(
            OrderNumberSequenceModel, seller_id=seller_id, order_date=order_date
        )
        if not sequence:
            # Lock Seller model
            sequence = self.get_for_update(
                OrderNumberSequenceModel, seller_id=seller_id, order_date=order_date
            )
            if not sequence:
                order_number = 1
                new_sequence = OrderNumberSequenceModel(
                    seller_id=seller_id,
                    order_date=order_date,
                    last_order_number=order_number,
                )
                self._save(new_sequence)
            else:
                order_number = sequence.last_order_number + 1
                sequence.last_order_number = order_number
                self._update(sequence)
        else:
            order_number = sequence.last_order_number + 1
            sequence.last_order_number = order_number
            self._update(sequence)

        self.flush_session()
        return order_number
