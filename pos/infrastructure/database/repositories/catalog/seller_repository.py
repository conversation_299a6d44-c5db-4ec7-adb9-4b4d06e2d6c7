from object_registry import register_instance
from pos.domain.catalog.aggregates.seller_aggregate import SellerAggregate
from pos.infrastructure.database.base_repository import BaseRepository
from pos.infrastructure.database.dtos.erp_event_details_dto import ERPSellerDetailsDTO
from pos.infrastructure.database.repositories.catalog.db_adaptors.seller_adaptor import (
    SellerAdaptor,
)
from shared_kernel.infrastructure.database.common_models import SellerModel


@register_instance()
class SellerRepository(BaseRepository):
    seller_adaptor = SellerAdaptor()

    def from_aggregate(self, aggregate: SellerAggregate = None):
        seller = aggregate.seller
        seller_model = self.seller_adaptor.to_db_entity(seller)
        return seller_model

    def to_aggregate(self, **kwargs):
        seller_model = kwargs.get('seller_model')
        seller = self.seller_adaptor.to_domain_entity(seller_model)
        return SellerAggregate(seller)

    def load(self, seller_id):
        seller_model = (
            self.query(SellerModel).filter(SellerModel.seller_id == seller_id).one()
        )
        return self.to_aggregate(seller_model=seller_model)

    def get_sellers_for_hotel(self, hotel_id):
        q = self.query(
            SellerModel.seller_id, SellerModel.name, SellerModel.state_name
        ).filter(SellerModel.hotel_id == hotel_id)
        sellers = q.all()
        seller_details = [ERPSellerDetailsDTO(seller).__dict__ for seller in sellers]
        return seller_details
