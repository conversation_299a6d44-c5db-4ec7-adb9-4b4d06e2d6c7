from typing import List

from treebo_commons.money.money import Money
from treebo_commons.utils import dateutils

from pos.domain.order.aggregates.order_aggregate import OrderAggregate
from pos.domain.order.entities.order_item import OrderItem
from ths_common.constants.billing_constants import (
    BillAppId,
    ChargeSplitType,
    ChargeStatus,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import ExpenseAddedBy
from ths_common.pos.constants.order_constants import PosOrderStatus
from ths_common.value_objects import NotAssigned
from thsc.crs.entities.billing import Bill, Charge, ChargeItem, ChargeSplit, Payment
from thsc.crs.entities.booking import Booking, Expense, Price


class CRSClient(object):
    def __init__(self):
        pass

    @staticmethod
    def setup_context():
        from thsc.crs import context

        context.application = "point-of-sale"

    @classmethod
    def get_charge_splits(cls, order_item):
        charge_splits = []
        total_charge_to = len(order_item.charge_to)
        percentage = 100.0 / total_charge_to
        for cto in order_item.charge_to:
            charge_splits.append(ChargeSplit(charge_to=cto, percentage=percentage))
        return charge_splits

    @classmethod
    def create_bill(
        cls, order_id, order_aggregate: OrderAggregate = None, charges=None
    ):
        cls.setup_context()

        parent_info = {
            "order_id": order_aggregate.order.order_id,
            "order_number": order_aggregate.order.order_number,
            "table_id": order_aggregate.order.table_id,
            "room_number": order_aggregate.order.room_number,
        }
        if not charges:
            charges = []
            for order_item in order_aggregate.order_items:
                charge_item = ChargeItem(
                    name=order_item.sku_vo.item_name,
                    sku_category_id=order_item.sku_vo.sku_category_code,
                    details=order_item.sku_vo.item_detail
                    if order_item.sku_vo.item_detail
                    else {},
                    item_code="",
                )
                charge_item.details.update({"quantity": order_item.quantity})
                charge_item.details.update({"order_item_id": order_item.order_item_id})
                charge_splits = []
                charge_splits = cls.get_charge_splits(order_item=order_item)

                pretax_amount = order_item.total_price_pretax
                posttax_amount = order_item.total_price_posttax

                if order_item.is_complimentary:
                    if pretax_amount:
                        pretax_amount = Money("0", order_aggregate.order.base_currency)
                    if posttax_amount:
                        posttax_amount = Money("0", order_aggregate.order.base_currency)
                    charge_item.name = "COMP.{}".format(charge_item.name)

                # Iterate and calculate sum of all discounts for a particular order_item and subtract it from the pretax price
                elif order_item.discounts:
                    discount_to_subtract = sum(
                        discount.amount for discount in order_item.discounts
                    )
                    pretax_amount = pretax_amount - discount_to_subtract
                    charge_item.name = "DISC.{}".format(charge_item.name)

                charges.append(
                    Charge.create_instance(
                        bill_to_type=order_item.bill_to_type,
                        applicable_date=dateutils.current_datetime(),
                        type=order_item.charge_type,
                        status=ChargeStatus.CREATED,
                        created_at=NotAssigned,
                        pretax_amount=pretax_amount,
                        posttax_amount=pretax_amount,
                        comments=None,
                        item=charge_item,
                        charge_splits=charge_splits,
                        charge_split_type=ChargeSplitType.EQUAL_SPLIT,
                    )
                )
        else:
            for charge in charges:
                order_item = order_aggregate.get_order_item_by_id(
                    order_item_id=charge.item.details.get("order_item_id")
                )
                charge.charge_splits = cls.get_charge_splits(order_item=order_item)

        bill = Bill.create_instance(
            bill_date=dateutils.current_datetime(),
            app_id=BillAppId.POS_APP.value,
            parent_reference_number=order_id,
            vendor_id=order_aggregate.order.seller_vo.seller_id,
            vendor_details=order_aggregate.order.seller_vo.seller_details.to_json(),
            charges=charges,
            parent_info=parent_info,
        )
        bill = bill.create()
        return bill

    @classmethod
    def create_bill_with_no_charges(cls, order_aggregate: OrderAggregate):
        cls.setup_context()

        parent_info = {
            "order_id": order_aggregate.order.order_id,
            "order_number": order_aggregate.order.order_number,
            "table_id": order_aggregate.order.table_id,
            "room_number": order_aggregate.order.room_number,
        }
        charges = []
        bill = Bill.create_instance(
            bill_date=dateutils.current_datetime(),
            app_id=BillAppId.POS_APP.value,
            parent_reference_number=order_aggregate.order_id,
            vendor_id=order_aggregate.order.seller_vo.seller_id,
            vendor_details=order_aggregate.order.seller_vo.seller_details.to_json(),
            charges=charges,
            parent_info=parent_info,
        )
        bill = bill.create()
        return bill

    @classmethod
    def cancel_charge(cls, bill, charge_id):
        cls.setup_context()
        charge = Charge.create_instance_for_update(charge_id)
        charge.status = ChargeStatus.CANCELLED
        charge.posttax_amount = NotAssigned
        charge = bill.update_charge(charge)
        return charge

    @classmethod
    def cancel_charges(cls, bill_id, charge_ids):
        cls.setup_context()
        charges = []
        for charge_id in charge_ids:
            charge = Charge.create_instance_for_update(charge_id)
            charge.status = ChargeStatus.CANCELLED
            charges.append(charge)
        bill = Bill.get(bill_id)
        charges = bill.update_charges(charges)
        return charges

    @classmethod
    def load_bill(cls, bill_id):
        cls.setup_context()
        bill = Bill.get(bill_id)
        return bill

    @classmethod
    def load_bills(cls, bill_ids):
        cls.setup_context()
        bill = Bill.get_bills(bill_ids)
        return bill

    @classmethod
    def void_bill(cls, bill_id):
        cls.setup_context()
        bill = Bill.get(bill_id=bill_id)
        bill.void()

    @classmethod
    def create_charge(
        cls,
        order_item,
        bill,
        charge_splits=None,
        charge_split_type=None,
        base_currency=None,
    ):
        cls.setup_context()
        charge_item = ChargeItem(
            name=order_item.item_name,
            sku_category_id=order_item.sku_category_code,
            details=order_item.sku_vo.item_detail
            if order_item.sku_vo.item_detail
            else {},
            item_code="",
        )
        charge_item.details.update({"quantity": order_item.quantity})
        charge_item.details.update({"order_item_id": order_item.order_item_id})

        if not charge_splits and not charge_split_type:
            charge_splits = cls.get_charge_splits(order_item=order_item)
            charge_split_type = ChargeSplitType.EQUAL_SPLIT

        pretax_amount = (
            order_item.total_price_pretax
            if order_item.total_price_pretax
            else NotAssigned
        )
        posttax_amount = (
            order_item.total_price_posttax
            if order_item.total_price_posttax
            else NotAssigned
        )

        if order_item.is_complimentary:
            if pretax_amount:
                pretax_amount = Money("0", base_currency)
            if posttax_amount:
                posttax_amount = Money("0", base_currency)
            charge_item.name = "COMP.{}".format(order_item.item_name)

        # Iterate and calculate sum of all discounts for a particular order_item and subtract it from the pretax price
        elif order_item.discounts:
            discount_to_subtract = sum(
                discount.amount for discount in order_item.discounts
            )
            pretax_amount = pretax_amount - discount_to_subtract
            charge_item.name = "DISC.{}".format(order_item.item_name)

        # TODO: Application Date should be passed in order_item
        charge = Charge.create_instance(
            bill_to_type=order_item.bill_to_type,
            applicable_date=dateutils.current_datetime(),
            type=order_item.charge_type,
            status=ChargeStatus.CREATED,
            created_at=NotAssigned,
            pretax_amount=pretax_amount,
            posttax_amount=posttax_amount,
            comments=None,
            item=charge_item,
            charge_splits=charge_splits,
            charge_split_type=charge_split_type,
        )

        charge = bill.add_charge(charge)
        return charge

    @classmethod
    def update_charges(
        cls,
        bill,
        updated_order_items: List[OrderItem] = None,
        charges_to_update=None,
        order_aggregate=None,
        consume=False,
    ):
        cls.setup_context()
        if charges_to_update:
            for charge in charges_to_update:
                order_item = order_aggregate.get_order_item_by_id(
                    order_item_id=charge.item.details.get("order_item_id")
                )
                charge_splits = cls.get_charge_splits(order_item=order_item)
                charge.posttax_amount = NotAssigned
                if consume:
                    charge.status = ChargeStatus.CONSUMED
                charge.charge_splits = charge_splits
        elif updated_order_items:
            charges_to_update = []
            for order_item in updated_order_items:
                is_complimentary = order_item.is_complimentary
                charge_ids = [
                    charge_id
                    for order_item_bill in order_item.bills
                    for charge_id in order_item_bill.charge_ids
                    if order_item_bill.bill_id == bill.bill_id
                ]

                for charge_id in charge_ids:
                    discount_to_be_subtracted = None
                    # sum up all discounts for the order_item
                    if order_item.discounts and not is_complimentary:
                        discount_to_be_subtracted = sum(
                            discount.amount for discount in order_item.discounts
                        )
                    charge = Charge.create_instance_for_update(charge_id)
                    if consume:
                        charge.status = ChargeStatus.CONSUMED
                    charge.bill_to_type = order_item.bill_to_type
                    charge.type = order_item.charge_type
                    charge_item_name = order_item.item_name

                    if order_item.total_price_pretax:
                        charge.pretax_amount = order_item.total_price_pretax
                        if is_complimentary:
                            charge.pretax_amount = Money(
                                "0", order_aggregate.order.base_currency
                            )
                            charge_item_name = "COMP.{}".format(order_item.item_name)
                        elif discount_to_be_subtracted:
                            charge.pretax_amount -= discount_to_be_subtracted
                            charge_item_name = "DISC.{}".format(order_item.item_name)
                    elif order_item.total_price_posttax:
                        charge.posttax_amount = order_item.total_price_posttax

                    charge_splits = cls.get_charge_splits(order_item=order_item)
                    charge.charge_splits = charge_splits
                    charge_item = ChargeItem(
                        name=charge_item_name,
                        sku_category_id=order_item.sku_category_code,
                        details=order_item.sku_vo.item_detail
                        if order_item.sku_vo.item_detail
                        else {},
                        item_code="",
                    )
                    charge_item.details.update({"quantity": order_item.quantity})
                    charge_item.details.update(
                        {"order_item_id": order_item.order_item_id}
                    )
                    charge.item = charge_item
                    charges_to_update.append(charge)

        updated_charges = (
            bill.update_charges(charges_to_update)
            if charges_to_update
            else charges_to_update
        )
        return updated_charges

    @staticmethod
    def get_charge_from_bill(bill):
        for charge in bill.charges:
            if charge.status in [ChargeStatus.CONSUMED, ChargeStatus.CREATED]:
                return charge
        return None

    @classmethod
    def cancel_charges_for_order_item(cls, bill, order_item_id):
        cls.setup_context()
        charges = []

        for charge in bill.charges:
            if (
                charge.item
                and charge.item.details.get("order_item_id") == order_item_id
            ):
                charge = Charge.create_instance_for_update(charge.charge_id)
                charge.status = ChargeStatus.CANCELLED
                charge.posttax_amount = NotAssigned
                charges.append(charge)
        return bill.update_charges(charges)

    @staticmethod
    def get_consumed_charges_from_bill(bill):
        return [
            charge for charge in bill.charges if charge.status == ChargeStatus.CONSUMED
        ]

    @staticmethod
    def get_created_charges_from_bill(bill):
        return [
            charge for charge in bill.charges if charge.status == ChargeStatus.CREATED
        ]

    @classmethod
    def add_payments(cls, bill, payments_data: List):
        cls.setup_context()
        payments = []
        for payment_data in payments_data:
            payment = Payment.create_instance(
                amount=payment_data['amount'],
                amount_in_payment_currency=payment_data['amount_in_payment_currency'],
                payment_mode=payment_data['payment_mode'],
                status=PaymentStatus(payment_data['status']),
                payment_mode_sub_type=payment_data.get('payment_mode_sub_type'),
                date_of_payment=dateutils.current_datetime(),
                payment_type=PaymentTypes.PAYMENT,
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                comment=payment_data.get('comment'),
            )
            payments.append(bill.add_payment(payment))
        return payments

    @classmethod
    def generate_invoices(
        cls,
        bill,
        charge_ids,
        bill_to_info,
        user_info_map,
        status,
        issued_to_type,
        issued_by_type,
        allow_mixed_charge_type_in_invoice=None,
    ):
        cls.setup_context()
        invoices = bill.generate_invoices(
            charge_ids,
            bill_to_info,
            user_info_map,
            status,
            issued_to_type,
            issued_by_type,
            allow_mixed_charge_type_in_invoice,
        )
        return invoices

    @staticmethod
    def print_bill(bill_id):
        url = Bill.print_bill(bill_id)
        return url

    @classmethod
    def add_expense(cls, booking_id, resource_version, expense):
        cls.setup_context()
        return Booking().add_expense(booking_id, resource_version, expense)

    @staticmethod
    def calculate_applicable_date(assigned_to_details, seller_curent_datetime):
        checkin_date = max(
            assigned_to['actual_checkin_date'] for assigned_to in assigned_to_details
        )
        checkout_dates = [
            assigned_to.get('actual_checkout_date')
            for assigned_to in assigned_to_details
            if assigned_to.get('actual_checkout_date')
        ]

        checkout_date = min(checkout_dates) if checkout_dates else None
        if not checkout_date:
            checkout_date = seller_curent_datetime
            return checkout_date

        return max(checkin_date, checkout_date)

    @staticmethod
    def create_expense(
        settle_order_request,
        charge,
        order_item,
        booking_resource_version,
        extra_information,
        seller_curent_datetime,
    ):
        assigned_to = [
            assigned_to['guest_id']
            for assigned_to in settle_order_request.room_booking_details['assigned_to']
        ]
        room_booking_details = settle_order_request.room_booking_details
        sku_id = order_item.sku_id

        expense = Expense(
            added_by=ExpenseAddedBy.POS,
            room_stay_id=room_booking_details['room_stay_id'],
            sku_id=sku_id,
            expense_item_id=sku_id,
            assigned_to=assigned_to,
            comments=charge.comments,
            price=Price(
                applicable_date=CRSClient.calculate_applicable_date(
                    room_booking_details['assigned_to'], seller_curent_datetime
                ),
                bill_to_type=charge.bill_to_type,
                type=charge.type,
                pretax_amount=charge.pretax_amount,
                posttax_amount=charge.posttax_amount,
            ),
            extra_information=extra_information,
        )

        return CRSClient.add_expense(
            booking_id=settle_order_request.room_booking_details['crs_booking_id'],
            resource_version=booking_resource_version,
            expense=expense,
        )

    @staticmethod
    def get_booking(booking_id):
        return Booking.get(booking_id)

    @classmethod
    def load_invoices(cls, invoice_ids):
        cls.setup_context()
        invoices = Bill.get_invoices(invoice_ids)
        return invoices
