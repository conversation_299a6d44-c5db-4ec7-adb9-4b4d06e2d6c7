# coding=utf-8
"""
Queue service
"""
import logging
from abc import ABC, abstractmethod

from kombu import Connection
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient

logger = logging.getLogger(__name__)


class BaseQueueService(ABC):
    """
    Queue service
    """

    def __init__(self):
        self.tenant_wise_connection = dict()
        for tenant in TenantClient.get_active_tenants():
            tenant_id = tenant.tenant_id
            try:
                rmq_url = AwsSecretManager.get_rmq_url(tenant_id)

            except Exception as e:
                logger.exception(
                    "Can't retrieve RMQ credentials for tenant_id: %s. Skipping",
                    tenant_id,
                )
                continue

            self.tenant_wise_connection[tenant_id] = Connection(
                rmq_url, heartbeat=5, transport_options={'confirm_publish': True}
            )
        self._init_complete = False

    def _initialize(self):
        if not self._init_complete:
            self._setup_entities()
            self._init_complete = True

    @abstractmethod
    def _setup_entities(self):
        # Setup Exchange and queues
        pass

    def _publish(self, producer, payload, routing_key=None):
        producer.publish(
            body=payload,
            routing_key=routing_key,
            retry_policy={
                'interval_start': 0,
                'interval_step': 2,
                'interval_max': 30,
                'max_retries': 3,
            },
            retry=True,
        )
