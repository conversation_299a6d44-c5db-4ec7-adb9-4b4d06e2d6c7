from pos.domain.catalog.dtos.seller_config_dto import SellerConfigDto
from pos.domain.catalog.entities.seller import Seller


class SellerAggregate(object):
    def __init__(self, seller: Seller):
        self.seller = seller

    @property
    def seller_config(self):
        if self.seller.seller_config:
            priority_multiplier = (
                float(self.seller.seller_config.get("priority_multiplier"))
                if self.seller.seller_config.get("priority_multiplier")
                else None
            )

            return SellerConfigDto(
                acceptance_time=self.seller.seller_config.get("acceptance_time"),
                delivery_time=self.seller.seller_config.get("delivery_time"),
                settlement_time=self.seller.seller_config.get("settlement_time"),
                priority_multiplier=priority_multiplier,
                order_item_cancellation_time=self.seller.seller_config.get(
                    "order_item_cancellation_time"
                ),
                generate_kot_only_when_item_is_preparing=self.seller.seller_config.get(
                    "generate_kot_only_when_item_is_preparing"
                ),
            )

        return SellerConfigDto()
