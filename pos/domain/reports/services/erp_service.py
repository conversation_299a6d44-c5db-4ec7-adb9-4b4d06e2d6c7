from pos.domain.reports.dtos.erp_events_details_dto import (
    ErpChargeDetailsDto,
    ERPEventDto,
    ErpPaymentDetailsDto,
    HotelDetailsDto,
    InvoiceDto,
    SellerDto,
    SkuCategoryDto,
)
from pos.domain.reports.erp_event_publisher import ERPEvent, ERPEventPublisher
from pos.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from pos.infrastructure.external_clients.crs_client import CRSClient


class ERPService:
    def __init__(self, seller_repo, order_repo):
        self.seller_repo = seller_repo
        self.order_repo = order_repo

    def prepare_and_publish_erp_details(
        self, hotel_id, business_date, event_id, batch_id
    ):
        sellers = self.seller_repo.get_sellers_for_hotel(hotel_id)
        seller_id = [seller.get('seller_id') for seller in sellers]

        orders = self.order_repo.get_settled_orders_for_seller(seller_id, business_date)
        bill_ids = [order.get('bill_id') for order in orders]

        bills = CRSClient.load_bills(bill_ids)
        charges, payments, allowances, refunds, folio_details = (
            list(),
            list(),
            list(),
            list(),
            list(),
        )
        for bill in bills.bills:
            bill_id = bill.bill_id
            for charge in bill.charges:
                charge_data = ErpChargeDetailsDto(
                    bill_id=bill_id,
                    sku_category_id=charge.item.sku_category_id,
                    pre_tax=str(charge.pretax_amount.amount),
                    tax_details=charge.tax_details,
                    charge_type=charge.type.value,
                    invoice_id=charge.charge_splits[0].invoice_id,
                )
                charges.append(charge_data.__dict__)
            for payment in bill.payments:
                payment_data = ErpPaymentDetailsDto(
                    bill_id=bill_id,
                    date_of_payment=payment.date_of_payment,
                    amount=str(payment.amount.amount),
                    payment_mode=payment.payment_mode,
                    payment_ref_id=payment.payment_ref_id,
                )
                payments.append(payment_data.__dict__)
        invoice_ids = {
            charge.get('invoice_id') for charge in charges if charge.get('invoice_id')
        }
        invoice_numbers = CRSClient.load_invoices(','.join(invoice_ids))
        seller_ids = {order.get('seller_id') for order in orders}
        invoice_details = [
            InvoiceDto(
                in_number.bill_id, in_number.invoice_id, in_number.invoice_number
            ).__dict__
            for in_number in invoice_numbers
        ]
        seller_details = [
            SellerDto(
                seller.get('seller_id'), seller.get('name'), seller.get('state')
            ).__dict__
            for seller in sellers
            for sid in seller_ids
            if seller.get('seller_id') == sid
        ]
        sku_category_response = CatalogServiceClient().fetch_sku_category()
        sku_categories = {charge.get('sku_category_id') for charge in charges}
        sku_category_details = [
            SkuCategoryDto(scr.get('code'), scr.get('hsn_sac')).__dict__
            for sc in sku_categories
            for scr in sku_category_response
            if sc == scr.get('code')
        ]
        hotel_details = (
            HotelDetailsDto(hotel_id, seller_details[0].get('seller_state')).__dict__
            if seller_details
            else dict()
        )
        pos_erp_event = ERPEventDto(
            charges,
            allowances,
            payments,
            refunds,
            folio_details,
            hotel_details,
            sku_category_details,
            seller_details,
            orders,
            invoice_details,
        ).__dict__
        erp_event = ERPEvent(
            body=dict(erp_data=pos_erp_event),
            routing_key="pos.erp.interface_exchange",
            event_type='erp-pos',
            event_id=event_id,
            batch_id=batch_id,
            source='pos',
        )
        ERPEventPublisher().publish(erp_event)
