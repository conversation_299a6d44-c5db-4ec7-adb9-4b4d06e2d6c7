import logging

from flask.cli import with_appcontext
from sentry_sdk.integrations.serverless import serverless_function

from pos.common.decorators import consumer_middleware
from pos.domain.reports.services.erp_service import ERPService
from pos.infrastructure.consumers.base_consumer import BaseRMQConsumer
from pos.infrastructure.consumers.consumer_config import InterfaceExchangeConsumerConfig

logger = logging.getLogger(__name__)


class ERPServiceConsumer(BaseRMQConsumer):
    def __init__(self, seller_repo, order_repo, tenant_id):
        super().__init__(InterfaceExchangeConsumerConfig(tenant_id))
        self.seller_repo = seller_repo
        self.order_repo = order_repo
        logger.info(
            f"Listening to RMQ on host: {self.connection} from queue: {self.queue}"
        )

    @serverless_function
    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        logger.info(f"Processing erp event {body}")
        try:
            payload = body.get('body')
            logger.info(f"Received erp pos Request with body: {payload}")
            if body.get('event_type') == 'pos-erp-data':
                hotel_id = payload.get('hotel_id')
                business_date = payload.get('business_date')
                module = payload.get('module_name')
                if module == 'pos':
                    erp_service = ERPService(self.seller_repo, self.order_repo)
                    logger.info("preparing erp data for pos")
                    erp_service.prepare_and_publish_erp_details(
                        hotel_id,
                        business_date,
                        body.get('event_id'),
                        body.get('batch_id'),
                    )

        except Exception as exc:
            logger.exception(f"Exception occurred while processing erp event: {body}")
            raise

        logger.info("erp message process complete. Message acknowledged")
        message.ack()
