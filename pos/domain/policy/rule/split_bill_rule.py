from pos.domain.policy.errors import PolicyError
from pos.domain.policy.facts.facts import Facts
from pos.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class SplitBillRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.CAN_SPLIT_BILL_FOR_ORDER not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_SPLIT_BILL_FOR_ORDER
            )
        return True
