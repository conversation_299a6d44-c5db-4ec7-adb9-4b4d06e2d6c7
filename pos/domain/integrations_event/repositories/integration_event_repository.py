import json

from object_registry import register_instance
from pos.domain.integrations_event.aggregates.integration_event_aggregate import (
    IntegrationEventAggregate,
)
from pos.domain.integrations_event.entities.integration_event_entity import (
    POSIntegrationEventEntity,
)
from pos.domain.integrations_event.models import IntegrationEventModel
from pos.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.integration_event_constants import (
    IntegrationEventStatus,
    IntegrationEventType,
)
from ths_common.utils.common_utils import json_dumps


class POSIntegrationEventAdapter:
    @staticmethod
    def to_db_model(event_entity):
        integration_event = IntegrationEventModel()
        integration_event.event_id = event_entity.event_id
        integration_event.seller_id = event_entity.seller_id
        integration_event.generated_at = event_entity.generated_at
        # Dumping and loading to get a dict that can be converted into json
        # TODO this is ugly fix
        integration_event.body = json.loads(json_dumps(event_entity.body))
        integration_event.event_type = event_entity.event_type.value
        integration_event.status = event_entity.status.value
        integration_event.user_action = event_entity.user_action
        integration_event.order_id = event_entity.order_id
        return integration_event

    @staticmethod
    def to_entity(event_model):
        return POSIntegrationEventEntity(
            IntegrationEventType(event_model.event_type),
            event_model.seller_id,
            event_model.generated_at,
            IntegrationEventStatus(event_model.status),
            event_model.body,
            event_model.event_id,
            order_id=event_model.order_id,
            user_action=event_model.user_action,
        )


@register_instance()
class IntegrationEventRepository(BaseRepository):
    def to_aggregate(self, integration_event_model, **kwargs):
        entity = POSIntegrationEventAdapter.to_entity(integration_event_model)
        return IntegrationEventAggregate(entity)

    def from_aggregate(self, aggregate=None):
        return POSIntegrationEventAdapter.to_db_model(aggregate.pos_integration_event)

    def get_oldest_unpublished_event(self):
        integration_event_model = (
            self.session()
            .query(IntegrationEventModel)
            .filter(
                IntegrationEventModel.status.in_(
                    (
                        IntegrationEventStatus.UNPUBLISHED.value,
                        IntegrationEventStatus.FAILED.value,
                    )
                )
            )
            .order_by(IntegrationEventModel.generated_at.asc())
            .first()
        )
        if not integration_event_model:
            return None
        return self.to_aggregate(integration_event_model=integration_event_model)

    def filter_events(self, hotels_ids, event_types):
        integration_event_models = (
            self.session()
            .query(IntegrationEventModel)
            .filter(IntegrationEventModel.hotel_id.in_(hotels_ids))
            .filter(IntegrationEventModel.event_type.in_(event_types))
            .order_by(IntegrationEventModel.generated_at.asc())
            .all()
        )
        if not integration_event_models:
            return None
        return [
            self.to_aggregate(integration_event_model=integration_event_model)
            for integration_event_model in integration_event_models
        ]

    def save(self, event_aggregate):
        integration_event_model = self.from_aggregate(event_aggregate)
        self._save(integration_event_model)
        self.flush_session()

    def update(self, event_aggregate):
        integration_event_model = self.from_aggregate(event_aggregate)
        self._update(integration_event_model)
        self.flush_session()

    def count_unpublished_events(self):
        event_count = self.filter(
            IntegrationEventModel,
            IntegrationEventModel.status.in_(
                (
                    IntegrationEventStatus.UNPUBLISHED.value,
                    IntegrationEventStatus.FAILED.value,
                )
            ),
        ).count()
        return event_count

    def get_all_unpublished_events(self):
        events = (
            self.filter(
                IntegrationEventModel,
                IntegrationEventModel.status.in_(
                    (
                        IntegrationEventStatus.UNPUBLISHED.value,
                        IntegrationEventStatus.FAILED.value,
                    )
                ),
            )
            .order_by(IntegrationEventModel.generated_at.asc())
            .all()
        )

        return [self.to_aggregate(integration_event_model=event) for event in events]
