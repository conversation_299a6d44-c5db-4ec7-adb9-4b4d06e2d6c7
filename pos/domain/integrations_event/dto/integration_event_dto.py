from ths_common.constants.integration_event_constants import IntegrationEventType


class IntegrationEventDTO:
    def __init__(
        self, event_type: IntegrationEventType, seller_id, generated_at, body, order_id
    ):
        """
        :param event_type (IntegrationEventType): the type of event
        :param seller_id: Seller of the event
        :param generated_at: The time at which the event happend. Usually the 'modified_at' of the aggregate
        :param body: Dict
        """
        self.body = body
        self.seller_id = seller_id
        self.event_type = event_type
        self.generated_at = generated_at
        self.order_id = order_id
        self._user_action = None

    @property
    def user_action(self):
        return self._user_action

    @user_action.setter
    def user_action(self, value):
        self._user_action = value
