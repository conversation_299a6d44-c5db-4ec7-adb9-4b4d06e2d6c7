from ths_common.exceptions import CRSError


class OrderError(CRSError):
    DEFAULT_ERROR = (
        "0100",
        "Something went wrong. Please try again, or contact escalation",
    )
    MIXED_BILL_TO_TYPE_UNSUPPORTED = (
        "0101",
        "POS Order cannot contain order_items with different bill_to_types",
    )
    ORDER_ALREADY_SETTLED = "0102", "Order is already settled"
    CANNOT_SETTLE_CANCELLED_ORDER = "0103", "Order is cancelled. Can't settle."
    CANNOT_CANCEL_CANCELLED_ORDER = "0104", "Order is already cancelled"
    CANNOT_CANCEL_SETTLED_ORDER = "0105", "Order is settled. Can't cancel"
    ONLY_PREPARING_ORDERS_CAN_BE_MARKED_READY = (
        "0106",
        "Only order items that are preparing can be marked ready",
    )
    ONLY_READY_ORDERS_CAN_BE_MARKED_DELIVERED = (
        "0107",
        "Only order items that are ready can be marked delivered",
    )
    ORDER_ITEMS_SENT_TO_KITCHEN_CANNOT_BE_REMOVED = (
        "0108",
        "Order Items sent to kitchen cannot be removed",
    )
    CANNOT_CANCEL_VOIDED_ORDER = "0109", "Order is voided. Can't cancel"
    CANNOT_SETTLE_VOIDED_ORDER = "0110", "Order is voided. Can't settle"
    CANNOT_VOID_VOIDED_ORDER = "0111", "Order is voided. Can't void"
    CANNOT_VOID_CANCELLED_ORDER = "0112", "Order is cancelled. Can't void"
    CANNOT_VOID_SETTLED_ORDER = "0113", "Order is setlled. Can't void"
    READY_OR_DELIVERED_ORDER_ITEMS_CANNOT_BE_REMOVED = (
        "0114",
        "Order Items which are ready or delivered cannot be removed",
    )


class OrderSplitBillError(CRSError):
    BILL_DOES_NOT_EXIST = "0115", "Bill does not exist"
    FOOD_AND_ALCOHOL_BILLS_CANNOT_BE_SPLIT_FURTHER = (
        "0116",
        "Food & Alcohol Split cannot be split further with the same type",
    )
    AMOUNT_IN_SPLITS_IS_NOT_EQUAL_TO_BILL_AMOUNT = (
        "0117",
        "Total amount in splits is not equal to the total amount in bill",
    )
    BILL_DOES_NOT_HAVE_ACTIVE_CHARGES = (
        "0118",
        "Bill does not have active charges to be split",
    )
    BILL_DOES_NOT_HAVE_ALCOHOLIC_ITEMS = (
        "0119",
        "Bill cannot be split since it doesn't have 'alcohol' category items",
    )
    BILL_DOES_NOT_HAVE_MULTIPLE_UNIQUE_ITEMS = (
        "0120",
        "Bill can't be split since it just has a single unique item.",
    )
    SETTLED_BILL_CANNOT_BE_SPLIT = "0121", "Settled bill cannot be split further"
