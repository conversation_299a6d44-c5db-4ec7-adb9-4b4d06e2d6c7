from copy import deepcopy
from datetime import datetime, timedelta
from typing import List

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from pos.application.dtos.edit_order_dto import EditPosCustomerDto, OrderRemarkDto
from pos.application.dtos.new_order_dto import NewPosCustomerDto
from pos.core.globals import pos_context
from pos.domain.order.entities.bill import SplitBill
from pos.domain.order.entities.order import Order
from pos.domain.order.entities.order_item import OrderItem
from pos.domain.order.entities.order_remark import OrderRemark
from pos.domain.order.entities.pos_customer import PosCustomer
from pos.domain.order.errors import OrderError
from pos.domain.order.exceptions import OrderInvarianceException
from pos.domain.order.value_objects.discount import Discount
from pos.domain.order.value_objects.sku import SkuVO
from pos.domain.order.value_objects.split_bill import OrderItemBill
from pos.domain.policy.engine import RuleEngine
from pos.domain.policy.facts.facts import Facts
from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes
from ths_common.exceptions import ResourceNotFound
from ths_common.pos.constants.order_constants import (
    PosBillStatus,
    PosOrderItemStatus,
    PosOrderPriorities,
    PosOrderStatus,
    PosSplitBillSplitType,
)
from ths_common.value_objects import NotAssigned


class OrderAggregate(object):
    def __init__(
        self,
        order: Order,
        order_items: List[OrderItem],
        customers: List[PosCustomer] = None,
        split_bills: List[SplitBill] = None,
        order_remarks: List[OrderRemark] = None,
    ):
        self.order = order
        self._order_items = order_items
        self._customers = []
        self._split_bills = split_bills
        self._order_remarks = order_remarks

        if customers:
            self._customers = customers
            self.customer_dict = {
                c.customer_id: c for c in self._customers if not c.deleted
            }

        if order_remarks:
            self._order_remark_dict = {
                order_remark.remark_id: order_remark
                for order_remark in self._order_remarks
            }

        self.order_item_dict = {
            order_item.order_item_id: order_item
            for order_item in self._order_items
            if not order_item.deleted
        }
        self.split_bill_dict = (
            {bill.bill_id: bill for bill in self._split_bills if not bill.deleted}
            if self._split_bills
            else {}
        )

    def check_invariance(self):
        distinct_bill_to_types = {item.bill_to_type for item in self.order_items}

        if len(distinct_bill_to_types) > 1:
            raise OrderInvarianceException(
                error=OrderError.MIXED_BILL_TO_TYPE_UNSUPPORTED
            )

    @property
    def order_items(self):
        return [item for item in self._order_items if not item.deleted]

    @property
    def order_remarks(self):
        return [remark for remark in self._order_remarks if not remark.deleted]

    @property
    def split_bills(self):
        return (
            [bill for bill in self._split_bills if not bill.deleted]
            if self._split_bills
            else []
        )

    def get_order_items(self, include_deleted=True):
        return self._order_items if include_deleted else self.order_items

    def get_order_remarks(self, include_deleted=True):
        return self._order_remarks if include_deleted else self.order_remarks

    def next_order_item_id(self):
        return max((item.order_item_id for item in self._order_items), default=0) + 1

    def next_order_remark_id(self):
        return max([remark.remark_id for remark in self._order_remarks], default=0) + 1

    def get_all_kitchen_ids(self):
        return set(
            [
                kitchen_id
                for order_item in self.order_items
                for kitchen_id in order_item.kitchen_ids
            ]
        )

    @property
    def created_at(self):
        return self.order.created_at

    @property
    def modified_at(self):
        return self.order.modified_at

    @property
    def version(self):
        return self.order.version

    @property
    def settlement_method(self):
        return self.order.settlement_method

    def increment_version(self):
        self.order.version += 1

    def update_bill_mapping(self, bill_id, charge_ids):
        self.order.update_bill_id(bill_id)
        for order_item, charge_id in zip(self.order_items, charge_ids):
            order_item.update_charge_id(charge_id)
            order_item_bill = OrderItemBill(bill_id=bill_id, charge_ids=[charge_id])
            order_item.update_bills(bills=[order_item_bill])
        self.check_invariance()

    def get_order_item_by_id(self, order_item_id):
        return self.order_item_dict.get(order_item_id)

    def add_order_items(self, order_item_dtos):
        order_items = []
        base_currency = self.order.base_currency
        for order_item_dto in order_item_dtos:
            amount_pretax = order_item_dto.unit_price_pretax
            amount_posttax = order_item_dto.unit_price_posttax
            if (
                amount_pretax
                and amount_pretax.currency
                and amount_pretax.currency != base_currency
            ) or (
                amount_posttax
                and amount_posttax.currency
                and amount_posttax.currency != base_currency
            ):
                raise ValueError(
                    'seller currency and item amount currency are not matching please provide valid currency with amount'
                )
            new_order_item = OrderItem(
                order_item_id=self.next_order_item_id(),
                sku_vo=SkuVO(
                    sku_category_code=order_item_dto.sku_category_code,
                    item_name=order_item_dto.item_name,
                    sku_id=order_item_dto.sku_id,
                    item_detail=order_item_dto.item_detail,
                ),
                remarks=order_item_dto.remarks,
                quantity=order_item_dto.quantity,
                unit_price_pretax=Money(amount_pretax.amount, base_currency)
                if amount_pretax
                else None,
                unit_price_posttax=Money(amount_posttax.amount, base_currency)
                if amount_posttax
                else None,
                charge_type=order_item_dto.charge_type,
                bill_to_type=order_item_dto.bill_to_type,
                kitchen_ids=order_item_dto.kitchen_ids,
                is_complimentary=order_item_dto.is_complimentary,
                charge_to=order_item_dto.charge_to,
                discounts=order_item_dto.discounts,
                status=PosOrderItemStatus.RECEIVED,
                status_updated_at=dateutils.current_datetime(),
                added_by=pos_context.get_user_data().user,
            )
            order_items.append(new_order_item)
            self._order_items.append(new_order_item)
            self.order_item_dict[new_order_item.order_item_id] = new_order_item

        self.check_invariance()
        return order_items

    def add_order_remark(self, order_item_id, action, remark):
        order_remark = OrderRemark(
            order_id=self.order_id,
            remark_id=self.next_order_remark_id(),
            order_item_id=order_item_id,
            action=action,
            remark=remark,
        )
        self._order_remarks.append(order_remark)
        return order_remark

    @property
    def order_id(self):
        return self.order.order_id

    @property
    def order_number(self):
        return self.order.order_number

    @property
    def scheduled_order_number(self):
        return self.order.scheduled_order_number

    @property
    def order_datetime(self):
        return self.order.order_datetime

    @property
    def scheduled_datetime(self):
        return self.order.scheduled_datetime

    @property
    def seller_id(self):
        return self.order.seller_vo.seller_id

    @property
    def guest_count(self):
        return self.order.guest_count

    @property
    def order_type(self):
        return self.order.order_type

    @property
    def seller_type(self):
        return self.order.seller_type

    @property
    def source_of_customer(self):
        return self.order.source_of_customer

    @property
    def status(self):
        return self.order.status

    @property
    def priority(self):
        return self.order.priority

    @property
    def status_updated_at(self):
        return self.order.status_updated_at

    @property
    def room_booking_details(self):
        return self.order.room_booking_details

    @property
    def table_id(self):
        return self.order.table_id

    @property
    def bill_id(self):
        return self.order.bill_id

    @property
    def remarks(self):
        return self.order.remarks

    @property
    def total_price_pretax(self):
        return self.order.total_price_pretax

    @property
    def bill_to(self):
        return self.order.bill_to

    @property
    def created_by(self):
        return self.order.created_by

    def update_order_number(self, order_number):
        self.order.order_number = order_number
        self.check_invariance()

    def update_table_id(self, table_id):
        self.order.table_id = table_id
        self.check_invariance()

    def update_remarks(self, remarks):
        self.order.remarks = remarks
        self.check_invariance()

    def update_order_remark(self, remark_id, remark):
        order_remark = self._order_remark_dict.get(remark_id)
        order_remark.update_remark(remark)
        return order_remark

    def update_status(self, status):
        self.order.status = PosOrderStatus(status)
        self.order.status_updated_at = dateutils.current_datetime()
        self.check_invariance()

    def update_order_type(self, order_type):
        self.order.order_type = order_type
        self.check_invariance()

    def update_guest_count(self, guest_count):
        self.order.guest_count = guest_count
        self.check_invariance()

    def update_seller_type(self, seller_type):
        self.order.seller_type = seller_type
        self.check_invariance()

    def update_source_of_customer(self, source_of_customer):
        self.order.source_of_customer = source_of_customer
        self.check_invariance()

    def update_room_booking_details(self, room_booking_details):
        self.order.update_room_booking_details(room_booking_details)
        self.check_invariance()

    def update_scheduled_datetime(self, scheduled_datetime):
        self.order.scheduled_datetime = scheduled_datetime
        self.check_invariance()

    def get_order_items_to_update_charges_for(self, order_item_dtos):
        order_item_ids_to_update_charges_for = []
        for item_dto in order_item_dtos:
            amount_pretax = item_dto.unit_price_pretax
            order_item = self.order_item_dict.get(item_dto.order_item_id)

            if (
                item_dto.unit_price_pretax.amount != NotAssigned
                and order_item.unit_price_pretax.amount != amount_pretax.amount
            ):
                order_item_ids_to_update_charges_for.append(order_item.order_item_id)
                continue
            elif (
                item_dto.quantity != NotAssigned
                and order_item.quantity != item_dto.quantity
            ):
                order_item_ids_to_update_charges_for.append(order_item.order_item_id)
                continue
            elif (
                item_dto.is_complimentary != NotAssigned
                and order_item.is_complimentary != item_dto.is_complimentary
            ):
                order_item_ids_to_update_charges_for.append(order_item.order_item_id)
                continue
            elif (
                item_dto.discounts != NotAssigned
                and order_item.discounts != item_dto.discounts
            ):
                order_item_ids_to_update_charges_for.append(order_item.order_item_id)

        return order_item_ids_to_update_charges_for

    def update_order_items(self, order_item_dtos):
        order_items = []
        base_currency = self.order.base_currency
        for item_dto in order_item_dtos:
            amount_pretax = item_dto.unit_price_pretax
            amount_posttax = item_dto.unit_price_posttax
            if (
                amount_pretax
                and amount_pretax.currency
                and amount_pretax.currency != base_currency
            ) or (
                amount_posttax
                and amount_posttax.currency
                and amount_posttax.currency != base_currency
            ):
                raise ValueError(
                    'seller currency and item amount currency are not matching please provide valid currency with amount'
                )

            order_item = self.order_item_dict.get(item_dto.order_item_id)

            if not order_item:
                raise ResourceNotFound(
                    "OrderItem",
                    description=f"order item id: {item_dto.order_item_id} is"
                    f" not present in charge dict",
                )
            item_dto.unit_price_pretax = (
                Money(amount_pretax.amount, base_currency) if amount_pretax else None
            )
            item_dto.unit_price_posttax = (
                Money(amount_posttax.amount, base_currency) if amount_posttax else None
            )
            order_item.update(item_dto)
            order_items.append(order_item)

            if item_dto.order_remarks != NotAssigned:
                for order_remark in item_dto.order_remarks:
                    self.add_order_remark(
                        order_item_id=item_dto.order_item_id,
                        action=order_remark.action,
                        remark=order_remark.remark,
                    )
        self.check_invariance()
        return order_items

    def get_order_items_whose_quantity_is_being_reduced(self, order_item_dtos):
        order_items_with_reduced_quantity = []

        for item_dto in order_item_dtos:
            order_item = self.order_item_dict.get(item_dto.order_item_id)

            if item_dto.quantity < order_item.quantity:
                cancelled_quantity = order_item.quantity - item_dto.quantity
                modified_item = deepcopy(order_item)
                modified_item.update_quantity(cancelled_quantity)
                order_items_with_reduced_quantity.append(modified_item)
        return order_items_with_reduced_quantity

    def delete_order_items_except(self, order_item_ids_to_skip):
        deleted_order_items = []
        for item in self.order_items:
            if item.order_item_id not in order_item_ids_to_skip:
                item.delete()
                self.order_item_dict.pop(item.order_item_id)
                deleted_order_items.append(item)
        self.check_invariance()
        return deleted_order_items

    def update_order_items_charge_to(self):
        for order_item in self.order_items:
            order_item.replace_charge_to([self.bill_to])
        self.check_invariance()

    def update_order_items_charge_type_to_credit(self):
        for order_item in self.order_items:
            order_item.charge_type = ChargeTypes.CREDIT
        self.check_invariance()

    def update_order_items_bill_to_company(self):
        for order_item in self.order_items:
            order_item.bill_to_type = ChargeBillToTypes.COMPANY
        self.check_invariance()

    @property
    def customers(self):
        return [
            customer
            for customer in self._customers
            if self._customers and not customer.deleted
        ]

    @property
    def non_anonymous_customers(self):
        return [
            customer
            for customer in self._customers
            if self._customers and not customer.deleted and not customer.is_anonymous()
        ]

    @property
    def split_bill_ids(self):
        return [bill.bill_id for bill in self.split_bills] if self.split_bills else []

    def get_order_items_by_kitchen(self, kitchen_id):
        return [
            order_item
            for order_item in self.order_items
            if kitchen_id in order_item.kitchen_ids
        ]

    def add_split_bill(self, bill: SplitBill):
        bill.split_id = self.next_split_id()
        self._split_bills.append(bill)
        self.split_bill_dict[bill.bill_id] = bill
        self.check_invariance()
        return bill

    def get_split_bill(self, bill_id):
        return self.split_bill_dict.get(bill_id)

    def update_split_bill(self, bill):
        existing_bill = self.split_bill_dict.get(bill.bill_id)
        existing_bill.update(bill)
        self.check_invariance()
        return existing_bill

    def get_customer(self, customer_id):
        customer = self.customer_dict.get(customer_id)
        if not customer:
            raise ResourceNotFound(
                "Customer",
                description=f"customer id: {customer_id} is"
                f" not present in customers",
            )
        return customer

    def get_customers(self, include_deleted=True):
        return self._customers if include_deleted else self.customers

    def get_split_bills(self, include_deleted=True):
        return self._split_bills if include_deleted else self.split_bills

    def update_customer_details(self, customer_id, bill_to):
        customer = self.get_customer(customer_id)
        customer.first_name = bill_to.first_name
        customer.last_name = bill_to.last_name
        customer.phone = bill_to.phone
        customer.email = bill_to.email
        customer.gstin_num = bill_to.gstin_num
        self.check_invariance()
        return customer

    def add_customer(self, customer_dto: NewPosCustomerDto):
        customer_id = self.next_customer_id()
        new_customer = PosCustomer(
            customer_id=customer_id,
            first_name=customer_dto.first_name,
            last_name=customer_dto.last_name,
            phone=customer_dto.phone,
            company_profile_id=customer_dto.company_profile_id,
            room_booking_details=customer_dto.room_booking_details,
            email=customer_dto.email,
            gstin_num=customer_dto.gstin_num,
        )
        self._customers.append(new_customer)
        self.customer_dict[new_customer.customer_id] = new_customer
        self.check_invariance()
        return new_customer

    def update_customers(self, customer_dtos: List[EditPosCustomerDto]):
        customers = []
        for customer_dto in customer_dtos:
            customer = self.customer_dict.get(customer_dto.customer_id)

            if not customer:
                raise ResourceNotFound(
                    "Customer",
                    description=f"customer id: {customer_dto.customer_id} is"
                    f" not present in customer dict",
                )
            customer.update(customer_dto)
            customers.append(customer)
        self.check_invariance()
        return customers

    def delete_customers(self, customer_ids_to_skip):
        deleted_customers = []
        for customer in self.customers:
            if customer.customer_id not in customer_ids_to_skip:
                customer.delete()
                self.customer_dict.pop(customer.customer_id)
                deleted_customers.append(customer)
        self.check_invariance()
        return deleted_customers

    def update_bill_to(self, customer_id):
        self.order.set_bill_to(customer_id)
        self.update_order_items_charge_to()
        self.check_invariance()

    def next_customer_id(self):
        return str(
            max([int(customer.customer_id) for customer in self._customers], default=0)
            + 1
        )

    def next_split_id(self):
        return str(
            max(
                [int(split_bill.split_id) for split_bill in self._split_bills],
                default=0,
            )
            + 1
        )

    def get_all_charge_ids(self):
        return [order_item.charge_id for order_item in self.order_items]

    def are_all_bills_settled(self):
        return all(bill.status == PosBillStatus.SETTLED for bill in self.split_bills)

    def are_all_non_primary_bills_settled(self):
        return all(
            bill.status == PosBillStatus.SETTLED
            for bill in self.split_bills
            if bill.split_type != PosSplitBillSplitType.PRIMARY
        )

    def mark_settled(self):
        self.order.mark_settled()
        self.mark_all_order_items_delivered()
        self.check_invariance()

    def mark_order_preparing(self):
        self.order.mark_preparing()
        self.check_invariance()

    def mark_order_items_preparing(self, order_items):
        [order_item.mark_preparing() for order_item in order_items]
        self.check_invariance()

    def mark_all_order_items_delivered(self):
        [order_item.mark_delivered() for order_item in self.order_items]
        self.check_invariance()

    def cancel(self):
        self.order.mark_cancelled()
        self.check_invariance()

    def void(self):
        self.order.mark_voided()
        self.check_invariance()

    def update_total_price_pretax(self, total_price_pretax):
        self.order.total_price_pretax = total_price_pretax
        self.check_invariance()

    def set_room_stay_id(self, room_stay_id):
        self.order.room_stay_id = room_stay_id
        self.check_invariance()

    def set_guest_ids(self, guest_ids):
        self.order.guest_ids = guest_ids
        self.check_invariance()

    def set_crs_booking_id(self, crs_booking_id):
        self.order.crs_booking_id = crs_booking_id

    def set_priorities(
        self, acceptance_time, delivery_time, settlement_time, priority_multiplier
    ):
        acceptance_time = datetime.strptime(acceptance_time, "%H:%M:%S")
        acceptance_timedelta = timedelta(
            hours=acceptance_time.hour,
            minutes=acceptance_time.minute,
            seconds=acceptance_time.second,
        )
        acceptance_timedelta_upper_limit = timedelta(
            seconds=acceptance_timedelta.total_seconds() * priority_multiplier
        )

        delivery_time = datetime.strptime(delivery_time, "%H:%M:%S")
        delivery_timedelta = timedelta(
            hours=delivery_time.hour,
            minutes=delivery_time.minute,
            seconds=delivery_time.second,
        )
        delivery_timedelta_upper_limit = timedelta(
            seconds=delivery_timedelta.total_seconds() * priority_multiplier
        )

        settlement_time = datetime.strptime(settlement_time, "%H:%M:%S")
        settlement_timedelta = timedelta(
            hours=settlement_time.hour,
            minutes=settlement_time.minute,
            seconds=settlement_time.second,
        )
        settlement_timedelta_upper_limit = timedelta(
            seconds=settlement_timedelta.total_seconds() * priority_multiplier
        )

        current_time = dateutils.current_datetime()

        [
            order_item.set_priority(
                current_time, delivery_timedelta, delivery_timedelta_upper_limit
            )
            for order_item in self.order_items
        ]

        order_has_delayed_item = False
        order_has_extremely_delayed_item = False

        if self.order.status in [
            PosOrderStatus.CREATED,
            PosOrderStatus.PREPARING,
            PosOrderStatus.READY,
            PosOrderStatus.DELIVERED,
        ]:
            order_has_delayed_item = any(
                [
                    order_item
                    for order_item in self.order_items
                    if order_item.priority == PosOrderPriorities.DELAYED
                ]
            )
            order_has_extremely_delayed_item = any(
                [
                    order_item
                    for order_item in self.order_items
                    if order_item.priority == PosOrderPriorities.EXTREMELY_DELAYED
                ]
            )

        self.order.set_priority(
            current_time,
            acceptance_timedelta,
            acceptance_timedelta_upper_limit,
            settlement_timedelta,
            settlement_timedelta_upper_limit,
            order_has_delayed_item,
            order_has_extremely_delayed_item,
        )
