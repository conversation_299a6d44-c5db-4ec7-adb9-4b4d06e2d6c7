from pos.domain.order.value_objects.discount import Discount


class OrderItemBill(object):
    def __init__(self, bill_id, charge_ids):
        self.bill_id = bill_id
        self.charge_ids = charge_ids

    @staticmethod
    def from_json(json):
        return OrderItemBill(
            bill_id=json.get("bill_id"), charge_ids=json.get("charge_ids")
        )

    def to_json(self):
        return {"bill_id": self.bill_id, "charge_ids": self.charge_ids}


class SplitBillDiscount(object):
    def __init__(self, order_item_id, discounts):
        self.order_item_id = order_item_id
        self.discounts = discounts

    @staticmethod
    def from_json(json, currency):
        return SplitBillDiscount(
            order_item_id=json.get("order_item_id"),
            discounts=[
                Discount.from_json(discount, currency)
                for discount in json.get("discounts")
            ]
            if json.get("discounts")
            else [],
        )

    def to_json(self):
        return {
            "order_item_id": self.order_item_id,
            "discounts": [discount.to_json() for discount in self.discounts]
            if self.discounts
            else [],
        }

    def get_discount(self, discount_id):
        for discount in self.discounts:
            if discount.discount_id == discount_id:
                return discount
        return None
