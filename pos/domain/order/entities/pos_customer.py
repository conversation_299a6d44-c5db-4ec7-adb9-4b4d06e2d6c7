class PosCustomer(object):
    def __init__(
        self,
        customer_id,
        first_name,
        last_name=None,
        phone=None,
        email=None,
        gstin_num=None,
        room_booking_details=None,
        company_profile_id=None,
        deleted=False,
    ):
        self.customer_id = customer_id
        self.first_name = first_name
        self.last_name = last_name
        self.phone = phone
        self.email = email
        self.gstin_num = gstin_num
        self.deleted = deleted
        self.company_profile_id = company_profile_id
        self.room_booking_details = room_booking_details

    def is_anonymous(self):
        return self.first_name == 'Anonymous'

    def delete(self):
        self.deleted = True

    def update(self, edit_customer):
        self.first_name = edit_customer.first_name
        self.last_name = edit_customer.last_name
        self.phone = edit_customer.phone
        self.email = edit_customer.email
        self.gstin_num = edit_customer.gstin_num
        self.room_booking_details = edit_customer.room_booking_details
