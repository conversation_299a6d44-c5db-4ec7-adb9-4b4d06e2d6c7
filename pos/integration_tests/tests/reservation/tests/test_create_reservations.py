import pytest

from pos.integration_tests.config.common_config import *
from pos.integration_tests.tests.base_test import BaseTest
from pos.integration_tests.tests.reservation.validations.validation_reservation import (
    ValidationCreateReservation,
)


class TestCreateReservation(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id,tc_description,multi_currency, status_code, user_type, skip_message",
        [
            (
                "CreateReservation_01",
                "Create a reservation with all fields",
                False,
                200,
                None,
                "",
            ),
            # -------------------Test cases related to allergens------------------------------------------#
            (
                "CreateReservation_02",
                "Create a reservation with allergens as NULL",
                False,
                200,
                None,
                "",
            ),
            (
                "CreateReservation_03",
                "Create a reservation with allergens as EMPTY",
                False,
                200,
                None,
                "",
            ),
            (
                "CreateReservation_04",
                "Create a reservation with allergen having special characters",
                False,
                200,
                None,
                "",
            ),
            # -------------------Test cases related to duration------------------------------------------#
            (
                "CreateReservation_05",
                "Create a reservation with duration as NULL",
                False,
                200,
                None,
                "",
            ),
            (
                "CreateReservation_06",
                "Create a reservation with duration as EMPTY",
                False,
                400,
                None,
                "",
            ),
            # -------------------Test cases related to occasion------------------------------------------#
            (
                "CreateReservation_07",
                "Create a reservation with occasion as EMPTY",
                False,
                200,
                None,
                "",
            ),
            (
                "CreateReservation_08",
                "Create a reservation with occasion as NULL",
                False,
                200,
                None,
                "",
            ),
            # -------------------Test cases related to special_requests------------------------------------------#
            (
                "CreateReservation_09",
                "Create a reservation with special_requests as NULL",
                False,
                200,
                None,
                "",
            ),
            (
                "CreateReservation_10",
                "Create a reservation with special_requests as EMPTY",
                False,
                200,
                None,
                "",
            ),
            # -------------------Test cases related to start_datetime------------------------------------------#
            (
                "CreateReservation_11",
                "Create a reservation with start_datetime as NULL",
                False,
                200,
                None,
                "skip",
            ),
            (
                "CreateReservation_12",
                "Create a reservation with start_datetime as EMPTY",
                False,
                400,
                None,
                "",
            ),
            # -------------------Test cases related to table_id------------------------------------------#
            (
                "CreateReservation_13",
                "Create a reservation with table_id as EMPTY",
                False,
                200,
                None,
                "",
            ),
            (
                "CreateReservation_14",
                "Create a reservation with table_id as NULL",
                False,
                200,
                None,
                "",
            ),
            # -------------------Test cases related to guest_count------------------------------------------#
            (
                "CreateReservation_15",
                "Create a reservation with guest_count as EMPTY",
                False,
                400,
                None,
                "",
            ),
            (
                "CreateReservation_16",
                "Create a reservation with guest_count as NULL",
                False,
                200,
                None,
                "",
            ),
        ],
    )
    @pytest.mark.regression
    def test_create_reservation(
        self,
        client_,
        test_case_id,
        tc_description,
        multi_currency,
        status_code,
        user_type,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)

        seller_id = SELLER_ID[0]

        response = self.reservation_request.create_reservation_request(
            client_, test_case_id, status_code, user_type, seller_id
        )
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(
                client_,
                response,
                test_case_id,
                seller_id,
                self.reservation_request.reservation_id,
                user_type,
            )
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(
        client_, response, test_case_id, seller_id, reservation_id, user_type
    ):
        validation = ValidationCreateReservation(
            client_, response, test_case_id, seller_id, reservation_id, user_type
        )
        validation.validate_response()
        validation.validate_reservation_exist_in_get_reservation_response(
            client_, reservation_id, 200, seller_id
        )
