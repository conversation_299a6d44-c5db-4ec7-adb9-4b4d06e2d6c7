import pytest

from pos.integration_tests.config.common_config import *
from pos.integration_tests.tests.base_test import BaseTest
from pos.integration_tests.tests.v2_order.validations.validate_settle_order_v2 import (
    ValidationSettleOrder,
)


class TestSettleOrder(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id,previous_actions,tc_description, multi_currency,status_code, user_type,skip_message",
        [
            (
                "SettleOrderV2_01",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle the Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_02",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle the Order with two order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_03",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle the Order with three order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_04",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle Order after editing and changing quantity",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_05",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle the order with charges as credit",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_06",
                [{'id': "CreateOrderV2_01", 'type': 'create_order'}],
                "Settle the Order with charges as non-credit and payments as null",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_07",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle payment without status",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_08",
                [{'id': "CreateOrderV2_01", 'type': 'create_order'}],
                "settle payment without comment",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_09",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "settle payment without amount",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_10",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "settle payment without payment_mode",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_11",
                [{'id': "CreateOrderV2_01", 'type': 'create_order'}],
                "Settle a order with 100 items",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_14",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle the Order with three order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_15",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle the Order with three order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_16",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle the Order with payment mode as NULL",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrderV2_17",
                [
                    CREATE_ORDER_V2_ONE_ORDERITEM,
                    {'id': "VoidOrder_01", 'type': 'void_order'},
                ],
                "Settle a void order",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrderV2_18",
                [CREATE_ORDER_V2_THREE_ORDERITEM],
                "settle payment without payment_mode",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrderV2_19",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle Order without bill_to",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrderV2_20",
                [
                    CREATE_ORDER_V2_ONE_ORDERITEM,
                    {'id': "EditOrderV2_01", 'type': 'edit_order'},
                ],
                "Settle the Edit order",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrderV2_21",
                [
                    CREATE_ORDER_V2_ONE_ORDERITEM,
                    {'id': "SettleOrderV2_01", 'type': 'settle_order_v2'},
                ],
                "Settle the already settled order",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrderV2_22",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle the Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_23",
                [CREATE_ORDER_V2_TWO_ORDERITEM],
                "Settle the Order with two order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_24",
                [CREATE_ORDER_V2_THREE_ORDERITEM],
                "Settle the Order with three order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_25",
                [CREATE_ORDER_V2_MULTIPLE_ORDERITEM],
                "Settle the Order with 100 order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_26",
                [CREATE_ORDER_V2_MULTIPLE_ORDERITEM],
                "Settle payment without status",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrderV2_27",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Settle payment with status as pending",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrderV2_28",
                CREATE_ORDER_FOR_BOOKING_WITH_TWO_ITEM,
                "Settle a room service order",
                False,
                400,
                None,
                "",
            ),
        ],
    )
    @pytest.mark.regression
    def test_settle_order(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        multi_currency,
        status_code,
        user_type,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)

        seller_id = SELLER_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, seller_id)
        order_id = self.order_request_v2.order_id
        bill_id = self.order_request_v2.bill_id

        if self.booking_request.booking_id:
            response = self.order_request_v2.settle_order_request_for_booking(
                client_,
                test_case_id,
                status_code,
                order_id,
                bill_id,
                user_type,
                self.booking_request.booking_id,
            )
        else:
            response = self.order_request_v2.settle_order_request(
                client_, test_case_id, status_code, order_id, bill_id, user_type
            )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(
                response,
                test_case_id,
                self.order_request_v2.bill_id,
                self.order_request_v2.invoice_id,
                seller_id,
            )
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, bill_id, invoice_id, seller_id='1'):
        validation = ValidationSettleOrder(
            test_case_id, response, bill_id, invoice_id, seller_id
        )
        validation.validate_response()
