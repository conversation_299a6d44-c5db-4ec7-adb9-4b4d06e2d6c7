import pytest

from pos.integration_tests.config.common_config import *
from pos.integration_tests.tests.base_test import BaseTest
from pos.integration_tests.tests.v2_order.validations.validation_update_order_v2 import (
    ValidationEditOrder,
)


class TestEditOrder(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, multi_currency,status_code, user_type, skip_message",
        [
            (
                "EditOrderV2_01",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Update from restaurant order to take_order",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_02",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Update from table_order to take_away",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_03",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Update from restaurant order to room_service",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_04",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Update from restaurant order to SPE@123",
                False,
                400,
                None,
                "",
            ),
            (
                "EditOrderV2_05",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Add remark to the order",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_06",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Change remark to the order",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_07",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "change table number",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_08",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Remove remark from the order",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_09",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Edit the order with price in posttax",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_10",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Edit Order with sku_category_id invalid",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_11",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Set Quantity as 0",
                False,
                400,
                None,
                "",
            ),
            (
                "EditOrderV2_12",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "order items with both posttax and pretax price",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_13",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Update an order date_time",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_14",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Update an order scheduled_datetime",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_15",
                [CREATE_ORDER_V2_TWO_ORDERITEM],
                "Update an order scheduled_datetime",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_16",
                [CREATE_ORDER_V2_MULTIPLE_ORDERITEM],
                "Update an order table_number",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_17",
                [CREATE_ORDER_V2_MULTIPLE_ORDERITEM],
                "Edit quantity of multiple order",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_18",
                [CREATE_ORDER_V2_TWO_ORDERITEM],
                "Change remark to the order",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_19",
                [CREATE_ORDER_V2_TWO_ORDERITEM],
                "Remove remark from the order",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_20",
                [CREATE_ORDER_V2_TWO_ORDERITEM],
                "Add Order with Order_type empty",
                False,
                201,
                None,
                "",
            ),
            (
                "EditOrderV2_21",
                [CREATE_ORDER_V2_TWO_ORDERITEM],
                "Update an order scheduled_datetime",
                False,
                201,
                None,
                "",
            ),
        ],
    )
    @pytest.mark.regression
    def test_edit_order(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        multi_currency,
        status_code,
        user_type,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)

        seller_id = SELLER_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, seller_id)

        response = self.order_request_v2.edit_order_request(
            client_, test_case_id, status_code, user_type
        )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(
                client_,
                response,
                test_case_id,
                self.order_request_v2.bill_id,
                seller_id,
                self.order_request_v2.order_id,
                user_type,
            )
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(
        client_, response, test_case_id, bill_id, seller_id, order_id, user_type
    ):
        validation = ValidationEditOrder(
            client_, response, test_case_id, bill_id, seller_id, order_id, user_type
        )
        validation.validate_response()
        validation.validate_get_order_response(client_, order_id, 200, test_case_id)
        validation.validate_order_exist_in_get_orders_response(client_, order_id, 200)
