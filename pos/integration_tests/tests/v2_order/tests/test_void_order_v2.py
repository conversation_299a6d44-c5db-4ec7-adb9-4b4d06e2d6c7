import pytest

from pos.integration_tests.config.common_config import *
from pos.integration_tests.tests.base_test import BaseTest
from pos.integration_tests.tests.v2_order.validations.validation_void_order_v2 import (
    ValidationVoidOrderV2,
)


class TestVoidOrder(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id,previous_actions,tc_description, multi_currency,status_code, user_type, skip_message",
        [
            (
                "VoidOrder_01",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Cancel the Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_02",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Cancel the Order with two order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_03",
                [
                    CREATE_ORDER_V2_ONE_ORDERITEM,
                    {'id': "EditOrderV2_02", 'type': 'edit_order'},
                ],
                "Cancel the Edited Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_04",
                [
                    CREATE_ORDER_V2_ONE_ORDERITEM,
                    {'id': "EditOrderV2_01", 'type': 'edit_order'},
                ],
                "Cancel the Edited Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_05",
                [
                    CREATE_ORDER_V2_TWO_ORDERITEM,
                    {'id': "EditOrderV2_01", 'type': 'edit_order'},
                ],
                "Cancel the Settled Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_06",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Cancel the Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_07",
                [CREATE_ORDER_V2_ONE_ORDERITEM],
                "Cancel the Order with two order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_08",
                [
                    CREATE_ORDER_V2_ONE_ORDERITEM,
                    {'id': "EditOrderV2_02", 'type': 'edit_order'},
                ],
                "Cancel the Edited Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_09",
                [
                    CREATE_ORDER_V2_ONE_ORDERITEM,
                    {'id': "EditOrderV2_01", 'type': 'edit_order'},
                ],
                "Cancel the Edited Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_10",
                [
                    CREATE_ORDER_V2_TWO_ORDERITEM,
                    {'id': "EditOrderV2_02", 'type': 'edit_order'},
                ],
                "Cancel the Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_11",
                [
                    CREATE_ORDER_V2_TWO_ORDERITEM,
                    {'id': "EditOrderV2_02", 'type': 'edit_order'},
                ],
                "Cancel the Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "VoidOrder_12",
                [
                    CREATE_ORDER_V2_TWO_ORDERITEM,
                    {'id': "EditOrderV2_03", 'type': 'edit_order'},
                ],
                "Cancel the Order with one order item",
                False,
                200,
                None,
                "",
            ),
        ],
    )
    @pytest.mark.regression
    def test_cancel_order(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        multi_currency,
        status_code,
        user_type,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)

        seller_id = SELLER_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, seller_id)

        response = self.order_request_v2.void_order_request(
            client_, test_case_id, status_code, user_type
        )
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, self.order_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, bill_id):
        validation = ValidationVoidOrderV2(test_case_id, response, bill_id)
        validation.validate_response()
