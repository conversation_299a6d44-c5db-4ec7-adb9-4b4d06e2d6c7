from pos.integration_tests.config import sheet_names
from pos.integration_tests.config.common_config import *
from pos.integration_tests.utilities.common_utils import assert_
from pos.integration_tests.utilities.excel_utils import get_test_case_data
from thsc.crs.entities.billing import Bill, Invoice


class ValidationSettleOrder:
    def __init__(
        self,
        test_case_id,
        response,
        bill_id,
        invoice_id,
        seller_id='1',
        booking_bill_id=None,
    ):
        self.test_data = get_test_case_data(
            sheet_names.SETTLE_ORDER_SHEET_NAME, test_case_id
        )[0]
        self.response = response
        self.bill_id = bill_id
        self.booking_bill_id = booking_bill_id
        self.invoice_id = invoice_id
        self.seller_id = seller_id

    def validate_response(self):
        assert_(
            self.test_data['expected_status'], self.response['data']['order']['status']
        )
        crs_invoice_obj = Invoice.get(self.invoice_id)
        crs_bill_obj = Bill.get(self.bill_id)
        if self.seller_id == SELLER_ID[1]:
            assert_(
                crs_bill_obj.tax_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.pretax_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.posttax_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.net_paid_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.total_credit_posttax_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.total_non_credit_invoiced_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.net_payable.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
        assert_(
            self.test_data['total_tax_amount'], str(crs_invoice_obj.tax_amount.amount)
        )
        assert_(
            self.test_data['total_pretax_amount'],
            str(crs_invoice_obj.pretax_amount.amount),
        )
        assert_(
            self.test_data['total_posttax_amount'],
            str(crs_invoice_obj.posttax_amount.amount),
        )
        assert_(
            self.test_data['net_paid_amount'], str(crs_bill_obj.net_paid_amount.amount)
        )
        assert_(
            self.test_data['total_credit_posttax_amount'],
            str(crs_bill_obj.total_credit_posttax_amount.amount),
        )
        assert_(
            self.test_data['total_non_credit_invoiced_amount'],
            str(crs_bill_obj.total_non_credit_invoiced_amount.amount),
        )
        assert_(self.test_data['net_payable'], str(crs_bill_obj.net_payable.amount))

    def validate_booking_response(self):
        assert_(
            self.test_data['expected_status'],
            self.response.json()['data']['order']['status'],
        )
        crs_bill_obj = Bill.get(self.booking_bill_id)
        if self.seller_id == SELLER_ID[1]:
            assert_(
                crs_bill_obj.total_tax_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.total_pretax_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.total_posttax_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.net_paid_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.total_credit_posttax_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.total_non_credit_invoiced_amount.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
            assert_(
                crs_bill_obj.net_payable.currency.value,
                SELLER_CURRENCY_MAP[self.seller_id],
            )
        assert_(
            self.test_data['total_tax_amount'],
            str(crs_bill_obj.total_tax_amount.amount),
        )
        assert_(
            self.test_data['total_pretax_amount'],
            str(crs_bill_obj.total_pretax_amount.amount),
        )
        assert_(
            self.test_data['total_posttax_amount'],
            str(crs_bill_obj.total_posttax_amount.amount),
        )
        assert_(
            self.test_data['net_paid_amount'], str(crs_bill_obj.net_paid_amount.amount)
        )
        assert_(
            self.test_data['total_credit_posttax_amount'],
            str(crs_bill_obj.total_credit_posttax_amount.amount),
        )
        assert_(
            self.test_data['total_non_credit_invoiced_amount'],
            str(crs_bill_obj.total_non_credit_invoiced_amount.amount),
        )
        assert_(self.test_data['net_payable'], str(crs_bill_obj.net_payable.amount))
