import pytest

from pos.integration_tests.config.common_config import *
from pos.integration_tests.tests.base_test import BaseTest
from pos.integration_tests.tests.order.validations.validation_cancel_order import (
    ValidationCancelOrder,
)


class TestCancelOrder(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id,previous_actions,tc_description, multi_currency,status_code, user_type, skip_message",
        [
            (
                "CancelOrder_01",
                [CREATE_ORDER_ONE_ORDERITEM],
                "Cancel the Order with one order item",
                False,
                201,
                None,
                "",
            ),
            (
                "CancelOrder_02",
                [CREATE_ORDER_TWO_ORDERITEM],
                "Cancel the Order with two order item",
                False,
                201,
                None,
                "",
            ),
            (
                "CancelOrder_03",
                [
                    CREATE_ORDER_ONE_ORDERITEM,
                    {'id': "EditOrder_02", 'type': 'edit_order'},
                ],
                "Cancel the Edited Order with one order item",
                False,
                201,
                None,
                "",
            ),
            (
                "CancelOrder_04",
                [
                    CREATE_ORDER_TWO_ORDERITEM,
                    {'id': "EditOrder_01", 'type': 'edit_order'},
                ],
                "Cancel the Edited Order with one order item",
                False,
                201,
                None,
                "",
            ),
            (
                "CancelOrder_05",
                [
                    CREATE_ORDER_ONE_ORDERITEM,
                    {'id': "SettleOrder_01", 'type': 'settle_order'},
                ],
                "Cancel the Settled Order with one order item",
                False,
                400,
                None,
                "",
            ),
            (
                "CancelOrder_01",
                [CREATE_ORDER_ONE_ORDERITEM],
                "Cancel the Order with one order item",
                True,
                201,
                None,
                "",
            ),
            (
                "CancelOrder_02",
                [CREATE_ORDER_TWO_ORDERITEM],
                "Cancel the Order with two order item",
                True,
                201,
                None,
                "",
            ),
            (
                "CancelOrder_03",
                [
                    CREATE_ORDER_ONE_ORDERITEM,
                    {'id': "EditOrder_02", 'type': 'edit_order'},
                ],
                "Cancel the Edited Order with one order item",
                True,
                201,
                None,
                "",
            ),
            (
                "CancelOrder_04",
                [
                    CREATE_ORDER_TWO_ORDERITEM,
                    {'id': "EditOrder_01", 'type': 'edit_order'},
                ],
                "Cancel the Edited Order with one order item",
                True,
                201,
                None,
                "",
            ),
            (
                "CancelOrder_05",
                [
                    CREATE_ORDER_ONE_ORDERITEM,
                    {'id': "SettleOrder_01", 'type': 'settle_order'},
                ],
                "Cancel the Settled Order with one order item",
                True,
                400,
                None,
                "",
            ),
        ],
    )
    @pytest.mark.regression
    def test_cancel_order(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        multi_currency,
        status_code,
        user_type,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)
        if multi_currency:
            seller_id = SELLER_ID[1]
        else:
            seller_id = SELLER_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, seller_id)

        response = self.order_request.cancel_order_request(
            client_, test_case_id, status_code, user_type
        )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, self.order_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, bill_id):
        validation = ValidationCancelOrder(test_case_id, response, bill_id)
        validation.validate_response()
