from pos.integration_tests.config import sheet_names
from pos.integration_tests.tests.order.validations.common_validations import (
    validate_order_items,
    validate_total_charges,
)
from pos.integration_tests.utilities.common_utils import assert_
from pos.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationCreateOrder:
    def __init__(self, test_case_id, response, bill_id, seller_id):
        self.test_data = get_test_case_data(
            sheet_names.NEW_ORDER_SHEET_NAME, test_case_id
        )[0]
        self.response = response
        self.bill_id = bill_id
        self.seller_id = seller_id

    def validate_response(self):
        assert_(self.test_data['order_type'], self.response['data']['order_type'])
        assert_(self.seller_id, self.response['data']['seller_id'])
        assert_(self.test_data['table_number'], self.response['data']['table_number'])
        assert_(self.test_data['expected_status'], self.response['data']['status'])
        validate_order_items(self.test_data['order_items'], self.response, self.bill_id)
        validate_total_charges(self.bill_id, self.test_data, self.seller_id)
