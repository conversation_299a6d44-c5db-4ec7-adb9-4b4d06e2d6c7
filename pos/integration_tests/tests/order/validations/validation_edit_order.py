from pos.integration_tests.config import sheet_names
from pos.integration_tests.tests.order.validations.common_validations import (
    validate_order_items,
    validate_total_charges,
)
from pos.integration_tests.utilities.common_utils import assert_, sanitize_test_data
from pos.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationEditOrder:
    def __init__(self, test_case_id, response, bill_id, seller_id):
        self.test_data = get_test_case_data(
            sheet_names.EDIT_ORDER_SHEET_NAME, test_case_id
        )[0]
        self.response = response
        self.bill_id = bill_id
        self.seller_id = seller_id

    def validate_response(self):
        assert_(
            self.test_data['expected_order_type'], self.response['data']['order_type']
        )
        assert_(self.test_data['table_number'], self.response['data']['table_number'])
        assert_(
            sanitize_test_data(self.test_data['remarks']),
            sanitize_test_data(self.response['data']['remarks']),
        )
        assert_(
            self.test_data['resource_version'] + 1, self.response['data']['version']
        )
        validate_order_items(
            self.test_data['order_items'],
            self.response,
            self.bill_id,
            self.test_data['order_items_id'],
        )
        validate_total_charges(self.bill_id, self.test_data, self.seller_id)
