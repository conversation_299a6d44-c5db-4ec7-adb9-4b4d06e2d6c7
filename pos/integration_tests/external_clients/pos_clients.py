import json

import requests

from pos.integration_tests.config.common_config import POS_DOMAIN
from pos.integration_tests.config.request_uris import *


class PosClient:
    headers = {'X-User-Type': 'super-admin'}

    def create_order_for_booking(self, payload):
        create_order_api = POS_DOMAIN + ORDER_URI

        return self.__make_call(
            method="POST",
            api=create_order_api,
            headers=self.headers,
            data=json.loads(payload),
        )

    def edit_order_for_booking(self, payload, order_id):
        edit_order_api = POS_DOMAIN + EDIT_ORDER_URI.format(order_id)

        return self.__make_call(
            method="PATCH",
            api=edit_order_api,
            headers=self.headers,
            data=json.loads(payload),
        )

    def settle_order_for_booking(self, payload, order_id):
        settle_order_api = POS_DOMAIN + SETTLE_ORDER_URI.format(order_id)

        return self.__make_call(
            method="POST",
            api=settle_order_api,
            headers=self.headers,
            data=json.loads(payload),
        )

    @staticmethod
    def __make_call(method, api, headers, data=None, params=None):
        response = requests.request(
            method, api, params=params, headers=headers, json=data, allow_redirects=True
        )
        return response
