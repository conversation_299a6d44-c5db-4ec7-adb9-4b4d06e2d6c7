import json

from pos.integration_tests.builders import booking_builder
from pos.integration_tests.config import common_config, sheet_names
from pos.integration_tests.external_clients.crs_clients import CrsClient
from pos.integration_tests.utilities import excel_utils
from pos.integration_tests.utilities.common_utils import increment_date, sanitize_blank


class BookingActionBuilder(object):
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.resource_version = 1
        self.data = BookingActionBuilder.Data(test_data).__dict__

    class Data(object):
        def __init__(self, test_data):
            self.action_type = sanitize_blank(test_data[0]['action_type'])
            self.payload = BookingActionBuilder.Data.Payload(test_data).__dict__

        class Payload(object):
            def __init__(self, booking_action_data):
                if booking_action_data[0]['action_type'] == 'checkin':
                    self.checkin = BookingActionBuilder.Data.Payload.CheckIn(
                        booking_action_data
                    ).__dict__

            class CheckIn(object):
                def __init__(self, booking_action_data):
                    room_stay_data = excel_utils.get_test_case_data(
                        sheet_names.ROOM_STAY_SHEET_NAME,
                        booking_action_data[0]['room_stays'],
                    )
                    self.room_stays = []
                    for room_stay in room_stay_data:
                        self.room_stays.append(
                            BookingActionBuilder.Data.Payload.CheckIn.RoomStay(
                                room_stay, booking_action_data
                            ).__dict__
                        )

                class RoomStay(object):
                    def __init__(self, room_stay_data, booking_action_data):
                        self.room_stay_id = room_stay_data['room_stay_id_forAction']
                        guest_stay_data = excel_utils.get_test_case_data(
                            sheet_names.GUEST_STAY_SHEET_NAME,
                            room_stay_data['GuestStay'],
                        )

                        self.guest_stays = []
                        for guest_stay in guest_stay_data:
                            self.guest_stays.append(
                                BookingActionBuilder.Data.Payload.CheckIn.RoomStay.GuestStay(
                                    guest_stay, booking_action_data
                                ).__dict__
                            )

                        self.room_allocation = BookingActionBuilder.Data.Payload.CheckIn.RoomStay.RoomAllocation(
                            room_stay_data
                        ).__dict__

                    class GuestStay(object):
                        def __init__(self, guest_stay_data, booking_action_data):
                            self.checkin_date = increment_date(
                                int(guest_stay_data['expected_checkin_date']),
                                common_config.CHECK_IN_TIME_ZONE,
                            )
                            self.guest_id = '1'
                            self.guest_stay_id = sanitize_blank(
                                guest_stay_data['guest_stay_id']
                            )
                            guest_data = excel_utils.get_test_case_data(
                                sheet_names.CUSTOMER_DETAILS_SHEET_NAME,
                                guest_stay_data['guest_details'],
                            )
                            self.guest = booking_builder.Customer(
                                guest_data[0]
                            ).__dict__

                    class RoomAllocation(object):
                        def __init__(self, room_allocation_data):
                            self.checkin_date = increment_date(
                                int(room_allocation_data['checkin_date']),
                                common_config.CHECK_IN_TIME_ZONE,
                            )
                            available_room = (
                                CrsClient()
                                .get_available_rooms(common_config.ROOM_TYPE)
                                .json()['data'][0]['room_id']
                            )
                            self.room_id = str(available_room)
