import json

from pos.integration_tests.builders.common_request_builder import (
    Customer,
    Discounts,
    OrderItems,
    RoomBookingDetails,
)
from pos.integration_tests.config.sheet_names import *
from pos.integration_tests.utilities import excel_utils
from pos.integration_tests.utilities.common_utils import sanitize_test_data


class CreateOrderBuilder(object):
    def __init__(self, sheet_name, test_case_id, seller_id='1', booking_id=None):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = OrderData(test_data, seller_id, booking_id).__dict__


class OrderData(object):
    def __init__(self, test_data, seller_id, booking_id):
        if sanitize_test_data(test_data['Customer_details']) is not None:
            self.customers = []
            customers_types = test_data['Customer_details'].split('#')
            for customer_type in customers_types:
                self.customers.append(
                    sanitize_test_data(Customer(json.loads(customer_type)).__dict__)
                )

        if sanitize_test_data(test_data['bill_to']) is not None:
            self.bill_to = sanitize_test_data(
                Customer(json.loads(test_data['bill_to'])).__dict__
            )

        self.extra_information = sanitize_test_data(test_data['extra_information'])
        self.order_datetime = sanitize_test_data(test_data['order_datetime'])

        if sanitize_test_data(test_data['order_items']) is not None:
            self.order_items = []
            order_items_type = test_data['order_items'].split('#')
            for order_type in order_items_type:
                order_data = excel_utils.get_test_case_data(
                    ORDER_ITEMS_SHEET_NAME, order_type
                )[0]
                self.order_items.append(
                    sanitize_test_data(OrderItems(order_data).__dict__)
                )

        self.order_type = sanitize_test_data(test_data['order_type'])
        self.remark = sanitize_test_data(test_data['remarks'])
        if sanitize_test_data(test_data['room_booking_details']) is not None:
            self.room_booking_details = sanitize_test_data(
                RoomBookingDetails(
                    json.loads(test_data['room_booking_details']), booking_id
                )
            ).__dict__
        self.seller_id = sanitize_test_data(seller_id)
        self.table_id = sanitize_test_data(str(test_data['table_id']))
        self.scheduled_datetime = sanitize_test_data(test_data['scheduled_datetime'])
        self.seller_type = sanitize_test_data(test_data['seller_type'])
        self.source_of_customer = sanitize_test_data(test_data['source_of_customer'])
