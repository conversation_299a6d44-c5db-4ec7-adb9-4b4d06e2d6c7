from datetime import datetime, time

import pytest
from treebo_commons.money.money import Money

from pos.api.serializers.request.value_objects import RoomBookingDetail
from pos.application.dtos.edit_order_dto import EditOrderItemDto, EditPosCustomerDto
from pos.application.dtos.new_order_dto import NewPosCustomerDto, OrderItemDto
from pos.domain.order.exceptions import OrderInvarianceException
from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes
from ths_common.exceptions import PolicyAuthException
from ths_common.pos.constants.order_constants import PosOrderType
from ths_common.value_objects import NotAssigned


def test_get_order_items(order_aggregate_without_customers_set):
    assert len(order_aggregate_without_customers_set.order_items) >= 1
    assert any(
        item.item_name == "Idli"
        for item in order_aggregate_without_customers_set.order_items
    )


def test_get_next_order_item_id(order_aggregate_without_customers_set):
    assert (
        order_aggregate_without_customers_set.next_order_item_id()
        == len(order_aggregate_without_customers_set.get_order_items()) + 1
    )


def test_add_order_items(order_aggregate_without_customers_set):
    new_order_item_dto = OrderItemDto(
        sku_id="2",
        sku_category_code="food",
        item_name="Pasta",
        quantity=2,
        unit_price_pretax=Money("200.00 INR"),
        item_detail={"menu_item_id": "3"},
        unit_price_posttax=None,
        charge_type=ChargeTypes.CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        remarks="super spicy",
    )
    order_items = order_aggregate_without_customers_set.add_order_items(
        [new_order_item_dto]
    )

    assert any(
        item.item_name == "Pasta"
        for item in order_aggregate_without_customers_set.order_items
    )
    assert any(
        item.order_item_id == order_items[0].order_item_id
        for item in order_aggregate_without_customers_set.order_items
    )


def test_update_order_items(order_aggregate_without_customers_set):
    updated_order_item_dto = EditOrderItemDto(
        order_item_id=1,
        sku_id="2",
        status="preparing",
        sku_category_code="alcohol",
        item_name="Pasta",
        quantity=1,
        unit_price_pretax=Money("200.00 INR"),
        unit_price_posttax=NotAssigned,
        item_detail=NotAssigned,
        charge_type=NotAssigned,
        bill_to_type=NotAssigned,
        remarks="Cheesy",
        is_complimentary=True,
    )
    order_aggregate_without_customers_set.update_order_items([updated_order_item_dto])
    assert order_aggregate_without_customers_set.order_items[0].quantity == 1
    assert order_aggregate_without_customers_set.order_items[
        0
    ].unit_price_pretax == Money("200.00 INR")
    assert order_aggregate_without_customers_set.order_items[0].is_complimentary
    assert order_aggregate_without_customers_set.order_items[0].remarks == "Cheesy"


def test_update_bill_mapping(order_aggregate_without_customers_set):
    charge_ids = ["1"]
    order_aggregate_without_customers_set.update_bill_mapping(
        bill_id="1", charge_ids=charge_ids
    )

    order_items = order_aggregate_without_customers_set.order_items
    for order_item, charge_id in zip(order_items, charge_ids):
        assert order_item.charge_id == charge_id


def test_increment_version(order_aggregate_without_customers_set):
    order_aggregate_without_customers_set.increment_version()
    assert order_aggregate_without_customers_set.version == 2


def test_variance(order_aggregate_without_customers_set):
    order_aggregate_without_customers_set.check_invariance()

    new_order_item_dto = OrderItemDto(
        sku_id="2",
        sku_category_code="food",
        item_name="Pasta",
        quantity=2,
        unit_price_pretax=Money("200.00 INR"),
        item_detail={"menu_item_id": "3"},
        unit_price_posttax=None,
        charge_type=ChargeTypes.CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        remarks="super spicy",
    )
    with pytest.raises(OrderInvarianceException):
        order_aggregate_without_customers_set.add_order_items([new_order_item_dto])


def test_update_table_number(order_aggregate_without_customers_set):
    order_aggregate_without_customers_set.update_table_number("1")

    assert order_aggregate_without_customers_set.table_number == "1"


def test_update_remarks(order_aggregate_without_customers_set):
    remark = "Make it spicy, with a hint of sweetness as well"
    order_aggregate_without_customers_set.update_remarks(remark)

    assert order_aggregate_without_customers_set.remarks == remark


def test_update_order_type(order_aggregate_without_customers_set):
    order_type = PosOrderType.ROOM_SERVICE

    order_aggregate_without_customers_set.update_order_type(order_type)

    assert order_aggregate_without_customers_set.order_type == order_type


def test_update_scheduled_datetime(order_aggregate_without_customers_set):
    previous_scheduled_datetime = (
        order_aggregate_without_customers_set.scheduled_datetime
    )

    new_scheduled_datetime = datetime.now()

    order_aggregate_without_customers_set.update_scheduled_datetime(
        new_scheduled_datetime
    )

    assert (
        order_aggregate_without_customers_set.scheduled_datetime
        != previous_scheduled_datetime
    )
    assert (
        order_aggregate_without_customers_set.scheduled_datetime
        == new_scheduled_datetime
    )


def test_update_room_booking_details(order_aggregate_without_customers_set):
    room_booking_detail = RoomBookingDetail(crs_booking_id="1", room_number="101")

    order_aggregate_without_customers_set.update_room_booking_details(
        room_booking_detail
    )

    assert (
        order_aggregate_without_customers_set.room_booking_details
        == room_booking_detail
    )


def test_delete_order_items_when_order_items_are_sent_to_kitchen(
    order_aggregate_with_kots,
):
    acceptable_order_item_cancellation_duration = time(0, 0, 0)
    with pytest.raises(PolicyAuthException):
        order_aggregate_with_kots.delete_order_items_except(
            [], acceptable_order_item_cancellation_duration
        )


def test_delete_order_items_when_order_items_are_sent_to_kitchen_within_order_cancellation_duration(
    order_aggregate_with_kots,
):
    acceptable_order_item_cancellation_duration = time(0, 5, 0)
    order_aggregate_with_kots.delete_order_items_except(
        [], acceptable_order_item_cancellation_duration
    )

    assert order_aggregate_with_kots.order_items == []


def test_add_customer(order_aggregate_without_customers_set):
    order_aggregate_without_customers_set.add_customer(
        NewPosCustomerDto(
            first_name="John", last_name="Wick", phone=None, email=None, gstin_num=None
        )
    )

    assert order_aggregate_without_customers_set.customers[1].first_name == "John"
    assert order_aggregate_without_customers_set.customers[1].customer_id == "2"
    assert order_aggregate_without_customers_set.customers[1].last_name == "Wick"


def test_update_customer(order_aggregate_with_customer_set):
    update_customer_dto = EditPosCustomerDto(
        customer_id=2,
        first_name="Mason",
        company_profile_id=None,
        last_name="Mount",
        phone=None,
        email="<EMAIL>",
        gstin_num=None,
    )
    order_aggregate_with_customer_set.update_customers([update_customer_dto])

    assert (
        order_aggregate_with_customer_set.customers[1].first_name
        == update_customer_dto.first_name
    )
    assert (
        order_aggregate_with_customer_set.customers[1].last_name
        == update_customer_dto.last_name
    )
    assert (
        order_aggregate_with_customer_set.customers[1].phone
        == update_customer_dto.phone
    )
    assert (
        order_aggregate_with_customer_set.customers[1].gstin_num
        == update_customer_dto.gstin_num
    )
    assert (
        order_aggregate_with_customer_set.customers[1].email
        == update_customer_dto.email
    )


def test_delete_customers(order_aggregate_with_customer_set):
    order_aggregate_with_customer_set.delete_customers([])

    assert order_aggregate_with_customer_set.customers == []
