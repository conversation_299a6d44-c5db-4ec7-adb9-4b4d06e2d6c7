import pytest

from pos.domain.order.entities.pos_customer import PosCustomer


@pytest.fixture
def anonymous_customer():
    return PosCustomer(customer_id="1", first_name="Anonymous")


def test_is_customer_anonymous(anonymous_customer):
    assert anonymous_customer.is_anonymous()


def test_update_customer(customer):
    updated_customer = <PERSON>sCustomer(
        customer_id="1",
        first_name="<PERSON>",
        last_name="<PERSON>",
        phone="+007007007",
        email="<EMAIL>",
        gstin_num="1212112",
    )
    customer.update(updated_customer)
    assert customer.first_name == updated_customer.first_name
    assert customer.last_name == updated_customer.last_name
    assert customer.phone == updated_customer.phone
    assert customer.email == updated_customer.email
    assert customer.gstin_num == updated_customer.gstin_num


def test_delete_customer(customer):
    customer.delete()
    assert customer.deleted
