import logging
import os

import flask
from treebo_commons.request_tracing.flask import after_request, before_request

from pos.middlewares.common_middlewares import after_request as custom_after_request


class DefaultConfig(object):
    """
    Base config
    """

    DEBUG = False
    TESTING = False

    # Database
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    SECRET_KEY = 'q_\xdd\x1c\xbd\x15\xeb\xdb\x8dD5\xc8\xfcR\x84\xd8?\xc5\x03rC=\x12\x98'
    DB_PASSWORD = os.environ.get("DB_PASSWORD", "")
    DB_USER = os.environ.get("DB_USER", "postgres")
    DB_MASTER_URL = os.environ.get("DB_HOST", "localhost")
    DB_SLAVE_URL = os.environ.get("DB_HOST", "localhost")
    DB_PORT = os.environ.get("DB_PORT", "5432")
    DB = os.environ.get("DB_NAME", "crs_db")
    SQLALCHEMY_DATABASE_URI = 'postgresql://%s:%s@%s:%s/%s' % (
        DB_USER,
        DB_PASSWORD,
        DB_MASTER_URL,
        DB_PORT,
        DB,
    )

    # Rabbit MQ
    RABBITMQ_HOST = os.environ.get('RABBITMQ_HOST', '127.0.0.1')
    URL_PREFIX = None

    # Logging
    LOG_FORMAT = '%(asctime)s:%(name)s:%(levelname)s:%(message)s'
    LOG_PATH = 'pos.log'
    LOG_LEVEL = logging.DEBUG
    LOG_ROOT = '.'

    EASYJOBLITE_CONFIG_PATH = "config/easyjoblite.yaml"

    # Middlewares
    WSGI_MIDDLEWARES = []
    BEFORE_REQUEST_MIDDLEWARES = [lambda: before_request(flask.request)]
    AFTER_REQUEST_MIDDLEWARES = [
        lambda resp: after_request(resp, flask.request),
        custom_after_request,
    ]

    JSONIFY_PRETTYPRINT_REGULAR = False
