#!/bin/bash

set -e
set -x

ENV="$1"
export VERSION="$2"
export APP="$3"
TARGET="$4"
export aws_region="$5"
export CLUSTER_IDENTIFIER="$6"
export AWS_SECRET_PREFIX="$7"
export tenant_service_url="$8"

echo "Using env : $ENV"
echo "Current build directory: $BUILD_DIR"

if [[ -z "${BUILD_DIR}" ]]; then
  echo "Please set BUILD_DIR env variable, which should be path where code will be checked out to create build"
  exit 1
fi

allTenants=()

function loadActiveTenants {
  # Read Tenant Ids from TenantGateway
  echo "Loading active tenants"
  if [ "$ENV" = "staging" ]; then
      active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")
  elif [ "$ENV" = "production" ]; then
      active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")
  fi
  # allTenants=($(echo "$active_tenants" | jq -r '.data[] | select(.tenant_code | .!= "treebo") | .tenant_id'))
  allTenants=($(echo "$active_tenants" | jq -r '.data[].tenant_id'))
  echo $allTenants
}

loadActiveTenants

echo "Tenants loaded: ${allTenants[@]}"
echo "Deploying $APP app on $ENV environment"


if [ "$ENV" = "staging" ]; then
    source $BUILD_DIR/$APP/envrepo/pos/build_env/staging
    echo "Running pos-apiservice container"
    docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/app-docker-compose.yml up -d pos-apiservice

    echo "Running worker containers for each tenants"
    for tenant_id in ${allTenants[@]}; do
        export TENANT_ID=$tenant_id
        # -p option is to pass COMPOSE_PROJECT_NAME. That is the only way to run multiple containers for same compose service
        # With this option, docker attaches different networks, to these containers
        echo "Tenant ID: $tenant_id"
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/app-docker-compose.yml -p pos_tenant_${TENANT_ID} up -d pos-event-daemon
    done

elif [ "$ENV" = "production" ]; then
    source $BUILD_DIR/$APP/envrepo/pos/build_env/prod
    echo "Running pos-apiservice container"
    if [ $TARGET = "app" ]; then
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/app-docker-compose.yml up -d pos-apiservice
    elif [ $TARGET = "serialized-workers" ]; then
    echo "Running worker containers for each tenants"
    for tenant_id in ${allTenants[@]}; do
        export TENANT_ID=$tenant_id
        # -p option is to pass COMPOSE_PROJECT_NAME. That is the only way to run multiple containers for same compose service
        # With this option, docker attaches different networks, to these containers
        echo "Tenant ID: $tenant_id"
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/app-docker-compose.yml -p pos_tenant_${TENANT_ID} up -d pos-event-daemon
    done
    fi
fi
