# coding=utf-8
"""
gunicorn config
"""
# pylint: skip-file
import os

from psycogreen.gevent import patch_psycopg

bind = '0.0.0.0:8000'

workers = 3 if os.environ.get('APP_ENV') in ('production', 'prod') else 1

worker_class = 'gevent'

worker_connections = 20 if os.environ.get('APP_ENV') in ('production', 'prod') else 3

timeout = 30

# pidfile = '/var/run/bb8_gunicorn.pid'
#
reload = False

environment = os.environ.get('APP_ENV', 'local')
log_root = os.environ.get('LOG_ROOT', '.')

logconfig_dict = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': "[%(asctime)s] %(levelname)s %(request_id)s - [%(name)s:%(lineno)s] %(message)s",
        },
        'logstash': {'()': 'logstash_formatter.LogstashFormatterV1'},
    },
    'handlers': {
        'console': {'class': 'logging.StreamHandler', 'formatter': 'logstash'}
    },
    'loggers': {
        'gunicorn.access': {
            'handlers': ['console'],
            'level': 'INFO' if environment == 'production' else 'DEBUG',
            'propagate': False,
            'qualname': 'gunicorn.access',
        },
        'gunicorn.error': {
            'handlers': ['console'],
            'level': 'ERROR',
            'propagate': False,
            'qualname': 'gunicorn.error',
        },
    },
}

proc_name = 'pos'


def post_fork(server, worker):
    """

    :param server:
    :param worker:
    :return:
    """
    # patch psycopg2 for gevent compatibility
    patch_psycopg()
