from object_registry import register_instance
from pos.application.writers.integration_event_writer import IntegrationEventWriter
from pos.domain.integrations_event.repositories.integration_event_repository import (
    IntegrationEventRepository,
)


@register_instance(dependencies=[IntegrationEventRepository])
class IntegrationEventWriterFactory:
    def __init__(self, pos_integration_event_repository):
        self.pos_integration_event_repository = pos_integration_event_repository

    def create_integration_event_writer(self):
        return IntegrationEventWriter(self.pos_integration_event_repository)
