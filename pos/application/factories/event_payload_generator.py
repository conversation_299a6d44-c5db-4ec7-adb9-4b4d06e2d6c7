from treebo_commons.utils import dateutils

from pos.api.serializers.response.order import OrderIntegrationEventSchema
from pos.application.errors import ApplicationErrors
from pos.domain.integrations_event.dto.integration_event_dto import IntegrationEventDTO
from ths_common.exceptions import InternalServerException


class EventPayloadGenerator(object):
    ORDER_ENTITY_NAME = 'order'

    @classmethod
    def generate_order_payload(cls, order_aggregate):
        order_integration_event_schema = OrderIntegrationEventSchema()
        order_dict = order_integration_event_schema.dump(order_aggregate).data
        return dict(entity_name=cls.ORDER_ENTITY_NAME, payload=order_dict)

    @classmethod
    def generate_event_dto(cls, event_type, order_aggregate=None):
        """
        generates an integration event for order with the given aggregates.
        Args:
            event_type:
            order_aggregate:
        Returns:

        """
        if not order_aggregate:
            raise InternalServerException(
                error=ApplicationErrors.INTEGRATION_EVENT_DTO_FAILURE_NO_DATA_PASSED,
                description="At least one order_aggregate should be passed.",
            )

        events = [cls.generate_order_payload(order_aggregate)]

        event_dto = IntegrationEventDTO(
            event_type=event_type,
            seller_id=order_aggregate.seller_id,
            generated_at=dateutils.current_datetime(),
            body=events,
            order_id=order_aggregate.order_id,
        )
        return event_dto
