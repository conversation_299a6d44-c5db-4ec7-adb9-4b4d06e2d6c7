from treebo_commons.utils import dateutils


class NewReservationDto(object):
    def __init__(
        self,
        table_id,
        start_datetime,
        duration,
        guests,
        guest_count,
        occasion,
        allergens,
        special_requests,
    ):
        self.table_id = table_id
        self.start_datetime = start_datetime
        self.duration = duration
        self.guests = guests
        self.guest_count = guest_count
        self.occasion = occasion
        self.allergens = allergens
        self.special_requests = special_requests


class EditReservationDto(object):
    def __init__(
        self,
        table_id,
        start_datetime,
        status,
        duration,
        guests,
        guest_count,
        occasion,
        allergens,
        special_requests,
    ):
        self.table_id = table_id
        self.start_datetime = start_datetime
        self.duration = duration
        self.guests = guests
        self.guest_count = guest_count
        self.occasion = occasion
        self.allergens = allergens
        self.status = status
        self.special_requests = special_requests


class SearchReservationQueryDto(object):
    def __init__(
        self,
        start_date=None,
        end_date=None,
        is_cancelled=False,
        table_id=None,
        statuses=None,
        sort_by=None,
    ):
        self.start_date = start_date
        self.end_date = end_date
        self.is_cancelled = is_cancelled
        if statuses:
            self.statuses = statuses.split(",")
        else:
            self.statuses = None
        self.table_id = table_id
        self.sort_by = sort_by
