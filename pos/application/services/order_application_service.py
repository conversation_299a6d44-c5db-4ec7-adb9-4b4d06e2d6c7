from collections import defaultdict
from copy import deepcopy
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from typing import List

from sqlalchemy.orm.exc import NoResultFound
from treebo_commons.money.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from pos.application.decorators import (
    read_seller_id_from_headers,
    session_manager,
    set_seller_context,
)
from pos.application.dtos.edit_order_dto import (
    EditOrderDto,
    EditOrderItemDto,
    EditOrderRemarkDto,
    EditPosCustomerDto,
)
from pos.application.dtos.new_order_dto import NewOrderDto
from pos.application.dtos.order_kot_dto import OrderWithKotDto
from pos.application.dtos.order_settled_dto import OrderSettledDto
from pos.application.dtos.search_order_query import PosOrderSearchQuery
from pos.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from pos.application.services.kot_service import KotService
from pos.core.globals import pos_context
from pos.domain.order.aggregates.order_aggregate import OrderAggregate
from pos.domain.order.entities.bill import SplitBill
from pos.domain.order.errors import OrderError, OrderSplitBillError
from pos.domain.order.order_factory import OrderFactory
from pos.domain.order.value_objects.discount import Discount
from pos.domain.order.value_objects.split_bill import OrderItemBill, SplitBillDiscount
from pos.domain.policy.engine import RuleEngine
from pos.domain.policy.facts.facts import Facts
from pos.infrastructure.database.repositories.catalog.seller_repository import (
    SellerRepository,
)
from pos.infrastructure.database.repositories.order.order_number_repository import (
    OrderNumberRepository,
)
from pos.infrastructure.database.repositories.order.order_repository import (
    OrderRepository,
)
from pos.infrastructure.database.repositories.order.scheduled_order_number_repository import (
    ScheduledOrderNumberRepository,
)
from pos.infrastructure.external_clients.crs_client import CRSClient
from pos.pos_commons.errors import ApplicationErrors
from shared_kernel.value_objects import SellerDetails
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    InvoiceStatus,
    IssuedByType,
    IssuedToType,
)
from ths_common.exceptions import (
    AuthorizationError,
    ResourceNotFound,
    ValidationException,
)
from ths_common.pos.constants.order_constants import (
    PosBillStatus,
    PosOrderItemStatus,
    PosOrderSettlementMethod,
    PosOrderStatus,
    PosSplitBillSplitType,
)
from ths_common.value_objects import (
    InvoiceBillToInfo,
    InvoiceChargeToInfo,
    Name,
    NotAssigned,
)
from thsc.crs.entities.billing import Charge


@register_instance(
    dependencies=[
        OrderRepository,
        OrderNumberRepository,
        ScheduledOrderNumberRepository,
        SellerRepository,
        KotService,
    ]
)
class OrderApplicationService(object):
    def __init__(
        self,
        order_repository,
        order_number_repository,
        scheduled_order_number_repository,
        seller_repository,
        kot_service,
    ):
        self.order_repository = order_repository
        self.order_number_repository = order_number_repository
        self.scheduled_order_number_repository = scheduled_order_number_repository
        self.seller_repository = seller_repository
        self.kot_service = kot_service

    @session_manager(commit=True)
    @set_seller_context()
    def create_new_order(
        self, new_order_dto: NewOrderDto, user_data, seller_aggregate=None
    ):
        seller_context = pos_context.get_seller_context()
        if seller_aggregate.seller.seller_id != new_order_dto.seller_id:
            raise AuthorizationError(description="Seller Id mismatch")
        RuleEngine.action_allowed(
            action='create_order',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        seller_details = SellerDetails(
            seller_id=new_order_dto.seller_id,
            name=seller_aggregate.seller.name,
            state_code=seller_aggregate.seller.state_code,
            address=seller_aggregate.seller.gst_address,
            gst_details=seller_aggregate.seller.gst_details,
            fssai_license_number=seller_aggregate.seller.fssai_license_number,
        )

        if new_order_dto.scheduled_datetime:
            new_order_dto.order_datetime = new_order_dto.scheduled_datetime
        else:
            order_datetime = new_order_dto.order_datetime
            new_order_dto.order_datetime = (
                order_datetime if order_datetime else seller_context.current_datetime()
            )

        new_order_dto.order_date = dateutils.to_date(new_order_dto.order_datetime)
        order_number = None
        scheduled_order_number = None

        if not new_order_dto.scheduled_datetime:
            order_number = self.order_number_repository.get_next_order_number(
                new_order_dto.seller_id, new_order_dto.order_date
            )
            status = PosOrderStatus.CREATED
        else:
            scheduled_order_date = dateutils.to_date(new_order_dto.scheduled_datetime)
            scheduled_order_number = (
                self.scheduled_order_number_repository.get_next_order_number(
                    new_order_dto.seller_id, scheduled_order_date
                )
            )
            status = PosOrderStatus.SCHEDULED

        if pos_context.get_user_data():
            new_order_dto.update_added_by_in_order_item_discounts(
                pos_context.get_user_data().user
            )

        order_aggregate = OrderFactory.create_new_order(
            new_order_dto,
            order_number,
            scheduled_order_number,
            seller_details,
            seller_context.base_currency,
            status,
        )

        bill = CRSClient.create_bill(order_aggregate.order.order_id, order_aggregate)
        order_aggregate.update_bill_mapping(
            bill.bill_id, [ch.charge_id for ch in bill.charges]
        )

        discounts = [
            SplitBillDiscount(order_item.order_item_id, discounts=order_item.discounts)
            for order_item in order_aggregate.order_items
        ]

        # Adding primary bill to order's split_bills.
        primary_split_bill = SplitBill(
            bill_id=bill.bill_id,
            status=PosBillStatus.UNSETTLED,
            discounts=discounts,
            split_type=PosSplitBillSplitType.PRIMARY,
            total_price_pretax=bill.total_pretax_amount,
            total_price_posttax=bill.total_posttax_amount,
            bill_number=bill.parent_info.get('bill_number'),
        )
        order_aggregate.add_split_bill(bill=primary_split_bill)

        IntegrationEventApplicationService.create_order_event(
            order_aggregate, user_action="create_order"
        )
        self.order_repository.save(order_aggregate)
        return order_aggregate

    @session_manager(commit=True)
    @read_seller_id_from_headers()
    def edit_order(
        self,
        edit_order_dto: EditOrderDto,
        user_data,
        order_id,
        resource_version,
        seller_id=None,
    ):
        RuleEngine.action_allowed(
            action='edit_order',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        try:
            order_aggregate = self.order_repository.load_for_update(order_id, seller_id)
        except NoResultFound:
            raise ResourceNotFound(
                "Order",
                "order_id: {0} , seller_id {1} not found".format(order_id, seller_id),
            )
        if order_aggregate.order.version != resource_version:
            raise ValidationException(
                ApplicationErrors.EDIT_ORDER_RESOURCE_VERSION_MISMATCH
            )
        if order_aggregate.order.status == PosOrderStatus.SETTLED:
            raise ValidationException(
                ApplicationErrors.CANNOT_EDIT_ORDER_AFTER_SETTLEMENT
            )

        # load primary bill
        bill = CRSClient.load_bill(order_aggregate.bill_id)
        seller_aggregate = self.seller_repository.load(order_aggregate.seller_id)

        if (
            edit_order_dto.table_id != NotAssigned
            and order_aggregate.table_id != edit_order_dto.table_id
        ):
            order_aggregate.update_table_id(edit_order_dto.table_id)

        if (
            edit_order_dto.remarks != NotAssigned
            and order_aggregate.remarks != edit_order_dto.remarks
        ):
            order_aggregate.update_remarks(edit_order_dto.remarks)

        if (
            edit_order_dto.order_type != NotAssigned
            and order_aggregate.order_type != edit_order_dto.order_type
        ):
            order_aggregate.update_order_type(edit_order_dto.order_type)

        if (
            edit_order_dto.seller_type != NotAssigned
            and order_aggregate.seller_type != edit_order_dto.seller_type
        ):
            order_aggregate.update_seller_type(edit_order_dto.seller_type)

        if edit_order_dto.room_booking_detail != NotAssigned:
            order_aggregate.update_room_booking_details(
                edit_order_dto.room_booking_detail
            )

        if (
            edit_order_dto.source_of_customer != NotAssigned
            and order_aggregate.source_of_customer != edit_order_dto.source_of_customer
        ):
            order_aggregate.update_source_of_customer(edit_order_dto.source_of_customer)

        if edit_order_dto.scheduled_datetime != NotAssigned:
            order_aggregate.update_scheduled_datetime(edit_order_dto.scheduled_datetime)

        if edit_order_dto.status != NotAssigned:
            order_aggregate.update_status(edit_order_dto.status)

        if edit_order_dto.guest_count != NotAssigned:
            order_aggregate.update_guest_count(edit_order_dto.guest_count)

        if edit_order_dto.customers != NotAssigned:
            order_aggregate = self._create_update_and_delete_customers(
                order_aggregate, edit_order_dto.customers
            )

        if pos_context.get_user_data():
            edit_order_dto.update_added_by_in_order_item_discounts(
                pos_context.get_user_data().user
            )

        order_kot_dto = OrderWithKotDto(order_aggregate, kots=[])
        if edit_order_dto.order_items != NotAssigned:
            self._check_if_order_items_can_be_cancelled(
                order_items_to_update=edit_order_dto.order_items,
                order_aggregate=order_aggregate,
                seller_aggregate=seller_aggregate,
            )
            order_kot_dto = self._add_update_remove_order_items(
                order_aggregate, edit_order_dto.order_items, bill, seller_aggregate
            )

        if edit_order_dto.order_remarks != NotAssigned:
            for order_remark in edit_order_dto.order_remarks:
                order_aggregate.add_order_remark(
                    order_item_id=order_remark.order_item_id,
                    action=order_remark.action,
                    remark=order_remark.remark,
                )

        self.order_repository.update(order_aggregate)
        IntegrationEventApplicationService.edit_order_event(
            order_aggregate, user_action="update_order"
        )
        return order_kot_dto

    @staticmethod
    def _check_if_order_items_can_be_cancelled(
        order_items_to_update, order_aggregate, seller_aggregate
    ):
        order_item_cancellation_duration = (
            seller_aggregate.seller_config.order_item_cancellation_time
        )
        existing_order_items = order_aggregate.order_items

        order_item_ids_to_update = [
            order_item.order_item_id
            for order_item in order_items_to_update
            if order_item.order_item_id != NotAssigned
        ]
        order_items_to_delete = [
            order_item
            for order_item in existing_order_items
            if order_item.order_item_id not in order_item_ids_to_update
        ]
        for order_item in order_items_to_delete:
            if order_item.status in [
                PosOrderItemStatus.READY,
                PosOrderItemStatus.DELIVERED,
            ]:
                raise ValidationException(
                    error=OrderError.READY_OR_DELIVERED_ORDER_ITEMS_CANNOT_BE_REMOVED
                )
            elif order_item.status == PosOrderItemStatus.PREPARING:
                current_datetime = dateutils.current_datetime()
                acceptable_order_item_cancellation_duration = datetime.strptime(
                    order_item_cancellation_duration, '%H:%M:%S'
                ).time()
                acceptable_order_item_cancellation_delta = timedelta(
                    hours=acceptable_order_item_cancellation_duration.hour,
                    minutes=acceptable_order_item_cancellation_duration.minute,
                    seconds=acceptable_order_item_cancellation_duration.second,
                )
                acceptable_datetime = (
                    order_item.status_updated_at
                    + acceptable_order_item_cancellation_delta
                )

                if current_datetime > acceptable_datetime:
                    raise ValidationException(
                        error=OrderError.ORDER_ITEMS_SENT_TO_KITCHEN_CANNOT_BE_REMOVED
                    )

    @staticmethod
    def _create_update_and_delete_customers(
        order_aggregate, customers: List[EditPosCustomerDto]
    ):
        customers_to_update = [
            customer for customer in customers if customer.customer_id != NotAssigned
        ]
        updated_customers = order_aggregate.update_customers(customers_to_update)

        anonymous_customer = [
            customer
            for customer in order_aggregate.customers
            if customer.is_anonymous()
        ]
        customer_ids_to_not_delete = [
            customer.customer_id for customer in updated_customers
        ]

        # anonymous customer would only be deleted while setting bill_to
        if anonymous_customer:
            customer_ids_to_not_delete.append(anonymous_customer[0].customer_id)

        order_aggregate.delete_customers(customer_ids_to_not_delete)

        customers_to_create = [
            customer for customer in customers if customer.customer_id == NotAssigned
        ]

        for customer in customers_to_create:
            order_aggregate.add_customer(customer)
        return order_aggregate

    def _add_update_remove_order_items(
        self,
        order_aggregate: OrderAggregate,
        order_items: List[EditOrderItemDto],
        bill,
        seller_aggregate,
    ):
        for order_item in order_items:
            if not order_item.bill_to_type and order_aggregate.order_items:
                order_item.bill_to_type = order_aggregate.order_items[0].bill_to_type

        # Update existing Order Items
        order_items_to_update = [
            order_item
            for order_item in order_items
            if order_item.order_item_id != NotAssigned
        ]
        order_item_ids_to_update_charge_for = (
            order_aggregate.get_order_items_to_update_charges_for(
                order_item_dtos=order_items_to_update
            )
        )

        order_items_with_reduced_quantity = (
            order_aggregate.get_order_items_whose_quantity_is_being_reduced(
                order_items_to_update
            )
        )

        # Delete all order items that were not updated in this edit order request
        deleted_order_items = order_aggregate.delete_order_items_except(
            [order_item.order_item_id for order_item in order_items_to_update]
        )

        order_items_to_create_cancelled_kot_for = (
            self._get_order_items_to_create_cancelled_kot(
                deleted_order_items + order_items_with_reduced_quantity,
                seller_aggregate.seller_config.generate_kot_only_when_item_is_preparing,
            )
        )

        updated_order_items = order_aggregate.update_order_items(order_items_to_update)

        order_items_to_update_charge_for = [
            order_item
            for order_item in updated_order_items
            if order_item.order_item_id in order_item_ids_to_update_charge_for
        ]

        # Create new Order Items
        order_items_to_create = [
            order_item
            for order_item in order_items
            if order_item.order_item_id is NotAssigned
        ]

        charge_to = [order_aggregate.bill_to]

        if order_aggregate.order_items:
            order_item = [
                order_item
                for order_item in order_aggregate.order_items
                if order_item.deleted is False
            ]
            charge_to = order_item[0].charge_to

        for order_item_dto in order_items_to_create:
            order_item_dto.charge_to = charge_to

        created_order_items = order_aggregate.add_order_items(order_items_to_create)

        total_price_pretax = sum(
            order_item.total_price_pretax for order_item in order_aggregate.order_items
        )
        order_aggregate.update_total_price_pretax(total_price_pretax)

        if created_order_items:
            self._create_charge_for_new_items(
                created_order_items, order_aggregate, bill
            )

        bills_to_update = defaultdict(list)

        for order_item in order_items_to_update_charge_for:
            if order_item.is_complimentary:
                order_item.update_discounts([])
            for order_item_bill in order_item.bills:
                bills_to_update[order_item_bill.bill_id].append(order_item)

        bills_with_charges_to_cancel = []

        # cancel charge for deleted order items
        if deleted_order_items:
            bills_with_charges_to_cancel = self._cancel_charges_for_removed_items(
                deleted_order_items, order_aggregate, bills_to_update
            )

        if bills_to_update:
            crs_bills = CRSClient.load_bills(
                [
                    bill
                    for bill in list(bills_to_update.keys())
                    + bills_with_charges_to_cancel
                ]
            )
            crs_bill_dict = {bill.bill_id: bill for bill in crs_bills.bills}
            order_aggregate = self._update_charges(
                bills_to_update, crs_bill_dict, order_aggregate
            )

        kots = self.kot_service.create_kots_for_cancelled_items(
            order_items_to_create_cancelled_kot_for, order_aggregate
        )
        return OrderWithKotDto(order_aggregate, kots, removed_items=deleted_order_items)

    @staticmethod
    def _get_order_items_to_create_cancelled_kot(
        order_items, generate_kot_for_prepared_item
    ):
        order_items_to_create_cancelled_kot = []

        for order_item in order_items:
            if generate_kot_for_prepared_item:
                if order_item.status == PosOrderItemStatus.PREPARING:
                    order_items_to_create_cancelled_kot.append(
                        {**order_item.to_json(), **{"quantity": order_item.quantity}}
                    )
            elif order_item.status in [
                PosOrderItemStatus.PREPARING,
                PosOrderItemStatus.DELIVERED,
                PosOrderItemStatus.READY,
            ]:
                order_items_to_create_cancelled_kot.append(
                    {**order_item.to_json(), **{"quantity": order_item.quantity}}
                )
        return order_items_to_create_cancelled_kot

    def _create_charge_for_new_items(self, order_items, order_aggregate, bill):
        for order_item in order_items:
            if order_item.is_complimentary:
                order_item.update_discounts([])
            charge = CRSClient.create_charge(
                order_item, bill, base_currency=order_aggregate.order.base_currency
            )
            order_item_bill = OrderItemBill(
                bill_id=bill.bill_id, charge_ids=[charge.charge_id]
            )
            order_item.update_bills(bills=[order_item_bill])
            primary_split_bill = order_aggregate.get_split_bill(bill.bill_id)
            (
                pretax_amount,
                posttax_amount,
            ) = self._get_total_pretax_and_posttax_of_bill_after_charge_update(
                bill, [charge]
            )
            primary_split_bill.update_pretax_and_posttax(pretax_amount, posttax_amount)
            primary_split_bill.discounts.append(
                SplitBillDiscount(order_item.order_item_id, order_item.discounts)
            )

    def _update_charges(self, bills_to_update, crs_bill_dict, order_aggregate):
        # load all bills to be updated
        """
        calculate the proportion of charges in the existing bills so that update with discounts are also applied
        with the same proportions
        """
        for bill_id, order_items in bills_to_update.items():
            split_bill = order_aggregate.get_split_bill(bill_id)
            updated_charges = []
            crs_bill = crs_bill_dict.get(bill_id)

            for order_item in order_items:
                order_item_bill = order_item.get_bill(bill_id)
                crs_charges = [
                    charge
                    for charge in crs_bill.charges
                    if charge.charge_id in order_item_bill.charge_ids
                ]

                proportion, pretax_amount = self._get_proportion_and_charge_pretax(
                    crs_bill_dict,
                    bill_id,
                    order_item,
                    order_aggregate.order.base_currency,
                    len(bills_to_update),
                )

                charge = self._create_charge_instance_for_update(
                    charge_id=order_item_bill.charge_ids[0],
                    pretax_amount=pretax_amount,
                    charge_item=crs_charges[0].item,
                    quantity=order_item.quantity,
                )
                updated_charges.append(charge)

                updated_discounts = []

                if order_item.discounts:
                    for discount in order_item.discounts:
                        updated_amount = proportion * discount.amount
                        updated_discount = Discount(
                            discount_id=discount.discount_id,
                            name=discount.name,
                            value_type=discount.value_type,
                            value=discount.value,
                            amount=updated_amount,
                            added_by=discount.added_by,
                        )
                        updated_discounts.append(updated_discount)

                split_bill.update_discounts(order_item, updated_discounts)

            copied_crs_bill = deepcopy(crs_bill)
            updated_charges = CRSClient.update_charges(
                copied_crs_bill,
                charges_to_update=updated_charges,
                order_aggregate=order_aggregate,
            )

            (
                pretax_amount,
                posttax_amount,
            ) = self._get_total_pretax_and_posttax_of_bill_after_charge_update(
                crs_bill, updated_charges
            )
            split_bill.update_pretax_and_posttax(pretax_amount, posttax_amount)
        return order_aggregate

    @staticmethod
    def _cancel_charges_for_removed_items(
        deleted_order_items, order_aggregate, bills_to_update
    ):
        bills_with_charges_to_cancel = []

        for item in deleted_order_items:
            for bill in item.bills:
                CRSClient.cancel_charges(
                    bill_id=bill.bill_id, charge_ids=bill.charge_ids
                )
                split_bill = order_aggregate.get_split_bill(bill.bill_id)
                split_bill.remove_discount(item)

                if bill.bill_id not in bills_to_update:
                    bills_with_charges_to_cancel.append(bill.bill_id)
        return bills_with_charges_to_cancel

    @staticmethod
    def _get_order_item_pretax_amount_in_all_bills(crs_bill_dict, order_item):
        order_item_total_pretax = 0

        for bill in order_item.bills:
            crs_bill = crs_bill_dict.get(bill.bill_id)
            crs_charges = [
                charge
                for charge in crs_bill.charges
                if charge.charge_id in bill.charge_ids
            ]
            order_item_total_pretax += sum(
                charge.pretax_amount for charge in crs_charges
            )
        return order_item_total_pretax

    def _get_proportion_and_charge_pretax(
        self, crs_bill_dict, bill_id, order_item, base_currency, number_of_bills
    ):
        crs_bill = crs_bill_dict.get(bill_id)
        order_item_bill = order_item.get_bill(bill_id)
        crs_charges = [
            charge
            for charge in crs_bill.charges
            if charge.charge_id in order_item_bill.charge_ids
        ]
        order_item_pretax_amount_in_current_bill = sum(
            charge.pretax_amount for charge in crs_charges
        )
        order_item_pretax_amount_in_all_bills = (
            self._get_order_item_pretax_amount_in_all_bills(crs_bill_dict, order_item)
        )
        ratio = 1

        # complimentary item check
        if order_item_pretax_amount_in_all_bills != 0:
            ratio = (
                order_item_pretax_amount_in_current_bill.amount
                / order_item_pretax_amount_in_all_bills.amount
            )
        # Retrieving the old split bill ratio, when order_item is marked un-complimentary
        else:
            ratio = Decimal(order_item.item_detail.get(bill_id, 1 / number_of_bills))

        discount_to_be_subtracted = (
            sum(discount.amount for discount in order_item.discounts)
            if order_item.discounts
            else 0
        )
        discount_to_be_subtracted_in_this_bill = discount_to_be_subtracted * ratio

        if order_item.is_complimentary:
            order_item.item_detail[bill_id] = str(ratio)
        pretax_amount = (
            Money(0, base_currency)
            if order_item.is_complimentary
            else order_item.total_price_pretax * ratio
            - discount_to_be_subtracted_in_this_bill
        )

        return ratio, pretax_amount

    @staticmethod
    def _get_total_pretax_and_posttax_of_bill_after_charge_update(
        crs_bill, updated_charges
    ):
        updated_charge_ids = [charge.charge_id for charge in updated_charges]
        pretax_sum_of_unupdated_charges = sum(
            charge.pretax_amount
            for charge in crs_bill.charges
            if charge.charge_id not in updated_charge_ids
            and charge.status != ChargeStatus.CANCELLED
        )
        posttax_sum_of_unupdated_charges = sum(
            charge.posttax_amount
            for charge in crs_bill.charges
            if charge.charge_id not in updated_charge_ids
            and charge.status != ChargeStatus.CANCELLED
        )
        pretax_amount = pretax_sum_of_unupdated_charges + sum(
            charge.pretax_amount
            for charge in updated_charges
            if charge.status != ChargeStatus.CANCELLED
        )
        posttax_amount = posttax_sum_of_unupdated_charges + sum(
            charge.posttax_amount
            for charge in updated_charges
            if charge.status != ChargeStatus.CANCELLED
        )
        return pretax_amount, posttax_amount

    @staticmethod
    def _create_charge_instance_for_update(
        charge_id, pretax_amount, charge_item, quantity=None
    ):
        charge = Charge.create_instance_for_update(charge_id=charge_id)
        charge.item = charge_item
        charge.pretax_amount = pretax_amount
        charge.status = NotAssigned
        charge.posttax_amount = NotAssigned
        if quantity:
            charge.item.details.update({"quantity": quantity})
        return charge

    @session_manager(commit=True)
    @set_seller_context()
    def settle_bill(
        self,
        order_id,
        bill_id,
        resource_version,
        settle_order_request,
        user_data,
        seller_aggregate=None,
    ):
        seller_id = seller_aggregate.seller.seller_id
        RuleEngine.action_allowed(
            action='settle_bill',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        try:
            order_aggregate = self.order_repository.load_for_update(order_id, seller_id)
        except NoResultFound:
            raise ResourceNotFound(
                "Order",
                "order_id: {0} , seller_id {1} not found".format(order_id, seller_id),
            )
        bill = order_aggregate.get_split_bill(bill_id)
        seller_context = pos_context.get_seller_context()
        if bill.split_type == PosSplitBillSplitType.PRIMARY:
            if not order_aggregate.are_all_non_primary_bills_settled():
                raise ValidationException(
                    ApplicationErrors.CANNOT_SETTLE_PRIMARY_BILL_UNLESS_OTHER_BILLS_ARE_SETTLED
                )
            order_aggregate.mark_settled()

        if bill.status == PosBillStatus.SETTLED:
            raise ValidationException(
                ApplicationErrors.CANNOT_SETTLE_ALREADY_SETTLED_BILL
            )

        if order_aggregate.order.version != resource_version:
            raise ValidationException(
                ApplicationErrors.EDIT_ORDER_RESOURCE_VERSION_MISMATCH
            )

        if (
            settle_order_request.settlement_method
            == PosOrderSettlementMethod.SETTLE_AT_POS.value
        ):
            order_settled_dto = self._settle_at_pos(
                bill_id, order_aggregate, settle_order_request
            )
        elif (
            settle_order_request.settlement_method
            == PosOrderSettlementMethod.TRANSFER_TO_ROOM.value
        ):
            order_settled_dto = self._settle_transfer_to_room(
                bill_id,
                order_aggregate,
                settle_order_request,
                seller_context.current_datetime(),
            )
        elif (
            settle_order_request.settlement_method
            == PosOrderSettlementMethod.TRANSFER_TO_COMPANY.value
        ):
            order_settled_dto = self._settle_transfer_to_company(
                bill_id, order_aggregate, settle_order_request
            )
        else:
            raise ValidationException(ApplicationErrors.INVALID_SETTLE_BILL_METHOD)
        bill.update_settled_by(pos_context.get_user_data().user)
        bill.update_settlement_datetime(seller_context.current_calendar_datetime())
        self.order_repository.update(order_aggregate)
        IntegrationEventApplicationService.settle_bill_event(
            order_aggregate, user_action="settle_bill_in_order"
        )
        return order_settled_dto

    def _settle_at_pos(self, bill_id, order_aggregate, settle_order_request):
        bill_to_request = settle_order_request.bill_to

        if bill_to_request:
            bill_to = self._add_or_update_bill_to_to_order(
                bill_to_request, order_aggregate
            )
        else:
            bill_to = order_aggregate.get_customer(order_aggregate.bill_to)

        user_info_map = {
            c.customer_id: InvoiceChargeToInfo(
                customer_id=c.customer_id,
                name=Name(first_name=c.first_name, last_name=c.last_name),
            ).to_json()
            for c in order_aggregate.customers
        }
        bill_to_info = InvoiceBillToInfo(
            customer_id=bill_to.customer_id,
            name=Name(first_name=bill_to.first_name, last_name=bill_to.last_name),
            address=None,
            gstin=bill_to.gstin_num,
            phone=bill_to.phone,
            email=bill_to.email,
        )

        crs_bill = CRSClient.load_bill(bill_id)
        existing_charges = CRSClient.get_created_charges_from_bill(bill=crs_bill)
        all_charge_ids = [charge.charge_id for charge in existing_charges]

        CRSClient.update_charges(
            bill=crs_bill,
            charges_to_update=existing_charges,
            order_aggregate=order_aggregate,
            consume=True,
        )

        if settle_order_request.payments:
            CRSClient.add_payments(crs_bill, settle_order_request.payments)
        invoices = CRSClient.generate_invoices(
            crs_bill,
            all_charge_ids,
            bill_to_info,
            user_info_map,
            InvoiceStatus.GENERATED,
            IssuedToType.CUSTOMER,
            IssuedByType.SELLER,
            allow_mixed_charge_type_in_invoice=True,
        )

        bill = order_aggregate.get_split_bill(bill_id)
        bill.update_bill_to(bill_to.customer_id)
        bill.settle(settlement_method=PosOrderSettlementMethod.SETTLE_AT_POS)
        return OrderSettledDto(order_aggregate, invoices)

    def _settle_transfer_to_room(
        self, bill_id, order_aggregate, settle_order_request, seller_curent_datetime
    ):
        bill = order_aggregate.get_split_bill(bill_id)
        crs_bill = CRSClient.load_bill(bill_id)
        bill_to_request = settle_order_request.bill_to

        if bill_to_request:
            bill_to = self._add_or_update_bill_to_to_order(
                bill_to_request, order_aggregate
            )
        else:
            bill_to = order_aggregate.get_customer(order_aggregate.bill_to)

        created_charges = CRSClient.get_created_charges_from_bill(crs_bill)
        charge_to_order_item_mapping = {
            charge.charge_id: order_aggregate.get_order_item_by_id(
                order_item_id=charge.item.details.get("order_item_id")
            )
            for charge in created_charges
        }

        crs_booking_id = settle_order_request.room_booking_details['crs_booking_id']
        booking = CRSClient.get_booking(crs_booking_id)
        booking_version = booking.version
        booking_bill_id = None

        for charge in created_charges:
            booking_bill_id = booking.bill_id
            order_item = charge_to_order_item_mapping.get(charge.charge_id)
            expense, booking_version = CRSClient.create_expense(
                settle_order_request,
                charge,
                order_item,
                booking_version,
                extra_information={
                    "order_id": order_aggregate.order.order_id,
                    "order_number": order_aggregate.order.order_number,
                    "bill_id": bill_id,
                    "bill_number": bill.bill_number,
                    "seller_name": order_aggregate.order.seller_vo.seller_details.name,
                    "quantity": order_item.quantity,
                },
                seller_curent_datetime=seller_curent_datetime,
            )
            booking_bill_in_order_item = order_item.get_bill(bill_id=booking_bill_id)
            # Add booking bill and expense charge
            if booking_bill_in_order_item:
                order_item.add_charge_to_bill(
                    bill_id=booking_bill_in_order_item.bill_id,
                    charge_id=expense.charge_id,
                )
            else:
                order_item.add_bill(
                    bill=OrderItemBill(
                        bill_id=booking_bill_id, charge_ids=[expense.charge_id]
                    )
                )
            # remove bill which is being settled
            if order_item.get_bill(bill_id):
                order_item.remove_bill(bill_id)
        CRSClient.update_charges(
            bill=crs_bill,
            charges_to_update=created_charges,
            order_aggregate=order_aggregate,
            consume=True,
        )
        CRSClient.void_bill(bill_id)
        room_stay_id = settle_order_request.room_booking_details["room_stay_id"]
        guest_ids = [
            assigned_to['guest_id']
            for assigned_to in settle_order_request.room_booking_details["assigned_to"]
        ]

        bill.settle(settlement_method=PosOrderSettlementMethod.TRANSFER_TO_ROOM)
        bill.set_room_booking_details(
            crs_booking_id=crs_booking_id,
            room_stay_id=room_stay_id,
            guest_ids=guest_ids,
        )
        bill.update_bill_to(bill_to.customer_id)
        bill.update_bill_id(booking_bill_id)
        bill.update_extra_information({'pos_bill_id': bill_id})
        return OrderSettledDto(order_aggregate, invoices=[])

    def _settle_transfer_to_company(
        self, bill_id, order_aggregate, settle_order_request
    ):
        crs_bill = CRSClient.load_bill(bill_id)

        bill_to_request = settle_order_request.bill_to

        if bill_to_request:
            bill_to = self._add_or_update_bill_to_to_order(
                bill_to_request, order_aggregate
            )
        else:
            bill_to = order_aggregate.get_customer(order_aggregate.bill_to)

        user_info_map = {
            c.customer_id: InvoiceChargeToInfo(
                customer_id=c.customer_id,
                name=Name(first_name=c.first_name, last_name=c.last_name),
                company_profile_id=c.company_profile_id,
            ).to_json()
            for c in order_aggregate.customers
        }
        bill_to_info = InvoiceBillToInfo(
            customer_id=bill_to.customer_id,
            name=Name(first_name=bill_to.first_name, last_name=bill_to.last_name),
            address=None,
            gstin=bill_to.gstin_num,
            phone=bill_to.phone,
            email=bill_to.email,
        )

        existing_charges = [
            Charge.create_instance_from_existing_instance(charge)
            for charge in CRSClient.get_created_charges_from_bill(bill=crs_bill)
        ]

        for charge in existing_charges:
            charge.type = ChargeTypes.CREDIT
            charge.bill_to_type = ChargeBillToTypes.COMPANY
            charge.posttax_amount = NotAssigned
            charge.status = NotAssigned

        CRSClient.update_charges(
            crs_bill,
            order_aggregate=order_aggregate,
            charges_to_update=existing_charges,
            consume=True,
        )

        all_charge_ids = [charge.charge_id for charge in existing_charges]

        invoices = CRSClient.generate_invoices(
            crs_bill,
            all_charge_ids,
            bill_to_info,
            user_info_map,
            InvoiceStatus.GENERATED,
            IssuedToType.CUSTOMER,
            IssuedByType.SELLER,
            allow_mixed_charge_type_in_invoice=True,
        )

        order_aggregate.update_bill_to(bill_to.customer_id)
        bill = order_aggregate.get_split_bill(bill_id)
        bill.settle(settlement_method=PosOrderSettlementMethod.TRANSFER_TO_COMPANY)
        return OrderSettledDto(order_aggregate, invoices=invoices)

    @staticmethod
    def _add_or_update_bill_to_to_order(bill_to_request, order_aggregate):
        if bill_to_request.customer_id:
            bill_to = order_aggregate.get_customer(bill_to_request.customer_id)
            order_aggregate.update_customer_details(
                bill_to.customer_id, bill_to_request
            )
        else:
            bill_to = order_aggregate.add_customer(bill_to_request)
        return bill_to

    @session_manager(commit=False)
    @read_seller_id_from_headers()
    def print_bill(self, user_data, order_id, bill_id=None, seller_id=None):
        RuleEngine.action_allowed(
            action='print_bill',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        try:
            order_aggregate = self.order_repository.load(order_id, seller_id)
        except NoResultFound:
            raise ResourceNotFound(
                "Order",
                "order_id: {0} , seller_id {1} not found".format(order_id, seller_id),
            )
        if not bill_id:
            bill_id = order_aggregate.bill_id
        url = CRSClient.print_bill(bill_id)
        return url

    @session_manager(commit=False)
    @set_seller_context()
    def get_order(self, order_id, seller_aggregate=None):
        seller_id = seller_aggregate.seller.seller_id
        try:
            order_aggregate = self.order_repository.load(order_id, seller_id)
        except NoResultFound:
            raise ResourceNotFound(
                "Order",
                "order_id: {0} , seller_id {1} not found".format(order_id, seller_id),
            )
        order_aggregate.set_priorities(
            seller_aggregate.seller_config.acceptance_time,
            seller_aggregate.seller_config.delivery_time,
            seller_aggregate.seller_config.settlement_time,
            seller_aggregate.seller_config.priority_multiplier,
        )
        return order_aggregate

    @session_manager(commit=False)
    @read_seller_id_from_headers()
    def search_orders(
        self, pos_order_search_query: PosOrderSearchQuery, seller_id=None
    ):
        order_aggregates = self.order_repository.search_orders(pos_order_search_query)
        if pos_order_search_query.seller_id:
            if pos_order_search_query.seller_id != seller_id:
                raise AuthorizationError()
            seller_aggregate = self.seller_repository.load(
                pos_order_search_query.seller_id
            )
            [
                order_aggregate.set_priorities(
                    seller_aggregate.seller_config.acceptance_time,
                    seller_aggregate.seller_config.delivery_time,
                    seller_aggregate.seller_config.settlement_time,
                    seller_aggregate.seller_config.priority_multiplier,
                )
                for order_aggregate in order_aggregates
            ]
            if hasattr(pos_order_search_query, 'priorities'):
                order_aggregates = [
                    order_aggregate
                    for order_aggregate in order_aggregates
                    if order_aggregate.order.priority.value
                    in pos_order_search_query.priorities
                ]

        return order_aggregates

    @session_manager(commit=True)
    @read_seller_id_from_headers()
    def cancel_order(self, order_id, user_data, seller_id=None):
        RuleEngine.action_allowed(
            action='cancel_order',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        try:
            order_aggregate = self.order_repository.load_for_update(order_id, seller_id)
        except NoResultFound:
            raise ResourceNotFound(
                "Order",
                "order_id: {0} , seller_id {1} not found".format(order_id, seller_id),
            )
        order_aggregate.cancel()
        self.order_repository.update(order_aggregate)

        charge_ids = order_aggregate.get_all_charge_ids()
        CRSClient.cancel_charges(order_aggregate.bill_id, charge_ids)
        return order_aggregate

    @session_manager(commit=False)
    def count_orders(self, query):
        return self.order_repository.count_orders(query)

    @session_manager(commit=True)
    @read_seller_id_from_headers()
    def split_bill(
        self,
        split_bill_request,
        user_data,
        order_id,
        bill_id,
        resource_version,
        seller_id=None,
    ):
        RuleEngine.action_allowed(
            action='split_bill',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        try:
            order_aggregate = self.order_repository.load_for_update(order_id, seller_id)
        except NoResultFound:
            raise ResourceNotFound(
                "Order",
                "order_id: {0} , seller_id {1} not found".format(order_id, seller_id),
            )

        split_bill = order_aggregate.split_bill_dict.get(bill_id)
        split_type_requested = PosSplitBillSplitType(
            split_bill_request.get("split_type")
        )
        self._validate_if_bill_can_be_split(
            bill_id=bill_id,
            split_type_requested=split_type_requested,
            split_bill=split_bill,
            resource_version=resource_version,
            order_aggregate=order_aggregate,
        )

        bill = CRSClient.load_bill(bill_id=bill_id) if bill_id else None
        charges = CRSClient.get_created_charges_from_bill(bill=bill)
        if not charges:
            raise ValidationException(
                OrderSplitBillError.BILL_DOES_NOT_HAVE_ACTIVE_CHARGES
            )

        if split_type_requested == PosSplitBillSplitType.FOOD_AND_ALCOHOL:
            order_aggregate = self.split_by_food_and_alcohol(
                bill=bill, order_aggregate=order_aggregate
            )

        elif split_type_requested == PosSplitBillSplitType.EQUAL:
            order_aggregate = self.split_equally(
                bill=bill,
                split_bill_request=split_bill_request,
                order_aggregate=order_aggregate,
            )
        elif split_type_requested == PosSplitBillSplitType.AMOUNT:
            order_aggregate = self.split_by_amount(
                bill=bill,
                split_bill_request=split_bill_request,
                order_aggregate=order_aggregate,
            )
        elif split_type_requested == PosSplitBillSplitType.PERCENTAGE:
            order_aggregate = self.split_by_percentage(
                bill=bill,
                split_bill_request=split_bill_request,
                order_aggregate=order_aggregate,
            )
        if bill_id != order_aggregate.bill_id:
            bill_being_split = SplitBill(
                bill_id=bill_id,
                status=PosBillStatus.UNSETTLED,
                split_type=split_type_requested,
            )
            existing_split_bill = order_aggregate.get_split_bill(bill_id=bill_id)

            if existing_split_bill is None:
                order_aggregate.add_split_bill(bill=bill_being_split)
            else:
                order_aggregate.update_split_bill(bill=bill_being_split)
        self.order_repository.update(order_aggregate)
        IntegrationEventApplicationService.edit_order_event(
            order_aggregate, user_action="update_order"
        )
        return order_aggregate

    @staticmethod
    def _validate_if_bill_can_be_split(
        bill_id,
        split_type_requested,
        split_bill,
        resource_version,
        order_aggregate,
    ):
        if split_bill.status == PosBillStatus.SETTLED:
            raise ValidationException(OrderSplitBillError.SETTLED_BILL_CANNOT_BE_SPLIT)
        if order_aggregate.order.version != resource_version:
            raise ValidationException(
                ApplicationErrors.EDIT_ORDER_RESOURCE_VERSION_MISMATCH
            )

        if bill_id not in order_aggregate.split_bill_ids:
            raise ValidationException(OrderSplitBillError.BILL_DOES_NOT_EXIST)

        if (
            split_bill.split_type == PosSplitBillSplitType.FOOD_AND_ALCOHOL
            and split_bill.split_type == split_type_requested
        ):
            raise ValidationException(
                OrderSplitBillError.FOOD_AND_ALCOHOL_BILLS_CANNOT_BE_SPLIT_FURTHER
            )

    @session_manager(commit=True)
    @read_seller_id_from_headers()
    def split_items_to_different_bills(
        self, split_items_request, user_data, order_id, resource_version, seller_id=None
    ):
        RuleEngine.action_allowed(
            action='split_order_items_to_multiple_bills',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        try:
            order_aggregate = self.order_repository.load_for_update(order_id, seller_id)
        except NoResultFound:
            raise ResourceNotFound(
                "Order",
                "order_id: {0} , seller_id {1} not found".format(order_id, seller_id),
            )
        for request in split_items_request:
            bill_id_to_move_order_items_to = request.get("bill_id")
            if bill_id_to_move_order_items_to:
                if (
                    order_aggregate.split_bill_dict.get(
                        bill_id_to_move_order_items_to
                    ).status
                    == PosBillStatus.SETTLED
                ):
                    raise ValidationException(
                        OrderSplitBillError.SETTLED_BILL_CANNOT_BE_SPLIT
                    )

        if order_aggregate.order.version != resource_version:
            raise ValidationException(
                ApplicationErrors.EDIT_ORDER_RESOURCE_VERSION_MISMATCH
            )
        for item_request in split_items_request:
            bill_being_split, order_aggregate = self.split_by_item(
                order_aggregate=order_aggregate, split_item_request=item_request
            )
            existing_split_bill = order_aggregate.get_split_bill(
                bill_id=bill_being_split.bill_id
            )

            if existing_split_bill is None:
                order_aggregate.add_split_bill(bill=bill_being_split)
            else:
                order_aggregate.update_split_bill(bill=bill_being_split)

        self.order_repository.update(order_aggregate)
        IntegrationEventApplicationService.edit_order_event(
            order_aggregate, user_action="update_order"
        )
        return order_aggregate

    @staticmethod
    def split_by_food_and_alcohol(bill, order_aggregate):
        charges = CRSClient.get_created_charges_from_bill(bill=bill)
        alcohol_charges = [
            charge for charge in charges if charge.item.sku_category_id == "alcohol"
        ]

        if not alcohol_charges:
            raise ValidationException(
                OrderSplitBillError.BILL_DOES_NOT_HAVE_ALCOHOLIC_ITEMS
            )

        """
        Food charges will remain in the original bill.
        Alcohol charges will be moved to the new bill created.
        """
        for charge in alcohol_charges:
            CRSClient.cancel_charge(bill=bill, charge_id=charge.charge_id)
            charge.charge_id = NotAssigned
            charge.charge_to = order_aggregate.get_order_item_by_id(
                order_item_id=charge.item.details.get("order_item_id")
            ).charge_to

        discounts = []
        # create a new bill and add alcohol category charges to it
        new_bill = CRSClient.create_bill(
            order_id=order_aggregate.order_id,
            order_aggregate=order_aggregate,
            charges=alcohol_charges,
        )
        existing_split_bill = order_aggregate.get_split_bill(bill.bill_id)

        for charge in new_bill.charges:
            order_item = order_aggregate.get_order_item_by_id(
                order_item_id=charge.item.details.get("order_item_id")
            )
            # remove old bill
            if order_item.get_bill(bill.bill_id):
                order_item.remove_bill(bill.bill_id)
            if order_item.get_bill(new_bill.bill_id):
                order_item.add_charge_to_bill(charge.charge_id, new_bill.bill_id)
            else:
                order_item.add_bill(
                    bill=OrderItemBill(
                        bill_id=new_bill.bill_id, charge_ids=[charge.charge_id]
                    )
                )

            existing_split_bill.remove_discount(order_item)
            discounts.append(
                SplitBillDiscount(
                    order_item.order_item_id, discounts=order_item.discounts
                )
            )

        new_split_bill = SplitBill(
            bill_id=new_bill.bill_id,
            status=PosBillStatus.UNSETTLED,
            split_type=PosSplitBillSplitType.FOOD_AND_ALCOHOL,
            discounts=discounts,
            bill_number=new_bill.parent_info.get('bill_number'),
        )

        order_aggregate.add_split_bill(bill=new_split_bill)
        return order_aggregate

    @staticmethod
    def split_by_item(order_aggregate, split_item_request):
        order_item_ids = split_item_request["order_item_ids"]
        bill_id_to_move_item_to = split_item_request["bill_id"]
        discounts = []
        for order_item_id in order_item_ids:
            order_item = order_aggregate.order_item_dict.get(order_item_id)
            bills = order_item.bills
            new_charges = []
            for bill in bills:
                charge_ids = bill.charge_ids
                existing_bill = CRSClient.load_bill(bill_id=bill.bill_id)
                charges = CRSClient.get_created_charges_from_bill(bill=existing_bill)

                existing_split_bill = order_aggregate.get_split_bill(
                    bill_id=existing_bill.bill_id
                )
                existing_split_bill.remove_discount(order_item)
                total_pretax = existing_bill.total_pretax_amount
                total_posttax = existing_bill.total_posttax_amount

                for charge in charges:
                    if charge.charge_id in charge_ids:
                        CRSClient.cancel_charge(
                            bill=existing_bill, charge_id=charge.charge_id
                        )
                        total_pretax -= charge.pretax_amount
                        total_posttax -= charge.posttax_amount
                existing_split_bill.update_pretax_and_posttax(
                    total_pretax, total_posttax
                )

            if bill_id_to_move_item_to:
                bill_to_move_item_to = CRSClient.load_bill(
                    bill_id=bill_id_to_move_item_to
                )
                new_charges.append(
                    CRSClient.create_charge(
                        bill=bill_to_move_item_to,
                        order_item=order_item,
                        base_currency=order_aggregate.order.base_currency,
                    )
                )
                split_bill = order_aggregate.get_split_bill(bill_id_to_move_item_to)
                if split_bill.get_discount(order_item.order_item_id):
                    split_bill.update_discounts(order_item)
                else:
                    bill_discount = SplitBillDiscount(
                        order_item.order_item_id, order_item.discounts
                    )
                    split_bill.discounts.append(bill_discount)
            else:
                # combine these two to a single API call
                bill_to_move_item_to = CRSClient.create_bill_with_no_charges(
                    order_aggregate=order_aggregate
                )
                new_charges.append(
                    CRSClient.create_charge(
                        bill=bill_to_move_item_to, order_item=order_item
                    )
                )
                discounts.append(
                    SplitBillDiscount(order_item_id, discounts=order_item.discounts)
                )

            order_item_bill = OrderItemBill(
                bill_id=bill_to_move_item_to.bill_id,
                charge_ids=[charge.charge_id for charge in new_charges],
            )
            # add value object to order_item
            order_item.update_bills(bills=[order_item_bill])

        bill_being_split = SplitBill(
            bill_id=bill_to_move_item_to.bill_id,
            status=PosBillStatus.UNSETTLED,
            split_type=PosSplitBillSplitType.ITEM,
            discounts=discounts,
            total_price_pretax=bill_to_move_item_to.total_pretax_amount,
            total_price_posttax=bill_to_move_item_to.total_posttax_amount,
        )
        return bill_being_split, order_aggregate

    def split_equally(self, bill, order_aggregate, split_bill_request):
        """
        We are assuming that the splits would follow the structure of percentage splits.
        The percentage values would be equal in all of the splits.
        """
        return self._split_by_value(
            existing_bill=bill,
            order_aggregate=order_aggregate,
            split_bill_request=split_bill_request,
        )

    def split_by_amount(self, bill, order_aggregate, split_bill_request):
        total_pretax_amount = bill.total_pretax_amount
        total_pretax_amount_request = Money(
            sum(split["value"] for split in split_bill_request["splits"])
        )
        if total_pretax_amount != total_pretax_amount_request:
            raise ValidationException(
                OrderSplitBillError.AMOUNT_IN_SPLITS_IS_NOT_EQUAL_TO_BILL_AMOUNT
            )

        return self._split_by_value(
            existing_bill=bill,
            order_aggregate=order_aggregate,
            split_bill_request=split_bill_request,
        )

    def split_by_percentage(self, bill, order_aggregate, split_bill_request):
        return self._split_by_value(
            existing_bill=bill,
            order_aggregate=order_aggregate,
            split_bill_request=split_bill_request,
        )

    def _split_by_value(self, existing_bill, order_aggregate, split_bill_request):
        split_type = PosSplitBillSplitType(split_bill_request["split_type"])
        existing_charges = CRSClient.get_created_charges_from_bill(bill=existing_bill)
        total_pretax_amount = existing_bill.total_pretax_amount.amount

        bills_added_and_updated = []
        bill_ratio_map = {}
        """
        When split_type is of type amount, charge value would be updated by calculating the percentage of each
        split bill to total_pretax_amount of the bill being split. The updated charge value would be equal to the amount
        derived after multiplying the percentage to the existing charge value.
        """

        for i, split in enumerate(split_bill_request["splits"]):
            """
            First iteration will update charges in existing bill.
            Subsequent iterations will create new bills every time.
            """
            new_charges = []
            percentage = 0
            for existing_charge in existing_charges:
                if split_type in [
                    PosSplitBillSplitType.PERCENTAGE,
                    PosSplitBillSplitType.EQUAL,
                ]:
                    percentage = split["percentage"]
                else:
                    percentage = split["value"].amount * 100 / total_pretax_amount

                split_value = percentage * existing_charge.pretax_amount / 100

                if i == 0:
                    charge = self._create_charge_instance_for_update(
                        existing_charge.charge_id, split_value, existing_charge.item
                    )
                    new_charges.append(charge)
                else:
                    charge = Charge.create_instance_from_existing_instance(
                        existing_charge
                    )
                    charge.pretax_amount = split_value
                    charge.status = ChargeStatus.CREATED
                    charge.charge_id = NotAssigned
                    charge.posttax_amount = NotAssigned
                    new_charges.append(charge)

            if i == 0:
                updated_charges = CRSClient.update_charges(
                    bill=existing_bill,
                    charges_to_update=new_charges,
                    order_aggregate=order_aggregate,
                )
                (
                    pretax_amount,
                    posttax_amount,
                ) = self._get_total_pretax_and_posttax_of_bill_after_charge_update(
                    existing_bill, updated_charges
                )

                split_bill = order_aggregate.get_split_bill(existing_bill.bill_id)
                split_bill.update_pretax_and_posttax(pretax_amount, posttax_amount)
                bill_ratio_map[existing_bill.bill_id] = percentage
                bills_added_and_updated.append(existing_bill)
            else:
                new_bill = CRSClient.create_bill(
                    order_id=order_aggregate.order_id,
                    order_aggregate=order_aggregate,
                    charges=new_charges,
                )
                new_split_bill = SplitBill(
                    bill_id=new_bill.bill_id,
                    status=PosBillStatus.UNSETTLED,
                    split_type=split_type,
                    total_price_pretax=new_bill.total_pretax_amount,
                    total_price_posttax=new_bill.total_posttax_amount,
                    bill_number=new_bill.parent_info.get('bill_number'),
                )
                bills_added_and_updated.append(new_bill)
                order_aggregate.add_split_bill(bill=new_split_bill)
                bill_ratio_map[new_bill.bill_id] = percentage

        for bill in bills_added_and_updated:
            for charge in bill.charges:
                order_item_id = charge.item.details.get("order_item_id")
                order_item = order_aggregate.get_order_item_by_id(
                    order_item_id=order_item_id
                )
                if bill.bill_id != existing_bill.bill_id:
                    order_item.add_bill(
                        bill=OrderItemBill(
                            bill_id=bill.bill_id, charge_ids=[charge.charge_id]
                        )
                    )
                split_bill = order_aggregate.get_split_bill(bill.bill_id)

                updated_discounts = []
                if order_item:
                    for discount in order_item.discounts:
                        updated_amount = (
                            bill_ratio_map.get(bill.bill_id) * discount.amount / 100
                        )
                        updated_discount = Discount(
                            discount_id=discount.discount_id,
                            name=discount.name,
                            value_type=discount.value_type,
                            value=discount.value,
                            amount=updated_amount,
                        )
                        updated_discounts.append(updated_discount)

                    if split_bill.get_discount(order_item_id):
                        split_bill.update_discounts(order_item, updated_discounts)
                    else:
                        bill_discount = SplitBillDiscount(
                            order_item.order_item_id, updated_discounts
                        )
                        split_bill.discounts.append(bill_discount)
        return order_aggregate

    @session_manager(commit=True)
    @read_seller_id_from_headers()
    def edit_remark(
        self,
        order_id,
        remark_id,
        edit_remark_dto: EditOrderRemarkDto,
        resource_version,
        user_data,
        seller_id=None,
    ):
        RuleEngine.action_allowed(
            action='edit_order',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        try:
            order_aggregate = self.order_repository.load_for_update(order_id, seller_id)
        except NoResultFound:
            raise ResourceNotFound(
                "Order",
                "order_id: {0} , seller_id {1} not found".format(order_id, seller_id),
            )
        if order_aggregate.order.version != resource_version:
            raise ValidationException(
                ApplicationErrors.EDIT_ORDER_RESOURCE_VERSION_MISMATCH
            )
        if order_aggregate.order.status == PosOrderStatus.SETTLED:
            raise ValidationException(
                ApplicationErrors.CANNOT_EDIT_ORDER_AFTER_SETTLEMENT
            )

        updated_remark = order_aggregate.update_order_remark(
            remark_id, edit_remark_dto.remark
        )
        self.order_repository.update(order_aggregate)

        return updated_remark
