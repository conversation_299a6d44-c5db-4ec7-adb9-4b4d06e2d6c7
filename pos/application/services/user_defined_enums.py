from object_registry import register_instance
from shared_kernel.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)


@register_instance(dependencies=[CatalogServiceClient])
class UserDefinedEnums(object):
    PAYMENT_MODE = 'payment_mode'
    PAYMENT_MODE_SUB_TYPES = 'payment_mode_sub_types'

    def __init__(self, catalog_service_client: CatalogServiceClient):
        self.catalog_service_client = catalog_service_client

    def get_enum(self, seller_id, enum_name):
        enums = self.catalog_service_client.get_enums(seller_id)
        enum = next(
            (enum for enum in enums if enum.get('enum_name') == enum_name), None
        )
        return enum.get('enum_values')
