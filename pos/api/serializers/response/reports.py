from marshmallow import Schema, fields
from treebo_commons.money.money_field import MoneyField

from pos.core.api_docs import swag_schema


@swag_schema
class SummarySchema(Schema):
    pre_tax_amount = MoneyField(required=False)
    tax_amount = MoneyField(required=False)
    total_amount = MoneyField(required=False)
    order_type = fields.String()
    seller_name = fields.String()


@swag_schema
class DateWiseSummarySchema(Schema):
    pre_tax_amount = MoneyField(required=False)
    tax_amount = MoneyField(required=False)
    total_amount = MoneyField(required=False)
    date = fields.String()
    seller_name = fields.String()


@swag_schema
class OrderDetailsSchema(Schema):
    booking_id = fields.String()
    order_date = fields.String()
    order_id = fields.String()
    order_status = fields.String()
    order_type = fields.String()
    room_number = fields.String()
    seller_id = fields.String()
    pre_tax_amount = MoneyField()
    tax_amount = MoneyField()
    total_amount = MoneyField()


@swag_schema
class OrderReportSummaryResponseSchema(Schema):
    summary = fields.Nested(SummarySchema, many=True)
    datewise_summary = fields.Nested(DateWiseSummarySchema, many=True)


@swag_schema
class OrderReportDetailsResponseSchema(Schema):
    order_details = fields.Nested(OrderDetailsSchema, many=True)
