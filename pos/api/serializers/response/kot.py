from marshmallow import Schema, fields

from pos.core.api_docs import swag_schema


@swag_schema
class KotOrderItemResponseSchema(Schema):
    order_item_id = fields.String()
    name = fields.String()
    quantity = fields.Integer()
    remarks = fields.String()
    item_detail = fields.Dict()
    added_by = fields.String()


@swag_schema
class KOTResponseSchema(Schema):
    created_at = fields.LocalDateTime()
    modified_at = fields.LocalDateTime()
    kot_id = fields.Integer()
    kitchen_id = fields.String()
    order_id = fields.String()
    message = fields.String()
    order_items = fields.Nested(KotOrderItemResponseSchema, many=True)
