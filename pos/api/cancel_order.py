from flask import request

from object_registry import inject
from pos.api import pos_bp
from pos.api.serializers.response.order import OrderResponseSchema
from pos.application.services.order_application_service import OrderApplicationService
from pos.common.request_parsers import (
    read_user_data_from_request_header_and_set_context,
)
from pos.core.api_docs import swag_route
from pos.core.globals import pos_context
from shared_kernel.api_response import ApiResponse
from shared_kernel.request_parsers import read_user_data_from_request_header


@swag_route
@pos_bp.route('/orders/<string:order_id>/cancel', methods=['POST'])
@inject(order_app_service=OrderApplicationService)
def cancel_order(order_app_service, order_id):
    """The order_id of the pos order that needs to be cancelled
    ---
    operationId: cancel_order
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Mark the order with given order id cancelled
        tags:
            - Order
        parameters:
            - in: path
              name: order_id
              description: The order_id of the pos order that needs to be cancelled
              required: True
              type: string
        responses:
            200:
                description: Cancelled POS Order object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/OrderResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header_and_set_context(
        default="backend-system"
    )
    order_aggregate = order_app_service.cancel_order(order_id, user_data)
    order_response_schema = OrderResponseSchema()
    response = order_response_schema.dump(order_aggregate)
    return ApiResponse.build(data=response.data, status_code=201)
