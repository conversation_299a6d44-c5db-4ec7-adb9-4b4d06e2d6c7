from collections import namedtuple

from object_registry import inject
from pos.api import pos_bp
from pos.api.serializers.request.order import SearchPosOrderSchema
from pos.api.serializers.response.order import PosOrderSearchResponseSchema
from pos.application.dtos.search_order_query import PosOrderSearchQuery
from pos.application.services.order_application_service import OrderApplicationService
from pos.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)
from shared_kernel.api_response import ApiResponse

SearchOrderResponse = namedtuple('SearchOrderResponse', 'orders offset limit total')


@swag_route
@pos_bp.route('/orders', methods=['GET'])
@schema_wrapper_parser(SearchPosOrderSchema, param_type=RequestTypes.ARGS)
@inject(order_app_service=OrderApplicationService)
def search_order(order_app_service, parsed_request):
    """Search POS Orders
    ---
    operationId: search_order
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: true
              schema: SearchPosOrderSchema
        description: Get list of orders which match the search criteria.
        tags:
            - Order
        responses:
            200:
                description: A shallow list of detail of the POS Order objects.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/PosOrderSearchResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    query = PosOrderSearchQuery(**parsed_request)
    order_aggregates = order_app_service.search_orders(query)
    total_orders = order_app_service.count_orders(query)

    search_order_response = SearchOrderResponse(
        order_aggregates, query.offset, query.limit, total_orders
    )
    search_response_schema = PosOrderSearchResponseSchema()
    response = search_response_schema.dump(search_order_response)

    return ApiResponse.build(status_code=200, data=response.data)
