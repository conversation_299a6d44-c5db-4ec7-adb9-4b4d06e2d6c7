from flask import request

from object_registry import inject
from pos.api import pos_bp
from pos.api.serializers.request.order import SettleBillSchema
from pos.api.serializers.response.order import SettleOrderResponseSchema
from pos.application.services.order_application_service import OrderApplicationService
from pos.common.request_parsers import (
    read_user_data_from_request_header_and_set_context,
)
from pos.core.api_docs import swag_route
from pos.core.globals import pos_context
from shared_kernel.api_helpers.request_parsers import schema_wrapper_and_version_parser
from shared_kernel.api_response import ApiResponse
from shared_kernel.request_parsers import read_user_data_from_request_header


@swag_route
@pos_bp.route(
    '/orders/<string:order_id>/bills/<string:bill_id>/settle', methods=['POST']
)
@schema_wrapper_and_version_parser(SettleBillSchema)
@inject(order_app_service=OrderApplicationService)
def settle_bill(order_app_service, order_id, bill_id, resource_version, parsed_request):
    """Settles the given POS Order, and generates invoice if eligible
    ---
    operationId: settle_order
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Settle POS Order Bill and generates invoice if eligible
        tags:
            - Order
        parameters:
            - in: path
              name: order_id
              description: The order_id of the pos order for which the bill needs to be settled
              required: True
              type: string
            - in: path
              name: bill_id
              description: The bill_id of the pos order that needs to be settled
              required: True
              type: string
            - in: body
              name: settle_order_detail
              description: The details needed to settle the order
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/definitions/SettleBillSchema"
        responses:
            200:
                description: Updated POS Order object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/SettleOrderResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header_and_set_context(
        default="backend-system"
    )
    order_settled_dto = order_app_service.settle_bill(
        order_id, bill_id, resource_version, parsed_request, user_data
    )
    settle_order_response_schema = SettleOrderResponseSchema()
    response = settle_order_response_schema.dump(
        {
            'order': order_settled_dto.order_aggregate,
            'invoice_details': order_settled_dto.invoice_details,
        }
    )
    return ApiResponse.build(data=response.data, status_code=200)
