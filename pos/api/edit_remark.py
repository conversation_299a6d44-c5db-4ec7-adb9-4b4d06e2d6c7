from object_registry import inject
from pos.api import pos_bp
from pos.api.serializers.request.order import EditRemarkSchema
from pos.api.serializers.response.order import OrderRemarkSchema
from pos.application.services.order_application_service import OrderApplicationService
from pos.common.request_parsers import (
    read_user_data_from_request_header_and_set_context,
)
from pos.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import schema_wrapper_and_version_parser
from shared_kernel.api_response import ApiResponse


@swag_route
@pos_bp.route('/orders/<string:order_id>/remarks/<int:remark_id>', methods=['PATCH'])
@schema_wrapper_and_version_parser(EditRemarkSchema)
@inject(order_app_service=OrderApplicationService)
def edit_remark(
    order_app_service, order_id, remark_id, resource_version, parsed_request
):
    """Edit the given POS Remark
    ---
    operationId: edit_remark
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: Edit the POS Remark
        tags:
            - Order
        parameters:
            - in: path
              name: order_id
              description: The order_id of the pos remark that needs to be edited
              required: True
              type: string
            - in: path
              name: remark_id
              description: The remark_id of the pos remark that needs to be edited
              required: True
              type: int
            - in: body
              name: edit_remark_details
              description: The remark details which needs to be updated
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/definitions/EditRemarkSchema"
        responses:
            200:
                description: Updated POS Order object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/OrderRemarkSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header_and_set_context(
        default="backend-system"
    )
    order_aggregate = order_app_service.edit_remark(
        order_id, remark_id, parsed_request, resource_version, user_data
    )
    order_remark_schema = OrderRemarkSchema()
    response = order_remark_schema.dump(order_aggregate)
    return ApiResponse.build(data=response.data, status_code=200)
