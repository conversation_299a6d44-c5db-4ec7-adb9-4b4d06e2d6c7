CREATE FUNCTION hotel_checkin_date(timestamptz, varchar)
returns date AS $$
SELECT
       CASE
              WHEN $1::time at time zone 'Asia/Calcutta' < $2::time
              THEN date($1::date at time zone 'Asia/Calcutta' - interval '1 DAY')
              ELSE     date($1::date at time zone 'Asia/Calcutta')
       END $$ language sql; 


CREATE FUNCTION hotel_checkout_date(timestamptz, varchar)
returns date AS $$
SELECT
       CASE
              WHEN $1::time at time zone 'Asia/Calcutta' > $2::time
              THEN date($1::date at time zone 'Asia/Calcutta' + interval '1 DAY')
              ELSE     date($1::date at time zone 'Asia/Calcutta')
       END $$ language sql;


CREATE OR REPLACE FUNCTION public.array_merge(arr1 anyarray, arr2 anyarray)
    returns anyarray language sql immutable
as $$
    select array_agg(distinct elem order by elem)
    from (
        select unnest(arr1) elem 
        union
        select unnest(arr2)
    ) s
$$;


CREATE AGGREGATE array_merge_agg(anyarray) (
    sfunc = array_merge,
    stype = anyarray
);
