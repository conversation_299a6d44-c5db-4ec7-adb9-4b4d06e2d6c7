from object_registry import register_instance
from prometheus.infrastructure.external_clients.catalog_service.dtos.sku_dto import (
    SkuDTO,
)
from shared_kernel.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from shared_kernel.infrastructure.external_clients.service_registry_client import (
    ServiceRegistryClient,
)


@register_instance()
class CatalogServiceClient(BaseExternalClient):
    page_map = {
        'get_room_type_configs': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/properties/{hotel_id}/room-type-configs",
        ),
        'properties_v3_get_name': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/api/v3/properties/?fields=name&id={hotel_id}",
        ),
        'property_v2_get_details': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v2/properties?property_id={hotel_id}",
        ),
        'get_enums': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/enums",
        ),
        'get_countries': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/countries/",
        ),
        'get_states': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/states?country_id={country_id}",
        ),
        'get_channels': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/channels/",
        ),
        'get_sub_channel_for_channel': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/channels/{channel_code}/subchannels/",
        ),
        'get_skus': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/properties/{property_id}/sku/",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_catalog_service_url()

    def get_room_type_config(self, hotel_id):
        page_name = "get_room_type_configs"
        url_params = dict(hotel_id=hotel_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response

    def get_property_name(self, hotel_id):
        page_name = "properties_v3_get_name"
        url_params = dict(hotel_id=hotel_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response.get("data")[0].get("name")

    def get_property_details(self, hotel_id):
        property_detail = None
        page_name = "property_v2_get_details"
        url_params = dict(hotel_id=hotel_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        hotels = response.json_response
        for hotel in hotels:
            if hotel['id'] == hotel_id:
                property_detail = hotel
        return property_detail

    def get_enums(self, property_id=None, enum_names=None):
        page_name = "get_enums"
        optional_url_params = dict()
        if property_id:
            optional_url_params['property_id'] = property_id
        if enum_names:
            if isinstance(enum_names, list):
                enum_names = ','.join(enum_names)
            optional_url_params['enum_names'] = enum_names
        response = self.make_call(
            page_name=page_name, optional_url_params=optional_url_params
        )
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.content
                )
            )
        enums = response.json_response
        enums_list = []
        for enum in enums:
            enum_values = [
                enum_value.get('value') for enum_value in enum.get('enum_values')
            ]
            enums_list.append(
                dict(enum_name=enum['enum_name'], enum_values=enum_values)
            )
        return enums_list

    def get_countries(self):
        page_name = "get_countries"
        response = self.make_call(page_name)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response

    def get_states(self, country_id):
        page_name = "get_states"
        url_params = dict(country_id=country_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response

    def get_channels(self):
        page_name = "get_channels"
        response = self.make_call(page_name)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response

    def get_sub_channel_for_channel(self, channel_code):
        page_name = "get_sub_channel_for_channel"
        url_params = dict(channel_code=channel_code)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response

    def get_skus(self, property_id, for_inclusions=False, codes=None):
        page_name = "get_skus"
        url_params = dict(property_id=property_id)
        response = self.make_call(
            page_name,
            url_parameters=url_params,
            optional_url_params=dict(for_inclusions=for_inclusions, codes=codes),
        )
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return [
            SkuDTO.create_from_catalog_data(sku)
            for sku in response.json_response
            if isinstance(sku, dict)
        ]
