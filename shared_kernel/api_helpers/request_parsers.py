import functools
import logging
import sys
from datetime import datetime
from enum import Enum
from typing import Dict, List, Type, Union

from flask import request
from marshmallow import Schema as MarshmallowSchema
from werkzeug.exceptions import UnsupportedMediaType

from ths_common.exceptions import ApiValidationException

logger = logging.getLogger(__name__)

# Conditionally import pydantic
if sys.version_info >= (3, 7):
    try:
        from pydantic import BaseModel as PydanticBaseModel
        from pydantic import ValidationError as PydanticValidationError

        PYDANTIC_SUPPORTED = True
    except ImportError:
        PydanticBaseModel = object
        PydanticValidationError = None
        PYDANTIC_SUPPORTED = False
else:
    PydanticBaseModel = object
    PydanticValidationError = None
    PYDANTIC_SUPPORTED = False


ReqSchema = Union[Type[MarshmallowSchema], Type[PydanticBaseModel]]


class RequestTypes(Enum):
    JSON = 'json'
    ARGS = 'args'
    FORM = 'form'


def prepare_error_list(marshmallow_errors):
    errors = []
    for k, v in marshmallow_errors.items():
        if isinstance(v, dict):
            error_list = prepare_error_list(v)
            error_list = [
                {
                    "field": "{parent_field}.{child_field}".format(
                        parent_field=k, child_field=error["field"]
                    ),
                    "error": error["error"],
                }
                for error in error_list
            ]
            errors.extend(error_list)
        else:
            v = v[0] if isinstance(v, list) else v
            errors.append(
                {
                    "field": k,
                    "error": "[{}] -> {}".format(k.replace("_", " ").title(), v),
                }
            )
    return errors


def prepare_pydantic_error_list(error: PydanticValidationError) -> List[Dict[str, str]]:
    formatted_errors = []
    for err in error.errors():
        field_path = ".".join(str(loc) for loc in err["loc"])
        error_message = err["msg"]
        formatted_errors.append({"field": field_path, "error": error_message})
    return formatted_errors


def parse_data_and_version(
    schema: ReqSchema, request_attr: RequestTypes, has_version: bool, many: bool
):
    def parse_data_and_version_inner(func):
        def wrapper(*args, **kwargs):
            if request_attr == RequestTypes.JSON:
                if 'application/json' not in request.content_type:
                    raise UnsupportedMediaType()
            if request_attr == RequestTypes.JSON and request.content_length == 0:
                data = dict()
            else:
                data = getattr(request, request_attr.value)

            if request_attr in [RequestTypes.ARGS, RequestTypes.FORM]:
                data = dict(data=data.to_dict())

            logger.info(
                "API request={0}, method={1}, data={2}".format(
                    request.base_url, request.method, data
                )
            )

            data['request_time'] = data.get('request_time', datetime.now())

            req_data = data.get('data', dict())

            if isinstance(schema, type) and issubclass(schema, MarshmallowSchema):
                parsed_data = schema().load(req_data, many=many)
                if parsed_data.errors:
                    logger.info("APIValidationError: %s", parsed_data.errors)
                    error_messages = prepare_error_list(parsed_data.errors)
                    raise ApiValidationException(error_messages=error_messages)

                kwargs['parsed_request'] = parsed_data.data

            elif isinstance(schema, type) and issubclass(schema, PydanticBaseModel):
                if not PYDANTIC_SUPPORTED:
                    raise RuntimeError(
                        "Pydantic is not supported in this Python version or not installed."
                    )

                try:
                    if many and isinstance(req_data, list):
                        kwargs['parsed_request'] = [schema(**rd) for rd in req_data]
                    else:
                        kwargs['parsed_request'] = schema(**req_data)
                except PydanticValidationError as e:
                    logger.info("APIValidationError: %s", e.errors())
                    error_messages = prepare_pydantic_error_list(e)
                    raise ApiValidationException(error_messages=error_messages)

            else:
                raise RuntimeError(
                    f"invalid req schema type given for parsing. {type(schema)}"
                )

            if has_version:
                if 'resource_version' not in data:
                    error_messages = [
                        {
                            "field": "resource_version",
                            "error": "[Resource Version] -> This is a required field",
                        }
                    ]
                    raise ApiValidationException(error_messages=error_messages)
                kwargs['resource_version'] = data.get('resource_version')

            return func(*args, **kwargs)

        return functools.update_wrapper(wrapper, func)

    return parse_data_and_version_inner


def schema_wrapper_and_version_parser(
    schema: ReqSchema, many: bool = False, param_type: RequestTypes = RequestTypes.JSON
):
    return parse_data_and_version(schema, param_type, has_version=True, many=many)


def schema_wrapper_parser(
    schema: ReqSchema, many: bool = False, param_type: RequestTypes = RequestTypes.JSON
):
    return parse_data_and_version(schema, param_type, has_version=False, many=many)
