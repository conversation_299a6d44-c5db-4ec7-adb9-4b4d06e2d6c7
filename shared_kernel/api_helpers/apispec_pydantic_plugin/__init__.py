import copy
from contextlib import suppress
from typing import Any, Optional

from apispec import APISpec, BasePlugin
from apispec.exceptions import DuplicateComponentNameError

from shared_kernel.api_helpers.apispec_pydantic_plugin.errors import (
    ResolverNotFoundError,
)
from shared_kernel.api_helpers.apispec_pydantic_plugin.models import BaseModelAlias
from shared_kernel.api_helpers.apispec_pydantic_plugin.normalizer import (
    APISpecSchemaNormalizer,
)
from shared_kernel.api_helpers.apispec_pydantic_plugin.resolver import SchemaResolver


class PydanticPlugin(BasePlugin):
    """APISpec plugin for translating pydantic models to OpenAPI/JSONSchema format."""

    spec: Optional[APISpec]
    openapi_version: Optional[Any]
    resolver: Optional[SchemaResolver]

    def __init__(self) -> None:
        self.spec = None
        self.openapi_version = None

        self.resolver = None

    def init_spec(self, spec: APISpec) -> None:
        """Initialize plugin with APISpec object

        :param APISpec spec: APISpec object this plugin instance is attached to
        """
        super().init_spec(spec=spec)
        self.spec = spec
        self.resolver = SchemaResolver(spec=self.spec)

    def schema_helper(
        self,
        name: str,  # noqa: ARG002
        definition: dict[Any, Any],  # noqa: ARG002
        **kwargs: Any,
    ) -> Optional[dict[str, Any]]:
        """Return the schema of the requested identifier.

        Parameters:
            name: The identifier which a schema can be referenced.
            definition: Schema definition
            kwargs: All additional keyword arguments sent to `APISpec.schema()`
        """
        model: Optional[BaseModelAlias] = kwargs.pop("schema", None)
        if model is None:
            return None
        schema = model.model_json_schema(ref_template="#/components/schemas/{model}")
        schema = APISpecSchemaNormalizer.normalize_pydantic_schema(schema)

        def_key = "$defs"
        if self.spec and def_key in schema:
            for k, v in schema[def_key].items():
                with suppress(DuplicateComponentNameError):
                    self.spec.components.schema(k, v)

            del schema[def_key]

        return schema

    def operation_helper(
        self,
        path: Optional[str] = None,
        operations: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        if self.resolver is None:
            raise ResolverNotFoundError("SchemaResolver was not initialized")
        self.resolver.resolve_operations(operations=operations, kwargs=kwargs)
