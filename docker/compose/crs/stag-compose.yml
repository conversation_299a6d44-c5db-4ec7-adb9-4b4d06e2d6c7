version: "2"

services:
  prometheus-apiservice:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    hostname: "${HOSTNAME}"
    ports:
      - "${HOST_PORT}:8000"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/prometheus/"
      - "${CONFIG_ROOT}/prometheus/config_files:/usr/src/app/config_files"
    container_name: "crs_app_server"
    restart: always
    entrypoint: /usr/src/app/gunicorn.sh
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}

  prometheus-reports-apiservice:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    hostname: "${HOSTNAME}"
    ports:
      - "${HOST_PORT}:8082"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/prometheus/"
      - "${CONFIG_ROOT}/prometheus/config_files:/usr/src/app/config_files"
    container_name: "crs_reports_app_server"
    restart: always
    entrypoint: /usr/src/app/reports_gunicorn.sh
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}

  prometheus-event-daemon:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/prometheus/"
      - "${CONFIG_ROOT}/prometheus/config_files:/usr/src/app/config_files"
    container_name: "crs_event_publisher_${TENANT_ID}"
    restart: always
    hostname: "${HOSTNAME}"
    entrypoint: ["/usr/src/app/prometheus/workers/integration_event_worker", "${TENANT_ID}"]
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}

  prometheus-task-daemon:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/prometheus/"
      - "${CONFIG_ROOT}/prometheus/config_files:/usr/src/app/config_files"
    container_name: "crs_job_scheduler_${TENANT_ID}"
    restart: always
    hostname: "${HOSTNAME}"
    entrypoint: ["/usr/src/app/prometheus/workers/job_scheduler_worker", "${TENANT_ID}"]
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}

  prometheus-task-executor:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/prometheus/"
      - "${CONFIG_ROOT}/prometheus/config_files:/usr/src/app/config_files"
    container_name: "crs_job_executor_${TENANT_ID}"
    restart: always
    hostname: "${HOSTNAME}"
    entrypoint: ["/usr/src/app/prometheus/workers/job_consumer_worker", "${TENANT_ID}"]
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}

  prometheus-catalog-consumer:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/prometheus/"
      - "${CONFIG_ROOT}/prometheus/config_files:/usr/src/app/config_files"
    container_name: "crs_catalog_consumer_${TENANT_ID}"
    restart: always
    hostname: "${HOSTNAME}"
    entrypoint: ["/usr/src/app/prometheus/workers/catalog_consumer_worker", "${TENANT_ID}"]
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}

  prometheus-erp-consumer:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/prometheus"
      - "${HOST_STATIC_ROOT}:/var/www/static/"
      - "${CONFIG_ROOT}/prometheus/config_files:/usr/src/app/config_files"
    container_name: "prometheus_erp_event_consumer"
    hostname: "${HOSTNAME}"
    entrypoint: ["/usr/src/app/prometheus/workers/erp_consumer_worker", "${TENANT_ID}"]
    tty: true
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}

  prometheus-funding-worker:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/"
      - "${HOST_STATIC_ROOT}:/var/www/static/"
      - "${CONFIG_ROOT}/prometheus/config_files:/usr/src/app/config_files"
    container_name: "crs_funding_worker_${TENANT_ID}"
    hostname: "${HOSTNAME}"
    entrypoint: ["/usr/src/app/prometheus/workers/funding_workerr", "${TENANT_ID}"]
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}
