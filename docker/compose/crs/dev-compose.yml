version: "2.1"

services:
  prometheus-apiservice:
    # build:
    #   context: ../../
    image: "${DOCKER_REGISTRY}/prometheus-dev:${VERSION}"
    ports:
      - "${HOST_PORT}:8000"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/"
      - "${HOST_STATIC_ROOT}:/var/www/static/"
      - "${CONFIG_ROOT}/prometheus/config_files:/usr/src/app/config_files"
    container_name: "prometheus_app_service"
    hostname: "${HOSTNAME}"
    entrypoint: /usr/src/app/gunicorn.sh
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"

    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    links:
        - rabbitmq
        - db

  prometheus-event-daemon:
    image: "${DOCKER_REGISTRY}/prometheus-dev:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/"
      - "${HOST_STATIC_ROOT}:/var/www/static/"
    container_name: "prometheus_event_daemon"
    hostname: "${HOSTNAME}"
    entrypoint: /usr/src/app/workers/integration_event_worker
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"

    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      prometheus-apiservice:
        condition: service_started
    links:
        - rabbitmq
        - db

  prometheus-task-daemon:
    image: "${DOCKER_REGISTRY}/prometheus-dev:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/"
      - "${HOST_STATIC_ROOT}:/var/www/static/"
    container_name: "prometheus_task_daemon"
    hostname: "${HOSTNAME}"
    entrypoint: /usr/src/app/workers/job_scheduler_worker
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"

    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      prometheus-apiservice:
        condition: service_started
    links:
        - rabbitmq
        - db

  prometheus-task-executor:
    image: "${DOCKER_REGISTRY}/prometheus-dev:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/"
      - "${HOST_STATIC_ROOT}:/var/www/static/"
    container_name: "prometheus_task_executor"
    hostname: "${HOSTNAME}"
    entrypoint: /usr/src/app/workers/job_consumer_worker
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"

    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      prometheus-apiservice:
        condition: service_started
    links:
        - rabbitmq
        - db

  prometheus-catalog-consumer:
    image: "${DOCKER_REGISTRY}/prometheus-dev:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/"
      - "${HOST_STATIC_ROOT}:/var/www/static/"
    container_name: "prometheus_catalog_consumer"
    hostname: "${HOSTNAME}"
    entrypoint: /usr/src/app/workers/catalog_consumer_worker
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"

    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      prometheus-apiservice:
        condition: service_started
    links:
        - rabbitmq
        - db

  prometheus-erp-consumer:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/prometheus"
      - "${HOST_STATIC_ROOT}:/var/www/static/"
      - "${CONFIG_ROOT}/prometheus/config_files:/usr/src/app/config_files"
    container_name: "prometheus_erp_event_consumer"
    hostname: "${HOSTNAME}"
    entrypoint: ["/usr/src/app/prometheus/workers/erp_consumer_worker", "${TENANT_ID}"]
    tty: true
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"

    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      prometheus-apiservice:
        condition: service_started
      links:
        - rabbitmq
        - db

  db:
    image: "postgres:9.6.5"
    container_name: "${DB_HOST}"
    hostname: "${DB_HOST}"
    environment:
      POSTGRES_USER: "${DB_USER}"
      POSTGRES_PASSWORD: "${DB_PASSWORD}"
      POSTGRES_DB: "${DB_NAME}"
    ports:
      - "${DB_PORT}:5432"
    tty: true
    healthcheck:
      test: "exit 0"

  rabbitmq:
    image: "${DOCKER_REGISTRY}/rmq_curl"
    ports:
     - "${RABBITMQ_PORT_ADMIN}:15672"
     - "${RABBITMQ_PORT}:5672"
    container_name: "${RABBITMQ_HOST}"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:15672"]
      interval: 30s
      timeout: 120s
      retries: 1