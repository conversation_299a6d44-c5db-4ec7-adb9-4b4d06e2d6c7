# coding=utf-8
"""
Invoke Commands
"""
import re

from invoke import task

# flake8: noqa


@task
def pylint(ctx):
    """
    Run flake8 on codebase
    Eg: invoke pylint
    """
    command = 'pylint --rcfile=.pylintrc prometheus'
    result = ctx.run(command, warn=True, hide=True)
    data = result.stdout.splitlines()[-2]
    score, *extra = re.findall(r'\d+.\d+', data)
    expected_score = '8.0'
    if score < expected_score:
        raise Exception(
            'current pylint score is %s, it should be greater than %s'
            % (score, expected_score)
        )
    else:
        print('current pylint score is %s' % score)


@task
def test(ctx, report=False, html=False, xml=False):
    """
    Run the tests.
    Eg: invoke test --report --html
    """
    command = 'coverage run --source prometheus -m pytest prometheus/tests --junitxml=junit.xml'
    if report:
        command += ' && coverage report'
    if html:
        command += ' && coverage html'
    if xml:
        command += ' && coverage xml'
    result = ctx.run(command, warn=True, hide=True)
    data = result.stdout.splitlines()[-1]

    if report:
        passed, *extra = re.findall(r'\d+.\d+%', data)
        if passed < '64.00%':
            raise Exception(
                'current coverage is %s, it should be >= 64.00 percent' % passed
            )
        else:
            print('current coverage is %s' % passed)
