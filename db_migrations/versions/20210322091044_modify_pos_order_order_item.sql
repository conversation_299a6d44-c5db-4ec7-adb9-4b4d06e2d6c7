-- revision: '20210322091044_modify_pos_order_order_item'
-- down_revision: '20210414192913_add_charges_trasnferred_to_ar'

-- upgrade
alter table pos_order add column scheduled_datetime timestamp with time zone;
alter table pos_order add column reservation_id character varying;
alter table pos_order add column total_price_pretax numeric(15,4);
alter table pos_order add column status_updated_at timestamp with time zone;

alter table pos_order_item add column is_complimentary boolean default false;
alter table pos_order_item add column remarks character varying;
alter table pos_order_item add column status_updated_at timestamp with time zone;

alter table pos_customer add column company_profile_id character varying;
alter table seller add column seller_config jsonb;
alter table seller add column seller_type character varying;

CREATE TABLE kitchen_order_ticket (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    kot_id integer NOT NULL,
    order_id character varying NOT NULL,
    order_items jsonb,
    deleted boolean
);

-- downgrade
alter table pos_order drop column scheduled_datetime;
alter table pos_order drop column reservation_id;
alter table pos_order drop column total_price_pretax;
alter table pos_order drop column status_updated_at;

alter table pos_order_item drop column is_complimentary;
alter table pos_order_item drop column remarks;
alter table pos_order_item drop column status_updated_at;

alter table pos_customer drop column company_profile_id;

alter table seller drop column seller_config;
alter table seller drop column seller_type;


drop table kitchen_order_ticket;