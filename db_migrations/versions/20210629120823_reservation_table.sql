-- revision: '20210629120823_reservation_table'
-- down_revision: '20210617112540_enhanced_booking_customer'

-- upgrade
CREATE TABLE reservation (
  duration interval,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  modified_at timestamp with time zone DEFAULT now() NOT NULL,
  start_datetime timestamp with time zone NOT NULL,
  deleted boolean DEFAULT FALSE,
  reservation_id character varying PRIMARY KEY NOT NULL,
  order_id character varying,
  table_id character varying,
  seller_id character varying,
  guests jsonb,
  occasion character varying,
  allergens character varying,
  special_requests character varying,
  status character varying NOT NULL
);

CREATE TABLE seller_table (
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  modified_at timestamp with time zone DEFAULT now() NOT NULL,
  status_updated_at timestamptz,
  deleted boolean DEFAULT FALSE,
  table_id CHARACTER VARYING PRIMARY KEY NOT NULL,
  seller_id CHARACTER VARYING  NOT NULL,
  name CHARACTER VARYING,
  table_number CHARACTER VARYING,
  current_status CHARACTER VARYING
);

-- downgrade
DROP TABLE reservation;
DROP TABLE seller_table;