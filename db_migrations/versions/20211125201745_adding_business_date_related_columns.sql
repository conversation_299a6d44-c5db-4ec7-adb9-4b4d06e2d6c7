-- revision: '20211125201745_adding_business_date_related_columns'
-- down_revision: '20211118124352_rename_report_date_to_business_date'

-- upgrade
ALTER TABLE charge ADD COLUMN applicable_business_date DATE;
ALTER TABLE payment ADD COLUMN payment_business_date DATE;
ALTER TABLE payment ADD COLUMN posting_date DATE;

ALTER TABLE booking ADD COLUMN checkin_business_date DATE;
ALTER TABLE booking ADD COLUMN checkout_business_date DATE;
ALTER TABLE booking ADD COLUMN actual_checkout_business_date DATE;

ALTER TABLE room_stay ADD COLUMN checkin_business_date DATE;
ALTER TABLE room_stay ADD COLUMN checkout_business_date DATE;
ALTER TABLE room_stay ADD COLUMN actual_checkout_business_date DATE;

ALTER TABLE room_allocation ADD COLUMN checkin_business_date DATE;
ALTER TABLE room_allocation ADD COLUMN checkout_business_date DATE;

ALTER TABLE guest_stay ADD COLUMN checkin_business_date DATE;
<PERSON>TER TABLE guest_stay ADD COLUMN checkout_business_date DATE;
<PERSON>TER TABLE guest_stay ADD COLUMN actual_checkout_business_date DATE;

ALTER TABLE guest_allocation ADD COLUMN checkin_business_date DATE;
ALTER TABLE guest_allocation ADD COLUMN checkout_business_date DATE;

ALTER TABLE expense ADD COLUMN applicable_business_date DATE;

-- downgrade
ALTER TABLE charge DROP COLUMN applicable_business_date;
ALTER TABLE payment DROP COLUMN payment_business_date;
ALTER TABLE payment DROP COLUMN posting_date;

ALTER TABLE booking DROP COLUMN checkin_business_date;
ALTER TABLE booking DROP COLUMN checkout_business_date;
ALTER TABLE booking DROP COLUMN actual_checkout_business_date;

ALTER TABLE room_stay DROP COLUMN checkin_business_date;
ALTER TABLE room_stay DROP COLUMN checkout_business_date;
ALTER TABLE room_stay DROP COLUMN actual_checkout_business_date;

ALTER TABLE room_allocation DROP COLUMN checkin_business_date;
ALTER TABLE room_allocation DROP COLUMN checkout_business_date;

ALTER TABLE guest_stay DROP COLUMN checkin_business_date;
ALTER TABLE guest_stay DROP COLUMN checkout_business_date;
ALTER TABLE guest_stay DROP COLUMN actual_checkout_business_date;

ALTER TABLE guest_allocation DROP COLUMN checkin_business_date;
ALTER TABLE guest_allocation DROP COLUMN checkout_business_date;

ALTER TABLE expense DROP COLUMN applicable_business_date;
