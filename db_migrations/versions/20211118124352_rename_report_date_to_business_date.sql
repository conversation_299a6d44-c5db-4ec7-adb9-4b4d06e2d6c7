-- revision: '20211118124352_rename_report_date_to_business_date'
-- down_revision: '20211115235211_credit_account_categorisation'

-- upgrade
DROP INDEX ix_glrv2_report_date;
DROP INDEX ix_glrv2_hotel_id_report_date_uniq;
DROP INDEX ix_arlrv2_report_date;
DROP INDEX ix_arlrv2_report_date_hotel_id_uniq;
DROP INDEX ix_dlrv2_report_date;
DROP INDEX ix_dlrv2_report_date_hotel_id_uniq;

ALTER TABLE guest_ledger_report_v2 RENAME report_date TO business_date;
ALTER TABLE deposit_ledger_report_v2 RENAME report_date TO business_date;
ALTER TABLE ar_ledger_report_v2 RENAME report_date TO business_date;

CREATE INDEX ix_glrv2_business_date ON guest_ledger_report_v2 USING btree (business_date);
CREATE UNIQUE INDEX ix_glrv2_hotel_id_business_date_uniq ON guest_ledger_report_v2 USING btree (hotel_id, business_date);

CREATE INDEX ix_arlrv2_business_date ON ar_ledger_report_v2 USING btree (business_date);
CREATE UNIQUE INDEX ix_arlrv2_business_date_hotel_id_uniq ON ar_ledger_report_v2 USING btree (hotel_id, business_date);

CREATE INDEX ix_dlrv2_business_date ON deposit_ledger_report_v2 USING btree (business_date);
CREATE UNIQUE INDEX ix_dlrv2_business_date_hotel_id_uniq ON deposit_ledger_report_v2 USING btree (hotel_id, business_date);

-- downgrade
DROP INDEX ix_glrv2_business_date;
DROP INDEX ix_glrv2_hotel_id_business_date_uniq;

DROP INDEX ix_arlrv2_business_date;
DROP INDEX ix_arlrv2_business_date_hotel_id_uniq;

DROP INDEX ix_dlrv2_business_date;
DROP INDEX ix_dlrv2_business_date_hotel_id_uniq;

ALTER TABLE guest_ledger_report_v2 RENAME business_date TO report_date;
ALTER TABLE deposit_ledger_report_v2 RENAME business_date TO report_date;
ALTER TABLE ar_ledger_report_v2 RENAME business_date TO report_date;

CREATE INDEX ix_glrv2_report_date ON guest_ledger_report_v2 USING btree (report_date);
CREATE UNIQUE INDEX ix_glrv2_hotel_id_report_date_uniq ON guest_ledger_report_v2 USING btree (hotel_id, report_date);

CREATE INDEX ix_arlrv2_report_date ON ar_ledger_report_v2 USING btree (report_date);
CREATE UNIQUE INDEX ix_arlrv2_report_date_hotel_id_uniq ON ar_ledger_report_v2 USING btree (hotel_id, report_date);

CREATE INDEX ix_dlrv2_report_date ON deposit_ledger_report_v2 USING btree (report_date);
CREATE UNIQUE INDEX ix_dlrv2_report_date_hotel_id_uniq ON deposit_ledger_report_v2 USING btree (hotel_id, report_date);