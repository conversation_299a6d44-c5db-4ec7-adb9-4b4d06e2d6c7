-- revision: '20250701120000_einvoicing_audit_trail_table'
-- down_revision: '20250617182800_added_fssai_number'

-- upgrade
CREATE TABLE einvoicing_audit_trail(
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    modified_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    einvoicing_audit_trail_id SERIAL PRIMARY KEY,
    booking_id VARCHAR,
    invoice_id VARCHAR,
    credit_note_id VARCHAR,
    bill_id VARCHAR,
    hotel_id VARCHAR NOT NULL,
    event_type VARCHAR NOT NULL,
    action_type VARCHAR,
    user_action VARCHAR,
    user_id VARCHAR,
    user_type VA<PERSON>HA<PERSON>,
    auth_id VARCHAR,
    application VARCHAR,
    request_id VARCHAR,
    cleartax_error_code VARCHAR,
    cleartax_error_message TEXT,
    cleartax_response_payload JSON,
    action_datetime TIMESTAMPTZ NOT NULL,
    remarks TEXT
);

CREATE INDEX idx_einvoicing_audit_trail_booking_id ON einvoicing_audit_trail(booking_id);
CREATE INDEX idx_einvoicing_audit_trail_invoice_id ON einvoicing_audit_trail(invoice_id);
CREATE INDEX idx_einvoicing_audit_trail_hotel_id ON einvoicing_audit_trail(hotel_id);
CREATE INDEX idx_einvoicing_audit_trail_event_type ON einvoicing_audit_trail(event_type);
CREATE INDEX idx_einvoicing_audit_trail_action_datetime ON einvoicing_audit_trail(action_datetime);

-- downgrade
DROP TABLE einvoicing_audit_trail;
