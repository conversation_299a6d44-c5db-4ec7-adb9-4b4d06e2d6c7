-- revision: '20210728211255_split_bill'
-- down_revision: '20210816105004_added_group_name_company_details_travel_agent_details_in_booking'

-- upgrade
alter table bill add column status character varying;

alter table kitchen_order_ticket add column message character varying;

alter table pos_order_item add column bills jsonb;
alter table pos_order_item alter column charge_id drop not null;
alter table pos_customer add column room_booking_details jsonb;


CREATE TABLE pos_split_bill (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    bill_id character varying NOT NULL,
    order_id character varying NOT NULL,
    status character varying,
    split_type character varying,
    crs_booking_id character varying,
    room_stay_id character varying,
    bill_to character varying,
    settlement_method character varying,
    guest_ids character varying[],
    deleted boolean
);

ALTER TABLE ONLY pos_split_bill
    ADD CONSTRAINT pos_split_bill_pkey PRIMARY KEY (bill_id);

-- downgrade
alter table bill drop column status;
alter table kitchen_order_ticket drop column message;
alter table pos_order_item drop column bills;
alter table pos_customer drop column room_booking_details;
drop table pos_split_bill;
