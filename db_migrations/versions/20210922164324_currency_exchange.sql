-- revision: '20210922164324_currency_exchange'
-- down_revision: '20210902181020_added_billing_cards_model'

-- upgrade
CREATE TABLE currency_exchange (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    currency_exchange_id CHARACTER VARYING NOT NULL PRIMARY KEY,
    room_number character varying NOT NULL,
    guest_name character varying NOT NULL,
    booking_reference_number character varying,
    id_proof_type character varying not null,
    id_number character varying not null,
    id_proof_country_code character varying NOT NULL,
    id_proof_issued_date character varying,
    id_proof_issued_place character varying,
    id_proof_attachment_id integer,
    amount_in_foreign_currency numeric(15,4) not null,
    foreign_currency_sold character varying not null,
    foreign_currency_payment_mode character varying not null,
    amount_in_base_currency numeric(15, 4) not null,
    taxable_amount numeric(15, 4) not null,
    tax_amount numeric(15, 4) not null,
    tax_details character varying[],
    round_off numeric(5, 4) not null,
    total_payable_in_base_currency numeric(15, 4) not null,
    exchange_rate character varying not null,
    transaction_date date not null,
    remarks character varying,
    transaction_id character varying not null,
    certificate_number character varying not null,
    encashment_certificate_url character varying not null
);

ALTER TABLE ONLY currency_exchange
    ADD CONSTRAINT ix_currency_exchange_tx_id UNIQUE (transaction_id);

ALTER TABLE ONLY currency_exchange
    ADD CONSTRAINT ix_currency_exchange_certificate_number UNIQUE (certificate_number);

CREATE TABLE currency_exchange_certificate_no_sequence (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    vendor_id character varying not null primary key,
    last_sequence_number integer not null
);

-- downgrade
DROP TABLE currency_exchange_certificate_no_sequence;
DROP TABLE currency_exchange;
