-- revision: '20240826122440_inventory_block_table'
-- down_revision: '20240903163913_adding_calendar_checkout_date_bookings'

-- upgrade
CREATE TABLE inventory_block (
	block_id varchar NOT NULL,
	hotel_id varchar NOT NULL,
	room_type_id varchar NOT NULL,
	start_date date NOT NULL,
	end_date date NOT NULL,
	status varchar NOT NULL,
	booking_id varchar NOT NULL,
	created_at timestamp with time zone DEFAULT now() NOT NULL,
	modified_at timestamp with time zone DEFAULT now() NOT NULL
);

CREATE INDEX IF NOT EXISTS ix_inventory_block_block_id ON inventory_block (block_id);

-- downgrade
DROP TABLE inventory_block;
DROP INDEX ix_inventory_block_block_id;
