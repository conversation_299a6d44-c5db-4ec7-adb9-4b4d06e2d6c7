-- revision: '20211027202201_house_statistics_table'
-- down_revision: '20211027132625_housekeeping_audit_trail_table'

-- upgrade
CREATE TABLE house_statistics (
	created_at timestamptz NOT NULL DEFAULT now(),
	modified_at timestamptz NOT NULL DEFAULT now(),
	house_statistics_id SERIAL,
	hotel_id varchar NOT NULL,
	business_date DATE NOT NULL,
	available_rooms INTEGER NOT NULL,
	occupied_rooms INTEGER NOT NULL,
	early_departures INTEGER NOT NULL,
	day_use_rooms INTEGER NOT NULL,
	day_of_arrival_cancellations INTEGER NOT NULL,
	total_rooms_under_group_booking INTEGER NOT NULL,
	total_guests_under_group_booking INTEGER NOT NULL,
	total_vip_guests_under_group_booking INTEGER NOT NULL,
	total_rooms_under_individual_booking INTEGER NOT NULL,
	total_guests_under_individual_booking INTEGER NOT NULL,
	total_vip_guests_under_individual_booking INTEGER NOT NULL,
	occupancy NUMERIC(5, 2),
	total_room_revenue NUMERIC(15, 4),
	average_room_revenue NUMERIC(15, 4),
	UNIQUE (hotel_id, business_date)
);

-- downgrade
DROP TABLE house_statistics;