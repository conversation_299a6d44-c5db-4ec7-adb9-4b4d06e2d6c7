-- revision: '20211109181922_trial_balance_v2_tables'
-- down_revision: '20211108151555_add_priority_in_job'

-- upgrade
CREATE TABLE guest_ledger_report_v2(
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    opening_balance_in_base_currency NUMERIC(15,4) NOT NULL,
    closing_balance_in_base_currency NUMERIC(15,4),
    total_payments NUMERIC(15,4),
    total_charges NUMERIC(15,4),
    deposit_transferred_at_checkin NUMERIC(15,4),
    charges_transferred_to_ar  NUMERIC(15,4),
    booking_id CHARACTER VARYING,
    hotel_id CHARACTER VARYING,
    base_currency CHARACTER VARYING,
    payment_components JSONB,
    revenue_components JSONB,
    non_revenue_components JSONB,
    deleted BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE TABLE ar_ledger_report_v2(
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    opening_balance_in_base_currency NUMERIC(15,4) NOT NULL,
    closing_balance_in_base_currency NUMERIC(15,4),
    total_charges NUMERIC(15,4),
    total_payments NUMERIC(15,4),
    hotel_id CHARACTER VARYING,
    base_currency CHARACTER VARYING,
    payment_components JSONB,
    revenue_components JSONB,
    non_revenue_components JSONB,
    deleted BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE TABLE deposit_ledger_report_v2(
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    opening_balance_in_base_currency NUMERIC(15,4) NOT NULL,
    closing_balance_in_base_currency NUMERIC(15,4),
    total_payments NUMERIC(15,4),
    total_charges NUMERIC(15,4),
    deposit_transferred_at_checkin NUMERIC(15,4),
    payment_components JSONB,
    revenue_components JSONB,
    non_revenue_components JSONB,
    hotel_id CHARACTER VARYING,
    base_currency CHARACTER VARYING,
    deleted BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE INDEX ix_glrv2_report_date ON guest_ledger_report_v2 USING btree (report_date);
CREATE UNIQUE INDEX ix_glrv2_hotel_id_report_date_uniq ON guest_ledger_report_v2 USING btree (hotel_id, report_date);

CREATE INDEX ix_arlrv2_report_date ON ar_ledger_report_v2 USING btree (report_date);
CREATE UNIQUE INDEX ix_arlrv2_report_date_hotel_id_uniq ON ar_ledger_report_v2 USING btree (hotel_id, report_date);

CREATE INDEX ix_dlrv2_report_date ON deposit_ledger_report_v2 USING btree (report_date);
CREATE UNIQUE INDEX ix_dlrv2_report_date_hotel_id_uniq ON deposit_ledger_report_v2 USING btree (hotel_id, report_date);


-- downgrade
DROP TABLE guest_ledger_report_v2;

DROP TABLE ar_ledger_report_v2;

DROP TABLE deposit_ledger_report_v2;
