-- revision: '20211005123925_order_remarks'
-- down_revision: '20211006213939_add_guest_count_to_reservation'

-- upgrade
CREATE TABLE pos_remark (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    order_id character varying NOT NULL,
    remark_id int NOT NULL,
    order_item_id character varying,
    action character varying,
    remark character varying,
    deleted boolean,
    PRIMARY KEY (order_id, remark_id)
);

ALTER TABLE pos_order ADD COLUMN created_by character varying;
ALTER TABLE pos_order ADD COLUMN auth_id character varying;

ALTER TABLE pos_split_bill ADD COLUMN settled_by character varying;
ALTER TABLE pos_order_item ADD COLUMN added_by character varying;


-- downgrade
DROP TABLE pos_remark;

ALTER TABLE pos_order DROP COLUMN created_by;
ALTER TABLE pos_order DROP COLUMN auth_id;
ALTER TABLE pos_split_bill DROP COLUMN settled_by;
ALTER TABLE pos_order_item DROP COLUMN added_by;
