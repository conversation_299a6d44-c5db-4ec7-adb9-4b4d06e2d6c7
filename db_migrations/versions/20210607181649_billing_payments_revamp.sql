-- revision: '20210607181649_billing_payments_revamp'
-- down_revision: '20210629120823_reservation_table'

-- upgrade

--
-- Name: billed_entity; Type: TABLE; Schema: public; Owner: -
--
CREATE TABLE billed_entity (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    billed_entity_id integer NOT NULL,
    deleted boolean,
    bill_id character varying NOT NULL,
    first_name character varying,
    last_name character varying,
    category character varying NOT NULL,
    PRIMARY KEY (bill_id, billed_entity_id));


--
-- Name: account; Type: TABLE; Schema: public; Owner: -
--
CREATE TABLE account (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    billed_entity_id integer NOT NULL,
    account_number integer NOT NULL,
    deleted boolean,
    invoiced boolean,
    locked boolean,
    bill_id character varying NOT NULL,
    PRIMARY KEY (bill_id, billed_entity_id, account_number));

--
-- Name: allowance; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE allowance (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    allowance_id integer NOT NULL,
    charge_id integer NOT NULL,
    charge_split_id integer NOT NULL,
    billed_entity_id integer,
    billed_entity_account_number integer,
    posting_date DATE,
    deleted boolean,
    bill_id character varying NOT NULL,
    invoice_id character varying,
    credit_note_id character varying,
    remarks character varying,
    tax_amount numeric(15,4),
    pretax_amount numeric(15,4),
    posttax_amount numeric(15,4),
    PRIMARY KEY (bill_id, charge_id, charge_split_id, allowance_id));

ALTER TABLE booking_customer ADD COLUMN billed_entity_id integer;
ALTER TABLE booking_customer ADD COLUMN company_billed_entity_id integer;

ALTER TABLE expense ADD COLUMN sku_name character varying;
ALTER TABLE expense ADD COLUMN via_rate_plan boolean;

ALTER TABLE charge ADD COLUMN charge_to character varying ARRAY;

ALTER TABLE charge_split ADD COLUMN billed_entity_id integer;
ALTER TABLE charge_split ADD COLUMN billed_entity_account_number integer;
ALTER TABLE charge_split ADD COLUMN charge_type character varying;
ALTER TABLE charge_split ADD COLUMN bill_to_type character varying;
ALTER TABLE charge_split ADD COLUMN payment_id integer;

ALTER TABLE payment_split DROP CONSTRAINT payment_split_pkey;
ALTER TABLE payment_split DROP COLUMN invoice_id;
ALTER TABLE payment_split ADD COLUMN billed_entity_id integer;
ALTER TABLE payment_split ADD COLUMN billed_entity_account_number integer;
ALTER TABLE payment_split ADD COLUMN bill_id character varying not null;
ALTER TABLE payment_split ADD CONSTRAINT payment_split_pkey PRIMARY KEY (bill_id, payment_id, payment_split_id);

ALTER TABLE invoice ADD COLUMN billed_entity_id integer;
ALTER TABLE invoice ADD COLUMN billed_entity_account_number integer;

-- downgrade
ALTER TABLE invoice DROP COLUMN billed_entity_id;
ALTER TABLE invoice DROP COLUMN billed_entity_account_number;

ALTER TABLE payment_split DROP COLUMN billed_entity_id;
ALTER TABLE payment_split DROP COLUMN billed_entity_account_number;
ALTER TABLE payment_split DROP COLUMN bill_id;
ALTER TABLE payment_split DROP CONSTRAINT payment_split_pkey;
ALTER TABLE payment_split ADD COLUMN invoice_id SET NOT NULL;

ALTER TABLE charge_split DROP COLUMN billed_entity_id;
ALTER TABLE charge_split DROP COLUMN billed_entity_account_number;
ALTER TABLE charge_split DROP COLUMN charge_type;
ALTER TABLE charge_split DROP COLUMN bill_to_type;
ALTER TABLE charge_split DROP COLUMN payment_id;

ALTER TABLE charge DROP COLUMN charge_to;

ALTER TABLE expense DROP COLUMN sku_name;
ALTER TABLE expense DROP COLUMN via_rate_plan;

ALTER TABLE booking_customer DROP COLUMN billed_entity_id;
ALTER TABLE booking_customer DROP COLUMN company_billed_entity_id;

DROP TABLE allowance;
DROP TABLE accounts;
DROP TABLE billed_entity;
