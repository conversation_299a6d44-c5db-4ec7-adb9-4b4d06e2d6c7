-- revision: '20200912150034_web_checkin'
-- down_revision: '20200811124855_eregcard_v1.1'

-- upgrade

CREATE TABLE web_checkin (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    web_checkin_id character varying NOT NULL,
    booking_id character varying NOT NULL,
    status character varying NOT NULL,
    guest_ids character varying []
);

ALTER TABLE booking_attachment ADD COLUMN status character varying NULL;
ALTER TABLE booking_attachment ADD COLUMN rejection_reason character varying NULL;


-- downgrade

DROP TABLE web_checkin;
ALTER TABLE booking_attachment DROP Column status;
ALTER TABLE booking_attachment DROP COLUMN rejection_reason;