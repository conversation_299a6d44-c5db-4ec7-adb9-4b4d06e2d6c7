-- revision: '20210827095528_night_audit_audit_trail_table'
-- down_revision: '20210917170953_add-seller-type-to-pos-order'

-- upgrade
create TABLE night_audit_audit_trail(
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    night_audit_audit_trail_id character varying NOT NULL,
    night_audit_id character varying NOT NULL,
    "user" character varying,
    user_type character varying,
    auth_id character varying,
    action_type character varying,
    scheduled_time timestamp with time zone,
    ran_manually boolean,
    PRIMARY KEY (night_audit_audit_trail_id));

alter table night_audit add COLUMN pending_critical_tasks JSONB;
alter table night_audit add COLUMN system_freeze_remaining_time character varying;

-- downgrade
drop table night_audit_audit_trail;
alter table night_audit drop COLUMN pending_critical_tasks;
alter table night_audit drop COLUMN system_freeze_remaining_time;
