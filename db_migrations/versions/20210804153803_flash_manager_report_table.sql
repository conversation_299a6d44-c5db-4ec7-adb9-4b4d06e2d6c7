-- revision: '20210804153803_flash_manager_report_table'
-- down_revision: '20210723110032_added_pan_tan_tin_in_hotel_table'

-- upgrade
CREATE TABLE flash_manager_report (
    id SERIAL PRIMARY KEY,
    report_date date,
    data json,
    hotel_id character varying,
    created_at timestamp with time zone DEFAULT now(),
    modified_at timestamp with time zone DEFAULT now()
);

CREATE INDEX idx_hotel_id_report_date ON flash_manager_report  USING btree (hotel_id, report_date);

-- downgrade
DROP TABLE flash_manager_report