#!/bin/bash

set -e
set -x

APP="$1"
ENV="$2"

if [[ -z "${BUILD_DIR}" ]]; then
  echo "Please set BUILD_DIR env variable, which should be path where code will be checked out to create build"
  exit 1
fi

echo $BUILD_DIR

cd $BUILD_DIR

if [ "$APP" = "crs" ]; then
    cp -r coderepo/prometheus/deployment/* coderepo/
    cp -r coderepo/docker/compose/crs/* coderepo/docker/compose/

elif [ "$APP" = "pos" ]; then
    cp -r coderepo/pos/deployment/* coderepo/
    cp -r coderepo/docker/compose/pos/* coderepo/docker/compose/

elif [ "$APP" = "ims" ]; then
    cp -r coderepo/inventory_management/deployment/* coderepo/
    cp -r coderepo/docker/compose/ims/* coderepo/docker/compose/
fi

if [ "$ENV" = "production" ]; then
    if [ "$APP" = "crs" ]; then
      tar cvzf ${APP}.tar.gz envrepo/prometheus coderepo/setup.sh coderepo/docker/compose/prod-compose.yml
    elif [ "$APP" = "pos" ]; then
      tar cvzf ${APP}.tar.gz envrepo/${APP} coderepo/setup.sh coderepo/docker/compose/app-docker-compose.yml
    else
      tar cvzf ${APP}.tar.gz envrepo/${APP} coderepo/setup.sh coderepo/docker/compose/prod-compose.yml
    fi
else
    if [ "$APP" = "crs" ]; then
      tar cvzf ${APP}.tar.gz envrepo/prometheus coderepo/setup.sh coderepo/docker/compose/stag-compose.yml
    elif [ "$APP" = "pos" ]; then
      tar cvzf ${APP}.tar.gz envrepo/${APP} coderepo/setup.sh coderepo/docker/compose/app-docker-compose.yml
    else
      tar cvzf ${APP}.tar.gz envrepo/${APP} coderepo/setup.sh coderepo/docker/compose/stag-compose.yml
    fi
fi
