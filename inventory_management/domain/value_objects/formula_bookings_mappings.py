from treebo_commons.utils import dateutils

from inventory_management.application.dtos.room_type_inventory_dto import (
    RoomTypeInventories,
)
from inventory_management.domain.entities.room_upgrade_formula import RoomUpgradeFormula


class FormulaBookingsMapping:
    def __init__(self, formula: RoomUpgradeFormula, bookings=None):
        self.formula = formula
        self.bookings = bookings if bookings else []
        self.inventory_available_for_upgrade = {}

    def accumulate_inventory_available_for_upgrade(
        self, day_wise_higher_rt_availability: RoomTypeInventories
    ):
        higher_room_fill_percent = self.formula.higher_room_type_fill_percent
        for inventory in day_wise_higher_rt_availability.room_type_inventories:
            d = inventory.get('date')
            if d not in self.inventory_available_for_upgrade:
                actual_count = inventory.get('actual_count')
                self.inventory_available_for_upgrade[d] = int(
                    actual_count * higher_room_fill_percent / 100
                )

    def is_inventory_available_for_upgrade(self, booking):
        room_count = len(booking.rooms)
        date_range = [booking.checkin_date]
        date_range += [
            dateutils.add(booking.checkin_date, days=n)
            for n in range(
                1,
                int((booking.checkout_date.date() - booking.checkin_date.date()).days),
            )
        ]
        for date in date_range:
            if (
                not self.inventory_available_for_upgrade.get(
                    dateutils.date_to_ymd_str(date)
                )
                >= room_count
            ):
                return False
        return True

    def decrease_inventory_available_for_upgrade(self, booking):
        room_count = len(booking.rooms)
        date_range = [booking.checkin_date]
        date_range += [
            dateutils.add(booking.checkin_date, days=n)
            for n in range(
                1,
                int((booking.checkout_date.date() - booking.checkin_date.date()).days),
            )
        ]
        for date in date_range:
            date_str = dateutils.date_to_ymd_str(date)
            self.inventory_available_for_upgrade[date_str] = (
                self.inventory_available_for_upgrade.get(date_str) - room_count
            )
