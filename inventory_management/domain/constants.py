from aenum import MultiValueEnum


class BaseEnum(MultiValueEnum):
    @classmethod
    def all(cls, exclude=None):
        if exclude:
            return [enum.value for enum in cls if enum not in exclude]
        else:
            return [enum.value for enum in cls]

    @classmethod
    def all_options(cls, as_set=False):
        return [enum for enum in cls] if not as_set else {enum for enum in cls}

    def __str__(self):
        return self.value

    @property
    def label(self):
        if len(self.values) > 1:
            return self.values[1]
        else:
            return self.value

    @classmethod
    def get_enums(cls, user_type):
        return [enum for enum in cls]


class UpgradeType(BaseEnum):
    PAID = 'paid'
    FREE = 'free'


class UpgradeStatus(BaseEnum):
    INITIATED = 'initiated'
    COMPLETED = 'completed'


class SlabType(BaseEnum):
    LOWER = 'lower'
    HIGHER = 'higher'


class BookingTypes(BaseEnum):
    SME_ENROLLED = "sme_enrolled"
    CORPORATE = "corporate"
    LOYALTY_ENROLLED = "loyalty_enrolled"
    DIRECT = "direct"
    OTA_PREPAID = "ota_prepaid"
    UNCLASSIFIED = "unclassified"


UPGRADE_PRIORITY = {
    BookingTypes.SME_ENROLLED: 1,
    BookingTypes.CORPORATE: 2,
    BookingTypes.LOYALTY_ENROLLED: 3,
    BookingTypes.DIRECT: 4,
    BookingTypes.OTA_PREPAID: 5,
    BookingTypes.UNCLASSIFIED: 6,
}


class MessageTemplates(BaseEnum):
    FREE_UPGRADE = 'free_upgrade_message'


class EmailTemplates(BaseEnum):
    @property
    def identifier(self):
        return self.values[0]

    @property
    def subject(self):
        return self.values[1]

    FREE_UPGRADE = (
        "free_upgrade_email",
        "Surprise Gift from Treebo - your stay at {h_name} Is upgraded for FREE!",
    )
