from inventory_management.domain.constants import UpgradeStatus, UpgradeType
from inventory_management.domain.value_objects.room_type_upgrade import RoomTypeUpgrade


class ProcessedRoomUpgrade:
    def __init__(
        self,
        formula_id,
        booking_id,
        room_stay_id,
        hotel_id,
        room_type_upgrade: RoomTypeUpgrade,
        upgrade_type: UpgradeType,
        upgrade_status: UpgradeStatus,
        upgrade_amount=0,
        created_at=None,
        modified_at=None,
    ):
        self.formula_id = formula_id
        self.booking_id = booking_id
        self.room_stay_id = room_stay_id
        self.hotel_id = hotel_id
        self.room_type_upgrade = room_type_upgrade
        self.upgrade_type = upgrade_type
        self.upgrade_status = upgrade_status
        self.upgrade_amount = upgrade_amount
        self.created_at = created_at
        self.modified_at = modified_at
