from inventory_management.domain.value_objects.room_type_upgrade import RoomTypeUpgrade
from inventory_management.infrastructure.database.base_repository import BaseRepository
from inventory_management.infrastructure.database.repositories.db_adapters.price_slab_adaptor import (
    PriceSlabAdaptor,
)
from inventory_management.infrastructure.database.repositories.db_adapters.room_type_adaptor import (
    RoomTypeAdaptor,
)
from inventory_management.infrastructure.database.repositories.models import (
    CustomPriceSlabModel,
    DefaultPriceSlabModel,
    RoomTypeModel,
)
from object_registry import register_instance


@register_instance()
class PaidUpgradePriceSlabRepository(BaseRepository):
    price_slab_adaptor = PriceSlabAdaptor()
    room_type_adaptor = RoomTypeAdaptor()

    def to_aggregate(self, **kwargs):
        price_slab_entry = kwargs.get('price_slab_entry')
        higher_room_type = self.room_type_adaptor.to_domain_entity(
            self.query(RoomTypeModel)
            .filter(RoomTypeModel.room_type_id == price_slab_entry.lower_room_type_id)
            .one()
        )
        lower_room_type = self.room_type_adaptor.to_domain_entity(
            self.query(RoomTypeModel)
            .filter(RoomTypeModel.room_type_id == price_slab_entry.higher_room_type_id)
            .one()
        )
        room_type_upgrade = RoomTypeUpgrade(
            lower_room_type=lower_room_type, higher_room_type=higher_room_type
        )
        return self.price_slab_adaptor.to_domain_entity(
            price_slab_entry, room_type_upgrade=room_type_upgrade
        )

    def from_aggregate(self, aggregate=None):
        pass

    def load_all_default_price_slab(self):
        default_price_slab_entries = self.query(DefaultPriceSlabModel).all()
        return [
            self.to_aggregate(price_slab_entry=price_slab_entry)
            for price_slab_entry in default_price_slab_entries
        ]

    def load_custom_price_slab(self, hotel_id):
        custom_price_slab_entries = (
            self.query(CustomPriceSlabModel)
            .filter(CustomPriceSlabModel.hotel_id == hotel_id)
            .all()
        )
        return [
            self.to_aggregate(price_slab_entry=price_slab_entry)
            for price_slab_entry in custom_price_slab_entries
        ]
