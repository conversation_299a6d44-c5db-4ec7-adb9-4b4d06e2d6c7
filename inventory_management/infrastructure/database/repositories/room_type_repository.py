from inventory_management.infrastructure.database.base_repository import BaseRepository
from inventory_management.infrastructure.database.repositories.db_adapters.room_type_adaptor import (
    RoomTypeAdaptor,
)
from inventory_management.infrastructure.database.repositories.models import (
    RoomTypeModel,
)
from object_registry import register_instance


@register_instance()
class RoomTypeRepository(BaseRepository):
    def to_aggregate(self, **kwargs):
        pass

    def from_aggregate(self, aggregate=None):
        pass

    room_type_adaptor = RoomTypeAdaptor()

    def load(self, room_type_id):
        db_entry = (
            self.query(RoomTypeModel)
            .filter(RoomTypeModel.room_type_id == room_type_id)
            .one()
        )
        return self.room_type_adaptor.to_domain_entity(db_entry)
