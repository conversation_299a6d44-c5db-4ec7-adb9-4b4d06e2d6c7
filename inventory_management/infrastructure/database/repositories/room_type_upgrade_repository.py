from inventory_management.domain.value_objects.room_type_upgrade import RoomTypeUpgrade
from inventory_management.infrastructure.database.base_repository import BaseRepository
from inventory_management.infrastructure.database.repositories.db_adapters.room_type_adaptor import (
    RoomTypeAdaptor,
)
from inventory_management.infrastructure.database.repositories.db_adapters.room_type_upgrade_level_adaptor import (
    RoomTypeUpgradeLevelAdaptor,
)
from inventory_management.infrastructure.database.repositories.models import (
    AvailableRoomTypeUpgradeModel,
    RoomTypeModel,
)
from object_registry import register_instance


@register_instance()
class RoomTypeUpgradeLevelRepository(BaseRepository):
    room_type_upgrade_level_adaptor = RoomTypeUpgradeLevelAdaptor()
    room_type_adaptor = RoomTypeAdaptor()

    def to_aggregate(self, **kwargs):
        db_entity = kwargs.get('available_room_type_upgrade')
        higher_room_type = self.room_type_adaptor.to_domain_entity(
            self.query(RoomTypeModel)
            .filter(RoomTypeModel.room_type_id == db_entity.higher_room_type_id)
            .one()
        )
        lower_room_type = self.room_type_adaptor.to_domain_entity(
            self.query(RoomTypeModel)
            .filter(RoomTypeModel.room_type_id == db_entity.lower_room_type_id)
            .one()
        )
        room_type_upgrade = RoomTypeUpgrade(
            lower_room_type=lower_room_type, higher_room_type=higher_room_type
        )
        return self.room_type_upgrade_level_adaptor.to_domain_entity(
            db_entity, room_type_upgrade=room_type_upgrade
        )

    def from_aggregate(self, aggregate=None):
        pass

    def load_all(self):
        available_room_type_upgrades = (
            self.query(AvailableRoomTypeUpgradeModel)
            .order_by(AvailableRoomTypeUpgradeModel.upgrade_priority)
            .all()
        )
        return [
            self.to_aggregate(available_room_type_upgrade=available_room_type_upgrade)
            for available_room_type_upgrade in available_room_type_upgrades
        ]
