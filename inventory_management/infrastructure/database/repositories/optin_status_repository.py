from inventory_management.domain.constants import UpgradeType
from inventory_management.domain.value_objects.room_type_upgrade import RoomTypeUpgrade
from inventory_management.infrastructure.database.base_repository import BaseRepository
from inventory_management.infrastructure.database.repositories.db_adapters.optin_status_adaptor import (
    OptinStatusAdaptor,
)
from inventory_management.infrastructure.database.repositories.db_adapters.room_type_adaptor import (
    RoomTypeAdaptor,
)
from inventory_management.infrastructure.database.repositories.models import (
    OptinStatusModel,
    RoomTypeModel,
)
from object_registry import register_instance


@register_instance()
class OptinStatusRepository(BaseRepository):
    optin_status_adaptor = OptinStatusAdaptor()
    room_type_adaptor = RoomTypeAdaptor()

    def to_aggregate(self, **kwargs):
        optin_status_entry = kwargs.get('optin_status_entry')
        higher_room_type = self.room_type_adaptor.to_domain_entity(
            self.query(RoomTypeModel)
            .filter(RoomTypeModel.room_type_id == optin_status_entry.lower_room_type_id)
            .one()
        )
        lower_room_type = self.room_type_adaptor.to_domain_entity(
            self.query(RoomTypeModel)
            .filter(
                RoomTypeModel.room_type_id == optin_status_entry.higher_room_type_id
            )
            .one()
        )
        room_type_upgrade = RoomTypeUpgrade(
            lower_room_type=lower_room_type, higher_room_type=higher_room_type
        )
        return self.optin_status_adaptor.to_domain_entity(
            optin_status_entry, room_type_upgrade=room_type_upgrade
        )

    def from_aggregate(self, aggregate=None):
        pass

    def load(
        self,
        hotel_id,
        lower_room_type_id,
        higher_room_type_id,
        upgrade_type=UpgradeType.FREE.value,
    ):
        optin_status_entry = (
            self.query(OptinStatusModel)
            .filter(
                OptinStatusModel.hotel_id == hotel_id,
                OptinStatusModel.lower_room_type_id == lower_room_type_id,
                OptinStatusModel.higher_room_type_id == higher_room_type_id,
                OptinStatusModel.upgrade_type == upgrade_type,
            )
            .one_or_none()
        )
        return (
            self.to_aggregate(optin_status_entry=optin_status_entry)
            if optin_status_entry
            else None
        )
