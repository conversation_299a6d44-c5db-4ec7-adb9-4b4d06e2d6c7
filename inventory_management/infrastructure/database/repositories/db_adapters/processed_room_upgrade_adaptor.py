from inventory_management.domain.constants import UpgradeStatus, UpgradeType
from inventory_management.domain.entities.processed_room_upgrade import (
    ProcessedRoomUpgrade,
)
from inventory_management.infrastructure.database.repositories.models import (
    ProcessedRoomUpgradeModel,
)


class ProcessedRoomUpgradeAdaptor:
    @staticmethod
    def to_db_entity(domain_entity: ProcessedRoomUpgrade, **kwargs):
        # noinspection PyArgumentList
        return ProcessedRoomUpgradeModel(
            booking_id=domain_entity.booking_id,
            room_stay_id=domain_entity.room_stay_id,
            hotel_id=domain_entity.hotel_id,
            lower_room_type_id=domain_entity.room_type_upgrade.lower_room_type.id,
            higher_room_type_id=domain_entity.room_type_upgrade.higher_room_type.id,
            formula_id=domain_entity.formula_id,
            upgrade_type=domain_entity.upgrade_type.value,
            upgrade_amount=domain_entity.upgrade_amount,
            upgrade_status=domain_entity.upgrade_status.value,
        )

    @staticmethod
    def to_domain_entity(db_entity: ProcessedRoomUpgradeModel, **kwargs):
        return ProcessedRoomUpgrade(
            formula_id=db_entity.formula_id,
            booking_id=db_entity.booking_id,
            room_stay_id=db_entity.room_stay_id,
            hotel_id=db_entity.hotel_id,
            room_type_upgrade=kwargs.get('room_type_upgrade'),
            upgrade_type=UpgradeType(db_entity.upgrade_type),
            upgrade_amount=db_entity.upgrade_amount,
            upgrade_status=UpgradeStatus(db_entity.upgrade_status),
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )
