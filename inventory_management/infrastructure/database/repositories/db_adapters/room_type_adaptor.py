from inventory_management.domain.entities.paid_upgrade_price_slab import (
    PaidUpgradePriceSlab,
)
from inventory_management.domain.entities.room_type import RoomType
from inventory_management.infrastructure.database.repositories.models import (
    RoomTypeModel,
)


class RoomTypeAdaptor:
    @staticmethod
    def to_db_entity(domain_entity: PaidUpgradePriceSlab, **kwargs):
        # noinspection PyArgumentList
        pass

    @staticmethod
    def to_domain_entity(db_entity: RoomTypeModel, **kwargs):
        return RoomType(
            room_type_id=db_entity.room_type_id,
            crs_id=db_entity.crs_id,
            name=db_entity.name,
            level=db_entity.level,
        )
