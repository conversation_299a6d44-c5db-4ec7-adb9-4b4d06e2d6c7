from inventory_management.domain.entities.processed_room_upgrade import (
    ProcessedRoomUpgrade,
)
from inventory_management.domain.value_objects.room_type_upgrade import RoomTypeUpgrade
from inventory_management.infrastructure.database.base_repository import BaseRepository
from inventory_management.infrastructure.database.repositories.db_adapters.processed_room_upgrade_adaptor import (
    ProcessedRoomUpgradeAdaptor,
)
from inventory_management.infrastructure.database.repositories.db_adapters.room_type_adaptor import (
    RoomTypeAdaptor,
)
from inventory_management.infrastructure.database.repositories.models import (
    ProcessedRoomUpgradeModel,
    RoomTypeModel,
)
from object_registry import register_instance


@register_instance()
class ProcessedRoomUpgradeRepository(BaseRepository):
    processed_room_upgrade_adaptor = ProcessedRoomUpgradeAdaptor()
    room_type_adaptor = RoomTypeAdaptor()

    def to_aggregate(self, **kwargs):
        processed_room_upgrade = kwargs.get('processed_room_upgrade')
        higher_room_type = self.room_type_adaptor.to_domain_entity(
            self.query(RoomTypeModel)
            .filter(
                RoomTypeModel.room_type_id == processed_room_upgrade.lower_room_type_id
            )
            .one()
        )
        lower_room_type = self.room_type_adaptor.to_domain_entity(
            self.query(RoomTypeModel)
            .filter(
                RoomTypeModel.room_type_id == processed_room_upgrade.higher_room_type_id
            )
            .one()
        )
        room_type_upgrade = RoomTypeUpgrade(
            lower_room_type=lower_room_type, higher_room_type=higher_room_type
        )
        return self.processed_room_upgrade_adaptor.to_domain_entity(
            processed_room_upgrade, room_type_upgrade=room_type_upgrade
        )

    def from_aggregate(self, aggregate=None):
        pass

    def save(self, processed_room_upgrade: ProcessedRoomUpgrade):
        self._save(
            self.processed_room_upgrade_adaptor.to_db_entity(processed_room_upgrade)
        )

    def load_processed_room_upgrades(self, booking_id):
        processed_room_upgrade_entries = (
            self.query(ProcessedRoomUpgradeModel)
            .filter(ProcessedRoomUpgradeModel.booking_id == booking_id)
            .all()
        )
        return [
            self.to_aggregate(processed_room_upgrade=processed_room_upgrade)
            for processed_room_upgrade in processed_room_upgrade_entries
        ]
