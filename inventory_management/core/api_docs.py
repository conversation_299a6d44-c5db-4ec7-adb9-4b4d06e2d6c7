import inspect

import marshmallow
from apispec import APISpec
from flasgger import Swagger


def setup_schema_definition(spec):
    for name, obj in inspect.getmembers(serializers):
        if inspect.isclass(obj) and type(obj) == marshmallow.schema.SchemaMeta:
            spec.definition(name, schema=obj)


def setup_path(spec):
    spec.add_path(view=print_bill)


def init_docs(app):
    ctx = app.test_request_context()
    ctx.push()

    # Create an APISpec
    spec = APISpec(
        title='Swagger POS',
        version='1.0.0',
        plugins=[
            'apispec.ext.flask',
            'apispec.ext.marshmallow',
        ],
    )

    setup_schema_definition(spec)
    setup_path(spec)
    sw = Swagger(template=spec.to_dict())
    sw.init_app(app)
