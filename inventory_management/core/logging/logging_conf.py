# coding=utf-8
"""
LOGGING CONF
"""
import os


def configure_logging(app):
    """
    Logging Setup
    :param app:
    :return:
    """
    import logging.config

    environment = os.environ.get('APP_ENV', 'local')

    logging_conf = {
        'version': 1,
        'filters': {
            'request_context': {
                '()': 'treebo_commons.request_tracing.log_filters.RequestContextFilter'
            }
        },
        'disable_existing_loggers': False,
        'formatters': {
            'verbose': {
                'format': "[%(asctime)s] %(levelname)s %(request_id)s - [%(name)s:%("
                "lineno)s] %(message)s",
                'datefmt': "%Y-%m-%d %H:%M:%S",
            },
            'logstash': {'()': 'logstash_formatter.LogstashFormatterV1'},
        },
        'handlers': {
            'null': {
                'level': 'DEBUG',
                'class': 'logging.NullHandler',
                'filters': ['request_context'],
            },
            'console': {
                'level': 'DEBUG',
                'class': 'logging.StreamHandler',
                'formatter': 'verbose',
                'filters': ['request_context'],
            },
        },
        'loggers': {
            'inventory_management': {
                'handlers': ['console'],
                'level': 'INFO' if environment == 'production' else 'DEBUG',
                'propagate': False,
            },
            'shared_kernel': {
                'handlers': ['console'],
                'level': 'INFO' if environment == 'production' else 'DEBUG',
                'propagate': False,
            },
            '': {
                'handlers': ['console'],
                'level': 'ERROR',
            },
            'request_handler': {
                'handlers': ['console'],
                'level': 'INFO' if environment == 'production' else 'DEBUG',
                'propagate': False,
            },
        },
    }
    logging.config.dictConfig(logging_conf)
