"""empty message

Revision ID: 7507a135f74c
Revises: 53bb01063279
Create Date: 2020-04-23 15:09:49.189948

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '7507a135f74c'
down_revision = '53bb01063279'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'available_room_type_upgrade',
        sa.Column('higher_room_type_id', sa.Integer(), nullable=False),
    )
    op.add_column(
        'available_room_type_upgrade',
        sa.Column('lower_room_type_id', sa.Integer(), nullable=False),
    )
    op.add_column(
        'available_room_type_upgrade',
        sa.Column('upgrade_priority', sa.Integer(), nullable=False),
    )
    op.create_unique_constraint(
        None, 'available_room_type_upgrade', ['upgrade_priority']
    )
    op.drop_constraint(
        'available_room_type_upgrade_higher_room_type_fkey',
        'available_room_type_upgrade',
        type_='foreignkey',
    )
    op.drop_constraint(
        'available_room_type_upgrade_lower_room_type_fkey',
        'available_room_type_upgrade',
        type_='foreignkey',
    )
    op.create_foreign_key(
        None,
        'available_room_type_upgrade',
        'room_type',
        ['higher_room_type_id'],
        ['room_type_id'],
    )
    op.create_foreign_key(
        None,
        'available_room_type_upgrade',
        'room_type',
        ['lower_room_type_id'],
        ['room_type_id'],
    )
    op.drop_column('available_room_type_upgrade', 'lower_room_type')
    op.drop_column('available_room_type_upgrade', 'higher_room_type')
    op.add_column(
        'custom_price_slab',
        sa.Column('higher_room_type_id', sa.Integer(), nullable=False),
    )
    op.add_column(
        'custom_price_slab',
        sa.Column('lower_room_type_id', sa.Integer(), nullable=False),
    )
    op.drop_constraint(
        'custom_price_slab_higher_room_type_fkey',
        'custom_price_slab',
        type_='foreignkey',
    )
    op.drop_constraint(
        'custom_price_slab_lower_room_type_fkey',
        'custom_price_slab',
        type_='foreignkey',
    )
    op.create_foreign_key(
        None,
        'custom_price_slab',
        'room_type',
        ['higher_room_type_id'],
        ['room_type_id'],
    )
    op.create_foreign_key(
        None, 'custom_price_slab', 'room_type', ['lower_room_type_id'], ['room_type_id']
    )
    op.drop_column('custom_price_slab', 'lower_room_type')
    op.drop_column('custom_price_slab', 'higher_room_type')
    op.add_column(
        'default_price_slab',
        sa.Column('higher_room_type_id', sa.Integer(), nullable=False),
    )
    op.add_column(
        'default_price_slab',
        sa.Column('lower_room_type_id', sa.Integer(), nullable=False),
    )
    op.drop_constraint(
        'default_price_slab_lower_room_type_fkey',
        'default_price_slab',
        type_='foreignkey',
    )
    op.drop_constraint(
        'default_price_slab_higher_room_type_fkey',
        'default_price_slab',
        type_='foreignkey',
    )
    op.create_foreign_key(
        None,
        'default_price_slab',
        'room_type',
        ['lower_room_type_id'],
        ['room_type_id'],
    )
    op.create_foreign_key(
        None,
        'default_price_slab',
        'room_type',
        ['higher_room_type_id'],
        ['room_type_id'],
    )
    op.drop_column('default_price_slab', 'lower_room_type')
    op.drop_column('default_price_slab', 'higher_room_type')
    op.add_column(
        'optin_status', sa.Column('higher_room_type_id', sa.Integer(), nullable=False)
    )
    op.add_column(
        'optin_status', sa.Column('lower_room_type_id', sa.Integer(), nullable=False)
    )
    op.drop_constraint(
        'optin_status_lower_room_type_fkey', 'optin_status', type_='foreignkey'
    )
    op.drop_constraint(
        'optin_status_higher_room_type_fkey', 'optin_status', type_='foreignkey'
    )
    op.create_foreign_key(
        None, 'optin_status', 'room_type', ['lower_room_type_id'], ['room_type_id']
    )
    op.create_foreign_key(
        None, 'optin_status', 'room_type', ['higher_room_type_id'], ['room_type_id']
    )
    op.drop_column('optin_status', 'lower_room_type')
    op.drop_column('optin_status', 'higher_room_type')
    op.add_column(
        'processed_room_upgrade', sa.Column('formula_id', sa.Integer(), nullable=False)
    )
    op.add_column(
        'processed_room_upgrade',
        sa.Column('higher_room_type_id', sa.Integer(), nullable=False),
    )
    op.add_column(
        'processed_room_upgrade',
        sa.Column('lower_room_type_id', sa.Integer(), nullable=False),
    )
    op.drop_constraint(
        'processed_room_upgrade_higher_room_type_fkey',
        'processed_room_upgrade',
        type_='foreignkey',
    )
    op.drop_constraint(
        'processed_room_upgrade_lower_room_type_fkey',
        'processed_room_upgrade',
        type_='foreignkey',
    )
    op.create_foreign_key(
        None,
        'processed_room_upgrade',
        'room_upgrade_formula',
        ['formula_id'],
        ['formula_id'],
    )
    op.create_foreign_key(
        None,
        'processed_room_upgrade',
        'room_type',
        ['higher_room_type_id'],
        ['room_type_id'],
    )
    op.create_foreign_key(
        None,
        'processed_room_upgrade',
        'room_type',
        ['lower_room_type_id'],
        ['room_type_id'],
    )
    op.drop_column('processed_room_upgrade', 'lower_room_type')
    op.drop_column('processed_room_upgrade', 'higher_room_type')
    op.add_column('room_type', sa.Column('crs_id', sa.String(), nullable=False))
    op.add_column('room_type', sa.Column('level', sa.Integer(), nullable=False))
    op.add_column('room_type', sa.Column('name', sa.String(), nullable=False))
    op.drop_constraint('room_type_room_type_level_key', 'room_type', type_='unique')
    op.drop_constraint('room_type_room_type_name_key', 'room_type', type_='unique')
    op.create_unique_constraint(None, 'room_type', ['level'])
    op.create_unique_constraint(None, 'room_type', ['crs_id'])
    op.create_unique_constraint(None, 'room_type', ['name'])
    op.drop_column('room_type', 'room_type_name')
    op.drop_column('room_type', 'room_type_level')
    op.add_column(
        'room_upgrade_formula', sa.Column('deleted', sa.Boolean(), nullable=True)
    )
    op.add_column(
        'room_upgrade_formula',
        sa.Column('higher_room_type_id', sa.Integer(), nullable=True),
    )
    op.add_column(
        'room_upgrade_formula',
        sa.Column('lower_room_type_id', sa.Integer(), nullable=True),
    )
    op.drop_constraint(
        'room_upgrade_formula_room_type_upgrade_id_fkey',
        'room_upgrade_formula',
        type_='foreignkey',
    )
    op.create_foreign_key(
        None,
        'room_upgrade_formula',
        'room_type',
        ['lower_room_type_id'],
        ['room_type_id'],
    )
    op.create_foreign_key(
        None,
        'room_upgrade_formula',
        'room_type',
        ['higher_room_type_id'],
        ['room_type_id'],
    )
    op.drop_column('room_upgrade_formula', 'room_type_upgrade_id')
    op.add_column('time_config', sa.Column('last_run', sa.DateTime(), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('time_config', 'last_run')
    op.add_column(
        'room_upgrade_formula',
        sa.Column(
            'room_type_upgrade_id', sa.INTEGER(), autoincrement=False, nullable=True
        ),
    )
    op.drop_constraint(None, 'room_upgrade_formula', type_='foreignkey')
    op.drop_constraint(None, 'room_upgrade_formula', type_='foreignkey')
    op.create_foreign_key(
        'room_upgrade_formula_room_type_upgrade_id_fkey',
        'room_upgrade_formula',
        'available_room_type_upgrade',
        ['room_type_upgrade_id'],
        ['room_type_upgrade_id'],
    )
    op.drop_column('room_upgrade_formula', 'lower_room_type_id')
    op.drop_column('room_upgrade_formula', 'higher_room_type_id')
    op.drop_column('room_upgrade_formula', 'deleted')
    op.add_column(
        'room_type',
        sa.Column('room_type_level', sa.INTEGER(), autoincrement=False, nullable=False),
    )
    op.add_column(
        'room_type',
        sa.Column('room_type_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    )
    op.drop_constraint(None, 'room_type', type_='unique')
    op.drop_constraint(None, 'room_type', type_='unique')
    op.drop_constraint(None, 'room_type', type_='unique')
    op.create_unique_constraint(
        'room_type_room_type_name_key', 'room_type', ['room_type_name']
    )
    op.create_unique_constraint(
        'room_type_room_type_level_key', 'room_type', ['room_type_level']
    )
    op.drop_column('room_type', 'name')
    op.drop_column('room_type', 'level')
    op.drop_column('room_type', 'crs_id')
    op.add_column(
        'processed_room_upgrade',
        sa.Column(
            'higher_room_type', sa.INTEGER(), autoincrement=False, nullable=False
        ),
    )
    op.add_column(
        'processed_room_upgrade',
        sa.Column('lower_room_type', sa.INTEGER(), autoincrement=False, nullable=False),
    )
    op.drop_constraint(None, 'processed_room_upgrade', type_='foreignkey')
    op.drop_constraint(None, 'processed_room_upgrade', type_='foreignkey')
    op.drop_constraint(None, 'processed_room_upgrade', type_='foreignkey')
    op.create_foreign_key(
        'processed_room_upgrade_lower_room_type_fkey',
        'processed_room_upgrade',
        'room_type',
        ['lower_room_type'],
        ['room_type_id'],
    )
    op.create_foreign_key(
        'processed_room_upgrade_higher_room_type_fkey',
        'processed_room_upgrade',
        'room_type',
        ['higher_room_type'],
        ['room_type_id'],
    )
    op.drop_column('processed_room_upgrade', 'lower_room_type_id')
    op.drop_column('processed_room_upgrade', 'higher_room_type_id')
    op.drop_column('processed_room_upgrade', 'formula_id')
    op.add_column(
        'optin_status',
        sa.Column(
            'higher_room_type', sa.INTEGER(), autoincrement=False, nullable=False
        ),
    )
    op.add_column(
        'optin_status',
        sa.Column('lower_room_type', sa.INTEGER(), autoincrement=False, nullable=False),
    )
    op.drop_constraint(None, 'optin_status', type_='foreignkey')
    op.drop_constraint(None, 'optin_status', type_='foreignkey')
    op.create_foreign_key(
        'optin_status_higher_room_type_fkey',
        'optin_status',
        'room_type',
        ['higher_room_type'],
        ['room_type_id'],
    )
    op.create_foreign_key(
        'optin_status_lower_room_type_fkey',
        'optin_status',
        'room_type',
        ['lower_room_type'],
        ['room_type_id'],
    )
    op.drop_column('optin_status', 'lower_room_type_id')
    op.drop_column('optin_status', 'higher_room_type_id')
    op.add_column(
        'default_price_slab',
        sa.Column(
            'higher_room_type', sa.INTEGER(), autoincrement=False, nullable=False
        ),
    )
    op.add_column(
        'default_price_slab',
        sa.Column('lower_room_type', sa.INTEGER(), autoincrement=False, nullable=False),
    )
    op.drop_constraint(None, 'default_price_slab', type_='foreignkey')
    op.drop_constraint(None, 'default_price_slab', type_='foreignkey')
    op.create_foreign_key(
        'default_price_slab_higher_room_type_fkey',
        'default_price_slab',
        'room_type',
        ['higher_room_type'],
        ['room_type_id'],
    )
    op.create_foreign_key(
        'default_price_slab_lower_room_type_fkey',
        'default_price_slab',
        'room_type',
        ['lower_room_type'],
        ['room_type_id'],
    )
    op.drop_column('default_price_slab', 'lower_room_type_id')
    op.drop_column('default_price_slab', 'higher_room_type_id')
    op.add_column(
        'custom_price_slab',
        sa.Column(
            'higher_room_type', sa.INTEGER(), autoincrement=False, nullable=False
        ),
    )
    op.add_column(
        'custom_price_slab',
        sa.Column('lower_room_type', sa.INTEGER(), autoincrement=False, nullable=False),
    )
    op.drop_constraint(None, 'custom_price_slab', type_='foreignkey')
    op.drop_constraint(None, 'custom_price_slab', type_='foreignkey')
    op.create_foreign_key(
        'custom_price_slab_lower_room_type_fkey',
        'custom_price_slab',
        'room_type',
        ['lower_room_type'],
        ['room_type_id'],
    )
    op.create_foreign_key(
        'custom_price_slab_higher_room_type_fkey',
        'custom_price_slab',
        'room_type',
        ['higher_room_type'],
        ['room_type_id'],
    )
    op.drop_column('custom_price_slab', 'lower_room_type_id')
    op.drop_column('custom_price_slab', 'higher_room_type_id')
    op.add_column(
        'available_room_type_upgrade',
        sa.Column(
            'higher_room_type', sa.INTEGER(), autoincrement=False, nullable=False
        ),
    )
    op.add_column(
        'available_room_type_upgrade',
        sa.Column('lower_room_type', sa.INTEGER(), autoincrement=False, nullable=False),
    )
    op.drop_constraint(None, 'available_room_type_upgrade', type_='foreignkey')
    op.drop_constraint(None, 'available_room_type_upgrade', type_='foreignkey')
    op.create_foreign_key(
        'available_room_type_upgrade_lower_room_type_fkey',
        'available_room_type_upgrade',
        'room_type',
        ['lower_room_type'],
        ['room_type_id'],
    )
    op.create_foreign_key(
        'available_room_type_upgrade_higher_room_type_fkey',
        'available_room_type_upgrade',
        'room_type',
        ['higher_room_type'],
        ['room_type_id'],
    )
    op.drop_constraint(None, 'available_room_type_upgrade', type_='unique')
    op.drop_column('available_room_type_upgrade', 'upgrade_priority')
    op.drop_column('available_room_type_upgrade', 'lower_room_type_id')
    op.drop_column('available_room_type_upgrade', 'higher_room_type_id')
    # ### end Alembic commands ###
