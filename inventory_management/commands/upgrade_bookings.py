import logging

import click
from flask.cli import with_appcontext

from inventory_management import RoomUpgradeService
from inventory_management.common.decorators import handle_db_commits
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--hotel_ids', help="*optional* list of hotel ids to run for specific hotel(s)"
)
@with_appcontext
@inject(room_upgrade_service=RoomUpgradeService)
def process_room_upgrade(room_upgrade_service, hotel_ids=None):
    room_upgrade_service.process_config(hotel_ids=hotel_ids)
