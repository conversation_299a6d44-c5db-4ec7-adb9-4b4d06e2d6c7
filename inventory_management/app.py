# coding=utf-8
"""
App Initiate file
"""
import logging
import os

import click
from flask import Flask
from flask_admin import Admin
from healthcheck import HealthCheck
from healthcheck.healthcheck import rds_available

from inventory_management import admin_views
from inventory_management.admin_views.index import MyAdminIndexView
from inventory_management.api import ims_bp
from inventory_management.commands.upgrade_bookings import process_room_upgrade
from inventory_management.config.default_config import DefaultConfig
from inventory_management.core.logging.logging_conf import configure_logging
from inventory_management.extensions import db, migrate
from inventory_management.middlewares.common_middlewares import exception_handler
from object_registry import locate_instance

logger = logging.getLogger(__name__)


def create_app():
    """
    Create App
    :return:
    """
    app = Flask(
        __name__,
        instance_relative_config=True,
        instance_path=os.environ.get('FLASK_APP_INSTANCE_PATH'),
    )
    setup_config(app)
    register_extensions(app)
    register_blueprints(app)
    register_commands(app)
    setup_admin(app)
    app.before_request_funcs = {None: app.config['BEFORE_REQUEST_MIDDLEWARES']}
    app.after_request_funcs = {None: app.config['AFTER_REQUEST_MIDDLEWARES']}
    register_error_handlers(app)
    configure_swagger(app)
    # init_docs(app)
    setup_health_check(app)

    @app.shell_context_processor
    def make_shell_context():
        ctx = app.test_request_context()
        ctx.push()
        return {'locate_instance': locate_instance, 'ctx': app.test_request_context()}

    return app


def register_error_handlers(app):
    """

    :param app:
    :return:
    """
    app.register_error_handler(Exception, exception_handler)


def setup_config(app):
    """ " """
    environment = os.environ.get('APP_ENV', 'local')
    # load the default config
    app.config.from_object(DefaultConfig)
    # load from config set by the app
    try:
        app.config.from_envvar('IMS_CONFIG_FILE', silent=False)
    except RuntimeError:
        if not os.environ.get('IMS_CONFIG_FILE'):
            click.echo(
                "IMS_CONFIG_FILE environment variable is not set. Default Config will be used"
            )
        else:
            click.echo(
                "Couldn't load config file from: %s" % os.environ.get('IMS_CONFIG_FILE')
            )

    click.echo(
        "Setting up Flask App: '%s', using environment: '%s', and config file: %s"
        % (__name__, environment, os.environ.get('IMS_CONFIG_FILE', 'DefaultConfig'))
    )
    configure_logging(app)


def setup_admin(app):
    app.config["FLASK_ADMIN_SWATCH"] = "Cosmo"
    admin = Admin(
        app,
        index_view=MyAdminIndexView(url="/ims/admin"),
        base_template="my_master.html",
        template_mode="bootstrap3",
    )
    for admin_view in admin_views.__all__:
        model_view = getattr(admin_views, admin_view)
        kwargs = {}
        if getattr(model_view, '_category', None):
            kwargs['category'] = model_view._category
        admin.add_view(
            model_view(model_view._model, db.session, model_view._name, **kwargs)
        )


def register_app_services():
    from object_registry import finalize_app_initialization

    finalize_app_initialization()


def setup_health_check(app):
    """

    :param app:
    :return:
    """
    health = HealthCheck(app, '/api/health', ['rds'])
    health.add_check(rds_available)


def register_extensions(app):
    """
    Registering extensions
    :param app:
    :return:
    """
    db.init_app(app)
    migrate.init_app(app)


def register_blueprints(app):
    """
    Registering BluePrints
    :param app:
    :param url_prefix:
    :return:
    """
    app.register_blueprint(ims_bp, url_prefix=ims_bp.url_prefix)


def register_commands(app):
    """Register Click commands."""
    app.cli.add_command(process_room_upgrade)


def configure_swagger(app):
    app.config['SWAGGER'] = {
        'title': 'IMS',
        'uiversion': 3,
    }
