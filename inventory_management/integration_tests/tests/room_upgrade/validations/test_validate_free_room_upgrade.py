import pytest

from inventory_management.integration_tests.config.common_config import HOTELID_LIST
from inventory_management.integration_tests.tests.room_upgrade.validations.validations_room_upgrade import (
    ValidationRoomUpgrade,
)


class TestValidateFreeRoomUpgrade(object):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, hotel_id, skip_message",
        [
            ("FreeRoomUpgrade_02", "validate free room upgrade", HOTELID_LIST[0], ""),
            ("FreeRoomUpgrade_03", "validate free room upgrade", HOTELID_LIST[1], True),
        ],
    )
    @pytest.mark.regression
    def test_validate_free_room_upgrade(
        self, test_case_id, tc_description, hotel_id, skip_message
    ):
        if skip_message:
            pytest.skip(skip_message)

        validation = ValidationRoomUpgrade(test_case_id, hotel_id)
        validation.validate_upgrade()
