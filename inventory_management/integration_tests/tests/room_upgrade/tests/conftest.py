import pytest

from inventory_management.integration_tests.config.common_config import HOTELID_LIST
from inventory_management.integration_tests.external_clients.crs_client import CrsClient
from inventory_management.integration_tests.requests.booking_requests import (
    BookingRequests,
)


@pytest.fixture(scope="session", autouse=True)
def cancel_booking():
    print("====> cancel old bookings running")
    for hotel_id in HOTELID_LIST:
        response = CrsClient().booking_search(hotel_id).json()
        bookings_data = response['data']['bookings']
        for booking_data in bookings_data:
            booking_id = booking_data['booking_id']
            version = booking_data['version']
            BookingRequests().cancel_booking_request(
                booking_id=booking_id, version=version
            )
    print("====> cancel old bookings completed")
