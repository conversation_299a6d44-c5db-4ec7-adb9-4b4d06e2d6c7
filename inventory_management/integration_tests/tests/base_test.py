from inventory_management.integration_tests.requests.booking_requests import *


class BaseTest(object):
    booking_request = BookingRequests()

    def common_request_caller(
        self, test_case_id_plus_action_to_be_performed_list, hotel_id
    ):
        for action in test_case_id_plus_action_to_be_performed_list:
            test_case_id = action['id'] if 'id' in action else None
            action_type = action['type']
            user_type = action['user_type'] if 'user_type' in action else None

            if action_type == 'create_booking':
                self.booking_request.create_booking_request(test_case_id, hotel_id)
            elif action_type == 'cancel':
                self.booking_request.cancel_booking_request(
                    self.booking_request.booking_id
                )

            else:
                raise ValueError(
                    action['id'] + ' is not handled in Common request caller'
                )
