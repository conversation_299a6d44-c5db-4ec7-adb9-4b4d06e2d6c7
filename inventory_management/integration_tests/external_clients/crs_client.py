import json

import requests
from treebo_commons.utils.dateutils import today, today_plus_days

from inventory_management.integration_tests.config.common_config import CRS_DOMAIN
from inventory_management.integration_tests.config.request_uris import *


class CrsClient:
    headers = {'X-User-Type': 'super-admin'}

    def create_booking(self, payload):
        create_booking_api = CRS_DOMAIN + CREATE_BOOKING_URI

        return self.__make_call(
            method="POST",
            api=create_booking_api,
            headers=self.headers,
            data=json.loads(payload),
        )

    def booking_action(self, payload, booking_id):
        booking_action_api = CRS_DOMAIN + BOOKING_ACTION_URI.format(booking_id)

        return self.__make_call(
            method="POST",
            api=booking_action_api,
            headers=self.headers,
            data=json.loads(payload),
        )

    def booking_search(self, hotel_id, first_name=None):
        if first_name is None:
            booking_search_api = CRS_DOMAIN + BOOKING_SEARCH_URI.format(
                today_plus_days(2), hotel_id, today()
            )
        else:
            booking_search_api = CRS_DOMAIN + BOOKING_SEARCH_URI_WITH_QUERY.format(
                first_name, today_plus_days(2), hotel_id, today()
            )

        return self.__make_call(
            method="GET", api=booking_search_api, headers=self.headers
        )

    @staticmethod
    def __make_call(method, api, headers, data=None, params=None):
        response = requests.request(
            method, api, params=params, headers=headers, json=data, allow_redirects=True
        )
        return response
