import json

from inventory_management.integration_tests.builders import (
    booking_action_builder,
    booking_builder,
)
from inventory_management.integration_tests.config.sheet_names import *
from inventory_management.integration_tests.external_clients.crs_client import CrsClient
from inventory_management.integration_tests.requests.base_request import BaseRequest
from inventory_management.integration_tests.utilities.common_utils import del_none


class BookingRequests(BaseRequest):
    def create_booking_request(self, test_case_id, hotel_id):
        request_json = json.dumps(
            del_none(
                booking_builder.CreateBookingRequest(
                    CREATE_BOOKING_SHEET_NAME, test_case_id, hotel_id
                ).__dict__
            )
        )
        response = CrsClient().create_booking(request_json).json()
        self.booking_id = response['data']['booking_id']
        return response

    def cancel_booking_request(self, booking_id, version):
        request_json = json.dumps(
            del_none(
                booking_action_builder.CancelBookingBuilder(version=version).__dict__
            )
        )
        response = CrsClient().booking_action(request_json, booking_id).json()
        self.action_id = response['data']['action_id']
        return response
