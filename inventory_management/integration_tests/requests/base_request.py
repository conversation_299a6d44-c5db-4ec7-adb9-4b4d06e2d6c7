import logging

from flask import json

from inventory_management.integration_tests.config import common_config
from inventory_management.integration_tests.utilities.common_utils import assert_

logger = logging.getLogger(__name__)


class BaseRequest(object):
    def __init__(self):
        self.booking_id = None
        self.action_id = None

    def request_processor(
        self,
        client_,
        request_type,
        url,
        status_code,
        request_json=None,
        user_type=None,
        parameters=None,
    ):
        headers = {
            'Content-Type': 'application/json',
            'X-User': 'automation',
            'X-Application': 'automation_suite',
            common_config.USER_TYPE_KEY: common_config.SUPER_ADMIN,
        }
        client_type = {
            "POST": client_.post,
            "PATCH": client_.patch,
            "GET": client_.get,
            "DELETE": client_.delete,
        }

        if user_type:  # if user type is present it will over-ride the existing value
            headers.update({common_config.USER_TYPE_KEY: user_type})
        print('\n\n' + '#' * 25 + 'REQUEST' + '#' * 25)
        print(
            'REQUEST URL: '
            + url
            + '\nREQUEST TYPE: '
            + request_type
            + '\nHEADERS: '
            + str(headers)
            + '\nREQUEST JSON: '
            + str(request_json)
            + '\nREQUEST PARAMS: '
            + str(parameters)
        )
        response = client_type.get(request_type)(
            url, data=request_json, headers=headers
        )
        print('\n\n' + '#' * 25 + 'RESPONSE' + '#' * 25)
        print(
            'RESPONSE CODE: '
            + str(response.status_code)
            + '\nRESPONSE DATA: '
            + json.dumps(response.json)
        )
        assert_(response.status_code, status_code, 'Status code is not matching')
        return response
