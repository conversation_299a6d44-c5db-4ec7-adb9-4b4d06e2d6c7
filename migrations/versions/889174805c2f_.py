"""empty message

Revision ID: 889174805c2f
Revises: 8446ace84e57
Create Date: 2020-02-20 17:20:31.127867

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '889174805c2f'
down_revision = 'b9f6336ad03e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('invoice', sa.Column('irn', sa.String(), nullable=True))
    op.add_column('invoice', sa.Column('irp_ack_number', sa.String(), nullable=True))
    op.add_column('invoice', sa.Column('qr_code', sa.String(), nullable=True))
    op.add_column('invoice', sa.Column('signed_invoice', sa.String(), nullable=True))
    op.add_column('invoice', sa.Column('irp_ack_date', sa.Date(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('invoice', 'signed_invoice')
    op.drop_column('invoice', 'qr_code')
    op.drop_column('invoice', 'irp_ack_number')
    op.drop_column('invoice', 'irn')
    op.drop_column('invoice', 'irp_ack_date')
    # ### end Alembic commands ###
