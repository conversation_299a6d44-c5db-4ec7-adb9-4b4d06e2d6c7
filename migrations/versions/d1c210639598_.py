"""empty message

Revision ID: d1c210639598
Revises: 8446ace84e57
Create Date: 2020-05-14 16:36:10.113243

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd1c210639598'
down_revision = '8446ace84e57'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('credit_note', sa.Column('signed_url', sa.String(), nullable=True))
    op.add_column(
        'credit_note',
        sa.Column('signed_url_expiry_time', sa.DateTime(timezone=True), nullable=True),
    )
    op.add_column('invoice', sa.Column('signed_url', sa.String(), nullable=True))
    op.add_column(
        'invoice',
        sa.Column('signed_url_expiry_time', sa.DateTime(timezone=True), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('invoice', 'signed_url_expiry_time')
    op.drop_column('invoice', 'signed_url')
    op.drop_column('credit_note', 'signed_url_expiry_time')
    op.drop_column('credit_note', 'signed_url')
    # ### end Alembic commands ###
