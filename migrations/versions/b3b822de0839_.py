"""empty message

Revision ID: b3b822de0839
Revises: 1d0a638ef14e
Create Date: 2019-05-30 16:01:25.485211

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'b3b822de0839'
down_revision = '1d0a638ef14e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('payment_split',
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted', sa.Bo<PERSON>an(), nullable=True),
    sa.Column('invoice_id', sa.String(), nullable=False),
    sa.Column('payment_split_id', sa.Integer(), nullable=False),
    sa.Column('payment_id', sa.Integer(), nullable=False),
    sa.Column('payment_type', sa.String(), nullable=True),
    sa.Column('amount', sa.DECIMAL(precision=15, scale=4), nullable=True),
    sa.PrimaryKeyConstraint('invoice_id', 'payment_split_id', 'payment_id')
    )
    op.create_index(op.f('ix_payment_split_deleted'), 'payment_split', ['deleted'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_payment_split_deleted'), table_name='payment_split')
    op.drop_table('payment_split')
    # ### end Alembic commands ###
