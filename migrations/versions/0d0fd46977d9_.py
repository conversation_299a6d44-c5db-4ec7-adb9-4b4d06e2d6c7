"""empty message

Revision ID: 0d0fd46977d9
Revises: 7db23742e05c
Create Date: 2018-10-23 14:43:38.776276

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '0d0fd46977d9'
down_revision = '7db23742e05c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bill', sa.Column('app_id', sa.String(), nullable=True))
    op.add_column('bill', sa.Column('parent_reference_number', sa.String(), nullable=True))
    op.drop_column('charge', 'is_refundable')
    op.drop_column('invoice_charges', 'is_refundable')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('invoice_charges', sa.Column('is_refundable', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('charge', sa.Column('is_refundable', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.drop_column('bill', 'parent_reference_number')
    op.drop_column('bill', 'app_id')
    # ### end Alembic commands ###
