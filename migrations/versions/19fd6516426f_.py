"""empty message

Revision ID: 19fd6516426f
Revises: 72cbfb50549f
Create Date: 2018-06-27 15:54:26.384123

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = '19fd6516426f'
down_revision = '72cbfb50549f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'expense_item', ['expense_item_id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'expense_item', type_='unique')
    # ### end Alembic commands ###
