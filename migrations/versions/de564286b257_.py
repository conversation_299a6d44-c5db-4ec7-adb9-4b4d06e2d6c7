"""empty message

Revision ID: de564286b257
Revises: 7a99fb4e0a92
Create Date: 2020-06-06 22:52:27.373680

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'de564286b257'
down_revision = '7a99fb4e0a92'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('booking_attachment', sa.Column('url', sa.String(), nullable=False))
    op.drop_column('booking_attachment', 'signed_url')
    op.drop_column('booking_attachment', 'expiry')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'booking_attachment',
        sa.Column('expiry', sa.DATE(), autoincrement=False, nullable=False),
    )
    op.add_column(
        'booking_attachment',
        sa.Column('signed_url', sa.VARCHAR(), autoincrement=False, nullable=False),
    )
    op.drop_column('booking_attachment', 'url')
    # ### end Alembic commands ###
