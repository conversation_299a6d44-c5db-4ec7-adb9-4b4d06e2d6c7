"""empty message

Revision ID: d96b4065ca8c
Revises: b5faba7fb96e
Create Date: 2019-06-12 13:20:05.894799

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'd96b4065ca8c'
down_revision = 'b5faba7fb96e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('charge', sa.Column('charge_components', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('charge', 'charge_components')
    # ### end Alembic commands ###
