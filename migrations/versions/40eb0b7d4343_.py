"""empty message

Revision ID: 40eb0b7d4343
Revises: 96bcbf1aea89
Create Date: 2019-01-16 16:23:44.369557

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = '40eb0b7d4343'
down_revision = '96bcbf1aea89'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('charge_idx_bill_id_charge_id', 'charge', ['bill_id', 'charge_id'], unique=False)
    op.create_index('charge_split_idx_bill_charge_charge_split_ids', 'charge_split', ['bill_id', 'charge_id', 'charge_split_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('charge_split_idx_bill_charge_charge_split_ids', table_name='charge_split')
    op.drop_index('charge_idx_bill_id_charge_id', table_name='charge')
    # ### end Alembic commands ###
