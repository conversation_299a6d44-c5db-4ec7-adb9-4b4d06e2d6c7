"""empty message

Revision ID: 2368e0fa360b
Revises: 6a8403007284
Create Date: 2018-09-20 16:10:44.925008

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '2368e0fa360b'
down_revision = '6a8403007284'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('payment', sa.Column('payment_mode_sub_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('payment', 'payment_mode_sub_type')
    # ### end Alembic commands ###
