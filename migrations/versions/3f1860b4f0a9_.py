"""empty message

Revision ID: 3f1860b4f0a9
Revises: 185057c17f27
Create Date: 2018-05-22 09:20:51.382568

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '3f1860b4f0a9'
down_revision = '185057c17f27'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('booking', sa.Column('cancellation_reason', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('booking', 'cancellation_reason')
    # ### end Alembic commands ###
