"""empty message

Revision ID: 077b029dd82d
Revises: feaa5ce70005
Create Date: 2018-05-17 19:59:17.557464

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '077b029dd82d'
down_revision = 'feaa5ce70005'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('invoice_charges', sa.Column('recorded_time', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('invoice_charges', 'recorded_time')
    # ### end Alembic commands ###
