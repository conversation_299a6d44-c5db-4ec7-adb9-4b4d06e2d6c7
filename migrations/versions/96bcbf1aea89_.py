"""empty message

Revision ID: 96bcbf1aea89
Revises: 10812b8fdec9
Create Date: 2019-01-11 12:20:25.191892

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '96bcbf1aea89'
down_revision = '10812b8fdec9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('room_inventory', 'status')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room_inventory', sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
