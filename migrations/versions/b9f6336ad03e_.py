"""empty message

Revision ID: b9f6336ad03e
Revises: b670b0945f92
Create Date: 2020-06-17 15:50:11.374543

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b9f6336ad03e'
down_revision = 'b670b0945f92'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'e_reg_card',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('room_stay_id', sa.Integer(), nullable=True),
        sa.Column('is_primary', sa.<PERSON>(), nullable=True),
        sa.Column('deleted', sa.<PERSON>(), nullable=True),
        sa.Column('ups_user_id', sa.String(), nullable=True),
        sa.Column('room_number', sa.String(), nullable=True),
        sa.Column('employment_details', sa.JSON(), nullable=True),
        sa.Column('travel_details', sa.JSON(), nullable=True),
        sa.Column('id_proof', sa.JSON(), nullable=True),
        sa.Column('e_reg_card_id', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('booking_id', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('e_reg_card_id'),
    )
    op.create_index(
        'ix_e_reg_card_booking_id', 'e_reg_card', ['booking_id'], unique=False
    )
    op.create_index(
        'ix_e_reg_card_booking_id_e_reg_card_id',
        'e_reg_card',
        ['booking_id', 'e_reg_card_id'],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_e_reg_card_booking_id_e_reg_card_id', table_name='e_reg_card')
    op.drop_index('ix_e_reg_card_booking_id', table_name='e_reg_card')
    op.drop_table('e_reg_card')
    # ### end Alembic commands ###
