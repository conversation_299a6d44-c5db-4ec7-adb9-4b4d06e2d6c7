"""empty message

Revision ID: d4e707acf777
Revises: b9f0db6e9f7e
Create Date: 2019-10-21 14:58:06.063125

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'd4e707acf777'
down_revision = 'b9f0db6e9f7e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('integration_event', sa.Column('booking_id', sa.String(), nullable=True))
    op.add_column('integration_event', sa.Column('user_action', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('integration_event', 'user_action')
    op.drop_column('integration_event', 'booking_id')
    # ### end Alembic commands ###
