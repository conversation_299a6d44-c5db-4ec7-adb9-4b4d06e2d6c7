"""empty message

Revision ID: 3e1945c12ba8
Revises: 08abe5f2f71a
Create Date: 2018-06-01 16:19:38.235855

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '3e1945c12ba8'
down_revision = 'b1beb3e3174b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('charge_split', sa.Column('percentage', sa.DECIMAL(precision=9, scale=4), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('charge_split', 'percentage')
    # ### end Alembic commands ###
