"""empty message

Revision ID: 8446ace84e57
Revises: 5124a960a87a
Create Date: 2019-12-15 10:42:51.656268

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '8446ace84e57'
down_revision = '5124a960a87a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(
        'ix_pos_order_seller_order_number_uq',
        'pos_order',
        ['seller_id', 'order_number', 'order_date'],
    )
    op.create_unique_constraint(
        'ix_pos_order_item_charge_id_uq',
        'pos_order_item',
        ['order_id', 'order_item_id', 'charge_id'],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        'ix_pos_order_item_charge_id_uq', 'pos_order_item', type_='unique'
    )
    op.drop_constraint(
        'ix_pos_order_seller_order_number_uq', 'pos_order', type_='unique'
    )
    # ### end Alembic commands ###
