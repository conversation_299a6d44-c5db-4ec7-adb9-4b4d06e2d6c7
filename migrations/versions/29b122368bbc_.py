"""empty message

Revision ID: 29b122368bbc
Revises: 1d5d58846d85
Create Date: 2019-02-06 13:53:20.455170

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '29b122368bbc'
down_revision = '1d5d58846d85'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('booking_action', sa.Column('is_reversible', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('booking_action', 'is_reversible')
    # ### end Alembic commands ###
