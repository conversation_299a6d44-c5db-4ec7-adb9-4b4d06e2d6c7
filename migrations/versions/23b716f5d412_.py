"""empty message

Revision ID: 23b716f5d412
Revises: 08abe5f2f71a
Create Date: 2018-06-01 11:23:11.007581

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '23b716f5d412'
down_revision = '08abe5f2f71a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('integration_event', 'entity_id')
    op.drop_column('integration_event', 'entity_version')
    op.drop_column('integration_event', 'minor_version')
    op.drop_column('integration_event', 'max_minor_version')
    op.drop_column('integration_event', 'entity_name')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('integration_event', sa.Column('entity_name', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('integration_event', sa.Column('max_minor_version', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('integration_event', sa.Column('minor_version', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('integration_event', sa.Column('entity_version', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('integration_event', sa.Column('entity_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
