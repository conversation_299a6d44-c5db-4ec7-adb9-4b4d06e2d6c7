"""empty message

Revision ID: 63ef322619f3
Revises: 077b029dd82d
Create Date: 2018-05-23 12:42:28.672434

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '63ef322619f3'
down_revision = '1712496e2832'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('integration_event', sa.Column('entity_id', sa.String(), nullable=True))
    op.add_column('integration_event', sa.Column('entity_name', sa.String(), nullable=False))
    op.add_column('integration_event', sa.Column('entity_version', sa.String(), nullable=False))
    op.add_column('integration_event', sa.Column('event_id', sa.String(), nullable=False))
    op.add_column('integration_event', sa.Column('max_minor_version', sa.Integer(), nullable=False))
    op.add_column('integration_event', sa.Column('minor_version', sa.Integer(), nullable=False))
    op.alter_column('integration_event', 'hotel_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.drop_column('integration_event', 'id')
    op.drop_column('integration_event', 'version')
    op.drop_column('integration_event', 'booking_id')
    op.add_column('job', sa.Column('job_id', sa.String(), nullable=False))
    op.alter_column('job', 'hotel_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.drop_column('job', 'id')
    op.drop_column('job', 'booking_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('job', sa.Column('booking_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('job', sa.Column('id', sa.INTEGER(), nullable=False))
    op.alter_column('job', 'hotel_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_column('job', 'job_id')
    op.add_column('integration_event', sa.Column('booking_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('integration_event', sa.Column('version', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('integration_event', sa.Column('id', sa.INTEGER(), nullable=False))
    op.alter_column('integration_event', 'hotel_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_column('integration_event', 'minor_version')
    op.drop_column('integration_event', 'max_minor_version')
    op.drop_column('integration_event', 'event_id')
    op.drop_column('integration_event', 'entity_version')
    op.drop_column('integration_event', 'entity_name')
    op.drop_column('integration_event', 'entity_id')
    # ### end Alembic commands ###
