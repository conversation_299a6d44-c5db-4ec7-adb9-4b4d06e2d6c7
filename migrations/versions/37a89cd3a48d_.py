"""empty message

Revision ID: 37a89cd3a48d
Revises: 3e1945c12ba8
Create Date: 2018-06-11 15:31:33.756932

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '37a89cd3a48d'
down_revision = '3e1945c12ba8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('addon_expense')
    op.add_column('addon', sa.Column('expenses', sa.ARRAY(sa.String()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('addon', 'expenses')
    op.create_table('addon_expense',
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('modified_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('deleted', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('addon_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('booking_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('room_stay_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('addon_expense_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('expense_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('expense_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('addon_id', 'booking_id', 'room_stay_id', 'addon_expense_id', name='addon_expense_pkey')
    )
    # ### end Alembic commands ###
