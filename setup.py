#!/usr/bin/env python
import os

from setuptools import find_packages, setup


def get_version():
    basedir = os.path.dirname(__file__)
    with open(os.path.join(basedir, 'thsc/crs/version.py')) as f:
        locals = {}
        exec(f.read(), locals)
        return locals['VERSION']
    raise RuntimeError('No version info found.')


setup(
    name='thsc',
    version=get_version(),
    description='THSC client library',
    author='THS',
    author_email='<EMAIL>',
    url='https://bitbucket.org/treebo/prometheus',
    download_url=' https://bitbucket.org/treebo/prometheus',
    packages=find_packages(include=['thsc', 'thsc.*', 'ths_common', 'ths_common.*']),
    zip_safe=False,
    include_package_data=True,
    license='MIT',
    platforms='any',
    install_requires=[
        'python-dateutil>=2.7.2',
        'enum34>=1.1.6',
        'mock==2.0.0',
        'simplejson>=3.17.0',
        'factory-boy==2.10.0',
        'pytz>=2018.3',
        'requests>=2.18.4',
        'aenum>=2.1.2',
        'treebo-commons>=1.2.3',
        'phonenumbers==8.10.17',
    ],
    classifiers=(
        'Development Status :: 5 - Production/Stable',
        'Environment :: Web Environment',
        'Framework :: Flask',
        'Programming Language :: Python',
    ),
)
